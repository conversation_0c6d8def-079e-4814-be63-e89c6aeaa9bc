/**
 * Test frontend retrieval with the new organized directory structure
 * This verifies that courses created with organized directories are properly retrieved and displayed
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3200/api';

async function apiRequest(method, endpoint, data = null) {
  const config = {
    method,
    url: `${API_BASE_URL}${endpoint}`,
    headers: {
      'Content-Type': 'application/json'
    }
  };
  
  if (data) {
    config.data = data;
  }
  
  try {
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`API Error (${method} ${endpoint}):`, error.response?.data || error.message);
    throw error;
  }
}

// Simulate the frontend course service's constructMediaUrl method
function constructMediaUrl(hashname) {
  if (!hashname) return '';
  
  // If already a complete URL, return as-is
  if (hashname.startsWith('http://') || hashname.startsWith('https://')) {
    return hashname;
  }
  
  // Remove leading slash if present to avoid double slashes
  const cleanHashname = hashname.startsWith('/') ? hashname.substring(1) : hashname;
  
  // Backend serves files directly from the root with the full path
  return `http://localhost:3200/${cleanHashname}`;
}

async function testFrontendRetrievalOrganized() {
  console.log('🔍 Testing Frontend Retrieval with Organized Directory Structure');
  console.log('=' .repeat(75));
  
  // Step 1: Create a course with organized media structure
  console.log('\n1️⃣ Creating course with organized media structure...');
  
  const tempCourseSlug = `frontend-test-${Date.now()}`;
  
  // Upload cover image
  const coverImageData = {
    Name: `${Date.now()}-frontend-cover.png`,
    Hashname: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==',
    Extension: 'image/png',
    Size: 1500,
    SubDir: `courses/${tempCourseSlug}/cover`
  };
  
  const uploadedCover = await apiRequest('POST', '/medias', coverImageData);
  
  // Upload presentation video
  const videoData = {
    Name: `${Date.now()}-frontend-video.mp4`,
    Hashname: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==',
    Extension: 'video/mp4',
    Size: 5000,
    SubDir: `courses/${tempCourseSlug}/video`
  };
  
  const uploadedVideo = await apiRequest('POST', '/medias', videoData);
  
  // Create course
  const coursePayload = {
    body: {
      Title: 'Frontend Retrieval Test Course',
      Resume: 'Testing frontend retrieval with organized directory structure',
      Keywords: ['frontend', 'retrieval', 'organized'],
      Format: 2,
      Language: 'en',
      Free: true,
      Level: [1],
      Message: 'Welcome message',
      Congratulation: 'Congratulations message',
      Categories: [],
      Published: true, // Make it published so it appears in listings
      Archived: false,
      coverImageId: uploadedCover.Id,
      presentationVideoId: uploadedVideo.Id,
      Slug: tempCourseSlug,
      CreatedBy: { Id: 1 }
    },
    origin: 'http://localhost:3000'
  };
  
  const createdCourse = await apiRequest('POST', '/courses', coursePayload);
  
  console.log('✅ Course created for testing:');
  console.log(`   ID: ${createdCourse.Id}`);
  console.log(`   Slug: ${createdCourse.Slug}`);
  console.log(`   Title: ${createdCourse.Title}`);
  
  // Step 2: Test course retrieval (simulating frontend)
  console.log('\n2️⃣ Testing course retrieval...');
  
  const retrievedCourse = await apiRequest('GET', `/courses/${createdCourse.Id}`);
  
  console.log('📊 Retrieved Course Media Information:');
  console.log(`   Course ID: ${retrievedCourse.Id}`);
  console.log(`   Course Slug: ${retrievedCourse.Slug}`);
  
  if (retrievedCourse.CoverImage) {
    console.log(`   Cover Image:`);
    console.log(`     ID: ${retrievedCourse.CoverImage.Id}`);
    console.log(`     Hashname: ${retrievedCourse.CoverImage.Hashname}`);
    console.log(`     SubDir: ${retrievedCourse.CoverImage.SubDir}`);
  }
  
  if (retrievedCourse.PresentationVideo) {
    console.log(`   Presentation Video:`);
    console.log(`     ID: ${retrievedCourse.PresentationVideo.Id}`);
    console.log(`     Hashname: ${retrievedCourse.PresentationVideo.Hashname}`);
    console.log(`     SubDir: ${retrievedCourse.PresentationVideo.SubDir}`);
  }
  
  // Step 3: Test frontend URL construction
  console.log('\n3️⃣ Testing frontend URL construction...');
  
  const coverImageUrl = retrievedCourse.CoverImage ? 
    constructMediaUrl(retrievedCourse.CoverImage.Hashname) : null;
  const videoUrl = retrievedCourse.PresentationVideo ? 
    constructMediaUrl(retrievedCourse.PresentationVideo.Hashname) : null;
  
  console.log('🔗 Frontend URL Construction:');
  if (coverImageUrl) {
    console.log(`   Cover Image URL: ${coverImageUrl}`);
  }
  if (videoUrl) {
    console.log(`   Video URL: ${videoUrl}`);
  }
  
  // Step 4: Test URL accessibility
  console.log('\n4️⃣ Testing URL accessibility...');
  
  const urlTests = [];
  
  if (coverImageUrl) {
    try {
      const response = await axios.head(coverImageUrl);
      urlTests.push({
        type: 'Cover Image',
        url: coverImageUrl,
        status: response.status,
        accessible: true
      });
      console.log(`   Cover Image URL: ✅ ACCESSIBLE (${response.status})`);
    } catch (error) {
      urlTests.push({
        type: 'Cover Image',
        url: coverImageUrl,
        status: error.response?.status || 'ERROR',
        accessible: false
      });
      console.log(`   Cover Image URL: ❌ NOT ACCESSIBLE (${error.response?.status || error.message})`);
    }
  }
  
  if (videoUrl) {
    try {
      const response = await axios.head(videoUrl);
      urlTests.push({
        type: 'Video',
        url: videoUrl,
        status: response.status,
        accessible: true
      });
      console.log(`   Video URL: ✅ ACCESSIBLE (${response.status})`);
    } catch (error) {
      urlTests.push({
        type: 'Video',
        url: videoUrl,
        status: error.response?.status || 'ERROR',
        accessible: false
      });
      console.log(`   Video URL: ❌ NOT ACCESSIBLE (${error.response?.status || error.message})`);
    }
  }
  
  // Step 5: Test public course listing
  console.log('\n5️⃣ Testing public course listing...');
  
  try {
    const publicCourses = await apiRequest('GET', '/courses/public');
    // Handle both array and object responses
    const coursesArray = Array.isArray(publicCourses) ? publicCourses : (publicCourses.courses || []);
    const ourCourse = coursesArray.find(course => course.Id === createdCourse.Id);
    
    if (ourCourse) {
      console.log('✅ Course appears in public listing');
      console.log(`   Title: ${ourCourse.Title}`);
      console.log(`   Has Cover Image: ${!!ourCourse.CoverImage}`);
      console.log(`   Has Video: ${!!ourCourse.PresentationVideo}`);
      
      if (ourCourse.CoverImage) {
        const publicCoverUrl = constructMediaUrl(ourCourse.CoverImage.Hashname);
        console.log(`   Public Cover URL: ${publicCoverUrl}`);
      }
    } else {
      console.log('❌ Course not found in public listing');
    }
  } catch (error) {
    console.log('❌ Error fetching public courses:', error.message);
  }
  
  // Step 6: Final assessment
  console.log('\n6️⃣ Final Assessment:');
  
  const allUrlsAccessible = urlTests.every(test => test.accessible);
  const hasOrganizedStructure = retrievedCourse.CoverImage?.SubDir?.includes('courses/') && 
                               retrievedCourse.PresentationVideo?.SubDir?.includes('courses/');
  // Extract just the filename from the full path for prefix checking
  const coverFilename = retrievedCourse.CoverImage?.Hashname?.split('/').pop() || '';
  const videoFilename = retrievedCourse.PresentationVideo?.Hashname?.split('/').pop() || '';
  const hasCorrectPrefixes = coverFilename.startsWith('cvr_') && videoFilename.startsWith('vid_');
  
  console.log(`   URL Accessibility: ${allUrlsAccessible ? '✅ ALL ACCESSIBLE' : '❌ SOME ISSUES'}`);
  console.log(`   Organized Structure: ${hasOrganizedStructure ? '✅ CORRECT' : '❌ INCORRECT'}`);
  console.log(`   UUID Prefixes: ${hasCorrectPrefixes ? '✅ CORRECT' : '❌ INCORRECT'}`);
  
  if (allUrlsAccessible && hasOrganizedStructure && hasCorrectPrefixes) {
    console.log('\n🎉 SUCCESS: Frontend retrieval with organized directory structure is working perfectly!');
    console.log('\n📋 Summary:');
    console.log('   ✅ Course creation with organized media structure');
    console.log('   ✅ Course retrieval with proper media references');
    console.log('   ✅ Frontend URL construction working');
    console.log('   ✅ Media files accessible via constructed URLs');
    console.log('   ✅ Public course listing includes organized media');
    console.log('\n🚀 The organized directory structure is ready for production use!');
  } else {
    console.log('\n❌ ISSUES FOUND: Some aspects need attention');
    console.log('\n🔧 Issues to address:');
    if (!allUrlsAccessible) console.log('   - URL accessibility problems');
    if (!hasOrganizedStructure) console.log('   - Directory structure not organized');
    if (!hasCorrectPrefixes) console.log('   - UUID prefixes incorrect');
  }
  
  return {
    success: allUrlsAccessible && hasOrganizedStructure && hasCorrectPrefixes,
    courseId: createdCourse.Id,
    courseSlug: createdCourse.Slug,
    urlTests,
    retrievedCourse
  };
}

testFrontendRetrievalOrganized().catch(console.error);
