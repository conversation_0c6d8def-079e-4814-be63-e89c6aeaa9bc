const axios = require('axios');

const API_BASE = 'http://localhost:3200/api';

/**
 * Test the specific course from the screenshot: "demo course two (Updated with...)"
 */

async function testSpecificCourse() {
  console.log('🔍 Testing Specific Course: "demo course two (Updated with...)"');
  console.log('=' .repeat(60));

  try {
    // Step 1: Find the exact course from the screenshot
    console.log('\n1️⃣ Searching for the course...');
    const coursesResponse = await axios.get(`${API_BASE}/courses/50/0`);
    const courses = coursesResponse.data;
    
    // Look for the course that matches the screenshot
    const targetCourse = courses.find(course => 
      course.Title && course.Title.toLowerCase().includes('demo course two')
    );

    if (!targetCourse) {
      console.log('❌ Could not find "demo course two" in the courses list');
      console.log('📋 Available courses:');
      courses.slice(0, 5).forEach((course, index) => {
        console.log(`   ${index + 1}. ${course.Title} (${course.Slug})`);
      });
      return;
    }

    console.log('✅ Found target course:');
    console.log(`   - Title: ${targetCourse.Title}`);
    console.log(`   - Slug: ${targetCourse.Slug}`);
    console.log(`   - ID: ${targetCourse.Id}`);

    // Step 2: Check cover image data
    console.log('\n2️⃣ Analyzing cover image data...');
    if (targetCourse.CoverImage) {
      console.log('✅ CoverImage object exists:');
      console.log(`   - Hashname: ${targetCourse.CoverImage.Hashname}`);
      console.log(`   - Name: ${targetCourse.CoverImage.Name || 'N/A'}`);
      console.log(`   - Extension: ${targetCourse.CoverImage.Extension || 'N/A'}`);
      console.log(`   - Size: ${targetCourse.CoverImage.Size || 'N/A'}`);
      console.log(`   - SubDir: ${targetCourse.CoverImage.SubDir || 'N/A'}`);
    } else {
      console.log('❌ No CoverImage object found');
      console.log('   This explains why the placeholder is showing');
      return;
    }

    // Step 3: Test different URL constructions
    console.log('\n3️⃣ Testing URL constructions...');
    const hashname = targetCourse.CoverImage.Hashname;
    
    const urlTests = [
      {
        name: 'Direct (Backend serves from root)',
        url: `http://localhost:3200/${hashname}`
      },
      {
        name: 'With public prefix',
        url: `http://localhost:3200/public/${hashname}`
      },
      {
        name: 'Clean hashname (remove leading slash)',
        url: `http://localhost:3200/${hashname.startsWith('/') ? hashname.substring(1) : hashname}`
      },
      {
        name: 'Frontend environment.path construction',
        url: `http://localhost:3200/${hashname.startsWith('/') ? hashname.substring(1) : hashname}`
      }
    ];

    for (const test of urlTests) {
      try {
        const response = await axios.head(test.url, { timeout: 5000 });
        console.log(`   ✅ ${test.name}: ${response.status} ${response.statusText}`);
        console.log(`      URL: ${test.url}`);
      } catch (error) {
        console.log(`   ❌ ${test.name}: ${error.response?.status || 'Network Error'}`);
        console.log(`      URL: ${test.url}`);
      }
    }

    // Step 4: Test the specific course endpoint
    console.log('\n4️⃣ Testing specific course endpoint...');
    try {
      const courseResponse = await axios.get(`${API_BASE}/courses/public/${targetCourse.Slug}`);
      const course = courseResponse.data;
      
      console.log('✅ Course endpoint response:');
      console.log(`   - Title: ${course.Title}`);
      
      if (course.CoverImage) {
        console.log(`   - CoverImage.Hashname: ${course.CoverImage.Hashname}`);
        
        // Test the exact URL that frontend should construct
        const frontendUrl = constructFrontendUrl(course.CoverImage.Hashname);
        console.log(`   - Frontend constructed URL: ${frontendUrl}`);
        
        try {
          const imageTest = await axios.head(frontendUrl, { timeout: 5000 });
          console.log(`   ✅ Frontend URL works: ${imageTest.status}`);
        } catch (error) {
          console.log(`   ❌ Frontend URL fails: ${error.response?.status || 'Network Error'}`);
        }
      } else {
        console.log('   ❌ No CoverImage in specific course response');
      }
    } catch (error) {
      console.log(`❌ Specific course endpoint failed: ${error.response?.status || error.message}`);
    }

    // Step 5: Check if file exists on disk
    console.log('\n5️⃣ Checking file system...');
    const fs = require('fs');
    const path = require('path');
    
    // The backend serves from PUBLIC_DIR which is /public
    const possiblePaths = [
      path.join(__dirname, 'backend', 'public', hashname),
      path.join(__dirname, 'backend', hashname),
      path.join(__dirname, 'backend', 'public', 'upload', 'courses', 'covers', path.basename(hashname))
    ];

    for (const filePath of possiblePaths) {
      if (fs.existsSync(filePath)) {
        console.log(`   ✅ File exists: ${filePath}`);
        const stats = fs.statSync(filePath);
        console.log(`      Size: ${stats.size} bytes`);
        console.log(`      Modified: ${stats.mtime}`);
      } else {
        console.log(`   ❌ File not found: ${filePath}`);
      }
    }

  } catch (error) {
    console.error('💥 Test failed:', error.message);
  }

  console.log('\n🎉 Specific course test completed!');
}

/**
 * Simulate frontend URL construction
 */
function constructFrontendUrl(hashname) {
  if (!hashname) return '';
  
  // If already a complete URL, return as-is
  if (hashname.startsWith('http://') || hashname.startsWith('https://')) {
    return hashname;
  }
  
  // Remove leading slash if present to avoid double slashes
  const cleanHashname = hashname.startsWith('/') ? hashname.substring(1) : hashname;
  
  // Use environment path (development)
  const environmentPath = 'http://localhost:3200';
  return `${environmentPath}/${cleanHashname}`;
}

// Run the test
testSpecificCourse().catch(console.error);
