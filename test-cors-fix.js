#!/usr/bin/env node

/**
 * Test CORS Fix for Media Files
 * Tests if media files are now accessible from the frontend with proper CORS headers
 */

const axios = require('axios');

async function testCORSFix() {
    console.log('🧪 Testing CORS Fix for Media Files...\n');

    // Test media files that were previously blocked
    const testFiles = [
        'http://localhost:3200/upload/D2174571/content/coverimage/cvr_18af6c7318624bf885f97a4fedf09bdf-database-test-cover.png',
        'http://localhost:3200/upload/D2174571/content/presentation/prs_4e2a2c913d30409585127e268d9eaa8f-database-test-video.mp4'
    ];

    for (const fileUrl of testFiles) {
        try {
            console.log(`📁 Testing: ${fileUrl.split('/').pop()}`);
            
            // Test with CORS headers
            const response = await axios.get(fileUrl, {
                headers: {
                    'Origin': 'http://localhost:5174',
                    'Access-Control-Request-Method': 'GET',
                    'Access-Control-Request-Headers': 'Content-Type'
                },
                timeout: 5000
            });

            console.log(`✅ Status: ${response.status}`);
            console.log(`📊 Content-Type: ${response.headers['content-type']}`);
            console.log(`🔒 CORS Headers:`);
            console.log(`   - Access-Control-Allow-Origin: ${response.headers['access-control-allow-origin']}`);
            console.log(`   - Access-Control-Allow-Methods: ${response.headers['access-control-allow-methods']}`);
            console.log(`   - Cross-Origin-Resource-Policy: ${response.headers['cross-origin-resource-policy']}`);
            console.log(`📏 Content-Length: ${response.headers['content-length']} bytes`);
            console.log('✅ SUCCESS: File accessible with CORS headers!\n');

        } catch (error) {
            console.log(`❌ FAILED: ${error.message}`);
            if (error.response) {
                console.log(`   Status: ${error.response.status}`);
                console.log(`   Headers:`, error.response.headers);
            }
            console.log('');
        }
    }

    // Test browser simulation
    console.log('🌐 Testing Browser Simulation...');
    try {
        const testUrl = 'http://localhost:5174/test-course-data.html';
        console.log(`📱 Frontend test page: ${testUrl}`);
        console.log('🔧 Please open this URL in your browser to test media loading');
        console.log('✅ CORS fix should resolve OpaqueResponseBlocking errors');
    } catch (error) {
        console.log(`❌ Browser test setup failed: ${error.message}`);
    }

    console.log('\n🎯 CORS Fix Test Complete!');
    console.log('📋 Summary:');
    console.log('   - Backend static file serving now includes CORS headers');
    console.log('   - Access-Control-Allow-Origin: * (allows all origins)');
    console.log('   - Cross-Origin-Resource-Policy: cross-origin');
    console.log('   - Media files should now load in browser without blocking');
}

// Run the test
testCORSFix().catch(console.error);
