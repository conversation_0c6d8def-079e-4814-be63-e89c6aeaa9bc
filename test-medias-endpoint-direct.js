/**
 * Direct test of the /api/medias endpoint
 * Testing if the medias endpoint is actually working now
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3200/api';
const TEST_IMAGE_BASE64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';

async function testMediasEndpointDirect() {
  console.log('🧪 Testing /api/medias endpoint directly...');
  
  try {
    // Test 1: Simple media creation
    console.log('1️⃣ Testing simple media creation...');
    const response1 = await axios.post(`${BASE_URL}/medias`, {
      Name: `test-direct-${Date.now()}.png`,
      Hashname: TEST_IMAGE_BASE64,
      Extension: 'image/png',
      Size: 32,
      SubDir: 'courses/covers'
    });
    
    console.log('✅ Simple media creation response:', {
      Id: response1.data.Id,
      Name: response1.data.Name,
      Hashname: response1.data.Hashname?.substring(0, 50) + '...',
      SubDir: response1.data.SubDir
    });

    // Test 2: Media creation with different format (matching other working examples)
    console.log('\n2️⃣ Testing media creation with Username field...');
    const response2 = await axios.post(`${BASE_URL}/medias`, {
      Username: `test-username-${Date.now()}.png`,
      Hashname: TEST_IMAGE_BASE64,
      Extension: 'image/png',
      Size: 32,
      SubDir: 'courses/covers'
    });
    
    console.log('✅ Username format response:', {
      Id: response2.data.Id,
      Username: response2.data.Username,
      Hashname: response2.data.Hashname?.substring(0, 50) + '...',
      SubDir: response2.data.SubDir
    });

    // Test 3: Media creation with Type field
    console.log('\n3️⃣ Testing media creation with Type field...');
    const response3 = await axios.post(`${BASE_URL}/medias`, {
      Name: `test-type-${Date.now()}.png`,
      Hashname: TEST_IMAGE_BASE64,
      Type: 'image/png',
      Size: 32,
      SubDir: 'courses/covers'
    });
    
    console.log('✅ Type format response:', {
      Id: response3.data.Id,
      Name: response3.data.Name,
      Type: response3.data.Type,
      Hashname: response3.data.Hashname?.substring(0, 50) + '...',
      SubDir: response3.data.SubDir
    });

    return {
      success: true,
      results: [response1.data, response2.data, response3.data]
    };

  } catch (error) {
    console.error('❌ Medias endpoint test failed:', error.response?.data || error.message);
    console.error('   - Status:', error.response?.status);
    console.error('   - Headers:', error.response?.headers);
    
    return {
      success: false,
      error: error.response?.data || error.message,
      status: error.response?.status
    };
  }
}

async function runDirectTest() {
  console.log('🚀 Direct /api/medias Endpoint Test');
  console.log('=' .repeat(50));
  
  const result = await testMediasEndpointDirect();
  
  console.log('\n📊 TEST RESULTS');
  console.log('=' .repeat(50));
  
  if (result.success) {
    console.log('🎉 SUCCESS: /api/medias endpoint is working!');
    console.log(`✅ Created ${result.results.length} media records`);
    console.log('\n💡 RECOMMENDATION: Use /api/medias endpoint instead of /api/utils/savefile');
    console.log('   - Creates proper database records');
    console.log('   - Handles file storage automatically');
    console.log('   - Returns media objects with IDs');
  } else {
    console.log('❌ FAILED: /api/medias endpoint is not working');
    console.log(`   - Status: ${result.status}`);
    console.log(`   - Error: ${result.error}`);
    console.log('\n💡 RECOMMENDATION: Continue using /api/utils/savefile endpoint');
  }
}

runDirectTest().catch(console.error);
