/**
 * Comprehensive Frontend vs Backend Data Comparison Test
 * This script compares the exact data received by frontend vs backend
 * to verify that course media is being properly processed and displayed
 */

const axios = require('axios');

const API_BASE = 'http://localhost:3200/api';
const FRONTEND_BASE = 'http://localhost:5174';
const TEST_COURSE_SLUG = 'd364109feb9c4332ba97561a5413ecc6';

/**
 * Test 1: Get Raw Backend Data
 */
async function getBackendData() {
  console.log('🔍 Step 1: Getting Raw Backend Data...');
  
  try {
    const response = await axios.get(`${API_BASE}/courses/public/${TEST_COURSE_SLUG}`);
    const course = response.data;
    
    console.log('✅ Backend Raw Data:', {
      courseId: course.Id,
      title: course.Title,
      slug: course.Slug,
      coverImage: course.CoverImage ? {
        id: course.CoverImage.Id,
        name: course.CoverImage.Name,
        hashname: course.CoverImage.Hashname,
        subDir: course.CoverImage.SubDir,
        extension: course.CoverImage.Extension
      } : null,
      presentationVideo: course.PresentationVideo ? {
        id: course.PresentationVideo.Id,
        name: course.PresentationVideo.Name,
        hashname: course.PresentationVideo.Hashname,
        subDir: course.PresentationVideo.SubDir,
        extension: course.PresentationVideo.Extension
      } : null
    });
    
    return course;
  } catch (error) {
    console.error('❌ Backend Data Error:', error.message);
    return null;
  }
}

/**
 * Test 2: Simulate Frontend Course Service Processing
 */
function simulateFrontendProcessing(backendCourse) {
  console.log('\n🔧 Step 2: Simulating Frontend Course Service Processing...');
  
  if (!backendCourse) {
    console.log('❌ No backend course data to process');
    return null;
  }
  
  // Simulate the frontend course service's formatCourse method
  const ENVIRONMENT_PATH = 'http://localhost:3200'; // Development environment
  
  function constructMediaUrl(hashname) {
    if (!hashname) return '';
    
    // If already a complete URL, return as-is
    if (hashname.startsWith('http://') || hashname.startsWith('https://')) {
      return hashname;
    }
    
    // Remove leading slash if present to avoid double slashes
    const cleanHashname = hashname.startsWith('/') ? hashname.substring(1) : hashname;
    
    // Backend serves static files directly from the root
    return `${ENVIRONMENT_PATH}/${cleanHashname}`;
  }
  
  // Create a copy of the course to simulate frontend processing
  const processedCourse = JSON.parse(JSON.stringify(backendCourse));
  
  // Format cover image URL (simulate frontend formatCourse method)
  if (processedCourse.CoverImage?.Hashname) {
    const originalHashname = processedCourse.CoverImage.Hashname;
    processedCourse.CoverImage.Hashname = constructMediaUrl(originalHashname);
    
    console.log('📸 Cover Image Processing:', {
      original: originalHashname,
      processed: processedCourse.CoverImage.Hashname
    });
  }
  
  // Format presentation video URL
  if (processedCourse.PresentationVideo?.Hashname) {
    const originalHashname = processedCourse.PresentationVideo.Hashname;
    processedCourse.PresentationVideo.Hashname = constructMediaUrl(originalHashname);
    
    console.log('🎥 Video Processing:', {
      original: originalHashname,
      processed: processedCourse.PresentationVideo.Hashname
    });
  }
  
  return processedCourse;
}

/**
 * Test 3: Test Actual Frontend API Call
 */
async function testFrontendAPI() {
  console.log('\n🌐 Step 3: Testing Frontend API Integration...');
  
  try {
    // Test if we can make the same API call that the frontend would make
    const response = await axios.get(`${API_BASE}/courses/public/${TEST_COURSE_SLUG}`, {
      headers: {
        'Origin': 'http://localhost:5174',
        'Referer': 'http://localhost:5174'
      }
    });
    
    console.log('✅ Frontend API Call Successful');
    return response.data;
  } catch (error) {
    console.error('❌ Frontend API Call Failed:', error.message);
    return null;
  }
}

/**
 * Test 4: Verify Media URLs are Accessible
 */
async function verifyMediaAccessibility(processedCourse) {
  console.log('\n🔗 Step 4: Verifying Media URL Accessibility...');
  
  const results = {
    coverImageAccessible: false,
    videoAccessible: false,
    coverImageUrl: null,
    videoUrl: null
  };
  
  // Test cover image accessibility
  if (processedCourse.CoverImage?.Hashname) {
    results.coverImageUrl = processedCourse.CoverImage.Hashname;
    try {
      const response = await axios.head(results.coverImageUrl, { timeout: 5000 });
      results.coverImageAccessible = response.status === 200;
      console.log(`✅ Cover Image Accessible: ${results.coverImageUrl}`);
    } catch (error) {
      console.log(`❌ Cover Image Not Accessible: ${results.coverImageUrl} (${error.response?.status || error.message})`);
    }
  }
  
  // Test video accessibility
  if (processedCourse.PresentationVideo?.Hashname) {
    results.videoUrl = processedCourse.PresentationVideo.Hashname;
    try {
      const response = await axios.head(results.videoUrl, { timeout: 5000 });
      results.videoAccessible = response.status === 200;
      console.log(`✅ Video Accessible: ${results.videoUrl}`);
    } catch (error) {
      console.log(`❌ Video Not Accessible: ${results.videoUrl} (${error.response?.status || error.message})`);
    }
  }
  
  return results;
}

/**
 * Test 5: Compare Data Structures
 */
function compareDataStructures(backendCourse, processedCourse) {
  console.log('\n📊 Step 5: Comparing Data Structures...');
  
  const comparison = {
    basicDataMatches: true,
    mediaDataMatches: true,
    urlsFormatted: true,
    issues: []
  };
  
  // Check basic course data
  if (backendCourse.Id !== processedCourse.Id) {
    comparison.basicDataMatches = false;
    comparison.issues.push('Course ID mismatch');
  }
  
  if (backendCourse.Title !== processedCourse.Title) {
    comparison.basicDataMatches = false;
    comparison.issues.push('Course title mismatch');
  }
  
  // Check cover image data
  if (backendCourse.CoverImage && processedCourse.CoverImage) {
    if (backendCourse.CoverImage.Id !== processedCourse.CoverImage.Id) {
      comparison.mediaDataMatches = false;
      comparison.issues.push('Cover image ID mismatch');
    }
    
    // Check if URL was properly formatted
    const originalUrl = backendCourse.CoverImage.Hashname;
    const processedUrl = processedCourse.CoverImage.Hashname;
    
    if (!processedUrl.startsWith('http://') && !processedUrl.startsWith('https://')) {
      comparison.urlsFormatted = false;
      comparison.issues.push('Cover image URL not properly formatted');
    }
    
    console.log('📸 Cover Image Comparison:', {
      originalHashname: originalUrl,
      processedHashname: processedUrl,
      properlyFormatted: processedUrl.startsWith('http')
    });
  }
  
  // Check video data
  if (backendCourse.PresentationVideo && processedCourse.PresentationVideo) {
    if (backendCourse.PresentationVideo.Id !== processedCourse.PresentationVideo.Id) {
      comparison.mediaDataMatches = false;
      comparison.issues.push('Video ID mismatch');
    }
    
    // Check if URL was properly formatted
    const originalUrl = backendCourse.PresentationVideo.Hashname;
    const processedUrl = processedCourse.PresentationVideo.Hashname;
    
    if (!processedUrl.startsWith('http://') && !processedUrl.startsWith('https://')) {
      comparison.urlsFormatted = false;
      comparison.issues.push('Video URL not properly formatted');
    }
    
    console.log('🎥 Video Comparison:', {
      originalHashname: originalUrl,
      processedHashname: processedUrl,
      properlyFormatted: processedUrl.startsWith('http')
    });
  }
  
  return comparison;
}

/**
 * Main Test Function
 */
async function runComparisonTest() {
  console.log('🧪 Frontend vs Backend Data Comparison Test');
  console.log('='.repeat(60));
  
  // Step 1: Get backend data
  const backendCourse = await getBackendData();
  if (!backendCourse) {
    console.log('❌ Cannot proceed without backend data');
    return;
  }
  
  // Step 2: Simulate frontend processing
  const processedCourse = simulateFrontendProcessing(backendCourse);
  if (!processedCourse) {
    console.log('❌ Cannot proceed without processed course data');
    return;
  }
  
  // Step 3: Test frontend API
  const frontendApiData = await testFrontendAPI();
  
  // Step 4: Verify media accessibility
  const accessibilityResults = await verifyMediaAccessibility(processedCourse);
  
  // Step 5: Compare data structures
  const comparison = compareDataStructures(backendCourse, processedCourse);
  
  // Final Summary
  console.log('\n📋 FINAL COMPARISON SUMMARY:');
  console.log('='.repeat(40));
  console.log(`✅ Backend Data Retrieved: YES`);
  console.log(`✅ Frontend Processing: YES`);
  console.log(`${frontendApiData ? '✅' : '❌'} Frontend API Call: ${frontendApiData ? 'SUCCESS' : 'FAILED'}`);
  console.log(`${comparison.basicDataMatches ? '✅' : '❌'} Basic Data Integrity: ${comparison.basicDataMatches ? 'PASSED' : 'FAILED'}`);
  console.log(`${comparison.mediaDataMatches ? '✅' : '❌'} Media Data Integrity: ${comparison.mediaDataMatches ? 'PASSED' : 'FAILED'}`);
  console.log(`${comparison.urlsFormatted ? '✅' : '❌'} URL Formatting: ${comparison.urlsFormatted ? 'PASSED' : 'FAILED'}`);
  console.log(`${accessibilityResults.coverImageAccessible ? '✅' : '❌'} Cover Image Accessible: ${accessibilityResults.coverImageAccessible ? 'YES' : 'NO'}`);
  console.log(`${accessibilityResults.videoAccessible ? '✅' : '❌'} Video Accessible: ${accessibilityResults.videoAccessible ? 'YES' : 'NO'}`);
  
  if (comparison.issues.length > 0) {
    console.log('\n⚠️ Issues Found:');
    comparison.issues.forEach(issue => console.log(`   - ${issue}`));
  }
  
  const allTestsPassed = comparison.basicDataMatches && 
                        comparison.mediaDataMatches && 
                        comparison.urlsFormatted && 
                        accessibilityResults.coverImageAccessible && 
                        accessibilityResults.videoAccessible;
  
  console.log('\n🎯 Overall Result:', allTestsPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED');
  
  // Provide URLs for manual testing
  console.log('\n🔗 URLs for Manual Testing:');
  console.log(`Frontend Course Page: http://localhost:5174/courses/${TEST_COURSE_SLUG}`);
  if (accessibilityResults.coverImageUrl) {
    console.log(`Cover Image: ${accessibilityResults.coverImageUrl}`);
  }
  if (accessibilityResults.videoUrl) {
    console.log(`Video: ${accessibilityResults.videoUrl}`);
  }
}

// Run the comparison test
runComparisonTest().catch(console.error);
