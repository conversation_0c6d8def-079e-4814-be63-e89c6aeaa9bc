const axios = require('axios');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3200/api';

async function testVideoFileInfo() {
  try {
    console.log('🎬 Testing Video File Information\n');

    // Get courses with videos
    const coursesResponse = await axios.get(`${BASE_URL}/courses/10/0`);
    const coursesWithVideos = coursesResponse.data.filter(course => course.PresentationVideo);
    
    if (coursesWithVideos.length === 0) {
      console.log('❌ No courses with presentation videos found');
      return;
    }

    console.log(`📊 Found ${coursesWithVideos.length} courses with videos\n`);

    for (const course of coursesWithVideos) {
      const video = course.PresentationVideo;
      console.log(`🎥 Course: ${course.Title}`);
      console.log(`   Video Name: ${video.Name}`);
      console.log(`   Video Hashname: ${video.Hashname}`);
      console.log(`   Video Size: ${video.Size} bytes`);
      console.log(`   Video Extension: ${video.Extension}`);

      // Test if the video URL is accessible
      const videoUrl = `http://localhost:3200/${video.Hashname}`;
      console.log(`   Testing URL: ${videoUrl}`);

      try {
        const response = await axios.head(videoUrl, { timeout: 5000 });
        console.log(`   ✅ HTTP Status: ${response.status}`);
        console.log(`   📄 Content-Type: ${response.headers['content-type']}`);
        console.log(`   📏 Content-Length: ${response.headers['content-length']} bytes`);
        console.log(`   🔒 Accept-Ranges: ${response.headers['accept-ranges'] || 'not supported'}`);
        
        // Check if it's a valid video MIME type
        const contentType = response.headers['content-type'];
        if (contentType && contentType.startsWith('video/')) {
          console.log(`   ✅ Valid video MIME type detected`);
        } else {
          console.log(`   ⚠️  Unexpected MIME type: ${contentType}`);
        }

        // Test if we can get a small chunk of the video to verify it's not corrupted
        try {
          const partialResponse = await axios.get(videoUrl, {
            headers: { 'Range': 'bytes=0-1023' }, // First 1KB
            timeout: 5000
          });
          console.log(`   ✅ Partial content accessible (${partialResponse.data.length} bytes)`);
        } catch (rangeError) {
          console.log(`   ⚠️  Range requests not supported or failed`);
        }

      } catch (error) {
        if (error.response) {
          console.log(`   ❌ HTTP Error: ${error.response.status} - ${error.response.statusText}`);
        } else {
          console.log(`   ❌ Network Error: ${error.message}`);
        }
      }

      console.log(''); // Empty line between courses
    }

    // Test a few common video URLs that should work
    console.log('🧪 Testing common video file patterns:');
    const testUrls = [
      'http://localhost:3200/upload/courses/videos/',
      'http://localhost:3200/public/upload/courses/videos/',
      'http://localhost:3200/static/upload/courses/videos/'
    ];

    for (const baseUrl of testUrls) {
      try {
        const response = await axios.get(baseUrl, { timeout: 3000 });
        console.log(`✅ ${baseUrl} - accessible (directory listing or file)`);
      } catch (error) {
        console.log(`❌ ${baseUrl} - ${error.response?.status || error.message}`);
      }
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testVideoFileInfo();
