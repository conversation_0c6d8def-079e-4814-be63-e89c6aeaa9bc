#!/usr/bin/env node

/**
 * COMPREHENSIVE BACKEND COURSE CREATION TEST
 * 
 * This script tests the complete backend functionality for course creation
 * with specific focus on file upload and data persistence workflow.
 * 
 * Test Coverage:
 * 1. Image Upload Backend Integration
 * 2. Data Persistence Verification  
 * 3. File-to-Course Relationship Validation
 * 4. End-to-End API Verification
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration
const BASE_URL = 'http://localhost:3200/api';
const TEST_IMAGE_PATH = './test-image.png';

// Test credentials (instructor account)
const INSTRUCTOR_CREDENTIALS = {
  username: '<EMAIL>',
  password: 'Admin@123',
  origin: 'http://localhost:5174'
};

// Test course data
const TEST_COURSE_DATA = {
  Title: 'Complete React Development Course - Backend Test',
  Resume: 'Learn React from basics to advanced concepts with hands-on projects, state management, routing, and real-world applications. This course tests the complete backend workflow including image upload and data persistence.',
  Keywords: ['react', 'javascript', 'frontend', 'backend-test'],
  Format: 2, // VIDEO
  Language: 'en',
  Level: [2], // Intermediate
  Categories: [1, 4], // Web Development, Data Science
  Goals: [
    'Master React hooks and functional components',
    'Build real-world applications with React',
    'Understand state management with Context API',
    'Implement routing with React Router',
    'Deploy React applications to production'
  ],
  Prerequisites: [
    'Basic JavaScript knowledge (ES6+)',
    'HTML and CSS fundamentals',
    'Understanding of DOM manipulation',
    'Familiarity with npm/yarn package manager'
  ],
  Free: false,
  Price: 99.99,
  NewPrice: 79.99,
  Currency: 'CAD',
  Message: 'Welcome to the Complete React Development Course! Get ready to master modern React development with hands-on projects and real-world applications.',
  Congratulation: 'Congratulations on completing the Complete React Development Course! You now have the skills and knowledge to build professional React applications.',
  Published: true,
  Archived: false
};

let authToken = null;
let instructorUser = null;

/**
 * Utility function to log test results
 */
function logTest(testName, status, details = '') {
  const statusIcon = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '🔍';
  console.log(`${statusIcon} ${testName}: ${status}`);
  if (details) {
    console.log(`   ${details}`);
  }
}

/**
 * Convert file to base64
 */
function fileToBase64(filePath) {
  try {
    const fileBuffer = fs.readFileSync(filePath);
    return fileBuffer.toString('base64');
  } catch (error) {
    throw new Error(`Failed to read file ${filePath}: ${error.message}`);
  }
}

/**
 * Step 1: Authenticate as instructor
 */
async function authenticateInstructor() {
  console.log('\n🔐 STEP 1: INSTRUCTOR AUTHENTICATION');
  
  try {
    const response = await axios.post(`${BASE_URL}/securities/login`, INSTRUCTOR_CREDENTIALS);

    if (response.data && response.data.Token) {
      authToken = response.data.Token;
      instructorUser = response.data.User;

      logTest('Instructor Authentication', 'PASS', `Logged in as: ${instructorUser.Firstname} ${instructorUser.Lastname} (${instructorUser.Email})`);
      logTest('Instructor Role Verification', instructorUser.Role === 3 ? 'PASS' : 'FAIL', `Role: ${instructorUser.Role} (Expected: 3 for instructor)`);

      return true;
    } else {
      logTest('Instructor Authentication', 'FAIL', 'No access token received');
      return false;
    }
  } catch (error) {
    logTest('Instructor Authentication', 'FAIL', error.response?.data?.message || error.message);
    return false;
  }
}

/**
 * Step 2: Test image upload backend integration
 */
async function testImageUpload() {
  console.log('\n🖼️ STEP 2: IMAGE UPLOAD BACKEND INTEGRATION');

  try {
    // Check if test image exists
    if (!fs.existsSync(TEST_IMAGE_PATH)) {
      logTest('Test Image File Check', 'FAIL', `File not found: ${TEST_IMAGE_PATH}`);
      return null;
    }

    logTest('Test Image File Check', 'PASS', `File found: ${TEST_IMAGE_PATH}`);

    // Convert image to base64
    const base64Data = fileToBase64(TEST_IMAGE_PATH);
    const fileStats = fs.statSync(TEST_IMAGE_PATH);

    logTest('Base64 Conversion', 'PASS', `File size: ${fileStats.size} bytes, Base64 length: ${base64Data.length}`);

    // Generate unique filename (backend behavior simulation)
    const timestamp = Date.now();
    const originalName = path.basename(TEST_IMAGE_PATH);
    const generatedFilename = `${timestamp}-${originalName}`;

    // STEP 2A: Test actual file saving with /api/utils/savefile
    console.log('   📁 Step 2A: Testing actual file saving with /api/utils/savefile...');

    const saveFileData = {
      File: base64Data,
      Filename: generatedFilename,
      SubDir: 'courses/covers'
    };

    let actualFileSaved = false;
    let savedFileUrl = null;

    try {
      const saveFileResponse = await axios.post(`${BASE_URL}/utils/savefile`, saveFileData, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        }
      });

      if (saveFileResponse.data) {
        actualFileSaved = true;
        savedFileUrl = `http://localhost:3200/public/upload/courses/covers/${generatedFilename}`;
        logTest('Actual File Saving', 'PASS', `File saved to disk: ${generatedFilename}`);

        // Test if the saved file is accessible
        try {
          const fileAccessResponse = await axios.head(savedFileUrl);
          logTest('Saved File Accessibility', fileAccessResponse.status === 200 ? 'PASS' : 'FAIL',
            `File accessible at: ${savedFileUrl}`);
        } catch (accessError) {
          logTest('Saved File Accessibility', 'FAIL', `File not accessible: ${accessError.message}`);
        }
      }
    } catch (saveError) {
      logTest('Actual File Saving', 'FAIL', `File save failed: ${saveError.message}`);
    }

    // STEP 2B: Test database record creation with /api/medias
    console.log('   📊 Step 2B: Testing database record creation with /api/medias...');

    const mediaData = {
      Name: generatedFilename,
      Hashname: base64Data,
      Extension: 'image/png',
      Size: fileStats.size,
      SubDir: 'courses/covers'
    };

    const uploadResponse = await axios.post(`${BASE_URL}/medias`, mediaData, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      }
    });

    if (uploadResponse.data && uploadResponse.data.Id) {
      const uploadedMedia = uploadResponse.data;

      logTest('Database Record Creation', 'PASS', `Media ID: ${uploadedMedia.Id}`);
      logTest('Filename Generation', 'PASS', `Original: ${originalName} → Generated: ${uploadedMedia.Hashname || uploadedMedia.Name}`);
      logTest('Database Storage', 'PASS', `Media record created with ID: ${uploadedMedia.Id}`);
      logTest('File Path Generation', 'PASS', `SubDir: ${uploadedMedia.SubDir}, Hashname: ${uploadedMedia.Hashname}`);

      // Add file saving status to the response
      uploadedMedia.actualFileSaved = actualFileSaved;
      uploadedMedia.savedFileUrl = savedFileUrl;

      return uploadedMedia;
    } else {
      logTest('Database Record Creation', 'FAIL', 'No media ID returned from upload');
      return null;
    }

  } catch (error) {
    logTest('Image Upload', 'FAIL', error.response?.data?.message || error.message);
    console.error('   Upload error details:', error.response?.data);
    return null;
  }
}

/**
 * Step 3: Test complete course creation with uploaded image
 */
async function testCourseCreation(uploadedMedia) {
  console.log('\n📚 STEP 3: COURSE CREATION WITH UPLOADED IMAGE');
  
  try {
    // Prepare course data with uploaded media
    const courseData = {
      ...TEST_COURSE_DATA,
      coverImageId: uploadedMedia ? uploadedMedia.Id : null,
      CreatedBy: {
        Id: instructorUser.Id
      }
    };
    
    console.log('   📝 Creating course with media relationship...');
    
    // Send course creation request (matching frontend format)
    const origin = 'http://localhost:5174';
    const courseResponse = await axios.post(`${BASE_URL}/courses`, {
      origin: origin,
      body: courseData
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    if (courseResponse.data && courseResponse.data.Id) {
      const createdCourse = courseResponse.data;
      
      logTest('Course Creation', 'PASS', `Course ID: ${createdCourse.Id}, Slug: ${createdCourse.Slug}`);
      logTest('Course Title', 'PASS', `Title: ${createdCourse.Title}`);
      logTest('Course Pricing', 'PASS', `Price: ${createdCourse.Currency} ${createdCourse.Price}`);
      logTest('Course Publication', createdCourse.Published ? 'PASS' : 'FAIL', `Published: ${createdCourse.Published}`);
      
      // Verify media relationship
      if (uploadedMedia && createdCourse.CoverImage) {
        const mediaMatch = createdCourse.CoverImage.Id === uploadedMedia.Id;
        logTest('Media-Course Relationship', mediaMatch ? 'PASS' : 'FAIL',
          `Course Cover Image ID: ${createdCourse.CoverImage.Id}, Uploaded Media ID: ${uploadedMedia.Id}`);
      } else if (uploadedMedia && createdCourse.coverImageId) {
        const mediaMatch = createdCourse.coverImageId === uploadedMedia.Id;
        logTest('Media-Course Relationship', mediaMatch ? 'PASS' : 'FAIL',
          `Course coverImageId: ${createdCourse.coverImageId}, Uploaded Media ID: ${uploadedMedia.Id}`);
      } else if (uploadedMedia) {
        logTest('Media-Course Relationship', 'FAIL', 'Cover image not attached to course');
      }
      
      return createdCourse;
    } else {
      logTest('Course Creation', 'FAIL', 'No course ID returned');
      return null;
    }
    
  } catch (error) {
    logTest('Course Creation', 'FAIL', error.response?.data?.message || error.message);
    console.error('   Course creation error details:', error.response?.data);
    return null;
  }
}

/**
 * Step 4: Verify data persistence by retrieving created course
 */
async function verifyDataPersistence(createdCourse) {
  console.log('\n🔍 STEP 4: DATA PERSISTENCE VERIFICATION');
  
  try {
    // Retrieve course by ID (more reliable than slug)
    console.log(`   📖 Retrieving course by ID: ${createdCourse.Id}`);

    const retrieveResponse = await axios.get(`${BASE_URL}/courses/10/0`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    // Find our created course in the list
    const courseList = retrieveResponse.data;
    const retrievedCourse = courseList.find(course => course.Id === createdCourse.Id);
    
    if (retrievedCourse) {
      
      logTest('Course Retrieval', 'PASS', `Retrieved course: ${retrievedCourse.Title}`);
      
      // Verify all data fields
      const dataChecks = [
        { field: 'Title', expected: TEST_COURSE_DATA.Title, actual: retrievedCourse.Title },
        { field: 'Price', expected: TEST_COURSE_DATA.Price, actual: retrievedCourse.Price },
        { field: 'Currency', expected: TEST_COURSE_DATA.Currency, actual: retrievedCourse.Currency },
        { field: 'Language', expected: TEST_COURSE_DATA.Language, actual: retrievedCourse.Language },
        { field: 'Published', expected: TEST_COURSE_DATA.Published, actual: retrievedCourse.Published }
      ];
      
      dataChecks.forEach(check => {
        const match = check.expected === check.actual;
        logTest(`${check.field} Persistence`, match ? 'PASS' : 'FAIL', 
          `Expected: ${check.expected}, Actual: ${check.actual}`);
      });
      
      // Verify cover image persistence
      if (retrievedCourse.CoverImage) {
        logTest('Cover Image Persistence', 'PASS',
          `Image ID: ${retrievedCourse.CoverImage.Id}, Hashname: ${retrievedCourse.CoverImage.Hashname}`);

        // Test multiple image URL accessibility patterns
        console.log('   🌐 Testing image accessibility with multiple URL patterns...');

        const urlPatterns = [
          `http://localhost:3200/public/${retrievedCourse.CoverImage.Hashname}`,
          `http://localhost:3200/public/upload/courses/covers/${retrievedCourse.CoverImage.Hashname}`,
          `http://localhost:3200/public/upload/${retrievedCourse.CoverImage.SubDir}/${retrievedCourse.CoverImage.Hashname}`
        ];

        let imageAccessible = false;
        let workingUrl = null;

        for (const imageUrl of urlPatterns) {
          try {
            console.log(`      Testing: ${imageUrl}`);
            const imageResponse = await axios.head(imageUrl);
            if (imageResponse.status === 200) {
              imageAccessible = true;
              workingUrl = imageUrl;
              logTest('Image File Accessibility', 'PASS', `Image accessible at: ${imageUrl}`);
              break;
            }
          } catch (imageError) {
            console.log(`      ❌ Failed: ${imageError.message}`);
          }
        }

        if (!imageAccessible) {
          logTest('Image File Accessibility', 'CRITICAL',
            'Image not accessible via any URL pattern - base64 data may not be converted to actual file');
        }

      } else {
        logTest('Cover Image Persistence', 'FAIL', 'No cover image found in retrieved course');
      }
      
      return true;
    } else {
      logTest('Course Retrieval', 'FAIL', `Course with ID ${createdCourse.Id} not found in course list`);
      return false;
    }
    
  } catch (error) {
    logTest('Data Persistence Verification', 'FAIL', error.response?.data?.message || error.message);
    return false;
  }
}

/**
 * Main test execution
 */
async function runComprehensiveTest() {
  console.log('🧪 COMPREHENSIVE BACKEND COURSE CREATION TEST');
  console.log('=' .repeat(60));
  
  try {
    // Step 1: Authenticate
    const authSuccess = await authenticateInstructor();
    if (!authSuccess) {
      console.log('\n❌ Test aborted: Authentication failed');
      return;
    }
    
    // Step 2: Upload image
    const uploadedMedia = await testImageUpload();
    
    // Step 3: Create course (with or without image)
    const createdCourse = await testCourseCreation(uploadedMedia);
    if (!createdCourse) {
      console.log('\n❌ Test aborted: Course creation failed');
      return;
    }
    
    // Step 4: Verify persistence
    const persistenceSuccess = await verifyDataPersistence(createdCourse);
    
    // Final summary
    console.log('\n📊 TEST SUMMARY');
    console.log('=' .repeat(60));
    console.log(`✅ Authentication: ${authSuccess ? 'PASSED' : 'FAILED'}`);
    console.log(`✅ Image Upload: ${uploadedMedia ? 'PASSED' : 'FAILED'}`);
    console.log(`✅ Course Creation: ${createdCourse ? 'PASSED' : 'FAILED'}`);
    console.log(`✅ Data Persistence: ${persistenceSuccess ? 'PASSED' : 'FAILED'}`);
    
    if (authSuccess && createdCourse && persistenceSuccess) {
      console.log('\n🎉 COMPREHENSIVE TEST COMPLETED SUCCESSFULLY!');
      console.log('   All backend functionality is working correctly.');
    } else {
      console.log('\n⚠️  Some tests failed. Please review the results above.');
    }
    
  } catch (error) {
    console.error('\n💥 UNEXPECTED ERROR:', error.message);
    console.error(error.stack);
  }
}

// Run the test
if (require.main === module) {
  runComprehensiveTest();
}

module.exports = {
  runComprehensiveTest,
  authenticateInstructor,
  testImageUpload,
  testCourseCreation,
  verifyDataPersistence
};
