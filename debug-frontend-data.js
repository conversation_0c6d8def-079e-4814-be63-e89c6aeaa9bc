const axios = require('axios');

/**
 * Debug what the frontend should be receiving after our fixes
 * This simulates the exact course service logic
 */

const API_BASE = 'http://localhost:3200/api';
const ENVIRONMENT_PATH = 'http://localhost:3200'; // Development environment

// Simulate the course service's constructMediaUrl method
function constructMediaUrl(hashname) {
  if (!hashname) return '';

  // If already a complete URL, return as-is
  if (hashname.startsWith('http://') || hashname.startsWith('https://')) {
    return hashname;
  }

  // Remove leading slash if present to avoid double slashes
  const cleanHashname = hashname.startsWith('/') ? hashname.substring(1) : hashname;

  // Backend serves static files directly from the root
  return `${ENVIRONMENT_PATH}/${cleanHashname}`;
}

// Simulate the course service's formatCourse method
function formatCourse(course) {
  if (!course) return course;

  // Format cover image URL
  if (course.CoverImage?.Hashname) {
    course.CoverImage.Hashname = constructMediaUrl(course.CoverImage.Hashname);
  }

  // Format presentation video URL
  if (course.PresentationVideo?.Hashname) {
    course.PresentationVideo.Hashname = constructMediaUrl(course.PresentationVideo.Hashname);
  }

  // Format creator photo
  if (course.CreatedBy?.Photo?.Hashname) {
    course.CreatedBy.Photo.Hashname = constructMediaUrl(course.CreatedBy.Photo.Hashname);
  }

  // Format categories photos
  if (course.Categories) {
    course.Categories = course.Categories.map(category => {
      if (category.Photo?.Hashname) {
        category.Photo.Hashname = constructMediaUrl(category.Photo.Hashname);
      }
      return category;
    });
  }

  return course;
}

// Simulate the course service's formatCourses method
function formatCourses(courses) {
  if (!Array.isArray(courses)) return [];
  return courses.map(course => formatCourse(course));
}

async function debugFrontendData() {
  console.log('🔍 Debug: What Frontend Should Receive After Our Fixes');
  console.log('=' .repeat(60));

  try {
    // Step 1: Get raw data from API (what backend returns)
    console.log('\n1️⃣ Raw API Response (what backend returns):');
    const response = await axios.get(`${API_BASE}/courses/15/0?justPublished=true`);
    const rawCourses = response.data;
    
    const targetCourse = rawCourses.find(course => 
      course.Title && course.Title.toLowerCase().includes('demo course two')
    );

    if (targetCourse) {
      console.log(`✅ Found target course: "${targetCourse.Title}"`);
      console.log(`   - Raw CoverImage.Hashname: "${targetCourse.CoverImage?.Hashname || 'NONE'}"`);
      console.log(`   - Raw CreatedBy.Photo.Hashname: "${targetCourse.CreatedBy?.Photo?.Hashname || 'NONE'}"`);
    }

    // Step 2: Apply our course service formatting
    console.log('\n2️⃣ After Course Service Formatting (what frontend should get):');
    const formattedCourses = formatCourses(rawCourses);
    const formattedTargetCourse = formattedCourses.find(course => 
      course.Title && course.Title.toLowerCase().includes('demo course two')
    );

    if (formattedTargetCourse) {
      console.log(`✅ Formatted target course: "${formattedTargetCourse.Title}"`);
      console.log(`   - Formatted CoverImage.Hashname: "${formattedTargetCourse.CoverImage?.Hashname || 'NONE'}"`);
      console.log(`   - Formatted CreatedBy.Photo.Hashname: "${formattedTargetCourse.CreatedBy?.Photo?.Hashname || 'NONE'}"`);
      
      // Step 3: Test if the formatted URLs work
      console.log('\n3️⃣ Testing Formatted URLs:');
      
      if (formattedTargetCourse.CoverImage?.Hashname) {
        try {
          const imageResponse = await axios.head(formattedTargetCourse.CoverImage.Hashname, { timeout: 5000 });
          console.log(`   ✅ Cover image URL works: ${imageResponse.status}`);
          console.log(`      URL: ${formattedTargetCourse.CoverImage.Hashname}`);
        } catch (error) {
          console.log(`   ❌ Cover image URL failed: ${error.response?.status || 'Network Error'}`);
          console.log(`      URL: ${formattedTargetCourse.CoverImage.Hashname}`);
        }
      }

      if (formattedTargetCourse.CreatedBy?.Photo?.Hashname) {
        try {
          const photoResponse = await axios.head(formattedTargetCourse.CreatedBy.Photo.Hashname, { timeout: 5000 });
          console.log(`   ✅ Creator photo URL works: ${photoResponse.status}`);
          console.log(`      URL: ${formattedTargetCourse.CreatedBy.Photo.Hashname}`);
        } catch (error) {
          console.log(`   ❌ Creator photo URL failed: ${error.response?.status || 'Network Error'}`);
          console.log(`      URL: ${formattedTargetCourse.CreatedBy.Photo.Hashname}`);
        }
      }
    }

    // Step 4: What CourseCard component should receive
    console.log('\n4️⃣ What CourseCard Component Should Receive:');
    if (formattedTargetCourse) {
      const courseCardData = {
        Id: formattedTargetCourse.Id,
        Title: formattedTargetCourse.Title,
        Slug: formattedTargetCourse.Slug,
        Price: formattedTargetCourse.Price,
        NewPrice: formattedTargetCourse.NewPrice,
        Free: formattedTargetCourse.Free,
        CoverImage: formattedTargetCourse.CoverImage, // This should have the formatted URL
        CreatedBy: formattedTargetCourse.CreatedBy ? {
          FirstName: formattedTargetCourse.CreatedBy.Firstname,
          LastName: formattedTargetCourse.CreatedBy.Lastname,
          Photo: formattedTargetCourse.CreatedBy.Photo // This should have the formatted URL
        } : undefined
      };

      console.log('   📦 CourseCard props:');
      console.log(`      - CoverImage.Hashname: "${courseCardData.CoverImage?.Hashname || 'NONE'}"`);
      console.log(`      - CreatedBy.Photo.Hashname: "${courseCardData.CreatedBy?.Photo?.Hashname || 'NONE'}"`);
      
      // Step 5: What getCourseImage() should return
      console.log('\n5️⃣ What getCourseImage() Should Return:');
      const courseImageUrl = courseCardData.CoverImage?.Hashname;
      console.log(`   📸 getCourseImage() result: "${courseImageUrl || 'undefined'}"`);
      
      if (courseImageUrl) {
        console.log(`   🎯 This URL should display the image in the browser`);
      } else {
        console.log(`   ⚠️  No image URL - will show placeholder`);
      }
    }

  } catch (error) {
    console.error('💥 Debug failed:', error.message);
  }

  console.log('\n🎉 Debug completed!');
  console.log('\n💡 If the URLs work here but not in the frontend:');
  console.log('   1. Frontend might be using cached code - restart the dev server');
  console.log('   2. Check browser console for any JavaScript errors');
  console.log('   3. Verify the CourseCard component is receiving the formatted data');
}

// Run the debug
debugFrontendData().catch(console.error);
