/**
 * Test the working format of /api/medias endpoint
 * Verify that it properly saves files and creates database records
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3200/api';
const TEST_IMAGE_BASE64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
const TEST_VIDEO_BASE64 = 'AAAAIGZ0eXBpc29tAAACAGlzb21pc28yYXZjMW1wNDEAAAAIZnJlZQAAAr1tZGF0';

async function testWorkingMediasFormat() {
  console.log('🧪 Testing Working /api/medias Format');
  console.log('=' .repeat(60));

  try {
    // Test 1: Cover Image with working format
    console.log('\n1️⃣ Creating cover image media record...');
    const coverImageName = `test-cover-${Date.now()}.png`;
    const coverResponse = await axios.post(`${BASE_URL}/medias`, {
      Name: coverImageName,
      Hashname: TEST_IMAGE_BASE64,
      Extension: 'image/png',
      Size: 32,
      SubDir: 'courses/covers'
    });
    
    console.log('✅ Cover image media created:', {
      Id: coverResponse.data.Id,
      Name: coverResponse.data.Name,
      Hashname: coverResponse.data.Hashname,
      SubDir: coverResponse.data.SubDir,
      Extension: coverResponse.data.Extension
    });

    // Test 2: Presentation Video with working format
    console.log('\n2️⃣ Creating presentation video media record...');
    const videoName = `test-video-${Date.now()}.mp4`;
    const videoResponse = await axios.post(`${BASE_URL}/medias`, {
      Name: videoName,
      Hashname: TEST_VIDEO_BASE64,
      Extension: 'video/mp4',
      Size: 64,
      SubDir: 'courses/videos'
    });
    
    console.log('✅ Video media created:', {
      Id: videoResponse.data.Id,
      Name: videoResponse.data.Name,
      Hashname: videoResponse.data.Hashname,
      SubDir: videoResponse.data.SubDir,
      Extension: videoResponse.data.Extension
    });

    // Test 3: Create course using these media objects
    console.log('\n3️⃣ Creating course with media objects...');
    const testCourse = {
      Title: `Test Course with Working Media ${Date.now()}`,
      Resume: 'This course uses the working /api/medias endpoint format.',
      Keywords: ['test', 'medias', 'working'],
      Price: 0,
      Language: 'en',
      Published: false,
      Archived: false,
      Free: true,
      Level: [1],
      Format: 2,
      Prerequisites: ['Basic knowledge'],
      Goals: ['Test working media format'],
      Message: 'Welcome!',
      Congratulation: 'Well done!',
      Currency: 'USD',
      CoverImage: {
        Id: coverResponse.data.Id,
        Name: coverResponse.data.Name,
        Hashname: coverResponse.data.Hashname,
        Extension: coverResponse.data.Extension,
        Size: coverResponse.data.Size,
        SubDir: coverResponse.data.SubDir,
        Slug: coverResponse.data.Slug
      },
      PresentationVideo: {
        Id: videoResponse.data.Id,
        Name: videoResponse.data.Name,
        Hashname: videoResponse.data.Hashname,
        Extension: videoResponse.data.Extension,
        Size: videoResponse.data.Size,
        SubDir: videoResponse.data.SubDir,
        Slug: videoResponse.data.Slug
      },
      Categories: [],
      CreatedBy: { Id: 1 }
    };

    const courseResponse = await axios.post(`${BASE_URL}/courses`, {
      origin: 'http://localhost:5174',
      body: testCourse
    });

    console.log('✅ Course created with working media format!');
    console.log('📊 Course details:');
    console.log('   - ID:', courseResponse.data.Id);
    console.log('   - Slug:', courseResponse.data.Slug);
    console.log('   - Title:', courseResponse.data.Title);

    // Verify media objects in course
    if (courseResponse.data.CoverImage) {
      console.log('✅ Cover Image in course:');
      console.log('   - ID:', courseResponse.data.CoverImage.Id);
      console.log('   - Hashname:', courseResponse.data.CoverImage.Hashname);
      console.log('   - SubDir:', courseResponse.data.CoverImage.SubDir);
    }

    if (courseResponse.data.PresentationVideo) {
      console.log('✅ Presentation Video in course:');
      console.log('   - ID:', courseResponse.data.PresentationVideo.Id);
      console.log('   - Hashname:', courseResponse.data.PresentationVideo.Hashname);
      console.log('   - SubDir:', courseResponse.data.PresentationVideo.SubDir);
    }

    // Test 4: Verify files were saved
    console.log('\n4️⃣ Verifying files were saved...');
    await verifyFilesSaved(coverResponse.data, videoResponse.data);

    return {
      success: true,
      coverMedia: coverResponse.data,
      videoMedia: videoResponse.data,
      course: courseResponse.data
    };

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    return {
      success: false,
      error: error.response?.data || error.message
    };
  }
}

async function verifyFilesSaved(coverMedia, videoMedia) {
  const uploadDir = path.join(__dirname, 'backend/public/upload');
  
  try {
    // Check if files exist in the expected locations
    const coverPath = path.join(uploadDir, coverMedia.SubDir, coverMedia.Hashname);
    const videoPath = path.join(uploadDir, videoMedia.SubDir, videoMedia.Hashname);
    
    console.log('🔍 Checking file locations:');
    console.log('   - Cover expected at:', coverPath);
    console.log('   - Video expected at:', videoPath);
    
    if (fs.existsSync(coverPath)) {
      console.log('✅ Cover image file found!');
    } else {
      console.log('❌ Cover image file NOT found');
      
      // Check if it's in a different location
      const coversDir = path.join(uploadDir, 'courses/covers');
      if (fs.existsSync(coversDir)) {
        const files = fs.readdirSync(coversDir);
        console.log('📁 Files in courses/covers:', files.slice(-3));
      }
    }
    
    if (fs.existsSync(videoPath)) {
      console.log('✅ Video file found!');
    } else {
      console.log('❌ Video file NOT found');
      
      // Check if it's in a different location
      const videosDir = path.join(uploadDir, 'courses/videos');
      if (fs.existsSync(videosDir)) {
        const files = fs.readdirSync(videosDir);
        console.log('📁 Files in courses/videos:', files.slice(-3));
      }
    }
    
  } catch (error) {
    console.error('❌ Error verifying files:', error.message);
  }
}

async function runWorkingFormatTest() {
  console.log('🚀 Testing Working /api/medias Format');
  console.log('Testing the format that actually works for media creation');
  
  const result = await testWorkingMediasFormat();
  
  console.log('\n📊 FINAL RESULTS');
  console.log('=' .repeat(60));
  
  if (result.success) {
    console.log('🎉 SUCCESS: Working /api/medias format confirmed!');
    console.log('\n📝 WORKING FORMAT:');
    console.log('   POST /api/medias');
    console.log('   {');
    console.log('     "Name": "filename.ext",');
    console.log('     "Hashname": "base64Data",');
    console.log('     "Extension": "image/png",');
    console.log('     "Size": 32,');
    console.log('     "SubDir": "courses/covers"');
    console.log('   }');
    
    console.log('\n🎯 RECOMMENDATION:');
    console.log('   ✅ Use /api/medias endpoint with Name field (not Username)');
    console.log('   ✅ Creates proper database records with IDs');
    console.log('   ✅ Handles file storage automatically');
    console.log('   ✅ Works with course creation');
    
  } else {
    console.log('❌ FAILED: Working format test failed');
    console.log('Error:', result.error);
  }
}

runWorkingFormatTest().catch(console.error);
