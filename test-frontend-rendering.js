const puppeteer = require('puppeteer');

/**
 * Test if the frontend is actually rendering the cover images
 * This will open the browser and check the actual DOM elements
 */

async function testFrontendRendering() {
  console.log('🔍 Testing Frontend Image Rendering');
  console.log('=' .repeat(50));

  let browser;
  let page;

  try {
    // Launch browser
    console.log('\n🚀 Launching browser...');
    browser = await puppeteer.launch({ 
      headless: false, // Set to true for headless mode
      defaultViewport: { width: 1280, height: 720 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    page = await browser.newPage();
    
    // Enable console logging from the page
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log('🔴 Browser Error:', msg.text());
      } else if (msg.type() === 'warn') {
        console.log('🟡 Browser Warning:', msg.text());
      }
    });

    // Test 1: Navigate to courses page
    console.log('\n📚 Testing Courses Page...');
    await page.goto('http://localhost:5174/courses', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });

    // Wait for courses to load
    console.log('⏳ Waiting for courses to load...');
    await page.waitForSelector('[data-testid="course-card"], .course-card, img[alt*="course"], img[src*="upload"]', { 
      timeout: 15000 
    }).catch(() => {
      console.log('⚠️  No course cards found with standard selectors, trying generic approach...');
    });

    // Check for our target course
    console.log('\n🎯 Looking for target course...');
    const targetCourseFound = await page.evaluate(() => {
      // Look for the specific course title
      const titleElements = document.querySelectorAll('h1, h2, h3, h4, h5, h6, p, span, div');
      for (let element of titleElements) {
        if (element.textContent && element.textContent.toLowerCase().includes('demo course two')) {
          return {
            found: true,
            title: element.textContent.trim(),
            element: element.tagName
          };
        }
      }
      return { found: false };
    });

    if (targetCourseFound.found) {
      console.log(`✅ Target course found: "${targetCourseFound.title}"`);
    } else {
      console.log('❌ Target course not found on page');
    }

    // Test 2: Check all images on the page
    console.log('\n🖼️  Analyzing all images on the page...');
    const imageAnalysis = await page.evaluate(() => {
      const images = Array.from(document.querySelectorAll('img'));
      return images.map(img => ({
        src: img.src,
        alt: img.alt || '',
        naturalWidth: img.naturalWidth,
        naturalHeight: img.naturalHeight,
        complete: img.complete,
        className: img.className,
        id: img.id,
        parentText: img.parentElement ? img.parentElement.textContent.substring(0, 50) : ''
      }));
    });

    console.log(`📊 Found ${imageAnalysis.length} images on the page:`);
    
    let coverImagesFound = 0;
    let workingCoverImages = 0;
    
    imageAnalysis.forEach((img, index) => {
      console.log(`\n   ${index + 1}. Image Analysis:`);
      console.log(`      - Source: ${img.src}`);
      console.log(`      - Alt: ${img.alt}`);
      console.log(`      - Loaded: ${img.complete ? '✅' : '❌'}`);
      console.log(`      - Dimensions: ${img.naturalWidth}x${img.naturalHeight}`);
      console.log(`      - Context: ${img.parentText.substring(0, 30)}...`);
      
      // Check if this looks like a course cover image
      if (img.src.includes('upload/courses/covers') || 
          img.alt.toLowerCase().includes('course') ||
          img.src.includes('fdce5674de1b42b78431413ddf88c29c')) {
        coverImagesFound++;
        console.log(`      🎯 This appears to be a course cover image!`);
        
        if (img.complete && img.naturalWidth > 0) {
          workingCoverImages++;
          console.log(`      ✅ Image loaded successfully!`);
        } else {
          console.log(`      ❌ Image failed to load`);
        }
      }
    });

    console.log(`\n📈 Cover Image Summary:`);
    console.log(`   - Total images: ${imageAnalysis.length}`);
    console.log(`   - Course cover images found: ${coverImagesFound}`);
    console.log(`   - Working cover images: ${workingCoverImages}`);

    // Test 3: Check for broken images
    console.log('\n🔍 Checking for broken images...');
    const brokenImages = imageAnalysis.filter(img => 
      !img.complete || img.naturalWidth === 0
    );

    if (brokenImages.length > 0) {
      console.log(`❌ Found ${brokenImages.length} broken images:`);
      brokenImages.forEach((img, index) => {
        console.log(`   ${index + 1}. ${img.src}`);
      });
    } else {
      console.log('✅ No broken images found');
    }

    // Test 4: Check network requests
    console.log('\n🌐 Checking network requests for images...');
    const responses = [];
    
    page.on('response', response => {
      if (response.url().includes('upload/courses/covers') || 
          response.url().includes('fdce5674de1b42b78431413ddf88c29c')) {
        responses.push({
          url: response.url(),
          status: response.status(),
          statusText: response.statusText()
        });
      }
    });

    // Reload page to capture network requests
    await page.reload({ waitUntil: 'networkidle2' });
    
    // Wait a bit for network requests to complete
    await page.waitForTimeout(3000);

    if (responses.length > 0) {
      console.log(`📡 Network requests for cover images:`);
      responses.forEach((response, index) => {
        console.log(`   ${index + 1}. ${response.status} ${response.statusText}: ${response.url}`);
      });
    } else {
      console.log('⚠️  No network requests detected for cover images');
    }

    // Test 5: Check console errors
    console.log('\n🔍 Checking for JavaScript errors...');
    const jsErrors = [];
    page.on('pageerror', error => {
      jsErrors.push(error.message);
    });

    if (jsErrors.length > 0) {
      console.log(`❌ JavaScript errors found:`);
      jsErrors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    } else {
      console.log('✅ No JavaScript errors detected');
    }

    // Test 6: Take a screenshot for manual verification
    console.log('\n📸 Taking screenshot for manual verification...');
    await page.screenshot({ 
      path: 'frontend-courses-page.png', 
      fullPage: true 
    });
    console.log('✅ Screenshot saved as: frontend-courses-page.png');

  } catch (error) {
    console.error('💥 Frontend test failed:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }

  console.log('\n🎉 Frontend rendering test completed!');
  console.log('\n💡 Next steps:');
  console.log('   1. Check the screenshot: frontend-courses-page.png');
  console.log('   2. If images are still not showing, check browser dev tools');
  console.log('   3. Verify the course service is being called correctly');
}

// Check if puppeteer is available
async function checkPuppeteer() {
  try {
    require('puppeteer');
    return true;
  } catch (error) {
    console.log('❌ Puppeteer not found. Installing...');
    console.log('   Run: npm install puppeteer');
    console.log('   Or use manual testing in browser at: http://localhost:5174/courses');
    return false;
  }
}

// Run the test
checkPuppeteer().then(available => {
  if (available) {
    testFrontendRendering().catch(console.error);
  }
});
