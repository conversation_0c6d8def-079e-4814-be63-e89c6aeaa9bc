/**
 * Debug script to understand what's happening with media creation during course creation
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3200/api';

async function apiRequest(method, endpoint, data = null) {
  const config = {
    method,
    url: `${API_BASE_URL}${endpoint}`,
    headers: {
      'Content-Type': 'application/json'
    }
  };
  
  if (data) {
    config.data = data;
  }
  
  try {
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`API Error (${method} ${endpoint}):`, error.response?.data || error.message);
    throw error;
  }
}

async function debugMediaCreation() {
  console.log('🔍 Debug: Media Creation During Course Creation');
  console.log('=' .repeat(60));
  
  // Step 1: Upload media to centralized directories
  console.log('\n1️⃣ Uploading cover image to centralized directory...');
  const coverImageData = {
    Name: 'debug-cover.png',
    Hashname: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==',
    Extension: 'image/png',
    Size: 1000,
    SubDir: 'courses/covers'
  };
  
  const uploadedCover = await apiRequest('POST', '/medias', coverImageData);
  console.log('✅ Cover uploaded:', {
    id: uploadedCover.Id,
    hashname: uploadedCover.Hashname,
    subDir: uploadedCover.SubDir
  });
  
  console.log('\n2️⃣ Uploading video to centralized directory...');
  const videoData = {
    Name: 'debug-video.mp4',
    Hashname: 'ZGF0YTp2aWRlby9tcDQ7YmFzZTY0LGRhdGE=',
    Extension: 'video/mp4',
    Size: 5000,
    SubDir: 'courses/videos'
  };
  
  const uploadedVideo = await apiRequest('POST', '/medias', videoData);
  console.log('✅ Video uploaded:', {
    id: uploadedVideo.Id,
    hashname: uploadedVideo.Hashname,
    subDir: uploadedVideo.SubDir
  });
  
  // Step 2: Check all media before course creation
  console.log('\n3️⃣ Checking all media before course creation...');
  const allMediaBefore = await apiRequest('GET', '/medias');
  const relevantMediaBefore = allMediaBefore.filter(m => 
    m.Id === uploadedCover.Id || m.Id === uploadedVideo.Id
  );
  console.log('📊 Relevant media before course creation:', relevantMediaBefore.map(m => ({
    id: m.Id,
    hashname: m.Hashname,
    subDir: m.SubDir
  })));
  
  // Step 3: Create course with media ID references
  console.log('\n4️⃣ Creating course with media ID references...');
  const coursePayload = {
    body: {
      Title: 'Debug Media Course',
      Resume: 'Testing media creation during course creation',
      Keywords: ['debug'],
      Format: 2,
      Language: 'en',
      Free: true,
      Level: [1],
      Message: 'Debug message',
      Congratulation: 'Debug congratulation',
      Categories: [],
      Published: false,
      Archived: false,
      coverImageId: uploadedCover.Id,
      presentationVideoId: uploadedVideo.Id,
      CreatedBy: { Id: 1 }
    },
    origin: 'http://localhost:3000'
  };
  
  console.log('📋 Course payload:', {
    coverImageId: coursePayload.body.coverImageId,
    presentationVideoId: coursePayload.body.presentationVideoId,
    hasCoverImageObject: !!coursePayload.body.CoverImage,
    hasPresentationVideoObject: !!coursePayload.body.PresentationVideo
  });
  
  const createdCourse = await apiRequest('POST', '/courses', coursePayload);
  console.log('✅ Course created:', {
    id: createdCourse.Id,
    slug: createdCourse.Slug,
    title: createdCourse.Title
  });
  
  // Step 4: Check all media after course creation
  console.log('\n5️⃣ Checking all media after course creation...');
  const allMediaAfter = await apiRequest('GET', '/medias');
  const newMedia = allMediaAfter.filter(m => 
    !allMediaBefore.some(before => before.Id === m.Id)
  );
  
  if (newMedia.length > 0) {
    console.log('🚨 NEW MEDIA CREATED during course creation:');
    newMedia.forEach(m => {
      console.log(`   ID: ${m.Id}, Hashname: ${m.Hashname}, SubDir: ${m.SubDir}`);
    });
  } else {
    console.log('✅ No new media created during course creation');
  }
  
  // Step 5: Retrieve the course to see what media it references
  console.log('\n6️⃣ Retrieving course to check media references...');
  const retrievedCourse = await apiRequest('GET', `/courses/public/${createdCourse.Slug}`);
  
  console.log('📊 Course media references:');
  if (retrievedCourse.CoverImage) {
    console.log(`   Cover Image: ID ${retrievedCourse.CoverImage.Id}, SubDir: ${retrievedCourse.CoverImage.SubDir}`);
    console.log(`   Cover Hashname: ${retrievedCourse.CoverImage.Hashname}`);
  }
  
  if (retrievedCourse.PresentationVideo) {
    console.log(`   Video: ID ${retrievedCourse.PresentationVideo.Id}, SubDir: ${retrievedCourse.PresentationVideo.SubDir}`);
    console.log(`   Video Hashname: ${retrievedCourse.PresentationVideo.Hashname}`);
  }
  
  // Step 6: Analysis
  console.log('\n7️⃣ Analysis:');
  const coverMatches = retrievedCourse.CoverImage && retrievedCourse.CoverImage.Id === uploadedCover.Id;
  const videoMatches = retrievedCourse.PresentationVideo && retrievedCourse.PresentationVideo.Id === uploadedVideo.Id;
  
  console.log(`   Cover image ID matches: ${coverMatches ? '✅ YES' : '❌ NO'}`);
  console.log(`   Video ID matches: ${videoMatches ? '✅ YES' : '❌ NO'}`);
  
  if (coverMatches && videoMatches) {
    console.log('✅ Course is using the original centralized media!');
    
    const coverCentralized = retrievedCourse.CoverImage.SubDir === 'courses/covers';
    const videoCentralized = retrievedCourse.PresentationVideo.SubDir === 'courses/videos';
    
    console.log(`   Cover in centralized dir: ${coverCentralized ? '✅ YES' : '❌ NO'}`);
    console.log(`   Video in centralized dir: ${videoCentralized ? '✅ YES' : '❌ NO'}`);
    
    if (coverCentralized && videoCentralized) {
      console.log('🎉 SUCCESS: Centralized media structure is working!');
    } else {
      console.log('❌ ISSUE: Media IDs match but directories are not centralized');
    }
  } else {
    console.log('❌ ISSUE: Course is not using the original media - backend created duplicates');
  }
}

debugMediaCreation().catch(console.error);
