#!/usr/bin/env node

/**
 * Test Script for Instructor Application Submission
 * This script tests the instructor application form submission and admin dashboard integration
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3200/api';

// Test application data
const testApplicationData = {
  FirstName: 'John',
  LastName: 'TestInstructor',
  Email: '<EMAIL>',
  Phone: '+1234567890',
  Country: 'United States',
  City: 'New York',
  Title: 'Senior Software Engineer & Tech Educator',
  Bio: 'I am a passionate software engineer with over 8 years of experience in full-stack development. I have worked with various technologies including React, Node.js, Python, and cloud platforms. I love teaching and have mentored many junior developers throughout my career. I believe in making complex concepts simple and accessible to everyone.',
  Experience: 'Expert (5+ years)',
  Expertise: ['JavaScript', 'React', 'Node.js', 'Python', 'Cloud Computing', 'Database Design'],
  LinkedinUrl: 'https://linkedin.com/in/johntestinstructor',
  WebsiteUrl: 'https://johntestinstructor.dev',
  Education: [],
  TeachingExperience: 'I have been teaching programming concepts to junior developers and interns for the past 3 years. I have conducted workshops on modern web development, mentored over 20 developers, and created internal training materials for my company.',
  PreviousPlatforms: '',
  SampleCourseOutline: 'Course: "Modern React Development"\n\nModule 1: React Fundamentals\n- Components and JSX\n- Props and State\n- Event Handling\n\nModule 2: Advanced React\n- Hooks (useState, useEffect, custom hooks)\n- Context API\n- Performance optimization\n\nModule 3: React Ecosystem\n- React Router\n- State management (Redux/Zustand)\n- Testing with Jest and React Testing Library\n\nModule 4: Real-world Project\n- Building a complete e-commerce application\n- Deployment and best practices'
};

// Admin login credentials (from test users)
const adminCredentials = {
  email: '<EMAIL>',
  password: 'password123' // This would be the actual password
};

async function submitInstructorApplication() {
  try {
    console.log('🚀 Testing Instructor Application Submission...\n');
    
    console.log('📝 Submitting application data:');
    console.log(`Name: ${testApplicationData.FirstName} ${testApplicationData.LastName}`);
    console.log(`Email: ${testApplicationData.Email}`);
    console.log(`Experience: ${testApplicationData.Experience}`);
    console.log(`Expertise: ${testApplicationData.Expertise.join(', ')}`);
    console.log('');
    
    // Submit the instructor application
    const response = await axios.post(`${API_BASE_URL}/instructor-applications`, testApplicationData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Application submitted successfully!');
    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(response.data, null, 2));
    
    return response.data;
    
  } catch (error) {
    console.error('❌ Error submitting application:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
    throw error;
  }
}

async function loginAsAdmin() {
  try {
    console.log('\n🔐 Attempting admin login...');
    
    const loginResponse = await axios.post(`${API_BASE_URL}/securities/login`, {
      Email: adminCredentials.email,
      Password: adminCredentials.password
    });
    
    console.log('✅ Admin login successful!');
    return loginResponse.data.token || loginResponse.data.access_token;
    
  } catch (error) {
    console.error('❌ Admin login failed:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
    return null;
  }
}

async function checkAdminDashboard(token) {
  try {
    console.log('\n📊 Checking admin dashboard for applications...');
    
    const headers = token ? {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    } : {
      'Content-Type': 'application/json'
    };
    
    // Get all instructor applications
    const applicationsResponse = await axios.get(`${API_BASE_URL}/instructor-applications`, {
      headers
    });
    
    console.log('✅ Successfully retrieved applications from admin dashboard!');
    console.log('Total applications:', applicationsResponse.data.length);
    
    // Find our test application
    const testApplication = applicationsResponse.data.find(app => 
      app.Email === testApplicationData.Email
    );
    
    if (testApplication) {
      console.log('\n🎯 Found our test application:');
      console.log(`ID: ${testApplication.Id}`);
      console.log(`Name: ${testApplication.FirstName} ${testApplication.LastName}`);
      console.log(`Email: ${testApplication.Email}`);
      console.log(`Status: ${testApplication.Status}`);
      console.log(`Created: ${testApplication.CreatedAt}`);
      console.log('✅ Test application successfully appears in admin dashboard!');
    } else {
      console.log('⚠️ Test application not found in admin dashboard');
    }
    
    return applicationsResponse.data;
    
  } catch (error) {
    console.error('❌ Error checking admin dashboard:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
    return null;
  }
}

async function runFullTest() {
  console.log('🧪 BrainMaker Academy - Instructor Application Test\n');
  console.log('=' .repeat(60));
  
  try {
    // Step 1: Submit instructor application
    const applicationResult = await submitInstructorApplication();
    
    // Step 2: Try to login as admin (optional, might fail if password is different)
    const adminToken = await loginAsAdmin();
    
    // Step 3: Check admin dashboard
    const dashboardData = await checkAdminDashboard(adminToken);
    
    console.log('\n' + '=' .repeat(60));
    console.log('🎉 TEST COMPLETED SUCCESSFULLY!');
    console.log('✅ Application submitted and appears in admin dashboard');
    console.log('✅ Email notifications should have been sent');
    console.log('✅ Admin can review and approve/reject the application');
    
  } catch (error) {
    console.log('\n' + '=' .repeat(60));
    console.log('❌ TEST FAILED');
    console.error('Error:', error.message);
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  runFullTest();
}

module.exports = {
  submitInstructorApplication,
  loginAsAdmin,
  checkAdminDashboard,
  runFullTest
};
