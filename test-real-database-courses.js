/**
 * Test Real Database Courses
 * This script fetches actual course data from the database and tests the complete
 * frontend-backend integration with real data instead of mock test data
 */

const axios = require('axios');
const mysql = require('mysql2/promise');
require('dotenv').config({ path: './backend/.env' });

const API_BASE_URL = 'http://localhost:3200/api';

// Database configuration from environment
const DB_CONFIG = {
  host: process.env.DEV_DB_HOSTNAME || 'localhost',
  user: process.env.DEV_DB_USERNAME || 'root',
  password: process.env.DEV_DB_PASSWORD || '',
  database: process.env.DEV_DB_NAME || 'brainmaker',
  port: process.env.DEV_DB_PORT || 3306
};

/**
 * Get database connection
 */
async function getDatabaseConnection() {
  try {
    const connection = await mysql.createConnection(DB_CONFIG);
    console.log('✅ Database connection established');
    return connection;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    throw error;
  }
}

/**
 * Fetch all courses from database with media information
 */
async function fetchRealCoursesFromDatabase() {
  console.log('🔍 Step 1: Fetching real courses from database...');
  
  const connection = await getDatabaseConnection();
  
  try {
    const [courses] = await connection.execute(`
      SELECT 
        c.Id,
        c.Slug,
        c.Title,
        c.Resume,
        c.Price,
        c.NewPrice,
        c.Free,
        c.Published,
        c.Archived,
        c.CreatedAt,
        c.UpdatedAt,
        c.CoverImageId,
        c.PresentationVideoId,
        
        -- Cover Image details
        ci.Id as CoverImage_Id,
        ci.Name as CoverImage_Name,
        ci.Hashname as CoverImage_Hashname,
        ci.SubDir as CoverImage_SubDir,
        ci.Extension as CoverImage_Extension,
        ci.Size as CoverImage_Size,
        
        -- Presentation Video details
        pv.Id as PresentationVideo_Id,
        pv.Name as PresentationVideo_Name,
        pv.Hashname as PresentationVideo_Hashname,
        pv.SubDir as PresentationVideo_SubDir,
        pv.Extension as PresentationVideo_Extension,
        pv.Size as PresentationVideo_Size,
        
        -- Creator details
        u.Id as Creator_Id,
        u.Username as Creator_Username,
        u.Firstname as Creator_Firstname,
        u.Lastname as Creator_Lastname,
        u.Slug as Creator_Slug
        
      FROM courses c
      LEFT JOIN medias ci ON c.CoverImageId = ci.Id
      LEFT JOIN medias pv ON c.PresentationVideoId = pv.Id
      LEFT JOIN users u ON c.CreatedById = u.Id
      ORDER BY c.CreatedAt DESC
      LIMIT 10
    `);
    
    console.log(`✅ Found ${courses.length} courses in database`);
    
    // Transform the flat result into nested objects
    const transformedCourses = courses.map(row => ({
      Id: row.Id,
      Slug: row.Slug,
      Title: row.Title,
      Resume: row.Resume,
      Price: row.Price,
      NewPrice: row.NewPrice,
      Free: row.Free,
      Published: row.Published,
      Archived: row.Archived,
      CreatedAt: row.CreatedAt,
      UpdatedAt: row.UpdatedAt,
      CoverImageId: row.CoverImageId,
      PresentationVideoId: row.PresentationVideoId,
      CoverImage: row.CoverImage_Id ? {
        Id: row.CoverImage_Id,
        Name: row.CoverImage_Name,
        Hashname: row.CoverImage_Hashname,
        SubDir: row.CoverImage_SubDir,
        Extension: row.CoverImage_Extension,
        Size: row.CoverImage_Size
      } : null,
      PresentationVideo: row.PresentationVideo_Id ? {
        Id: row.PresentationVideo_Id,
        Name: row.PresentationVideo_Name,
        Hashname: row.PresentationVideo_Hashname,
        SubDir: row.PresentationVideo_SubDir,
        Extension: row.PresentationVideo_Extension,
        Size: row.PresentationVideo_Size
      } : null,
      CreatedBy: {
        Id: row.Creator_Id,
        Username: row.Creator_Username,
        Firstname: row.Creator_Firstname,
        Lastname: row.Creator_Lastname,
        Slug: row.Creator_Slug
      }
    }));
    
    return transformedCourses;
    
  } finally {
    await connection.end();
  }
}

/**
 * Test API endpoint for each real course
 */
async function testAPIEndpointsForRealCourses(courses) {
  console.log('\n🌐 Step 2: Testing API endpoints for real courses...');
  
  const results = [];
  
  for (const course of courses) {
    console.log(`\n📚 Testing course: ${course.Title} (ID: ${course.Id}, Slug: ${course.Slug})`);
    
    const testResult = {
      course: {
        id: course.Id,
        slug: course.Slug,
        title: course.Title
      },
      tests: {
        byId: false,
        bySlug: false,
        publicBySlug: false
      },
      media: {
        hasCoverImage: !!course.CoverImage,
        hasVideo: !!course.PresentationVideo,
        coverImageAccessible: false,
        videoAccessible: false
      },
      apiResponses: {}
    };
    
    // Test 1: Get by ID
    try {
      const response = await axios.get(`${API_BASE_URL}/courses/${course.Id}`);
      testResult.tests.byId = true;
      testResult.apiResponses.byId = {
        status: 'success',
        hasCoverImage: !!response.data.CoverImage,
        hasVideo: !!response.data.PresentationVideo
      };
      console.log(`   ✅ GET /courses/${course.Id} - SUCCESS`);
    } catch (error) {
      testResult.apiResponses.byId = {
        status: 'failed',
        error: error.response?.status || error.message
      };
      console.log(`   ❌ GET /courses/${course.Id} - FAILED (${error.response?.status || error.message})`);
    }
    
    // Test 2: Get by Slug
    try {
      const response = await axios.get(`${API_BASE_URL}/courses/${course.Slug}`);
      testResult.tests.bySlug = true;
      testResult.apiResponses.bySlug = {
        status: 'success',
        hasCoverImage: !!response.data.CoverImage,
        hasVideo: !!response.data.PresentationVideo
      };
      console.log(`   ✅ GET /courses/${course.Slug} - SUCCESS`);
    } catch (error) {
      testResult.apiResponses.bySlug = {
        status: 'failed',
        error: error.response?.status || error.message
      };
      console.log(`   ❌ GET /courses/${course.Slug} - FAILED (${error.response?.status || error.message})`);
    }
    
    // Test 3: Get by Public Slug (most important for frontend)
    try {
      const response = await axios.get(`${API_BASE_URL}/courses/public/${course.Slug}`);
      testResult.tests.publicBySlug = true;
      testResult.apiResponses.publicBySlug = {
        status: 'success',
        hasCoverImage: !!response.data.CoverImage,
        hasVideo: !!response.data.PresentationVideo,
        coverImageHashname: response.data.CoverImage?.Hashname,
        videoHashname: response.data.PresentationVideo?.Hashname
      };
      console.log(`   ✅ GET /courses/public/${course.Slug} - SUCCESS`);
      
      // Test media accessibility if available
      if (response.data.CoverImage?.Hashname) {
        const coverUrl = `http://localhost:3200/${response.data.CoverImage.Hashname}`;
        try {
          await axios.head(coverUrl);
          testResult.media.coverImageAccessible = true;
          console.log(`   ✅ Cover image accessible: ${coverUrl}`);
        } catch (error) {
          console.log(`   ❌ Cover image not accessible: ${coverUrl}`);
        }
      }
      
      if (response.data.PresentationVideo?.Hashname) {
        const videoUrl = `http://localhost:3200/${response.data.PresentationVideo.Hashname}`;
        try {
          await axios.head(videoUrl);
          testResult.media.videoAccessible = true;
          console.log(`   ✅ Video accessible: ${videoUrl}`);
        } catch (error) {
          console.log(`   ❌ Video not accessible: ${videoUrl}`);
        }
      }
      
    } catch (error) {
      testResult.apiResponses.publicBySlug = {
        status: 'failed',
        error: error.response?.status || error.message
      };
      console.log(`   ❌ GET /courses/public/${course.Slug} - FAILED (${error.response?.status || error.message})`);
    }
    
    results.push(testResult);
  }
  
  return results;
}

/**
 * Test frontend course service simulation
 */
async function testFrontendCourseServiceSimulation(testResults) {
  console.log('\n🔧 Step 3: Testing frontend course service simulation...');
  
  const ENVIRONMENT_PATH = 'http://localhost:3200';
  
  function constructMediaUrl(hashname) {
    if (!hashname) return '';
    if (hashname.startsWith('http://') || hashname.startsWith('https://')) {
      return hashname;
    }
    const cleanHashname = hashname.startsWith('/') ? hashname.substring(1) : hashname;
    return `${ENVIRONMENT_PATH}/${cleanHashname}`;
  }
  
  const frontendResults = [];
  
  for (const result of testResults) {
    if (result.tests.publicBySlug && result.apiResponses.publicBySlug.status === 'success') {
      const apiResponse = result.apiResponses.publicBySlug;
      
      const frontendProcessed = {
        courseId: result.course.id,
        courseTitle: result.course.title,
        coverImage: apiResponse.coverImageHashname ? {
          original: apiResponse.coverImageHashname,
          processed: constructMediaUrl(apiResponse.coverImageHashname)
        } : null,
        video: apiResponse.videoHashname ? {
          original: apiResponse.videoHashname,
          processed: constructMediaUrl(apiResponse.videoHashname)
        } : null
      };
      
      frontendResults.push(frontendProcessed);
      
      console.log(`📚 ${result.course.title}:`);
      if (frontendProcessed.coverImage) {
        console.log(`   📸 Cover: ${frontendProcessed.coverImage.processed}`);
      }
      if (frontendProcessed.video) {
        console.log(`   🎥 Video: ${frontendProcessed.video.processed}`);
      }
    }
  }
  
  return frontendResults;
}

/**
 * Generate comprehensive test summary
 */
function generateTestSummary(courses, testResults, frontendResults) {
  console.log('\n📊 Step 4: Comprehensive Test Summary');
  console.log('='.repeat(80));
  
  const summary = {
    totalCourses: courses.length,
    coursesWithMedia: courses.filter(c => c.CoverImage || c.PresentationVideo).length,
    coursesWithCoverImage: courses.filter(c => c.CoverImage).length,
    coursesWithVideo: courses.filter(c => c.PresentationVideo).length,
    apiTests: {
      byIdSuccess: testResults.filter(r => r.tests.byId).length,
      bySlugSuccess: testResults.filter(r => r.tests.bySlug).length,
      publicBySlugSuccess: testResults.filter(r => r.tests.publicBySlug).length
    },
    mediaAccessibility: {
      coverImagesAccessible: testResults.filter(r => r.media.coverImageAccessible).length,
      videosAccessible: testResults.filter(r => r.media.videoAccessible).length
    },
    frontendProcessing: frontendResults.length
  };
  
  console.log(`📈 Database Analysis:`);
  console.log(`   Total Courses: ${summary.totalCourses}`);
  console.log(`   Courses with Media: ${summary.coursesWithMedia}`);
  console.log(`   Courses with Cover Images: ${summary.coursesWithCoverImage}`);
  console.log(`   Courses with Videos: ${summary.coursesWithVideo}`);
  
  console.log(`\n🌐 API Endpoint Tests:`);
  console.log(`   GET /courses/:id Success: ${summary.apiTests.byIdSuccess}/${summary.totalCourses}`);
  console.log(`   GET /courses/:slug Success: ${summary.apiTests.bySlugSuccess}/${summary.totalCourses}`);
  console.log(`   GET /courses/public/:slug Success: ${summary.apiTests.publicBySlugSuccess}/${summary.totalCourses}`);
  
  console.log(`\n📁 Media Accessibility:`);
  console.log(`   Cover Images Accessible: ${summary.mediaAccessibility.coverImagesAccessible}/${summary.coursesWithCoverImage}`);
  console.log(`   Videos Accessible: ${summary.mediaAccessibility.videosAccessible}/${summary.coursesWithVideo}`);
  
  console.log(`\n🔧 Frontend Processing:`);
  console.log(`   Courses Successfully Processed: ${summary.frontendProcessing}/${summary.totalCourses}`);
  
  // Overall assessment
  const overallSuccess = summary.apiTests.publicBySlugSuccess > 0 && 
                        summary.mediaAccessibility.coverImagesAccessible > 0 &&
                        summary.frontendProcessing > 0;
  
  console.log(`\n🎯 Overall Assessment: ${overallSuccess ? '✅ SUCCESS' : '❌ NEEDS ATTENTION'}`);
  
  if (overallSuccess) {
    console.log('✅ Real database courses are working with frontend-backend integration');
    console.log('✅ Media files are accessible and properly formatted');
    console.log('✅ Frontend course service can process real course data');
  } else {
    console.log('❌ Some issues found with real database course integration');
  }
  
  return summary;
}

/**
 * Main test execution
 */
async function runRealDatabaseTest() {
  console.log('🧪 Real Database Courses Integration Test');
  console.log('Testing frontend-backend integration with actual database courses');
  console.log('='.repeat(80));
  
  try {
    // Step 1: Fetch real courses from database
    const courses = await fetchRealCoursesFromDatabase();
    
    if (courses.length === 0) {
      console.log('⚠️ No courses found in database. Please create some courses first.');
      return;
    }
    
    // Step 2: Test API endpoints for each course
    const testResults = await testAPIEndpointsForRealCourses(courses);
    
    // Step 3: Test frontend processing simulation
    const frontendResults = await testFrontendCourseServiceSimulation(testResults);
    
    // Step 4: Generate comprehensive summary
    const summary = generateTestSummary(courses, testResults, frontendResults);
    
    console.log('\n🔗 URLs for Manual Testing:');
    const workingCourses = testResults.filter(r => r.tests.publicBySlug);
    workingCourses.slice(0, 3).forEach(result => {
      console.log(`   Frontend: http://localhost:5174/courses/${result.course.slug}`);
      console.log(`   API: http://localhost:3200/api/courses/public/${result.course.slug}`);
    });
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run the real database test
runRealDatabaseTest();
