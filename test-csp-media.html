<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSP Media Test</title>
    <!-- Updated CSP with media-src directive -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https:; style-src 'self' 'unsafe-inline' https:; img-src 'self' data: https: http://localhost:*; media-src 'self' data: https: http://localhost:*; font-src 'self' https:; connect-src 'self' http://localhost:* ws://localhost:* https:;" />
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
        video, img {
            max-width: 100%;
            height: auto;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>CSP Media Loading Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Image from localhost:3200</h2>
        <p>Testing if images can load from the backend server:</p>
        <img 
            src="http://localhost:3200/upload/courses/covers/test-image.png" 
            alt="Test Image"
            onload="document.getElementById('img-status').innerHTML = '<span class=success>✅ Image loaded successfully</span>'"
            onerror="document.getElementById('img-status').innerHTML = '<span class=error>❌ Image failed to load</span>'"
        />
        <p id="img-status">Loading...</p>
    </div>

    <div class="test-section">
        <h2>Test 2: Video from localhost:3200</h2>
        <p>Testing if videos can load from the backend server:</p>
        <video 
            controls 
            width="400"
            onloadstart="document.getElementById('video-status').innerHTML = '<span class=success>✅ Video started loading</span>'"
            onerror="document.getElementById('video-status').innerHTML = '<span class=error>❌ Video failed to load</span>'"
        >
            <source src="http://localhost:3200/upload/courses/videos/test-video.mp4" type="video/mp4">
            Your browser does not support the video tag.
        </video>
        <p id="video-status">Loading...</p>
    </div>

    <div class="test-section">
        <h2>Test 3: URL Encoding Test</h2>
        <p>Testing if URLs with special characters work:</p>
        <video 
            controls 
            width="400"
            onloadstart="document.getElementById('encoded-status').innerHTML = '<span class=success>✅ Encoded URL video started loading</span>'"
            onerror="document.getElementById('encoded-status').innerHTML = '<span class=error>❌ Encoded URL video failed to load</span>'"
        >
            <source src="http://localhost:3200/upload/courses/videos/17f7fd07ae4b417ea7f401d57abf434a-#Olungiya%20#brianlubega.mp4" type="video/mp4">
            Your browser does not support the video tag.
        </video>
        <p id="encoded-status">Loading...</p>
    </div>

    <div class="test-section">
        <h2>Console Check</h2>
        <p>Open browser developer tools (F12) and check the Console tab for any CSP violations.</p>
        <p>If the CSP is working correctly, you should see no "Content-Security-Policy" errors.</p>
    </div>

    <script>
        // Test script loading (should work with current CSP)
        console.log('✅ JavaScript is working - CSP allows script execution');
        
        // Test fetch to localhost:3200 (should work with current CSP)
        fetch('http://localhost:3200/api/medias')
            .then(response => {
                console.log('✅ Fetch to localhost:3200 successful - CSP allows connect-src');
            })
            .catch(error => {
                console.log('❌ Fetch failed:', error.message);
            });
    </script>
</body>
</html>
