<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Playback Test</title>
    <!-- Updated CSP with media-src directive -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https:; style-src 'self' 'unsafe-inline' https:; img-src 'self' data: https: http://localhost:*; media-src 'self' data: https: http://localhost:*; font-src 'self' https:; connect-src 'self' http://localhost:* ws://localhost:* https:;" />
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        video {
            max-width: 100%;
            height: auto;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <h1>Video Playback Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Real Video File (5.52 MB)</h2>
        <p>Testing with a real video file:</p>
        <video
            controls
            width="400"
            preload="metadata"
            onloadstart="console.log('Video 1 load started'); document.getElementById('status1').innerHTML = '<span class=info>📡 Loading started...</span>'"
            onloadedmetadata="console.log('Video 1 metadata loaded'); document.getElementById('status1').innerHTML = '<span class=success>✅ Metadata loaded</span>'"
            oncanplay="console.log('Video 1 can play'); document.getElementById('status1').innerHTML = '<span class=success>✅ Video ready to play</span>'"
            onerror="console.error('Video 1 error:', event); document.getElementById('status1').innerHTML = '<span class=error>❌ Video failed to load</span>'"
        >
            <source src="http://localhost:3200/upload/courses/videos/b61efe6cfabb4324a63bdb273c41f965-real-video-test-1752509782289.mp4" type="video/mp4">
            Your browser does not support the video tag.
        </video>
        <p id="status1">Waiting...</p>
    </div>

    <div class="test-section">
        <h2>Test 2: Video with Special Characters</h2>
        <p>Testing video with # and spaces in filename:</p>
        <video 
            controls 
            width="400"
            preload="metadata"
            onloadstart="console.log('Video 2 load started'); document.getElementById('status2').innerHTML = '<span class=info>📡 Loading started...</span>'"
            onloadedmetadata="console.log('Video 2 metadata loaded'); document.getElementById('status2').innerHTML = '<span class=success>✅ Metadata loaded</span>'"
            oncanplay="console.log('Video 2 can play'); document.getElementById('status2').innerHTML = '<span class=success>✅ Video ready to play</span>'"
            onerror="console.error('Video 2 error:', event); document.getElementById('status2').innerHTML = '<span class=error>❌ Video failed to load</span>'"
        >
            <source src="http://localhost:3200/upload/courses/videos/17f7fd07ae4b417ea7f401d57abf434a-#Olungiya #brianlubega.mp4" type="video/mp4">
            Your browser does not support the video tag.
        </video>
        <p id="status2">Waiting...</p>
    </div>

    <div class="test-section">
        <h2>Test 3: URL Encoded Version</h2>
        <p>Testing URL encoded version of special characters:</p>
        <video 
            controls 
            width="400"
            preload="metadata"
            onloadstart="console.log('Video 3 load started'); document.getElementById('status3').innerHTML = '<span class=info>📡 Loading started...</span>'"
            onloadedmetadata="console.log('Video 3 metadata loaded'); document.getElementById('status3').innerHTML = '<span class=success>✅ Metadata loaded</span>'"
            oncanplay="console.log('Video 3 can play'); document.getElementById('status3').innerHTML = '<span class=success>✅ Video ready to play</span>'"
            onerror="console.error('Video 3 error:', event); document.getElementById('status3').innerHTML = '<span class=error>❌ Video failed to load</span>'"
        >
            <source src="http://localhost:3200/upload/courses/videos/17f7fd07ae4b417ea7f401d57abf434a-%23Olungiya%20%23brianlubega.mp4" type="video/mp4">
            Your browser does not support the video tag.
        </video>
        <p id="status3">Waiting...</p>
    </div>

    <div class="test-section">
        <h2>Console Output</h2>
        <p>Check the browser console (F12) for detailed error messages and loading events.</p>
        <button onclick="testUrls()">Test URLs Programmatically</button>
        <div id="url-test-results"></div>
    </div>

    <script>
        function testUrls() {
            const urls = [
                'http://localhost:3200/upload/courses/videos/b61efe6cfabb4324a63bdb273c41f965-real-video-test-1752509782289.mp4',
                'http://localhost:3200/upload/courses/videos/17f7fd07ae4b417ea7f401d57abf434a-#Olungiya #brianlubega.mp4',
                'http://localhost:3200/upload/courses/videos/17f7fd07ae4b417ea7f401d57abf434a-%23Olungiya%20%23brianlubega.mp4'
            ];

            const resultsDiv = document.getElementById('url-test-results');
            resultsDiv.innerHTML = '<h3>URL Test Results:</h3>';

            urls.forEach((url, index) => {
                fetch(url, { method: 'HEAD' })
                    .then(response => {
                        resultsDiv.innerHTML += `<p><span class="success">✅ URL ${index + 1}: ${response.status} - ${response.headers.get('content-type')}</span></p>`;
                    })
                    .catch(error => {
                        resultsDiv.innerHTML += `<p><span class="error">❌ URL ${index + 1}: ${error.message}</span></p>`;
                    });
            });
        }

        // Log all video events for debugging
        document.querySelectorAll('video').forEach((video, index) => {
            ['loadstart', 'durationchange', 'loadedmetadata', 'loadeddata', 'progress', 'canplay', 'canplaythrough', 'error'].forEach(event => {
                video.addEventListener(event, (e) => {
                    console.log(`Video ${index + 1} - ${event}:`, e);
                });
            });
        });
    </script>
</body>
</html>
