/**
 * Test Course Media Database Save Functionality
 * Verifies that cover images and presentation videos are properly saved to the database
 */

const axios = require('axios');
const fs = require('fs');

const BASE_URL = 'http://localhost:3200/api';

// Test image and video data
const TEST_IMAGE_BASE64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
const TEST_VIDEO_BASE64 = 'AAAAIGZ0eXBpc29tAAACAGlzb21pc28yYXZjMW1wNDEAAAAIZnJlZQAAAr1tZGF0';

async function testMediaUploadAndDatabaseSave() {
  console.log('🧪 Testing Media Upload and Database Save Functionality');
  console.log('=' .repeat(70));

  try {
    // Step 1: Upload cover image
    console.log('\n1️⃣ Uploading cover image...');
    const coverImageFilename = `test-cover-${Date.now()}.png`;
    const coverResponse = await axios.post(`${BASE_URL}/utils/savefile`, {
      File: TEST_IMAGE_BASE64,
      Filename: coverImageFilename,
      SubDir: 'courses/covers'
    });
    
    console.log('✅ Cover image uploaded:', coverResponse.data);
    const coverImageUrl = `http://localhost:3200/public/upload/courses/covers/${coverImageFilename}`;
    console.log('📁 Cover image URL:', coverImageUrl);

    // Step 2: Upload presentation video
    console.log('\n2️⃣ Uploading presentation video...');
    const videoFilename = `test-video-${Date.now()}.mp4`;
    const videoResponse = await axios.post(`${BASE_URL}/utils/savefile`, {
      File: TEST_VIDEO_BASE64,
      Filename: videoFilename,
      SubDir: 'courses/videos'
    });
    
    console.log('✅ Presentation video uploaded:', videoResponse.data);
    const videoUrl = `http://localhost:3200/public/upload/courses/videos/${videoFilename}`;
    console.log('📁 Video URL:', videoUrl);

    // Step 3: Create course with media objects
    console.log('\n3️⃣ Creating course with media objects...');
    
    // Simulate the Media objects that the React component creates
    const coverImageMedia = {
      Id: 0, // Will be set by backend
      Name: 'test-cover.png',
      Hashname: coverImageFilename,
      Extension: 'image/png',
      Size: 32,
      SubDir: 'courses/covers',
      Slug: `cover-${Date.now()}`
    };

    const presentationVideoMedia = {
      Id: 0, // Will be set by backend
      Name: 'test-video.mp4', 
      Hashname: videoFilename,
      Extension: 'video/mp4',
      Size: 64,
      SubDir: 'courses/videos',
      Slug: `video-${Date.now()}`
    };

    const testCourse = {
      Title: `Test Course with Media ${Date.now()}`,
      Resume: 'This is a test course to verify media database saving functionality.',
      Keywords: ['test', 'media', 'database'],
      Price: 0,
      Language: 'en',
      Published: false,
      Archived: false,
      Free: true,
      Level: [1], // Beginner
      Format: 2, // VIDEO format
      Prerequisites: ['Basic computer skills'],
      Goals: ['Learn media upload', 'Verify database saving'],
      Message: 'Welcome to the test course!',
      Congratulation: 'Congratulations on completing the test!',
      Currency: 'USD',
      CoverImage: coverImageMedia,
      PresentationVideo: presentationVideoMedia,
      Categories: [], // Empty for test
      CreatedBy: { Id: 1 } // Assuming user ID 1 exists
    };

    console.log('📝 Course data with media objects:');
    console.log('   - Title:', testCourse.Title);
    console.log('   - CoverImage.Hashname:', testCourse.CoverImage.Hashname);
    console.log('   - PresentationVideo.Hashname:', testCourse.PresentationVideo.Hashname);

    // Step 4: Send course creation request
    console.log('\n4️⃣ Sending course creation request...');
    
    const origin = 'http://localhost:5174';
    const courseResponse = await axios.post(`${BASE_URL}/courses`, {
      origin: origin,
      body: testCourse
    });

    console.log('✅ Course created successfully!');
    console.log('📊 Course response summary:');
    console.log('   - Course ID:', courseResponse.data.Id);
    console.log('   - Course Slug:', courseResponse.data.Slug);
    console.log('   - Title:', courseResponse.data.Title);
    
    // Check if media objects were saved
    if (courseResponse.data.CoverImage) {
      console.log('✅ Cover Image saved to database:');
      console.log('   - ID:', courseResponse.data.CoverImage.Id);
      console.log('   - Hashname:', courseResponse.data.CoverImage.Hashname);
      console.log('   - SubDir:', courseResponse.data.CoverImage.SubDir);
    } else {
      console.log('❌ Cover Image NOT saved to database');
    }

    if (courseResponse.data.PresentationVideo) {
      console.log('✅ Presentation Video saved to database:');
      console.log('   - ID:', courseResponse.data.PresentationVideo.Id);
      console.log('   - Hashname:', courseResponse.data.PresentationVideo.Hashname);
      console.log('   - SubDir:', courseResponse.data.PresentationVideo.SubDir);
    } else {
      console.log('❌ Presentation Video NOT saved to database');
    }

    // Step 5: Verify by fetching the course
    console.log('\n5️⃣ Verifying by fetching the created course...');
    const fetchResponse = await axios.get(`${BASE_URL}/courses/${courseResponse.data.Slug}`);
    
    console.log('📖 Fetched course verification:');
    console.log('   - Title:', fetchResponse.data.Title);
    
    if (fetchResponse.data.CoverImage) {
      console.log('✅ Cover Image persisted in database:');
      console.log('   - ID:', fetchResponse.data.CoverImage.Id);
      console.log('   - Hashname:', fetchResponse.data.CoverImage.Hashname);
      console.log('   - Full URL should be:', `http://localhost:3200/public/upload/${fetchResponse.data.CoverImage.SubDir}/${fetchResponse.data.CoverImage.Hashname}`);
    }

    if (fetchResponse.data.PresentationVideo) {
      console.log('✅ Presentation Video persisted in database:');
      console.log('   - ID:', fetchResponse.data.PresentationVideo.Id);
      console.log('   - Hashname:', fetchResponse.data.PresentationVideo.Hashname);
      console.log('   - Full URL should be:', `http://localhost:3200/public/upload/${fetchResponse.data.PresentationVideo.SubDir}/${fetchResponse.data.PresentationVideo.Hashname}`);
    }

    return {
      success: true,
      courseId: courseResponse.data.Id,
      courseSlug: courseResponse.data.Slug,
      coverImageSaved: !!courseResponse.data.CoverImage,
      presentationVideoSaved: !!courseResponse.data.PresentationVideo,
      coverImageUrl: coverImageUrl,
      videoUrl: videoUrl
    };

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    
    if (error.response?.status) {
      console.error('   - Status:', error.response.status);
    }
    
    if (error.response?.data) {
      console.error('   - Response data:', JSON.stringify(error.response.data, null, 2));
    }

    return {
      success: false,
      error: error.response?.data || error.message
    };
  }
}

async function runTest() {
  console.log('🚀 Starting Course Media Database Save Test');
  console.log('Testing if cover images and presentation videos are properly saved to database');
  console.log('');

  const result = await testMediaUploadAndDatabaseSave();

  console.log('\n📊 TEST RESULTS SUMMARY');
  console.log('=' .repeat(70));
  
  if (result.success) {
    console.log('🎉 TEST PASSED - Media database saving is working!');
    console.log(`✅ Course created with ID: ${result.courseId}`);
    console.log(`✅ Course slug: ${result.courseSlug}`);
    console.log(`${result.coverImageSaved ? '✅' : '❌'} Cover image saved to database: ${result.coverImageSaved}`);
    console.log(`${result.presentationVideoSaved ? '✅' : '❌'} Presentation video saved to database: ${result.presentationVideoSaved}`);
    
    if (result.coverImageSaved && result.presentationVideoSaved) {
      console.log('\n🎯 CONCLUSION: Both cover image and presentation video are being saved to the database correctly!');
      console.log('   - Files are uploaded to backend/public/upload/');
      console.log('   - Media metadata is stored in database');
      console.log('   - Course references the media objects properly');
    } else {
      console.log('\n⚠️  PARTIAL SUCCESS: Some media objects are not being saved to database');
    }
  } else {
    console.log('❌ TEST FAILED - Media database saving has issues');
    console.log('Error:', result.error);
  }
}

// Run the test
runTest().catch(console.error);
