/**
 * React App Diagnostics Test
 * Tests if the React app can access the backend and media endpoints
 */

const axios = require('axios');

const FRONTEND_URL = 'http://localhost:5174';
const BACKEND_URL = 'http://localhost:3200';

async function testReactAppDiagnostics() {
  console.log('🔍 React App Diagnostics Test');
  console.log('=' .repeat(50));

  const results = {
    frontend: false,
    backend: false,
    mediasEndpoint: false,
    cors: false
  };

  try {
    // Test 1: Frontend accessibility
    console.log('\n1️⃣ Testing frontend accessibility...');
    try {
      const frontendResponse = await axios.get(FRONTEND_URL, { timeout: 5000 });
      if (frontendResponse.status === 200) {
        console.log('✅ Frontend is accessible');
        results.frontend = true;
      }
    } catch (error) {
      console.log('❌ Frontend not accessible:', error.message);
    }

    // Test 2: Backend accessibility
    console.log('\n2️⃣ Testing backend accessibility...');
    try {
      const backendResponse = await axios.get(`${BACKEND_URL}/api`, { timeout: 5000 });
      console.log('✅ Backend is accessible');
      results.backend = true;
    } catch (error) {
      console.log('❌ Backend not accessible:', error.message);
    }

    // Test 3: Medias endpoint functionality
    console.log('\n3️⃣ Testing /api/medias endpoint...');
    try {
      const testData = {
        Name: `diagnostic-test-${Date.now()}.png`,
        Hashname: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==',
        Extension: 'image/png',
        Size: 32,
        SubDir: 'courses/covers'
      };

      const mediasResponse = await axios.post(`${BACKEND_URL}/api/medias`, testData, {
        headers: { 'Content-Type': 'application/json' },
        timeout: 10000
      });

      if (mediasResponse.status === 200 || mediasResponse.status === 201) {
        console.log('✅ /api/medias endpoint is working');
        console.log('   Response:', {
          Id: mediasResponse.data.Id,
          Name: mediasResponse.data.Name,
          Hashname: mediasResponse.data.Hashname
        });
        results.mediasEndpoint = true;
      }
    } catch (error) {
      console.log('❌ /api/medias endpoint failed:', error.response?.data || error.message);
    }

    // Test 4: CORS configuration
    console.log('\n4️⃣ Testing CORS configuration...');
    try {
      const corsResponse = await axios.options(`${BACKEND_URL}/api/medias`, {
        headers: {
          'Origin': FRONTEND_URL,
          'Access-Control-Request-Method': 'POST',
          'Access-Control-Request-Headers': 'Content-Type'
        },
        timeout: 5000
      });

      const corsHeaders = corsResponse.headers;
      console.log('✅ CORS preflight successful');
      console.log('   Access-Control-Allow-Origin:', corsHeaders['access-control-allow-origin']);
      console.log('   Access-Control-Allow-Methods:', corsHeaders['access-control-allow-methods']);
      console.log('   Access-Control-Allow-Headers:', corsHeaders['access-control-allow-headers']);
      results.cors = true;
    } catch (error) {
      console.log('❌ CORS preflight failed:', error.message);
    }

    // Test 5: Network connectivity from frontend perspective
    console.log('\n5️⃣ Testing network connectivity...');
    try {
      // Simulate what the React app would do
      const networkTest = await axios.post(`${BACKEND_URL}/api/medias`, {
        Name: `network-test-${Date.now()}.png`,
        Hashname: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==',
        Extension: 'image/png',
        Size: 32,
        SubDir: 'courses/covers'
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Origin': FRONTEND_URL
        },
        timeout: 10000
      });

      console.log('✅ Network connectivity test passed');
      console.log('   Can successfully POST to /api/medias from frontend origin');
    } catch (error) {
      console.log('❌ Network connectivity test failed:', error.response?.data || error.message);
    }

    // Test 6: Check if React dev server is running
    console.log('\n6️⃣ Checking React dev server status...');
    try {
      const devServerResponse = await axios.get(`${FRONTEND_URL}/instructor/courses/create`, {
        timeout: 5000,
        validateStatus: () => true // Accept any status code
      });

      if (devServerResponse.status === 200) {
        console.log('✅ React dev server is running and serving pages');
        console.log('   Course creation page is accessible');
      } else {
        console.log(`⚠️  React dev server responded with status: ${devServerResponse.status}`);
      }
    } catch (error) {
      console.log('❌ React dev server check failed:', error.message);
    }

  } catch (error) {
    console.error('❌ Diagnostic test failed:', error.message);
  }

  // Summary
  console.log('\n📊 DIAGNOSTIC RESULTS');
  console.log('=' .repeat(50));
  console.log(`Frontend Accessible: ${results.frontend ? '✅' : '❌'}`);
  console.log(`Backend Accessible: ${results.backend ? '✅' : '❌'}`);
  console.log(`Medias Endpoint: ${results.mediasEndpoint ? '✅' : '❌'}`);
  console.log(`CORS Configuration: ${results.cors ? '✅' : '❌'}`);

  const allGood = Object.values(results).every(result => result);
  
  if (allGood) {
    console.log('\n🎉 ALL SYSTEMS OPERATIONAL');
    console.log('The media upload should be working in the React app.');
    console.log('\n🔧 TROUBLESHOOTING STEPS:');
    console.log('1. Open browser dev tools (F12)');
    console.log('2. Go to http://localhost:5174/instructor/courses/create');
    console.log('3. Try uploading a file');
    console.log('4. Check Console tab for JavaScript errors');
    console.log('5. Check Network tab for failed requests');
  } else {
    console.log('\n⚠️  ISSUES DETECTED');
    console.log('Some components are not working properly.');
    console.log('\n🔧 RECOMMENDED ACTIONS:');
    
    if (!results.frontend) {
      console.log('- Start the React dev server: npm run dev');
    }
    if (!results.backend) {
      console.log('- Start the backend server');
    }
    if (!results.mediasEndpoint) {
      console.log('- Check backend logs for /api/medias endpoint errors');
    }
    if (!results.cors) {
      console.log('- Check CORS configuration in backend');
    }
  }

  return results;
}

// Run diagnostics
testReactAppDiagnostics().catch(console.error);
