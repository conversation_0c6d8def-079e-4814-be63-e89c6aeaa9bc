const axios = require('axios');

const API_BASE = 'http://localhost:3200/api';
const STATIC_BASE = 'http://localhost:3200';

/**
 * Test the exact endpoints used by:
 * 1. Skills Section (Home Page)
 * 2. Public Courses Page  
 * 3. Course Details Page
 * 4. Static File Serving
 */

async function testCourseEndpoints() {
  console.log('🔍 Testing Course Cover Image Endpoints');
  console.log('=' .repeat(60));

  try {
    // Test 1: Skills Section (Home Page) Endpoints
    console.log('\n🏠 1. SKILLS SECTION (HOME PAGE) ENDPOINTS');
    console.log('-' .repeat(50));
    
    // 1a. Get categories for navigation
    console.log('\n📂 Testing categories endpoint...');
    try {
      const categoriesResponse = await axios.get(`${API_BASE}/categories/8/0`);
      const categories = categoriesResponse.data;
      console.log(`✅ Categories: Found ${categories.length} categories`);
      
      if (categories.length > 0) {
        const firstCategory = categories[0];
        console.log(`   - First category: "${firstCategory.Title}" (${firstCategory.Slug})`);
        
        // 1b. Get courses for first category
        console.log(`\n🎯 Testing courses by category: ${firstCategory.Slug}...`);
        const categoryCoursesResponse = await axios.get(
          `${API_BASE}/courses/categories/${firstCategory.Slug}/8/0`
        );
        const categoryCourses = categoryCoursesResponse.data;
        
        if (categoryCourses.Courses && categoryCourses.Courses.length > 0) {
          console.log(`✅ Category courses: Found ${categoryCourses.Courses.length} courses`);
          
          // Check cover images in category courses
          const coursesWithImages = categoryCourses.Courses.filter(course => course.CoverImage?.Hashname);
          console.log(`   - Courses with cover images: ${coursesWithImages.length}`);
          
          if (coursesWithImages.length > 0) {
            const sampleCourse = coursesWithImages[0];
            console.log(`   - Sample: "${sampleCourse.Title}"`);
            console.log(`   - Cover image: ${sampleCourse.CoverImage.Hashname}`);
            
            // Test the cover image URL
            const imageUrl = `${STATIC_BASE}/${sampleCourse.CoverImage.Hashname}`;
            try {
              const imageResponse = await axios.head(imageUrl, { timeout: 5000 });
              console.log(`   ✅ Cover image accessible: ${imageResponse.status}`);
            } catch (imageError) {
              console.log(`   ❌ Cover image failed: ${imageError.response?.status || 'Network Error'}`);
            }
          }
        } else {
          console.log('⚠️  No courses found for category');
        }
      }
    } catch (error) {
      console.log(`❌ Categories test failed: ${error.response?.status || error.message}`);
    }

    // Test 2: Public Courses Page Endpoints
    console.log('\n\n📚 2. PUBLIC COURSES PAGE ENDPOINTS');
    console.log('-' .repeat(50));
    
    // 2a. Main courses list
    console.log('\n📋 Testing main courses list...');
    try {
      const coursesResponse = await axios.get(`${API_BASE}/courses/15/0?justPublished=true`);
      const courses = coursesResponse.data;
      console.log(`✅ Main courses: Found ${courses.length} courses`);
      
      // Find our target course
      const targetCourse = courses.find(course => 
        course.Title && course.Title.toLowerCase().includes('demo course two')
      );
      
      if (targetCourse) {
        console.log(`   - Target course found: "${targetCourse.Title}"`);
        console.log(`   - Slug: ${targetCourse.Slug}`);
        
        if (targetCourse.CoverImage?.Hashname) {
          console.log(`   - Cover image: ${targetCourse.CoverImage.Hashname}`);
          
          // Test the cover image URL
          const imageUrl = `${STATIC_BASE}/${targetCourse.CoverImage.Hashname}`;
          try {
            const imageResponse = await axios.head(imageUrl, { timeout: 5000 });
            console.log(`   ✅ Target course image accessible: ${imageResponse.status}`);
          } catch (imageError) {
            console.log(`   ❌ Target course image failed: ${imageError.response?.status || 'Network Error'}`);
          }
        } else {
          console.log('   ❌ No cover image found for target course');
        }
      } else {
        console.log('   ⚠️  Target course not found in main list');
      }
      
      // 2b. Course count
      console.log('\n🔢 Testing course count...');
      const countResponse = await axios.get(`${API_BASE}/courses/count`);
      console.log(`✅ Total courses: ${countResponse.data}`);
      
    } catch (error) {
      console.log(`❌ Courses list test failed: ${error.response?.status || error.message}`);
    }

    // Test 3: Course Details Page Endpoints
    console.log('\n\n📖 3. COURSE DETAILS PAGE ENDPOINTS');
    console.log('-' .repeat(50));
    
    console.log('\n🎯 Testing course details by slug...');
    const targetSlug = 'demo-course-two-updated-with-media-updated-with-media-real-video';
    
    try {
      // 3a. Primary endpoint - by slug
      const courseResponse = await axios.get(`${API_BASE}/courses/public/${targetSlug}`);
      const course = courseResponse.data;
      console.log(`✅ Course details: "${course.Title}"`);
      console.log(`   - ID: ${course.Id}`);
      console.log(`   - Slug: ${course.Slug}`);
      
      if (course.CoverImage?.Hashname) {
        console.log(`   - Cover image: ${course.CoverImage.Hashname}`);
        
        // Test the cover image URL
        const imageUrl = `${STATIC_BASE}/${course.CoverImage.Hashname}`;
        try {
          const imageResponse = await axios.head(imageUrl, { timeout: 5000 });
          console.log(`   ✅ Course detail image accessible: ${imageResponse.status}`);
        } catch (imageError) {
          console.log(`   ❌ Course detail image failed: ${imageError.response?.status || 'Network Error'}`);
        }
      } else {
        console.log('   ❌ No cover image in course details');
      }
      
      // 3b. Fallback endpoint - by ID (if available)
      if (course.Id) {
        console.log(`\n🔄 Testing fallback endpoint by ID: ${course.Id}...`);
        try {
          const idResponse = await axios.get(`${API_BASE}/courses/${course.Id}`);
          console.log(`✅ Fallback by ID works: "${idResponse.data.Title}"`);
        } catch (idError) {
          console.log(`⚠️  Fallback by ID failed: ${idError.response?.status || idError.message}`);
        }
      }
      
    } catch (error) {
      console.log(`❌ Course details test failed: ${error.response?.status || error.message}`);
    }

    // Test 4: Static File Serving Summary
    console.log('\n\n🖼️  4. STATIC FILE SERVING SUMMARY');
    console.log('-' .repeat(50));
    
    console.log('\n📊 Summary of cover image tests:');
    console.log('   - Backend serves files from: /public directory');
    console.log('   - URL pattern: http://localhost:3200/[hashname]');
    console.log('   - Hashname format: upload/courses/covers/[filename]');
    console.log('   - Frontend should construct: environment.path + "/" + hashname');

  } catch (error) {
    console.error('💥 Test suite failed:', error.message);
  }

  console.log('\n🎉 Course endpoints test completed!');
  console.log('\n💡 Next steps:');
  console.log('   1. Verify frontend is using the formatted URLs from course service');
  console.log('   2. Check if CourseCard component receives properly formatted image URLs');
  console.log('   3. Ensure course service formatCourse() method is being called');
}

// Run the test
testCourseEndpoints().catch(console.error);
