#!/usr/bin/env node

/**
 * Cleanup Test Data Script
 * Removes test instructor applications and users to allow fresh testing
 */

const axios = require('axios');

// Configuration
const API_BASE = 'http://localhost:3200/api';
const FRONTEND_URL = 'http://localhost:5174';

const adminCredentials = {
  username: 'admin',
  password: 'Admin@123',
  origin: FRONTEND_URL
};

const testEmail = '<EMAIL>';

// Utility functions
const log = {
  info: (msg) => console.log(`ℹ️  ${msg}`),
  success: (msg) => console.log(`✅ ${msg}`),
  error: (msg) => console.log(`❌ ${msg}`),
  warning: (msg) => console.log(`⚠️  ${msg}`),
  step: (msg) => console.log(`\n🔄 ${msg}`)
};

async function apiCall(method, endpoint, data = null, token = null) {
  try {
    const config = {
      method,
      url: `${API_BASE}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
        'Origin': FRONTEND_URL
      }
    };

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return response.data;
  } catch (error) {
    if (error.response?.status === 404) {
      return null; // Not found is OK for cleanup
    }
    console.error(`API Error [${method} ${endpoint}]:`, {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message
    });
    throw error;
  }
}

async function cleanupTestData() {
  console.log('🧹 BrainMaker Academy - Test Data Cleanup\n');
  console.log('=' .repeat(60));
  
  try {
    // Step 1: Admin login
    log.step('Step 1: Admin Login');
    log.info('Logging in as admin...');
    const loginResponse = await apiCall('POST', '/securities/login', adminCredentials);
    
    if (!loginResponse.Token) {
      throw new Error('No token received from login');
    }
    
    log.success('Admin login successful!');
    const adminToken = loginResponse.Token;

    // Step 2: Find and delete test applications
    log.step('Step 2: Cleanup Test Applications');
    log.info('Fetching instructor applications...');
    const applications = await apiCall('GET', '/instructor-applications', null, adminToken);
    
    const testApplications = applications.filter(app => app.Email === testEmail);
    log.info(`Found ${testApplications.length} test applications to delete`);
    
    for (const app of testApplications) {
      try {
        log.info(`Deleting application: ${app.FirstName} ${app.LastName} (${app.Email})`);
        await apiCall('DELETE', `/instructor-applications/${app.Slug}`, null, adminToken);
        log.success(`Deleted application: ${app.Slug}`);
      } catch (error) {
        log.warning(`Failed to delete application ${app.Slug}: ${error.message}`);
      }
    }

    // Step 3: Find and delete test users
    log.step('Step 3: Cleanup Test Users');
    log.info('Fetching users...');
    const users = await apiCall('GET', '/users', null, adminToken);
    
    const testUsers = users.filter(user => user.Email === testEmail);
    log.info(`Found ${testUsers.length} test users to delete`);
    
    for (const user of testUsers) {
      try {
        log.info(`Deleting user: ${user.Firstname} ${user.Lastname} (${user.Email})`);
        await apiCall('DELETE', `/users/${user.Slug}`, null, adminToken);
        log.success(`Deleted user: ${user.Slug}`);
      } catch (error) {
        log.warning(`Failed to delete user ${user.Slug}: ${error.message}`);
      }
    }

    console.log('\n' + '=' .repeat(60));
    log.success('🎉 Test data cleanup completed successfully!');
    
  } catch (error) {
    console.log('\n' + '=' .repeat(60));
    log.error('❌ Cleanup failed!');
    console.error('Error details:', error.message);
    process.exit(1);
  }
}

// Run the cleanup
if (require.main === module) {
  cleanupTestData();
}

module.exports = { cleanupTestData };
