/**
 * Simple test to check if backend debugging is working
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3200/api';

async function apiRequest(method, endpoint, data = null) {
  const config = {
    method,
    url: `${API_BASE_URL}${endpoint}`,
    headers: {
      'Content-Type': 'application/json'
    }
  };
  
  if (data) {
    config.data = data;
  }
  
  try {
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`API Error (${method} ${endpoint}):`, error.response?.data || error.message);
    throw error;
  }
}

async function simpleDebugTest() {
  console.log('🧪 Simple Debug Test - Check Backend Logging');
  console.log('=' .repeat(50));
  
  // Upload a single media file
  console.log('\n1️⃣ Uploading test media...');
  const mediaData = {
    Name: 'simple-test.png',
    Hashname: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==',
    Extension: 'image/png',
    Size: 1000,
    SubDir: 'courses/covers'
  };
  
  const uploadedMedia = await apiRequest('POST', '/medias', mediaData);
  console.log('✅ Media uploaded:', uploadedMedia.Id);
  
  // Create course with media ID reference
  console.log('\n2️⃣ Creating course with media ID reference...');
  const coursePayload = {
    body: {
      Title: 'Simple Debug Test Course',
      Resume: 'Testing backend debugging',
      Keywords: ['debug'],
      Format: 2,
      Language: 'en',
      Free: true,
      Level: [1],
      Message: 'Debug message',
      Congratulation: 'Debug congratulation',
      Categories: [],
      Published: false,
      Archived: false,
      coverImageId: uploadedMedia.Id, // Using NEW approach
      CreatedBy: { Id: 1 }
    },
    origin: 'http://localhost:3000'
  };
  
  console.log('📋 Sending course payload with coverImageId:', coursePayload.body.coverImageId);
  console.log('📋 No nested media objects in payload');
  
  const createdCourse = await apiRequest('POST', '/courses', coursePayload);
  console.log('✅ Course created:', createdCourse.Id, createdCourse.Title);
  
  console.log('\n🔍 Check the backend server logs for debugging output!');
  console.log('Look for lines starting with 🔍, 🖼️, 🎥, or 🚨');
}

simpleDebugTest().catch(console.error);
