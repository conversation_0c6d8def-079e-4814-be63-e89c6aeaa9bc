/**
 * Comprehensive test simulating the exact browser workflow from instructor dashboard
 * This will help us understand why data is being saved in media folder instead of course folders
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3200/api';

async function apiRequest(method, endpoint, data = null) {
  const config = {
    method,
    url: `${API_BASE_URL}${endpoint}`,
    headers: {
      'Content-Type': 'application/json'
    }
  };
  
  if (data) {
    config.data = data;
  }
  
  try {
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`API Error (${method} ${endpoint}):`, error.response?.data || error.message);
    throw error;
  }
}

// Simulate the frontend media service getSubDirectory method exactly
function simulateFrontendGetSubDirectory(mimeType, context, userSlug, courseSlug) {
  console.log('🔍 Frontend getSubDirectory called with:', { mimeType, context, userSlug, courseSlug });
  
  // NEW ORGANIZED DIRECTORY STRUCTURE
  
  // Profile photos: profiles/userSlug/
  if (context === 'profile' && userSlug) {
    console.log('✅ Using profile directory:', `profiles/${userSlug}`);
    return `profiles/${userSlug}`;
  }

  // Course-specific directories: courses/courseSlug/
  if (context === 'course-cover' && courseSlug) {
    console.log('✅ Using course cover directory:', `courses/${courseSlug}/cover`);
    return `courses/${courseSlug}/cover`;
  }

  if (context === 'course-video' && courseSlug) {
    console.log('✅ Using course video directory:', `courses/${courseSlug}/video`);
    return `courses/${courseSlug}/video`;
  }

  // FALLBACK: If no courseSlug provided, use temporary structure
  if (context === 'course-cover') {
    console.log('⚠️ Using fallback course cover directory (no courseSlug):', 'courses/temp/cover');
    return 'courses/temp/cover';
  }

  if (context === 'course-video') {
    console.log('⚠️ Using fallback course video directory (no courseSlug):', 'courses/temp/video');
    return 'courses/temp/video';
  }

  // Generic fallback based on MIME type
  console.log('❌ Using generic fallback directory for mimeType:', mimeType);
  if (mimeType.startsWith('image/')) {
    console.log('❌ Fallback to media/images');
    return 'media/images';
  }
  if (mimeType.startsWith('video/')) {
    console.log('❌ Fallback to media/videos');
    return 'media/videos';
  }
  console.log('❌ Fallback to media/general');
  return 'media/general';
}

async function testBrowserCourseCreationWorkflow() {
  console.log('🌐 COMPREHENSIVE BROWSER COURSE CREATION TEST');
  console.log('Simulating exact instructor dashboard workflow');
  console.log('=' .repeat(80));
  
  // Step 1: Simulate user login and dashboard access
  console.log('\n1️⃣ Simulating instructor dashboard access...');
  const instructor = {
    Id: 1,
    Slug: 'D2174571',
    Name: 'Test Instructor'
  };
  console.log(`   Instructor: ${instructor.Name} (${instructor.Slug})`);
  
  // Step 2: Simulate course creation page load
  console.log('\n2️⃣ Simulating course creation page load...');
  const tempCourseSlug = `temp-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
  console.log(`   Generated temp course slug: ${tempCourseSlug}`);
  
  // Step 3: Simulate cover image upload from browser
  console.log('\n3️⃣ Simulating cover image upload from browser...');
  console.log('   User selects cover image file...');
  console.log('   Browser calls mediaService.uploadFile with:');
  
  const coverUploadOptions = {
    context: 'course-cover',
    userSlug: instructor.Slug,
    courseSlug: tempCourseSlug
  };
  
  console.log('   Upload options:', coverUploadOptions);
  
  // Simulate frontend getSubDirectory call
  const coverSubDir = simulateFrontendGetSubDirectory(
    'image/png', 
    coverUploadOptions.context, 
    coverUploadOptions.userSlug, 
    coverUploadOptions.courseSlug
  );
  
  console.log(`   Frontend determined SubDir: ${coverSubDir}`);
  
  // Simulate actual API call to /medias
  const coverMediaData = {
    Name: `${Date.now()}-browser-cover.png`,
    Hashname: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==',
    Extension: 'image/png',
    Size: 2000,
    SubDir: coverSubDir
  };
  
  console.log('   Sending to /api/medias:', {
    Name: coverMediaData.Name,
    SubDir: coverMediaData.SubDir,
    Extension: coverMediaData.Extension
  });
  
  const uploadedCover = await apiRequest('POST', '/medias', coverMediaData);
  
  console.log('   📤 Cover uploaded - Backend response:');
  console.log(`     ID: ${uploadedCover.Id}`);
  console.log(`     Hashname: ${uploadedCover.Hashname}`);
  console.log(`     SubDir: ${uploadedCover.SubDir}`);
  console.log(`     Prefix: ${uploadedCover.Hashname.substring(0, 4)}`);
  
  // Step 4: Simulate presentation video upload from browser
  console.log('\n4️⃣ Simulating presentation video upload from browser...');
  console.log('   User selects presentation video file...');
  console.log('   Browser calls mediaService.uploadFile with:');
  
  const videoUploadOptions = {
    context: 'course-video',
    userSlug: instructor.Slug,
    courseSlug: tempCourseSlug
  };
  
  console.log('   Upload options:', videoUploadOptions);
  
  // Simulate frontend getSubDirectory call
  const videoSubDir = simulateFrontendGetSubDirectory(
    'video/mp4', 
    videoUploadOptions.context, 
    videoUploadOptions.userSlug, 
    videoUploadOptions.courseSlug
  );
  
  console.log(`   Frontend determined SubDir: ${videoSubDir}`);
  
  // Simulate actual API call to /medias
  const videoMediaData = {
    Name: `${Date.now()}-browser-video.mp4`,
    Hashname: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==',
    Extension: 'video/mp4',
    Size: 8000,
    SubDir: videoSubDir
  };
  
  console.log('   Sending to /api/medias:', {
    Name: videoMediaData.Name,
    SubDir: videoMediaData.SubDir,
    Extension: videoMediaData.Extension
  });
  
  const uploadedVideo = await apiRequest('POST', '/medias', videoMediaData);
  
  console.log('   📤 Video uploaded - Backend response:');
  console.log(`     ID: ${uploadedVideo.Id}`);
  console.log(`     Hashname: ${uploadedVideo.Hashname}`);
  console.log(`     SubDir: ${uploadedVideo.SubDir}`);
  console.log(`     Prefix: ${uploadedVideo.Hashname.substring(0, 4)}`);
  
  // Step 5: Simulate course creation form submission
  console.log('\n5️⃣ Simulating course creation form submission...');
  console.log('   User fills out course form and clicks "Create Course"...');
  
  const courseFormData = {
    Title: 'Browser Test Course',
    Resume: 'This course was created via browser simulation to test organized directory structure',
    Keywords: ['browser', 'test', 'organized'],
    Prerequisites: [],
    Goals: [],
    Format: 2,
    Language: 'en',
    Free: true,
    Level: [1],
    Message: 'Welcome to the browser test course',
    Congratulation: 'Congratulations on completing the browser test',
    Categories: [],
    Published: false,
    Archived: false,
    coverImageId: uploadedCover.Id,
    presentationVideoId: uploadedVideo.Id,
    Slug: tempCourseSlug,
    CreatedBy: { Id: instructor.Id }
  };
  
  console.log('   Course form data:');
  console.log(`     Title: ${courseFormData.Title}`);
  console.log(`     Cover Image ID: ${courseFormData.coverImageId}`);
  console.log(`     Video ID: ${courseFormData.presentationVideoId}`);
  console.log(`     Temp Slug: ${courseFormData.Slug}`);
  
  const coursePayload = {
    body: courseFormData,
    origin: 'http://localhost:5174'
  };
  
  const createdCourse = await apiRequest('POST', '/courses', coursePayload);
  
  console.log('   📚 Course created - Backend response:');
  console.log(`     ID: ${createdCourse.Id}`);
  console.log(`     Title: ${createdCourse.Title}`);
  console.log(`     Slug: ${createdCourse.Slug}`);
  
  // Step 6: Verify the actual file storage locations
  console.log('\n6️⃣ Verifying actual file storage locations...');
  
  const retrievedCourse = await apiRequest('GET', `/courses/${createdCourse.Id}`);
  
  console.log('   📊 Retrieved course media information:');
  if (retrievedCourse.CoverImage) {
    console.log(`     Cover Image:`);
    console.log(`       ID: ${retrievedCourse.CoverImage.Id}`);
    console.log(`       Hashname: ${retrievedCourse.CoverImage.Hashname}`);
    console.log(`       SubDir: ${retrievedCourse.CoverImage.SubDir}`);
  }
  
  if (retrievedCourse.PresentationVideo) {
    console.log(`     Presentation Video:`);
    console.log(`       ID: ${retrievedCourse.PresentationVideo.Id}`);
    console.log(`       Hashname: ${retrievedCourse.PresentationVideo.Hashname}`);
    console.log(`       SubDir: ${retrievedCourse.PresentationVideo.SubDir}`);
  }
  
  // Step 7: Analysis and diagnosis
  console.log('\n7️⃣ ANALYSIS AND DIAGNOSIS:');
  
  const expectedCoverDir = `courses/${tempCourseSlug}/cover`;
  const expectedVideoDir = `courses/${tempCourseSlug}/video`;
  
  const actualCoverDir = retrievedCourse.CoverImage?.SubDir;
  const actualVideoDir = retrievedCourse.PresentationVideo?.SubDir;
  
  console.log('   📁 Directory Structure Analysis:');
  console.log(`     Expected Cover Dir: ${expectedCoverDir}`);
  console.log(`     Actual Cover Dir: ${actualCoverDir}`);
  console.log(`     Cover Dir Match: ${actualCoverDir === expectedCoverDir ? '✅ CORRECT' : '❌ WRONG'}`);
  
  console.log(`     Expected Video Dir: ${expectedVideoDir}`);
  console.log(`     Actual Video Dir: ${actualVideoDir}`);
  console.log(`     Video Dir Match: ${actualVideoDir === expectedVideoDir ? '✅ CORRECT' : '❌ WRONG'}`);
  
  // Check if files ended up in media folder
  const coverInMediaFolder = actualCoverDir?.startsWith('media/');
  const videoInMediaFolder = actualVideoDir?.startsWith('media/');
  
  console.log('\n   🚨 PROBLEM DIAGNOSIS:');
  if (coverInMediaFolder || videoInMediaFolder) {
    console.log('   ❌ CONFIRMED: Files are being saved in media folder instead of course folders!');
    console.log(`     Cover in media folder: ${coverInMediaFolder ? 'YES' : 'NO'}`);
    console.log(`     Video in media folder: ${videoInMediaFolder ? 'YES' : 'NO'}`);
    
    console.log('\n   🔍 ROOT CAUSE ANALYSIS:');
    if (coverSubDir.startsWith('media/')) {
      console.log('     ❌ Frontend getSubDirectory is returning media/ path for cover');
      console.log('     ❌ This means courseSlug is not being passed correctly or context is wrong');
    }
    if (videoSubDir.startsWith('media/')) {
      console.log('     ❌ Frontend getSubDirectory is returning media/ path for video');
      console.log('     ❌ This means courseSlug is not being passed correctly or context is wrong');
    }
  } else {
    console.log('   ✅ SUCCESS: Files are being saved in organized course directories!');
  }
  
  return {
    success: !coverInMediaFolder && !videoInMediaFolder,
    courseId: createdCourse.Id,
    tempCourseSlug,
    actualCoverDir,
    actualVideoDir,
    expectedCoverDir,
    expectedVideoDir
  };
}

testBrowserCourseCreationWorkflow().catch(console.error);
