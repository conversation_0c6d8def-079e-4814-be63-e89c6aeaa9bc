<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug CourseCard Images</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .debug-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .image-test {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
        }
        .image-test img {
            width: 100px;
            height: 60px;
            object-fit: cover;
            border-radius: 4px;
        }
        .code {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <h1>🔍 Debug CourseCard Images</h1>
    <p>This page will test if the CourseCard images are working correctly by simulating the frontend behavior.</p>

    <div class="debug-container">
        <h2>📡 API Test Results</h2>
        <div id="api-results">Loading...</div>
    </div>

    <div class="debug-container">
        <h2>🖼️ Image URL Tests</h2>
        <div id="image-results">Loading...</div>
    </div>

    <div class="debug-container">
        <h2>🌐 Frontend Integration Test</h2>
        <div id="frontend-results">Loading...</div>
    </div>

    <div class="debug-container">
        <h2>🔧 Debug Instructions</h2>
        <div class="info test-result">
            <strong>To debug the actual CoursesPage:</strong>
            <ol>
                <li>Open <a href="http://localhost:5174/courses" target="_blank">http://localhost:5174/courses</a></li>
                <li>Open Developer Tools (F12)</li>
                <li>Go to Console tab</li>
                <li>Run this command:</li>
            </ol>
            <div class="code">
// Check if CourseCard components are rendered
document.querySelectorAll('[data-testid="course-card"], .course-card, img[alt*="course"]').length

// Check for images with course cover URLs
Array.from(document.querySelectorAll('img')).filter(img => 
  img.src.includes('upload/courses/covers')
).length

// Check for broken images
Array.from(document.querySelectorAll('img')).filter(img => 
  !img.complete || img.naturalWidth === 0
).length
            </div>
        </div>
    </div>

    <script>
        async function debugCourseCardImages() {
            const apiResults = document.getElementById('api-results');
            const imageResults = document.getElementById('image-results');
            const frontendResults = document.getElementById('frontend-results');

            try {
                // Test 1: API Call
                apiResults.innerHTML = '<div class="info test-result">Testing API...</div>';
                
                const response = await fetch('http://localhost:3200/api/courses/15/0?justPublished=true');
                const courses = await response.json();
                
                apiResults.innerHTML = `
                    <div class="success test-result">
                        ✅ API Success: ${courses.length} courses loaded
                    </div>
                `;

                // Test 2: Find target course
                const targetCourse = courses.find(course => 
                    course.Title && course.Title.toLowerCase().includes('demo course two')
                );

                if (targetCourse) {
                    apiResults.innerHTML += `
                        <div class="success test-result">
                            ✅ Target course found: "${targetCourse.Title}"<br>
                            📸 Cover Image: ${targetCourse.CoverImage?.Hashname || 'NONE'}<br>
                            👤 Instructor Photo: ${targetCourse.CreatedBy?.Photo?.Hashname || 'NONE'}
                        </div>
                    `;

                    // Test 3: Image URLs
                    if (targetCourse.CoverImage?.Hashname) {
                        const imageUrl = `http://localhost:3200/${targetCourse.CoverImage.Hashname}`;
                        
                        imageResults.innerHTML = `
                            <div class="info test-result">Testing image URL: ${imageUrl}</div>
                            <div class="image-test">
                                <img src="${imageUrl}" alt="Course Cover" onload="this.nextElementSibling.innerHTML='✅ Image loaded successfully'" onerror="this.nextElementSibling.innerHTML='❌ Image failed to load'">
                                <span>Loading...</span>
                            </div>
                        `;

                        // Test the URL with fetch
                        try {
                            const imgResponse = await fetch(imageUrl, { method: 'HEAD' });
                            if (imgResponse.ok) {
                                imageResults.innerHTML += `
                                    <div class="success test-result">
                                        ✅ Image URL accessible: ${imgResponse.status} ${imgResponse.statusText}
                                    </div>
                                `;
                            } else {
                                imageResults.innerHTML += `
                                    <div class="error test-result">
                                        ❌ Image URL failed: ${imgResponse.status} ${imgResponse.statusText}
                                    </div>
                                `;
                            }
                        } catch (imgError) {
                            imageResults.innerHTML += `
                                <div class="error test-result">
                                    ❌ Image URL error: ${imgError.message}
                                </div>
                            `;
                        }
                    } else {
                        imageResults.innerHTML = `
                            <div class="warning test-result">
                                ⚠️ Target course has no cover image
                            </div>
                        `;
                    }

                    // Test 4: Frontend Integration
                    frontendResults.innerHTML = `
                        <div class="info test-result">
                            <strong>What CourseCard should receive:</strong><br>
                            Title: "${targetCourse.Title}"<br>
                            CoverImage.Hashname: "${targetCourse.CoverImage?.Hashname || 'undefined'}"<br>
                            CreatedBy.Firstname: "${targetCourse.CreatedBy?.Firstname || 'undefined'}"<br>
                            CreatedBy.Lastname: "${targetCourse.CreatedBy?.Lastname || 'undefined'}"
                        </div>
                    `;

                    // Simulate CourseCard's getCourseImage() method
                    const getCourseImage = () => {
                        if (targetCourse.CoverImage?.Hashname) {
                            return `http://localhost:3200/${targetCourse.CoverImage.Hashname}`;
                        }
                        return undefined;
                    };

                    const courseImageUrl = getCourseImage();
                    frontendResults.innerHTML += `
                        <div class="info test-result">
                            <strong>CourseCard.getCourseImage() result:</strong><br>
                            "${courseImageUrl || 'undefined'}"
                        </div>
                    `;

                    if (courseImageUrl) {
                        frontendResults.innerHTML += `
                            <div class="success test-result">
                                ✅ CourseCard should display image correctly
                            </div>
                        `;
                    } else {
                        frontendResults.innerHTML += `
                            <div class="warning test-result">
                                ⚠️ CourseCard will show placeholder (no image URL)
                            </div>
                        `;
                    }

                } else {
                    apiResults.innerHTML += `
                        <div class="error test-result">
                            ❌ Target course not found
                        </div>
                    `;
                }

            } catch (error) {
                apiResults.innerHTML = `
                    <div class="error test-result">
                        ❌ API Error: ${error.message}
                    </div>
                `;
            }
        }

        // Run the debug when page loads
        debugCourseCardImages();
    </script>
</body>
</html>
