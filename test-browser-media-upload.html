<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Media Upload</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            padding: 20px;
            text-align: center;
            margin: 20px 0;
        }
        .upload-area:hover {
            border-color: #007bff;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background-color: #007bff;
            width: 0%;
            transition: width 0.3s ease;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>Media Upload Test</h1>
    <p>This test simulates exactly what the React app does when uploading media files.</p>

    <div class="upload-area" onclick="document.getElementById('fileInput').click()">
        <p>Click here to select a file (or drag and drop)</p>
        <input type="file" id="fileInput" style="display: none;" accept="image/*,video/*">
    </div>

    <div class="progress" id="progressContainer" style="display: none;">
        <div class="progress-bar" id="progressBar"></div>
    </div>

    <div id="result"></div>
    <div class="log" id="log"></div>

    <script>
        const BASE_URL = 'http://localhost:3200/api';
        let logContent = '';

        function log(message) {
            logContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            document.getElementById('log').textContent = logContent;
            console.log(message);
        }

        function showProgress(percentage) {
            const container = document.getElementById('progressContainer');
            const bar = document.getElementById('progressBar');
            container.style.display = 'block';
            bar.style.width = percentage + '%';
        }

        function hideProgress() {
            document.getElementById('progressContainer').style.display = 'none';
        }

        function showResult(message, isError = false) {
            const result = document.getElementById('result');
            result.className = 'result ' + (isError ? 'error' : 'success');
            result.innerHTML = message;
        }

        // Convert file to base64 (same as media service)
        function convertToBase64(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => {
                    const base64 = reader.result.split(',')[1]; // Remove data:image/jpeg;base64, prefix
                    resolve(base64);
                };
                reader.onerror = reject;
                reader.readAsDataURL(file);
            });
        }

        // Get subdirectory (same as media service)
        function getSubDirectory(mimeType) {
            if (mimeType.startsWith('image/')) return 'courses/covers';
            if (mimeType.startsWith('video/')) return 'courses/videos';
            if (mimeType.startsWith('audio/')) return 'courses/videos';
            if (mimeType.includes('pdf')) return 'documents';
            if (mimeType.includes('document') || mimeType.includes('text')) return 'documents';
            return 'documents';
        }

        // Simulate media service uploadFile method
        async function uploadFile(file) {
            try {
                log(`Starting upload for: ${file.name} (${file.size} bytes, ${file.type})`);
                showProgress(0);

                // Convert to base64
                log('Converting file to base64...');
                const base64Data = await convertToBase64(file);
                showProgress(50);
                log(`Base64 conversion complete (${base64Data.length} characters)`);

                // Prepare data
                const fileName = `${Date.now()}-${file.name}`;
                const subDir = getSubDirectory(file.type);
                
                const mediaData = {
                    Name: fileName,
                    Hashname: base64Data,
                    Extension: file.type,
                    Size: file.size,
                    SubDir: subDir
                };

                log(`Prepared media data:`);
                log(`  - Name: ${mediaData.Name}`);
                log(`  - Extension: ${mediaData.Extension}`);
                log(`  - Size: ${mediaData.Size}`);
                log(`  - SubDir: ${mediaData.SubDir}`);
                log(`  - Base64 length: ${mediaData.Hashname.length}`);

                showProgress(70);

                // Call API
                log('Calling /api/medias endpoint...');
                const response = await fetch(`${BASE_URL}/medias`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(mediaData)
                });

                showProgress(90);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const responseData = await response.json();
                showProgress(100);

                log('✅ Upload successful!');
                log(`Response: ${JSON.stringify(responseData, null, 2)}`);

                // Create MediaFile object (same as service)
                const mediaFile = {
                    id: responseData.Id?.toString() || Date.now().toString(),
                    filename: responseData.Hashname || fileName,
                    originalUsername: responseData.Name || file.name,
                    mimeType: responseData.Extension || file.type,
                    size: responseData.Size || file.size,
                    url: `http://localhost:3200/public/upload/${responseData.SubDir || subDir}/${responseData.Hashname || fileName}`,
                    subDir: responseData.SubDir || subDir,
                    uploadedBy: 'current-user',
                    createdAt: responseData.CreatedAt || new Date().toISOString(),
                    updatedAt: responseData.UpdatedAt || new Date().toISOString(),
                    mediaId: responseData.Id,
                    slug: responseData.Slug
                };

                log('Created MediaFile object:');
                log(JSON.stringify(mediaFile, null, 2));

                // Test file accessibility
                log('Testing file accessibility...');
                const fileResponse = await fetch(mediaFile.url);
                if (fileResponse.ok) {
                    log('✅ File is accessible via URL');
                } else {
                    log(`❌ File not accessible: ${fileResponse.status}`);
                }

                hideProgress();
                showResult(`
                    <h3>Upload Successful!</h3>
                    <p><strong>File:</strong> ${file.name}</p>
                    <p><strong>Media ID:</strong> ${mediaFile.mediaId}</p>
                    <p><strong>Filename:</strong> ${mediaFile.filename}</p>
                    <p><strong>URL:</strong> <a href="${mediaFile.url}" target="_blank">${mediaFile.url}</a></p>
                    <p><strong>SubDir:</strong> ${mediaFile.subDir}</p>
                `);

                return mediaFile;

            } catch (error) {
                hideProgress();
                log(`❌ Upload failed: ${error.message}`);
                showResult(`<h3>Upload Failed!</h3><p>${error.message}</p>`, true);
                throw error;
            }
        }

        // File input handler
        document.getElementById('fileInput').addEventListener('change', async (event) => {
            const file = event.target.files[0];
            if (!file) return;

            logContent = ''; // Clear log
            document.getElementById('log').textContent = '';
            document.getElementById('result').innerHTML = '';

            try {
                await uploadFile(file);
            } catch (error) {
                console.error('Upload error:', error);
            }
        });

        // Drag and drop support
        const uploadArea = document.querySelector('.upload-area');
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#007bff';
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#ccc';
        });

        uploadArea.addEventListener('drop', async (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#ccc';
            
            const file = e.dataTransfer.files[0];
            if (!file) return;

            logContent = ''; // Clear log
            document.getElementById('log').textContent = '';
            document.getElementById('result').innerHTML = '';

            try {
                await uploadFile(file);
            } catch (error) {
                console.error('Upload error:', error);
            }
        });

        log('Media upload test ready. Select a file to test the upload functionality.');
    </script>
</body>
</html>
