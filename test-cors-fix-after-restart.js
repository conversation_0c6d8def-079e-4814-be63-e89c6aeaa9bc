const axios = require('axios');

async function testCORSFix() {
    console.log('🧪 Testing CORS Fix After Backend Restart...\n');

    const testFiles = [
        'cvr_18af6c7318624bf885f97a4fedf09bdf-database-test-cover.png',
        'prs_4e2a2c913d30409585127e268d9eaa8f-database-test-video.mp4'
    ];

    for (const filename of testFiles) {
        console.log(`📁 Testing: ${filename}`);
        
        const fileUrl = `http://localhost:3200/upload/D2174571/content/${filename.startsWith('cvr_') ? 'coverimage' : 'presentation'}/${filename}`;
        
        try {
            const response = await axios.get(fileUrl, {
                headers: {
                    'Origin': 'http://localhost:5174',
                    'Access-Control-Request-Method': 'GET',
                    'Access-Control-Request-Headers': 'Content-Type'
                },
                timeout: 5000
            });

            console.log(`✅ Status: ${response.status}`);
            console.log(`📊 Content-Type: ${response.headers['content-type']}`);
            console.log(`🔒 CORS Headers:`);
            console.log(`   - Access-Control-Allow-Origin: ${response.headers['access-control-allow-origin'] || 'NOT SET'}`);
            console.log(`   - Access-Control-Allow-Methods: ${response.headers['access-control-allow-methods'] || 'NOT SET'}`);
            console.log(`   - Cross-Origin-Resource-Policy: ${response.headers['cross-origin-resource-policy'] || 'NOT SET'}`);
            console.log(`📏 Content-Length: ${response.headers['content-length']} bytes`);
            
            if (response.headers['access-control-allow-origin']) {
                console.log(`✅ SUCCESS: File accessible with CORS headers!`);
            } else {
                console.log(`❌ FAILED: Missing CORS headers!`);
            }
            
        } catch (error) {
            console.log(`❌ ERROR: ${error.message}`);
            if (error.response) {
                console.log(`   Status: ${error.response.status}`);
                console.log(`   Headers:`, error.response.headers);
            }
        }
        
        console.log(''); // Empty line for separation
    }
}

testCORSFix().catch(console.error);
