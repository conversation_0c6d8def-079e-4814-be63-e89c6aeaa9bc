const axios = require('axios');

/**
 * Test the CourseDetailPage to ensure it's properly utilizing the API service
 * This simulates what the CourseDetailPage should receive after our fixes
 */

const API_BASE = 'http://localhost:3200/api';
const ENVIRONMENT_PATH = 'http://localhost:3200';

// Simulate the course service's formatCourse method
function formatCourse(course) {
  if (!course) return course;

  const constructMediaUrl = (hashname) => {
    if (!hashname) return '';
    if (hashname.startsWith('http://') || hashname.startsWith('https://')) {
      return hashname;
    }
    const cleanHashname = hashname.startsWith('/') ? hashname.substring(1) : hashname;
    return `${ENVIRONMENT_PATH}/${cleanHashname}`;
  };

  // Format cover image URL
  if (course.CoverImage?.Hashname) {
    course.CoverImage.Hashname = constructMediaUrl(course.CoverImage.Hashname);
  }

  // Format presentation video URL
  if (course.PresentationVideo?.Hashname) {
    course.PresentationVideo.Hashname = constructMediaUrl(course.PresentationVideo.Hashname);
  }

  // Format creator photo
  if (course.CreatedBy?.Photo?.Hashname) {
    course.CreatedBy.Photo.Hashname = constructMediaUrl(course.CreatedBy.Photo.Hashname);
  }

  return course;
}

// Simulate the transformCourseForDetail function from CourseDetailPage
function transformCourseForDetail(course) {
  // Calculate average rating
  const ratings = course.Ratings || [];
  const averageRating = ratings.length > 0
    ? ratings.reduce((sum, r) => sum + (r.Rating || 0), 0) / ratings.length
    : 0;

  // Get instructor information
  const instructor = {
    name: course.CreatedBy
      ? `${course.CreatedBy.Firstname || ''} ${course.CreatedBy.Lastname || ''}`.trim() || 'Unknown Instructor'
      : 'Unknown Instructor',
    bio: course.CreatedBy?.About || 'No bio available',
    avatar: course.CreatedBy?.Photo?.Hashname || undefined,
    email: course.CreatedBy?.Email || '',
    slug: course.CreatedBy?.Slug || ''
  };

  // Get course image - already formatted by course service
  const courseImage = course.CoverImage?.Hashname || undefined;

  // Get price information
  const price = course.Free ? 0 : (course.NewPrice || course.Price || 0);
  const originalPrice = course.Free ? 0 : course.Price;
  const hasDiscount = !course.Free && course.NewPrice && course.Price && course.NewPrice < course.Price;

  return {
    id: course.Id || 0,
    slug: course.Slug || '',
    title: course.Title || 'Untitled Course',
    description: course.Resume ? course.Resume.replace(/<[^>]*>/g, '') : 'No description available',
    price,
    originalPrice,
    hasDiscount,
    instructor,
    rating: Number(averageRating.toFixed(1)),
    reviewCount: ratings.length,
    image: courseImage,
    sections: course.Sections || [],
    categories: course.Categories || [],
    free: course.Free || false
  };
}

async function testCourseDetailPage() {
  console.log('🔍 Testing Course Detail Page API Integration');
  console.log('=' .repeat(60));

  const targetSlug = 'd77df2a16a0745a3ad04f8d9a9e797de';

  try {
    // Step 1: Test the API endpoint that CourseDetailPage uses
    console.log('\n1️⃣ TESTING API ENDPOINT');
    console.log('-' .repeat(40));
    
    console.log(`📡 Calling: GET ${API_BASE}/courses/public/${targetSlug}`);
    const rawResponse = await axios.get(`${API_BASE}/courses/public/${targetSlug}`);
    const rawCourse = rawResponse.data;
    
    console.log(`✅ API Response received`);
    console.log(`   - Course Title: "${rawCourse.Title}"`);
    console.log(`   - Raw CoverImage.Hashname: "${rawCourse.CoverImage?.Hashname || 'NONE'}"`);
    console.log(`   - Raw CreatedBy.Photo.Hashname: "${rawCourse.CreatedBy?.Photo?.Hashname || 'NONE'}"`);

    // Step 2: Apply course service formatting (what getBySlug should do now)
    console.log('\n2️⃣ APPLYING COURSE SERVICE FORMATTING');
    console.log('-' .repeat(40));
    
    const formattedCourse = formatCourse({ ...rawCourse });
    console.log(`✅ Course service formatting applied`);
    console.log(`   - Formatted CoverImage.Hashname: "${formattedCourse.CoverImage?.Hashname || 'NONE'}"`);
    console.log(`   - Formatted CreatedBy.Photo.Hashname: "${formattedCourse.CreatedBy?.Photo?.Hashname || 'NONE'}"`);

    // Step 3: Apply CourseDetailPage transformation
    console.log('\n3️⃣ APPLYING COURSE DETAIL PAGE TRANSFORMATION');
    console.log('-' .repeat(40));
    
    const transformedCourse = transformCourseForDetail(formattedCourse);
    console.log(`✅ CourseDetailPage transformation applied`);
    console.log(`   - Final course image: "${transformedCourse.image || 'NONE'}"`);
    console.log(`   - Final instructor avatar: "${transformedCourse.instructor.avatar || 'NONE'}"`);
    console.log(`   - Instructor name: "${transformedCourse.instructor.name}"`);
    console.log(`   - Course title: "${transformedCourse.title}"`);
    console.log(`   - Price: ${transformedCourse.free ? 'FREE' : `$${transformedCourse.price}`}`);
    console.log(`   - Rating: ${transformedCourse.rating}/5 (${transformedCourse.reviewCount} reviews)`);

    // Step 4: Test the URLs
    console.log('\n4️⃣ TESTING FINAL URLs');
    console.log('-' .repeat(40));
    
    if (transformedCourse.image) {
      try {
        const imageResponse = await axios.head(transformedCourse.image, { timeout: 5000 });
        console.log(`✅ Course cover image accessible: ${imageResponse.status}`);
        console.log(`   URL: ${transformedCourse.image}`);
      } catch (error) {
        console.log(`❌ Course cover image failed: ${error.response?.status || 'Network Error'}`);
        console.log(`   URL: ${transformedCourse.image}`);
      }
    } else {
      console.log(`⚠️  No course cover image URL`);
    }

    if (transformedCourse.instructor.avatar) {
      try {
        const avatarResponse = await axios.head(transformedCourse.instructor.avatar, { timeout: 5000 });
        console.log(`✅ Instructor avatar accessible: ${avatarResponse.status}`);
        console.log(`   URL: ${transformedCourse.instructor.avatar}`);
      } catch (error) {
        console.log(`❌ Instructor avatar failed: ${error.response?.status || 'Network Error'}`);
        console.log(`   URL: ${transformedCourse.instructor.avatar}`);
      }
    } else {
      console.log(`⚠️  No instructor avatar URL`);
    }

    // Step 5: Summary
    console.log('\n5️⃣ COURSE DETAIL PAGE SUMMARY');
    console.log('-' .repeat(40));
    
    console.log(`📊 What the CourseDetailPage should display:`);
    console.log(`   - Course: "${transformedCourse.title}"`);
    console.log(`   - Instructor: ${transformedCourse.instructor.name}`);
    console.log(`   - Price: ${transformedCourse.free ? 'FREE' : `$${transformedCourse.price}`}`);
    console.log(`   - Cover Image: ${transformedCourse.image ? '✅ Available' : '❌ Missing'}`);
    console.log(`   - Instructor Avatar: ${transformedCourse.instructor.avatar ? '✅ Available' : '❌ Missing'}`);
    console.log(`   - Sections: ${transformedCourse.sections.length} sections`);
    console.log(`   - Categories: ${transformedCourse.categories.length} categories`);

  } catch (error) {
    console.error('💥 Course detail page test failed:', error.message);
  }

  console.log('\n🎉 Course Detail Page test completed!');
  console.log('\n💡 Manual verification:');
  console.log(`   👉 Open: http://localhost:5174/courses/${targetSlug}`);
  console.log('   🔍 Check if the cover image and instructor avatar are displayed');
  console.log('   📱 The page should show the course details with proper images');
}

// Run the test
testCourseDetailPage().catch(console.error);
