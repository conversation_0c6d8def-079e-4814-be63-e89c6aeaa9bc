#!/bin/bash

# Script to migrate all Ant Design imports to shadcn/ui compatibility layer

echo "🚀 Starting Ant Design to shadcn/ui migration..."

# Find all TypeScript/TSX files that import from 'antd'
files=$(find src -name "*.tsx" -o -name "*.ts" | xargs grep -l "from 'antd'")

count=0
total=$(echo "$files" | wc -l)

echo "📁 Found $total files to migrate"

for file in $files; do
    count=$((count + 1))
    echo "[$count/$total] Migrating: $file"
    
    # Replace direct antd imports with compatibility layer imports
    sed -i '' "s|from 'antd'|from '../antd'|g" "$file" 2>/dev/null || \
    sed -i '' "s|from 'antd'|from '../../antd'|g" "$file" 2>/dev/null || \
    sed -i '' "s|from 'antd'|from '../../../antd'|g" "$file" 2>/dev/null || \
    sed -i '' "s|from 'antd'|from '../../../../antd'|g" "$file" 2>/dev/null || \
    sed -i '' "s|from 'antd'|from '../../../../../antd'|g" "$file" 2>/dev/null
    
    # Try to determine correct relative path based on file location
    if [[ "$file" == src/components/* ]]; then
        sed -i '' "s|from 'antd'|from '../antd'|g" "$file"
    elif [[ "$file" == src/pages/* ]]; then
        sed -i '' "s|from 'antd'|from '../../components/antd'|g" "$file"
    elif [[ "$file" == src/hooks/* ]]; then
        sed -i '' "s|from 'antd'|from '../components/antd'|g" "$file"
    else
        # Default fallback
        sed -i '' "s|from 'antd'|from '../components/antd'|g" "$file"
    fi
done

echo "✅ Migration complete!"
echo "🔍 Checking for remaining antd imports..."

remaining=$(find src -name "*.tsx" -o -name "*.ts" | xargs grep -l "from 'antd'" | wc -l)
echo "📊 Remaining files with antd imports: $remaining"

if [ "$remaining" -gt 0 ]; then
    echo "⚠️  Some files still have antd imports. Manual review needed:"
    find src -name "*.tsx" -o -name "*.ts" | xargs grep -l "from 'antd'"
fi
