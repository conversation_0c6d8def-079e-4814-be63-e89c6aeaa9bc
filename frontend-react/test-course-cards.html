<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Course Card Test</title>
    <style>
        body {
            font-family: 'Nunito', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-top: 20px;
        }
        .card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 24px rgba(0,0,0,0.15);
        }
        .card-image {
            width: 100%;
            height: 200px;
            background: linear-gradient(135deg, #dc3545, #c82333);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            font-weight: 600;
        }
        .card-content {
            padding: 20px;
        }
        .card-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #1a1a1a;
        }
        .card-instructor {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 12px;
        }
        .card-price {
            font-size: 20px;
            font-weight: 700;
            color: #dc3545;
        }
        .status {
            padding: 8px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 600;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .test-info {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Course Card Cover Image Test</h1>
        
        <div class="test-info">
            <h2>Test Results</h2>
            <div id="test-status" class="status">Testing...</div>
            <div id="test-details"></div>
        </div>

        <h2>Sample Course Cards</h2>
        <div class="grid" id="course-grid">
            <!-- Course cards will be populated here -->
        </div>
    </div>

    <script>
        // Test data with different cover image scenarios
        const testCourses = [
            {
                id: 1,
                title: "React Fundamentals",
                instructor: "John Doe",
                price: "$49.99",
                coverImage: "http://localhost:3200/public/upload/images/react-course.jpg"
            },
            {
                id: 2,
                title: "Advanced JavaScript",
                instructor: "Jane Smith",
                price: "Free",
                coverImage: null // No cover image
            },
            {
                id: 3,
                title: "Node.js Backend Development",
                instructor: "Mike Johnson",
                price: "$79.99",
                coverImage: "http://localhost:3200/public/upload/images/nodejs-course.jpg"
            },
            {
                id: 4,
                title: "Full Stack Web Development",
                instructor: "Sarah Wilson",
                price: "$99.99",
                coverImage: "https://via.placeholder.com/400x200/dc3545/ffffff?text=Full+Stack"
            }
        ];

        function createCourseCard(course) {
            const card = document.createElement('div');
            card.className = 'card';
            
            const imageSection = course.coverImage 
                ? `<img src="${course.coverImage}" alt="${course.title}" style="width: 100%; height: 200px; object-fit: cover;" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">`
                : '';
            
            const fallbackSection = `<div class="card-image" style="${course.coverImage ? 'display: none;' : ''}">📚 ${course.title}</div>`;
            
            card.innerHTML = `
                ${imageSection}
                ${fallbackSection}
                <div class="card-content">
                    <div class="card-title">${course.title}</div>
                    <div class="card-instructor">By ${course.instructor}</div>
                    <div class="card-price">${course.price}</div>
                </div>
            `;
            
            return card;
        }

        function renderCourses() {
            const grid = document.getElementById('course-grid');
            testCourses.forEach(course => {
                grid.appendChild(createCourseCard(course));
            });
        }

        async function testCourseAPI() {
            const statusEl = document.getElementById('test-status');
            const detailsEl = document.getElementById('test-details');
            
            try {
                statusEl.textContent = 'Testing API connection...';
                
                // Test the courses API
                const response = await fetch('http://localhost:3200/api/courses/10/0?justPublished=justPublished');
                
                if (response.ok) {
                    const courses = await response.json();
                    statusEl.className = 'status success';
                    statusEl.textContent = '✅ API Connection Successful';
                    
                    detailsEl.innerHTML = `
                        <p><strong>Courses found:</strong> ${courses.length}</p>
                        <p><strong>Courses with cover images:</strong> ${courses.filter(c => c.CoverImage?.Hashname).length}</p>
                        <p><strong>Sample course data:</strong></p>
                        <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px;">
${JSON.stringify(courses[0] || {}, null, 2)}
                        </pre>
                    `;
                } else {
                    throw new Error(`API returned ${response.status}`);
                }
            } catch (error) {
                statusEl.className = 'status error';
                statusEl.textContent = '❌ API Connection Failed';
                detailsEl.innerHTML = `
                    <p><strong>Error:</strong> ${error.message}</p>
                    <p>Make sure the backend server is running on http://localhost:3200</p>
                `;
            }
        }

        // Initialize the test
        document.addEventListener('DOMContentLoaded', () => {
            renderCourses();
            testCourseAPI();
        });
    </script>
</body>
</html>
