// Test script to verify media upload functionality
// Run this in the browser console on the course creator page

console.log('🧪 Testing Media Upload Functionality');

// Test 1: Check if media service is available
if (typeof window !== 'undefined' && window.mediaService) {
  console.log('✅ Media service is available');
} else {
  console.log('❌ Media service not found');
}

// Test 2: Check if course creator component is loaded
const courseCreatorElement = document.querySelector('[data-testid="course-creator"]') || 
                            document.querySelector('.course-creator') ||
                            document.querySelector('form');

if (courseCreatorElement) {
  console.log('✅ Course creator component found');
} else {
  console.log('❌ Course creator component not found');
}

// Test 3: Check if file upload inputs exist
const coverImageInput = document.querySelector('#cover-image-upload-step4') || 
                       document.querySelector('#cover-image-upload');
const videoInput = document.querySelector('#video-upload-step4') || 
                  document.querySelector('#video-upload');

console.log('Cover image input:', coverImageInput ? '✅ Found' : '❌ Not found');
console.log('Video input:', videoInput ? '✅ Found' : '❌ Not found');

// Test 4: Check if we're on the correct step
const currentStepElement = document.querySelector('[data-step]') || 
                          document.querySelector('.step-indicator') ||
                          document.querySelector('.current-step');

if (currentStepElement) {
  console.log('✅ Step indicator found');
} else {
  console.log('❌ Step indicator not found');
}

// Test 5: Create a mock file upload test
function createMockFile(name, size, type) {
  const file = new File(['mock content'], name, { type, lastModified: Date.now() });
  Object.defineProperty(file, 'size', { value: size });
  return file;
}

// Test function to simulate file upload
function testFileUpload() {
  console.log('🧪 Testing file upload simulation...');
  
  const mockImageFile = createMockFile('test-cover.jpg', 1024 * 1024, 'image/jpeg');
  const mockVideoFile = createMockFile('test-video.mp4', 10 * 1024 * 1024, 'video/mp4');
  
  console.log('Mock image file:', mockImageFile);
  console.log('Mock video file:', mockVideoFile);
  
  // Check if we can access the upload handlers
  if (window.React && window.React.version) {
    console.log('✅ React is available, version:', window.React.version);
  }
  
  return { mockImageFile, mockVideoFile };
}

// Run the test
const testResults = testFileUpload();

console.log('🏁 Media upload test completed');
console.log('Test results:', testResults);

// Instructions for manual testing
console.log(`
📋 Manual Testing Instructions:
1. Navigate to Step 4 (Presentation & Media)
2. Try uploading an image file for cover image
3. Try uploading a video file for presentation video
4. Check browser console for upload logs
5. Verify preview appears after successful upload
6. Check if course data is updated with media information
`);
