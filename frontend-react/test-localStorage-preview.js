// Test script for localStorage media preview system
// Run this in browser console on the course creator page

console.log('🧪 Testing localStorage Media Preview System');

// Test 1: Check if mediaPreviewService is available
if (typeof window.mediaPreviewService !== 'undefined') {
  console.log('✅ mediaPreviewService is available globally');
} else {
  console.log('❌ mediaPreviewService not found globally');
  console.log('ℹ️ This is expected - service is imported in component');
}

// Test 2: Check localStorage functionality
try {
  localStorage.setItem('test-key', 'test-value');
  const testValue = localStorage.getItem('test-key');
  localStorage.removeItem('test-key');
  
  if (testValue === 'test-value') {
    console.log('✅ localStorage is working');
  } else {
    console.log('❌ localStorage test failed');
  }
} catch (error) {
  console.log('❌ localStorage is not available:', error);
}

// Test 3: Check for existing media previews
try {
  const existingPreviews = localStorage.getItem('brainmaker-media-previews');
  if (existingPreviews) {
    const previews = JSON.parse(existingPreviews);
    console.log(`📦 Found ${previews.length} existing media previews:`, previews);
  } else {
    console.log('📦 No existing media previews found');
  }
} catch (error) {
  console.log('❌ Error reading existing previews:', error);
}

// Test 4: Check course creator component
const courseCreatorElement = document.querySelector('[data-testid="course-creator"]') || 
                            document.querySelector('.course-creator') ||
                            document.querySelector('form');

if (courseCreatorElement) {
  console.log('✅ Course creator component found');
} else {
  console.log('❌ Course creator component not found');
}

// Test 5: Check file upload inputs
const coverImageInput = document.querySelector('#cover-image-upload-step4') || 
                       document.querySelector('#cover-image-upload');
const videoInput = document.querySelector('#video-upload-step4') || 
                  document.querySelector('#video-upload');

console.log('Cover image input:', coverImageInput ? '✅ Found' : '❌ Not found');
console.log('Video input:', videoInput ? '✅ Found' : '❌ Not found');

// Test 6: Create a mock file for testing
function createMockFile(name, size, type) {
  const content = 'mock file content for testing';
  const file = new File([content], name, { type, lastModified: Date.now() });
  Object.defineProperty(file, 'size', { value: size });
  return file;
}

// Test 7: Simulate file upload process
async function testFileUploadSimulation() {
  console.log('🧪 Testing file upload simulation...');
  
  try {
    // Create mock files
    const mockImageFile = createMockFile('test-image.jpg', 1024 * 1024, 'image/jpeg');
    const mockVideoFile = createMockFile('test-video.mp4', 10 * 1024 * 1024, 'video/mp4');
    
    console.log('Mock image file:', mockImageFile);
    console.log('Mock video file:', mockVideoFile);
    
    // Test FileReader functionality
    const reader = new FileReader();
    reader.onload = function(e) {
      console.log('✅ FileReader test successful');
      console.log('Base64 data length:', e.target.result.length);
    };
    reader.onerror = function(e) {
      console.log('❌ FileReader test failed:', e);
    };
    reader.readAsDataURL(mockImageFile);
    
    return { mockImageFile, mockVideoFile };
  } catch (error) {
    console.log('❌ File upload simulation failed:', error);
    return null;
  }
}

// Test 8: Check browser compatibility
function checkBrowserCompatibility() {
  console.log('🌐 Checking browser compatibility...');
  
  const features = {
    'localStorage': typeof Storage !== 'undefined',
    'FileReader': typeof FileReader !== 'undefined',
    'URL.createObjectURL': typeof URL !== 'undefined' && typeof URL.createObjectURL === 'function',
    'File API': typeof File !== 'undefined',
    'base64 encoding': typeof btoa !== 'undefined'
  };
  
  Object.entries(features).forEach(([feature, supported]) => {
    console.log(`${supported ? '✅' : '❌'} ${feature}: ${supported ? 'Supported' : 'Not supported'}`);
  });
  
  return features;
}

// Run all tests
console.log('🚀 Running localStorage preview tests...');

const compatibility = checkBrowserCompatibility();
const mockFiles = testFileUploadSimulation();

console.log('📋 Test Summary:');
console.log('- localStorage available:', compatibility.localStorage);
console.log('- FileReader available:', compatibility.FileReader);
console.log('- File API available:', compatibility['File API']);
console.log('- Base64 encoding available:', compatibility['base64 encoding']);

console.log('💡 Manual Testing Instructions:');
console.log('1. Navigate to Step 4 (Presentation & Media)');
console.log('2. Upload an image file');
console.log('3. Check console for localStorage logs');
console.log('4. Verify preview appears immediately');
console.log('5. Check localStorage in DevTools Application tab');
console.log('6. Look for "brainmaker-media-previews" key');

console.log('🏁 localStorage preview test completed');
