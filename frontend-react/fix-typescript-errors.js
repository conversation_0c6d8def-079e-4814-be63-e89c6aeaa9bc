#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Common TypeScript error fixes
const fixes = [
  // Fix specific event handler types
  {
    pattern: /onChange=\{\(e: React\.ChangeEvent<HTMLInputElement \| HTMLTextAreaElement>\) => ([^}]+)e\.target\.checked/g,
    replacement: 'onChange={(e: React.ChangeEvent<HTMLInputElement>) => $1e.target.checked'
  },
  {
    pattern: /onChange=\{\(e: React\.ChangeEvent<HTMLInputElement \| HTMLTextAreaElement>\) => ([^}]+)e\.target\.files/g,
    replacement: 'onChange={(e: React.ChangeEvent<HTMLInputElement>) => $1e.target.files'
  },
  // Fix select element onChange handlers
  {
    pattern: /onChange=\{\(e: React\.ChangeEvent<HTMLInputElement \| HTMLTextAreaElement>\) => ([^}]+setSortBy|setFilterBy|setSelectedCategory)/g,
    replacement: 'onChange={(e: React.ChangeEvent<HTMLSelectElement>) => $1'
  },
  // Fix mouse event style access
  {
    pattern: /onMouseEnter=\{\(e: React\.MouseEvent\) => ([^}]+)e\.currentTarget\.style/g,
    replacement: 'onMouseEnter={(e: React.MouseEvent<HTMLElement>) => $1(e.currentTarget as HTMLElement).style'
  },
  {
    pattern: /onMouseLeave=\{\(e: React\.MouseEvent\) => ([^}]+)e\.currentTarget\.style/g,
    replacement: 'onMouseLeave={(e: React.MouseEvent<HTMLElement>) => $1(e.currentTarget as HTMLElement).style'
  },
  // Fix malformed type annotations (param: Type: any) -> (param: any)
  {
    pattern: /\(([^:,)]+): [^:,)]+: any\)/g,
    replacement: '($1: any)'
  },
  {
    pattern: /\(([^:,)]+): [^:,)]+: any, ([^:,)]+): [^:,)]+: any\)/g,
    replacement: '($1: any, $2: any)'
  },
  // Fix specific complex patterns
  {
    pattern: /\(([^:,)]+): [^:,)]+: any, ([^:,)]+): [^:,)]+: any, ([^:,)]+): [^:,)]+: any\)/g,
    replacement: '($1: any, $2: any, $3: any)'
  },
  // Fix showTotal patterns specifically
  {
    pattern: /showTotal: \(([^:,)]+): any: number, ([^:,)]+): any: \[number, number\]\)/g,
    replacement: 'showTotal: ($1: number, $2: [number, number])'
  },
  // Fix renderItem patterns with index
  {
    pattern: /renderItem=\{\(([^:,)]+): any, ([^:,)]+): number: any\)/g,
    replacement: 'renderItem={($1: any, $2: number)'
  },
  // Fix remaining malformed type patterns
  {
    pattern: /\(([^:,)]+): ([^:,)]+): any\)/g,
    replacement: '($1: any)'
  },
  // Fix broken style attributes
  {
    pattern: /style=\{\{ ([^}]+): any\) => /g,
    replacement: 'style={{ $1: "100%" }} onChange={(value: any) => '
  },
  // Fix broken Form.Item patterns
  {
    pattern: /style=\{\{ marginBottom: any\) => /g,
    replacement: 'style={{ marginBottom: 16 }} onChange={(value: any) => '
  },
  // Fix onSelect parameters
  {
    pattern: /onSelect=\{\(\{ key \}\) => /g,
    replacement: 'onSelect={({ key }: { key: string }) => '
  },
  // Fix User model property mismatches based on actual model definitions
  {
    pattern: /\.FirstName/g,
    replacement: '.Firstname'
  },
  {
    pattern: /\.LastName/g,
    replacement: '.Lastname'
  },
  {
    pattern: /\.Name(?!\w)/g,  // Avoid matching 'Firstname' or 'Lastname'
    replacement: '.Username'
  },
  {
    pattern: /\.Avatar/g,
    replacement: '.Photo'
  },
  // Fix property access in object destructuring and assignments
  {
    pattern: /FirstName:/g,
    replacement: 'Firstname:'
  },
  {
    pattern: /LastName:/g,
    replacement: 'Lastname:'
  },
  {
    pattern: /Name:/g,
    replacement: 'Username:'
  },
  {
    pattern: /Avatar:/g,
    replacement: 'Photo:'
  }
];

function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    fixes.forEach(fix => {
      const newContent = content.replace(fix.pattern, fix.replacement);
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    });

    if (modified) {
      fs.writeFileSync(filePath, content);
      console.log(`Fixed: ${filePath}`);
    }
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
  }
}

function processDirectory(dir) {
  const entries = fs.readdirSync(dir, { withFileTypes: true });
  
  for (const entry of entries) {
    const fullPath = path.join(dir, entry.name);
    
    if (entry.isDirectory() && !entry.name.startsWith('.') && entry.name !== 'node_modules') {
      processDirectory(fullPath);
    } else if (entry.isFile() && (entry.name.endsWith('.tsx') || entry.name.endsWith('.ts'))) {
      fixFile(fullPath);
    }
  }
}

// Start processing from src directory
const srcDir = path.join(__dirname, 'src');
console.log('Starting TypeScript error fixes...');
processDirectory(srcDir);
console.log('TypeScript error fixes completed!');
