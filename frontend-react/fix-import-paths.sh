#!/bin/bash

# <PERSON>ript to fix all import paths to the antd compatibility layer

echo "🔧 Fixing import paths to antd compatibility layer..."

# Find all files that import from '../antd' or similar patterns
files=$(find src -name "*.tsx" -o -name "*.ts" | xargs grep -l "from.*antd")

count=0
total=$(echo "$files" | wc -l)

echo "📁 Found $total files to fix"

for file in $files; do
    count=$((count + 1))
    echo "[$count/$total] Fixing: $file"
    
    # Determine the correct relative path based on file location
    if [[ "$file" == src/components/antd/* ]]; then
        # Skip the antd compatibility layer itself
        continue
    elif [[ "$file" == src/components/* ]]; then
        # Components should import from './antd' or '../antd'
        sed -i '' "s|from ['\"].*antd['\"]|from './antd'|g" "$file"
    elif [[ "$file" == src/pages/* ]]; then
        # Pages should import from '../../components/antd'
        sed -i '' "s|from ['\"].*antd['\"]|from '../../components/antd'|g" "$file"
    elif [[ "$file" == src/hooks/* ]]; then
        # Hooks should import from '../components/antd'
        sed -i '' "s|from ['\"].*antd['\"]|from '../components/antd'|g" "$file"
    elif [[ "$file" == src/services/* ]]; then
        # Services should import from '../components/antd'
        sed -i '' "s|from ['\"].*antd['\"]|from '../components/antd'|g" "$file"
    elif [[ "$file" == src/utils/* ]]; then
        # Utils should import from '../components/antd'
        sed -i '' "s|from ['\"].*antd['\"]|from '../components/antd'|g" "$file"
    else
        # Default fallback
        sed -i '' "s|from ['\"].*antd['\"]|from '../components/antd'|g" "$file"
    fi
done

echo "✅ Import path fixing complete!"

# Check for any remaining issues
echo "🔍 Checking for remaining import issues..."
remaining=$(find src -name "*.tsx" -o -name "*.ts" | xargs grep -l "from.*antd" | grep -v "src/components/antd" | wc -l)
echo "📊 Files still importing antd: $remaining"

if [ "$remaining" -gt 0 ]; then
    echo "⚠️  Files that still need manual review:"
    find src -name "*.tsx" -o -name "*.ts" | xargs grep -l "from.*antd" | grep -v "src/components/antd"
fi
