#!/usr/bin/env node

/**
 * Comprehensive Course Structure API Testing Script
 * Tests all CRUD operations for course sections and content
 */

import axios from 'axios';

const BASE_URL = 'http://localhost:3200/api';
const TEST_COURSE_ID = 40; // Using existing course ID

// Test configuration
const config = {
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
};

// Test results tracking
let testResults = {
  passed: 0,
  failed: 0,
  errors: []
};

// Helper function to log test results
function logTest(testName, success, error = null) {
  if (success) {
    console.log(`✅ ${testName}`);
    testResults.passed++;
  } else {
    console.log(`❌ ${testName}: ${error?.message || 'Unknown error'}`);
    testResults.failed++;
    testResults.errors.push({ test: testName, error: error?.message || 'Unknown error' });
  }
}

// Helper function to make API requests with error handling
async function apiRequest(method, url, data = null) {
  try {
    const response = await axios({
      method,
      url: `${BASE_URL}${url}`,
      data,
      ...config
    });
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return { 
      success: false, 
      error: error.response?.data || error.message,
      status: error.response?.status || 0
    };
  }
}

// Test 1: Backend connectivity
async function testBackendConnectivity() {
  console.log('\n🔍 Testing Backend Connectivity...');
  
  const result = await apiRequest('GET', '/courses');
  logTest('Backend API connectivity', result.success, result.error);
  
  if (result.success) {
    console.log(`   Found ${result.data.length} courses in database`);
  }
  
  return result.success;
}

// Test 2: Course sections API
async function testCourseSectionsAPI() {
  console.log('\n🔍 Testing Course Sections API...');
  
  // Test GET all sections
  const getSections = await apiRequest('GET', '/courseSections');
  logTest('GET /courseSections', getSections.success, getSections.error);
  
  if (getSections.success) {
    console.log(`   Found ${getSections.data.length} sections in database`);
  }
  
  return getSections.success;
}

// Test 3: Course contents API
async function testCourseContentsAPI() {
  console.log('\n🔍 Testing Course Contents API...');
  
  // Test GET all contents
  const getContents = await apiRequest('GET', '/courseContents');
  logTest('GET /courseContents', getContents.success, getContents.error);
  
  if (getContents.success) {
    console.log(`   Found ${getContents.data.length} contents in database`);
  }
  
  return getContents.success;
}

// Test 4: Create course section
async function testCreateSection() {
  console.log('\n🔍 Testing Course Section Creation...');
  
  const sectionData = {
    Title: `Test Section ${Date.now()}`,
    Description: 'Test section created by automated test',
    Position: 1,
    Course: { Id: TEST_COURSE_ID },
    IsActive: true,
    IsPublished: false
  };
  
  const result = await apiRequest('POST', '/courseSections', sectionData);
  logTest('Create course section', result.success, result.error);
  
  if (result.success) {
    console.log(`   Created section with ID: ${result.data.Id}`);
    return result.data;
  }
  
  return null;
}

// Test 5: Update course section
async function testUpdateSection(section) {
  if (!section) return false;
  
  console.log('\n🔍 Testing Course Section Update...');
  
  const updateData = {
    Title: `Updated ${section.Title}`,
    Description: 'Updated description by automated test'
  };
  
  const result = await apiRequest('PUT', `/courseSections/${section.Slug}`, updateData);
  logTest('Update course section', result.success, result.error);
  
  if (result.success) {
    console.log(`   Updated section: ${result.data.Title}`);
    return result.data;
  }
  
  return null;
}

// Test 6: Create course content
async function testCreateContent(section) {
  if (!section) return false;
  
  console.log('\n🔍 Testing Course Content Creation...');
  
  const contentData = {
    Title: `Test Content ${Date.now()}`,
    Description: 'Test content created by automated test',
    Content: 'This is test content body',
    Type: 1, // Assuming 1 is a valid content type
    Position: 1,
    SectionId: section.Id,
    Section: { Id: section.Id },
    IsActive: true,
    IsPublished: false,
    IsFree: false,
    Duration: 300
  };
  
  const result = await apiRequest('POST', '/courseContents', contentData);
  logTest('Create course content', result.success, result.error);
  
  if (result.success) {
    console.log(`   Created content with ID: ${result.data.Id}`);
    return result.data;
  }
  
  return null;
}

// Test 7: Update course content
async function testUpdateContent(content) {
  if (!content) return false;
  
  console.log('\n🔍 Testing Course Content Update...');
  
  const updateData = {
    Title: `Updated ${content.Title}`,
    Description: 'Updated content description by automated test',
    Content: 'Updated content body'
  };
  
  const result = await apiRequest('PUT', `/courseContents/${content.Slug}`, updateData);
  logTest('Update course content', result.success, result.error);
  
  if (result.success) {
    console.log(`   Updated content: ${result.data.Title}`);
    return result.data;
  }
  
  return null;
}

// Test 8: Delete course content
async function testDeleteContent(content) {
  if (!content) return false;
  
  console.log('\n🔍 Testing Course Content Deletion...');
  
  const result = await apiRequest('DELETE', `/courseContents/${content.Slug}`);
  logTest('Delete course content', result.success, result.error);
  
  return result.success;
}

// Test 9: Delete course section
async function testDeleteSection(section) {
  if (!section) return false;
  
  console.log('\n🔍 Testing Course Section Deletion...');
  
  const result = await apiRequest('DELETE', `/courseSections/${section.Slug}`);
  logTest('Delete course section', result.success, result.error);
  
  return result.success;
}

// Main test runner
async function runTests() {
  console.log('🚀 Starting Course Structure API Tests...');
  console.log(`📍 Testing against: ${BASE_URL}`);
  console.log(`📚 Using test course ID: ${TEST_COURSE_ID}`);
  
  // Test backend connectivity first
  const backendWorking = await testBackendConnectivity();
  if (!backendWorking) {
    console.log('\n❌ Backend is not accessible. Please start the backend server first.');
    return;
  }
  
  // Test API endpoints
  await testCourseSectionsAPI();
  await testCourseContentsAPI();
  
  // Test CRUD operations
  const createdSection = await testCreateSection();
  const updatedSection = await testUpdateSection(createdSection);
  
  const createdContent = await testCreateContent(updatedSection || createdSection);
  const updatedContent = await testUpdateContent(createdContent);
  
  // Cleanup - delete test data
  await testDeleteContent(updatedContent || createdContent);
  await testDeleteSection(updatedSection || createdSection);
  
  // Print final results
  console.log('\n📊 Test Results Summary:');
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`📈 Success Rate: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);
  
  if (testResults.errors.length > 0) {
    console.log('\n🚨 Failed Tests:');
    testResults.errors.forEach(error => {
      console.log(`   • ${error.test}: ${error.error}`);
    });
  }
  
  if (testResults.failed === 0) {
    console.log('\n🎉 All tests passed! Course structure API is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the backend implementation.');
  }
}

// Run the tests
runTests().catch(error => {
  console.error('💥 Test runner failed:', error);
  process.exit(1);
});
