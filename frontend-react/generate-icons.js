#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create the missing icon files by copying the existing logo
// This is a temporary solution until proper icons can be generated

const sourceIcon = path.join(__dirname, 'public/assets/img/brain-maker-logo.png');
const targetDir = path.join(__dirname, 'public/assets/images');

// Ensure target directory exists
if (!fs.existsSync(targetDir)) {
  fs.mkdirSync(targetDir, { recursive: true });
}

// Icon sizes needed for PWA
const iconSizes = [
  '72x72',
  '96x96', 
  '128x128',
  '144x144',
  '152x152',
  '192x192',
  '384x384',
  '512x512'
];

// Check if source exists
if (!fs.existsSync(sourceIcon)) {
  console.error('Source icon not found:', sourceIcon);
  process.exit(1);
}

// Copy the logo to each required size filename
// Note: This creates files with the same content but different names
// In production, you'd want to actually resize the images
iconSizes.forEach(size => {
  const targetFile = path.join(targetDir, `icon-${size}.png`);
  try {
    fs.copyFileSync(sourceIcon, targetFile);
    console.log(`Created: ${targetFile}`);
  } catch (error) {
    console.error(`Failed to create ${targetFile}:`, error.message);
  }
});

console.log('Icon generation complete!');
console.log('Note: All icons are copies of the original logo.');
console.log('For production, consider using proper image resizing tools.');
