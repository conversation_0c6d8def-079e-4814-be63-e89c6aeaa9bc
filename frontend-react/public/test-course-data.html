<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Course Data Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        .media-test {
            display: flex;
            gap: 20px;
            margin-top: 15px;
        }
        .media-item {
            flex: 1;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        img, video {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .data-display {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            font-weight: bold;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Frontend Course Data Test</h1>
        <p>This test verifies that the frontend can properly fetch and display course data with media from the backend.</p>
        
        <button onclick="runTest()">🚀 Run Test</button>
        <button onclick="testNextCourse()">➡️ Test Next Course</button>
        <button onclick="testAllCourses()">🔄 Test All Courses</button>
        <button onclick="clearResults()">🧹 Clear Results</button>

        <div style="margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 4px;">
            <strong>Current Course:</strong> <span id="currentCourseInfo">Course 1 of 3</span>
        </div>

        <div style="margin-top: 10px; padding: 10px; background: #e8f5e8; border-radius: 4px; font-size: 12px;">
            <strong>✅ Enhanced CORS Fix Applied:</strong> Backend now serves static files with comprehensive CORS headers to prevent OpaqueResponseBlocking errors.
            <br><small>Includes: Access-Control-Allow-Origin, Cross-Origin-Resource-Policy, Cross-Origin-Embedder-Policy, and Cache-Control headers.</small>
        </div>

        <div style="margin-top: 10px; padding: 10px; background: #fff3cd; border-radius: 4px; font-size: 12px; border-left: 4px solid #ffc107;">
            <strong>⚠️ If you still see OpaqueResponseBlocking errors:</strong>
            <br>1. Clear browser cache: <kbd>Ctrl+Shift+R</kbd> (Windows/Linux) or <kbd>Cmd+Shift+R</kbd> (Mac)
            <br>2. Open Developer Tools (F12) → Network tab → Check "Disable cache"
            <br>3. Refresh the page to reload media files with new headers
            <br><small>Browser caching can cause old failed requests to persist even after server fixes.</small>
        </div>
    </div>

    <div id="results"></div>

    <script>
        const API_BASE = 'http://localhost:3200/api';
        // Real course slugs from database (will be populated dynamically)
        const REAL_COURSE_SLUGS = [
            'd364109feb9c4332ba97561a5413ecc6', // Database Storage Test Course (has cover + video)
            '1ef06a97e20748d7ace6aa70d631379a', // Debug Course (has cover)
            'f93905031e3e4844a3a21e2e445bc78c'  // Simple Test (no media)
        ];
        let currentCourseIndex = 0;
        
        let testResults = {
            backendFetch: false,
            dataIntegrity: false,
            mediaUrls: false,
            mediaAccessibility: false
        };

        function updateCourseInfo() {
            const courseInfo = document.getElementById('currentCourseInfo');
            const currentSlug = REAL_COURSE_SLUGS[currentCourseIndex];
            courseInfo.textContent = `Course ${currentCourseIndex + 1} of ${REAL_COURSE_SLUGS.length} (${currentSlug})`;
        }

        async function runTest() {
            const currentSlug = REAL_COURSE_SLUGS[currentCourseIndex];
            updateCourseInfo();

            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = `<div class="container info"><h2>🔄 Testing Course: ${currentSlug}</h2></div>`;

            try {
                // Test 1: Backend API Fetch
                await testBackendFetch(currentSlug);

                // Test 2: Data Integrity
                await testDataIntegrity();

                // Test 3: Media URL Construction
                await testMediaUrls();

                // Test 4: Media Accessibility
                await testMediaAccessibility();

                // Display final results
                displayFinalResults();

            } catch (error) {
                console.error('Test failed:', error);
                resultsDiv.innerHTML += `
                    <div class="container error">
                        <h3>❌ Test Failed</h3>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
            }
        }

        function testNextCourse() {
            currentCourseIndex = (currentCourseIndex + 1) % REAL_COURSE_SLUGS.length;
            runTest();
        }

        async function testAllCourses() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="container info"><h2>🔄 Testing All Courses...</h2></div>';

            for (let i = 0; i < REAL_COURSE_SLUGS.length; i++) {
                currentCourseIndex = i;
                const currentSlug = REAL_COURSE_SLUGS[currentCourseIndex];

                resultsDiv.innerHTML += `<div class="container info"><h3>Testing Course ${i + 1}: ${currentSlug}</h3></div>`;

                try {
                    await testBackendFetch(currentSlug);
                    await testDataIntegrity();
                    await testMediaUrls();
                    await testMediaAccessibility();

                    resultsDiv.innerHTML += `<div class="container success"><h4>✅ Course ${i + 1} Test Complete</h4></div>`;
                } catch (error) {
                    resultsDiv.innerHTML += `<div class="container error"><h4>❌ Course ${i + 1} Test Failed: ${error.message}</h4></div>`;
                }
            }

            resultsDiv.innerHTML += '<div class="container success"><h2>🎉 All Course Tests Complete!</h2></div>';
        }

        async function testBackendFetch(courseSlug) {
            const resultsDiv = document.getElementById('results');

            try {
                const response = await fetch(`${API_BASE}/courses/public/${courseSlug}`);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const courseData = await response.json();
                window.testCourseData = courseData; // Store for other tests

                testResults.backendFetch = true;

                resultsDiv.innerHTML += `
                    <div class="container success">
                        <h3>✅ Test 1: Backend API Fetch - PASSED</h3>
                        <div class="status">Successfully fetched real course data from database</div>
                        <p><strong>Course:</strong> ${courseData.Title} (ID: ${courseData.Id})</p>
                        <p><strong>Slug:</strong> ${courseData.Slug}</p>
                        <p><strong>Has Cover Image:</strong> ${courseData.CoverImage ? 'Yes' : 'No'}</p>
                        <p><strong>Has Video:</strong> ${courseData.PresentationVideo ? 'Yes' : 'No'}</p>
                        <p><strong>Published:</strong> ${courseData.Published ? 'Yes' : 'No'}</p>
                        <p><strong>Price:</strong> $${courseData.Price || 'Free'}</p>
                        <details>
                            <summary>Raw Backend Data</summary>
                            <div class="data-display">${JSON.stringify(courseData, null, 2)}</div>
                        </details>
                    </div>
                `;

            } catch (error) {
                testResults.backendFetch = false;
                resultsDiv.innerHTML += `
                    <div class="container error">
                        <h3>❌ Test 1: Backend API Fetch - FAILED</h3>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
                throw error;
            }
        }

        async function testDataIntegrity() {
            const resultsDiv = document.getElementById('results');
            const courseData = window.testCourseData;
            
            if (!courseData) {
                throw new Error('No course data available for integrity test');
            }
            
            const checks = {
                hasId: !!courseData.Id,
                hasTitle: !!courseData.Title,
                hasSlug: !!courseData.Slug,
                hasCoverImage: !!courseData.CoverImage,
                hasVideo: !!courseData.PresentationVideo,
                coverImageHasHashname: courseData.CoverImage?.Hashname ? true : false,
                videoHasHashname: courseData.PresentationVideo?.Hashname ? true : false
            };
            
            const allChecksPass = Object.values(checks).every(check => check);
            testResults.dataIntegrity = allChecksPass;
            
            resultsDiv.innerHTML += `
                <div class="container ${allChecksPass ? 'success' : 'error'}">
                    <h3>${allChecksPass ? '✅' : '❌'} Test 2: Data Integrity - ${allChecksPass ? 'PASSED' : 'FAILED'}</h3>
                    <div class="status">Checking course data structure and required fields</div>
                    <ul>
                        <li>${checks.hasId ? '✅' : '❌'} Has Course ID</li>
                        <li>${checks.hasTitle ? '✅' : '❌'} Has Title</li>
                        <li>${checks.hasSlug ? '✅' : '❌'} Has Slug</li>
                        <li>${checks.hasCoverImage ? '✅' : '❌'} Has Cover Image Object</li>
                        <li>${checks.hasVideo ? '✅' : '❌'} Has Video Object</li>
                        <li>${checks.coverImageHasHashname ? '✅' : '❌'} Cover Image Has Hashname</li>
                        <li>${checks.videoHasHashname ? '✅' : '❌'} Video Has Hashname</li>
                    </ul>
                </div>
            `;
        }

        async function testMediaUrls() {
            const resultsDiv = document.getElementById('results');
            const courseData = window.testCourseData;
            
            if (!courseData) {
                throw new Error('No course data available for URL test');
            }
            
            // Simulate frontend URL construction
            function constructMediaUrl(hashname) {
                if (!hashname) return '';
                if (hashname.startsWith('http://') || hashname.startsWith('https://')) {
                    return hashname;
                }
                const cleanHashname = hashname.startsWith('/') ? hashname.substring(1) : hashname;
                return `http://localhost:3200/${cleanHashname}`;
            }
            
            const coverImageUrl = courseData.CoverImage?.Hashname ? 
                constructMediaUrl(courseData.CoverImage.Hashname) : null;
            const videoUrl = courseData.PresentationVideo?.Hashname ? 
                constructMediaUrl(courseData.PresentationVideo.Hashname) : null;
            
            const urlsValid = (coverImageUrl ? coverImageUrl.startsWith('http') : true) && 
                             (videoUrl ? videoUrl.startsWith('http') : true);
            
            testResults.mediaUrls = urlsValid;
            window.testMediaUrls = { coverImageUrl, videoUrl }; // Store for accessibility test
            
            resultsDiv.innerHTML += `
                <div class="container ${urlsValid ? 'success' : 'error'}">
                    <h3>${urlsValid ? '✅' : '❌'} Test 3: Media URL Construction - ${urlsValid ? 'PASSED' : 'FAILED'}</h3>
                    <div class="status">Testing URL construction logic</div>
                    ${coverImageUrl ? `<p><strong>Cover Image URL:</strong> <code>${coverImageUrl}</code></p>` : ''}
                    ${videoUrl ? `<p><strong>Video URL:</strong> <code>${videoUrl}</code></p>` : ''}
                </div>
            `;
        }

        async function testMediaAccessibility() {
            const resultsDiv = document.getElementById('results');
            const urls = window.testMediaUrls;
            
            if (!urls) {
                throw new Error('No media URLs available for accessibility test');
            }
            
            let coverImageAccessible = false;
            let videoAccessible = false;
            
            // Test cover image accessibility
            if (urls.coverImageUrl) {
                try {
                    const response = await fetch(urls.coverImageUrl, { method: 'HEAD' });
                    coverImageAccessible = response.ok;
                } catch (error) {
                    console.warn('Cover image accessibility test failed:', error);
                }
            }
            
            // Test video accessibility
            if (urls.videoUrl) {
                try {
                    const response = await fetch(urls.videoUrl, { method: 'HEAD' });
                    videoAccessible = response.ok;
                } catch (error) {
                    console.warn('Video accessibility test failed:', error);
                }
            }
            
            const allMediaAccessible = (!urls.coverImageUrl || coverImageAccessible) && 
                                     (!urls.videoUrl || videoAccessible);
            
            testResults.mediaAccessibility = allMediaAccessible;
            
            resultsDiv.innerHTML += `
                <div class="container ${allMediaAccessible ? 'success' : 'error'}">
                    <h3>${allMediaAccessible ? '✅' : '❌'} Test 4: Media Accessibility - ${allMediaAccessible ? 'PASSED' : 'FAILED'}</h3>
                    <div class="status">Testing if media files are accessible via HTTP</div>
                    ${urls.coverImageUrl ? `<p>${coverImageAccessible ? '✅' : '❌'} Cover Image Accessible</p>` : ''}
                    ${urls.videoUrl ? `<p>${videoAccessible ? '✅' : '❌'} Video Accessible</p>` : ''}
                    
                    <div class="media-test">
                        ${urls.coverImageUrl ? `
                            <div class="media-item">
                                <h4>Cover Image Test</h4>
                                <img src="${urls.coverImageUrl}" alt="Course Cover" 
                                     onload="this.style.border='2px solid green'" 
                                     onerror="this.style.border='2px solid red'; this.alt='Failed to load'">
                            </div>
                        ` : ''}
                        ${urls.videoUrl ? `
                            <div class="media-item">
                                <h4>Video Test</h4>
                                <video controls style="max-height: 200px;">
                                    <source src="${urls.videoUrl}" type="video/mp4">
                                    Your browser does not support the video tag.
                                </video>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
        }

        function displayFinalResults() {
            const resultsDiv = document.getElementById('results');
            const allTestsPassed = Object.values(testResults).every(result => result);
            
            resultsDiv.innerHTML += `
                <div class="container ${allTestsPassed ? 'success' : 'error'}">
                    <h2>${allTestsPassed ? '🎉' : '⚠️'} Final Results</h2>
                    <div class="status">Overall Test Status: ${allTestsPassed ? 'ALL TESTS PASSED' : 'SOME TESTS FAILED'}</div>
                    <ul>
                        <li>${testResults.backendFetch ? '✅' : '❌'} Backend API Fetch</li>
                        <li>${testResults.dataIntegrity ? '✅' : '❌'} Data Integrity</li>
                        <li>${testResults.mediaUrls ? '✅' : '❌'} Media URL Construction</li>
                        <li>${testResults.mediaAccessibility ? '✅' : '❌'} Media Accessibility</li>
                    </ul>
                    
                    ${allTestsPassed ? `
                        <div style="margin-top: 20px; padding: 15px; background: #e8f5e8; border-radius: 5px;">
                            <h3>✅ Frontend Course Retrieval Integration: SUCCESSFUL</h3>
                            <p>The frontend can successfully:</p>
                            <ul>
                                <li>Fetch course data from the backend API</li>
                                <li>Process and validate the data structure</li>
                                <li>Construct proper media URLs</li>
                                <li>Display cover images and presentation videos</li>
                            </ul>
                            <p><strong>Next Step:</strong> Test the actual React course components in the browser.</p>
                        </div>
                    ` : `
                        <div style="margin-top: 20px; padding: 15px; background: #f8e8e8; border-radius: 5px;">
                            <h3>❌ Some Tests Failed</h3>
                            <p>Please check the individual test results above for details.</p>
                        </div>
                    `}
                </div>
            `;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            testResults = {
                backendFetch: false,
                dataIntegrity: false,
                mediaUrls: false,
                mediaAccessibility: false
            };
            currentCourseIndex = 0;
            updateCourseInfo();
        }

        // Initialize on page load
        window.onload = function() {
            updateCourseInfo();
        };
    </script>
</body>
</html>
