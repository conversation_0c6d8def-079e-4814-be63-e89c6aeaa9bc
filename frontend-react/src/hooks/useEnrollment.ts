import { useState, useEffect, useCallback } from 'react';
import enrollmentService, { type EnrollmentStatus } from '../services/enrollment.service';

/**
 * Hook for checking course access
 */
export function useCourseAccess(courseId: number | null, userId?: number) {
  const [enrollmentStatus, setEnrollmentStatus] = useState<EnrollmentStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const checkAccess = useCallback(async () => {
    if (!courseId) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const status = await enrollmentService.checkCourseAccess(courseId, userId);
      setEnrollmentStatus(status);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to check course access');
      setEnrollmentStatus({ hasAccess: false });
    } finally {
      setLoading(false);
    }
  }, [courseId, userId]);

  useEffect(() => {
    checkAccess();
  }, [checkAccess]);

  return {
    enrollmentStatus,
    loading,
    error,
    refetch: checkAccess
  };
}

/**
 * Hook for enrolling in free courses
 */
export function useFreeEnrollment() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const enrollInFreeCourse = useCallback(async (courseId: number) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await enrollmentService.enrollInFreeCourse(courseId);
      return result;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to enroll in course');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    enrollInFreeCourse,
    loading,
    error
  };
}

/**
 * Hook for getting user enrollments
 */
export function useUserEnrollments(userId?: number) {
  const [enrollments, setEnrollments] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchEnrollments = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const data = await enrollmentService.getUserEnrollments(userId);
      setEnrollments(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch enrollments');
      setEnrollments([]);
    } finally {
      setLoading(false);
    }
  }, [userId]);

  useEffect(() => {
    fetchEnrollments();
  }, [fetchEnrollments]);

  return {
    enrollments,
    loading,
    error,
    refetch: fetchEnrollments
  };
}

/**
 * Hook for managing enrollment progress
 */
export function useEnrollmentProgress(courseId: number | null) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const updateProgress = useCallback(async (progress: {
    completionPercentage?: number;
    lastAccessedDate?: string;
    currentLessonId?: number;
  }) => {
    if (!courseId) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const result = await enrollmentService.updateProgress(courseId, progress);
      return result;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update progress');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [courseId]);

  const completeCourse = useCallback(async () => {
    if (!courseId) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const result = await enrollmentService.completeCourse(courseId);
      return result;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to complete course');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [courseId]);

  return {
    updateProgress,
    completeCourse,
    loading,
    error
  };
}

/**
 * Hook for checking subscription access
 */
export function useSubscriptionAccess(courseId?: number) {
  const [subscriptionStatus, setSubscriptionStatus] = useState<{
    hasActiveSubscription: boolean;
    subscriptionType?: string;
    expiryDate?: string;
    coversAllCourses?: boolean;
  } | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const checkSubscriptionAccess = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const status = await enrollmentService.checkSubscriptionAccess(courseId);
      setSubscriptionStatus(status);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to check subscription access');
      setSubscriptionStatus({ hasActiveSubscription: false });
    } finally {
      setLoading(false);
    }
  }, [courseId]);

  useEffect(() => {
    checkSubscriptionAccess();
  }, [checkSubscriptionAccess]);

  return {
    subscriptionStatus,
    loading,
    error,
    refetch: checkSubscriptionAccess
  };
}

/**
 * Hook for getting course certificate
 */
export function useCourseCertificate(courseId: number | null) {
  const [certificateUrl, setCertificateUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchCertificate = useCallback(async () => {
    if (!courseId) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const url = await enrollmentService.getCertificate(courseId);
      setCertificateUrl(url);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch certificate');
      setCertificateUrl(null);
    } finally {
      setLoading(false);
    }
  }, [courseId]);

  useEffect(() => {
    fetchCertificate();
  }, [fetchCertificate]);

  return {
    certificateUrl,
    loading,
    error,
    refetch: fetchCertificate
  };
}

/**
 * Hook for unenrolling from courses
 */
export function useUnenrollment() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const unenrollFromCourse = useCallback(async (courseId: number) => {
    setLoading(true);
    setError(null);
    
    try {
      const success = await enrollmentService.unenrollFromCourse(courseId);
      return success;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to unenroll from course');
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    unenrollFromCourse,
    loading,
    error
  };
}
