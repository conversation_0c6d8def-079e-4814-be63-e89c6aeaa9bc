import { useState, useCallback, useRef, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { aiService } from '../services/ai.service';
import type { AIMessage, AIContext, AIRequest } from '../services/ai.service';
import { notify } from '../utils/errorHandling';
import { useAuth } from '../contexts/AuthContext';

interface UseAIChatOptions {
  context?: AIContext;
  autoSave?: boolean;
  maxMessages?: number;
}

interface UseAIChatReturn {
  messages: AIMessage[];
  isLoading: boolean;
  error: Error | null;
  sendMessage: (message: string, options?: Partial<AIRequest['options']>) => Promise<void>;
  clearMessages: () => void;
  regenerateLastResponse: () => Promise<void>;
  submitFeedback: (messageId: string, feedback: 'positive' | 'negative', comment?: string) => Promise<void>;
  suggestions: string[];
  isTyping: boolean;
  conversationId: string | null;
}

export const useAIChat = (options: UseAIChatOptions = {}): UseAIChatReturn => {
  const { context, autoSave = true, maxMessages = 50 } = options;
  const { authState } = useAuth();
  const queryClient = useQueryClient();
  
  const [messages, setMessages] = useState<AIMessage[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [conversationId, setConversationId] = useState<string | null>(null);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const lastUserMessageRef = useRef<string>('');

  // Load conversation history
  const { data: conversations, isLoading: isLoadingHistory } = useQuery({
    queryKey: ['ai-conversations', authState.user?.Id],
    queryFn: () => aiService.getConversations(authState.user?.Id?.toString() || ''),
    enabled: !!authState.user?.Id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Get AI suggestions based on context (disabled until backend AI endpoints are implemented)
  const { data: contextSuggestions } = useQuery({
    queryKey: ['ai-suggestions', context],
    queryFn: () => aiService.getSuggestions(context || {}),
    enabled: false, // Disabled until AI endpoints are implemented
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  // Update suggestions when context changes
  useEffect(() => {
    if (contextSuggestions) {
      setSuggestions(contextSuggestions);
    } else {
      // Provide fallback suggestions when AI is not available
      const fallbackSuggestions = [
        'Explain this concept in simple terms',
        'Give me practice questions',
        'Suggest study strategies',
        'Help me understand this better',
      ];
      setSuggestions(fallbackSuggestions);
    }
  }, [contextSuggestions]);

  // Send message mutation
  const sendMessageMutation = useMutation({
    mutationFn: async (request: AIRequest) => {
      setIsTyping(true);
      
      // Add user message immediately
      const userMessage: AIMessage = {
        id: Date.now().toString(),
        type: 'user',
        content: request.message,
        timestamp: new Date().toISOString(),
        context: request.context,
      };
      
      setMessages(prev => [...prev, userMessage]);
      lastUserMessageRef.current = request.message;

      try {
        // Call AI service
        const response = await aiService.sendMessage(request);
        
        // Convert response to AIMessage
        const aiMessage: AIMessage = {
          id: response.id,
          type: 'assistant',
          content: response.content,
          timestamp: response.timestamp,
          context: request.context,
          metadata: response.metadata,
        };

        setMessages(prev => [...prev, aiMessage]);
        
        // Update suggestions if provided
        if (response.suggestions) {
          setSuggestions(response.suggestions);
        }

        return response;
      } catch (error) {
        // Fallback to mock response if API fails
        console.warn('AI API failed, using mock response:', error);
        
        const mockResponse = aiService.generateMockResponse(request.message, request.context);
        const aiMessage: AIMessage = {
          id: mockResponse.id,
          type: 'assistant',
          content: mockResponse.content,
          timestamp: mockResponse.timestamp,
          context: request.context,
          metadata: mockResponse.metadata,
        };

        setMessages(prev => [...prev, aiMessage]);
        
        if (mockResponse.suggestions) {
          setSuggestions(mockResponse.suggestions);
        }

        return mockResponse;
      } finally {
        setIsTyping(false);
      }
    },
    onError: (error) => {
      setIsTyping(false);
      notify.error('Failed to send message', {
        description: 'Please try again later.',
      });
      console.error('AI chat error:', error);
    },
  });

  // Save conversation mutation
  const saveConversationMutation = useMutation({
    mutationFn: async (messages: AIMessage[]) => {
      if (!authState.user?.Id || messages.length === 0) return;

      const conversationTitle = messages[0]?.content.slice(0, 50) + '...' || 'New Conversation';
      
      return aiService.saveConversation({
        userId: authState.user.Id.toString(),
        title: conversationTitle,
        messages,
      });
    },
    onSuccess: (conversationId) => {
      if (conversationId) {
        setConversationId(conversationId);
        queryClient.invalidateQueries({ queryKey: ['ai-conversations'] });
      }
    },
  });

  // Feedback mutation
  const feedbackMutation = useMutation({
    mutationFn: async ({ messageId, feedback, comment }: {
      messageId: string;
      feedback: 'positive' | 'negative';
      comment?: string;
    }) => {
      return aiService.submitFeedback(messageId, {
        rating: feedback,
        comment,
        category: 'general',
      });
    },
    onSuccess: () => {
      notify.success('Feedback submitted', {
        description: 'Thank you for helping us improve!',
      });
    },
    onError: () => {
      notify.error('Failed to submit feedback', {
        description: 'Your feedback is important to us. Please try again.',
      });
    },
  });

  // Auto-save conversation when messages change
  useEffect(() => {
    if (autoSave && messages.length > 0 && messages.length % 4 === 0) {
      saveConversationMutation.mutate(messages);
    }
  }, [messages, autoSave]);

  // Limit message history
  useEffect(() => {
    if (messages.length > maxMessages) {
      setMessages(prev => prev.slice(-maxMessages));
    }
  }, [messages, maxMessages]);

  const sendMessage = useCallback(async (
    message: string,
    options?: Partial<AIRequest['options']>
  ) => {
    if (!message.trim() || sendMessageMutation.isPending) return;

    const request: AIRequest = {
      message: message.trim(),
      context: {
        ...context,
        conversationHistory: messages.slice(-10), // Include last 10 messages for context
      },
      options: {
        temperature: 0.7,
        maxTokens: 1000,
        includeHistory: true,
        ...options,
      },
    };

    await sendMessageMutation.mutateAsync(request);
  }, [context, messages, sendMessageMutation]);

  const clearMessages = useCallback(() => {
    setMessages([]);
    setConversationId(null);
    setSuggestions(contextSuggestions || []);
  }, [contextSuggestions]);

  const regenerateLastResponse = useCallback(async () => {
    if (!lastUserMessageRef.current || sendMessageMutation.isPending) return;

    // Remove the last AI response
    setMessages(prev => {
      const lastMessage = prev[prev.length - 1];
      if (lastMessage?.type === 'assistant') {
        return prev.slice(0, -1);
      }
      return prev;
    });

    // Resend the last user message
    await sendMessage(lastUserMessageRef.current);
  }, [sendMessage, sendMessageMutation.isPending]);

  const submitFeedback = useCallback(async (
    messageId: string,
    feedback: 'positive' | 'negative',
    comment?: string
  ) => {
    await feedbackMutation.mutateAsync({ messageId, feedback, comment });
    
    // Update local message with feedback
    setMessages(prev => 
      prev.map(msg => 
        msg.id === messageId 
          ? { ...msg, metadata: { ...msg.metadata, feedback } }
          : msg
      )
    );
  }, [feedbackMutation]);

  return {
    messages,
    isLoading: sendMessageMutation.isPending || isLoadingHistory,
    error: sendMessageMutation.error,
    sendMessage,
    clearMessages,
    regenerateLastResponse,
    submitFeedback,
    suggestions,
    isTyping,
    conversationId,
  };
};

// Hook for AI analytics
export const useAIAnalytics = () => {
  const { authState } = useAuth();

  return useQuery({
    queryKey: ['ai-analytics', authState.user?.Id],
    queryFn: () => aiService.getAnalytics(authState.user?.Id?.toString() || ''),
    enabled: !!authState.user?.Id,
    staleTime: 30 * 60 * 1000, // 30 minutes
  });
};

// Hook for AI study plan generation
export const useAIStudyPlan = () => {
  return useMutation({
    mutationFn: aiService.generateStudyPlan,
    onSuccess: () => {
      notify.success('Study plan generated', {
        description: 'Your personalized study plan is ready!',
      });
    },
    onError: () => {
      notify.error('Failed to generate study plan', {
        description: 'Please try again later.',
      });
    },
  });
};
