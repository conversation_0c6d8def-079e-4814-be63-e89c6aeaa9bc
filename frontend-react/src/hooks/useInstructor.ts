import { useApi, useApiMutation } from './useApi';
import instructorService from '../services/instructor.service';
import type { Instructor, InstructorApplication } from '../models';

/**
 * Hook to get all instructors
 */
export function useInstructors() {
  return useApi(() => instructorService.getAll());
}

/**
 * Hook to get instructor dashboard data
 * @param instructorSlug - The instructor slug
 */
export function useInstructorDashboard(instructorSlug: string | null) {
  return useApi(
    () => instructorSlug ? instructorService.getDashboardData(instructorSlug) : Promise.resolve(null),
    {
      immediate: !!instructorSlug,
      dependencies: [instructorSlug]
    }
  );
}

/**
 * Hook to get instructor students
 * @param instructorSlug - The instructor slug
 * @param take - Number of students to take
 * @param skip - Number of students to skip
 */
export function useInstructorStudents(instructorSlug: string | null, take = 10, skip = 0) {
  return useApi(
    () => instructorSlug ? instructorService.getStudents(instructorSlug, take, skip) : Promise.resolve({ students: [], total: 0 }),
    {
      immediate: !!instructorSlug,
      dependencies: [instructorSlug, take, skip]
    }
  );
}

/**
 * Hook to get instructor earnings
 * @param instructorSlug - The instructor slug
 * @param take - Number of earnings to take
 * @param skip - Number of earnings to skip
 */
export function useInstructorEarnings(instructorSlug: string | null, take = 10, skip = 0) {
  return useApi(
    () => instructorSlug ? instructorService.getEarnings(instructorSlug, take, skip) : Promise.resolve({
      earnings: [],
      total: 0,
      totalAmount: 0,
      totalEarnings: 0,
      currentMonthEarnings: 0,
      monthlyAverage: 0,
      totalBillings: 0,
      paidBillings: [],
      recentEarnings: []
    }),
    {
      immediate: !!instructorSlug,
      dependencies: [instructorSlug, take, skip]
    }
  );
}

/**
 * Hook to get instructor messages
 * @param instructorSlug - The instructor slug
 * @param take - Number of messages to take
 * @param skip - Number of messages to skip
 */
export function useInstructorMessages(instructorSlug: string | null, take = 10, skip = 0) {
  return useApi(
    () => instructorSlug ? instructorService.getMessages(instructorSlug, take, skip) : Promise.resolve({ messages: [], total: 0, unreadCount: 0 }),
    {
      immediate: !!instructorSlug,
      dependencies: [instructorSlug, take, skip]
    }
  );
}

/**
 * Hook to get instructor by user slug
 * @param slug - The user slug
 */
export function useInstructorBySlug(slug: string | null) {
  return useApi(
    () => slug ? instructorService.getByUserSlug(slug) : Promise.resolve(null),
    {
      immediate: !!slug,
      dependencies: [slug]
    }
  );
}

/**
 * Hook to get instructor statistics
 * @param instructorId - The instructor ID
 */
export function useInstructorStatistics(instructorId: number | null) {
  return useApi(
    () => instructorId ? instructorService.getStatistics(instructorId) : Promise.resolve(null),
    {
      immediate: !!instructorId,
      dependencies: [instructorId]
    }
  );
}

/**
 * Hook to apply to become an instructor
 */
export function useInstructorApplication() {
  return useApiMutation((application: any) => instructorService.apply(application));
}

/**
 * Hook to get instructor applications (admin only)
 */
export function useInstructorApplications() {
  return useApi(() => instructorService.getApplications());
}

/**
 * Hook to get instructor applications by status (admin only)
 * @param status - Application status
 */
export function useInstructorApplicationsByStatus(status: 'pending' | 'approved' | 'rejected') {
  return useApi(
    () => instructorService.getApplicationsByStatus(status),
    {
      dependencies: [status]
    }
  );
}

/**
 * Hook to approve instructor application (admin only)
 */
export function useApproveInstructorApplication() {
  return useApiMutation((data: { slug: string; reviewedBySlug: string; adminNotes?: string }) => 
    instructorService.approveApplication(data.slug, data.reviewedBySlug, data.adminNotes)
  );
}

/**
 * Hook to reject instructor application (admin only)
 */
export function useRejectInstructorApplication() {
  return useApiMutation((data: { slug: string; reviewedBySlug: string; adminNotes?: string }) => 
    instructorService.rejectApplication(data.slug, data.reviewedBySlug, data.adminNotes)
  );
}

/**
 * Hook to delete instructor application (admin only)
 */
export function useDeleteInstructorApplication() {
  return useApiMutation((slug: string) => instructorService.deleteApplication(slug));
}

/**
 * Hook to update instructor profile
 */
export function useUpdateInstructorProfile() {
  return useApiMutation((instructor: Instructor) => instructorService.updateProfile(instructor));
}

/**
 * Hook to search instructors
 * @param query - Search query
 */
export function useSearchInstructors(query: string) {
  return useApi(
    () => query ? instructorService.search(query) : Promise.resolve([]),
    {
      immediate: !!query,
      dependencies: [query]
    }
  );
}
