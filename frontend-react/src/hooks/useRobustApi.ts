import { useState, useEffect, useCallback, useRef } from 'react';
import {
  withRetry,
  handleError,
  withFallback,
  CircuitBreaker
} from '../utils/errorHandling';
import type {
  AppError,
  RetryConfig,
  ErrorHandlerConfig
} from '../utils/errorHandling';

// Enhanced API state
export interface RobustApiState<T> {
  data: T | null;
  loading: boolean;
  error: AppError | null;
  isRetrying: boolean;
  retryCount: number;
  lastFetch: number | null;
  isStale: boolean;
}

// Configuration for robust API hook
export interface RobustApiConfig {
  retry?: Partial<RetryConfig>;
  errorHandler?: ErrorHandlerConfig;
  staleTime?: number; // Time in ms before data is considered stale
  cacheTime?: number; // Time in ms to keep data in cache
  refetchOnWindowFocus?: boolean;
  refetchOnReconnect?: boolean;
  enabled?: boolean;
  fallbackData?: any;
  circuitBreaker?: boolean;
}

// Circuit breaker instances cache
const circuitBreakers = new Map<string, CircuitBreaker>();

// Data cache
const dataCache = new Map<string, {
  data: any;
  timestamp: number;
  staleTime: number;
  cacheTime: number;
}>();

// Generate cache key from function and dependencies
const generateCacheKey = (fn: Function, deps: any[]): string => {
  return `${fn.name || 'anonymous'}_${JSON.stringify(deps)}`;
};

// Check if cached data is fresh
const isCacheFresh = (cacheKey: string): boolean => {
  const cached = dataCache.get(cacheKey);
  if (!cached) return false;
  
  const now = Date.now();
  return now - cached.timestamp < cached.staleTime;
};

// Check if cached data should be kept
const shouldKeepCache = (cacheKey: string): boolean => {
  const cached = dataCache.get(cacheKey);
  if (!cached) return false;
  
  const now = Date.now();
  return now - cached.timestamp < cached.cacheTime;
};

// Clean expired cache entries
const cleanExpiredCache = (): void => {
  const now = Date.now();
  for (const [key, cached] of dataCache.entries()) {
    if (now - cached.timestamp > cached.cacheTime) {
      dataCache.delete(key);
    }
  }
};

// Enhanced API hook with robust error handling and caching
export function useRobustApi<T>(
  apiFunction: (...args: any[]) => Promise<T>,
  dependencies: any[] = [],
  config: RobustApiConfig = {}
): RobustApiState<T> & {
  refetch: () => Promise<void>;
  reset: () => void;
  invalidateCache: () => void;
} {
  const {
    retry = {},
    errorHandler = {},
    staleTime = 5 * 60 * 1000, // 5 minutes
    cacheTime = 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus = true,
    refetchOnReconnect = true,
    enabled = true,
    fallbackData = null,
    circuitBreaker = true
  } = config;

  const [state, setState] = useState<RobustApiState<T>>({
    data: fallbackData,
    loading: false,
    error: null,
    isRetrying: false,
    retryCount: 0,
    lastFetch: null,
    isStale: false
  });

  const apiFunctionRef = useRef(apiFunction);
  const abortControllerRef = useRef<AbortController | null>(null);
  const cacheKey = generateCacheKey(apiFunction, dependencies);

  // Update function ref when it changes
  useEffect(() => {
    apiFunctionRef.current = apiFunction;
  }, [apiFunction]);

  // Get or create circuit breaker
  const getCircuitBreaker = useCallback((): CircuitBreaker | null => {
    if (!circuitBreaker) return null;
    
    if (!circuitBreakers.has(cacheKey)) {
      circuitBreakers.set(cacheKey, new CircuitBreaker());
    }
    return circuitBreakers.get(cacheKey)!;
  }, [cacheKey, circuitBreaker]);

  // Fetch data with all enhancements
  const fetchData = useCallback(async (isRetry = false): Promise<void> => {
    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Check cache first
    if (!isRetry && isCacheFresh(cacheKey)) {
      const cached = dataCache.get(cacheKey);
      if (cached) {
        setState(prev => ({
          ...prev,
          data: cached.data,
          loading: false,
          error: null,
          isStale: false,
          lastFetch: cached.timestamp
        }));
        return;
      }
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();
    const signal = abortControllerRef.current.signal;

    setState(prev => ({
      ...prev,
      loading: true,
      error: null,
      isRetrying: isRetry,
      retryCount: isRetry ? prev.retryCount + 1 : 0
    }));

    try {
      const operation = async (): Promise<T> => {
        if (signal.aborted) {
          throw new Error('Request was cancelled');
        }
        return await apiFunctionRef.current(...dependencies);
      };

      let result: T;
      const cb = getCircuitBreaker();

      if (cb) {
        result = await cb.execute(() => withRetry(operation, retry));
      } else {
        result = await withRetry(operation, retry);
      }

      if (signal.aborted) return;

      const now = Date.now();

      // Cache the result
      dataCache.set(cacheKey, {
        data: result,
        timestamp: now,
        staleTime,
        cacheTime
      });

      setState(prev => ({
        ...prev,
        data: result,
        loading: false,
        error: null,
        isRetrying: false,
        lastFetch: now,
        isStale: false
      }));

    } catch (error) {
      if (signal.aborted) return;

      const appError = handleError(error, {
        ...errorHandler,
        context: { 
          ...errorHandler.context, 
          cacheKey, 
          retryCount: state.retryCount 
        }
      });

      setState(prev => ({
        ...prev,
        loading: false,
        error: appError,
        isRetrying: false,
        data: prev.data || fallbackData
      }));
    }
  }, [dependencies, cacheKey, retry, errorHandler, staleTime, cacheTime, fallbackData, state.retryCount]);

  // Manual refetch
  const refetch = useCallback(async (): Promise<void> => {
    await fetchData(false);
  }, [fetchData]);

  // Reset state
  const reset = useCallback((): void => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    setState({
      data: fallbackData,
      loading: false,
      error: null,
      isRetrying: false,
      retryCount: 0,
      lastFetch: null,
      isStale: false
    });
  }, [fallbackData]);

  // Invalidate cache
  const invalidateCache = useCallback((): void => {
    dataCache.delete(cacheKey);
    setState(prev => ({ ...prev, isStale: true }));
  }, [cacheKey]);

  // Mark data as stale
  const markStale = useCallback((): void => {
    setState(prev => ({ ...prev, isStale: true }));
  }, []);

  // Initial fetch
  useEffect(() => {
    if (enabled) {
      fetchData();
    }
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [enabled, ...dependencies]);

  // Window focus refetch
  useEffect(() => {
    if (!refetchOnWindowFocus) return;

    const handleFocus = () => {
      if (state.data && state.lastFetch) {
        const timeSinceLastFetch = Date.now() - state.lastFetch;
        if (timeSinceLastFetch > staleTime) {
          markStale();
          fetchData();
        }
      }
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [refetchOnWindowFocus, state.data, state.lastFetch, staleTime, fetchData, markStale]);

  // Network reconnect refetch
  useEffect(() => {
    if (!refetchOnReconnect) return;

    const handleOnline = () => {
      if (state.error || state.isStale) {
        fetchData();
      }
    };

    window.addEventListener('online', handleOnline);
    return () => window.removeEventListener('online', handleOnline);
  }, [refetchOnReconnect, state.error, state.isStale, fetchData]);

  // Clean cache periodically
  useEffect(() => {
    const interval = setInterval(cleanExpiredCache, 60000); // Clean every minute
    return () => clearInterval(interval);
  }, []);

  return {
    ...state,
    refetch,
    reset,
    invalidateCache
  };
}

// Mutation hook with robust error handling
export function useRobustMutation<TData, TVariables = void>(
  mutationFn: (variables: TVariables) => Promise<TData>,
  config: {
    onSuccess?: (data: TData, variables: TVariables) => void;
    onError?: (error: AppError, variables: TVariables) => void;
    retry?: Partial<RetryConfig>;
    errorHandler?: ErrorHandlerConfig;
  } = {}
) {
  const [state, setState] = useState<{
    data: TData | null;
    loading: boolean;
    error: AppError | null;
  }>({
    data: null,
    loading: false,
    error: null
  });

  const mutate = useCallback(async (variables: TVariables): Promise<TData | null> => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const result = await withRetry(
        () => mutationFn(variables),
        config.retry || {}
      );

      setState({
        data: result,
        loading: false,
        error: null
      });

      config.onSuccess?.(result, variables);
      return result;

    } catch (error) {
      const appError = handleError(error, config.errorHandler || {});
      
      setState({
        data: null,
        loading: false,
        error: appError
      });

      config.onError?.(appError, variables);
      return null;
    }
  }, [mutationFn, config]);

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null
    });
  }, []);

  return {
    ...state,
    mutate,
    reset
  };
}
