/**
 * Enhanced User Layout Component
 * Modern, responsive layout with shadcn/ui components and unified theme
 * Based on instructor layout design
 */

import React, { useState, useEffect, Suspense } from 'react';
import { Outlet } from 'react-router-dom';
import { Sheet, SheetContent } from "@/components/ui/sheet";
import UserSidebar from '@/components/user/UserSidebar';
import ResponsiveHeader from '@/components/layout/ResponsiveHeader';
import EmailVerificationPrompt from '../components/auth/EmailVerificationPrompt';
import LoadingSpinner from '../components/common/LoadingSpinner';
import BackToTopButton from '../components/common/BackToTopButton';
import { useAuth } from '../contexts/AuthContext';
import './UserLayout.css';


// Custom hook for responsive breakpoints
const useBreakpoint = () => {
  const [breakpoint, setBreakpoint] = useState({
    sm: false,
    md: false,
    lg: false,
    xl: false,
  });

  useEffect(() => {
    const updateBreakpoint = () => {
      setBreakpoint({
        sm: window.innerWidth >= 640,
        md: window.innerWidth >= 768,
        lg: window.innerWidth >= 1024,
        xl: window.innerWidth >= 1280,
      });
    };

    updateBreakpoint();
    window.addEventListener('resize', updateBreakpoint);
    return () => window.removeEventListener('resize', updateBreakpoint);
  }, []);

  return breakpoint;
};

// ============================================================================
// ENHANCED USER LAYOUT
// ============================================================================

const UserLayout: React.FC = () => {
  const { authState } = useAuth();
  const { user } = authState;
  const needsEmailVerification = user && !user.IsEmailConfirm;
  
  const [collapsed, setCollapsed] = useState(false);
  const [mobileDrawerOpen, setMobileDrawerOpen] = useState(false);

  // Use custom responsive breakpoints
  const screens = useBreakpoint();
  const isMobile = !screens.md;
  const isTablet = screens.md && !screens.lg;

  // Auto-collapse sidebar on tablet
  useEffect(() => {
    if (isTablet) {
      setCollapsed(true);
    } else if (screens.lg) {
      setCollapsed(false);
    }
  }, [isTablet, screens.lg]);

  // Sidebar configuration
  const siderWidth = 280;
  const collapsedWidth = 80;

  // Handle mobile drawer toggle
  const toggleMobileDrawer = () => {
    setMobileDrawerOpen(!mobileDrawerOpen);
  };

  // Handle desktop sidebar collapse
  const toggleCollapse = () => {
    setCollapsed(!collapsed);
  };

  // Render sidebar content
  const renderSidebar = () => (
    <UserSidebar
      collapsed={isMobile ? false : collapsed}
      onToggle={isMobile ? toggleMobileDrawer : toggleCollapse}
    />
  );

  return (
    <div className="min-h-screen bg-gray-50 font-nunito">
      {/* Header */}
      <header className="fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 shadow-sm">
        <ResponsiveHeader />
      </header>

      {/* Email verification banner */}
      {needsEmailVerification && user?.Slug && (
        <div className="fixed top-16 left-0 right-0 z-40 bg-yellow-50 border-b border-yellow-200">
          <EmailVerificationPrompt
            userSlug={user.Slug}
            userEmail={user.Email || ''}
            variant="banner"
            className="rounded-0 border-0"
          />
        </div>
      )}

      {/* Desktop Sidebar */}
      {!isMobile && (
        <aside
          className="fixed left-0 top-16 bottom-0 z-30 bg-gray-900 shadow-xl transition-all duration-300 ease-in-out overflow-auto"
          style={{ width: collapsed ? collapsedWidth : siderWidth }}
        >
          {renderSidebar()}
        </aside>
      )}

      {/* Mobile Drawer */}
      {isMobile && (
        <Sheet open={mobileDrawerOpen} onOpenChange={setMobileDrawerOpen}>
          <SheetContent side="left" className="p-0 w-70 bg-gray-900" style={{ width: siderWidth }}>
            {renderSidebar()}
          </SheetContent>
        </Sheet>
      )}

      {/* Main Layout */}
      <div
        className="transition-all duration-300 ease-in-out min-h-screen pt-16"
        style={{
          marginLeft: isMobile ? 0 : (collapsed ? collapsedWidth : siderWidth),
          paddingTop: needsEmailVerification ? '120px' : '64px', // Adjust for header + email banner
        }}
      >
        {/* Content */}
        <main className={`bg-gray-50 min-h-[calc(100vh-4rem)] overflow-auto ${isMobile ? 'p-4' : 'p-6'}`}>
          {/* Main Content */}
          <div className="max-w-7xl mx-auto w-full">
            <Suspense fallback={<LoadingSpinner />}>
              <Outlet />
            </Suspense>
          </div>
        </main>
      </div>

      {/* Global Styles for Custom Scrollbar */}
      <style>
        {`
          /* Custom scrollbar for sidebar */
          aside::-webkit-scrollbar {
            width: 6px;
          }

          aside::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
          }

          aside::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
          }

          aside::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
          }
        `}
      </style>

      {/* Back to Top Button */}
      <BackToTopButton />
    </div>
  );
};

export default UserLayout;
