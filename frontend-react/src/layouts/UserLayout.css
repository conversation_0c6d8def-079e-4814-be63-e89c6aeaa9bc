/* Enhanced User Layout Styles - Based on Instructor Design */

/* Ensure smooth transitions and proper font loading */
* {
  box-sizing: border-box;
}

/* Layout container */
.user-layout {
  min-height: 100vh;
  background-color: #f9fafb;
  font-family: '<PERSON>uni<PERSON>', sans-serif;
}

/* Header styles */
.user-layout-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  height: 64px;
}

/* Sidebar styles - matching instructor design */
.user-layout-sidebar {
  position: fixed;
  left: 0;
  top: 64px;
  bottom: 0;
  width: 280px;
  background: #111827; /* gray-900 */
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  z-index: 30;
  overflow-y: auto;
  transition: all 0.3s ease;
}

.user-layout-sidebar.collapsed {
  width: 80px;
}

/* Main content area */
.user-layout-main {
  margin-left: 280px;
  min-height: calc(100vh - 64px);
  padding-top: 64px;
  transition: margin-left 0.3s ease;
  background-color: #f9fafb;
}

.user-layout-main.collapsed {
  margin-left: 80px;
}

/* Mobile sidebar overlay */
.user-layout-mobile-overlay {
  position: fixed;
  inset: 0;
  z-index: 50;
  background-color: rgba(0, 0, 0, 0.5);
}

.user-layout-mobile-sidebar {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  width: 280px;
  background: #111827;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  z-index: 51;
  overflow-y: auto;
}

.user-layout-mobile-sidebar.open {
  transform: translateX(0);
}

/* Content padding adjustments */
.user-layout-content {
  padding: 24px;
  max-width: 1280px;
  margin: 0 auto;
  width: 100%;
}

/* Responsive breakpoints */
@media (max-width: 767px) {
  .user-layout-sidebar {
    display: none;
  }
  
  .user-layout-main {
    margin-left: 0;
  }
  
  .user-layout-content {
    padding: 16px;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .user-layout-sidebar {
    width: 80px;
  }
  
  .user-layout-main {
    margin-left: 80px;
  }
}

@media (min-width: 1024px) {
  .user-layout-mobile-overlay,
  .user-layout-mobile-sidebar {
    display: none;
  }
}

/* Custom scrollbar for sidebar - matching instructor design */
.user-layout-sidebar::-webkit-scrollbar {
  width: 6px;
}

.user-layout-sidebar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.user-layout-sidebar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.user-layout-sidebar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Animation for mobile sidebar */
@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-100%);
  }
}

.user-layout-mobile-sidebar.open {
  animation: slideIn 0.3s ease forwards;
}

.user-layout-mobile-sidebar.closing {
  animation: slideOut 0.3s ease forwards;
}
