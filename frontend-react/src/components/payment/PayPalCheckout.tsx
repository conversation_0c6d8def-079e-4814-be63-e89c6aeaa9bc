import React, { useEffect, useRef, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { useToast } from '@/hooks/use-toast';
import { Loader2, CreditCard, Shield, CheckCircle } from 'lucide-react';
import payPalService from '../../services/paypal.service';
import { useCart } from '../../contexts/CartContext';
import { useAuth } from '../../contexts/AuthContext';
import type { PayPalCaptureResponse, PayPalItem } from '../../models/paypal.model';

interface PayPalCheckoutProps {
  onSuccess?: (orderData: { orderID: string }, captureData: PayPalCaptureResponse) => void;
  onError?: (error: any) => void;
  onCancel?: (data: any) => void;
  className?: string;
  disabled?: boolean;
}

const PayPalCheckout: React.FC<PayPalCheckoutProps> = ({
  onSuccess,
  onError,
  onCancel,
  className = '',
  disabled = false
}) => {
  const { toast } = useToast();
  const { cart, clearCart } = useCart();
  const { authState } = useAuth();
  
  const paypalRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    if (!cart.Items || cart.Items.length === 0) {
      setError('Your cart is empty');
      setIsLoading(false);
      return;
    }

    if (!authState.isAuthenticated) {
      setError('Please log in to continue with payment');
      setIsLoading(false);
      return;
    }

    initializePayPal();
  }, [cart, authState.isAuthenticated]);

  const initializePayPal = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Load PayPal SDK
      await payPalService.loadPayPalScript({
        clientId: process.env.REACT_APP_PAYPAL_CLIENT_ID || '',
        currency: 'CAD',
        intent: 'capture',
        environment: process.env.NODE_ENV === 'production' ? 'production' : 'sandbox'
      });

      if (!payPalService.isPayPalReady()) {
        throw new Error('PayPal SDK failed to load');
      }

      // Create PayPal buttons
      await createPayPalButtons();
      
    } catch (err) {
      console.error('Error initializing PayPal:', err);
      setError('Failed to load PayPal. Please refresh the page and try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const createPayPalButtons = async () => {
    if (!paypalRef.current || !cart.Items) return;

    const containerId = 'paypal-button-container';
    paypalRef.current.id = containerId;

    // Convert cart items to PayPal format
    const paypalItems: PayPalItem[] = payPalService.convertCartItemsToPayPalItems(cart.Items);
    
    // Create order configuration
    const orderConfig = payPalService.createOrderConfig(
      payPalService.formatAmount(cart.TotalPrice || 0),
      paypalItems,
      'CAD',
      `BrainMaker Course Purchase - ${cart.Items.length} item(s)`
    );

    try {
      await payPalService.createPayPalButtons(
        containerId,
        orderConfig,
        handlePayPalSuccess,
        handlePayPalError,
        handlePayPalCancel
      );
    } catch (error) {
      console.error('Error creating PayPal buttons:', error);
      setError('Failed to initialize PayPal buttons');
    }
  };

  const handlePayPalSuccess = async (orderData: { orderID: string }, captureData: PayPalCaptureResponse) => {
    try {
      setIsProcessing(true);

      // Call success callback if provided
      if (onSuccess) {
        await onSuccess(orderData, captureData);
      }

      // Clear cart after successful payment
      await clearCart();

      toast({
        title: 'Payment Successful!',
        description: `Your order ${orderData.orderID} has been processed successfully.`,
      });

    } catch (error) {
      console.error('Error processing successful payment:', error);
      toast({
        title: 'Payment Processing Error',
        description: 'Payment was successful but there was an error processing your order. Please contact support.',
        variant: 'destructive'
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handlePayPalError = (error: any) => {
    console.error('PayPal error:', error);
    
    const errorMessage = error?.message || 'An error occurred during payment processing';
    setError(errorMessage);
    
    if (onError) {
      onError(error);
    }

    toast({
      title: 'Payment Error',
      description: errorMessage,
      variant: 'destructive'
    });
  };

  const handlePayPalCancel = (data: any) => {
    console.log('PayPal payment cancelled:', data);
    
    if (onCancel) {
      onCancel(data);
    }

    toast({
      title: 'Payment Cancelled',
      description: 'Your payment has been cancelled. You can try again when ready.',
    });
  };

  const retryPayPal = () => {
    setError(null);
    initializePayPal();
  };

  if (!cart.Items || cart.Items.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <Alert>
            <AlertDescription>
              Your cart is empty. Please add some courses before proceeding to payment.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (!authState.isAuthenticated) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <Alert>
            <AlertDescription>
              Please log in to continue with your purchase.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CreditCard className="h-5 w-5" />
          PayPal Checkout
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Order Summary */}
        <div className="space-y-3">
          <h4 className="font-medium text-gray-900">Order Summary</h4>
          
          {cart.Items.map((item, index) => (
            <div key={item.Id || index} className="flex justify-between items-center py-2">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">
                  {item.Course?.Title || 'Course'}
                </p>
                <p className="text-xs text-gray-500">
                  Digital Course Access
                </p>
              </div>
              <p className="text-sm font-medium text-gray-900">
                ${payPalService.formatAmount(item.Price || item.Course?.Price || 0)}
              </p>
            </div>
          ))}
          
          <Separator />
          
          <div className="flex justify-between items-center font-medium">
            <span>Total</span>
            <span className="text-lg">
              ${payPalService.formatAmount(cart.TotalPrice || 0)} CAD
            </span>
          </div>
        </div>

        <Separator />

        {/* PayPal Button Container */}
        <div className="space-y-4">
          {isLoading && (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              <span className="text-sm text-gray-600">Loading PayPal...</span>
            </div>
          )}

          {error && (
            <Alert variant="destructive">
              <AlertDescription className="flex items-center justify-between">
                <span>{error}</span>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={retryPayPal}
                  className="ml-2"
                >
                  Retry
                </Button>
              </AlertDescription>
            </Alert>
          )}

          {isProcessing && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                Processing your payment... Please wait.
              </AlertDescription>
            </Alert>
          )}

          {/* PayPal Buttons */}
          <div 
            ref={paypalRef}
            className={`${isLoading || error || disabled ? 'opacity-50 pointer-events-none' : ''}`}
            style={{ minHeight: '45px' }}
          />

          {/* Security Notice */}
          <div className="flex items-center gap-2 text-xs text-gray-500 mt-4">
            <Shield className="h-4 w-4" />
            <span>
              Your payment is secured by PayPal's advanced encryption and fraud protection.
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default PayPalCheckout;
