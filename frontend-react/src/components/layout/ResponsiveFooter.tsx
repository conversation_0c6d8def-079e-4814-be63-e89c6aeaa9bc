import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import {
  FaFacebook,
  FaTwitter,
  FaLinkedin,
  FaInstagram,
  FaEnvelope,
  FaPhone,
  FaMapMarkerAlt
} from 'react-icons/fa';
import { cn } from '../../lib/utils';

interface ResponsiveFooterProps {
  className?: string;
}

const ResponsiveFooter: React.FC<ResponsiveFooterProps> = ({ className }) => {
  const [email, setEmail] = useState('');
  const [isSubscribing, setIsSubscribing] = useState(false);
  const currentYear = new Date().getFullYear();

  const platformLinks = [
    { name: 'Home', url: '/' },
    { name: 'About', url: '/about' },
    { name: 'Courses', url: '/courses' },
    { name: 'Instructors', url: '/instructors' },
    { name: 'Forum', url: '/forum' },
    { name: 'Blog', url: '/blog' }
  ];

  const learningLinks = [
    { name: 'Browse Courses', url: '/courses' },
    { name: 'Course Categories', url: '/courses' },
    { name: 'Featured Courses', url: '/courses?featured=true' },
    { name: 'Free Courses', url: '/courses?price=free' },
    { name: 'New Courses', url: '/courses?sort=newest' },
    { name: 'Popular Courses', url: '/courses?sort=popular' }
  ];

  const communityLinks = [
    { name: 'Community Forum', url: '/forum' },
    { name: 'Live Meetings', url: '/meetings' },
    { name: 'Become Instructor', url: '/become-instructor' },
    { name: 'Student Success', url: '/blog' },
    { name: 'Course Reviews', url: '/courses' }
  ];

  const supportLinks = [
    { name: 'FAQ', url: '/faq' },
    { name: 'Contact Us', url: '/contact' },
    { name: 'Help Center', url: '/faq' },
    { name: 'Technical Support', url: '/contact' },
    { name: 'Report Issue', url: '/contact' }
  ];

  const businessLinks = [
    { name: 'Become an Instructor', url: '/become-instructor' },
    { name: 'Instructor Resources', url: '/become-instructor' },
    { name: 'Teaching Guidelines', url: '/become-instructor' },
    { name: 'Course Creation', url: '/become-instructor' }
  ];

  const legalLinks = [
    { name: 'Privacy Policy', url: '/privacy-policy' },
    { name: 'Terms of Service', url: '/terms' },
    { name: 'Cookie Policy', url: '/cookies' },
    { name: 'Refund Policy', url: '/refund' }
  ];

  const socialLinks = [
    { icon: FaFacebook, url: 'https://facebook.com/brainmaker', name: 'Facebook' },
    { icon: FaTwitter, url: 'https://twitter.com/brainmaker', name: 'Twitter' },
    { icon: FaLinkedin, url: 'https://linkedin.com/company/brainmaker', name: 'LinkedIn' },
    { icon: FaInstagram, url: 'https://instagram.com/brainmaker', name: 'Instagram' }
  ];

  const handleNewsletterSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email.trim()) return;

    setIsSubscribing(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setEmail('');
      // Show success message
    } catch (error) {
      // Show error message
    } finally {
      setIsSubscribing(false);
    }
  };

  return (
    <footer className={cn("bg-gray-900 text-white mt-20", className)}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* First Row */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {/* Company Info & Logo */}
          <div className="space-y-4">
            <img
              src="/assets/img/brain-maker-logo.png"
              alt="BrainMaker Academy"
              className="h-12 w-auto filter brightness-0 invert"
              onError={(e) => {
                e.currentTarget.style.display = 'none';
              }}
            />
            <p className="text-gray-400 text-sm leading-relaxed">
              Empowering minds through expert-led online education. Join thousands of learners worldwide.
            </p>

            {/* Contact Info */}
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <FaMapMarkerAlt className="text-red-500 text-sm flex-shrink-0" />
                <span className="text-gray-400 text-sm">
                  580 rue de Fontenay, Laval, QC
                </span>
              </div>
              <div className="flex items-center gap-3">
                <FaEnvelope className="text-red-500 text-sm flex-shrink-0" />
                <span className="text-gray-400 text-sm">
                  <EMAIL>
                </span>
              </div>
              <div className="flex items-center gap-3">
                <FaPhone className="text-red-500 text-sm flex-shrink-0" />
                <span className="text-gray-400 text-sm">
                  ******-688-6064
                </span>
              </div>
            </div>
          </div>

          {/* Platform */}
          <div className="space-y-4">
            <h4 className="text-white text-lg font-semibold">
              Platform
            </h4>
            <div className="space-y-2">
              {platformLinks.map((link) => (
                <Link
                  key={link.name}
                  to={link.url}
                  className="block text-gray-400 text-sm hover:text-red-500 transition-colors duration-200"
                >
                  {link.name}
                </Link>
              ))}
            </div>
          </div>

          {/* Learning */}
          <div className="space-y-4">
            <h4 className="text-white text-lg font-semibold">
              Learning
            </h4>
            <div className="space-y-2">
              {learningLinks.map((link) => (
                <Link
                  key={link.name}
                  to={link.url}
                  className="block text-gray-400 text-sm hover:text-red-500 transition-colors duration-200"
                >
                  {link.name}
                </Link>
              ))}
            </div>
          </div>

          {/* Community */}
          <div className="space-y-4">
            <h4 className="text-white text-lg font-semibold">
              Community
            </h4>
            <div className="space-y-2">
              {communityLinks.map((link) => (
                <Link
                  key={link.name}
                  to={link.url}
                  className="block text-gray-400 text-sm hover:text-red-500 transition-colors duration-200"
                >
                  {link.name}
                </Link>
              ))}
            </div>
          </div>

        </div>

        {/* Second Row */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 pt-8 border-t border-gray-800">
          {/* Support */}
          <div className="space-y-4">
            <h4 className="text-white text-lg font-semibold">
              Support
            </h4>
            <div className="space-y-2">
              {supportLinks.map((link) => (
                <Link
                  key={link.name}
                  to={link.url}
                  className="block text-gray-400 text-sm hover:text-red-500 transition-colors duration-200"
                >
                  {link.name}
                </Link>
              ))}
            </div>
          </div>

          {/* Teaching & Business */}
          <div className="space-y-4">
            <h4 className="text-white text-lg font-semibold">
              Teaching & Business
            </h4>
            <div className="space-y-2">
              {businessLinks.map((link) => (
                <Link
                  key={link.name}
                  to={link.url}
                  className="block text-gray-400 text-sm hover:text-red-500 transition-colors duration-200"
                >
                  {link.name}
                </Link>
              ))}
            </div>
          </div>

          {/* Newsletter & Social */}
          <div className="space-y-6">
            <div>
              <h4 className="text-white text-lg font-semibold mb-4">
                Stay Updated
              </h4>

              {/* Newsletter Signup */}
              <form onSubmit={handleNewsletterSubmit} className="space-y-3">
                <p className="text-gray-400 text-sm">
                  Get the latest courses and updates delivered to your inbox.
                </p>
                <div className="flex flex-col sm:flex-row gap-2">
                  <Input
                    type="email"
                    placeholder="Enter your email"
                    value={email}
                    onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => setEmail(e.target.value)}
                    className="flex-1 bg-gray-800 border-gray-700 text-white placeholder:text-gray-400"
                    required
                  />
                  <Button
                    type="submit"
                    disabled={isSubscribing}
                    className="bg-red-600 hover:bg-red-700 text-white"
                  >
                    {isSubscribing ? 'Subscribing...' : 'Subscribe'}
                  </Button>
                </div>
              </form>
            </div>

            {/* Social Media */}
            <div>
              <p className="text-gray-400 text-sm mb-3">
                Follow us on social media
              </p>
              <div className="flex space-x-3">
                {socialLinks.map((social) => (
                  <a
                    key={social.name}
                    href={social.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-10 h-10 bg-gray-800 hover:bg-red-600 rounded-full flex items-center justify-center text-gray-400 hover:text-white transition-all duration-200"
                    aria-label={social.name}
                  >
                    <social.icon className="w-4 h-4" />
                  </a>
                ))}
              </div>
            </div>
          </div>

          {/* Empty column for balance */}
          <div></div>
        </div>





        <Separator className="my-8 bg-gray-800" />

        {/* Footer Bottom */}
        <div className="flex flex-col md:flex-row justify-between items-center gap-4">
          <div className="text-center md:text-left">
            <p className="text-gray-400 text-sm">
              © {currentYear} BrainMaker Academy. All rights reserved.
            </p>
          </div>

          <div className="flex flex-wrap justify-center md:justify-end gap-4 text-xs">
            {legalLinks.map((link, index) => (
              <React.Fragment key={link.name}>
                <Link
                  to={link.url}
                  className="text-gray-400 hover:text-red-500 transition-colors"
                >
                  {link.name}
                </Link>
                {index < legalLinks.length - 1 && (
                  <span className="text-gray-600">•</span>
                )}
              </React.Fragment>
            ))}
          </div>
        </div>
      </div>
    </footer>
  );
};

export default ResponsiveFooter;
