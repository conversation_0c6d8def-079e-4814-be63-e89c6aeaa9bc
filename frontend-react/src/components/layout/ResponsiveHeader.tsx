import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useCart } from '../../contexts/CartContext';
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import {
  Search,
  User,
  ShoppingCart,
  Settings,
  LogOut,
  BookOpen,
  Heart,
  Menu as MenuIcon,
  X as XIcon,
  ChevronDown
} from 'lucide-react';
import { cn } from '../../lib/utils';

interface ResponsiveHeaderProps {
  className?: string;
}

const ResponsiveHeader: React.FC<ResponsiveHeaderProps> = ({ className }) => {
  const { authState, logout } = useAuth();
  const { cart } = useCart();
  const { isAuthenticated, user } = authState;
  const navigate = useNavigate();
  const location = useLocation();
  
  // State management
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isCategoriesOpen, setIsCategoriesOpen] = useState(false);

  // Responsive breakpoints
  const [windowWidth, setWindowWidth] = useState(typeof window !== 'undefined' ? window.innerWidth : 1200);
  const isMobile = windowWidth < 768;

  // Handle window resize
  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => setIsScrolled(window.scrollY > 10);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Navigation items
  const navigationItems = [
    { key: 'home', label: 'Home', path: '/' },
    { key: 'courses', label: 'Courses', path: '/courses' },
    { key: 'instructors', label: 'Instructors', path: '/instructors' }
  ];

  // Category items for dropdown
  const categoryItems = [
    { key: 'web-development', label: 'Web Development', path: '/courses/web-development' },
    { key: 'mobile-development', label: 'Mobile Development', path: '/courses/mobile-development' },
    { key: 'data-science', label: 'Data Science', path: '/courses/data-science' },
    { key: 'artificial-intelligence', label: 'Artificial Intelligence', path: '/courses/artificial-intelligence' },
    { key: 'cybersecurity', label: 'Cybersecurity', path: '/courses/cybersecurity' },
    { key: 'cloud-computing', label: 'Cloud Computing', path: '/courses/cloud-computing' },
    { key: 'digital-marketing', label: 'Digital Marketing', path: '/courses/digital-marketing' },
    { key: 'graphic-design', label: 'Graphic Design', path: '/courses/graphic-design' }
  ];

  // User menu items
  const userMenuItems = [
    {
      key: 'profile',
      label: 'Profile',
      icon: <User className="w-4 h-4" />,
      onClick: () => navigate('/user/profile')
    },
    {
      key: 'browse-courses',
      label: 'Browse Courses',
      icon: <BookOpen className="w-4 h-4" />,
      onClick: () => navigate('/')
    },
    {
      key: 'bookmarks',
      label: 'Bookmarks',
      icon: <Heart className="w-4 h-4" />,
      onClick: () => navigate('/user/bookmarks')
    },
    {
      key: 'settings',
      label: 'Settings',
      icon: <Settings className="w-4 h-4" />,
      onClick: () => navigate('/user/settings')
    },
    { type: 'divider' },
    {
      key: 'logout',
      label: 'Logout',
      icon: <LogOut className="w-4 h-4" />,
      onClick: logout
    }
  ];

  // Get current selected key
  const selectedKeys = navigationItems
    .filter(item => location.pathname === item.path || 
            (item.path !== '/' && location.pathname.startsWith(item.path)))
    .map(item => item.key);

  const handleSearch = (value: string) => {
    if (value.trim()) {
      navigate(`/search?q=${encodeURIComponent(value.trim())}`);
      setIsSearchOpen(false);
      setSearchQuery('');
    }
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
    setIsCategoriesOpen(false);
  };

  return (
    <div>
      <header
        className={cn(
          "fixed top-0 left-0 right-0 z-50 w-full transition-all duration-300 border-b",
          "bg-white/95 backdrop-blur-md overflow-visible",
          isScrolled
            ? "shadow-lg border-gray-200/80 bg-white/98"
            : "border-gray-100/50",
          className
        )}
        style={{
          backgroundImage: isScrolled
            ? 'radial-gradient(circle at 20% 50%, rgba(220, 53, 69, 0.03) 0%, transparent 50%), radial-gradient(circle at 80% 50%, rgba(220, 53, 69, 0.03) 0%, transparent 50%)'
            : 'radial-gradient(circle at 20% 50%, rgba(220, 53, 69, 0.02) 0%, transparent 50%), radial-gradient(circle at 80% 50%, rgba(220, 53, 69, 0.02) 0%, transparent 50%)',
          overflow: 'visible'
        }}
      >
        <div className="container mx-auto max-w-7xl px-4 sm:px-6 lg:px-8" style={{ overflow: 'visible' }}>
          {/* Main Header Content */}
          <div className={cn(
            "flex items-center justify-between transition-all duration-300",
            isMobile ? "h-14" : "h-16 lg:h-18"
          )}>

            {/* Logo Section */}
            <div className="flex items-center flex-shrink-0">
              <Link
                to="/"
                className="flex items-center"
                onClick={closeMobileMenu}
              >
                <div>
                  <img
                    src="/assets/img/brain-maker-logo.png"
                    alt="BrainMaker Academy"
                    className={cn(
                      "filter drop-shadow-sm",
                      isMobile ? "h-8 w-auto" : "h-10 lg:h-12 w-auto"
                    )}
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                      e.currentTarget.nextElementSibling?.classList.remove('hidden');
                    }}
                  />
                </div>

              </Link>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-1">
              {navigationItems.map((item) => (
                <Link
                  key={item.key}
                  to={item.path}
                  className={cn(
                    "relative px-4 py-2 text-base font-medium transition-colors duration-200",
                    selectedKeys.includes(item.key)
                      ? "text-red-600"
                      : "text-gray-900 hover:text-red-600"
                  )}
                >
                  {item.label}
                  {selectedKeys.includes(item.key) && (
                    <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1.5 h-1.5 bg-red-600 rounded-full" />
                  )}
                </Link>
              ))}

              {/* Categories Dropdown */}
              <DropdownMenu>
                <DropdownMenuTrigger className={cn(
                  "relative px-4 py-2 text-base font-medium transition-colors duration-200 flex items-center space-x-1",
                  "text-gray-900 hover:text-red-600 bg-transparent border-none cursor-pointer"
                )}>
                  <span>Categories</span>
                  <ChevronDown className="w-4 h-4" />
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  className="w-56 bg-white border border-gray-200 shadow-lg rounded-md p-1"
                  align="start"
                  sideOffset={5}
                  style={{
                    zIndex: 9999,
                    backgroundColor: 'white',
                    border: '1px solid #e5e7eb',
                    boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'
                  }}
                >
                  {categoryItems.map((category) => (
                    <DropdownMenuItem key={category.key} asChild>
                      <Link
                        to={category.path}
                        className="flex items-center px-3 py-2 text-sm text-gray-900 hover:text-red-600 hover:bg-gray-50 rounded-sm transition-colors duration-200 cursor-pointer w-full"
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          padding: '8px 12px',
                          fontSize: '14px',
                          color: '#111827',
                          textDecoration: 'none'
                        }}
                      >
                        {category.label}
                      </Link>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </nav>

            {/* Right Section */}
            <div className="flex items-center space-x-3">

              {/* Search - Desktop */}
              <div className="hidden lg:block">
                <div className="relative w-64 xl:w-80">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search courses..."
                    value={searchQuery}
                    onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => setSearchQuery(e.target.value)}
                    onKeyDown={(e) => e.key === 'Enter' && handleSearch(searchQuery)}
                    className="pl-10 h-10 text-base border-gray-200 focus:border-red-500 focus:ring-red-500"
                  />
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center space-x-2">
                {/* Search Button - Mobile/Tablet */}
                <button
                  className={cn(
                    "flex items-center justify-center w-10 h-10",
                    "text-gray-900 hover:text-red-600",
                    "transition-colors duration-200 lg:hidden"
                  )}
                  onClick={() => setIsSearchOpen(true)}
                  aria-label="Search"
                >
                  <Search className="w-4 h-4" />
                </button>

                {/* Cart Icon */}
                <div className="relative">
                  <button
                    className={cn(
                      "flex items-center justify-center w-10 h-10 relative",
                      "text-gray-900 hover:text-red-600",
                      "transition-colors duration-200"
                    )}
                    onClick={() => navigate('/cart')}
                    aria-label={`Shopping Cart (${cart.ItemCount} items)`}
                  >
                    <ShoppingCart className="w-4 h-4" />
                  </button>
                  {/* Cart badge */}
                  {cart.ItemCount > 0 && (
                    <Badge className="absolute -top-1 -right-1 h-5 w-5 text-xs bg-red-600 hover:bg-red-700 text-white rounded-full flex items-center justify-center">
                      {cart.ItemCount}
                    </Badge>
                  )}
                </div>
              </div>

              {/* User Section */}
              <div className="flex items-center space-x-3">
                {isAuthenticated ? (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <div className="flex items-center space-x-3 cursor-pointer group">
                        <div className="hidden md:block text-right">
                          <div className="text-sm font-medium text-gray-900 group-hover:text-red-600 transition-colors">
                            {user?.Firstname} {user?.Lastname}
                          </div>
                          <div className="text-xs text-gray-500">
                            {user?.Email}
                          </div>
                        </div>
                        <div className="relative">
                          <Avatar className={cn(
                            "cursor-pointer bg-gradient-to-br from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 transition-all duration-200 shadow-lg hover:shadow-xl border-2 border-white",
                            isMobile ? "h-9 w-9" : "h-10 w-10"
                          )}>
                            <AvatarFallback className="bg-red-600 text-white">
                              {user?.Username?.charAt(0).toUpperCase() || 'U'}
                            </AvatarFallback>
                          </Avatar>
                          <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 border-2 border-white rounded-full"></div>
                        </div>
                      </div>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                      className="w-56 bg-white border border-gray-200 shadow-lg rounded-md p-1"
                      align="end"
                      sideOffset={5}
                      style={{
                        zIndex: 9999,
                        backgroundColor: 'white',
                        border: '1px solid #e5e7eb',
                        boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'
                      }}
                    >
                      <DropdownMenuLabel className="px-3 py-2 text-sm font-semibold text-gray-900">My Account</DropdownMenuLabel>
                      <DropdownMenuSeparator className="my-1 border-gray-200" />
                      {userMenuItems.filter(item => item.type !== 'divider').map((item) => (
                        <DropdownMenuItem key={item.key} onClick={() => item.onClick?.()} className="flex items-center px-3 py-2 text-sm text-gray-900 hover:text-red-600 hover:bg-gray-50 rounded-sm transition-colors duration-200 cursor-pointer">
                          {item.icon}
                          <span className="ml-2">{item.label}</span>
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                ) : (
                  <div className="hidden sm:flex items-center space-x-2">
                    <Link
                      to="/auth/login"
                      className={cn(
                        "px-4 py-2 text-base font-medium transition-colors duration-200",
                        "text-gray-900 hover:text-red-600"
                      )}
                    >
                      Login
                    </Link>
                    <Link
                      to="/auth/register"
                      className={cn(
                        "px-4 py-2 text-base font-medium transition-colors duration-200",
                        "bg-red-600 text-white hover:bg-red-700 rounded-lg"
                      )}
                    >
                      Sign Up
                    </Link>
                  </div>
                )}

                {/* Mobile Menu Toggle */}
                <button
                  className={cn(
                    "flex items-center justify-center w-10 h-10 lg:hidden",
                    "text-gray-900 hover:text-red-600",
                    "transition-colors duration-200"
                  )}
                  onClick={() => setIsMobileMenuOpen(true)}
                  aria-label="Open Menu"
                >
                  <MenuIcon size={20} />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile Search Overlay */}
        {isSearchOpen && (
          <div className="absolute top-full left-0 right-0 bg-white/95 backdrop-blur-md border-b border-gray-200 p-4 lg:hidden shadow-lg">
            <div className="container mx-auto max-w-7xl px-4">
              <div className="flex items-center space-x-3">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search courses, instructors, topics..."
                    value={searchQuery}
                    onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => setSearchQuery(e.target.value)}
                    onKeyDown={(e) => e.key === 'Enter' && handleSearch(searchQuery)}
                    autoFocus
                    className="pl-10 h-12 text-base border-gray-200 focus:border-red-500 focus:ring-red-500 w-full"
                  />
                </div>
                <button
                  className={cn(
                    "flex items-center justify-center w-10 h-10",
                    "text-gray-900 hover:text-red-600",
                    "transition-colors duration-200"
                  )}
                  onClick={() => setIsSearchOpen(false)}
                  aria-label="Close Search"
                >
                  <XIcon className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Mobile Menu Sheet */}
        <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
          <SheetContent side="right" className="w-full sm:max-w-md bg-white">
            <SheetHeader className="pb-4 border-b border-gray-100">
              <SheetTitle className="flex items-center gap-2 text-gray-900">
                <div className="w-8 h-8 bg-red-600 rounded-lg flex items-center justify-center">
                  <MenuIcon className="w-4 h-4 text-white" />
                </div>
                Menu
              </SheetTitle>
            </SheetHeader>

            <div className="mt-6 space-y-4">
              {/* User Profile Section */}
              {isAuthenticated && user && (
                <div className="flex items-center gap-3 p-4 bg-gray-50 border border-gray-200 rounded-lg">
                  <Avatar className="h-12 w-12">
                    <AvatarFallback className="bg-red-600 text-white font-semibold">
                      {user?.Username?.charAt(0).toUpperCase() || 'U'}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <p className="font-semibold text-sm truncate text-gray-900">
                      {user.Firstname} {user.Lastname}
                    </p>
                    <p className="text-xs text-gray-600 truncate">
                      {user.Email}
                    </p>
                    {user.Role && (
                      <div className="flex items-center gap-1 mt-1">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span className="text-xs text-blue-600 font-medium">
                          {String(user.Role) === 'instructor' ? 'Instructor' : String(user.Role) === 'admin' ? 'Admin' : 'Student'}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Navigation Links */}
              <div className="space-y-1">
                <h4 className="text-sm font-semibold text-gray-700 px-2 mb-2 flex items-center gap-2">
                  <div className="w-1 h-4 bg-red-600 rounded-full"></div>
                  Navigation
                </h4>
                {navigationItems.map((item) => (
                  <Button
                    key={item.key}
                    variant={selectedKeys.includes(item.key) ? "secondary" : "ghost"}
                    className={cn(
                      "w-full justify-start",
                      selectedKeys.includes(item.key)
                        ? "bg-red-50 text-red-700"
                        : "hover:bg-gray-50"
                    )}
                    asChild
                  >
                    <Link to={item.path} onClick={closeMobileMenu}>
                      {item.label}
                    </Link>
                  </Button>
                ))}
              </div>

              {/* Categories */}
              <div className="space-y-1">
                <h4 className="text-sm font-semibold text-gray-700 px-2 mb-2 flex items-center gap-2">
                  <div className="w-1 h-4 bg-gray-400 rounded-full"></div>
                  Categories
                </h4>
                <div className="grid gap-1">
                  {categoryItems.slice(0, 6).map((category) => (
                    <Button
                      key={category.key}
                      variant="ghost"
                      size="sm"
                      className="justify-start h-auto py-2 hover:bg-gray-50"
                      asChild
                    >
                      <Link to={category.path} onClick={closeMobileMenu}>
                        {category.label}
                      </Link>
                    </Button>
                  ))}
                  {categoryItems.length > 6 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="justify-start text-gray-500 hover:bg-gray-50"
                      onClick={() => setIsCategoriesOpen(!isCategoriesOpen)}
                    >
                      {isCategoriesOpen ? 'Show Less' : `+${categoryItems.length - 6} More`}
                      <ChevronDown className={cn("ml-auto h-4 w-4 transition-transform", isCategoriesOpen && "rotate-180")} />
                    </Button>
                  )}
                  {isCategoriesOpen && categoryItems.slice(6).map((category) => (
                    <Button
                      key={category.key}
                      variant="ghost"
                      size="sm"
                      className="justify-start h-auto py-2 hover:bg-gray-50"
                      asChild
                    >
                      <Link to={category.path} onClick={closeMobileMenu}>
                        {category.label}
                      </Link>
                    </Button>
                  ))}
                </div>
              </div>

              {/* User Actions */}
              {isAuthenticated && (
                <div className="space-y-1 pt-4 border-t border-gray-200">
                  <h4 className="text-sm font-semibold text-gray-700 px-2 mb-2 flex items-center gap-2">
                    <div className="w-1 h-4 bg-gray-400 rounded-full"></div>
                    Account
                  </h4>
                  {userMenuItems.filter(item => item.type !== 'divider').map((item) => (
                    <Button
                      key={item.key}
                      variant="ghost"
                      className={cn(
                        "w-full justify-start",
                        item.key === 'logout'
                          ? "text-red-600 hover:text-red-700 hover:bg-red-50"
                          : "hover:bg-gray-50"
                      )}
                      onClick={() => {
                        item.onClick?.();
                        closeMobileMenu();
                      }}
                    >
                      {item.icon}
                      <span className="ml-2">{item.label}</span>
                    </Button>
                  ))}
                </div>
              )}

              {/* Auth Buttons for non-authenticated users */}
              {!isAuthenticated && (
                <div className="space-y-3 pt-4 border-t border-gray-200">
                  <h4 className="text-sm font-semibold text-gray-700 px-2 mb-2 flex items-center gap-2">
                    <div className="w-1 h-4 bg-gray-400 rounded-full"></div>
                    Get Started
                  </h4>
                  <Button className="w-full" variant="outline" asChild>
                    <Link to="/auth/login" onClick={closeMobileMenu}>
                      Login
                    </Link>
                  </Button>
                  <Button className="w-full bg-red-600 hover:bg-red-700" asChild>
                    <Link to="/auth/register" onClick={closeMobileMenu}>
                      Sign Up
                    </Link>
                  </Button>
                </div>
              )}
            </div>
          </SheetContent>
        </Sheet>
      </header>

      {/* Spacer to push content below fixed header */}
      <div className={cn(
        "w-full transition-all duration-300",
        isMobile ? "h-14" : "h-16 lg:h-18"
      )} />
    </div>
  );
};

export default ResponsiveHeader;
