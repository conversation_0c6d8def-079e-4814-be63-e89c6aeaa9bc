import React, { createContext, useContext, useState, useEffect } from 'react';
import { AILearningAssistant } from './AILearningAssistant';
import { Button } from '../ui/button';
import { Bot, MessageSquare } from 'lucide-react';
import { cn } from '../../lib/utils';
import { useLocation } from 'react-router-dom';

// AI Assistant Context
interface AIAssistantContextType {
  isOpen: boolean;
  toggle: () => void;
  open: () => void;
  close: () => void;
  setContext: (context: AIContext) => void;
  context: AIContext | null;
}

interface AIContext {
  courseId?: string;
  lessonId?: string;
  pageUrl?: string;
  selectedText?: string;
  pageTitle?: string;
  userRole?: string;
}

const AIAssistantContext = createContext<AIAssistantContextType | undefined>(undefined);

// Hook to use AI Assistant
export const useAIAssistant = () => {
  const context = useContext(AIAssistantContext);
  if (!context) {
    throw new Error('useAIAssistant must be used within AIAssistantProvider');
  }
  return context;
};

// AI Assistant Provider Props
interface AIAssistantProviderProps {
  children: React.ReactNode;
  position?: 'bottom-right' | 'bottom-left' | 'center';
  showFloatingButton?: boolean;
  floatingButtonPosition?: 'bottom-right' | 'bottom-left';
  className?: string;
}

// Floating AI Button Component
interface FloatingAIButtonProps {
  onClick: () => void;
  position: 'bottom-right' | 'bottom-left';
  isOpen: boolean;
  hasNewFeatures?: boolean;
}

const FloatingAIButton: React.FC<FloatingAIButtonProps> = ({
  onClick,
  position,
  isOpen,
  hasNewFeatures = false
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);

  useEffect(() => {
    // Show tooltip for first-time users
    const hasSeenTooltip = localStorage.getItem('ai-assistant-tooltip-seen');
    if (!hasSeenTooltip) {
      const timer = setTimeout(() => {
        setShowTooltip(true);
        setTimeout(() => {
          setShowTooltip(false);
          localStorage.setItem('ai-assistant-tooltip-seen', 'true');
        }, 5000);
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, []);

  const positionClasses = {
    'bottom-right': 'fixed bottom-6 right-6',
    'bottom-left': 'fixed bottom-6 left-6'
  };

  return (
    <div className={cn('z-40', positionClasses[position])}>
      {/* Tooltip */}
      {showTooltip && (
        <div className={cn(
          'absolute mb-2 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg shadow-lg',
          'animate-in fade-in-0 slide-in-from-bottom-2 duration-300',
          position === 'bottom-right' ? 'bottom-full right-0' : 'bottom-full left-0'
        )}>
          <div className="whitespace-nowrap">
            👋 Need help? Ask your AI assistant!
          </div>
          <div className={cn(
            'absolute top-full w-0 h-0 border-l-4 border-r-4 border-t-4',
            'border-l-transparent border-r-transparent border-t-gray-900',
            position === 'bottom-right' ? 'right-4' : 'left-4'
          )} />
        </div>
      )}

      {/* Floating Button */}
      <Button
        onClick={onClick}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        className={cn(
          'relative w-14 h-14 rounded-full shadow-2xl transition-all duration-300',
          'bg-gradient-to-r from-red-600 to-gray-900 hover:from-red-700 hover:to-black',
          'border-2 border-white/20 hover:border-white/40',
          'transform hover:scale-110 active:scale-95',
          isOpen && 'rotate-180',
          isHovered && 'shadow-blue-500/25'
        )}
        size="lg"
      >
        {/* Notification Badge */}
        {hasNewFeatures && (
          <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
            <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
          </div>
        )}

        {/* Icon */}
        <div className="relative">
          <Bot className={cn(
            'w-6 h-6 text-white transition-all duration-300',
            isOpen && 'rotate-180'
          )} />
          
          {/* Pulse Animation */}
          <div className={cn(
            'absolute inset-0 rounded-full bg-red-400/30 animate-ping',
            !isHovered && 'hidden'
          )} />
        </div>

        {/* Ripple Effect */}
        <div className={cn(
          'absolute inset-0 rounded-full bg-white/10 scale-0 transition-transform duration-300',
          isHovered && 'scale-100'
        )} />
      </Button>

      {/* Hover Label */}
      {isHovered && !isOpen && (
        <div className={cn(
          'absolute mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded shadow-lg',
          'animate-in fade-in-0 slide-in-from-bottom-1 duration-200',
          position === 'bottom-right' ? 'bottom-full right-0' : 'bottom-full left-0'
        )}>
          AI Assistant
        </div>
      )}
    </div>
  );
};

// Main AI Assistant Provider
export const AIAssistantProvider: React.FC<AIAssistantProviderProps> = ({
  children,
  position = 'bottom-right',
  showFloatingButton = true,
  floatingButtonPosition = 'bottom-right',
  className
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [context, setContext] = useState<AIContext | null>(null);
  const location = useLocation();

  // Auto-update context based on current page
  useEffect(() => {
    const updateContextFromLocation = () => {
      const newContext: AIContext = {
        pageUrl: location.pathname,
        pageTitle: document.title,
      };

      // Extract course/lesson IDs from URL
      const pathParts = location.pathname.split('/');
      if (pathParts.includes('courses') && pathParts.length > 2) {
        const courseIndex = pathParts.indexOf('courses');
        if (pathParts[courseIndex + 1]) {
          newContext.courseId = pathParts[courseIndex + 1];
        }
        if (pathParts[courseIndex + 2] === 'lessons' && pathParts[courseIndex + 3]) {
          newContext.lessonId = pathParts[courseIndex + 3];
        }
      }

      setContext(newContext);
    };

    updateContextFromLocation();
  }, [location]);

  // Handle text selection for context
  useEffect(() => {
    const handleTextSelection = () => {
      const selection = window.getSelection();
      if (selection && selection.toString().trim().length > 0) {
        setContext(prev => ({
          ...prev,
          selectedText: selection.toString().trim()
        }));
      }
    };

    document.addEventListener('mouseup', handleTextSelection);
    return () => document.removeEventListener('mouseup', handleTextSelection);
  }, []);

  // Keyboard shortcut to toggle AI assistant
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl/Cmd + Shift + A to toggle AI assistant
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'A') {
        e.preventDefault();
        toggle();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  const toggle = () => setIsOpen(prev => !prev);
  const open = () => setIsOpen(true);
  const close = () => setIsOpen(false);

  const contextValue: AIAssistantContextType = {
    isOpen,
    toggle,
    open,
    close,
    setContext,
    context
  };

  return (
    <AIAssistantContext.Provider value={contextValue}>
      {children}
      
      {/* Floating Button */}
      {showFloatingButton && (
        <FloatingAIButton
          onClick={toggle}
          position={floatingButtonPosition}
          isOpen={isOpen}
          hasNewFeatures={false} // Set to true when you have new features
        />
      )}

      {/* AI Assistant Modal */}
      <AILearningAssistant
        isOpen={isOpen}
        onToggle={toggle}
        position={position}
        context={context || undefined}
        className={className}
      />
    </AIAssistantContext.Provider>
  );
};

// Quick access hooks for common AI actions
export const useAIQuickActions = () => {
  const { open, setContext } = useAIAssistant();

  const explainConcept = (concept: string, additionalContext?: Partial<AIContext>) => {
    setContext({
      selectedText: concept,
      ...additionalContext
    });
    open();
  };

  const getPracticeQuestions = (topic: string, additionalContext?: Partial<AIContext>) => {
    setContext({
      selectedText: `Generate practice questions for: ${topic}`,
      ...additionalContext
    });
    open();
  };

  const getStudyHelp = (subject: string, additionalContext?: Partial<AIContext>) => {
    setContext({
      selectedText: `I need help studying: ${subject}`,
      ...additionalContext
    });
    open();
  };

  const explainCode = (code: string, language?: string, additionalContext?: Partial<AIContext>) => {
    setContext({
      selectedText: `Explain this ${language || ''} code: ${code}`,
      ...additionalContext
    });
    open();
  };

  return {
    explainConcept,
    getPracticeQuestions,
    getStudyHelp,
    explainCode
  };
};

// Context menu integration
export const AIContextMenu: React.FC<{
  selectedText: string;
  onClose: () => void;
  position: { x: number; y: number };
}> = ({ selectedText, onClose, position }) => {
  const { explainConcept, getPracticeQuestions } = useAIQuickActions();

  const handleAction = (action: () => void) => {
    action();
    onClose();
  };

  return (
    <div
      className="fixed z-50 bg-white rounded-lg shadow-xl border border-gray-200 py-2 min-w-48"
      style={{ left: position.x, top: position.y }}
    >
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleAction(() => explainConcept(selectedText))}
        className="w-full justify-start px-3 py-2 text-sm"
      >
        <Bot className="w-4 h-4 mr-2" />
        Ask AI to explain this
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleAction(() => getPracticeQuestions(selectedText))}
        className="w-full justify-start px-3 py-2 text-sm"
      >
        <MessageSquare className="w-4 h-4 mr-2" />
        Get practice questions
      </Button>
    </div>
  );
};
