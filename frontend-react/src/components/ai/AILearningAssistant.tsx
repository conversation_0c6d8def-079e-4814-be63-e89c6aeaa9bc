import React, { useState, useRef, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '../ui/card';
import { But<PERSON> } from '../ui/button';
import { Input } from '../ui/input';
import { Badge } from '../ui/badge';
import {
  Bot,
  Send,
  Trash2,
  ThumbsUp,
  ThumbsDown,
  Copy,
  Minimize2,
  Maximize2,
  X,
  Lightbulb,
  BookOpen,
  Code,
  Sparkles,
  Volume2,
  VolumeX,
  Mic,
  MicOff
} from 'lucide-react';
import { cn } from '../../lib/utils';
import { notify } from '../../utils/errorHandling';
import { LoadingSpinner, InlineLoading } from '../ui/loading-states';
import { useAIChat } from '../../hooks/useAIChat';
import type { AIContext } from '../../services/ai.service';

interface AIAssistantProps {
  isOpen: boolean;
  onToggle: () => void;
  position?: 'bottom-right' | 'bottom-left' | 'center';
  context?: AIContext;
  className?: string;
}

// Quick action prompts
const QUICK_PROMPTS = [
  {
    id: 'explain',
    text: 'Explain this concept in simple terms',
    icon: Lightbulb,
    category: 'understanding'
  },
  {
    id: 'practice',
    text: 'Give me practice questions',
    icon: BookOpen,
    category: 'practice'
  },
  {
    id: 'strategies',
    text: 'Suggest study strategies',
    icon: Sparkles,
    category: 'study'
  },
  {
    id: 'code',
    text: 'Help me understand this code',
    icon: Code,
    category: 'technical'
  }
];

export const AILearningAssistant: React.FC<AIAssistantProps> = ({
  isOpen,
  onToggle,
  position = 'bottom-right',
  context,
  className
}) => {
  // Use AI chat hook for real AI functionality
  const {
    messages,
    isLoading,
    sendMessage: aiSendMessage,
    clearMessages,
    submitFeedback,
    suggestions: aiSuggestions,
    isTyping
  } = useAIChat({ context });

  const [inputValue, setInputValue] = useState('');
  const [isMinimized, setIsMinimized] = useState(false);
  const [speechEnabled, setSpeechEnabled] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [messageFeedback, setMessageFeedback] = useState<Record<string, 'positive' | 'negative'>>({});
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const recognitionRef = useRef<any>(null);

  // Initialize speech recognition
  useEffect(() => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
      recognitionRef.current = new SpeechRecognition();
      recognitionRef.current.continuous = false;
      recognitionRef.current.interimResults = false;
      recognitionRef.current.lang = 'en-US';

      recognitionRef.current.onresult = (event: any) => {
        const transcript = event.results[0][0].transcript;
        setInputValue(transcript);
        setIsListening(false);
      };

      recognitionRef.current.onerror = () => {
        setIsListening(false);
        notify.error('Speech recognition error');
      };

      recognitionRef.current.onend = () => {
        setIsListening(false);
      };
    }
  }, []);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Focus input when opened
  useEffect(() => {
    if (isOpen && !isMinimized) {
      setTimeout(() => inputRef.current?.focus(), 100);
    }
  }, [isOpen, isMinimized]);

  // Send message using AI chat hook
  const sendMessage = async (message: string) => {
    setInputValue('');

    try {
      await aiSendMessage(message);

      // Text-to-speech if enabled (for the latest AI response)
      if (speechEnabled && messages.length > 0) {
        const lastMessage = messages[messages.length - 1];
        if (lastMessage.type === 'assistant') {
          speakText(lastMessage.content);
        }
      }
    } catch (error) {
      notify.error('Failed to get AI response', {
        description: 'Please try again later.'
      });
    }
  };



  // Text-to-speech functionality
  const speakText = (text: string) => {
    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.rate = 0.8;
      utterance.pitch = 1;
      speechSynthesis.speak(utterance);
    }
  };

  // Voice input functionality
  const toggleVoiceInput = () => {
    if (!recognitionRef.current) {
      notify.error('Speech recognition not supported');
      return;
    }

    if (isListening) {
      recognitionRef.current.stop();
      setIsListening(false);
    } else {
      recognitionRef.current.start();
      setIsListening(true);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (inputValue.trim() && !isLoading) {
      sendMessage(inputValue.trim());
    }
  };

  const handleQuickPrompt = (prompt: typeof QUICK_PROMPTS[0]) => {
    sendMessage(prompt.text);
  };

  const handleFeedback = async (messageId: string, feedback: 'positive' | 'negative') => {
    try {
      // Update local feedback state immediately
      setMessageFeedback(prev => ({ ...prev, [messageId]: feedback }));

      // Submit to backend
      await submitFeedback(messageId, feedback);
    } catch (error) {
      console.error('Failed to submit feedback:', error);
      // Revert local state on error
      setMessageFeedback(prev => {
        const newState = { ...prev };
        delete newState[messageId];
        return newState;
      });
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    notify.success('Copied to clipboard');
  };

  const clearConversation = () => {
    clearMessages();
    notify.info('Conversation cleared');
  };

  const positionClasses = {
    'bottom-right': 'fixed bottom-4 right-4 z-50',
    'bottom-left': 'fixed bottom-4 left-4 z-50',
    'center': 'fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50'
  };

  if (!isOpen) return null;

  return (
    <div className={cn(positionClasses[position], className)}>
      <Card className={cn(
        'w-96 shadow-2xl border-2 transition-all duration-300',
        isMinimized ? 'h-16' : 'h-[600px]'
      )}>
        {/* Header */}
        <CardHeader className="pb-3 bg-gradient-to-r from-red-600 to-gray-900 text-white rounded-t-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="relative">
                <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                  <Bot className="w-6 h-6" />
                </div>
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-400 rounded-full animate-pulse" />
              </div>
              <div className="flex-1 min-w-0">
                <CardTitle className="text-lg font-nunito-bold truncate">AI Learning Assistant</CardTitle>
                {!isMinimized && (
                  <p className="text-xs text-white/80 truncate">Your personal study companion</p>
                )}
              </div>
            </div>

            <div className="flex items-center space-x-1 flex-shrink-0">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSpeechEnabled(!speechEnabled)}
                className="text-white hover:bg-white/20 p-1 h-7 w-7"
                title={speechEnabled ? "Disable voice" : "Enable voice"}
              >
                {speechEnabled ? <Volume2 className="w-3 h-3" /> : <VolumeX className="w-3 h-3" />}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsMinimized(!isMinimized)}
                className="text-white hover:bg-white/20 p-1 h-7 w-7"
                title={isMinimized ? "Expand" : "Minimize"}
              >
                {isMinimized ? <Maximize2 className="w-3 h-3" /> : <Minimize2 className="w-3 h-3" />}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={onToggle}
                className="text-white hover:bg-white/20 p-1 h-7 w-7"
                title="Close"
              >
                <X className="w-3 h-3" />
              </Button>
            </div>
          </div>
        </CardHeader>

        {!isMinimized && (
          <CardContent className="p-0 flex flex-col h-[calc(600px-80px)]">
            {/* Quick Actions */}
            {messages.length === 0 && (
              <div className="p-4 border-b bg-gray-50">
                <p className="text-sm text-gray-700 mb-3 font-medium">Quick actions to get started:</p>
                <div className="grid grid-cols-1 gap-2">
                  {QUICK_PROMPTS.map((prompt) => {
                    const Icon = prompt.icon;
                    return (
                      <Button
                        key={prompt.id}
                        variant="outline"
                        size="sm"
                        onClick={() => handleQuickPrompt(prompt)}
                        className="justify-start text-xs h-auto py-3 px-3 border-gray-300 hover:border-red-500 hover:bg-red-50 text-gray-700 hover:text-red-700 transition-colors"
                        disabled={isLoading || isTyping}
                      >
                        <Icon className="w-4 h-4 mr-3 flex-shrink-0" />
                        <span className="text-left leading-tight">{prompt.text}</span>
                      </Button>
                    );
                  })}
                </div>

                {/* AI Suggestions */}
                {aiSuggestions && aiSuggestions.length > 0 && (
                  <div className="mt-3 pt-3 border-t border-gray-200">
                    <p className="text-xs text-gray-600 mb-2 font-medium">AI Suggestions:</p>
                    <div className="space-y-1">
                      {aiSuggestions.slice(0, 3).map((suggestion, index) => (
                        <Button
                          key={index}
                          variant="ghost"
                          size="sm"
                          onClick={() => sendMessage(suggestion)}
                          className="w-full justify-start text-xs h-auto py-2 px-2 text-gray-600 hover:text-red-600 hover:bg-red-50 transition-colors"
                          disabled={isLoading || isTyping}
                        >
                          <span className="text-left leading-tight">{suggestion}</span>
                        </Button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4 max-h-96">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={cn(
                    'flex',
                    message.type === 'user' ? 'justify-end' : 'justify-start'
                  )}
                >
                  <div
                    className={cn(
                      'max-w-[80%] rounded-lg p-3 text-sm',
                      message.type === 'user'
                        ? 'bg-red-600 text-white'
                        : 'bg-gray-100 text-gray-900 border border-gray-200'
                    )}
                  >
                    <p className="whitespace-pre-wrap leading-relaxed">{message.content}</p>

                    {message.type === 'assistant' && (
                      <div className="flex items-center justify-between mt-3 pt-2 border-t border-gray-200">
                        <div className="flex items-center space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => copyToClipboard(message.content)}
                            className="p-1 h-7 w-7 hover:bg-gray-200 text-gray-600 hover:text-gray-900"
                            title="Copy message"
                          >
                            <Copy className="w-3 h-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleFeedback(message.id, 'positive')}
                            className={cn(
                              'p-1 h-7 w-7 hover:bg-green-50',
                              messageFeedback[message.id] === 'positive'
                                ? 'text-green-600 bg-green-50'
                                : 'text-gray-600 hover:text-green-600'
                            )}
                            title="Good response"
                          >
                            <ThumbsUp className="w-3 h-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleFeedback(message.id, 'negative')}
                            className={cn(
                              'p-1 h-7 w-7 hover:bg-red-50',
                              messageFeedback[message.id] === 'negative'
                                ? 'text-red-600 bg-red-50'
                                : 'text-gray-600 hover:text-red-600'
                            )}
                            title="Poor response"
                          >
                            <ThumbsDown className="w-3 h-3" />
                          </Button>
                        </div>
                        <span className="text-xs text-gray-500 ml-2 flex-shrink-0">
                          {new Date(message.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              ))}

              {(isLoading || isTyping) && (
                <div className="flex justify-start">
                  <div className="bg-gray-100 rounded-lg p-3">
                    <InlineLoading message="AI is thinking..." size="sm" />
                  </div>
                </div>
              )}

              <div ref={messagesEndRef} />
            </div>

            {/* Input Area */}
            <div className="border-t p-4 bg-white">
              <form onSubmit={handleSubmit} className="flex space-x-2">
                <div className="flex-1 relative">
                  <Input
                    ref={inputRef}
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    placeholder="Ask me anything about your studies..."
                    disabled={isLoading}
                    className="pr-10 border-gray-300 focus:border-red-500 focus:ring-red-500"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={toggleVoiceInput}
                    className={cn(
                      'absolute right-2 top-1/2 transform -translate-y-1/2 p-1',
                      isListening ? 'text-red-500 animate-pulse' : 'text-gray-400 hover:text-red-500'
                    )}
                    disabled={isLoading}
                    title={isListening ? "Stop listening" : "Start voice input"}
                  >
                    {isListening ? <MicOff className="w-4 h-4" /> : <Mic className="w-4 h-4" />}
                  </Button>
                </div>
                <Button
                  type="submit"
                  disabled={!inputValue.trim() || isLoading}
                  className="bg-red-600 hover:bg-red-700 text-white border-0"
                >
                  {isLoading ? (
                    <LoadingSpinner size="sm" />
                  ) : (
                    <Send className="w-4 h-4" />
                  )}
                </Button>
              </form>

              {messages.length > 0 && (
                <div className="flex justify-between items-center mt-2">
                  <Badge variant="secondary" className="text-xs bg-gray-100 text-gray-600">
                    {messages.length} messages
                  </Badge>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearConversation}
                    className="text-xs text-gray-500 hover:text-red-600 hover:bg-red-50 transition-colors"
                  >
                    <Trash2 className="w-3 h-3 mr-1" />
                    Clear
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        )}
      </Card>
    </div>
  );
};