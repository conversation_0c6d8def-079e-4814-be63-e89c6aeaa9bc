import React, { useState, useEffect } from 'react';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { 
  Bot, 
  MessageSquare, 
  Lightbulb, 
  BookOpen, 
  HelpCircle,
  Sparkles
} from 'lucide-react';
import { useAIAssistant, useAIQuickActions } from './AIAssistantProvider';
import { cn } from '../../lib/utils';

// AI Assistant Integration Components for different contexts

// Course Page Integration
interface CourseAIIntegrationProps {
  courseId: string;
  courseTitle: string;
  currentLesson?: {
    id: string;
    title: string;
    content?: string;
  };
  className?: string;
}

export const CourseAIIntegration: React.FC<CourseAIIntegrationProps> = ({
  courseId,
  courseTitle,
  currentLesson,
  className
}) => {
  const { open, setContext } = useAIAssistant();
  const { explainConcept, getPracticeQuestions, getStudyHelp } = useAIQuickActions();

  const handleAskAboutCourse = () => {
    setContext({
      courseId,
      pageTitle: courseTitle,
      pageUrl: window.location.pathname,
    });
    open();
  };

  const handleAskAboutLesson = () => {
    if (currentLesson) {
      setContext({
        courseId,
        lessonId: currentLesson.id,
        pageTitle: `${courseTitle} - ${currentLesson.title}`,
        pageUrl: window.location.pathname,
        selectedText: currentLesson.content?.slice(0, 500), // First 500 chars as context
      });
      open();
    }
  };

  return (
    <div className={cn('flex flex-wrap gap-2', className)}>
      <Button
        variant="outline"
        size="sm"
        onClick={handleAskAboutCourse}
        className="text-blue-600 border-blue-200 hover:bg-blue-50"
      >
        <Bot className="w-4 h-4 mr-2" />
        Ask AI about this course
      </Button>
      
      {currentLesson && (
        <Button
          variant="outline"
          size="sm"
          onClick={handleAskAboutLesson}
          className="text-purple-600 border-purple-200 hover:bg-purple-50"
        >
          <MessageSquare className="w-4 h-4 mr-2" />
          Ask about this lesson
        </Button>
      )}
      
      <Button
        variant="outline"
        size="sm"
        onClick={() => getPracticeQuestions(courseTitle, { courseId })}
        className="text-green-600 border-green-200 hover:bg-green-50"
      >
        <BookOpen className="w-4 h-4 mr-2" />
        Get practice questions
      </Button>
    </div>
  );
};

// Text Selection AI Menu
interface TextSelectionAIMenuProps {
  selectedText: string;
  onClose: () => void;
  position: { x: number; y: number };
}

export const TextSelectionAIMenu: React.FC<TextSelectionAIMenuProps> = ({
  selectedText,
  onClose,
  position
}) => {
  const { explainConcept, getPracticeQuestions, explainCode } = useAIQuickActions();
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      if (!target.closest('[data-ai-menu]')) {
        setIsVisible(false);
        setTimeout(onClose, 150);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onClose]);

  const handleAction = (action: () => void) => {
    action();
    setIsVisible(false);
    setTimeout(onClose, 150);
  };

  if (!isVisible) return null;

  // Detect if selected text looks like code
  const looksLikeCode = /[{}();=<>]/.test(selectedText) || selectedText.includes('function') || selectedText.includes('class');

  return (
    <div
      data-ai-menu
      className={cn(
        'fixed z-50 bg-white rounded-lg shadow-xl border border-gray-200 py-2 min-w-48',
        'animate-in fade-in-0 zoom-in-95 duration-150'
      )}
      style={{ 
        left: Math.min(position.x, window.innerWidth - 200), 
        top: Math.min(position.y, window.innerHeight - 200) 
      }}
    >
      <div className="px-3 py-1 text-xs text-gray-500 border-b border-gray-100 mb-1">
        AI Assistant
      </div>
      
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleAction(() => explainConcept(selectedText))}
        className="w-full justify-start px-3 py-2 text-sm hover:bg-blue-50"
      >
        <Lightbulb className="w-4 h-4 mr-2 text-blue-500" />
        Explain this concept
      </Button>
      
      {looksLikeCode && (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleAction(() => explainCode(selectedText))}
          className="w-full justify-start px-3 py-2 text-sm hover:bg-purple-50"
        >
          <Bot className="w-4 h-4 mr-2 text-purple-500" />
          Explain this code
        </Button>
      )}
      
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleAction(() => getPracticeQuestions(selectedText))}
        className="w-full justify-start px-3 py-2 text-sm hover:bg-green-50"
      >
        <BookOpen className="w-4 h-4 mr-2 text-green-500" />
        Get practice questions
      </Button>
    </div>
  );
};

// AI Study Helper Widget
interface AIStudyHelperProps {
  subject?: string;
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  className?: string;
}

export const AIStudyHelper: React.FC<AIStudyHelperProps> = ({
  subject,
  difficulty,
  className
}) => {
  const { getStudyHelp, explainConcept, getPracticeQuestions } = useAIQuickActions();

  const studyActions = [
    {
      label: 'Get study tips',
      icon: Sparkles,
      action: () => getStudyHelp(subject || 'this topic'),
      color: 'text-yellow-600 bg-yellow-50 border-yellow-200'
    },
    {
      label: 'Explain concepts',
      icon: Lightbulb,
      action: () => explainConcept(subject || 'key concepts'),
      color: 'text-blue-600 bg-blue-50 border-blue-200'
    },
    {
      label: 'Practice questions',
      icon: BookOpen,
      action: () => getPracticeQuestions(subject || 'this topic'),
      color: 'text-green-600 bg-green-50 border-green-200'
    }
  ];

  return (
    <div className={cn('bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4 border border-blue-100', className)}>
      <div className="flex items-center mb-3">
        <Bot className="w-5 h-5 text-blue-600 mr-2" />
        <h3 className="font-semibold text-gray-900">AI Study Assistant</h3>
        {difficulty && (
          <Badge variant="secondary" className="ml-2 text-xs">
            {difficulty}
          </Badge>
        )}
      </div>
      
      <p className="text-sm text-gray-600 mb-3">
        Get personalized help with your studies using AI
      </p>
      
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
        {studyActions.map((action, index) => {
          const Icon = action.icon;
          return (
            <Button
              key={index}
              variant="outline"
              size="sm"
              onClick={action.action}
              className={cn('justify-start h-auto py-2', action.color)}
            >
              <Icon className="w-4 h-4 mr-2" />
              <span className="text-xs">{action.label}</span>
            </Button>
          );
        })}
      </div>
    </div>
  );
};

// AI Floating Action Button (alternative to the main floating button)
interface AIFloatingActionProps {
  context?: {
    courseId?: string;
    lessonId?: string;
    subject?: string;
  };
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const AIFloatingAction: React.FC<AIFloatingActionProps> = ({
  context,
  position = 'bottom-right',
  size = 'md',
  className
}) => {
  const { open, setContext } = useAIAssistant();
  const [isHovered, setIsHovered] = useState(false);

  const handleClick = () => {
    if (context) {
      setContext({
        ...context,
        pageUrl: window.location.pathname,
        pageTitle: document.title,
      });
    }
    open();
  };

  const positionClasses = {
    'bottom-right': 'fixed bottom-4 right-4',
    'bottom-left': 'fixed bottom-4 left-4',
    'top-right': 'fixed top-4 right-4',
    'top-left': 'fixed top-4 left-4',
  };

  const sizeClasses = {
    sm: 'w-10 h-10',
    md: 'w-12 h-12',
    lg: 'w-14 h-14',
  };

  return (
    <div className={cn(positionClasses[position], 'z-30', className)}>
      <Button
        onClick={handleClick}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        className={cn(
          'rounded-full shadow-lg transition-all duration-300',
          'bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700',
          'border-2 border-white/20 hover:border-white/40',
          'transform hover:scale-110 active:scale-95',
          sizeClasses[size]
        )}
        size="lg"
      >
        <Bot className={cn(
          'text-white transition-all duration-300',
          size === 'sm' && 'w-4 h-4',
          size === 'md' && 'w-5 h-5',
          size === 'lg' && 'w-6 h-6'
        )} />
        
        {/* Pulse Animation */}
        <div className={cn(
          'absolute inset-0 rounded-full bg-white/20 animate-ping',
          !isHovered && 'hidden'
        )} />
      </Button>
      
      {/* Tooltip */}
      {isHovered && (
        <div className={cn(
          'absolute mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded shadow-lg whitespace-nowrap',
          'animate-in fade-in-0 slide-in-from-bottom-1 duration-200',
          position.includes('right') ? 'bottom-full right-0' : 'bottom-full left-0'
        )}>
          Ask AI Assistant
        </div>
      )}
    </div>
  );
};

// Hook for text selection AI integration
export const useTextSelectionAI = () => {
  const [selectedText, setSelectedText] = useState('');
  const [selectionPosition, setSelectionPosition] = useState<{ x: number; y: number } | null>(null);
  const [showMenu, setShowMenu] = useState(false);

  useEffect(() => {
    const handleTextSelection = () => {
      const selection = window.getSelection();
      const text = selection?.toString().trim();
      
      if (text && text.length > 10) { // Only show for meaningful selections
        const range = selection?.getRangeAt(0);
        const rect = range?.getBoundingClientRect();
        
        if (rect) {
          setSelectedText(text);
          setSelectionPosition({
            x: rect.right + window.scrollX,
            y: rect.bottom + window.scrollY + 5
          });
          setShowMenu(true);
        }
      } else {
        setShowMenu(false);
      }
    };

    const handleClickOutside = () => {
      setShowMenu(false);
    };

    document.addEventListener('mouseup', handleTextSelection);
    document.addEventListener('mousedown', handleClickOutside);
    
    return () => {
      document.removeEventListener('mouseup', handleTextSelection);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return {
    selectedText,
    selectionPosition,
    showMenu,
    hideMenu: () => setShowMenu(false)
  };
};
