import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { 
  Video, 
  Image, 
  FileText, 
  File, 
  Folder, 
  Eye, 
  Download, 
  MoreHorizontal 
} from 'lucide-react';
// Define types locally since we removed mock data
export interface ContentItem {
  id: string;
  name: string;
  type: 'video' | 'image' | 'document' | 'folder';
  size: string;
  lastModified: string;
  thumbnail?: string;
  status?: 'ready' | 'processed' | 'processing' | 'uploading' | 'error';
  uploadProgress?: number;
}

export type StatusType = 'ready' | 'processed' | 'processing' | 'uploading' | 'error';

// Brand colors for consistent styling
export const brandColors = {
  primary: '#dc3545',
  success: '#28a745',
  warning: '#ffc107',
  info: '#17a2b8'
};

// Status configuration for badges
export const statusConfig = {
  ready: { classUsername: 'bg-green-100 text-green-800', text: 'Ready' },
  processed: { classUsername: 'bg-green-100 text-green-800', text: 'Processed' },
  processing: { classUsername: 'bg-blue-100 text-blue-800', text: 'Processing' },
  uploading: { classUsername: 'bg-yellow-100 text-yellow-800', text: 'Uploading' },
  error: { classUsername: 'bg-red-100 text-red-800', text: 'Error' }
} as const;

interface FileCardProps {
  item: ContentItem;
}

const FileCard: React.FC<FileCardProps> = ({ item }) => {
  const getFileIcon = (type: string) => {
    switch (type) {
      case 'video':
        return <Video className="w-6 h-6" style={{ color: brandColors.primary }} />;
      case 'image':
        return <Image className="w-6 h-6" style={{ color: brandColors.success }} />;
      case 'document':
        return <FileText className="w-6 h-6" style={{ color: brandColors.warning }} />;
      case 'folder':
        return <Folder className="w-6 h-6" style={{ color: brandColors.info }} />;
      default:
        return <File className="w-6 h-6" style={{ color: '#666' }} />;
    }
  };

  const getStatusBadge = (status: StatusType) => {
    const config = statusConfig[status] || statusConfig.ready;
    return <Badge className={config.classUsername}>{config.text}</Badge>;
  };

  return (
    <Card className="hover:shadow-md transition-shadow cursor-pointer">
      <div className="relative">
        {item.thumbnail ? (
          <img
            alt={item.name}
            src={item.thumbnail}
            className="w-full h-32 object-cover rounded-t-lg"
          />
        ) : (
          <div className="w-full h-32 flex items-center justify-center bg-gray-50 rounded-t-lg">
            {getFileIcon(item.type)}
          </div>
        )}
      </div>
      <CardContent className="p-3">
        <div className="space-y-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <h4 className="font-nunito-medium text-sm truncate">
                  {item.name}
                </h4>
              </TooltipTrigger>
              <TooltipContent>{item.name}</TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <div className="space-y-1">
            <p className="text-xs text-gray-500">
              {item.size} • {item.lastModified}
            </p>
            {item.status && getStatusBadge(item.status)}
          </div>
          <div className="flex justify-end space-x-1 pt-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button size="sm" variant="ghost">
                    <Eye className="w-3 h-3" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>View</TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button size="sm" variant="ghost">
                    <Download className="w-3 h-3" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Download</TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button size="sm" variant="ghost">
                  <MoreHorizontal className="w-3 h-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem>Edit</DropdownMenuItem>
                <DropdownMenuItem>Share</DropdownMenuItem>
                <DropdownMenuItem className="text-red-600">Delete</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default FileCard;
