import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { 
  Video, 
  Image, 
  FileText, 
  File, 
  Folder 
} from 'lucide-react';
// Define types locally since we removed mock data
export interface ContentItem {
  id: string;
  name: string;
  type: 'video' | 'image' | 'document' | 'folder';
  size: string;
  lastModified: string;
  thumbnail?: string;
  status?: 'ready' | 'processed' | 'processing' | 'uploading' | 'error';
  uploadProgress?: number;
}

// Brand colors for consistent styling
export const brandColors = {
  primary: '#dc3545',
  success: '#28a745',
  warning: '#ffc107',
  info: '#17a2b8'
};

interface RecentUploadsCardProps {
  items: ContentItem[];
}

const RecentUploadsCard: React.FC<RecentUploadsCardProps> = ({ items }) => {
  const getFileIcon = (type: string) => {
    switch (type) {
      case 'video':
        return <Video className="w-6 h-6" style={{ color: brandColors.primary }} />;
      case 'image':
        return <Image className="w-6 h-6" style={{ color: brandColors.success }} />;
      case 'document':
        return <FileText className="w-6 h-6" style={{ color: brandColors.warning }} />;
      case 'folder':
        return <Folder className="w-6 h-6" style={{ color: brandColors.info }} />;
      default:
        return <File className="w-6 h-6" style={{ color: '#666' }} />;
    }
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm">Recent Uploads</CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {items.map((item) => (
          <div key={item.id} className="flex items-start space-x-3 p-2 rounded-lg hover:bg-gray-50">
            <div className="flex-shrink-0">
              {getFileIcon(item.type)}
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-nunito-medium truncate">
                {item.name}
              </p>
              <div className="space-y-1">
                <p className="text-xs text-gray-500">
                  {item.size} • {item.status}
                </p>
                {item.uploadProgress !== undefined && (
                  <Progress value={item.uploadProgress} className="h-1" />
                )}
              </div>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
};

export default RecentUploadsCard;
