import React from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Cloud } from 'lucide-react';

interface UploadModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const UploadModal: React.FC<UploadModalProps> = ({ isOpen, onClose }) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Upload Files</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div
            className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors cursor-pointer"
            onDrop={(e) => {
              e.preventDefault();
              // Handle file drop
            }}
            onDragOver={(e) => e.preventDefault()}
          >
            <Cloud className="w-12 h-12 mx-auto mb-4 text-gray-400" />
            <p className="text-lg font-nunito-medium text-gray-700 mb-2">
              Drop files here or click to browse
            </p>
            <p className="text-sm text-gray-500">
              Supports: MP4, PDF, DOCX, PNG, JPG (Max 100MB per file)
            </p>
            <input
              type="file"
              multiple
              className="hidden"
              id="file-upload"
            />
            <label htmlFor="file-upload" className="cursor-pointer">
              <Button className="mt-4">Select Files</Button>
            </label>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default UploadModal;
