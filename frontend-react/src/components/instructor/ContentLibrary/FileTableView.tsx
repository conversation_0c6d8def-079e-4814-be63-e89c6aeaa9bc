import React from 'react';
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { 
  Video, 
  Image, 
  FileText, 
  File, 
  Folder, 
  MoreHorizontal 
} from 'lucide-react';
// Define types locally since we removed mock data
export interface ContentItem {
  id: string;
  name: string;
  type: 'video' | 'image' | 'document' | 'folder';
  size: string;
  lastModified: string;
  thumbnail?: string;
  status?: 'ready' | 'processed' | 'processing' | 'uploading' | 'error';
  uploadProgress?: number;
}

export type StatusType = 'ready' | 'processed' | 'processing' | 'uploading' | 'error';

// Brand colors for consistent styling
export const brandColors = {
  primary: '#dc3545',
  success: '#28a745',
  warning: '#ffc107',
  info: '#17a2b8'
};

// Status configuration for badges
export const statusConfig = {
  ready: { classUsername: 'bg-green-100 text-green-800', text: 'Ready' },
  processed: { classUsername: 'bg-green-100 text-green-800', text: 'Processed' },
  processing: { classUsername: 'bg-blue-100 text-blue-800', text: 'Processing' },
  uploading: { classUsername: 'bg-yellow-100 text-yellow-800', text: 'Uploading' },
  error: { classUsername: 'bg-red-100 text-red-800', text: 'Error' }
} as const;

interface FileTableViewProps {
  items: ContentItem[];
}

const FileTableView: React.FC<FileTableViewProps> = ({ items }) => {
  const getFileIcon = (type: string) => {
    switch (type) {
      case 'video':
        return <Video className="w-6 h-6" style={{ color: brandColors.primary }} />;
      case 'image':
        return <Image className="w-6 h-6" style={{ color: brandColors.success }} />;
      case 'document':
        return <FileText className="w-6 h-6" style={{ color: brandColors.warning }} />;
      case 'folder':
        return <Folder className="w-6 h-6" style={{ color: brandColors.info }} />;
      default:
        return <File className="w-6 h-6" style={{ color: '#666' }} />;
    }
  };

  const getStatusBadge = (status: StatusType) => {
    const config = statusConfig[status] || statusConfig.ready;
    return <Badge className={config.classUsername}>{config.text}</Badge>;
  };

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Name</TableHead>
          <TableHead>Type</TableHead>
          <TableHead>Size</TableHead>
          <TableHead>Status</TableHead>
          <TableHead>Modified</TableHead>
          <TableHead>Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {items.map((item) => (
          <TableRow key={item.id}>
            <TableCell>
              <div className="flex items-center space-x-2">
                {getFileIcon(item.type)}
                <span>{item.name}</span>
              </div>
            </TableCell>
            <TableCell>
              <Badge variant="outline">{item.type}</Badge>
            </TableCell>
            <TableCell>{item.size}</TableCell>
            <TableCell>
              {item.status && getStatusBadge(item.status)}
            </TableCell>
            <TableCell>{item.lastModified}</TableCell>
            <TableCell>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button size="sm" variant="ghost">
                    <MoreHorizontal className="w-4 h-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem>Edit</DropdownMenuItem>
                  <DropdownMenuItem>Share</DropdownMenuItem>
                  <DropdownMenuItem className="text-red-600">Delete</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};

export default FileTableView;
