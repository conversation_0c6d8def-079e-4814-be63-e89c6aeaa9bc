import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useToast } from '../../hooks/use-toast';
import courseService from '../../services/course.service';
import categoryService from '../../services/category.service';
import type { Course, CourseFormData, ContentType, CourseFormatOption, CourseLanguageOption, CourseLevelOption } from '../../models/course.model';
import type { Category } from '../../models/category.model';
import type { User } from '../../models/user.model';

// shadcn/ui components
import { Button } from '../ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Progress } from '../ui/progress';
// Icons
import { Save, Send, FileText, Image, DollarSign, MessageSquare, FolderOpen } from 'lucide-react';

// Tab components
import CourseInformationTab from './tabs/CourseInformationTab';
import CoursePresentationTab from './tabs/CoursePresentationTab';
import CoursePricingTab from './tabs/CoursePricingTab';
import CourseMessagingTab from './tabs/CourseMessagingTab';
import CourseStructureTab from './tabs/CourseStructureTab';

interface ComprehensiveCourseCreatorProps {
  courseId?: string; // For editing existing courses
  onSave?: (course: Course) => void;
  onCancel?: () => void;
}

const ComprehensiveCourseCreator: React.FC<ComprehensiveCourseCreatorProps> = ({
  courseId,
  onSave,
  onCancel
}) => {
  const navigate = useNavigate();
  const { authState } = useAuth();
  const { user } = authState;
  const { toast } = useToast();

  // State management
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('information');
  const [categories, setCategories] = useState<Category[]>([]);
  const [errors, setErrors] = useState<Record<string, string[]>>({});

  // Course form data
  const [courseData, setCourseData] = useState<CourseFormData>({
    Title: '',
    Format: 2, // Default to VIDEO
    Resume: '',
    Keywords: [],
    Language: 'en',
    Level: [1], // Default to beginner
    Categories: [],
    Goals: [],
    Prerequisites: [],
    Free: false,
    Price: 0,
    NewPrice: 0,
    Currency: 'CAD',
    Message: '',
    Congratulation: ''
  });

  // Tab configuration
  const tabs = [
    {
      id: 'information',
      title: 'Course Information',
      description: 'Basic course details and learning objectives',
      icon: FileText,
      required: true
    },
    {
      id: 'presentation',
      title: 'Presentation',
      description: 'Cover image and presentation video',
      icon: Image,
      required: false
    },
    {
      id: 'pricing',
      title: 'Pricing',
      description: 'Course pricing and promotional options',
      icon: DollarSign,
      required: true
    },
    {
      id: 'messaging',
      title: 'Course Messages',
      description: 'Welcome and congratulation messages',
      icon: MessageSquare,
      required: false
    },
    {
      id: 'structure',
      title: 'Course Structure',
      description: 'Sections and content organization',
      icon: FolderOpen,
      required: false
    }
  ];

  // Validation state
  const [tabValidation, setTabValidation] = useState<Record<string, boolean>>({
    information: false,
    presentation: true, // Optional
    pricing: false,
    messaging: true, // Optional
    structure: true // Optional for initial creation
  });

  // Load initial data
  useEffect(() => {
    loadCategories();
    if (courseId) {
      loadCourse(courseId);
    }
  }, [courseId]);

  const loadCategories = async () => {
    try {
      const categoriesData = await categoryService.getAll();
      setCategories(categoriesData);
    } catch (error) {
      console.error('Failed to load categories:', error);
      toast({
        title: "Error",
        description: "Failed to load categories",
        variant: "destructive"
      });
    }
  };

  const loadCourse = async (id: string) => {
    try {
      setLoading(true);
      const course = await courseService.getBySlug(id);
      
      // Convert course to form data
      setCourseData({
        Title: course.Title || '',
        Format: course.Format || 2,
        Resume: course.Resume || '',
        Keywords: course.Keywords || [],
        Language: course.Language || 'en',
        Level: course.Level || [1],
        Categories: course.Categories || [],
        Goals: course.Goals || [],
        Prerequisites: course.Prerequisites || [],
        Free: course.Free || false,
        Price: course.Price || 0,
        NewPrice: course.NewPrice || 0,
        Currency: course.Currency || 'CAD',
        Message: course.Message || '',
        Congratulation: course.Congratulation || ''
      });

    } catch (error) {
      console.error('Failed to load course:', error);
      toast({
        title: "Error",
        description: "Failed to load course",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // Validation functions
  const validateInformation = (): boolean => {
    const errors: string[] = [];
    
    if (!courseData.Title?.trim()) errors.push('Title is required');
    if (!courseData.Resume?.trim()) errors.push('Description is required');
    if (!courseData.Keywords?.length) errors.push('At least one keyword is required');
    if (!courseData.Categories?.length) errors.push('At least one category is required');
    if (!courseData.Goals?.length) errors.push('At least one learning goal is required');
    if (!courseData.Prerequisites?.length) errors.push('At least one prerequisite is required');
    
    setErrors(prev => ({ ...prev, information: errors }));
    return errors.length === 0;
  };

  const validatePricing = (): boolean => {
    const errors: string[] = [];
    
    if (!courseData.Free && (!courseData.Price || courseData.Price < 7)) {
      errors.push('Price must be at least $7 for paid courses');
    }
    
    setErrors(prev => ({ ...prev, pricing: errors }));
    return errors.length === 0;
  };

  // Update validation when data changes
  useEffect(() => {
    setTabValidation(prev => ({
      ...prev,
      information: validateInformation(),
      pricing: validatePricing()
    }));
  }, [courseData]);

  // Save course
  const handleSave = async (publish = false) => {
    try {
      setSaving(true);

      // Validate all required tabs
      const isValid = tabValidation.information && tabValidation.pricing;
      if (!isValid) {
        toast({
          title: "Validation Error",
          description: "Please complete all required fields",
          variant: "destructive"
        });
        return;
      }

      // Prepare course data for API
      const coursePayload: Partial<Course> = {
        Title: courseData.Title,
        Resume: courseData.Resume,
        Keywords: courseData.Keywords,
        Format: courseData.Format,
        Language: courseData.Language,
        Level: courseData.Level,
        Categories: courseData.Categories,
        Goals: courseData.Goals,
        Prerequisites: courseData.Prerequisites,
        Free: courseData.Free,
        Price: courseData.Free ? 0 : courseData.Price,
        NewPrice: courseData.Free ? 0 : courseData.NewPrice,
        Currency: courseData.Currency,
        Message: courseData.Message,
        Congratulation: courseData.Congratulation,
        Published: publish,
        Archived: false,
        CreatedBy: user ? { Id: user.Id } : undefined
      };

      let result: Course;
      if (courseId) {
        // Update existing course
        result = await courseService.edit({ ...coursePayload, Slug: courseId } as Course);
      } else {
        // Create new course
        result = await courseService.add(coursePayload as Course);
      }

      toast({
        title: "Success",
        description: `Course ${courseId ? 'updated' : 'created'} successfully!`
      });

      if (onSave) {
        onSave(result);
      } else {
        navigate(`/instructor/courses/${result.Slug}/structure`);
      }

    } catch (error) {
      console.error('Failed to save course:', error);
      toast({
        title: "Error",
        description: "Failed to save course. Please try again.",
        variant: "destructive"
      });
    } finally {
      setSaving(false);
    }
  };

  // Calculate progress
  const completedTabs = Object.values(tabValidation).filter(Boolean).length;
  const totalTabs = Object.keys(tabValidation).length;
  const progress = (completedTabs / totalTabs) * 100;

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading course...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      <div className="max-w-7xl mx-auto p-4 sm:p-6 lg:p-8 space-y-6">
        {/* Header */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
          <div className="bg-gradient-to-r from-red-600 to-red-700 px-6 py-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              <div className="flex-1">
                <h1 className="text-2xl sm:text-3xl font-bold text-white">
                  {courseId ? 'Edit Course' : 'Create New Course'}
                </h1>
                <p className="text-red-100 mt-2 text-sm sm:text-base">
                  {courseId ? 'Update your course information' : 'Build your course step by step'}
                </p>
              </div>
              <div className="flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-3">
                <Button
                  variant="outline"
                  onClick={onCancel || (() => navigate('/instructor/courses'))}
                  className="w-full sm:w-auto bg-white/10 border-white/20 text-white hover:bg-white/20 hover:text-white"
                >
                  Cancel
                </Button>
                <Button
                  onClick={() => handleSave(false)}
                  disabled={saving || !tabValidation.information || !tabValidation.pricing}
                  className="w-full sm:w-auto bg-white text-red-600 hover:bg-gray-50 border-0 font-semibold"
                >
                  <Save className="w-4 h-4 mr-2" />
                  {saving ? 'Saving...' : 'Save Draft'}
                </Button>
                <Button
                  onClick={() => handleSave(true)}
                  disabled={saving || !tabValidation.information || !tabValidation.pricing}
                  className="bg-yellow-500 hover:bg-yellow-600 text-gray-900 font-semibold w-full sm:w-auto border-0 shadow-md"
                >
                  <Send className="w-4 h-4 mr-2" />
                  {saving ? 'Publishing...' : 'Save & Publish'}
                </Button>
              </div>
            </div>
          </div>

          {/* Progress */}
          <div className="px-6 py-4 bg-gray-50 border-t border-gray-100">
            <div className="space-y-3">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-700 font-medium">Course Completion Progress</span>
                <span className="font-bold text-red-600">{Math.round(progress)}%</span>
              </div>
              <div className="relative">
                <Progress value={progress} className="h-3 bg-gray-200" />
                <div
                  className="absolute top-0 left-0 h-3 bg-gradient-to-r from-red-500 to-red-600 rounded-full transition-all duration-500 ease-out"
                  style={{ width: `${progress}%` }}
                />
              </div>
            </div>
          </div>
        </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        {/* Tab Navigation */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
          <TabsList className="grid w-full grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 h-auto p-0 gap-0 bg-gray-50">
            {tabs.map((tab, index) => {
              const isValid = tabValidation[tab.id];
              const IconComponent = tab.icon;

              return (
                <TabsTrigger
                  key={tab.id}
                  value={tab.id}
                  className={`
                    flex flex-col items-center p-3 sm:p-4 lg:p-5 space-y-2
                    min-h-[80px] sm:min-h-[90px] relative transition-all duration-200
                    data-[state=active]:bg-white data-[state=active]:shadow-md
                    data-[state=active]:border-b-4 data-[state=active]:border-b-red-600
                    hover:bg-white/50 border-r border-gray-200 last:border-r-0
                    ${index === 0 ? 'rounded-tl-xl' : ''}
                    ${index === tabs.length - 1 ? 'rounded-tr-xl' : ''}
                  `}
                >
                  <div className="flex items-center justify-center">
                    <IconComponent
                      className={`w-5 h-5 sm:w-6 sm:h-6 ${
                        isValid ? 'text-green-600' :
                        tab.required ? 'text-red-600' : 'text-gray-500'
                      }`}
                    />
                  </div>
                  <div className="text-center">
                    <div className="font-semibold text-xs sm:text-sm leading-tight text-gray-900 flex items-center justify-center space-x-1">
                      <span>{tab.title}</span>
                      {tab.required && !isValid && (
                        <span className="text-red-500 text-xs">*</span>
                      )}
                    </div>
                  </div>
                </TabsTrigger>
              );
            })}
          </TabsList>
        </div>

        {/* Tab Content */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 min-h-[500px] sm:min-h-[600px] overflow-hidden transition-all duration-300">
          <TabsContent value="information" className="space-y-6">
            <CourseInformationTab
              data={courseData}
              onChange={setCourseData}
              categories={categories}
              errors={errors.information || []}
            />
          </TabsContent>

          <TabsContent value="presentation" className="space-y-6">
            <CoursePresentationTab
              data={courseData}
              onChange={setCourseData}
              errors={errors.presentation || []}
            />
          </TabsContent>

          <TabsContent value="pricing" className="space-y-6">
            <CoursePricingTab
              data={courseData}
              onChange={setCourseData}
              errors={errors.pricing || []}
            />
          </TabsContent>

          <TabsContent value="messaging" className="space-y-6">
            <CourseMessagingTab
              data={courseData}
              onChange={setCourseData}
              errors={errors.messaging || []}
            />
          </TabsContent>

          <TabsContent value="structure" className="space-y-6">
            <CourseStructureTab
              courseId={courseId}
              data={courseData}
              onChange={setCourseData}
              errors={errors.structure || []}
            />
          </TabsContent>
        </div>
      </Tabs>
      </div>
    </div>
  );
};

export default ComprehensiveCourseCreator;
