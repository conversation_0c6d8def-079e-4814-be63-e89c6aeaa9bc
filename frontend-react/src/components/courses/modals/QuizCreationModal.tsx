import React, { useState } from 'react';
import { X, HelpCircle } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '../../ui/dialog';
import { Button } from '../../ui/button';
import { ContentType } from '../../../models';
import type { CourseSection, CourseContent, Course } from '../../../models';
import QuizContentEditor from '../content-editors/QuizContentEditor';
import { courseContentService } from '../../../services/courseContent.service';
import courseService from '../../../services/course.service';
import { useToast } from '../../../hooks/use-toast';

interface QuizCreationModalProps {
  isOpen: boolean;
  onClose: () => void;
  section: CourseSection;
  course: Course;
  onQuizCreated: (updatedCourse: Course) => void;
}

const QuizCreationModal: React.FC<QuizCreationModalProps> = ({
  isOpen,
  onClose,
  section,
  course,
  onQuizCreated
}) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);

  const handleQuizSave = async (quizData: Partial<CourseContent>) => {
    try {
      setLoading(true);

      // Validation
      if (!quizData.Title?.trim()) {
        toast({
          title: "Validation Error",
          description: "Quiz title is required",
          variant: "destructive"
        });
        return;
      }

      if (!section.Id) {
        toast({
          title: "Validation Error",
          description: "Section information is missing",
          variant: "destructive"
        });
        return;
      }

      // Prepare quiz data for creation
      const createData = {
        ...quizData,
        Type: ContentType.QUIZ,
        SectionId: section.Id,
        Section: { Id: section.Id },
        Position: (section.Contents?.length || 0) + 1,
        IsActive: true,
        IsPublished: false,
        IsFree: false,
        // Default quiz settings
        TotalTime: quizData.TotalTime || 30, // 30 minutes default
        TypeOfTest: quizData.TypeOfTest || 1, // Default quiz type
        Questions: [] // Questions will be managed separately
      };

      console.log('Creating quiz:', createData);
      await courseContentService.createContent(createData as any);

      // Refresh course data
      const updatedCourse = await courseService.getBySlug(course.Slug!);
      onQuizCreated(updatedCourse);

      toast({
        title: "Success",
        description: "Quiz created successfully"
      });

      onClose();
    } catch (error) {
      console.error('Error creating quiz:', error);

      // Comprehensive error handling
      let errorMessage = "Failed to create quiz";

      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'object' && error !== null) {
        const apiError = error as any;
        if (apiError.response?.data?.message) {
          errorMessage = apiError.response.data.message;
        } else if (apiError.message) {
          errorMessage = apiError.message;
        }
      }

      toast({
        title: "Quiz Creation Failed",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl font-semibold">
              <div className="flex items-center space-x-2">
                <HelpCircle className="h-6 w-6 text-purple-600" />
                <span>Create Quiz</span>
              </div>
            </DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCancel}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <p className="text-sm text-gray-600 mt-2">
            Create an interactive quiz for section "{section.Title}"
          </p>
        </DialogHeader>

        <div className="mt-4">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
              <span className="ml-3 text-gray-600">Creating quiz...</span>
            </div>
          ) : (
            <QuizContentEditor
              content={undefined}
              onSave={handleQuizSave}
              onCancel={handleCancel}
            />
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default QuizCreationModal;
