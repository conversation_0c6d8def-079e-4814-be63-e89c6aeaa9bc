import React, { useState } from 'react';
import { X, BookOpen, FileText, Book } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '../../ui/dialog';
import { Button } from '../../ui/button';
import { Card, CardContent } from '../../ui/card';
import { ContentType } from '../../../models';
import type { CourseSection, CourseContent, Course } from '../../../models';
import VideoContentEditor from '../content-editors/VideoContentEditor';
import PDFContentEditor from '../content-editors/PDFContentEditor';
import EbookContentEditor from '../content-editors/EbookContentEditor';
import { courseContentService } from '../../../services/courseContent.service';
import courseService from '../../../services/course.service';
import { useToast } from '../../../hooks/use-toast';

interface ContentCreationModalProps {
  isOpen: boolean;
  onClose: () => void;
  section: CourseSection;
  course: Course;
  onContentCreated: (updatedCourse: Course) => void;
}

const ContentCreationModal: React.FC<ContentCreationModalProps> = ({
  isOpen,
  onClose,
  section,
  course,
  onContentCreated
}) => {
  const { toast } = useToast();
  const [selectedContentType, setSelectedContentType] = useState<ContentType | null>(null);
  const [loading, setLoading] = useState(false);

  const contentTypeOptions = [
    {
      value: ContentType.VIDEO,
      label: 'Video Lesson',
      icon: <BookOpen className="h-6 w-6" />,
      description: 'Upload video content or embed from YouTube/Vimeo',
      color: 'bg-blue-50 border-blue-200 hover:bg-blue-100'
    },
    {
      value: ContentType.PDF,
      label: 'PDF Document',
      icon: <FileText className="h-6 w-6" />,
      description: 'Upload PDF files for reading materials',
      color: 'bg-red-50 border-red-200 hover:bg-red-100'
    },
    {
      value: ContentType.EBOOK,
      label: 'E-book Content',
      icon: <Book className="h-6 w-6" />,
      description: 'Rich text content for reading',
      color: 'bg-green-50 border-green-200 hover:bg-green-100'
    }
  ];

  const handleContentSave = async (contentData: Partial<CourseContent>) => {
    try {
      setLoading(true);

      // Validation
      if (!contentData.Title?.trim()) {
        toast({
          title: "Validation Error",
          description: "Content title is required",
          variant: "destructive"
        });
        return;
      }

      if (!section.Id) {
        toast({
          title: "Validation Error",
          description: "Section information is missing",
          variant: "destructive"
        });
        return;
      }

      // Prepare content data for creation
      const createData = {
        ...contentData,
        SectionId: section.Id,
        Section: { Id: section.Id },
        Position: (section.Contents?.length || 0) + 1,
        IsActive: true,
        IsPublished: false,
        IsFree: false
      };

      console.log('Creating content:', createData);
      await courseContentService.createContent(createData as any);

      // Show immediate success feedback
      toast({
        title: "Success",
        description: "Content created successfully"
      });

      // Refresh course data with loading state
      const updatedCourse = await courseService.getBySlug(course.Slug!);
      onContentCreated(updatedCourse);

      // Close modal and reset state
      setSelectedContentType(null);
      onClose();
    } catch (error) {
      console.error('Error creating content:', error);

      // Comprehensive error handling
      let errorMessage = "Failed to create content";

      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'object' && error !== null) {
        // Handle API error responses
        const apiError = error as any;
        if (apiError.response?.data?.message) {
          errorMessage = apiError.response.data.message;
        } else if (apiError.message) {
          errorMessage = apiError.message;
        }
      }

      toast({
        title: "Content Creation Failed",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setSelectedContentType(null);
    onClose();
  };

  const handleBackToTypeSelector = () => {
    setSelectedContentType(null);
  };

  const renderContentTypeSelector = () => (
    <div className="space-y-4">
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Choose Content Type
        </h3>
        <p className="text-sm text-gray-600">
          Select the type of content you want to add to "{section.Title}"
        </p>
      </div>

      <div className="grid gap-4">
        {contentTypeOptions.map((option) => (
          <Card
            key={option.value}
            className={`cursor-pointer transition-all duration-200 ${option.color}`}
            onClick={() => setSelectedContentType(option.value)}
          >
            <CardContent className="p-4">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 text-gray-700">
                  {option.icon}
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-gray-900 mb-1">
                    {option.label}
                  </h4>
                  <p className="text-sm text-gray-600">
                    {option.description}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );

  const renderContentEditor = () => {
    const commonProps = {
      content: undefined,
      onSave: handleContentSave,
      onCancel: handleBackToTypeSelector
    };

    switch (selectedContentType) {
      case ContentType.VIDEO:
        return <VideoContentEditor {...commonProps} />;
      case ContentType.PDF:
        return <PDFContentEditor {...commonProps} />;
      case ContentType.EBOOK:
        return <EbookContentEditor {...commonProps} />;
      default:
        return renderContentTypeSelector();
    }
  };

  const selectedOption = contentTypeOptions.find(opt => opt.value === selectedContentType);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl font-semibold">
              {selectedContentType ? (
                <div className="flex items-center space-x-2">
                  {selectedOption?.icon}
                  <span>Create {selectedOption?.label}</span>
                </div>
              ) : (
                'Create Course Content'
              )}
            </DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCancel}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        <div className="mt-4">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
              <span className="ml-3 text-gray-600">Creating content...</span>
            </div>
          ) : (
            renderContentEditor()
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ContentCreationModal;
