import React, { useState } from 'react';
import { X, FileText } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '../../ui/dialog';
import { Button } from '../../ui/button';
import { ContentType } from '../../../models';
import type { CourseSection, CourseContent, Course } from '../../../models';
import AssignmentContentEditor from '../content-editors/AssignmentContentEditor';
import { courseContentService } from '../../../services/courseContent.service';
import courseService from '../../../services/course.service';
import { useToast } from '../../../hooks/use-toast';

interface AssignmentCreationModalProps {
  isOpen: boolean;
  onClose: () => void;
  section: CourseSection;
  course: Course;
  onAssignmentCreated: (updatedCourse: Course) => void;
}

const AssignmentCreationModal: React.FC<AssignmentCreationModalProps> = ({
  isOpen,
  onClose,
  section,
  course,
  onAssignmentCreated
}) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);

  const handleAssignmentSave = async (assignmentData: Partial<CourseContent>) => {
    try {
      setLoading(true);

      // Validation
      if (!assignmentData.Title?.trim()) {
        toast({
          title: "Validation Error",
          description: "Assignment title is required",
          variant: "destructive"
        });
        return;
      }

      if (!assignmentData.Content?.trim()) {
        toast({
          title: "Validation Error",
          description: "Assignment instructions are required",
          variant: "destructive"
        });
        return;
      }

      if (!section.Id) {
        toast({
          title: "Validation Error",
          description: "Section information is missing",
          variant: "destructive"
        });
        return;
      }

      // Prepare assignment data for creation
      const createData = {
        ...assignmentData,
        Type: ContentType.ASSIGNMENT,
        SectionId: section.Id,
        Section: { Id: section.Id },
        Position: (section.Contents?.length || 0) + 1,
        IsActive: true,
        IsPublished: false,
        IsFree: false,
        // Default assignment settings
        TotalTime: assignmentData.TotalTime || 0, // No time limit by default
        Instructions: assignmentData.Content || '', // Instructions stored in Content field
        DueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now
      };

      console.log('Creating assignment:', createData);
      await courseContentService.createContent(createData as any);

      // Refresh course data
      const updatedCourse = await courseService.getBySlug(course.Slug!);
      onAssignmentCreated(updatedCourse);

      toast({
        title: "Success",
        description: "Assignment created successfully"
      });

      onClose();
    } catch (error) {
      console.error('Error creating assignment:', error);

      // Comprehensive error handling
      let errorMessage = "Failed to create assignment";

      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'object' && error !== null) {
        const apiError = error as any;
        if (apiError.response?.data?.message) {
          errorMessage = apiError.response.data.message;
        } else if (apiError.message) {
          errorMessage = apiError.message;
        }
      }

      toast({
        title: "Assignment Creation Failed",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl font-semibold">
              <div className="flex items-center space-x-2">
                <FileText className="h-6 w-6 text-orange-600" />
                <span>Create Assignment</span>
              </div>
            </DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCancel}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <p className="text-sm text-gray-600 mt-2">
            Create an assignment for section "{section.Title}"
          </p>
        </DialogHeader>

        <div className="mt-4">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600"></div>
              <span className="ml-3 text-gray-600">Creating assignment...</span>
            </div>
          ) : (
            <AssignmentContentEditor
              content={null}
              onSave={handleAssignmentSave}
              onCancel={handleCancel}
            />
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AssignmentCreationModal;
