import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Badge } from '../ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Separator } from '../ui/separator';
import {
  Upload,
  Image as ImageIcon,
  Video,
  Save,
  Eye,
  X,
  Plus,
  DollarSign,
  Clock,
  Users,
  Star,
  Globe,
  Tag,
  Loader2
} from 'lucide-react';
import { useToast } from '../../hooks/use-toast';
import { useMediaMutations } from '../../hooks/useMediaEnhanced';
import { environment } from '../../config/environment';
import type { Course, Media } from '../../models';

interface CoursePageEditorProps {
  course: Course;
  onSave: (updatedCourse: Partial<Course>) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
}

interface CourseFormData {
  title: string;
  subtitle: string;
  description: string;
  shortDescription: string;
  price: number;
  newPrice?: number;
  free: boolean;
  language: string;
  level: string;
  category: string;
  tags: string[];
  coverImage?: File;
  presentationVideo?: File;
  uploadedCoverImage?: Media;
  uploadedPresentationVideo?: Media;
  requirements: string[];
  whatYouWillLearn: string[];
  targetAudience: string[];
  estimatedDuration: number;
  maxStudents?: number;
}

const CoursePageEditor: React.FC<CoursePageEditorProps> = ({
  course,
  onSave,
  onCancel,
  loading = false
}) => {
  const { toast } = useToast();
  const { uploadFile } = useMediaMutations();

  // Helper function to construct proper media URLs
  const constructMediaUrl = (hashname: string): string => {
    if (!hashname) return '';

    // If already a complete URL, return as-is
    if (hashname.startsWith('http://') || hashname.startsWith('https://')) {
      return hashname;
    }

    // Remove leading slash if present to avoid double slashes
    const cleanHashname = hashname.startsWith('/') ? hashname.substring(1) : hashname;

    // Use environment configuration for consistent URL construction
    // This will work for both development and production
    const finalUrl = `${environment.path}/${cleanHashname}`;

    console.log('🔗 Constructing media URL:', {
      input: hashname,
      cleaned: cleanHashname,
      final: finalUrl
    });

    return finalUrl;
  };

  // Upload states
  const [uploadingCover, setUploadingCover] = useState(false);
  const [uploadingVideo, setUploadingVideo] = useState(false);
  const [uploadProgress, setUploadProgress] = useState({ cover: 0, video: 0 });

  const [formData, setFormData] = useState<CourseFormData>({
    title: course.Title || '',
    subtitle: (course as any).Subtitle || '',
    description: (course as any).Description || course.Resume || '',
    shortDescription: course.Resume || '',
    price: course.Price || 0,
    newPrice: course.NewPrice,
    free: course.Free || false,
    language: course.Language || 'English',
    level: Array.isArray(course.Level) ? 'Beginner' : (course.Level as string | undefined) || 'Beginner',
    category: (course as any).Category?.Username || '',
    tags: course.Keywords || [],
    requirements: (course as any).Requirements || [],
    whatYouWillLearn: (course as any).WhatYouWillLearn || [],
    targetAudience: (course as any).TargetAudience || [],
    estimatedDuration: (course as any).EstimatedDuration || 0,
    maxStudents: course.Students
  });

  const [newTag, setNewTag] = useState('');
  const [newRequirement, setNewRequirement] = useState('');
  const [newLearningPoint, setNewLearningPoint] = useState('');
  const [newAudiencePoint, setNewAudiencePoint] = useState('');
  const [coverImagePreview, setCoverImagePreview] = useState<string | null>(null);
  const [videoPreview, setVideoPreview] = useState<string | null>(null);

  useEffect(() => {
    // Set initial cover image preview if exists
    if (course.CoverImage?.Hashname) {
      setCoverImagePreview(constructMediaUrl(course.CoverImage.Hashname));
    }

    // Set initial video preview if exists
    if (course.PresentationVideo?.Hashname) {
      setVideoPreview(constructMediaUrl(course.PresentationVideo.Hashname));
    }
  }, [course]);

  const handleInputChange = (field: keyof CourseFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleFileUpload = async (type: 'cover' | 'video', file: File) => {
    try {
      if (type === 'cover') {
        setUploadingCover(true);
        setUploadProgress(prev => ({ ...prev, cover: 0 }));

        // Validate file
        if (!file.type.startsWith('image/')) {
          throw new Error('Please select a valid image file');
        }

        if (file.size > 10 * 1024 * 1024) { // 10MB limit
          throw new Error('Image file size must be less than 10MB');
        }

        // Simulate progress for user feedback
        setUploadProgress(prev => ({ ...prev, cover: 25 }));

        // Convert file to base64
        const base64Data = await new Promise<string>((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => {
            const result = reader.result as string;
            const base64 = result.split(',')[1]; // Remove data:image/...;base64, prefix
            resolve(base64);
          };
          reader.onerror = reject;
          reader.readAsDataURL(file);
        });

        setUploadProgress(prev => ({ ...prev, cover: 50 }));

        // Upload directly to medias endpoint for reliable database storage
        const response = await fetch('http://localhost:3200/api/medias', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            Username: file.name,
            Hashname: base64Data,
            Extension: file.type,
            Size: file.size,
            SubDir: 'courses/covers'
          })
        });

        setUploadProgress(prev => ({ ...prev, cover: 75 }));

        if (!response.ok) {
          throw new Error(`Upload failed: ${response.statusText}`);
        }

        const uploadedMedia = await response.json();
        setUploadProgress(prev => ({ ...prev, cover: 100 }));

        // Create Media object for the course
        const mediaFile: Media = {
          Id: uploadedMedia.Id,
          Hashname: uploadedMedia.Hashname,
          Name: uploadedMedia.Name,
          Extension: uploadedMedia.Extension,
          Size: uploadedMedia.Size,
          SubDir: uploadedMedia.SubDir,
          Slug: uploadedMedia.Slug
        };

        // Update form data with uploaded file reference
        setFormData(prev => ({
          ...prev,
          coverImage: file,
          uploadedCoverImage: mediaFile
        }));
        // Set preview using helper function
        setCoverImagePreview(constructMediaUrl(uploadedMedia.Hashname));

        toast({
          title: "Success",
          description: "Cover image uploaded successfully"
        });
      } else {
        setUploadingVideo(true);
        setUploadProgress(prev => ({ ...prev, video: 0 }));

        // Validate file
        if (!file.type.startsWith('video/')) {
          throw new Error('Please select a valid video file');
        }

        if (file.size > 500 * 1024 * 1024) { // 500MB limit
          throw new Error('Video file size must be less than 500MB');
        }

        // Simulate progress for user feedback
        setUploadProgress(prev => ({ ...prev, video: 25 }));

        // Convert file to base64
        const base64Data = await new Promise<string>((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => {
            const result = reader.result as string;
            const base64 = result.split(',')[1]; // Remove data:video/...;base64, prefix
            resolve(base64);
          };
          reader.onerror = reject;
          reader.readAsDataURL(file);
        });

        setUploadProgress(prev => ({ ...prev, video: 50 }));

        // Upload directly to medias endpoint for reliable database storage
        const response = await fetch('http://localhost:3200/api/medias', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            Username: file.name,
            Hashname: base64Data,
            Extension: file.type,
            Size: file.size,
            SubDir: 'courses/videos'
          })
        });

        setUploadProgress(prev => ({ ...prev, video: 75 }));

        if (!response.ok) {
          throw new Error(`Upload failed: ${response.statusText}`);
        }

        const uploadedMedia = await response.json();
        setUploadProgress(prev => ({ ...prev, video: 100 }));

        // Create Media object for the course
        const mediaFile: Media = {
          Id: uploadedMedia.Id,
          Hashname: uploadedMedia.Hashname,
          Name: uploadedMedia.Name,
          Extension: uploadedMedia.Extension,
          Size: uploadedMedia.Size,
          SubDir: uploadedMedia.SubDir,
          Slug: uploadedMedia.Slug
        };

        // Update form data with uploaded file reference
        setFormData(prev => ({
          ...prev,
          presentationVideo: file,
          uploadedPresentationVideo: mediaFile
        }));
        // Set preview using helper function
        setVideoPreview(constructMediaUrl(uploadedMedia.Hashname));

        toast({
          title: "Success",
          description: "Presentation video uploaded successfully"
        });
      }
    } catch (error) {
      console.error('Upload error:', error);
      toast({
        title: "Upload Error",
        description: error instanceof Error ? error.message : "Failed to upload file",
        variant: "destructive"
      });
    } finally {
      if (type === 'cover') {
        setUploadingCover(false);
        setUploadProgress(prev => ({ ...prev, cover: 0 }));
      } else {
        setUploadingVideo(false);
        setUploadProgress(prev => ({ ...prev, video: 0 }));
      }
    }
  };

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const addRequirement = () => {
    if (newRequirement.trim()) {
      setFormData(prev => ({
        ...prev,
        requirements: [...prev.requirements, newRequirement.trim()]
      }));
      setNewRequirement('');
    }
  };

  const removeRequirement = (index: number) => {
    setFormData(prev => ({
      ...prev,
      requirements: prev.requirements.filter((_, i) => i !== index)
    }));
  };

  const addLearningPoint = () => {
    if (newLearningPoint.trim()) {
      setFormData(prev => ({
        ...prev,
        whatYouWillLearn: [...prev.whatYouWillLearn, newLearningPoint.trim()]
      }));
      setNewLearningPoint('');
    }
  };

  const removeLearningPoint = (index: number) => {
    setFormData(prev => ({
      ...prev,
      whatYouWillLearn: prev.whatYouWillLearn.filter((_, i) => i !== index)
    }));
  };

  const addAudiencePoint = () => {
    if (newAudiencePoint.trim()) {
      setFormData(prev => ({
        ...prev,
        targetAudience: [...prev.targetAudience, newAudiencePoint.trim()]
      }));
      setNewAudiencePoint('');
    }
  };

  const removeAudiencePoint = (index: number) => {
    setFormData(prev => ({
      ...prev,
      targetAudience: prev.targetAudience.filter((_, i) => i !== index)
    }));
  };

  const handleSave = async () => {
    try {
      // Validate required fields
      if (!formData.title.trim()) {
        toast({
          title: "Validation Error",
          description: "Course title is required",
          variant: "destructive"
        });
        return;
      }

      if (!formData.description.trim()) {
        toast({
          title: "Validation Error",
          description: "Course description is required",
          variant: "destructive"
        });
        return;
      }

      // Prepare the updated course data
      const updatedCourse: Partial<Course> = {
        Title: formData.title,
        Resume: formData.shortDescription,
        Price: formData.price,
        NewPrice: formData.newPrice,
        Free: formData.free,
        Language: formData.language,
        Keywords: formData.tags,
        Students: formData.maxStudents,
        // Add additional fields as custom properties
        ...(formData.subtitle && { Subtitle: formData.subtitle }),
        ...(formData.description && { Description: formData.description }),
        ...(formData.level && { Level: formData.level }),
        ...(formData.category && { Category: { Username: formData.category } }),
        ...(formData.requirements.length > 0 && { Requirements: formData.requirements }),
        ...(formData.whatYouWillLearn.length > 0 && { WhatYouWillLearn: formData.whatYouWillLearn }),
        ...(formData.targetAudience.length > 0 && { TargetAudience: formData.targetAudience }),
        ...(formData.estimatedDuration && { EstimatedDuration: formData.estimatedDuration }),
        // Include uploaded media files
        ...(formData.uploadedCoverImage && { CoverImage: formData.uploadedCoverImage }),
        ...(formData.uploadedPresentationVideo && { PresentationVideo: formData.uploadedPresentationVideo })
      } as any;

      await onSave(updatedCourse);

      toast({
        title: "Success",
        description: "Course page content updated successfully"
      });

    } catch (error) {
      console.error('Error saving course:', error);
      toast({
        title: "Error",
        description: "Failed to save course. Please try again.",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Edit Course Page Content</h1>
          <p className="text-gray-600 mt-1">Update your course information, media, and details</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={onCancel} disabled={loading}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={loading} className="bg-red-600 hover:bg-red-700">
            <Save className="w-4 h-4 mr-2" />
            {loading ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </div>

      <Tabs defaultValue="basic" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="basic">Basic Info</TabsTrigger>
          <TabsTrigger value="media">Media</TabsTrigger>
          <TabsTrigger value="pricing">Pricing</TabsTrigger>
          <TabsTrigger value="details">Course Details</TabsTrigger>
        </TabsList>

        {/* Basic Information Tab */}
        <TabsContent value="basic" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="w-5 h-5" />
                Course Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Course Title *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => handleInputChange('title', e.target.value)}
                    placeholder="Enter course title"
                    className="w-full"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="subtitle">Subtitle</Label>
                  <Input
                    id="subtitle"
                    value={formData.subtitle}
                    onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => handleInputChange('subtitle', e.target.value)}
                    placeholder="Enter course subtitle"
                    className="w-full"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="shortDescription">Short Description</Label>
                <Textarea
                  id="shortDescription"
                  value={formData.shortDescription}
                  onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => handleInputChange('shortDescription', e.target.value)}
                  placeholder="Brief description for course cards and previews"
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Full Description *</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => handleInputChange('description', e.target.value)}
                  placeholder="Detailed course description"
                  rows={6}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="language">Language</Label>
                  <Select value={formData.language} onValueChange={(value) => handleInputChange('language', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select language" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="English">English</SelectItem>
                      <SelectItem value="Spanish">Spanish</SelectItem>
                      <SelectItem value="French">French</SelectItem>
                      <SelectItem value="German">German</SelectItem>
                      <SelectItem value="Portuguese">Portuguese</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="level">Level</Label>
                  <Select value={formData.level} onValueChange={(value) => handleInputChange('level', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Beginner">Beginner</SelectItem>
                      <SelectItem value="Intermediate">Intermediate</SelectItem>
                      <SelectItem value="Advanced">Advanced</SelectItem>
                      <SelectItem value="All Levels">All Levels</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="category">Category</Label>
                  <Input
                    id="category"
                    value={formData.category}
                    onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => handleInputChange('category', e.target.value)}
                    placeholder="Course category"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Tags Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Tag className="w-5 h-5" />
                Tags & Keywords
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  value={newTag}
                  onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => setNewTag(e.target.value)}
                  placeholder="Add a tag"
                  onKeyPress={(e) => e.key === 'Enter' && addTag()}
                />
                <Button onClick={addTag} size="sm">
                  <Plus className="w-4 h-4" />
                </Button>
              </div>

              <div className="flex flex-wrap gap-2">
                {formData.tags.map((tag, index) => (
                  <Badge key={index} variant="secondary" className="flex items-center gap-1">
                    {tag}
                    <X
                      className="w-3 h-3 cursor-pointer hover:text-red-600"
                      onClick={() => removeTag(tag)}
                    />
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Media Tab */}
        <TabsContent value="media" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ImageIcon className="w-5 h-5" />
                Course Cover Image
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                {coverImagePreview ? (
                  <div className="space-y-4">
                    <img
                      src={coverImagePreview}
                      alt="Course cover"
                      className="max-w-full h-48 object-cover mx-auto rounded-lg"
                    />
                    <Button
                      variant="outline"
                      onClick={() => {
                        setCoverImagePreview(null);
                        setFormData(prev => ({
                          ...prev,
                          coverImage: undefined,
                          uploadedCoverImage: undefined
                        }));
                      }}
                      disabled={uploadingCover}
                    >
                      Remove Image
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {uploadingCover ? (
                      <>
                        <Loader2 className="w-12 h-12 text-red-600 mx-auto animate-spin" />
                        <div>
                          <p className="text-lg font-medium">Uploading Cover Image...</p>
                          <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                            <div
                              className="bg-red-600 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${uploadProgress.cover}%` }}
                            />
                          </div>
                          <p className="text-sm text-gray-500 mt-1">{uploadProgress.cover}% complete</p>
                        </div>
                      </>
                    ) : (
                      <>
                        <Upload className="w-12 h-12 text-gray-400 mx-auto" />
                        <div>
                          <p className="text-lg font-medium">Upload Course Cover Image</p>
                          <p className="text-gray-500">Recommended: 1280x720px, JPG or PNG, Max 10MB</p>
                        </div>
                        <input
                          type="file"
                          accept="image/*"
                          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                            const file = e.target.files?.[0];
                            if (file) handleFileUpload('cover', file);
                          }}
                          className="hidden"
                          id="cover-upload"
                          disabled={uploadingCover}
                        />
                        <Button asChild disabled={uploadingCover}>
                          <label htmlFor="cover-upload" className="cursor-pointer">
                            Choose Image
                          </label>
                        </Button>
                      </>
                    )}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Video className="w-5 h-5" />
                Presentation Video
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                {videoPreview ? (
                  <div className="space-y-4">
                    <video
                      src={videoPreview}
                      controls
                      className="max-w-full h-48 mx-auto rounded-lg"
                      preload="metadata"
                      onError={(e) => {
                        console.error('Video load error:', e);
                        console.error('Video URL:', videoPreview);
                      }}
                      onLoadStart={() => console.log('Video load started:', videoPreview)}
                    >
                      Your browser does not support the video tag.
                    </video>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setVideoPreview(null);
                        setFormData(prev => ({
                          ...prev,
                          presentationVideo: undefined,
                          uploadedPresentationVideo: undefined
                        }));
                      }}
                      disabled={uploadingVideo}
                    >
                      Remove Video
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {uploadingVideo ? (
                      <>
                        <Loader2 className="w-12 h-12 text-red-600 mx-auto animate-spin" />
                        <div>
                          <p className="text-lg font-medium">Uploading Presentation Video...</p>
                          <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                            <div
                              className="bg-red-600 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${uploadProgress.video}%` }}
                            />
                          </div>
                          <p className="text-sm text-gray-500 mt-1">{uploadProgress.video}% complete</p>
                        </div>
                      </>
                    ) : (
                      <>
                        <Video className="w-12 h-12 text-gray-400 mx-auto" />
                        <div>
                          <p className="text-lg font-medium">Upload Presentation Video</p>
                          <p className="text-gray-500">Recommended: MP4, Max 500MB</p>
                        </div>
                        <input
                          type="file"
                          accept="video/*"
                          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                            const file = e.target.files?.[0];
                            if (file) handleFileUpload('video', file);
                          }}
                          className="hidden"
                          id="video-upload"
                          disabled={uploadingVideo}
                        />
                        <Button asChild disabled={uploadingVideo}>
                          <label htmlFor="video-upload" className="cursor-pointer">
                            Choose Video
                          </label>
                        </Button>
                      </>
                    )}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Pricing Tab */}
        <TabsContent value="pricing" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="w-5 h-5" />
                Course Pricing
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="free-course"
                  checked={formData.free}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('free', e.target.checked)}
                  className="rounded"
                />
                <Label htmlFor="free-course">This is a free course</Label>
              </div>

              {!formData.free && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="price">Regular Price ($)</Label>
                    <Input
                      id="price"
                      type="number"
                      value={formData.price}
                      onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => handleInputChange('price', parseFloat(e.target.value) || 0)}
                      placeholder="0.00"
                      min="0"
                      step="0.01"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="newPrice">Sale Price ($) - Optional</Label>
                    <Input
                      id="newPrice"
                      type="number"
                      value={formData.newPrice || ''}
                      onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => handleInputChange('newPrice', parseFloat(e.target.value) || undefined)}
                      placeholder="0.00"
                      min="0"
                      step="0.01"
                    />
                  </div>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="duration">Estimated Duration (hours)</Label>
                  <Input
                    id="duration"
                    type="number"
                    value={formData.estimatedDuration}
                    onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => handleInputChange('estimatedDuration', parseFloat(e.target.value) || 0)}
                    placeholder="0"
                    min="0"
                    step="0.5"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxStudents">Max Students (Optional)</Label>
                  <Input
                    id="maxStudents"
                    type="number"
                    value={formData.maxStudents || ''}
                    onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => handleInputChange('maxStudents', parseInt(e.target.value) || undefined)}
                    placeholder="Unlimited"
                    min="1"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Course Details Tab */}
        <TabsContent value="details" className="space-y-6">
          {/* Requirements */}
          <Card>
            <CardHeader>
              <CardTitle>Course Requirements</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  value={newRequirement}
                  onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => setNewRequirement(e.target.value)}
                  placeholder="Add a requirement"
                  onKeyPress={(e) => e.key === 'Enter' && addRequirement()}
                />
                <Button onClick={addRequirement} size="sm">
                  <Plus className="w-4 h-4" />
                </Button>
              </div>

              <div className="space-y-2">
                {formData.requirements.map((requirement, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <span>{requirement}</span>
                    <X
                      className="w-4 h-4 cursor-pointer hover:text-red-600"
                      onClick={() => removeRequirement(index)}
                    />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* What You'll Learn */}
          <Card>
            <CardHeader>
              <CardTitle>What Students Will Learn</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  value={newLearningPoint}
                  onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => setNewLearningPoint(e.target.value)}
                  placeholder="Add a learning outcome"
                  onKeyPress={(e) => e.key === 'Enter' && addLearningPoint()}
                />
                <Button onClick={addLearningPoint} size="sm">
                  <Plus className="w-4 h-4" />
                </Button>
              </div>

              <div className="space-y-2">
                {formData.whatYouWillLearn.map((point, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <span>{point}</span>
                    <X
                      className="w-4 h-4 cursor-pointer hover:text-red-600"
                      onClick={() => removeLearningPoint(index)}
                    />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Target Audience */}
          <Card>
            <CardHeader>
              <CardTitle>Target Audience</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  value={newAudiencePoint}
                  onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => setNewAudiencePoint(e.target.value)}
                  placeholder="Add target audience description"
                  onKeyPress={(e) => e.key === 'Enter' && addAudiencePoint()}
                />
                <Button onClick={addAudiencePoint} size="sm">
                  <Plus className="w-4 h-4" />
                </Button>
              </div>

              <div className="space-y-2">
                {formData.targetAudience.map((point, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <span>{point}</span>
                    <X
                      className="w-4 h-4 cursor-pointer hover:text-red-600"
                      onClick={() => removeAudiencePoint(index)}
                    />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CoursePageEditor;
