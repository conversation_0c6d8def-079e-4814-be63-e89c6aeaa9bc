import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useToast } from '../../hooks/use-toast';
import courseService from '../../services/course.service';
import categoryService from '../../services/category.service';
import mediaService from '../../services/media.service';
import mediaPreviewService from '../../services/mediaPreview.service';
import type { Category as CategoryModel } from '../../models/category.model';

// shadcn/ui components
import { Button } from '../ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { RadioGroup, RadioGroupItem } from '../ui/radio-group';
import { Switch } from '../ui/switch';
import { Progress } from '../ui/progress';
import { Badge } from '../ui/badge';
import { Alert, AlertDescription } from '../ui/alert';
import { Separator } from '../ui/separator';

// Icons
import {
  BookOpen, Settings, Target, DollarSign, CheckCircle, Save,
  Loader2, AlertCircle, Plus, MinusCircle, ImageIcon, Video,
  FileText, BookOpenIcon, HelpCircle, Zap, X, Search, ChevronDown
} from 'lucide-react';

// Custom components
import RichTextEditor from '../common/RichTextEditor';

// Types and interfaces
interface Course {
  Id?: number;
  Title: string;
  Resume: string;
  Keywords: string[];
  Language: string;
  Format: number;
  Price: number;
  NewPrice: number;
  Free: boolean;
  Currency?: string;
  Prerequisites: string[];
  Goals: string[];
  Level: number[];
  Message: string;
  Congratulation: string;
  Categories: Category[];
  // Use media ID references instead of nested media objects
  coverImageId?: number;
  presentationVideoId?: number;
  CreatedBy?: any;
  Published: boolean;
  Archived: boolean;
}

interface MediaFile {
  Id?: number;
  Hashname: string;
  SubDir: string;
  Type: string;
  Size: number;
  OriginalUsername: string;
  Slug?: string;
}

interface Category {
  Id?: number;
  Title: string;
  Slug: string;
}

interface Step {
  id: number;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  fields: string[];
}

// Categories Multi-Select Component with Search
interface CategoriesMultiSelectProps {
  categories: CategoryModel[];
  selectedCategories: CategoryModel[];
  onSelectionChange: (categories: CategoryModel[]) => void;
}

const CategoriesMultiSelect: React.FC<CategoriesMultiSelectProps> = ({
  categories,
  selectedCategories,
  onSelectionChange
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const dropdownRef = React.useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const filteredCategories = categories.filter(category =>
    category.Title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    category.Name?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const toggleCategory = (category: CategoryModel) => {
    const isSelected = selectedCategories.some(cat => cat.Id === category.Id);
    if (isSelected) {
      onSelectionChange(selectedCategories.filter(cat => cat.Id !== category.Id));
    } else {
      onSelectionChange([...selectedCategories, category]);
    }
  };

  return (
    <div className="space-y-3">
      {/* Selected Categories Display */}
      {selectedCategories.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {selectedCategories.map(category => (
            <Badge
              key={category.Id}
              variant="secondary"
              className="px-2 py-1 cursor-pointer hover:bg-red-100 transition-colors"
              onClick={() => toggleCategory(category)}
            >
              {category.Title || category.Name}
              <X className="ml-1 w-3 h-3" />
            </Badge>
          ))}
        </div>
      )}

      {/* Dropdown */}
      <div className="relative" ref={dropdownRef}>
        <Button
          type="button"
          variant="outline"
          className="w-full justify-between"
          onClick={() => setIsOpen(!isOpen)}
        >
          <span className="text-left">
            {selectedCategories.length > 0
              ? `${selectedCategories.length} categories selected`
              : "Select categories"
            }
          </span>
          <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </Button>

        {isOpen && (
          <div className="absolute z-50 w-full mt-1 bg-white border rounded-lg shadow-lg max-h-60 overflow-hidden">
            {/* Search Input */}
            <div className="p-2 border-b">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search categories..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>

            {/* Categories List */}
            <div className="max-h-40 overflow-y-auto">
              {filteredCategories.length > 0 ? (
                filteredCategories.map(category => {
                  const isSelected = selectedCategories.some(cat => cat.Id === category.Id);
                  return (
                    <div
                      key={category.Id}
                      className={`px-3 py-2 cursor-pointer hover:bg-gray-50 flex items-center justify-between ${
                        isSelected ? 'bg-blue-50 text-blue-700' : ''
                      }`}
                      onClick={() => toggleCategory(category)}
                    >
                      <span>{category.Title || category.Name}</span>
                      {isSelected && <CheckCircle className="h-4 w-4" />}
                    </div>
                  );
                })
              ) : (
                <div className="px-3 py-2 text-gray-500 text-sm">
                  No categories found
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

const UnifiedCourseCreator: React.FC = () => {
  const navigate = useNavigate();
  const { authState } = useAuth();
  const { user } = authState;
  const { toast } = useToast();

  // Form state
  const [currentStep, setCurrentStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());
  const [stepValidation, setStepValidation] = useState<Record<number, boolean>>({});

  // Loading states
  const [loading, setLoading] = useState(false);
  const [categoriesLoading, setCategoriesLoading] = useState(false);
  const [uploadingCover, setUploadingCover] = useState(false);
  const [uploadingVideo, setUploadingVideo] = useState(false);

  // Data states
  const [categories, setCategories] = useState<CategoryModel[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});
  const [coverImagePreviewId, setCoverImagePreviewId] = useState<string | null>(null);
  const [videoPreviewId, setVideoPreviewId] = useState<string | null>(null);

  // Generate a temporary course slug for organized media storage
  const [tempCourseSlug] = useState(() => {
    return `temp-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
  });

  // Course data with proper defaults
  const [courseData, setCourseData] = useState<Course>({
    Title: '',
    Resume: '',
    Keywords: [''], // Start with one empty field
    Language: 'en',
    Format: 2, // Default to VIDEO
    Price: 0,
    NewPrice: 0,
    Free: true,
    Currency: undefined,
    Prerequisites: [''], // Start with one empty field
    Goals: [''], // Start with one empty field
    Level: [1], // Default to Beginner
    Message: '',
    Congratulation: '',
    Categories: [],
    // Initialize media ID fields as undefined
    coverImageId: undefined,
    presentationVideoId: undefined,
    Published: false,
    Archived: false,
    CreatedBy: user
  });

  // Step definitions
  const steps: Step[] = [
    {
      id: 1,
      title: "Basic Information",
      description: "Course title, description, and language",
      icon: BookOpen,
      fields: ['Title', 'Resume', 'Language']
    },
    {
      id: 2,
      title: "Course Details",
      description: "Format, difficulty level, and categories",
      icon: Settings,
      fields: ['Format', 'Level', 'Categories']
    },
    {
      id: 3,
      title: "Requirements & Goals",
      description: "Prerequisites, learning goals, and keywords",
      icon: Target,
      fields: ['Prerequisites', 'Goals', 'Keywords']
    },
    {
      id: 4,
      title: "Presentation & Media",
      description: "Cover image and presentation video",
      icon: ImageIcon,
      fields: ['coverImageId', 'presentationVideoId']
    },
    {
      id: 5,
      title: "Pricing",
      description: "Course pricing and promotional offers",
      icon: DollarSign,
      fields: ['Free', 'Price', 'NewPrice', 'Currency']
    },
    {
      id: 6,
      title: "Course Messages",
      description: "Welcome and congratulations messages",
      icon: FileText,
      fields: ['Message', 'Congratulation']
    },
    {
      id: 7,
      title: "Review & Submit",
      description: "Review your course and submit for approval",
      icon: CheckCircle,
      fields: []
    }
  ];

  const totalSteps = steps.length;

  // Options data
  const languageOptions = [
    { value: 'en', label: 'English' },
    { value: 'fr', label: 'French' },
    { value: 'es', label: 'Spanish' },
    { value: 'nl', label: 'Dutch' },
    { value: 'ge', label: 'German' },
    { value: 'la', label: 'Latin' },
    { value: 'ar', label: 'Arabic' },
    { value: 'ch', label: 'Chinese' }
  ];

  const formatOptions = [
    { value: 1, label: 'PDF Document', icon: <FileText className="h-5 w-5" />, description: 'Text-based course content' },
    { value: 2, label: 'Video Course', icon: <Video className="h-5 w-5" />, description: 'Video-based learning' },
    { value: 3, label: 'E-book', icon: <BookOpenIcon className="h-5 w-5" />, description: 'Digital book format' },
    { value: 4, label: 'Quiz', icon: <HelpCircle className="h-5 w-5" />, description: 'Interactive quiz content' },
    { value: 6, label: 'Assignment', icon: <Zap className="h-5 w-5" />, description: 'Practical assignments' }
  ];

  const levelOptions = [
    { value: 1, label: 'Beginner' },
    { value: 2, label: 'Intermediate' },
    { value: 3, label: 'Advanced' },
    { value: 4, label: 'Expert' }
  ];

  const currencyOptions = [
    { value: 'USD', label: 'USD - US Dollar' },
    { value: 'EUR', label: 'EUR - Euro' },
    { value: 'GBP', label: 'GBP - British Pound' },
    { value: 'CAD', label: 'CAD - Canadian Dollar' },
    { value: 'AUD', label: 'AUD - Australian Dollar' },
    { value: 'JPY', label: 'JPY - Japanese Yen' },
    { value: 'CNY', label: 'CNY - Chinese Yuan' },
    { value: 'INR', label: 'INR - Indian Rupee' }
  ];

  // Load categories on component mount
  useEffect(() => {
    loadCategories();
  }, []);

  // Cleanup localStorage previews on component unmount
  useEffect(() => {
    return () => {
      if (coverImagePreviewId) {
        mediaPreviewService.removeMediaPreview(coverImagePreviewId);
      }
      if (videoPreviewId) {
        mediaPreviewService.removeMediaPreview(videoPreviewId);
      }
    };
  }, [coverImagePreviewId, videoPreviewId]);

  const loadCategories = async () => {
    try {
      setCategoriesLoading(true);
      const categoriesData = await categoryService.getAll();
      setCategories(categoriesData);
    } catch (error) {
      console.error('Error loading categories:', error);
      toast({
        title: "Error",
        description: "Failed to load categories",
        variant: "destructive"
      });
    } finally {
      setCategoriesLoading(false);
    }
  };

  // Field change handler
  const handleFieldChange = (field: string, value: any) => {
    setCourseData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  // Field validation
  const validateField = (field: keyof Course, value: any): string => {
    switch (field) {
      case 'Title':
        if (!value || value.trim().length === 0) return 'Course title is required';
        if (value.length > 100) return 'Course title must be less than 100 characters';
        return '';
      case 'Resume':
        if (!value || value.trim().length === 0) return 'Course description is required';
        if (value.length < 50) return 'Course description must be at least 50 characters';
        return '';
      case 'Message':
        if (value && value.length > 250) return 'Welcome message must be less than 250 characters';
        return '';
      case 'Congratulation':
        if (value && value.length > 250) return 'Congratulations message must be less than 250 characters';
        return '';
      case 'Categories':
        if (!value || value.length === 0) return 'At least one category is required';
        return '';
      case 'Level':
        if (!value || value.length === 0) return 'At least one difficulty level is required';
        return '';
      case 'Format':
        if (!value) return 'Course format is required';
        return '';
      case 'Goals':
        if (!value || value.length === 0 || !value.some((g: string) => g.trim())) {
          return 'At least one learning goal is required';
        }
        return '';
      case 'Keywords':
        if (!value || value.length === 0 || !value.some((k: string) => k.trim())) {
          return 'At least one keyword is required';
        }
        return '';
      case 'Prerequisites':
        if (!value || value.length === 0 || !value.some((p: string) => p.trim())) {
          return 'At least one prerequisite is required';
        }
        return '';
      case 'Price':
        if (!courseData.Free && (!value || value < 0)) return 'Price must be a positive number';
        return '';
      case 'Currency':
        if (!courseData.Free && !value) return 'Currency is required for paid courses';
        return '';
      default:
        return '';
    }
  };

  // Step validation
  const validateStep = (stepNumber: number): boolean => {
    const step = steps.find(s => s.id === stepNumber);
    if (!step) return false;

    const stepErrors: Record<string, string> = {};

    step.fields.forEach(field => {
      const error = validateField(field as keyof Course, courseData[field as keyof Course]);
      if (error) {
        stepErrors[field] = error;
      }
    });

    // Update errors for this step
    setErrors(prev => ({
      ...prev,
      ...stepErrors
    }));

    const isValid = Object.keys(stepErrors).length === 0;
    setStepValidation(prev => ({
      ...prev,
      [stepNumber]: isValid
    }));

    return isValid;
  };

  // Step navigation
  const handleNextStep = () => {
    if (validateStep(currentStep)) {
      setCompletedSteps(prev => new Set([...prev, currentStep]));
      if (currentStep < totalSteps) {
        setCurrentStep(prev => prev + 1);
      }
    } else {
      toast({
        title: "Validation Error",
        description: "Please fix the errors before proceeding to the next step",
        variant: "destructive"
      });
    }
  };

  const handlePreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const handleStepClick = (stepNumber: number) => {
    // Allow clicking on previous steps or next step if current is valid
    if (stepNumber <= currentStep || (stepNumber === currentStep + 1 && validateStep(currentStep))) {
      setCurrentStep(stepNumber);
    }
  };

  // Real media upload functionality
  const handleCoverImageUpload = async (file: File) => {
    try {
      console.log('🖼️ Starting cover image upload:', file.name, file.size, file.type);
      setUploadingCover(true);
      setUploadProgress(prev => ({ ...prev, cover: 0 }));

      // Store in localStorage for immediate preview
      const preview = await mediaPreviewService.storeMediaPreview(file);
      setCoverImagePreviewId(preview.id);
      console.log('🖼️ Created localStorage preview:', preview.id);

      const uploadedFile = await mediaService.uploadFile(file, {
        onProgress: (progress) => {
          setUploadProgress(prev => ({ ...prev, cover: progress.percentage }));
        },
        maxSize: 100 * 1024 * 1024, // 100MB
        allowedTypes: ['image/jpeg', 'image/png', 'image/webp'],
        context: 'course-cover',
        userSlug: user?.Slug,
        courseSlug: tempCourseSlug
      });

      console.log('📤 Cover image uploaded successfully:', uploadedFile);

      // Store only the media ID reference for course creation
      // The backend expects coverImageId, not a nested CoverImage object
      setCourseData(prev => ({
        ...prev,
        coverImageId: uploadedFile.mediaId || parseInt(uploadedFile.id) || 0
      }));

      console.log('💾 Course data updated with cover image ID:', {
        coverImageId: uploadedFile.mediaId || parseInt(uploadedFile.id) || 0,
        uploadedFile: {
          Id: parseInt(uploadedFile.id) || 0,
          Hashname: uploadedFile.filename,
          SubDir: uploadedFile.subDir || 'courses/covers',
          Type: uploadedFile.mimeType,
          Size: uploadedFile.size,
          OriginalUsername: uploadedFile.originalUsername
        }
      });

      // Store server URL in course data, keep localStorage preview
      console.log('🖼️ Server URL stored in course data:', uploadedFile.url);
      console.log('🖼️ Keeping localStorage preview ID:', coverImagePreviewId);
      toast({
        title: "Success",
        description: "Cover image uploaded successfully!"
      });
    } catch (error) {
      console.error('Cover image upload failed:', error);
      toast({
        title: "Upload Error",
        description: "Failed to upload cover image. Please try again.",
        variant: "destructive"
      });
    } finally {
      setUploadingCover(false);
      setUploadProgress(prev => ({ ...prev, cover: 0 }));
    }
  };

  const handlePresentationVideoUpload = async (file: File) => {
    try {
      console.log('🎥 Starting presentation video upload:', file.name, file.size, file.type);
      setUploadingVideo(true);
      setUploadProgress(prev => ({ ...prev, video: 0 }));

      // Store in localStorage for immediate preview
      const preview = await mediaPreviewService.storeMediaPreview(file);
      setVideoPreviewId(preview.id);
      console.log('🎥 Created localStorage preview:', preview.id);

      const uploadedFile = await mediaService.uploadFile(file, {
        onProgress: (progress) => {
          setUploadProgress(prev => ({ ...prev, video: progress.percentage }));
        },
        maxSize: 500 * 1024 * 1024, // 500MB for videos
        allowedTypes: ['video/mp4', 'video/webm', 'video/ogg'],
        context: 'course-video',
        userSlug: user?.Slug,
        courseSlug: tempCourseSlug
      });

      console.log('📤 Presentation video uploaded successfully:', uploadedFile);

      // Store only the media ID reference for course creation
      // The backend expects presentationVideoId, not a nested PresentationVideo object
      setCourseData(prev => ({
        ...prev,
        presentationVideoId: uploadedFile.mediaId || parseInt(uploadedFile.id) || 0
      }));

      console.log('💾 Course data updated with presentation video ID:', {
        presentationVideoId: uploadedFile.mediaId || parseInt(uploadedFile.id) || 0,
        uploadedFile: {
          Id: parseInt(uploadedFile.id) || 0,
          Hashname: uploadedFile.filename,
          SubDir: uploadedFile.subDir || 'courses/videos',
          Type: uploadedFile.mimeType,
          Size: uploadedFile.size,
          OriginalUsername: uploadedFile.originalUsername
        }
      });
      console.log('📹 Keeping localStorage preview ID:', videoPreviewId);
      toast({
        title: "Success",
        description: "Presentation video uploaded successfully!"
      });
    } catch (error) {
      console.error('Presentation video upload failed:', error);
      toast({
        title: "Upload Error",
        description: "Failed to upload presentation video. Please try again.",
        variant: "destructive"
      });
    } finally {
      setUploadingVideo(false);
      setUploadProgress(prev => ({ ...prev, video: 0 }));
    }
  };

  // Course submission
  const handleSubmit = async () => {
    try {
      // Validate all steps
      let allValid = true;
      for (let i = 1; i <= totalSteps; i++) {
        if (!validateStep(i)) {
          allValid = false;
        }
      }

      if (!allValid) {
        toast({
          title: "Validation Error",
          description: "Please complete all required fields before submitting",
          variant: "destructive"
        });
        return;
      }

      setLoading(true);

      // Clean and prepare course data for submission
      const cleanCourseData = {
        ...courseData,
        Keywords: courseData.Keywords.filter(k => k.trim()),
        Prerequisites: courseData.Prerequisites.filter(p => p.trim()),
        Goals: courseData.Goals.filter(g => g.trim()),
        CreatedBy: { Id: user?.Id },
        // Use the temporary course slug for consistent directory structure
        Slug: tempCourseSlug
      };

      // Debug: Log the course data being submitted
      console.log('🚀 Submitting course data:', cleanCourseData);
      console.log('📷 Cover Image ID:', cleanCourseData.coverImageId);
      console.log('🎥 Presentation Video ID:', cleanCourseData.presentationVideoId);
      console.log('🖼️ Cover Image Preview ID:', coverImagePreviewId);
      console.log('📹 Video Preview ID:', videoPreviewId);

      // Final validation to ensure we have meaningful content
      if (cleanCourseData.Keywords.length === 0) {
        toast({
          title: "Validation Error",
          description: "At least one keyword is required",
          variant: "destructive"
        });
        setLoading(false);
        return;
      }

      if (cleanCourseData.Prerequisites.length === 0) {
        toast({
          title: "Validation Error",
          description: "At least one prerequisite is required",
          variant: "destructive"
        });
        setLoading(false);
        return;
      }

      if (cleanCourseData.Goals.length === 0) {
        toast({
          title: "Validation Error",
          description: "At least one learning goal is required",
          variant: "destructive"
        });
        setLoading(false);
        return;
      }

      const result = await courseService.add(cleanCourseData);

      toast({
        title: "Success",
        description: "Course created successfully!"
      });

      // Navigate to course structure page to add sections and content
      navigate(`/instructor/courses/${result.Slug}/structure`);
    } catch (error) {
      console.error('Error creating course:', error);
      toast({
        title: "Error",
        description: "Failed to create course. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // Step 1: Basic Information
  const renderBasicInformation = () => (
    <Card className="border-2 border-gray-200 shadow-lg">
      <CardHeader className="bg-gray-50 border-b border-gray-200">
        <CardTitle className="flex items-center text-brand-red-primary text-xl">
          <BookOpen className="mr-3 h-6 w-6" />
          Basic Course Information
        </CardTitle>
        <CardDescription className="text-gray-600 mt-2">
          Provide the essential details about your course that will help students understand what they'll learn.
        </CardDescription>
      </CardHeader>
      <CardContent className="p-8">
        <div className="space-y-8">
          {/* Course Title */}
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Label htmlFor="title" className="text-sm font-semibold text-gray-900">
                Course Title
              </Label>
              <span className="text-brand-red-primary text-sm">*</span>
            </div>
            <Input
              id="title"
              placeholder="Enter a compelling title for your course..."
              maxLength={100}
              value={courseData.Title}
              onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => handleFieldChange('Title', e.target.value)}
              className={`h-12 text-lg border-2 transition-all duration-200 ${
                errors.Title
                  ? 'border-red-500 focus:border-red-500 focus:ring-red-200'
                  : 'border-gray-200 focus:border-brand-red-primary focus:ring-brand-red-light'
              }`}
            />
            <div className="flex justify-between items-center">
              <div className="text-xs text-gray-500">
                {courseData.Title?.length || 0}/100 characters
              </div>
              {errors.Title && (
                <p className="text-sm text-red-500 flex items-center">
                  <AlertCircle className="mr-1 h-4 w-4" />
                  {errors.Title}
                </p>
              )}
            </div>
          </div>

        <div className="space-y-2">
          <Label htmlFor="description" className="text-sm font-medium">
            Course Description *
          </Label>
          <RichTextEditor
            value={courseData.Resume}
            onChange={(value: any) => handleFieldChange('Resume', value)}
            placeholder="Describe what students will learn in this course..."
            height="200px"
          />
          {errors.Resume && (
            <p className="text-sm text-red-500 flex items-center">
              <AlertCircle className="mr-1 h-4 w-4" />
              {errors.Resume}
            </p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="language" className="text-sm font-medium">
            Language *
          </Label>
          <div className={`border ${errors.Language ? 'border-red-500' : 'border-gray-300 hover:border-brand-red-primary'} rounded-md transition-colors`}>
            <Select
              value={courseData.Language}
              placeholder="Select course language"
              onValueChange={(value) => handleFieldChange('Language', value)}
            >
              {languageOptions.map(lang => (
                <SelectItem key={lang.value} value={lang.value}>
                  {lang.label}
                </SelectItem>
              ))}
            </Select>
          </div>
          {errors.Language && (
            <p className="text-sm text-red-500 flex items-center">
              <AlertCircle className="mr-1 h-4 w-4" />
              {errors.Language}
            </p>
          )}
        </div>
      </div>
      </CardContent>
    </Card>
  );

  // Step 2: Course Details
  const renderCourseDetails = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center text-brand-red-primary">
          <Settings className="mr-2 h-5 w-5" />
          Course Details
        </CardTitle>
        <CardDescription>
          Choose the format, difficulty level, and categories for your course
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label className="text-sm font-medium">
            Course Format *
          </Label>
          <RadioGroup
            value={courseData.Format.toString()}
            onValueChange={(value: string) => handleFieldChange('Format', parseInt(value))}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
          >
            {formatOptions.map(option => (
              <div key={option.value} className="flex items-center space-x-2">
                <RadioGroupItem value={option.value.toString()} id={`format-${option.value}`} />
                <Label
                  htmlFor={`format-${option.value}`}
                  className="flex items-center space-x-3 cursor-pointer p-4 border rounded-lg hover:bg-gray-50 flex-1"
                >
                  {option.icon}
                  <div>
                    <div className="font-medium">{option.label}</div>
                    <div className="text-xs text-gray-500">{option.description}</div>
                  </div>
                </Label>
              </div>
            ))}
          </RadioGroup>
          {errors.Format && (
            <p className="text-sm text-red-500 flex items-center">
              <AlertCircle className="mr-1 h-4 w-4" />
              {errors.Format}
            </p>
          )}
        </div>

        <div className="space-y-2">
          <Label className="text-sm font-medium">
            Difficulty Level *
          </Label>
          <div className={`border ${errors.Level ? 'border-red-500' : 'border-gray-300 hover:border-brand-red-primary'} rounded-md transition-colors`}>
            <Select
              value={courseData.Level[0]?.toString() || '1'}
              onValueChange={(value: string) => handleFieldChange('Level', [parseInt(value)])}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select difficulty level" />
              </SelectTrigger>
              <SelectContent>
                {levelOptions.map(level => (
                  <SelectItem key={level.value} value={level.value.toString()}>
                    {level.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          {errors.Level && (
            <p className="text-sm text-red-500 flex items-center">
              <AlertCircle className="mr-1 h-4 w-4" />
              {errors.Level}
            </p>
          )}
        </div>

        <div className="space-y-2">
          <Label className="text-sm font-medium">
            Categories *
          </Label>
          {categoriesLoading ? (
            <div className="flex items-center space-x-2 p-3 border rounded-lg">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span className="text-sm text-gray-500">Loading categories...</span>
            </div>
          ) : (
            <CategoriesMultiSelect
              categories={categories}
              selectedCategories={courseData.Categories || []}
              onSelectionChange={(newCategories) => handleFieldChange('Categories', newCategories)}
            />
          )}
          {errors.Categories && (
            <p className="text-sm text-red-500 flex items-center">
              <AlertCircle className="mr-1 h-4 w-4" />
              {errors.Categories}
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );

  // Step 3: Requirements & Goals
  const renderRequirementsAndGoals = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center text-brand-red-primary">
          <Target className="mr-2 h-5 w-5" />
          Requirements & Goals
        </CardTitle>
        <CardDescription>
          Define what students need to know and what they'll achieve. All fields in this section are required.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label className="text-sm font-medium">
            Prerequisites *
          </Label>
          <div className="space-y-2">
            {courseData.Prerequisites.map((prerequisite, index) => (
              <div key={index} className="flex items-center space-x-2">
                <Input
                  placeholder="Enter a prerequisite"
                  value={prerequisite}
                  onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
                    const newPrerequisites = [...courseData.Prerequisites];
                    newPrerequisites[index] = e.target.value;
                    handleFieldChange('Prerequisites', newPrerequisites);
                  }}
                  className="flex-1"
                />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newPrerequisites = courseData.Prerequisites.filter((_, i) => i !== index);
                    handleFieldChange('Prerequisites', newPrerequisites.length > 0 ? newPrerequisites : ['']);
                  }}
                  disabled={courseData.Prerequisites.length === 1}
                  className="border-gray-400 text-black hover:bg-gray-500 hover:text-white"
                >
                  <MinusCircle className="h-4 w-4" />
                </Button>
              </div>
            ))}
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => {
                handleFieldChange('Prerequisites', [...courseData.Prerequisites, '']);
              }}
              className="w-full border-red-600 text-black hover:bg-red-600 hover:text-white"
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Prerequisite
            </Button>
          </div>
          {errors.Prerequisites && (
            <p className="text-sm text-red-500 flex items-center">
              <AlertCircle className="mr-1 h-4 w-4" />
              {errors.Prerequisites}
            </p>
          )}
        </div>

        <div className="space-y-2">
          <Label className="text-sm font-medium">
            Learning Goals *
          </Label>
          <div className="space-y-2">
            {courseData.Goals.map((goal, index) => (
              <div key={index} className="flex items-center space-x-2">
                <Input
                  placeholder="Enter a learning goal"
                  value={goal}
                  onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
                    const newGoals = [...courseData.Goals];
                    newGoals[index] = e.target.value;
                    handleFieldChange('Goals', newGoals);
                  }}
                  className="flex-1"
                />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newGoals = courseData.Goals.filter((_, i) => i !== index);
                    handleFieldChange('Goals', newGoals.length > 0 ? newGoals : ['']);
                  }}
                  disabled={courseData.Goals.length === 1}
                  className="border-gray-400 text-black hover:bg-gray-500 hover:text-white"
                >
                  <MinusCircle className="h-4 w-4" />
                </Button>
              </div>
            ))}
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => {
                handleFieldChange('Goals', [...courseData.Goals, '']);
              }}
              className="w-full border-red-600 text-black hover:bg-red-600 hover:text-white"
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Learning Goal
            </Button>
          </div>
          {errors.Goals && (
            <p className="text-sm text-red-500 flex items-center">
              <AlertCircle className="mr-1 h-4 w-4" />
              {errors.Goals}
            </p>
          )}
        </div>

        <div className="space-y-2">
          <Label className="text-sm font-medium">
            Keywords *
          </Label>
          <div className="space-y-2">
            {courseData.Keywords.map((keyword, index) => (
              <div key={index} className="flex items-center space-x-2">
                <Input
                  placeholder="Enter a keyword"
                  value={keyword}
                  onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
                    const newKeywords = [...courseData.Keywords];
                    newKeywords[index] = e.target.value;
                    handleFieldChange('Keywords', newKeywords);
                  }}
                  className="flex-1"
                />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newKeywords = courseData.Keywords.filter((_, i) => i !== index);
                    handleFieldChange('Keywords', newKeywords.length > 0 ? newKeywords : ['']);
                  }}
                  disabled={courseData.Keywords.length === 1}
                  className="border-gray-400 text-black hover:bg-gray-500 hover:text-white"
                >
                  <MinusCircle className="h-4 w-4" />
                </Button>
              </div>
            ))}
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => {
                handleFieldChange('Keywords', [...courseData.Keywords, '']);
              }}
              className="w-full border-red-600 text-black hover:bg-brand-red-primary hover:text-white"
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Keyword
            </Button>
          </div>
          {errors.Keywords && (
            <p className="text-sm text-red-500 flex items-center">
              <AlertCircle className="mr-1 h-4 w-4" />
              {errors.Keywords}
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );

  // Step 4: Presentation & Media
  const renderPresentationAndMedia = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center text-brand-red-primary">
          <ImageIcon className="mr-2 h-5 w-5" />
          Presentation & Media
        </CardTitle>
        <CardDescription>
          Upload a cover image and presentation video for your course
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label className="text-sm font-medium">
            Course Cover Image
          </Label>
          <div className="border-2 border-dashed border-brand-red-primary rounded-lg p-6 text-center hover:bg-gray-50 transition-colors">
            <input
              type="file"
              accept="image/*"
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                const file = e.target.files?.[0];
                if (file) {
                  handleCoverImageUpload(file);
                }
              }}
              disabled={uploadingCover}
              className="hidden"
              id="cover-image-upload-step4"
            />
            <label htmlFor="cover-image-upload-step4" className={`cursor-pointer ${uploadingCover ? 'pointer-events-none' : ''}`}>
              {uploadingCover ? (
                <div className="space-y-2">
                  <Loader2 className="h-12 w-12 text-brand-red-primary mx-auto animate-spin" />
                  <p className="text-sm font-medium text-gray-700">Uploading cover image...</p>
                  <Progress value={uploadProgress.cover || 0} className="w-full" />
                  <p className="text-xs text-gray-500">{Math.round(uploadProgress.cover || 0)}% complete</p>
                </div>
              ) : coverImagePreviewId ? (
                <div className="space-y-4">
                  <div className="relative bg-gray-50 rounded-lg p-4 border-2 border-green-200">
                    <img
                      src={mediaPreviewService.getPreviewUrl(coverImagePreviewId) || ''}
                      alt="Course Cover Preview"
                      className="max-h-48 w-full object-contain mx-auto rounded-lg shadow-md"
                      onError={(e) => {
                        console.error('Failed to load localStorage image preview:', coverImagePreviewId);
                        e.currentTarget.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzY5NzM4MyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIFByZXZpZXc8L3RleHQ+PC9zdmc+';
                      }}
                      onLoad={() => {
                        console.log('✅ Cover image preview loaded successfully from localStorage');
                      }}
                    />
                    <Button
                      variant="destructive"
                      size="sm"
                      className="absolute top-2 right-2 h-8 w-8 p-0 shadow-lg"
                      onClick={(e) => {
                        e.preventDefault();
                        // Remove from localStorage
                        if (coverImagePreviewId) {
                          mediaPreviewService.removeMediaPreview(coverImagePreviewId);
                        }
                        setCoverImagePreviewId(null);
                        setCourseData(prev => ({ ...prev, coverImageId: undefined }));
                        console.log('🗑️ Cover image removed');
                      }}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-green-600 flex items-center justify-center">
                      <CheckCircle className="mr-1 h-4 w-4" />
                      Cover image uploaded successfully!
                    </p>
                  </div>
                </div>
              ) : (
                <div className="space-y-2">
                  <ImageIcon className="h-12 w-12 text-brand-red-primary mx-auto" />
                  <p className="text-sm font-medium text-gray-700">
                    Drop cover image here or click to browse
                  </p>
                  <p className="text-xs text-gray-500">
                    Recommended: 1280x720px, JPG/PNG/WebP, max 100MB
                  </p>
                </div>
              )}
            </label>
          </div>
        </div>

        <div className="space-y-2">
          <Label className="text-sm font-medium">
            Presentation Video (Optional)
          </Label>
          <div className="border-2 border-dashed border-brand-red-primary rounded-lg p-6 text-center hover:bg-gray-50 transition-colors">
            <input
              type="file"
              accept="video/*"
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                const file = e.target.files?.[0];
                if (file) {
                  handlePresentationVideoUpload(file);
                }
              }}
              disabled={uploadingVideo}
              className="hidden"
              id="video-upload-step4"
            />
            <label htmlFor="video-upload-step4" className={`cursor-pointer ${uploadingVideo ? 'pointer-events-none' : ''}`}>
              {uploadingVideo ? (
                <div className="space-y-2">
                  <Loader2 className="h-12 w-12 text-brand-red-primary mx-auto animate-spin" />
                  <p className="text-sm font-medium text-gray-700">Uploading presentation video...</p>
                  <Progress value={uploadProgress.video || 0} className="w-full" />
                  <p className="text-xs text-gray-500">{Math.round(uploadProgress.video || 0)}% complete</p>
                </div>
              ) : videoPreviewId ? (
                <div className="space-y-4">
                  <div className="relative bg-gray-50 rounded-lg p-4 border-2 border-green-200">
                    <video
                      src={mediaPreviewService.getPreviewUrl(videoPreviewId) || ''}
                      className="max-h-64 w-full mx-auto rounded-lg shadow-md"
                      controls
                      preload="metadata"
                      onError={() => {
                        console.error('Failed to load localStorage video preview:', videoPreviewId);
                      }}
                      onLoadedMetadata={() => {
                        console.log('✅ Video preview loaded successfully from localStorage');
                      }}
                    >
                      <p className="text-center text-gray-500 p-4">
                        Your browser does not support the video tag.
                      </p>
                    </video>
                    <Button
                      variant="destructive"
                      size="sm"
                      className="absolute top-2 right-2 h-8 w-8 p-0 shadow-lg"
                      onClick={(e) => {
                        e.preventDefault();
                        // Remove from localStorage
                        if (videoPreviewId) {
                          mediaPreviewService.removeMediaPreview(videoPreviewId);
                        }
                        setVideoPreviewId(null);
                        setCourseData(prev => ({ ...prev, presentationVideoId: undefined }));
                        console.log('🗑️ Presentation video removed');
                      }}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-green-600 flex items-center justify-center">
                      <CheckCircle className="mr-1 h-4 w-4" />
                      Presentation video uploaded successfully!
                    </p>
                  </div>
                </div>
              ) : (
                <div className="space-y-2">
                  <Video className="h-12 w-12 text-brand-red-primary mx-auto" />
                  <p className="text-sm font-medium text-gray-700">
                    Drop presentation video here or click to browse
                  </p>
                  <p className="text-xs text-gray-500">
                    Recommended: MP4 format, max 500MB
                  </p>
                </div>
              )}
            </label>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  // Step 5: Pricing
  const renderPricing = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center text-brand-red-primary">
          <DollarSign className="mr-2 h-5 w-5" />
          Course Pricing
        </CardTitle>
        <CardDescription>
          Set your course pricing and promotional offers
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="free-course"
              checked={courseData.Free}
              onCheckedChange={(checked: boolean) => {
                handleFieldChange('Free', checked);
                if (checked) {
                  handleFieldChange('Price', 0);
                  handleFieldChange('NewPrice', 0);
                  handleFieldChange('Currency', undefined);
                }
              }}
            />
            <Label htmlFor="free-course" className="text-sm font-medium">
              This is a free course
            </Label>
          </div>

          {!courseData.Free && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="currency" className="text-sm font-medium">
                  Currency *
                </Label>
                <div className={`border ${errors.Currency ? 'border-red-500' : 'border-gray-300 hover:border-brand-red-primary'} rounded-md transition-colors`}>
                  <Select
                    value={courseData.Currency || ''}
                    placeholder="Select currency"
                    onValueChange={(value) => handleFieldChange('Currency', value)}
                  >
                    {currencyOptions.map(currency => (
                      <SelectItem key={currency.value} value={currency.value}>
                        {currency.label}
                      </SelectItem>
                    ))}
                  </Select>
                </div>
                {errors.Currency && (
                  <p className="text-sm text-red-500 flex items-center">
                    <AlertCircle className="mr-1 h-4 w-4" />
                    {errors.Currency}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="price" className="text-sm font-medium">
                  Regular Price *
                </Label>
                <Input
                  id="price"
                  type="number"
                  min="0"
                  step="0.01"
                  placeholder="0.00"
                  value={courseData.Price || ''}
                  onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => handleFieldChange('Price', parseFloat(e.target.value) || 0)}
                  className={`${errors.Price ? 'border-red-500' : 'border-brand-red-primary'}`}
                />
                {errors.Price && (
                  <p className="text-sm text-red-500 flex items-center">
                    <AlertCircle className="mr-1 h-4 w-4" />
                    {errors.Price}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="new-price" className="text-sm font-medium">
                  Promotional Price (Optional)
                </Label>
                <Input
                  id="new-price"
                  type="number"
                  min="0"
                  step="0.01"
                  placeholder="0.00"
                  value={courseData.NewPrice || ''}
                  onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => handleFieldChange('NewPrice', parseFloat(e.target.value) || 0)}
                  className="border-brand-red-primary"
                />
                {courseData.NewPrice > 0 && courseData.Price > courseData.NewPrice && (
                  <p className="text-sm text-green-600">
                    Save {courseData.Currency} {(courseData.Price - courseData.NewPrice).toFixed(2)}
                    ({Math.round(((courseData.Price - courseData.NewPrice) / courseData.Price) * 100)}% off)
                  </p>
                )}
              </div>
            </div>
          )}
        </div>

        <Separator />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              Cover Image (Optional)
            </Label>
            <div className="border-2 border-dashed border-brand-red-primary rounded-lg p-6 text-center hover:bg-gray-50 transition-colors">
              <input
                type="file"
                accept="image/*"
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    handleCoverImageUpload(file);
                  }
                }}
                disabled={uploadingCover}
                className="hidden"
                id="cover-image-upload"
              />
              <label htmlFor="cover-image-upload" className={`cursor-pointer ${uploadingCover ? 'pointer-events-none' : ''}`}>
                {uploadingCover ? (
                  <div className="space-y-2">
                    <Loader2 className="h-12 w-12 text-brand-red-primary mx-auto animate-spin" />
                    <p className="text-sm font-medium text-gray-700">Uploading cover image...</p>
                    <Progress value={uploadProgress.cover || 0} className="w-full" />
                    <p className="text-xs text-gray-500">{Math.round(uploadProgress.cover || 0)}% complete</p>
                  </div>
                ) : coverImagePreviewId ? (
                  <div className="space-y-4">
                    <div className="relative bg-gray-50 rounded-lg p-4 border-2 border-green-200">
                      <img
                        src={mediaPreviewService.getPreviewUrl(coverImagePreviewId) || ''}
                        alt="Course Cover Preview"
                        className="max-h-48 w-full object-contain mx-auto rounded-lg shadow-md"
                        onError={(e) => {
                          console.error('Failed to load localStorage image preview (Step 5):', coverImagePreviewId);
                          e.currentTarget.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzY5NzM4MyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIFByZXZpZXc8L3RleHQ+PC9zdmc+';
                        }}
                        onLoad={() => {
                          console.log('✅ Cover image preview loaded successfully (Step 5)');
                        }}
                      />
                      <Button
                        variant="destructive"
                        size="sm"
                        className="absolute top-2 right-2 h-8 w-8 p-0 shadow-lg"
                        onClick={(e) => {
                          e.preventDefault();
                          // Remove from localStorage
                          if (coverImagePreviewId) {
                            mediaPreviewService.removeMediaPreview(coverImagePreviewId);
                          }
                          setCoverImagePreviewId(null);
                          setCourseData(prev => ({ ...prev, coverImageId: undefined }));
                          console.log('🗑️ Cover image removed (Step 5)');
                        }}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-green-600 flex items-center justify-center">
                        <CheckCircle className="mr-1 h-4 w-4" />
                        Cover image uploaded successfully!
                      </p>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <ImageIcon className="h-12 w-12 text-brand-red-primary mx-auto" />
                    <p className="text-sm font-medium text-gray-700">
                      Drop cover image here or click to browse
                    </p>
                    <p className="text-xs text-gray-500">
                      Recommended: 1280x720 pixels, JPG or PNG format, max 100MB
                    </p>
                  </div>
                )}
              </label>
            </div>
          </div>

          <div className="space-y-2">
            <Label className="text-sm font-medium">
              Presentation Video (Optional)
            </Label>
            <div className="border-2 border-dashed border-brand-red-primary rounded-lg p-6 text-center hover:bg-gray-50 transition-colors">
              <input
                type="file"
                accept="video/*"
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    handlePresentationVideoUpload(file);
                  }
                }}
                disabled={uploadingVideo}
                className="hidden"
                id="video-upload"
              />
              <label htmlFor="video-upload" className={`cursor-pointer ${uploadingVideo ? 'pointer-events-none' : ''}`}>
                {uploadingVideo ? (
                  <div className="space-y-2">
                    <Loader2 className="h-12 w-12 text-brand-red-primary mx-auto animate-spin" />
                    <p className="text-sm font-medium text-gray-700">Uploading presentation video...</p>
                    <Progress value={uploadProgress.video || 0} className="w-full" />
                    <p className="text-xs text-gray-500">{Math.round(uploadProgress.video || 0)}% complete</p>
                  </div>
                ) : videoPreviewId ? (
                  <div className="space-y-4">
                    <div className="relative bg-gray-50 rounded-lg p-4 border-2 border-green-200">
                      <video
                        src={mediaPreviewService.getPreviewUrl(videoPreviewId) || ''}
                        className="max-h-64 w-full mx-auto rounded-lg shadow-md"
                        controls
                        preload="metadata"
                        onError={() => {
                          console.error('Failed to load localStorage video preview (Step 5):', videoPreviewId);
                        }}
                        onLoadedMetadata={() => {
                          console.log('✅ Video preview loaded successfully (Step 5)');
                        }}
                      >
                        <p className="text-center text-gray-500 p-4">
                          Your browser does not support the video tag.
                        </p>
                      </video>
                      <Button
                        variant="destructive"
                        size="sm"
                        className="absolute top-2 right-2 h-8 w-8 p-0 shadow-lg"
                        onClick={(e) => {
                          e.preventDefault();
                          // Remove from localStorage
                          if (videoPreviewId) {
                            mediaPreviewService.removeMediaPreview(videoPreviewId);
                          }
                          setVideoPreviewId(null);
                          setCourseData(prev => ({ ...prev, presentationVideoId: undefined }));
                          console.log('🗑️ Presentation video removed (Step 5)');
                        }}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-green-600 flex items-center justify-center">
                        <CheckCircle className="mr-1 h-4 w-4" />
                        Presentation video uploaded successfully!
                      </p>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <Video className="h-12 w-12 text-brand-red-primary mx-auto" />
                    <p className="text-sm font-medium text-gray-700">
                      Drop presentation video here or click to browse
                    </p>
                    <p className="text-xs text-gray-500">
                      Recommended: MP4 format, max 500MB
                    </p>
                  </div>
                )}
              </label>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  // Step 6: Course Messages
  const renderCourseMessages = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center text-brand-red-primary">
          <FileText className="mr-2 h-5 w-5" />
          Course Messages
        </CardTitle>
        <CardDescription>
          Create welcome and congratulations messages for your students
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="welcome-message" className="text-sm font-medium">
            Welcome Message (Optional)
          </Label>
          <Textarea
            id="welcome-message"
            placeholder="Write a welcome message for students when they enroll in your course..."
            value={courseData.Message || ''}
            onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => handleFieldChange('Message', e.target.value)}
            className="min-h-[120px] border-brand-red-primary"
            maxLength={250}
          />
          <p className="text-xs text-gray-500">
            {courseData.Message?.length || 0}/250 characters
          </p>
          {errors.Message && (
            <p className="text-sm text-red-500 flex items-center">
              <AlertCircle className="mr-1 h-4 w-4" />
              {errors.Message}
            </p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="congratulations-message" className="text-sm font-medium">
            Congratulations Message (Optional)
          </Label>
          <Textarea
            id="congratulations-message"
            placeholder="Write a congratulations message for students when they complete your course..."
            value={courseData.Congratulation || ''}
            onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => handleFieldChange('Congratulation', e.target.value)}
            className="min-h-[120px] border-brand-red-primary"
            maxLength={250}
          />
          <p className="text-xs text-gray-500">
            {courseData.Congratulation?.length || 0}/250 characters
          </p>
          {errors.Congratulation && (
            <p className="text-sm text-red-500 flex items-center">
              <AlertCircle className="mr-1 h-4 w-4" />
              {errors.Congratulation}
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );

  // Step 7: Final Settings
  const renderFinalSettings = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center text-brand-red-primary">
          <CheckCircle className="mr-2 h-5 w-5" />
          Final Settings
        </CardTitle>
        <CardDescription>
          Add welcome messages and review your course details
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="welcome-message" className="text-sm font-medium">
            Welcome Message (Optional)
          </Label>
          <RichTextEditor
            value={courseData.Message}
            onChange={(value: any) => handleFieldChange('Message', value)}
            placeholder="Write a welcome message for your students..."
            height="150px"
          />
          <p className="text-xs text-gray-500">
            This message will be shown to students when they enroll in your course
          </p>
        </div>

        <div className="space-y-2">
          <Label htmlFor="congratulation-message" className="text-sm font-medium">
            Congratulations Message (Optional)
          </Label>
          <RichTextEditor
            value={courseData.Congratulation}
            onChange={(value: any) => handleFieldChange('Congratulation', value)}
            placeholder="Write a congratulations message for course completion..."
            height="150px"
          />
          <p className="text-xs text-gray-500">
            This message will be shown to students when they complete your course
          </p>
        </div>

        <Separator />

        <Alert className="border-blue-200 bg-blue-50">
          <AlertCircle className="h-4 w-4 text-blue-600" />
          <AlertDescription className="text-blue-800">
            <div className="font-medium mb-1">Next Steps After Creation</div>
            <p className="text-sm">
              After creating your course, you'll be able to add course sections and content.
              Remember that courses need at least one section with content before they can be published.
            </p>
          </AlertDescription>
        </Alert>

        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-brand-red-primary">Course Summary</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-600">Title:</Label>
              <p className="text-sm">{courseData.Title || 'Not specified'}</p>
            </div>
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-600">Language:</Label>
              <p className="text-sm">{languageOptions.find(l => l.value === courseData.Language)?.label || 'Not specified'}</p>
            </div>
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-600">Format:</Label>
              <p className="text-sm">{formatOptions.find(f => f.value === courseData.Format)?.label || 'Not specified'}</p>
            </div>
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-600">Pricing:</Label>
              <p className="text-sm">
                {courseData.Free ? 'Free' : `${courseData.Currency} ${courseData.NewPrice > 0 ? courseData.NewPrice : courseData.Price}`}
              </p>
            </div>
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-600">Categories:</Label>
              <div className="flex flex-wrap gap-1">
                {courseData.Categories?.map(category => (
                  <Badge key={category.Id} variant="secondary" className="text-xs">
                    {category.Title}
                  </Badge>
                )) || <span className="text-sm text-gray-500">None selected</span>}
              </div>
            </div>
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-600">Difficulty:</Label>
              <div className="flex flex-wrap gap-1">
                {courseData.Level?.map(level => (
                  <Badge key={level} variant="outline" className="text-xs">
                    {levelOptions.find(l => l.value === level)?.label}
                  </Badge>
                )) || <span className="text-sm text-gray-500">None selected</span>}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  // Render step content based on current step
  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return renderBasicInformation();
      case 2:
        return renderCourseDetails();
      case 3:
        return renderRequirementsAndGoals();
      case 4:
        return renderPresentationAndMedia();
      case 5:
        return renderPricing();
      case 6:
        return renderCourseMessages();
      case 7:
        return renderFinalSettings();
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-6xl mx-auto">
        {/* Modern Header with Brand Styling */}
        <div className="bg-black text-white px-8 py-12">
          <div className="max-w-4xl mx-auto">
            <div className="flex items-center space-x-4 mb-6">
              <div className="w-12 h-12 bg-brand-red-primary rounded-lg flex items-center justify-center">
                <BookOpen className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-4xl font-bold mb-2">Create New Course</h1>
                <p className="text-gray-300 text-lg">
                  Build and launch your course with our step-by-step creator
                </p>
              </div>
            </div>

            {/* Current Step Info */}
            <div className="bg-gray-900 rounded-xl p-6 border border-gray-800">
              <div className="text-white font-medium text-lg">
                {steps[currentStep - 1]?.title}
              </div>
              <div className="text-gray-400 text-sm mt-2">
                {steps[currentStep - 1]?.description}
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Area with Sidebar Navigation */}
        <div className="flex min-h-screen">
          {/* Modern Sidebar Navigation */}
          <div className="w-80 bg-gray-50 border-r border-gray-200 p-6">
            <div className="space-y-2">
              {steps.map((step) => {
                const isActive = currentStep === step.id;
                const isCompleted = completedSteps.has(step.id);
                const isClickable = step.id <= currentStep || (step.id === currentStep + 1 && stepValidation[currentStep]);

                return (
                  <div
                    key={step.id}
                    className={`group flex items-center p-3 rounded-lg transition-all duration-200 cursor-pointer ${
                      isActive
                        ? 'bg-red-600 text-white border-2 border-red-600 shadow-md'
                        : isCompleted
                        ? 'bg-white border border-green-200 text-gray-900 hover:bg-gray-50'
                        : isClickable
                        ? 'bg-white border border-gray-200 text-gray-700 hover:border-red-600'
                        : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    }`}
                    style={isActive ? { backgroundColor: '#dc3545', borderColor: '#dc3545', color: '#ffffff' } : {}}
                    onClick={() => isClickable && handleStepClick(step.id)}
                  >
                    {/* Step Icon */}
                    <div className={`flex items-center justify-center w-8 h-8 rounded-lg mr-3 transition-colors ${
                      isActive
                        ? 'bg-white bg-opacity-20 text-white'
                        : isCompleted
                        ? 'text-green-600'
                        : isClickable
                        ? 'text-gray-600 group-hover:text-brand-red-primary'
                        : 'text-gray-400'
                    }`}>
                      {isCompleted ? (
                        <CheckCircle className={`h-4 w-4 ${isActive ? 'text-white' : 'text-green-600'}`} />
                      ) : (
                        <step.icon className={`h-4 w-4 ${isActive ? 'text-white' : ''}`} />
                      )}
                    </div>

                    {/* Step Content */}
                    <div className="flex-1 min-w-0">
                      <div className={`font-semibold text-sm ${
                        isActive
                          ? 'text-white'
                          : isCompleted
                          ? 'text-gray-900'
                          : 'text-gray-700'
                      }`}>
                        {step.title}
                      </div>
                      <div className={`text-xs mt-1 ${
                        isActive
                          ? 'text-white opacity-90'
                          : isCompleted
                          ? 'text-gray-600'
                          : 'text-gray-500'
                      }`}>
                        {step.description}
                      </div>
                    </div>


                  </div>
                );
              })}
            </div>


          </div>

          {/* Main Content Area */}
          <div className="flex-1 p-8">
            <div className="max-w-4xl mx-auto">
              {renderStepContent()}
            </div>
          </div>
        </div>

        {/* Modern Navigation Bar */}
        <div className="bg-white border-t border-gray-200 px-8 py-6">
          <div className="max-w-4xl mx-auto flex justify-between items-center">
            <Button
              variant="outline"
              onClick={handlePreviousStep}
              disabled={currentStep === 1}
              className="border-gray-300 text-gray-700 hover:border-brand-red-primary hover:text-brand-red-primary disabled:opacity-50 disabled:cursor-not-allowed px-6 py-3"
            >
              <ChevronDown className="mr-2 h-4 w-4 rotate-90" />
              Previous
            </Button>

            {/* Current Step Title */}
            <div className="text-center">
              <span className="text-sm font-medium text-gray-600">
                {steps[currentStep - 1]?.title}
              </span>
            </div>

            <div className="flex space-x-3">
              {currentStep < totalSteps ? (
                <Button
                  onClick={handleNextStep}
                  className="bg-brand-red-primary hover:bg-brand-red-secondary text-white px-8 py-3 font-medium transition-all duration-200 shadow-lg hover:shadow-xl"
                >
                  Continue
                  <ChevronDown className="ml-2 h-4 w-4 -rotate-90" />
                </Button>
              ) : (
                <Button
                  onClick={handleSubmit}
                  disabled={loading}
                  className="bg-black hover:bg-gray-800 text-white px-8 py-3 font-medium transition-all duration-200 shadow-lg hover:shadow-xl disabled:opacity-50"
                >
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating Course...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Create Course
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Modern Error Display */}
        {Object.keys(errors).length > 0 && (
          <div className="bg-red-50 border-l-4 border-brand-red-primary px-8 py-6">
            <div className="max-w-4xl mx-auto">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <AlertCircle className="h-5 w-5 text-brand-red-primary" />
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800 mb-2">
                    Please fix the following errors to continue:
                  </h3>
                  <div className="space-y-2">
                    {Object.entries(errors).map(([field, error]) => (
                      <div key={field} className="flex items-center text-sm text-red-700">
                        <div className="w-2 h-2 bg-brand-red-primary rounded-full mr-3" />
                        <span className="font-medium">{field}:</span>
                        <span className="ml-1">{error}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default UnifiedCourseCreator;
