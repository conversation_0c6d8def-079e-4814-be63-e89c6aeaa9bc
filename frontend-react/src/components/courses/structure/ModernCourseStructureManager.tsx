import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '../../ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../../ui/card';
import { useToast } from '../../../hooks/use-toast';
import {
  Plus,
  BookOpen,
  Video,
  FileText,
  HelpCircle,
  Edit,
  Trash2,
  ChevronDown,
  ChevronRight,
  Menu,
  X,
  Save,
  RotateCcw
} from 'lucide-react';

import type { Course, CourseSection, User } from '../../../models';
import { ContentType } from '../../../models';
import { courseContentService } from '../../../services/courseContent.service';
import courseSectionService from '../../../services/courseSection.service';
import courseService from '../../../services/course.service';
import ContentCreationModal from '../modals/ContentCreationModal';
import QuizCreationModal from '../modals/QuizCreationModal';
import AssignmentCreationModal from '../modals/AssignmentCreationModal';

interface ModernCourseStructureManagerProps {
  course: Course;
  user: User;
  onCourseUpdate: (course: Course) => void;
}

const ModernCourseStructureManager: React.FC<ModernCourseStructureManagerProps> = ({
  course,
  user: _user, // Keep for future use
  onCourseUpdate
}) => {
  const { toast } = useToast();
  
  // State management
  const [sections, setSections] = useState<CourseSection[]>(course.Sections || []);
  const [showAddSection, setShowAddSection] = useState(false);
  const [newSection, setNewSection] = useState<Partial<CourseSection>>({
    Title: '',
    Description: ''
  });
  const [editingSection, setEditingSection] = useState<CourseSection | null>(null);
  const [loading, setLoading] = useState(false);
  const [expandedSections, setExpandedSections] = useState<Set<number>>(new Set());
  const [managementMenus, setManagementMenus] = useState<boolean[]>([]);

  // Modal state management
  const [contentModalOpen, setContentModalOpen] = useState(false);
  const [quizModalOpen, setQuizModalOpen] = useState(false);
  const [assignmentModalOpen, setAssignmentModalOpen] = useState(false);
  const [selectedSection, setSelectedSection] = useState<CourseSection | null>(null);

  // Initialize management menus
  useEffect(() => {
    setManagementMenus(sections.map(() => false));
  }, [sections]);

  // Content type helpers
  const getContentTypeIcon = (type: ContentType) => {
    switch (type) {
      case ContentType.VIDEO:
        return <Video className="w-4 h-4" />;
      case ContentType.PDF:
      case ContentType.EBOOK:
        return <FileText className="w-4 h-4" />;
      case ContentType.QUIZ:
        return <HelpCircle className="w-4 h-4" />;
      case ContentType.ASSIGNMENT:
        return <BookOpen className="w-4 h-4" />;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };

  const getContentTypeLabel = (type: ContentType) => {
    switch (type) {
      case ContentType.VIDEO:
      case ContentType.PDF:
      case ContentType.EBOOK:
        return 'Course';
      case ContentType.QUIZ:
        return 'Question';
      case ContentType.ASSIGNMENT:
        return 'Assignment';
      default:
        return 'Content';
    }
  };

  const isRightType = (section: CourseSection, type: ContentType): boolean => {
    // Angular logic: If section has content, only allow same type as first content
    // If section has no content, allow all types
    return section.Contents && section.Contents.length > 0
      ? section.Contents[0].Type === type
      : true;
  };

  // Section management
  const handleAddSection = () => {
    setEditingSection(null); // Clear editing section
    setShowAddSection(true);
    setNewSection({
      Title: '',
      Description: ''
    });
  };

  const handleCancelAddSection = () => {
    setShowAddSection(false);
    setEditingSection(null);
    setNewSection({
      Title: '',
      Description: ''
    });
  };

  const handleSaveSection = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newSection.Title?.trim()) {
      toast({
        title: "Error",
        description: "Section title is required",
        variant: "destructive"
      });
      return;
    }

    try {
      setLoading(true);

      if (editingSection && editingSection.Slug) {
        // Update existing section
        const updateData = {
          Title: newSection.Title.trim(),
          Description: newSection.Description?.trim() || '',
          Position: editingSection.Position,
          IsActive: true,
          IsPublished: false, // Keep as unpublished for now
          courseId: course.Id!
        };

        console.log('Updating section:', editingSection.Slug, updateData);
        await courseSectionService.update(editingSection.Slug, updateData);

        toast({
          title: "Success",
          description: "Section updated successfully"
        });
      } else {
        // Create new section
        const sectionData = {
          ...newSection,
          CourseId: course.Id,
          Course: { Id: course.Id },
          Position: sections.length + 1,
          IsActive: true,
          IsPublished: false,
          Contents: []
        };

        console.log('Creating section:', sectionData);
        await courseSectionService.create(sectionData as any, course);

        toast({
          title: "Success",
          description: "Section created successfully"
        });
      }

      // Refresh course data
      const updatedCourse = await courseService.getBySlug(course.Slug!);
      setSections(updatedCourse.Sections || []);
      onCourseUpdate(updatedCourse);

      handleCancelAddSection();
    } catch (error) {
      console.error('Error saving section:', error);
      toast({
        title: "Error",
        description: editingSection ? "Failed to update section" : "Failed to create section",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // Management menu toggle
  const toggleManagementMenu = (index: number) => {
    const newMenus = managementMenus.map(() => false);
    newMenus[index] = !managementMenus[index];
    setManagementMenus(newMenus);
  };

  // Content management handlers - matching Angular approach
  const handleCreateContent = (section: CourseSection, index: number) => {
    toggleManagementMenu(index);
    setSelectedSection(section);
    setContentModalOpen(true);
  };

  const handleCreateQuiz = (section: CourseSection, index: number) => {
    toggleManagementMenu(index);
    setSelectedSection(section);
    setQuizModalOpen(true);
  };

  const handleCreateAssignment = (section: CourseSection, index: number) => {
    toggleManagementMenu(index);
    setSelectedSection(section);
    setAssignmentModalOpen(true);
  };



  const handleEditSection = (section: CourseSection, index: number) => {
    toggleManagementMenu(index);

    // Set the section for editing
    setEditingSection(section);
    setNewSection({
      Title: section.Title,
      Description: section.Description || ''
    });
    setShowAddSection(true);

    console.log('Editing section:', section);
  };

  const handleDeleteSection = async (section: CourseSection, _index: number) => {
    if (!window.confirm(`Are you sure you want to delete the section "${section.Title}"? This action cannot be undone.`)) {
      return;
    }

    try {
      setLoading(true);

      console.log('Deleting section:', section);

      // Delete the section using the courseSectionService (requires slug)
      if (!section.Slug) {
        throw new Error('Section slug is required for deletion');
      }
      await courseSectionService.delete(section.Slug);

      // Refresh course data
      const updatedCourse = await courseService.getBySlug(course.Slug!);
      setSections(updatedCourse.Sections || []);
      onCourseUpdate(updatedCourse);

      toast({
        title: "Success",
        description: "Section deleted successfully"
      });
    } catch (error) {
      console.error('Error deleting section:', error);
      toast({
        title: "Error",
        description: "Failed to delete section. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // Section expansion toggle
  const toggleSectionExpansion = (sectionId: number) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(sectionId)) {
      newExpanded.delete(sectionId);
    } else {
      newExpanded.add(sectionId);
    }
    setExpandedSections(newExpanded);
  };

  // Content management handlers
  const handleEditContent = (content: any) => {
    // TODO: Open content edit modal
    console.log('Edit content:', content);
    toast({
      title: "Info",
      description: "Content editing modal will be implemented soon"
    });
  };

  const handleDeleteContent = async (content: any) => {
    if (!window.confirm(`Are you sure you want to delete "${content.Title}"?`)) {
      return;
    }

    try {
      setLoading(true);

      if (!content.Slug) {
        throw new Error('Content slug is required for deletion');
      }

      await courseContentService.deleteContent(content.Slug);

      // Refresh course data
      const updatedCourse = await courseService.getBySlug(course.Slug!);
      setSections(updatedCourse.Sections || []);
      onCourseUpdate(updatedCourse);

      toast({
        title: "Success",
        description: "Content deleted successfully"
      });
    } catch (error) {
      console.error('Error deleting content:', error);
      toast({
        title: "Error",
        description: "Failed to delete content",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleTogglePublish = async (content: any) => {
    try {
      setLoading(true);

      const updateData = {
        IsPublished: !(content as any).IsPublished
      };

      await courseContentService.updateContent(content.Slug, updateData);

      // Refresh course data
      const updatedCourse = await courseService.getBySlug(course.Slug!);
      setSections(updatedCourse.Sections || []);
      onCourseUpdate(updatedCourse);

      toast({
        title: "Success",
        description: `Content ${updateData.IsPublished ? 'published' : 'unpublished'} successfully`
      });
    } catch (error) {
      console.error('Error toggling publish status:', error);
      toast({
        title: "Error",
        description: "Failed to update publish status",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 space-y-6 relative">
      {/* Loading Overlay */}
      {loading && (
        <div className="absolute inset-0 bg-white/80 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="flex items-center space-x-3 bg-white rounded-lg shadow-lg p-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-red-600"></div>
            <span className="text-gray-700 font-medium">Processing...</span>
          </div>
        </div>
      )}
      {/* Header */}
      <div className="border-b border-gray-200 pb-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Course Structure</h2>
            <p className="text-gray-600 mt-1">
              Organize your course content into sections and lessons
            </p>
          </div>
          <Button
            onClick={handleAddSection}
            className="bg-red-600 hover:bg-red-700 text-white"
          >
            <Plus className="mr-2 h-4 w-4" />
            New Section
          </Button>
        </div>
      </div>

      {/* Info Alert */}
      <div className="bg-gray-800 text-white rounded-lg p-4">
        <p>
          Course structure allows you to organize your content into logical sections. 
          <strong> Each section can contain lessons, quizzes, and assignments.</strong>
        </p>
      </div>

      {/* Add Section Form */}
      {showAddSection && (
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>{editingSection ? 'Edit Section' : 'New Section'}</CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCancelAddSection}
            >
              <X className="h-4 w-4" />
            </Button>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSaveSection} className="space-y-4">
              <div>
                <label htmlFor="sectionTitle" className="block text-sm font-medium text-gray-700 mb-1">
                  Title
                </label>
                <input
                  id="sectionTitle"
                  type="text"
                  value={newSection.Title || ''}
                  onChange={(e) => setNewSection({ ...newSection, Title: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
                  maxLength={100}
                  required
                />
              </div>
              
              <div>
                <label htmlFor="sectionDescription" className="block text-sm font-medium text-gray-700 mb-1">
                  Learning Objectives
                </label>
                <input
                  id="sectionDescription"
                  type="text"
                  value={newSection.Description || ''}
                  onChange={(e) => setNewSection({ ...newSection, Description: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
                  maxLength={200}
                />
                <small className="text-gray-500 text-sm">
                  What will students learn in this section?
                </small>
              </div>

              <div className="flex space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancelAddSection}
                >
                  <RotateCcw className="mr-2 h-4 w-4" />
                  Reset
                </Button>
                <Button
                  type="submit"
                  disabled={loading}
                  className="bg-gray-800 hover:bg-gray-900 text-white"
                >
                  <Save className="mr-2 h-4 w-4" />
                  Save
                </Button>
              </div>
              
              {loading && (
                <div className="flex items-center text-gray-600">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                  Processing...
                </div>
              )}
            </form>
          </CardContent>
        </Card>
      )}

      {/* Sections List */}
      {sections.length === 0 ? (
        <Card>
          <CardContent className="py-12 text-center">
            <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No sections created yet</h3>
            <p className="text-gray-500 mb-4">
              Start building your course by adding your first section
            </p>
            <Button
              onClick={handleAddSection}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              <Plus className="mr-2 h-4 w-4" />
              Add First Section
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {sections.map((section, index) => (
            <Card key={section.Id || index} className="bg-gray-50">
              <CardHeader className="flex flex-row items-center justify-between">
                <div className="flex-1">
                  <h3 className="font-medium">
                    Section {section.Position}: {' '}
                    {section.Title.length > 80 
                      ? `${section.Title.substring(0, 77)}...` 
                      : section.Title
                    }
                  </h3>
                </div>
                
                <div className="flex items-center space-x-2">
                  {/* Management Menu Buttons */}
                  {managementMenus[index] && (
                    <>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleEditSection(section, index)}
                      >
                        <Edit className="mr-1 h-3 w-3" />
                        Edit
                      </Button>
                      
                      {(isRightType(section, ContentType.VIDEO) ||
                        isRightType(section, ContentType.PDF) ||
                        isRightType(section, ContentType.EBOOK)) && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleCreateContent(section, index)}
                        >
                          <BookOpen className="mr-1 h-3 w-3" />
                          Content
                        </Button>
                      )}
                      
                      {isRightType(section, ContentType.ASSIGNMENT) && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleCreateAssignment(section, index)}
                        >
                          <BookOpen className="mr-1 h-3 w-3" />
                          Assignment
                        </Button>
                      )}
                      
                      {isRightType(section, ContentType.QUIZ) && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleCreateQuiz(section, index)}
                        >
                          <HelpCircle className="mr-1 h-3 w-3" />
                          Quiz
                        </Button>
                      )}
                      
                      {/* Delete button - only for ASSIGNMENT sections as per Angular logic */}
                      {isRightType(section, ContentType.ASSIGNMENT) && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDeleteSection(section, index)}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="mr-1 h-3 w-3" />
                          Delete
                        </Button>
                      )}
                    </>
                  )}
                  
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={() => toggleManagementMenu(index)}
                  >
                    {managementMenus[index] ? (
                      <>
                        <X className="mr-1 h-3 w-3" />
                        Close
                      </>
                    ) : (
                      <>
                        <Menu className="mr-1 h-3 w-3" />
                        Menu
                      </>
                    )}
                  </Button>
                </div>
              </CardHeader>
              
              {/* Section Contents */}
              {section.Contents && section.Contents.length > 0 && (
                <CardContent>
                  <div className="space-y-2">
                    {section.Contents.map((content, contentIndex) => (
                      <div
                        key={content.Id || contentIndex}
                        className="border border-gray-200 rounded-lg"
                      >
                        <div
                          className="flex items-center justify-between p-3 cursor-pointer hover:bg-gray-50"
                          onClick={() => toggleSectionExpansion(content.Id!)}
                        >
                          <div className="flex items-center space-x-3">
                            {getContentTypeIcon(content.Type!)}
                            <span className="font-medium">
                              {getContentTypeLabel(content.Type!)} {content.Position}:
                            </span>
                            <span>
                              {content.Title.length > 80 
                                ? `${content.Title.substring(0, 77)}...` 
                                : content.Title
                              }
                            </span>
                          </div>
                          
                          {expandedSections.has(content.Id!) ? (
                            <ChevronDown className="h-4 w-4" />
                          ) : (
                            <ChevronRight className="h-4 w-4" />
                          )}
                        </div>
                        
                        {expandedSections.has(content.Id!) && (
                          <div className="border-t border-gray-200 p-4 bg-white">
                            <div className="space-y-4">
                              {/* Content Information */}
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                  <h4 className="font-semibold text-gray-900 mb-2">Content Details</h4>
                                  <div className="space-y-2 text-sm">
                                    <div className="flex justify-between">
                                      <span className="text-gray-600">Type:</span>
                                      <span className="font-medium">{getContentTypeLabel(content.Type!)}</span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-gray-600">Position:</span>
                                      <span className="font-medium">{content.Position}</span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-gray-600">Status:</span>
                                      <span className={`font-medium ${content.IsActive ? 'text-green-600' : 'text-red-600'}`}>
                                        {content.IsActive ? 'Active' : 'Inactive'}
                                      </span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-gray-600">Published:</span>
                                      <span className={`font-medium ${(content as any).IsPublished ? 'text-green-600' : 'text-orange-600'}`}>
                                        {(content as any).IsPublished ? 'Published' : 'Draft'}
                                      </span>
                                    </div>
                                    {content.Duration && (
                                      <div className="flex justify-between">
                                        <span className="text-gray-600">Duration:</span>
                                        <span className="font-medium">
                                          {Math.floor(content.Duration / 60)}:{(content.Duration % 60).toString().padStart(2, '0')}
                                        </span>
                                      </div>
                                    )}
                                    {content.TotalTime && (
                                      <div className="flex justify-between">
                                        <span className="text-gray-600">Total Time:</span>
                                        <span className="font-medium">{content.TotalTime} minutes</span>
                                      </div>
                                    )}
                                  </div>
                                </div>

                                <div>
                                  <h4 className="font-semibold text-gray-900 mb-2">Content Description</h4>
                                  <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
                                    {content.Description || content.Content || 'No description available'}
                                  </div>
                                </div>
                              </div>

                              {/* Content-specific details */}
                              {content.Type === ContentType.VIDEO && (
                                <div>
                                  <h4 className="font-semibold text-gray-900 mb-2">Video Details</h4>
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                    {(content as any).VideoUrl && (
                                      <div>
                                        <span className="text-gray-600">Video URL:</span>
                                        <div className="mt-1 p-2 bg-gray-50 rounded text-xs break-all">
                                          {(content as any).VideoUrl}
                                        </div>
                                      </div>
                                    )}
                                    {(content as any).YoutubeLink && (
                                      <div>
                                        <span className="text-gray-600">YouTube Link:</span>
                                        <div className="mt-1 p-2 bg-gray-50 rounded text-xs break-all">
                                          {(content as any).YoutubeLink}
                                        </div>
                                      </div>
                                    )}
                                    {(content as any).VimeoLink && (
                                      <div>
                                        <span className="text-gray-600">Vimeo Link:</span>
                                        <div className="mt-1 p-2 bg-gray-50 rounded text-xs break-all">
                                          {(content as any).VimeoLink}
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              )}

                              {content.Type === ContentType.QUIZ && (
                                <div>
                                  <h4 className="font-semibold text-gray-900 mb-2">Quiz Details</h4>
                                  <div className="text-sm">
                                    <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                                      <span className="text-gray-600">Time Limit:</span>
                                      <span className="font-medium text-blue-700">
                                        {content.TotalTime || 30} minutes
                                      </span>
                                    </div>
                                  </div>
                                </div>
                              )}

                              {content.Type === ContentType.ASSIGNMENT && (
                                <div>
                                  <h4 className="font-semibold text-gray-900 mb-2">Assignment Details</h4>
                                  <div className="text-sm">
                                    <div className="p-3 bg-purple-50 rounded-lg">
                                      <div className="flex justify-between items-center">
                                        <span className="text-gray-600">Instructions:</span>
                                        <span className="text-purple-700 font-medium">Available</span>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              )}

                              {/* Action Buttons */}
                              <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                                <div className="flex items-center space-x-2">
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => handleEditContent(content)}
                                    className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                                  >
                                    <Edit className="mr-1 h-3 w-3" />
                                    Edit Content
                                  </Button>

                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => handleDeleteContent(content)}
                                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                  >
                                    <Trash2 className="mr-1 h-3 w-3" />
                                    Delete
                                  </Button>
                                </div>

                                <div className="flex items-center space-x-2">
                                  <Button
                                    size="sm"
                                    variant={(content as any).IsPublished ? "default" : "secondary"}
                                    onClick={() => handleTogglePublish(content)}
                                  >
                                    {(content as any).IsPublished ? 'Unpublish' : 'Publish'}
                                  </Button>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              )}
            </Card>
          ))}
        </div>
      )}

      {/* Content Creation Modals */}
      {selectedSection && (
        <>
          <ContentCreationModal
            isOpen={contentModalOpen}
            onClose={() => {
              setContentModalOpen(false);
              setSelectedSection(null);
            }}
            section={selectedSection}
            course={course}
            onContentCreated={(updatedCourse) => {
              setSections(updatedCourse.Sections || []);
              onCourseUpdate(updatedCourse);
            }}
          />

          <QuizCreationModal
            isOpen={quizModalOpen}
            onClose={() => {
              setQuizModalOpen(false);
              setSelectedSection(null);
            }}
            section={selectedSection}
            course={course}
            onQuizCreated={(updatedCourse) => {
              setSections(updatedCourse.Sections || []);
              onCourseUpdate(updatedCourse);
            }}
          />

          <AssignmentCreationModal
            isOpen={assignmentModalOpen}
            onClose={() => {
              setAssignmentModalOpen(false);
              setSelectedSection(null);
            }}
            section={selectedSection}
            course={course}
            onAssignmentCreated={(updatedCourse) => {
              setSections(updatedCourse.Sections || []);
              onCourseUpdate(updatedCourse);
            }}
          />
        </>
      )}
    </div>
  );
};

export default ModernCourseStructureManager;
