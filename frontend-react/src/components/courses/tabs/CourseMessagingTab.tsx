import React from 'react';
import type { CourseFormData } from '../../../models/course.model';

// shadcn/ui components
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../ui/card';
import { Textarea } from '../../ui/textarea';
import { Label } from '../../ui/label';
import { Alert, AlertDescription } from '../../ui/alert';
import { Badge } from '../../ui/badge';

// Icons
import { MessageSquare, AlertCircle, Info, Heart, Sparkles } from 'lucide-react';

interface CourseMessagingTabProps {
  data: CourseFormData;
  onChange: (data: CourseFormData) => void;
  errors: string[];
}

const CourseMessagingTab: React.FC<CourseMessagingTabProps> = ({
  data,
  onChange,
  errors
}) => {
  // Helper functions
  const updateData = (updates: Partial<CourseFormData>) => {
    onChange({ ...data, ...updates });
  };

  const handleMessageChange = (value: string) => {
    updateData({ Message: value });
  };

  const handleCongratulationChange = (value: string) => {
    updateData({ Congratulation: value });
  };

  // Character limits
  const MESSAGE_LIMIT = 250;
  const CONGRATULATION_LIMIT = 250;

  // Sample messages for inspiration
  const sampleWelcomeMessages = [
    "Welcome to this exciting learning journey! I'm thrilled to have you here and can't wait to share my knowledge with you.",
    "Hello and welcome! You've made a great decision by enrolling in this course. Let's achieve your learning goals together!",
    "Welcome aboard! I'm excited to guide you through this comprehensive course. Get ready to transform your skills!",
    "Hi there! Welcome to our learning community. I'm here to support you every step of the way on this educational adventure."
  ];

  const sampleCongratulationMessages = [
    "Congratulations on completing the course! You've shown dedication and commitment. I'm proud of your achievement!",
    "Well done! You've successfully completed all the lessons. Your hard work has paid off. Keep applying what you've learned!",
    "Amazing work! You've reached the finish line. Take a moment to celebrate your accomplishment and continue growing!",
    "Fantastic! You've completed the entire course. Your persistence and effort are truly commendable. Best of luck in your future endeavors!"
  ];

  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <div className="border-b border-gray-200 pb-6">
        <div className="flex items-center space-x-3 mb-3">
          <div className="p-2 bg-red-100 rounded-lg">
            <MessageSquare className="w-6 h-6 text-red-600" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Course Messages</h2>
            <p className="text-gray-600 mt-1">
              Create personalized messages to welcome students and congratulate them on completion.
            </p>
          </div>
        </div>
      </div>

      {/* Errors */}
      {errors.length > 0 && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <ul className="list-disc list-inside space-y-1">
              {errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      {/* Info Alert */}
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          <div className="space-y-2">
            <p className="font-semibold">Why Course Messages Matter:</p>
            <ul className="text-sm space-y-1">
              <li>• Welcome messages create a positive first impression</li>
              <li>• Congratulation messages motivate students to complete the course</li>
              <li>• Personal touches help build stronger instructor-student relationships</li>
              <li>• These messages are optional but highly recommended</li>
            </ul>
          </div>
        </AlertDescription>
      </Alert>

      {/* Welcome Message */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Heart className="w-5 h-5 text-red-500" />
            <span>Welcome Message</span>
            <Badge variant="secondary">Optional</Badge>
          </CardTitle>
          <CardDescription>
            This message will be shown to students when they first enroll in your course.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="welcome-message">Welcome Message</Label>
            <Textarea
              id="welcome-message"
              value={data.Message || ''}
              onChange={(e) => handleMessageChange(e.target.value)}
              placeholder="Write a warm welcome message for your students..."
              rows={5}
              maxLength={MESSAGE_LIMIT}
              className="resize-none"
            />
            <div className="flex justify-between items-center text-sm">
              <span className="text-gray-500">
                This message will appear when students first access your course
              </span>
              <span className={`${(data.Message?.length || 0) > MESSAGE_LIMIT * 0.9 ? 'text-red-500' : 'text-gray-500'}`}>
                {data.Message?.length || 0}/{MESSAGE_LIMIT}
              </span>
            </div>
          </div>

          {/* Sample Messages */}
          <div className="space-y-3">
            <h4 className="font-semibold text-sm">Sample Welcome Messages:</h4>
            <div className="grid gap-3">
              {sampleWelcomeMessages.map((message, index) => (
                <div
                  key={index}
                  className="p-3 bg-gray-50 rounded-lg border cursor-pointer hover:bg-gray-100 transition-colors"
                  onClick={() => handleMessageChange(message)}
                >
                  <p className="text-sm text-gray-700">{message}</p>
                  <p className="text-xs text-gray-500 mt-1">Click to use this message</p>
                </div>
              ))}
            </div>
          </div>

          {/* Preview */}
          {data.Message && (
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-semibold text-blue-800 mb-2">Preview:</h4>
              <div className="bg-white p-3 rounded border">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
                  <div>
                    <div className="font-semibold text-sm">Instructor</div>
                    <div className="text-xs text-gray-500">Welcome message</div>
                  </div>
                </div>
                <p className="text-sm">{data.Message}</p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Congratulation Message */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Sparkles className="w-5 h-5 text-yellow-500" />
            <span>Congratulation Message</span>
            <Badge variant="secondary">Optional</Badge>
          </CardTitle>
          <CardDescription>
            This message will be shown to students when they complete your course.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="congratulation-message">Congratulation Message</Label>
            <Textarea
              id="congratulation-message"
              value={data.Congratulation || ''}
              onChange={(e) => handleCongratulationChange(e.target.value)}
              placeholder="Write a congratulatory message for students who complete your course..."
              rows={5}
              maxLength={CONGRATULATION_LIMIT}
              className="resize-none"
            />
            <div className="flex justify-between items-center text-sm">
              <span className="text-gray-500">
                This message will appear when students complete all course content
              </span>
              <span className={`${(data.Congratulation?.length || 0) > CONGRATULATION_LIMIT * 0.9 ? 'text-red-500' : 'text-gray-500'}`}>
                {data.Congratulation?.length || 0}/{CONGRATULATION_LIMIT}
              </span>
            </div>
          </div>

          {/* Sample Messages */}
          <div className="space-y-3">
            <h4 className="font-semibold text-sm">Sample Congratulation Messages:</h4>
            <div className="grid gap-3">
              {sampleCongratulationMessages.map((message, index) => (
                <div
                  key={index}
                  className="p-3 bg-gray-50 rounded-lg border cursor-pointer hover:bg-gray-100 transition-colors"
                  onClick={() => handleCongratulationChange(message)}
                >
                  <p className="text-sm text-gray-700">{message}</p>
                  <p className="text-xs text-gray-500 mt-1">Click to use this message</p>
                </div>
              ))}
            </div>
          </div>

          {/* Preview */}
          {data.Congratulation && (
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <h4 className="font-semibold text-green-800 mb-2">Preview:</h4>
              <div className="bg-white p-3 rounded border">
                <div className="flex items-center space-x-2 mb-2">
                  <Sparkles className="w-6 h-6 text-yellow-500" />
                  <div>
                    <div className="font-semibold text-sm">Course Completed!</div>
                    <div className="text-xs text-gray-500">Congratulations message</div>
                  </div>
                </div>
                <p className="text-sm">{data.Congratulation}</p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Best Practices */}
      <Card>
        <CardHeader>
          <CardTitle>Best Practices for Course Messages</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-2">Welcome Message Tips:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Be warm and enthusiastic</li>
                <li>• Introduce yourself briefly</li>
                <li>• Set expectations for the course</li>
                <li>• Encourage students to engage</li>
                <li>• Keep it concise but personal</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Congratulation Message Tips:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Acknowledge their achievement</li>
                <li>• Encourage continued learning</li>
                <li>• Suggest next steps or resources</li>
                <li>• Express pride in their progress</li>
                <li>• Invite them to stay connected</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CourseMessagingTab;
