import React, { useState } from 'react';
import type { CourseFormData, ContentType, CourseFormatOption, CourseLanguageOption, CourseLevelOption } from '../../../models/course.model';
import type { Category } from '../../../models/category.model';

// shadcn/ui components
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../ui/card';
import { Input } from '../../ui/input';
import { Label } from '../../ui/label';
import { Textarea } from '../../ui/textarea';
import { Button } from '../../ui/button';
import { Badge } from '../../ui/badge';
import { Alert, AlertDescription } from '../../ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../ui/select';
import { Checkbox } from '../../ui/checkbox';

// Icons
import { Plus, X, AlertCircle, Video, FileText, BookOpen, Info } from 'lucide-react';

interface CourseInformationTabProps {
  data: CourseFormData;
  onChange: (data: CourseFormData) => void;
  categories: Category[];
  errors: string[];
}

const CourseInformationTab: React.FC<CourseInformationTabProps> = ({
  data,
  onChange,
  categories,
  errors
}) => {
  // Course format options (matching Angular)
  const formatOptions: CourseFormatOption[] = [
    {
      id: 2, // VIDEO
      title: 'Video Course',
      description: 'Create a course with video lessons and multimedia content',
      icon: 'video'
    },
    {
      id: 1, // PDF
      title: 'Document Course',
      description: 'Create a course with PDF documents and text-based content',
      icon: 'document'
    },
    {
      id: 3, // EBOOK
      title: 'E-book Course',
      description: 'Create an interactive e-book style course',
      icon: 'book'
    }
  ];

  // Language options (matching Angular)
  const languageOptions: CourseLanguageOption[] = [
    { code: 'en', name: 'English' },
    { code: 'fr', name: 'Français' },
    { code: 'es', name: 'Español' },
    { code: 'de', name: 'Deutsch' },
    { code: 'it', name: 'Italiano' },
    { code: 'pt', name: 'Português' },
    { code: 'ru', name: 'Русский' },
    { code: 'zh', name: '中文' },
    { code: 'ja', name: '日本語' },
    { code: 'ko', name: '한국어' }
  ];

  // Level options (matching Angular)
  const levelOptions: CourseLevelOption[] = [
    { id: 1, name: 'Beginner', description: 'No prior experience required' },
    { id: 2, name: 'Intermediate', description: 'Some experience recommended' },
    { id: 3, name: 'Advanced', description: 'Extensive experience required' },
    { id: 4, name: 'Expert', description: 'Professional level expertise' }
  ];

  // Local state for input fields
  const [newKeyword, setNewKeyword] = useState('');
  const [newGoal, setNewGoal] = useState('');
  const [newPrerequisite, setNewPrerequisite] = useState('');

  // Helper functions
  const updateData = (updates: Partial<CourseFormData>) => {
    onChange({ ...data, ...updates });
  };

  const addKeyword = () => {
    if (newKeyword.trim() && !data.Keywords.includes(newKeyword.trim())) {
      updateData({ Keywords: [...data.Keywords, newKeyword.trim()] });
      setNewKeyword('');
    }
  };

  const removeKeyword = (index: number) => {
    updateData({ Keywords: data.Keywords.filter((_, i) => i !== index) });
  };

  const addGoal = () => {
    if (newGoal.trim() && !data.Goals.includes(newGoal.trim())) {
      updateData({ Goals: [...data.Goals, newGoal.trim()] });
      setNewGoal('');
    }
  };

  const removeGoal = (index: number) => {
    updateData({ Goals: data.Goals.filter((_, i) => i !== index) });
  };

  const addPrerequisite = () => {
    if (newPrerequisite.trim() && !data.Prerequisites.includes(newPrerequisite.trim())) {
      updateData({ Prerequisites: [...data.Prerequisites, newPrerequisite.trim()] });
      setNewPrerequisite('');
    }
  };

  const removePrerequisite = (index: number) => {
    updateData({ Prerequisites: data.Prerequisites.filter((_, i) => i !== index) });
  };

  const toggleCategory = (category: Category) => {
    const isSelected = data.Categories.some(c => c.Id === category.Id);
    if (isSelected) {
      updateData({ Categories: data.Categories.filter(c => c.Id !== category.Id) });
    } else {
      updateData({ Categories: [...data.Categories, category] });
    }
  };

  const toggleLevel = (levelId: number) => {
    const isSelected = data.Level.includes(levelId);
    if (isSelected) {
      updateData({ Level: data.Level.filter(id => id !== levelId) });
    } else {
      updateData({ Level: [...data.Level, levelId] });
    }
  };

  const getFormatIcon = (format: number) => {
    switch (format) {
      case 2: return <Video className="w-5 h-5" />;
      case 1: return <FileText className="w-5 h-5" />;
      case 3: return <BookOpen className="w-5 h-5" />;
      default: return <Video className="w-5 h-5" />;
    }
  };

  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <div className="border-b border-gray-200 pb-6">
        <div className="flex items-center space-x-3 mb-3">
          <div className="p-2 bg-red-100 rounded-lg">
            <Info className="w-6 h-6 text-red-600" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Course Information</h2>
            <p className="text-gray-600 mt-1">
              Provide the basic information about your course. This information will help students understand what your course is about.
            </p>
          </div>
        </div>
      </div>

      {/* Errors */}
      {errors.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <AlertCircle className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="text-sm font-semibold text-red-800 mb-2">Please fix the following errors:</h3>
              <ul className="list-disc list-inside space-y-1 text-sm text-red-700">
                {errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}

      {/* Course Format Selection */}
      <div className="bg-gray-50 rounded-xl p-6">
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Course Format</h3>
          <p className="text-gray-600">Choose the primary format for your course content</p>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {formatOptions.map((format) => (
            <div
              key={format.id}
              className={`p-5 border-2 rounded-xl cursor-pointer transition-all duration-200 bg-white ${
                data.Format === format.id
                  ? 'border-red-500 bg-red-50 shadow-md ring-2 ring-red-200'
                  : 'border-gray-200 hover:border-red-300 hover:shadow-md'
              }`}
              onClick={() => updateData({ Format: format.id as ContentType })}
            >
              <div className="flex items-center space-x-3 mb-3">
                <div className={`p-2 rounded-lg ${
                  data.Format === format.id ? 'bg-red-100' : 'bg-gray-100'
                }`}>
                  {getFormatIcon(format.id)}
                </div>
                <h3 className="font-semibold text-gray-900">{format.title}</h3>
              </div>
              <p className="text-sm text-gray-600 leading-relaxed">{format.description}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Basic Information */}
      <div className="bg-white rounded-xl border border-gray-200 shadow-sm">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Basic Information</h3>
        </div>
        <div className="p-6 space-y-6">
          {/* Course Title */}
          <div className="space-y-3">
            <Label htmlFor="title" className="text-sm font-semibold text-gray-900">
              Course Title <span className="text-red-500">*</span>
            </Label>
            <Input
              id="title"
              value={data.Title}
              onChange={(e) => updateData({ Title: e.target.value })}
              placeholder="Enter your course title"
              maxLength={100}
              className={`h-12 text-base ${errors.some(e => e.includes('Title')) ? 'border-red-500 focus:border-red-500 focus:ring-red-200' : 'border-gray-300 focus:border-red-500 focus:ring-red-200'}`}
            />
            <div className="flex justify-between items-center">
              <p className="text-sm text-gray-500">
                Choose a clear, descriptive title for your course
              </p>
              <p className="text-sm text-gray-500">
                {data.Title.length}/100 characters
              </p>
            </div>
          </div>

          {/* Course Description */}
          <div className="space-y-3">
            <Label htmlFor="resume" className="text-sm font-semibold text-gray-900">
              Course Description <span className="text-red-500">*</span>
            </Label>
            <Textarea
              id="resume"
              value={data.Resume}
              onChange={(e) => updateData({ Resume: e.target.value })}
              placeholder="Describe what your course is about, what students will learn, and why they should take it"
              rows={6}
              className={`text-base resize-none ${errors.some(e => e.includes('Description')) ? 'border-red-500 focus:border-red-500 focus:ring-red-200' : 'border-gray-300 focus:border-red-500 focus:ring-red-200'}`}
            />
            <p className="text-sm text-gray-500">
              Provide a detailed description that helps students understand the value of your course
            </p>
          </div>
        </div>
      </div>

      {/* Keywords */}
      <div className="bg-white rounded-xl border border-gray-200 shadow-sm">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Student Target Keywords</h3>
          <p className="text-sm text-gray-600 mt-1">Add keywords that describe what students will learn</p>
        </div>
        <div className="p-6 space-y-4">
          <div className="flex space-x-3">
            <Input
              value={newKeyword}
              onChange={(e) => setNewKeyword(e.target.value)}
              placeholder="Add a keyword (e.g., React, JavaScript, Web Development)"
              onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addKeyword())}
              className="h-12 text-base border-gray-300 focus:border-red-500 focus:ring-red-200"
            />
            <Button
              type="button"
              onClick={addKeyword}
              className="h-12 px-6 bg-red-600 hover:bg-red-700"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add
            </Button>
          </div>
          <div className="flex flex-wrap gap-2">
            {data.Keywords.map((keyword, index) => (
              <Badge key={index} variant="secondary" className="flex items-center space-x-2 px-3 py-1.5 bg-red-50 text-red-700 border border-red-200">
                <span className="font-medium">{keyword}</span>
                <button
                  type="button"
                  onClick={() => removeKeyword(index)}
                  className="hover:text-red-900 transition-colors"
                >
                  <X className="w-3 h-3" />
                </button>
              </Badge>
            ))}
          </div>
          <p className="text-sm text-gray-500">
            Press Enter to add keywords or use the Add button. Keywords help students find your course.
          </p>
        </div>
      </div>

      {/* Course Settings */}
      <div className="bg-white rounded-xl border border-gray-200 shadow-sm">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Course Settings</h3>
          <p className="text-sm text-gray-600 mt-1">Configure language, difficulty level, and categories</p>
        </div>
        <div className="p-6 space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Language */}
            <div className="space-y-2">
              <Label>Language</Label>
              <Select value={data.Language} onValueChange={(value) => updateData({ Language: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select language" />
                </SelectTrigger>
                <SelectContent>
                  {languageOptions.map((lang) => (
                    <SelectItem key={lang.code} value={lang.code}>
                      {lang.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-sm text-gray-500">
                The language in which the course will be published
              </p>
            </div>

            {/* Level */}
            <div className="space-y-2">
              <Label>Course Level *</Label>
              <div className="space-y-2">
                {levelOptions.map((level) => (
                  <div key={level.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`level-${level.id}`}
                      checked={data.Level.includes(level.id)}
                      onCheckedChange={() => toggleLevel(level.id)}
                    />
                    <Label htmlFor={`level-${level.id}`} className="flex-1">
                      <div>
                        <div className="font-medium">{level.name}</div>
                        <div className="text-sm text-gray-500">{level.description}</div>
                      </div>
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Categories */}
            <div className="space-y-2">
              <Label>Categories *</Label>
              <div className="space-y-2 max-h-48 overflow-y-auto">
                {categories.map((category) => (
                  <div key={category.Id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`category-${category.Id}`}
                      checked={data.Categories.some(c => c.Id === category.Id)}
                      onCheckedChange={() => toggleCategory(category)}
                    />
                    <Label htmlFor={`category-${category.Id}`} className="flex-1">
                      {category.Title}
                    </Label>
                  </div>
                ))}
              </div>
              <p className="text-sm text-gray-500">
                Select the categories that best describe your course
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Learning Objectives */}
      <div className="bg-white rounded-xl border border-gray-200 shadow-sm">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Learning Objectives</h3>
          <p className="text-sm text-gray-600 mt-1">Define what students will learn and what they need to know beforehand</p>
        </div>
        <div className="p-6 space-y-6">
          {/* What students will learn */}
          <div className="space-y-2">
            <Label>What will students learn? *</Label>
            <div className="space-y-3">
              <div className="flex space-x-2">
                <Input
                  value={newGoal}
                  onChange={(e) => setNewGoal(e.target.value)}
                  placeholder="Add a learning goal"
                  onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addGoal())}
                />
                <Button type="button" onClick={addGoal} size="sm">
                  <Plus className="w-4 h-4" />
                </Button>
              </div>
              <div className="space-y-2">
                {data.Goals.map((goal, index) => (
                  <div key={index} className="flex items-center space-x-2 p-2 bg-gray-50 rounded">
                    <span className="flex-1">{goal}</span>
                    <button
                      type="button"
                      onClick={() => removeGoal(index)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
              <p className="text-sm text-gray-500">
                Press Enter to add each learning goal
              </p>
            </div>
          </div>

          {/* Prerequisites */}
          <div className="space-y-2">
            <Label>Prerequisites *</Label>
            <div className="space-y-3">
              <div className="flex space-x-2">
                <Input
                  value={newPrerequisite}
                  onChange={(e) => setNewPrerequisite(e.target.value)}
                  placeholder="Add a prerequisite"
                  onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addPrerequisite())}
                />
                <Button type="button" onClick={addPrerequisite} size="sm">
                  <Plus className="w-4 h-4" />
                </Button>
              </div>
              <div className="space-y-2">
                {data.Prerequisites.map((prerequisite, index) => (
                  <div key={index} className="flex items-center space-x-2 p-2 bg-gray-50 rounded">
                    <span className="flex-1">{prerequisite}</span>
                    <button
                      type="button"
                      onClick={() => removePrerequisite(index)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
              <p className="text-sm text-gray-500">
                Press Enter to add each prerequisite
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CourseInformationTab;
