import React, { useState, useRef, useEffect } from 'react';
import type { CourseFormData } from '../../../models/course.model';
import type { Media } from '../../../models/user.model';
import mediaService from '../../../services/media.service';

// shadcn/ui components
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../ui/card';
import { Button } from '../../ui/button';
import { Input } from '../../ui/input';
import { Label } from '../../ui/label';
import { Alert, AlertDescription } from '../../ui/alert';
import { Progress } from '../../ui/progress';

// Icons
import { Upload, Image as ImageIcon, Video, X, AlertCircle, Camera } from 'lucide-react';

interface CoursePresentationTabProps {
  data: CourseFormData;
  onChange: (data: CourseFormData) => void;
  errors: string[];
}

const CoursePresentationTab: React.FC<CoursePresentationTabProps> = ({
  data,
  onChange,
  errors
}) => {
  // State
  const [uploadingCover, setUploadingCover] = useState(false);
  const [uploadingVideo, setUploadingVideo] = useState(false);
  const [uploadProgress, setUploadProgress] = useState({ cover: 0, video: 0 });
  const [coverPreview, setCoverPreview] = useState<string | null>(null);
  const [videoPreview, setVideoPreview] = useState<string | null>(null);
  const [imageLoadError, setImageLoadError] = useState(false);
  const [videoLoadError, setVideoLoadError] = useState(false);

  // Refs
  const coverInputRef = useRef<HTMLInputElement>(null);
  const videoInputRef = useRef<HTMLInputElement>(null);

  // Cleanup object URLs on unmount to prevent memory leaks
  useEffect(() => {
    return () => {
      if (coverPreview) {
        URL.revokeObjectURL(coverPreview);
      }
      if (videoPreview) {
        URL.revokeObjectURL(videoPreview);
      }
    };
  }, [coverPreview, videoPreview]);

  // Helper functions
  const updateData = (updates: Partial<CourseFormData>) => {
    onChange({ ...data, ...updates });
  };

  // Cover image upload
  const handleCoverImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      console.error('Invalid file type. Please select an image file.');
      return;
    }

    // Validate file size (10MB)
    if (file.size > 10 * 1024 * 1024) {
      console.error('File too large. Please select an image under 10MB.');
      return;
    }

    try {
      setUploadingCover(true);
      setUploadProgress(prev => ({ ...prev, cover: 0 }));
      setImageLoadError(false);

      // Clean up previous preview URL
      if (coverPreview) {
        URL.revokeObjectURL(coverPreview);
      }

      // Create preview
      const previewUrl = URL.createObjectURL(file);
      console.log('✅ Created cover image blob URL:', previewUrl);
      setCoverPreview(previewUrl);

      // Upload file
      let uploadedFile;
      try {
        uploadedFile = await mediaService.uploadFile(file, {
          onProgress: (progress) => {
            setUploadProgress(prev => ({ ...prev, cover: progress.percentage }));
          },
          maxSize: 10 * 1024 * 1024, // 10MB
          allowedTypes: ['image/jpeg', 'image/png', 'image/webp'],
          context: 'course-cover',
          courseSlug: course?.Slug // Use the course slug for organized directory structure
        });
      } catch (uploadError) {
        console.warn('Media service upload failed, using preview URL:', uploadError);
        // Fallback: use the preview URL as the main URL
        uploadedFile = {
          id: Date.now().toString(),
          filename: `cover_${Date.now()}.${file.name.split('.').pop()}`,
          subDir: 'coverimage',
          mimeType: file.type,
          size: file.size,
          originalUsername: file.name,
          url: previewUrl
        };
      }

      // Update course data
      const coverImage: Media = {
        Id: parseInt(uploadedFile.id),
        Hashname: uploadedFile.filename,
        SubDir: uploadedFile.subDir || 'coverimage',
        Type: uploadedFile.mimeType,
        Size: uploadedFile.size,
        OriginalUsername: uploadedFile.originalUsername || file.name,
        Url: uploadedFile.url
      };

      updateData({ CoverImage: coverImage });

      console.log('✅ Cover image uploaded successfully:', coverImage);

    } catch (error) {
      console.error('❌ Cover image upload failed:', error);
      setCoverPreview(null);
      alert('Failed to upload cover image. Please try again.');
    } finally {
      setUploadingCover(false);
      setUploadProgress(prev => ({ ...prev, cover: 0 }));
    }
  };

  // Presentation video upload
  const handlePresentationVideoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('video/')) {
      console.error('Invalid file type. Please select a video file.');
      return;
    }

    // Validate file size (500MB)
    if (file.size > 500 * 1024 * 1024) {
      console.error('File too large. Please select a video under 500MB.');
      return;
    }

    try {
      setUploadingVideo(true);
      setUploadProgress(prev => ({ ...prev, video: 0 }));
      setVideoLoadError(false);

      // Clean up previous preview URL
      if (videoPreview) {
        URL.revokeObjectURL(videoPreview);
      }

      // Create preview
      const previewUrl = URL.createObjectURL(file);
      console.log('✅ Created video blob URL:', previewUrl);
      setVideoPreview(previewUrl);

      // Upload file
      let uploadedFile;
      try {
        uploadedFile = await mediaService.uploadFile(file, {
          onProgress: (progress) => {
            setUploadProgress(prev => ({ ...prev, video: progress.percentage }));
          },
          maxSize: 500 * 1024 * 1024, // 500MB
          allowedTypes: ['video/mp4', 'video/webm', 'video/ogg'],
          context: 'course-video',
          courseSlug: course?.Slug // Use the course slug for organized directory structure
        });
      } catch (uploadError) {
        console.warn('Media service upload failed, using preview URL:', uploadError);
        // Fallback: use the preview URL as the main URL
        uploadedFile = {
          id: Date.now().toString(),
          filename: `video_${Date.now()}.${file.name.split('.').pop()}`,
          subDir: 'videos',
          mimeType: file.type,
          size: file.size,
          originalUsername: file.name,
          url: previewUrl
        };
      }

      // Update course data
      const presentationVideo: Media = {
        Id: parseInt(uploadedFile.id),
        Hashname: uploadedFile.filename,
        SubDir: uploadedFile.subDir || 'videos',
        Type: uploadedFile.mimeType,
        Size: uploadedFile.size,
        OriginalUsername: uploadedFile.originalUsername || file.name,
        Url: uploadedFile.url
      };

      updateData({ PresentationVideo: presentationVideo });

      console.log('✅ Presentation video uploaded successfully:', presentationVideo);

    } catch (error) {
      console.error('❌ Presentation video upload failed:', error);
      setVideoPreview(null);
      alert('Failed to upload presentation video. Please try again.');
    } finally {
      setUploadingVideo(false);
      setUploadProgress(prev => ({ ...prev, video: 0 }));
    }
  };

  // Remove cover image
  const removeCoverImage = () => {
    if (coverPreview) {
      URL.revokeObjectURL(coverPreview);
      setCoverPreview(null);
    }
    updateData({ CoverImage: undefined });
    setImageLoadError(false);
    if (coverInputRef.current) {
      coverInputRef.current.value = '';
    }
  };

  // Remove presentation video
  const removePresentationVideo = () => {
    if (videoPreview) {
      URL.revokeObjectURL(videoPreview);
      setVideoPreview(null);
    }
    updateData({ PresentationVideo: undefined });
    setVideoLoadError(false);
    if (videoInputRef.current) {
      videoInputRef.current.value = '';
    }
  };

  // Get display URL for existing media
  const getCoverImageUrl = () => {
    const url = coverPreview || data.CoverImage?.Url || null;
    if (url) {
      console.log('Cover image URL:', url);
    }
    return url;
  };

  const getVideoUrl = () => {
    const url = videoPreview || data.PresentationVideo?.Url || null;
    if (url) {
      console.log('Video URL:', url);
    }
    return url;
  };

  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <div className="border-b border-gray-200 pb-6">
        <div className="flex items-center space-x-3 mb-3">
          <div className="p-2 bg-red-100 rounded-lg">
            <Camera className="w-6 h-6 text-red-600" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Course Presentation</h2>
            <p className="text-gray-600 mt-1">
              Upload a cover image and presentation video to make your course more appealing to students.
            </p>
          </div>
        </div>
      </div>

      {/* Errors */}
      {errors.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <AlertCircle className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="text-sm font-semibold text-red-800 mb-2">Please fix the following errors:</h3>
              <ul className="list-disc list-inside space-y-1 text-sm text-red-700">
                {errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}

      {/* Cover Image */}
      <div className="bg-white rounded-xl border border-gray-200 shadow-sm">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Course Cover Image</h3>
          <p className="text-sm text-gray-600 mt-1">
            Upload an attractive cover image for your course. Recommended size: 1280x720 pixels.
          </p>
        </div>
        <div className="p-6 space-y-4">
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
            {/* Preview */}
            <div className="space-y-4">
              {getCoverImageUrl() && !imageLoadError ? (
                <div className="relative">
                  <img
                    src={getCoverImageUrl()!}
                    alt="Course Cover Preview"
                    className="w-full h-48 object-cover rounded-lg border"
                    onError={() => {
                      console.error('Failed to load cover image:', getCoverImageUrl());
                      setImageLoadError(true);
                    }}
                    onLoad={() => setImageLoadError(false)}
                  />
                  <Button
                    variant="destructive"
                    size="sm"
                    className="absolute top-2 right-2"
                    onClick={removeCoverImage}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              ) : (
                <div className="w-full h-48 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <ImageIcon className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-500">
                      {imageLoadError ? 'Failed to load image' : 'No cover image uploaded'}
                    </p>
                    {imageLoadError && getCoverImageUrl() && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="mt-2"
                        onClick={() => {
                          setImageLoadError(false);
                          // Force reload
                          const img = new Image();
                          img.src = getCoverImageUrl()!;
                        }}
                      >
                        Retry
                      </Button>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Upload */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="cover-image">Cover Image</Label>
                <div className="flex space-x-2">
                  <Input
                    id="cover-image"
                    type="file"
                    ref={coverInputRef}
                    accept="image/*"
                    onChange={handleCoverImageUpload}
                    disabled={uploadingCover}
                    className="hidden"
                  />
                  <Input
                    value={data.CoverImage?.OriginalUsername || ''}
                    placeholder="No file selected"
                    readOnly
                    className="flex-1"
                  />
                  <Button
                    type="button"
                    onClick={() => coverInputRef.current?.click()}
                    disabled={uploadingCover}
                  >
                    <Upload className="w-4 h-4 mr-2" />
                    Upload
                  </Button>
                </div>
              </div>

              {uploadingCover && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Uploading...</span>
                    <span>{Math.round(uploadProgress.cover)}%</span>
                  </div>
                  <Progress value={uploadProgress.cover} className="w-full" />
                </div>
              )}

              <div className="text-sm text-gray-500 space-y-1">
                <p>• Recommended size: 1280x720 pixels</p>
                <p>• Supported formats: JPG, PNG, WebP</p>
                <p>• Maximum file size: 10MB</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Presentation Video */}
      <div className="bg-white rounded-xl border border-gray-200 shadow-sm">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Presentation Video</h3>
          <p className="text-sm text-gray-600 mt-1">
            Upload a short presentation video to introduce your course to potential students.
          </p>
        </div>
        <div className="p-6 space-y-4">
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
            {/* Preview */}
            <div className="space-y-4">
              {getVideoUrl() && !videoLoadError ? (
                <div className="relative">
                  <video
                    src={getVideoUrl()!}
                    className="w-full h-48 object-cover rounded-lg border"
                    controls
                    preload="metadata"
                    onError={(e) => {
                      console.error('Failed to load video:', getVideoUrl());
                      setVideoLoadError(true);
                    }}
                    onLoadedData={() => setVideoLoadError(false)}
                  >
                    Your browser does not support the video tag.
                  </video>
                  <Button
                    variant="destructive"
                    size="sm"
                    className="absolute top-2 right-2"
                    onClick={removePresentationVideo}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              ) : (
                <div className="w-full h-48 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <Video className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-500">
                      {videoLoadError ? 'Failed to load video' : 'No presentation video uploaded'}
                    </p>
                    {videoLoadError && getVideoUrl() && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="mt-2"
                        onClick={() => {
                          setVideoLoadError(false);
                          // Force reload by creating a new video element
                          const video = document.createElement('video');
                          video.onloadeddata = () => setVideoLoadError(false);
                          video.onerror = () => setVideoLoadError(true);
                          video.src = getVideoUrl()!;
                        }}
                      >
                        Retry
                      </Button>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Upload */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="presentation-video">Presentation Video</Label>
                <div className="flex space-x-2">
                  <Input
                    id="presentation-video"
                    type="file"
                    ref={videoInputRef}
                    accept="video/*"
                    onChange={handlePresentationVideoUpload}
                    disabled={uploadingVideo}
                    className="hidden"
                  />
                  <Input
                    value={data.PresentationVideo?.OriginalUsername || ''}
                    placeholder="No file selected"
                    readOnly
                    className="flex-1"
                  />
                  <Button
                    type="button"
                    onClick={() => videoInputRef.current?.click()}
                    disabled={uploadingVideo}
                  >
                    <Upload className="w-4 h-4 mr-2" />
                    Upload
                  </Button>
                </div>
              </div>

              {uploadingVideo && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Uploading...</span>
                    <span>{Math.round(uploadProgress.video)}%</span>
                  </div>
                  <Progress value={uploadProgress.video} className="w-full" />
                </div>
              )}

              <div className="text-sm text-gray-500 space-y-1">
                <p>• Recommended length: 1-3 minutes</p>
                <p>• Supported formats: MP4, WebM, OGG</p>
                <p>• Maximum file size: 500MB</p>
                <p>• Keep it engaging and informative</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tips */}
      <div className="bg-white rounded-xl border border-gray-200 shadow-sm">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Tips for Great Course Presentation</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-2">Cover Image Tips:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Use high-quality, professional images</li>
                <li>• Include course title or key concepts</li>
                <li>• Use contrasting colors for readability</li>
                <li>• Avoid cluttered or busy designs</li>
                <li>• Test how it looks on different devices</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Presentation Video Tips:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Introduce yourself and your expertise</li>
                <li>• Explain what students will learn</li>
                <li>• Keep it concise and engaging</li>
                <li>• Use good lighting and clear audio</li>
                <li>• End with a call to action</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CoursePresentationTab;
