import React from 'react';
import type { CourseFormData } from '../../../models/course.model';

// shadcn/ui components
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../ui/card';
import { Input } from '../../ui/input';
import { Label } from '../../ui/label';
import { Checkbox } from '../../ui/checkbox';
import { Alert, AlertDescription } from '../../ui/alert';
import { Badge } from '../../ui/badge';

// Icons
import { DollarSign, AlertCircle, Info, TrendingUp, Gift } from 'lucide-react';

interface CoursePricingTabProps {
  data: CourseFormData;
  onChange: (data: CourseFormData) => void;
  errors: string[];
}

const CoursePricingTab: React.FC<CoursePricingTabProps> = ({
  data,
  onChange,
  errors
}) => {
  // Helper functions
  const updateData = (updates: Partial<CourseFormData>) => {
    onChange({ ...data, ...updates });
  };

  const handleFreeToggle = (checked: boolean) => {
    updateData({
      Free: checked,
      Price: checked ? 0 : data.Price,
      NewPrice: checked ? 0 : data.NewPrice
    });
  };

  const handlePriceChange = (value: string) => {
    const price = parseFloat(value) || 0;
    updateData({ Price: price });
  };

  const handleNewPriceChange = (value: string) => {
    const newPrice = parseFloat(value) || 0;
    updateData({ NewPrice: newPrice });
  };

  // Calculate discount percentage
  const getDiscountPercentage = () => {
    if (data.Price && data.NewPrice && data.NewPrice < data.Price) {
      return Math.round(((data.Price - data.NewPrice) / data.Price) * 100);
    }
    return 0;
  };

  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <div className="border-b border-gray-200 pb-6">
        <div className="flex items-center space-x-3 mb-3">
          <div className="p-2 bg-red-100 rounded-lg">
            <DollarSign className="w-6 h-6 text-red-600" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Course Pricing</h2>
            <p className="text-gray-600 mt-1">
              Set the price for your course. You can offer it for free or set a competitive price.
            </p>
          </div>
        </div>
      </div>

      {/* Errors */}
      {errors.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <AlertCircle className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="text-sm font-semibold text-red-800 mb-2">Please fix the following errors:</h3>
              <ul className="list-disc list-inside space-y-1 text-sm text-red-700">
                {errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}

      {/* Pricing Guidelines */}
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          <div className="space-y-2">
            <p className="font-semibold">Pricing Guidelines:</p>
            <ul className="text-sm space-y-1">
              <li>• Free courses are great for building your audience and getting initial reviews</li>
              <li>• Paid courses must be priced at least $7 CAD</li>
              <li>• Consider your course length, depth, and target audience when setting prices</li>
              <li>• You can always adjust pricing later based on student feedback</li>
            </ul>
          </div>
        </AlertDescription>
      </Alert>

      {/* Free Course Option */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Gift className="w-5 h-5" />
            <span>Free Course</span>
          </CardTitle>
          <CardDescription>
            Offer your course for free to build your audience and gather reviews
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="course-free"
              checked={data.Free}
              onCheckedChange={handleFreeToggle}
            />
            <Label htmlFor="course-free" className="text-base">
              Make this course free
            </Label>
          </div>
          {data.Free && (
            <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center space-x-2">
                <Gift className="w-5 h-5 text-green-600" />
                <span className="font-semibold text-green-800">Free Course Benefits:</span>
              </div>
              <ul className="mt-2 text-sm text-green-700 space-y-1">
                <li>• Attract more students initially</li>
                <li>• Build your reputation as an instructor</li>
                <li>• Gather valuable student feedback</li>
                <li>• Create a foundation for future paid courses</li>
              </ul>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Paid Course Pricing */}
      {!data.Free && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="w-5 h-5" />
              <span>Course Pricing</span>
            </CardTitle>
            <CardDescription>
              Set competitive pricing for your course content
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Regular Price */}
              <div className="space-y-2">
                <Label htmlFor="price">Course Price *</Label>
                <div className="relative">
                  <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                    CA$
                  </span>
                  <Input
                    id="price"
                    type="number"
                    min="7"
                    step="0.01"
                    value={data.Price || ''}
                    onChange={(e) => handlePriceChange(e.target.value)}
                    placeholder="0.00"
                    className={`pl-12 ${errors.some(e => e.includes('Price')) ? 'border-red-500' : ''}`}
                  />
                </div>
                <p className="text-sm text-gray-500">
                  Minimum price is $7 CAD
                </p>
              </div>

              {/* Promotional Price */}
              <div className="space-y-2">
                <Label htmlFor="new-price">Promotional Price (Optional)</Label>
                <div className="relative">
                  <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                    CA$
                  </span>
                  <Input
                    id="new-price"
                    type="number"
                    min="0"
                    step="0.01"
                    value={data.NewPrice || ''}
                    onChange={(e) => handleNewPriceChange(e.target.value)}
                    placeholder="0.00"
                    className="pl-12"
                  />
                </div>
                <p className="text-sm text-gray-500">
                  Set a lower price for promotions
                </p>
              </div>
            </div>

            {/* Pricing Preview */}
            {data.Price && data.Price > 0 && (
              <div className="p-4 bg-gray-50 border rounded-lg">
                <h4 className="font-semibold mb-3">Pricing Preview</h4>
                <div className="flex items-center space-x-4">
                  <div className="text-center">
                    <div className="text-sm text-gray-600">Regular Price</div>
                    <div className={`text-lg font-bold ${data.NewPrice && data.NewPrice < data.Price ? 'line-through text-gray-500' : 'text-green-600'}`}>
                      CA${data.Price.toFixed(2)}
                    </div>
                  </div>
                  
                  {data.NewPrice && data.NewPrice < data.Price && (
                    <>
                      <div className="text-2xl text-gray-400">→</div>
                      <div className="text-center">
                        <div className="text-sm text-gray-600">Promotional Price</div>
                        <div className="text-lg font-bold text-red-600">
                          CA${data.NewPrice.toFixed(2)}
                        </div>
                      </div>
                      <Badge variant="destructive" className="ml-2">
                        {getDiscountPercentage()}% OFF
                      </Badge>
                    </>
                  )}
                </div>
                
                {data.NewPrice && data.NewPrice >= data.Price && (
                  <p className="text-sm text-amber-600 mt-2">
                    ⚠️ Promotional price should be lower than the regular price
                  </p>
                )}
              </div>
            )}

            {/* Revenue Estimate */}
            {data.Price && data.Price > 0 && (
              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 className="font-semibold text-blue-800 mb-2">Revenue Estimate</h4>
                <div className="text-sm text-blue-700 space-y-1">
                  <p>Based on your pricing, here's what you could earn:</p>
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mt-3">
                    <div className="text-center">
                      <div className="font-semibold">10 Students</div>
                      <div>CA${((data.NewPrice || data.Price) * 10 * 0.7).toFixed(2)}</div>
                      <div className="text-xs">(70% revenue share)</div>
                    </div>
                    <div className="text-center">
                      <div className="font-semibold">50 Students</div>
                      <div>CA${((data.NewPrice || data.Price) * 50 * 0.7).toFixed(2)}</div>
                      <div className="text-xs">(70% revenue share)</div>
                    </div>
                    <div className="text-center">
                      <div className="font-semibold">100 Students</div>
                      <div>CA${((data.NewPrice || data.Price) * 100 * 0.7).toFixed(2)}</div>
                      <div className="text-xs">(70% revenue share)</div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Pricing Strategy Tips */}
      <Card>
        <CardHeader>
          <CardTitle>Pricing Strategy Tips</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-2">For New Instructors:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Start with a lower price to attract initial students</li>
                <li>• Offer promotional pricing for early adopters</li>
                <li>• Focus on getting positive reviews first</li>
                <li>• Consider making your first course free</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Pricing Factors:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Course length and depth of content</li>
                <li>• Your expertise and credentials</li>
                <li>• Market demand for the topic</li>
                <li>• Competitor pricing analysis</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CoursePricingTab;
