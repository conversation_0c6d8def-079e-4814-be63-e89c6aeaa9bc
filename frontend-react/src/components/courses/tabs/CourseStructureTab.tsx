import React from 'react';
import type { CourseFormData } from '../../../models/course.model';

// shadcn/ui components
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../ui/card';
import { But<PERSON> } from '../../ui/button';
import { Alert, AlertDescription } from '../../ui/alert';

// Icons
import { BookOpen, AlertCircle, Info, Plus, ArrowRight } from 'lucide-react';

interface CourseStructureTabProps {
  courseId?: string;
  data: CourseFormData;
  onChange: (data: CourseFormData) => void;
  errors: string[];
}

const CourseStructureTab: React.FC<CourseStructureTabProps> = ({
  courseId,
  data,
  onChange,
  errors
}) => {
  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <div className="border-b border-gray-200 pb-6">
        <div className="flex items-center space-x-3 mb-3">
          <div className="p-2 bg-red-100 rounded-lg">
            <BookOpen className="w-6 h-6 text-red-600" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Course Structure</h2>
            <p className="text-gray-600 mt-1">
              Course structure will be available after you create your course.
            </p>
          </div>
        </div>
      </div>

      {/* Errors */}
      {errors.length > 0 && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <ul className="list-disc list-inside space-y-1">
              {errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      {/* Course Structure Info */}
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          <div className="space-y-2">
            <p>
              Course structure will be available after you create your course. You'll be able to add sections, lessons, and content to organize your learning materials.
            </p>
            <p className="font-medium">
              Complete the other tabs and create your course to start building the structure.
            </p>
          </div>
        </AlertDescription>
      </Alert>

      {/* Course Structure Preview */}
      <Card>
        <CardHeader>
          <CardTitle>Course Structure Overview</CardTitle>
          <CardDescription>
            Here's how your course structure will be organized
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            {/* Sample Structure */}
            <div className="border rounded-lg p-4">
              <h3 className="font-semibold text-lg mb-4">Sample Course Structure:</h3>
              
              <div className="space-y-3">
                <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded">
                  <BookOpen className="w-5 h-5 text-blue-600" />
                  <div className="flex-1">
                    <div className="font-medium">Section 1: Introduction</div>
                    <div className="text-sm text-gray-600">Welcome and course overview</div>
                  </div>
                </div>
                
                <div className="ml-8 space-y-2">
                  <div className="flex items-center space-x-3 p-2 bg-white border rounded">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm">Lesson 1.1: Welcome to the course</span>
                  </div>
                  <div className="flex items-center space-x-3 p-2 bg-white border rounded">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm">Lesson 1.2: Course objectives</span>
                  </div>
                </div>

                <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded">
                  <BookOpen className="w-5 h-5 text-blue-600" />
                  <div className="flex-1">
                    <div className="font-medium">Section 2: Core Content</div>
                    <div className="text-sm text-gray-600">Main learning material</div>
                  </div>
                </div>
                
                <div className="ml-8 space-y-2">
                  <div className="flex items-center space-x-3 p-2 bg-white border rounded">
                    <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
                    <span className="text-sm text-gray-500">Lesson 2.1: [To be added]</span>
                  </div>
                  <div className="flex items-center space-x-3 p-2 bg-white border rounded">
                    <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
                    <span className="text-sm text-gray-500">Lesson 2.2: [To be added]</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Next Steps */}
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-semibold text-blue-800 mb-2">Next Steps:</h4>
              <div className="text-sm text-blue-700 space-y-2">
                <p>After saving your course, you'll be able to:</p>
                <ul className="space-y-1 ml-4">
                  <li>• Create course sections</li>
                  <li>• Add lessons to each section</li>
                  <li>• Upload content (videos, documents, quizzes)</li>
                  <li>• Organize learning materials</li>
                  <li>• Set lesson prerequisites</li>
                </ul>
              </div>
            </div>

            {/* Action Button */}
            {courseId ? (
              <div className="flex justify-center">
                <Button 
                  onClick={() => window.open(`/instructor/courses/${courseId}/structure`, '_blank')}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <BookOpen className="w-4 h-4 mr-2" />
                  Manage Course Structure
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </div>
            ) : (
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <p className="text-gray-600 mb-2">Save your course first to access structure management</p>
                <p className="text-sm text-gray-500">You'll be redirected to the structure editor after saving</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Structure Tips */}
      <Card>
        <CardHeader>
          <CardTitle>Course Structure Best Practices</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-2">Section Organization:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Group related topics into sections</li>
                <li>• Start with an introduction section</li>
                <li>• End with a conclusion or next steps</li>
                <li>• Keep sections focused and manageable</li>
                <li>• Use clear, descriptive section titles</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Lesson Planning:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Keep lessons concise (5-15 minutes)</li>
                <li>• Build complexity gradually</li>
                <li>• Include practical exercises</li>
                <li>• Add quizzes to reinforce learning</li>
                <li>• Provide downloadable resources</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CourseStructureTab;
