import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import {
  FaSearch,
  FaShoppingCart,
  FaHeart,
  FaBell,
  FaBars,
  FaGraduationCap,
  FaUser,
  FaSignOutAlt,
  FaSignInAlt,
  FaUserPlus,
  FaCreditCard,
  FaComments,
  FaNewspaper,
  FaShieldAlt,
  FaBookOpen,
  FaChevronDown
} from 'react-icons/fa';
import { Avatar as ShadcnAvatar, AvatarFallback } from "@/components/ui/avatar";
import { Dialog } from "@/components/ui/dialog";
import {
  Layout,
  Menu,
  Space,
  Badge,
  Avatar,
  Dropdown,
  Drawer,
  Input,
  Button,
  ConfigProvider,
  Affix
} from '../antd';
import { useAuth } from '../../contexts/AuthContext';
import { useCart } from '../../contexts/CartContext';
import { useNavigationCategories } from '../../hooks/useCategories';
// import { useNotifications } from '../../hooks/useNotifications'; // Temporarily disabled
import type { Category } from '../../models/course.model';
import type { MenuProps } from '../antd';

const { Header: AntHeader } = Layout;
const { Search } = Input;

const ModernHeader: React.FC = () => {
  const { authState, logout } = useAuth();
  const { cart } = useCart();
  const { isAuthenticated, user } = authState;
  const navigate = useNavigate();
  const location = useLocation();

  // Temporarily disable notifications - backend /api/notifications endpoint returns 500 error
  const unreadCount = 0;
  const notificationsError = null;

  // TODO: Re-enable when backend /api/notifications endpoint is fixed
  // const { unreadCount, error: notificationsError } = useNotifications(user?.Id || null, {
  //   autoRefresh: false,
  //   filters: { limit: 5 }
  // });

  // State management
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [searchVisible, setSearchVisible] = useState(false);
  const [searchValue, setSearchValue] = useState('');

  // Categories for navigation
  const { data: categories } = useNavigationCategories(8, 0);
  const allCategories = categories || [];

  // Responsive breakpoints
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);

      if (!mobile && mobileMenuOpen) {
        setMobileMenuOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [mobileMenuOpen]);

  // Get current path for menu highlighting
  const currentPath = location.pathname;
  const selectedKeys = [currentPath === '/' ? 'courses' : currentPath.split('/')[1]];

  // Event handlers
  const handleLogout = () => {
    logout('/auth/login');
    setMobileMenuOpen(false);
  };

  const handleSearch = (query: string) => {
    if (query.trim()) {
      navigate(`/courses?search=${encodeURIComponent(query.trim())}`);
      setSearchVisible(false);
      setMobileMenuOpen(false);
    }
  };

  const toggleSearch = () => {
    setSearchVisible(!searchVisible);
    setSearchValue('');
  };



  // Modern navigation items with clean design
  const navigationItems: MenuProps['items'] = [
    {
      key: 'courses',
      label: (
        <Link to="/courses" className="nav-item">
          <FaBookOpen className="nav-icon" />
          <span>Courses</span>
        </Link>
      ),
    },
    {
      key: 'categories',
      label: (
        <div className="nav-item dropdown-trigger">
          <FaBars className="nav-icon" />
          <span>Categories</span>
          <FaChevronDown className="dropdown-arrow" />
        </div>
      ),
      children: allCategories.slice(0, 8).map((category: Category) => ({
        key: `category-${category.Id || category.Slug}`,
        label: (
          <Link
            to={`/courses?category=${category.Slug || category.Id?.toString()}`}
            className="category-item"
          >
            {(category as any).Title || (category as any).Username || 'Untitled Category'}
          </Link>
        ),
      })),
    },
    {
      key: 'blog',
      label: (
        <Link to="/blog" className="nav-item">
          <FaNewspaper className="nav-icon" />
          <span>Blog</span>
        </Link>
      ),
    },
    {
      key: 'forum',
      label: (
        <Link to="/forum" className="nav-item">
          <FaComments className="nav-icon" />
          <span>Forum</span>
        </Link>
      ),
    },
  ];

  // User menu items with improved styling
  const userMenuItems: MenuProps['items'] = [
    {
      key: 'profile-header',
      label: (
        <div className="px-4 py-3 border-b border-gray-100 mb-2">
          <div className="flex items-center space-x-3">
            <Avatar
              size={48}
              className="bg-red-600 border-2 border-red-100"
              src={user?.Photo?.Hashname ? `${process.env.REACT_APP_API_URL || 'http://localhost:3200'}/${user.Photo.Hashname}` : undefined}
            >
              {user?.Username?.charAt(0).toUpperCase() || 'U'}
            </Avatar>
            <div className="flex-1 min-w-0">
              <div className="font-semibold text-gray-900 text-sm truncate">
                {user?.Firstname} {user?.Lastname}
              </div>
              <div className="text-xs text-gray-500 truncate">
                {user?.Email}
              </div>
              <div className="text-xs text-red-600 font-medium mt-1">
                View Profile
              </div>
            </div>
          </div>
        </div>
      ),
      disabled: true,
    },
    {
      key: 'exit-dashboard',
      label: (
        <div className="flex items-center space-x-3 px-4 py-2 hover:bg-gray-50 rounded-lg transition-colors">
          <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
            <FaBookOpen className="w-4 h-4 text-blue-600" />
          </div>
          <div>
            <div className="font-medium text-gray-900 text-sm">Browse Courses</div>
            <div className="text-xs text-gray-500">Exit dashboard, stay logged in</div>
          </div>
        </div>
      ),
      onClick: () => navigate('/'),
    },
    {
      key: 'dashboard',
      label: (
        <div className="flex items-center space-x-3 py-1">
          <FaUser className="text-gray-500" />
          <span className="font-medium">Dashboard</span>
        </div>
      ),
      onClick: () => navigate('/user/dashboard'),
    },
    {
      key: 'my-learning',
      label: (
        <div className="flex items-center space-x-3 py-1">
          <FaGraduationCap className="text-gray-500" />
          <span className="font-medium">My Learning</span>
        </div>
      ),
      onClick: () => navigate('/user/courses'),
    },
    {
      key: 'wishlist',
      label: (
        <div className="flex items-center space-x-3 py-1">
          <FaHeart className="text-gray-500" />
          <span className="font-medium">Wishlist</span>
        </div>
      ),
      onClick: () => navigate('/user/wishlist'),
    },
    { type: 'divider' },
    {
      key: 'profile',
      label: (
        <div className="flex items-center space-x-3 py-1">
          <FaUser className="text-gray-500" />
          <span className="font-medium">Profile Settings</span>
        </div>
      ),
      onClick: () => navigate('/user/profile'),
    },
    {
      key: 'billing',
      label: (
        <div className="flex items-center space-x-3 py-1">
          <FaCreditCard className="text-gray-500" />
          <span className="font-medium">Billing & Payments</span>
        </div>
      ),
      onClick: () => navigate('/user/billing'),
    },
    ...(user?.Role === 3 ? [
      { type: 'divider' as const },
      {
        key: 'admin',
        label: (
          <div className="flex items-center space-x-3 py-1">
            <FaShieldAlt className="text-red-600" />
            <span className="font-medium text-red-600">Admin Dashboard</span>
          </div>
        ),
        onClick: () => navigate('/admin'),
      }
    ] : []),
    { type: 'divider' },
    {
      key: 'logout',
      label: (
        <div className="flex items-center space-x-3 py-1 text-red-600">
          <FaSignOutAlt />
          <span className="font-medium">Sign Out</span>
        </div>
      ),
      onClick: handleLogout,
      danger: true,
    },
  ];



  return (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: '#dc3545',
          borderRadius: 6,
        },
        components: {
          Menu: {
            horizontalItemSelectedColor: '#dc3545',
            horizontalItemHoverColor: '#dc3545',
            itemSelectedBg: 'rgba(220, 53, 69, 0.08)',
            itemHoverBg: 'rgba(220, 53, 69, 0.05)',
            colorBgContainer: '#ffffff',
            colorBgElevated: '#ffffff',
          },
          Button: {
            borderRadius: 6,
          },
          Input: {
            borderRadius: 6,
          },
          Drawer: {
            colorBgElevated: '#ffffff',
            colorText: '#1a1a1a',
          },
        },
      }}
    >
      {/* Modern Clean Header */}
      <Affix offsetTop={0}>
        <AntHeader
          className="modern-clean-header"
          style={{
            backgroundColor: '#ffffff',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
            padding: isMobile ? '0 16px' : '0 24px',
            height: '72px',
            lineHeight: '72px',
            borderBottom: '1px solid #f0f0f0',
            position: 'relative',
            zIndex: 1000,
          }}
        >
          <div className="header-container">
            {/* Logo Section */}
            <div className="logo-section">
              <Link to="/" className="logo-link">
                <img
                  src="/assets/img/brain-maker-logo.png"
                  alt="BrainMaker Academy"
                  className="logo-image"
                  style={{
                    height: '40px',
                    width: 'auto',
                    objectFit: 'contain',
                  }}
                />
              </Link>
            </div>

            {/* Desktop Navigation */}
            {!isMobile && (
              <div className="nav-section">
                <Menu
                  mode="horizontal"
                  items={navigationItems}
                  selectedKeys={selectedKeys}
                  className="main-nav"
                  style={{
                    border: 'none',
                    backgroundColor: 'transparent',
                    fontSize: '15px',
                    fontWeight: '500',
                    lineHeight: '72px',
                  }}
                />
              </div>
            )}

            {/* Search Section - Desktop */}
            {!isMobile && (
              <div className="search-section">
                {searchVisible ? (
                  <div className="search-input-container">
                    <Search
                      placeholder="Search courses..."
                      value={searchValue}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchValue(e.target.value)}
                      onSearch={handleSearch}
                      onBlur={() => !searchValue && setSearchVisible(false)}
                      autoFocus
                      allowClear
                      style={{ width: '300px' }}
                    />
                  </div>
                ) : (
                  <Button
                    type="text"
                    icon={<FaSearch />}
                    onClick={toggleSearch}
                    className="search-trigger"
                  />
                )}
              </div>
            )}

            {/* Actions Section */}
            <div className="actions-section">
              <Space size="middle">
                {/* Mobile Search */}
                {isMobile && (
                  <Button
                    type="text"
                    icon={<FaSearch />}
                    onClick={toggleSearch}
                    className="mobile-search-btn"
                  />
                )}

                {/* Shopping Cart */}
                <Badge count={cart.ItemCount} size="small">
                  <Button
                    type="text"
                    icon={<FaShoppingCart />}
                    onClick={() => navigate('/cart')}
                    className="action-icon"
                  />
                </Badge>

                {/* Wishlist - Desktop Only */}
                {!isMobile && isAuthenticated && (
                  <Badge count={0} size="small">
                    <Button
                      type="text"
                      icon={<FaHeart />}
                      onClick={() => navigate('/user/wishlist')}
                      className="action-icon"
                    />
                  </Badge>
                )}

                {/* Notifications - Desktop Only */}
                {!isMobile && isAuthenticated && (
                  <Badge count={notificationsError ? 0 : (unreadCount || 0)} size="small">
                    <Button
                      type="text"
                      icon={<FaBell />}
                      onClick={() => navigate('/user/notifications')}
                      className="action-icon"
                    />
                  </Badge>
                )}

                {/* User Menu or Auth */}
                {isAuthenticated ? (
                  <Dropdown
                    menu={{
                      items: userMenuItems,
                      style: {
                        minWidth: '280px',
                        borderRadius: '12px',
                        boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
                        border: '1px solid #f0f0f0',
                        padding: '8px 0'
                      }
                    }}
                    placement="bottomRight"
                    trigger={['click']}
                    overlayClassName="user-menu-dropdown"
                  >
                    <div className="flex items-center space-x-2 cursor-pointer hover:bg-gray-50 rounded-lg p-2 transition-colors">
                      <Avatar
                        size={36}
                        className="bg-red-600 border-2 border-red-100 shadow-sm"
                        src={user?.Photo?.Hashname ? `${process.env.REACT_APP_API_URL || 'http://localhost:3200'}/${user.Photo.Hashname}` : undefined}
                      >
                        {user?.Username?.charAt(0).toUpperCase() || 'U'}
                      </Avatar>
                      <FaChevronDown className="text-gray-400 text-xs" />
                    </div>
                  </Dropdown>
                ) : (
                  <Space size="small">
                    {!isMobile && (
                      <>
                        <Button
                          onClick={() => navigate('/auth/login')}
                          className="auth-btn login-btn"
                        >
                          Log in
                        </Button>
                        <Button
                          type="primary"
                          onClick={() => navigate('/auth/register')}
                          className="auth-btn signup-btn"
                        >
                          Sign up
                        </Button>
                      </>
                    )}
                  </Space>
                )}

                {/* Mobile Menu Toggle */}
                {isMobile && (
                  <Button
                    type="text"
                    icon={<FaBars />}
                    onClick={() => setMobileMenuOpen(true)}
                    className="mobile-menu-btn"
                  />
                )}
              </Space>
            </div>
          </div>
        </AntHeader>
      </Affix>

      {/* Mobile Search Modal */}
      {isMobile && (
        <Dialog
          open={searchVisible}
          onOpenChange={setSearchVisible}
        >
          <div className="mobile-search-content">
            <Search
              placeholder="Search courses, topics, instructors..."
              value={searchValue}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchValue(e.target.value)}
              onSearch={handleSearch}
              size="large"
              autoFocus
              allowClear
              style={{ marginBottom: '24px' }}
            />

            <div className="search-suggestions">
              <h4>Popular Searches</h4>
              <div className="suggestion-tags">
                {['React', 'Python', 'JavaScript', 'Machine Learning', 'Web Development', 'Data Science'].map(tag => (
                  <Button
                    key={tag}
                    onClick={() => handleSearch(tag)}
                    className="suggestion-tag"
                  >
                    {tag}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </Dialog>
      )}

      {/* Modern Mobile Menu */}
      <Dialog
        open={mobileMenuOpen}
        onOpenChange={setMobileMenuOpen}
      >
        <div className="mobile-menu-content max-w-sm mx-auto bg-white rounded-lg shadow-xl">
          {/* User Profile Section */}
          {isAuthenticated && user && (
            <div className="bg-gradient-to-r from-red-600 to-red-700 p-6 rounded-t-lg">
              <div className="flex items-center space-x-4">
                <ShadcnAvatar className="w-16 h-16 border-3 border-white/20">
                  <AvatarFallback className="bg-white/20 text-white text-lg font-semibold">
                    {user?.Username?.charAt(0).toUpperCase() || 'U'}
                  </AvatarFallback>
                </ShadcnAvatar>
                <div className="flex-1 min-w-0">
                  <h3 className="text-white font-semibold text-lg truncate">
                    {user.Firstname} {user.Lastname}
                  </h3>
                  <p className="text-white/80 text-sm truncate">{user.Email}</p>
                  <div className="mt-2">
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-white/20 text-white">
                      {user.Role === 3 ? 'Admin' : user.Role === 2 ? 'Instructor' : 'Student'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Navigation Menu */}
          <div className="p-4">
            <Menu
              mode="inline"
              className="border-0 bg-transparent"
              style={{
                border: 'none',
                backgroundColor: 'transparent'
              }}
              items={[
                {
                  key: 'courses',
                  label: (
                    <div className="flex items-center space-x-3 py-2">
                      <FaBookOpen className="text-gray-600" />
                      <span className="font-medium text-gray-800">Courses</span>
                    </div>
                  ),
                  onClick: () => {
                    navigate('/courses');
                    setMobileMenuOpen(false);
                  },
                },
                {
                  key: 'categories',
                  label: (
                    <div className="flex items-center space-x-3 py-2">
                      <FaBars className="text-gray-600" />
                      <span className="font-medium text-gray-800">Categories</span>
                    </div>
                  ),
                  children: allCategories.slice(0, 6).map((category: Category) => ({
                    key: `category-${category.Id || category.Slug}`,
                    label: (
                      <span className="text-gray-700 font-medium">
                        {(category as any).Title || (category as any).Username || 'Untitled Category'}
                      </span>
                    ),
                    onClick: () => {
                      navigate(`/courses?category=${category.Slug || category.Id?.toString()}`);
                      setMobileMenuOpen(false);
                    },
                  })),
                },
                {
                  key: 'blog',
                  label: (
                    <div className="flex items-center space-x-3 py-2">
                      <FaNewspaper className="text-gray-600" />
                      <span className="font-medium text-gray-800">Blog</span>
                    </div>
                  ),
                  onClick: () => {
                    navigate('/blog');
                    setMobileMenuOpen(false);
                  },
                },
                {
                  key: 'forum',
                  label: (
                    <div className="flex items-center space-x-3 py-2">
                      <FaComments className="text-gray-600" />
                      <span className="font-medium text-gray-800">Forum</span>
                    </div>
                  ),
                  onClick: () => {
                    navigate('/forum');
                    setMobileMenuOpen(false);
                  },
                },
                ...(isAuthenticated ? [
                  { type: 'divider' as const },
                  {
                    key: 'my-learning',
                    label: (
                      <div className="flex items-center space-x-3 py-2">
                        <FaGraduationCap className="text-red-600" />
                        <span className="font-medium text-gray-800">My Learning</span>
                      </div>
                    ),
                    onClick: () => {
                      navigate('/user/courses');
                      setMobileMenuOpen(false);
                    },
                  },
                  {
                    key: 'wishlist',
                    label: (
                      <div className="flex items-center space-x-3 py-2">
                        <FaHeart className="text-red-600" />
                        <span className="font-medium text-gray-800">Wishlist</span>
                      </div>
                    ),
                    onClick: () => {
                      navigate('/user/wishlist');
                      setMobileMenuOpen(false);
                    },
                  },
                  {
                    key: 'profile',
                    label: 'Profile',
                    icon: <FaUser />,
                    onClick: () => {
                      navigate('/user/profile');
                      setMobileMenuOpen(false);
                    },
                  },
                  ...(user?.Role === 3 ? [{
                    key: 'admin',
                    label: 'Admin Dashboard',
                    icon: <FaShieldAlt />,
                    onClick: () => {
                      navigate('/admin');
                      setMobileMenuOpen(false);
                    },
                  }] : []),
                  {
                    key: 'logout',
                    label: 'Sign Out',
                    icon: <FaSignOutAlt />,
                    onClick: handleLogout,
                    danger: true,
                  },
                ] : [
                  { type: 'divider' as const },
                  {
                    key: 'login',
                    label: 'Log In',
                    icon: <FaSignInAlt />,
                    onClick: () => {
                      navigate('/auth/login');
                      setMobileMenuOpen(false);
                    },
                  },
                  {
                    key: 'register',
                    label: 'Sign Up',
                    icon: <FaUserPlus />,
                    onClick: () => {
                      navigate('/auth/register');
                      setMobileMenuOpen(false);
                    },
                  },
                ]),
              ]}
            />
          </div>
        </div>
      </Dialog>
    </ConfigProvider>
  );
};

export default ModernHeader;
