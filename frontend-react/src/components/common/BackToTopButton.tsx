import { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { ChevronUp } from 'lucide-react';
import { cn } from "@/lib/utils";

interface BackToTopButtonProps {
  /**
   * Scroll position threshold to show the button
   * @default 300
   */
  showAfter?: number;
  /**
   * Whether to use smooth scrolling animation
   * @default true
   */
  smooth?: boolean;
  /**
   * Custom className for styling
   */
  className?: string;
  /**
   * Button size
   * @default "default"
   */
  size?: "default" | "sm" | "lg" | "icon";
  /**
   * Button variant
   * @default "default"
   */
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
}

/**
 * BackToTopButton component that appears when user scrolls down
 * and allows them to quickly return to the top of the page
 */
const BackToTopButton: React.FC<BackToTopButtonProps> = ({
  showAfter = 300,
  smooth = true,
  className,
  size = "icon",
  variant = "default"
}) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const toggleVisibility = () => {
      if (window.pageYOffset > showAfter) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    window.addEventListener('scroll', toggleVisibility);

    return () => {
      window.removeEventListener('scroll', toggleVisibility);
    };
  }, [showAfter]);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: smooth ? 'smooth' : 'instant'
    });
  };

  if (!isVisible) {
    return null;
  }

  return (
    <Button
      onClick={scrollToTop}
      size={size}
      variant={variant}
      className={cn(
        "fixed bottom-8 right-8 z-50 shadow-lg transition-all duration-300 hover:shadow-xl",
        "bg-red-600 hover:bg-red-700 text-white border-0",
        "rounded-full w-12 h-12 p-0",
        "animate-in fade-in slide-in-from-bottom-2",
        className
      )}
      aria-label="Back to top"
    >
      <ChevronUp className="w-5 h-5" />
    </Button>
  );
};

export default BackToTopButton;
