import { useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';

interface ScrollToTopProps {
  /**
   * Whether to use smooth scrolling animation
   * @default true
   */
  smooth?: boolean;
  /**
   * Delay in milliseconds before scrolling (useful for page transitions)
   * @default 0
   */
  delay?: number;
  /**
   * Array of pathname patterns to exclude from auto-scrolling
   * @default []
   */
  excludePatterns?: string[];
}

/**
 * ScrollToTop component that automatically scrolls to the top of the page
 * whenever the route changes. This ensures a consistent user experience
 * where users always start at the top of a new page.
 *
 * @param smooth - Whether to use smooth scrolling animation (default: true)
 * @param delay - Delay in milliseconds before scrolling (default: 0)
 * @param excludePatterns - Array of pathname patterns to exclude from auto-scrolling
 */
const ScrollToTop: React.FC<ScrollToTopProps> = ({
  smooth = true,
  delay = 0,
  excludePatterns = []
}) => {
  const { pathname } = useLocation();
  const prevPathname = useRef<string>('');

  useEffect(() => {
    // Only scroll if the pathname actually changed
    if (prevPathname.current === pathname) {
      return;
    }

    // Update the previous pathname
    prevPathname.current = pathname;

    // Check if current pathname should be excluded
    const shouldExclude = excludePatterns.some(pattern => {
      if (pattern.includes('*')) {
        // Simple wildcard matching
        const regex = new RegExp(pattern.replace(/\*/g, '.*'));
        return regex.test(pathname);
      }
      return pathname.includes(pattern);
    });

    if (shouldExclude) {
      return;
    }

    const scrollToTop = () => {

      // Multiple fallback methods to ensure scrolling works
      try {
        // Method 1: Modern scrollTo with behavior
        window.scrollTo({
          top: 0,
          left: 0,
          behavior: smooth ? 'smooth' : 'instant'
        });
      } catch (error) {
        // Method 2: Fallback for older browsers
        try {
          window.scrollTo(0, 0);
        } catch (fallbackError) {
          // Method 3: Direct DOM manipulation
          try {
            document.documentElement.scrollTop = 0;
            document.body.scrollTop = 0;
          } catch (domError) {
            // Silently fail - scroll behavior is not critical
          }
        }
      }
    };

    if (delay > 0) {
      const timeoutId = setTimeout(scrollToTop, delay);
      return () => clearTimeout(timeoutId);
    } else {
      // Add a small delay to ensure the page has rendered and any other scroll behaviors have completed
      const timeoutId = setTimeout(scrollToTop, 150);
      return () => clearTimeout(timeoutId);
    }
  }, [pathname, smooth, delay, excludePatterns]);

  return null; // This component doesn't render anything
};

export default ScrollToTop;

/**
 * Hook for programmatic scrolling to top
 * Useful for buttons or other interactive elements
 */
export const useScrollToTop = (smooth: boolean = true) => {
  return (delay: number = 0) => {
    const scrollToTop = () => {
      window.scrollTo({
        top: 0,
        left: 0,
        behavior: smooth ? 'smooth' : 'instant'
      });
    };

    if (delay > 0) {
      setTimeout(scrollToTop, delay);
    } else {
      scrollToTop();
    }
  };
};
