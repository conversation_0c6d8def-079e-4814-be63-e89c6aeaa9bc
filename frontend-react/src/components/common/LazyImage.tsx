import React, { useState, useRef, useEffect } from 'react';
import { cn } from '../../lib/utils';
import { useLazyImage, useIntersectionObserver } from '../../utils/performance';
import { Skeleton } from '../ui/skeleton';
import { AlertCircle } from 'lucide-react';

interface LazyImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  alt: string;
  placeholder?: string;
  fallback?: string;
  showSkeleton?: boolean;
  skeletonClassName?: string;
  errorIcon?: React.ReactNode;
  onLoad?: () => void;
  onError?: () => void;
  threshold?: number;
  rootMargin?: string;
}

export const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt,
  placeholder,
  fallback,
  showSkeleton = true,
  skeletonClassName,
  errorIcon,
  onLoad,
  onError,
  threshold = 0.1,
  rootMargin = '50px',
  className,
  style,
  ...props
}) => {
  const [imageState, setImageState] = useState<'loading' | 'loaded' | 'error'>('loading');
  const [imageSrc, setImageSrc] = useState<string>(placeholder || '');
  const imgRef = useRef<HTMLImageElement>(null);
  
  const [intersectionRef, isIntersecting] = useIntersectionObserver({
    threshold,
    rootMargin,
  });

  // Load image when it comes into view
  useEffect(() => {
    if (isIntersecting && src && imageState === 'loading') {
      const img = new Image();
      
      img.onload = () => {
        setImageSrc(src);
        setImageState('loaded');
        onLoad?.();
      };
      
      img.onerror = () => {
        if (fallback) {
          setImageSrc(fallback);
          setImageState('loaded');
        } else {
          setImageState('error');
        }
        onError?.();
      };
      
      img.src = src;
    }
  }, [isIntersecting, src, imageState, fallback, onLoad, onError]);

  // Combine refs
  const setRefs = (element: HTMLImageElement | null) => {
    if (imgRef.current !== element) {
      (imgRef as React.MutableRefObject<HTMLImageElement | null>).current = element;
    }
    if (intersectionRef && 'current' in intersectionRef) {
      (intersectionRef as React.MutableRefObject<HTMLImageElement | null>).current = element;
    }
  };

  if (imageState === 'loading' && showSkeleton && !placeholder) {
    return (
      <div className={cn('w-full h-full', skeletonClassName, className)} style={style}>
        <Skeleton className="w-full h-full" />
      </div>
    );
  }

  if (imageState === 'error' && !fallback) {
    return (
      <div 
        ref={setRefs}
        className={cn(
          'flex items-center justify-center bg-gray-100 text-gray-400',
          className
        )}
        style={style}
      >
        {errorIcon || <AlertCircle className="w-8 h-8" />}
      </div>
    );
  }

  return (
    <img
      ref={setRefs}
      src={imageSrc}
      alt={alt}
      className={cn(
        'transition-opacity duration-300',
        imageState === 'loaded' ? 'opacity-100' : 'opacity-0',
        className
      )}
      style={style}
      {...props}
    />
  );
};

// Progressive image loading component
interface ProgressiveImageProps extends LazyImageProps {
  lowQualitySrc?: string;
  highQualitySrc: string;
}

export const ProgressiveImage: React.FC<ProgressiveImageProps> = ({
  lowQualitySrc,
  highQualitySrc,
  ...props
}) => {
  const [currentSrc, setCurrentSrc] = useState(lowQualitySrc || '');
  const [isHighQualityLoaded, setIsHighQualityLoaded] = useState(false);

  useEffect(() => {
    if (highQualitySrc) {
      const img = new Image();
      img.onload = () => {
        setCurrentSrc(highQualitySrc);
        setIsHighQualityLoaded(true);
      };
      img.src = highQualitySrc;
    }
  }, [highQualitySrc]);

  return (
    <LazyImage
      {...props}
      src={currentSrc}
      className={cn(
        'transition-all duration-500',
        !isHighQualityLoaded && lowQualitySrc && 'blur-sm scale-105',
        props.className
      )}
    />
  );
};

// Image gallery with lazy loading
interface LazyImageGalleryProps {
  images: Array<{
    src: string;
    alt: string;
    thumbnail?: string;
    caption?: string;
  }>;
  columns?: number;
  gap?: number;
  onImageClick?: (index: number) => void;
  className?: string;
}

export const LazyImageGallery: React.FC<LazyImageGalleryProps> = ({
  images,
  columns = 3,
  gap = 4,
  onImageClick,
  className,
}) => {
  return (
    <div 
      className={cn(
        'grid gap-4',
        columns === 2 && 'grid-cols-2',
        columns === 3 && 'grid-cols-3',
        columns === 4 && 'grid-cols-4',
        className
      )}
      style={{ gap: `${gap * 0.25}rem` }}
    >
      {images.map((image, index) => (
        <div
          key={index}
          className="relative group cursor-pointer overflow-hidden rounded-lg"
          onClick={() => onImageClick?.(index)}
        >
          <LazyImage
            src={image.thumbnail || image.src}
            alt={image.alt}
            className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105"
          />
          {image.caption && (
            <div className="absolute bottom-0 left-0 right-0 bg-black/50 text-white p-2 text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              {image.caption}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

// Avatar with lazy loading and fallback
interface LazyAvatarProps {
  src?: string;
  alt: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  fallbackText?: string;
  className?: string;
}

export const LazyAvatar: React.FC<LazyAvatarProps> = ({
  src,
  alt,
  size = 'md',
  fallbackText,
  className,
}) => {
  const sizeClasses = {
    sm: 'w-8 h-8 text-xs',
    md: 'w-10 h-10 text-sm',
    lg: 'w-12 h-12 text-base',
    xl: 'w-16 h-16 text-lg',
  };

  const fallbackContent = fallbackText || alt.charAt(0).toUpperCase();

  if (!src) {
    return (
      <div className={cn(
        'rounded-full bg-gray-200 flex items-center justify-center font-medium text-gray-600',
        sizeClasses[size],
        className
      )}>
        {fallbackContent}
      </div>
    );
  }

  return (
    <LazyImage
      src={src}
      alt={alt}
      className={cn(
        'rounded-full object-cover',
        sizeClasses[size],
        className
      )}
      fallback={`data:image/svg+xml;base64,${btoa(`
        <svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
          <rect width="100" height="100" fill="#e5e7eb"/>
          <text x="50" y="50" font-family="Arial" font-size="24" fill="#6b7280" text-anchor="middle" dy="8">
            ${fallbackContent}
          </text>
        </svg>
      `)}`}
    />
  );
};

// Background image with lazy loading
interface LazyBackgroundProps {
  src: string;
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  placeholder?: string;
}

export const LazyBackground: React.FC<LazyBackgroundProps> = ({
  src,
  children,
  className,
  style,
  placeholder,
}) => {
  const [backgroundImage, setBackgroundImage] = useState<string>(
    placeholder ? `url(${placeholder})` : 'none'
  );
  const [isLoaded, setIsLoaded] = useState(false);
  const [ref, isIntersecting] = useIntersectionObserver();

  useEffect(() => {
    if (isIntersecting && src && !isLoaded) {
      const img = new Image();
      img.onload = () => {
        setBackgroundImage(`url(${src})`);
        setIsLoaded(true);
      };
      img.src = src;
    }
  }, [isIntersecting, src, isLoaded]);

  return (
    <div
      ref={ref as React.RefObject<HTMLDivElement>}
      className={cn(
        'bg-cover bg-center bg-no-repeat transition-all duration-500',
        !isLoaded && placeholder && 'blur-sm',
        className
      )}
      style={{
        backgroundImage,
        ...style,
      }}
    >
      {children}
    </div>
  );
};
