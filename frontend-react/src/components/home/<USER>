import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { FaLock, FaCertificate, FaStar, FaCheckCircle } from 'react-icons/fa';

// Import company logos
import googleLogo from '@/assets/img/companies/google.png';
import microsoftLogo from '@/assets/img/companies/microsoft.png';
import amazonLogo from '@/assets/img/companies/amazon.png';
import appleLogo from '@/assets/img/companies/apple.png';
import metaLogo from '@/assets/img/companies/meta.png';
import netflixLogo from '@/assets/img/companies/netflix.png';

const TrustSignalsSection: React.FC = () => {

  const companyLogos = [
    {
      name: 'Google',
      logo: googleLogo,
      color: '#4285f4',
      employees: '156K+'
    },
    {
      name: 'Microsoft',
      logo: microsoftLogo,
      color: '#00a1f1',
      employees: '221K+'
    },
    {
      name: 'Amazon',
      logo: amazon<PERSON>ogo,
      color: '#ff9900',
      employees: '1.5M+'
    },
    {
      name: 'Apple',
      logo: apple<PERSON>ogo,
      color: '#007aff',
      employees: '164K+'
    },
    {
      name: 'Meta',
      logo: metaLogo,
      color: '#1877f2',
      employees: '86K+'
    },
    {
      name: 'Netflix',
      logo: netflixLogo,
      color: '#e50914',
      employees: '12K+'
    }
  ];



  return (
    <section className="bg-brand-surface py-12 md:py-16 lg:py-24">
      <div className="container-responsive">
        {/* Enhanced Section Header */}
        <div className="text-center mb-12 md:mb-16">
          <div className="space-y-4 md:space-y-6">
            <div className="flex items-center justify-center gap-3">
              <FaCheckCircle className="text-green-500 text-base md:text-lg" />
              <Badge variant="outline" className="text-brand-red border-brand-primary px-3 md:px-4 py-1 md:py-2 text-xs md:text-sm font-semibold uppercase tracking-wider">
                Trusted Worldwide
              </Badge>
            </div>
            <h2 className="text-nunito-bold text-2xl md:text-3xl lg:text-4xl xl:text-5xl text-brand-primary">
              Learn with Confidence
            </h2>
            <p className="text-sm md:text-base text-brand-secondary max-w-xl md:max-w-2xl mx-auto leading-relaxed px-4 md:px-0">
              Join professionals from the world's leading companies who trust BrainMaker Academy for their learning journey
            </p>
          </div>
        </div>

        {/* Enhanced Company Trust Section */}
        <Card className="bg-gradient-to-br from-brand-gray-light to-brand-background border-none rounded-brand-2xl p-6 md:p-8 lg:p-12 mb-12 md:mb-16">
          <CardContent className="p-0">
            <div className="text-center mb-8 md:mb-10">
              <div className="space-y-3 md:space-y-4">
                <h3 className="text-nunito-semibold text-base md:text-lg text-brand-primary uppercase tracking-wider">
                  Trusted by professionals at
                </h3>
                <p className="text-xs md:text-sm text-brand-secondary">
                  Join learners from the world's most innovative companies
                </p>
              </div>
            </div>

            {/* Enhanced Company Logos - Responsive Grid */}
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 lg:grid-cols-6 gap-4 md:gap-6">
              {companyLogos.map((company, index) => (
                <Card
                  key={index}
                  className="text-center border-brand rounded-brand-xl transition-all duration-300 hover:-translate-y-1 hover:shadow-brand cursor-pointer bg-brand-surface group"
                >
                  <CardContent className="p-3 md:p-4 space-y-2 md:space-y-3">
                    <img
                      src={company.logo}
                      alt={company.name}
                      className="h-6 md:h-8 max-w-[80px] md:max-w-[100px] object-contain mx-auto transition-all duration-300 group-hover:scale-110"
                    />
                    <div className="hidden sm:block">
                      <h4 className="text-xs md:text-sm font-nunito-semibold text-brand-primary">
                        {company.name}
                      </h4>
                      <p className="text-xs text-brand-secondary">
                        {company.employees} employees
                      </p>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Enhanced Security & Quality Badges */}
        <div className="text-center mb-6 md:mb-8">
          <h4 className="text-nunito-semibold text-lg md:text-xl text-brand-primary mb-3 md:mb-4">
            Your Success, Our Priority
          </h4>
          <p className="text-brand-secondary text-xs md:text-sm px-4 md:px-0">
            We're committed to providing a secure, high-quality learning experience
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
          <Card className="card-brand text-center border-none rounded-brand-xl bg-gradient-to-br from-green-50 to-brand-surface shadow-brand-sm hover:shadow-brand-lg transition-all duration-300 hover:-translate-y-1 p-4 md:p-6">
            <CardContent className="p-0 space-y-3 md:space-y-4">
              <div className="w-12 h-12 md:w-15 md:h-15 bg-gradient-to-br from-green-500 to-green-600 rounded-brand-full flex items-center justify-center mx-auto">
                <FaLock className="text-lg md:text-2xl text-white" />
              </div>
              <div>
                <h4 className="text-sm md:text-base font-nunito-semibold text-brand-primary">
                  SSL Secured
                </h4>
                <p className="text-xs text-brand-secondary mt-1">
                  Bank-level security for your data
                </p>
              </div>
            </CardContent>
          </Card>

          <Card className="card-brand text-center border-none rounded-brand-xl bg-gradient-to-br from-yellow-50 to-brand-surface shadow-brand-sm hover:shadow-brand-lg transition-all duration-300 hover:-translate-y-1 p-4 md:p-6">
            <CardContent className="p-0 space-y-3 md:space-y-4">
              <div className="w-12 h-12 md:w-15 md:h-15 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-brand-full flex items-center justify-center mx-auto">
                <FaStar className="text-lg md:text-2xl text-white" />
              </div>
              <div>
                <h4 className="text-sm md:text-base font-nunito-semibold text-brand-primary">
                  4.8/5 Rating
                </h4>
                <p className="text-xs text-brand-secondary mt-1">
                  Based on 50K+ verified reviews
                </p>
              </div>
            </CardContent>
          </Card>

          <Card className="card-brand text-center border-none rounded-brand-xl bg-gradient-to-br from-red-50 to-brand-surface shadow-brand-sm hover:shadow-brand-lg transition-all duration-300 hover:-translate-y-1 p-4 md:p-6">
            <CardContent className="p-0 space-y-3 md:space-y-4">
              <div className="w-12 h-12 md:w-15 md:h-15 bg-gradient-to-br from-red-500 to-red-600 rounded-brand-full flex items-center justify-center mx-auto">
                <FaCertificate className="text-lg md:text-2xl text-white" />
              </div>
              <div>
                <h4 className="text-sm md:text-base font-nunito-semibold text-brand-primary">
                  Accredited
                </h4>
                <p className="text-xs text-brand-secondary mt-1">
                  Industry-recognized certifications
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default TrustSignalsSection;
