import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  Play, 
  Pause, 
  SkipForward, 
  SkipBack,
  Volume2,
  Maximize,
  BookOpen,
  FileText,
  MessageCircle,
  Download,
  CheckCircle,
  Clock,
  User
} from 'lucide-react';

interface CoursePlayerProps {
  courseId: string;
  lessonId?: string;
}

const CoursePlayer: React.FC<CoursePlayerProps> = ({ courseId, lessonId }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(100);
  const [activeTab, setActiveTab] = useState('overview');

  // Mock course data
  const courseData = {
    id: courseId,
    title: 'Advanced React Development',
    instructor: '<PERSON>',
    currentLesson: {
      id: lessonId || '1',
      title: 'Introduction to React Hooks',
      duration: 1200, // 20 minutes in seconds
      videoUrl: '/api/placeholder/video',
      description: 'Learn the fundamentals of React Hooks and how to use them effectively in your applications.',
      resources: [
        { name: 'Lesson Notes.pdf', size: '2.3 MB', url: '#' },
        { name: 'Code Examples.zip', size: '1.1 MB', url: '#' },
        { name: 'Additional Reading.pdf', size: '890 KB', url: '#' }
      ]
    },
    lessons: [
      { id: '1', title: 'Introduction to React Hooks', duration: 1200, completed: false },
      { id: '2', title: 'useState Hook', duration: 900, completed: false },
      { id: '3', title: 'useEffect Hook', duration: 1500, completed: false },
      { id: '4', title: 'Custom Hooks', duration: 1800, completed: false },
      { id: '5', title: 'Advanced Patterns', duration: 2100, completed: false }
    ],
    progress: 25
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const togglePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  const handleProgressChange = (value: number[]) => {
    setCurrentTime(value[0]);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto p-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Video Player */}
          <div className="lg:col-span-3 space-y-6">
            <Card>
              <CardContent className="p-0">
                {/* Video Container */}
                <div className="relative bg-black aspect-video rounded-t-lg overflow-hidden">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-white text-center">
                      <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mb-4 mx-auto">
                        {isPlaying ? <Pause className="w-8 h-8" /> : <Play className="w-8 h-8" />}
                      </div>
                      <p className="text-lg font-medium">{courseData.currentLesson.title}</p>
                      <p className="text-sm opacity-75">Click to {isPlaying ? 'pause' : 'play'}</p>
                    </div>
                  </div>
                  
                  {/* Video Controls Overlay */}
                  <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
                    <div className="flex items-center space-x-4">
                      <Button
                        size="sm"
                        variant="ghost"
                        className="text-white hover:bg-white/20"
                        onClick={togglePlayPause}
                      >
                        {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                      </Button>
                      
                      <Button size="sm" variant="ghost" className="text-white hover:bg-white/20">
                        <SkipBack className="w-4 h-4" />
                      </Button>
                      
                      <Button size="sm" variant="ghost" className="text-white hover:bg-white/20">
                        <SkipForward className="w-4 h-4" />
                      </Button>
                      
                      <div className="flex-1 flex items-center space-x-2">
                        <span className="text-white text-sm">{formatTime(currentTime)}</span>
                        <Progress 
                          value={(currentTime / duration) * 100} 
                          className="flex-1"
                        />
                        <span className="text-white text-sm">{formatTime(duration)}</span>
                      </div>
                      
                      <Button size="sm" variant="ghost" className="text-white hover:bg-white/20">
                        <Volume2 className="w-4 h-4" />
                      </Button>
                      
                      <Button size="sm" variant="ghost" className="text-white hover:bg-white/20">
                        <Maximize className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Lesson Content */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  {courseData.currentLesson.title}
                  <Badge variant="outline">
                    <Clock className="w-3 h-3 mr-1" />
                    {Math.floor(courseData.currentLesson.duration / 60)} min
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Tabs value={activeTab} onValueChange={setActiveTab}>
                  <TabsList>
                    <TabsTrigger value="overview">Overview</TabsTrigger>
                    <TabsTrigger value="resources">Resources</TabsTrigger>
                    <TabsTrigger value="notes">Notes</TabsTrigger>
                    <TabsTrigger value="discussion">Discussion</TabsTrigger>
                  </TabsList>

                  <TabsContent value="overview" className="mt-4">
                    <div className="space-y-4">
                      <p className="text-muted-foreground">
                        {courseData.currentLesson.description}
                      </p>
                      
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                        <div className="flex items-center">
                          <User className="w-4 h-4 mr-1" />
                          {courseData.instructor}
                        </div>
                        <div className="flex items-center">
                          <Clock className="w-4 h-4 mr-1" />
                          {Math.floor(courseData.currentLesson.duration / 60)} minutes
                        </div>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="resources" className="mt-4">
                    <div className="space-y-3">
                      <h4 className="font-medium">Lesson Resources</h4>
                      {courseData.currentLesson.resources.map((resource, index) => (
                        <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center space-x-3">
                            <FileText className="w-5 h-5 text-muted-foreground" />
                            <div>
                              <p className="font-medium text-sm">{resource.name}</p>
                              <p className="text-xs text-muted-foreground">{resource.size}</p>
                            </div>
                          </div>
                          <Button size="sm" variant="outline">
                            <Download className="w-4 h-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </TabsContent>

                  <TabsContent value="notes" className="mt-4">
                    <div className="space-y-4">
                      <h4 className="font-medium">My Notes</h4>
                      <p className="text-muted-foreground text-sm">
                        Take notes while watching the lesson. Your notes will be saved automatically.
                      </p>
                      <div className="border rounded-lg p-4 min-h-32 bg-gray-50">
                        <p className="text-muted-foreground text-sm">Start taking notes...</p>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="discussion" className="mt-4">
                    <div className="space-y-4">
                      <h4 className="font-medium">Discussion</h4>
                      <p className="text-muted-foreground text-sm">
                        Ask questions and discuss this lesson with other students.
                      </p>
                      <Button variant="outline" className="w-full">
                        <MessageCircle className="w-4 h-4 mr-2" />
                        Start a Discussion
                      </Button>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>

          {/* Course Sidebar */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">{courseData.title}</CardTitle>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Progress</span>
                  <span className="font-medium">{courseData.progress}%</span>
                </div>
                <Progress value={courseData.progress} />
              </CardHeader>
              <CardContent className="space-y-4">
                <h4 className="font-medium">Course Content</h4>
                <div className="space-y-2">
                  {courseData.lessons.map((lesson, index) => (
                    <div
                      key={lesson.id}
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        lesson.id === courseData.currentLesson.id
                          ? 'bg-primary/10 border-primary'
                          : 'hover:bg-gray-50'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="w-6 h-6 rounded-full border-2 flex items-center justify-center">
                            {lesson.completed ? (
                              <CheckCircle className="w-4 h-4 text-green-500" />
                            ) : (
                              <span className="text-xs font-medium">{index + 1}</span>
                            )}
                          </div>
                          <div>
                            <p className="font-medium text-sm">{lesson.title}</p>
                            <p className="text-xs text-muted-foreground">
                              {Math.floor(lesson.duration / 60)} min
                            </p>
                          </div>
                        </div>
                        {lesson.id === courseData.currentLesson.id && (
                          <Play className="w-4 h-4 text-primary" />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CoursePlayer;
