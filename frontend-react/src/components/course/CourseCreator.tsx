import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardH<PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { 
  Plus, 
  Save, 
  Eye,
  Upload,
  Video,
  FileText,
  Image,
  Trash2,
  Edit,
  GripVertical,
  BookOpen,
  Settings,
  Send
} from 'lucide-react';

const CourseCreator: React.FC = () => {
  const [activeTab, setActiveTab] = useState('basic');
  const [courseData, setCourseData] = useState({
    title: '',
    subtitle: '',
    description: '',
    category: '',
    level: '',
    language: 'English',
    price: '',
    thumbnail: null as File | null
  });

  const [sections, setSections] = useState([
    {
      id: '1',
      title: 'Introduction',
      lessons: [
        { id: '1-1', title: 'Welcome to the Course', type: 'video', duration: 300 },
        { id: '1-2', title: 'Course Overview', type: 'text', duration: 180 }
      ]
    }
  ]);

  const addSection = () => {
    const newSection = {
      id: Date.now().toString(),
      title: 'New Section',
      lessons: []
    };
    setSections([...sections, newSection]);
  };

  const addLesson = (sectionId: string) => {
    setSections(sections.map(section => 
      section.id === sectionId 
        ? {
            ...section,
            lessons: [
              ...section.lessons,
              {
                id: `${sectionId}-${Date.now()}`,
                title: 'New Lesson',
                type: 'video',
                duration: 0
              }
            ]
          }
        : section
    ));
  };

  const removeSection = (sectionId: string) => {
    setSections(sections.filter(section => section.id !== sectionId));
  };

  const removeLesson = (sectionId: string, lessonId: string) => {
    setSections(sections.map(section =>
      section.id === sectionId
        ? {
            ...section,
            lessons: section.lessons.filter(lesson => lesson.id !== lessonId)
          }
        : section
    ));
  };

  const getTotalDuration = () => {
    return sections.reduce((total, section) => 
      total + section.lessons.reduce((sectionTotal, lesson) => sectionTotal + lesson.duration, 0), 0
    );
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;
  };

  const getCompletionPercentage = () => {
    const requiredFields = ['title', 'description', 'category', 'level'];
    const completedFields = requiredFields.filter(field => courseData[field as keyof typeof courseData]);
    const hasContent = sections.some(section => section.lessons.length > 0);
    return Math.round(((completedFields.length + (hasContent ? 1 : 0)) / (requiredFields.length + 1)) * 100);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Course Creator</h1>
          <p className="text-muted-foreground">Create and manage your course content</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Eye className="w-4 h-4 mr-2" />
            Preview
          </Button>
          <Button variant="outline">
            <Save className="w-4 h-4 mr-2" />
            Save Draft
          </Button>
          <Button>
            <Send className="w-4 h-4 mr-2" />
            Publish Course
          </Button>
        </div>
      </div>

      {/* Progress Overview */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-medium">Course Completion</h3>
            <span className="text-sm text-muted-foreground">{getCompletionPercentage()}% complete</span>
          </div>
          <Progress value={getCompletionPercentage()} className="mb-4" />
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
            <div className="text-center">
              <p className="font-medium">{sections.length}</p>
              <p className="text-muted-foreground">Sections</p>
            </div>
            <div className="text-center">
              <p className="font-medium">{sections.reduce((total, section) => total + section.lessons.length, 0)}</p>
              <p className="text-muted-foreground">Lessons</p>
            </div>
            <div className="text-center">
              <p className="font-medium">{formatDuration(getTotalDuration())}</p>
              <p className="text-muted-foreground">Total Duration</p>
            </div>
            <div className="text-center">
              <p className="font-medium">{courseData.price || 'Free'}</p>
              <p className="text-muted-foreground">Price</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="basic">Basic Info</TabsTrigger>
          <TabsTrigger value="curriculum">Curriculum</TabsTrigger>
          <TabsTrigger value="pricing">Pricing</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Course Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="title">Course Title *</Label>
                <Input
                  id="title"
                  placeholder="Enter course title"
                  value={courseData.title}
                  onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => setCourseData({...courseData, title: e.target.value})}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="subtitle">Course Subtitle</Label>
                <Input
                  id="subtitle"
                  placeholder="Enter course subtitle"
                  value={courseData.subtitle}
                  onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => setCourseData({...courseData, subtitle: e.target.value})}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Course Description *</Label>
                <Textarea
                  id="description"
                  placeholder="Describe what students will learn"
                  rows={4}
                  value={courseData.description}
                  onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => setCourseData({...courseData, description: e.target.value})}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="category">Category *</Label>
                  <Select value={courseData.category} onValueChange={(value) => setCourseData({...courseData, category: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="programming">Programming</SelectItem>
                      <SelectItem value="design">Design</SelectItem>
                      <SelectItem value="business">Business</SelectItem>
                      <SelectItem value="marketing">Marketing</SelectItem>
                      <SelectItem value="photography">Photography</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="level">Difficulty Level *</Label>
                  <Select value={courseData.level} onValueChange={(value) => setCourseData({...courseData, level: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="beginner">Beginner</SelectItem>
                      <SelectItem value="intermediate">Intermediate</SelectItem>
                      <SelectItem value="advanced">Advanced</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="thumbnail">Course Thumbnail</Label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  <Upload className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                  <p className="text-sm text-muted-foreground">
                    Click to upload or drag and drop
                  </p>
                  <p className="text-xs text-muted-foreground">
                    PNG, JPG up to 2MB (1280x720 recommended)
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="curriculum" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Course Curriculum
                <Button onClick={addSection}>
                  <Plus className="w-4 h-4 mr-2" />
                  Add Section
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {sections.map((section, sectionIndex) => (
                <div key={section.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2">
                      <GripVertical className="w-4 h-4 text-muted-foreground" />
                      <Input
                        value={section.title}
                        onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
                          const newSections = [...sections];
                          newSections[sectionIndex].title = e.target.value;
                          setSections(newSections);
                        }}
                        className="font-medium"
                      />
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => addLesson(section.id)}
                      >
                        <Plus className="w-4 h-4 mr-1" />
                        Add Lesson
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => removeSection(section.id)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    {section.lessons.map((lesson, lessonIndex) => (
                      <div key={lesson.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                        <GripVertical className="w-4 h-4 text-muted-foreground" />
                        {lesson.type === 'video' ? (
                          <Video className="w-4 h-4 text-blue-500" />
                        ) : (
                          <FileText className="w-4 h-4 text-green-500" />
                        )}
                        <Input
                          value={lesson.title}
                          onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
                            const newSections = [...sections];
                            newSections[sectionIndex].lessons[lessonIndex].title = e.target.value;
                            setSections(newSections);
                          }}
                          className="flex-1"
                        />
                        <Badge variant="outline">{lesson.type}</Badge>
                        <span className="text-sm text-muted-foreground">
                          {formatDuration(lesson.duration)}
                        </span>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => removeLesson(section.id, lesson.id)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="pricing" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Course Pricing</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="price">Course Price (USD)</Label>
                <Input
                  id="price"
                  type="number"
                  placeholder="0.00"
                  value={courseData.price}
                  onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => setCourseData({...courseData, price: e.target.value})}
                />
                <p className="text-sm text-muted-foreground">
                  Leave empty for free course. Recommended price range: $10-$200
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Course Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="language">Course Language</Label>
                <Select value={courseData.language} onValueChange={(value) => setCourseData({...courseData, language: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="English">English</SelectItem>
                    <SelectItem value="Spanish">Spanish</SelectItem>
                    <SelectItem value="French">French</SelectItem>
                    <SelectItem value="German">German</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CourseCreator;
