import React, { useState } from 'react';
import {
  FaUsers,
  FaUserTie,
  FaGraduationCap,
  FaDollarSign,
  FaShieldAlt,
  FaClock,
  FaBell,
  FaChartLine,
  FaBolt,
  FaEye,
  FaCheck,
  FaTimes,
  FaUser,
  FaBook,
  FaMoneyBillWave,
  FaFlag,
  FaCheckCircle,
  FaExclamationTriangle,
  FaChartBar,
  FaCalendarAlt,
  FaArrowUp,
  FaArrowDown,
  FaHeart
} from 'react-icons/fa';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Alert, AlertDescription } from '../ui/alert';
import { Progress } from '../ui/progress';
import { Separator } from '../ui/separator';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '../ui/dropdown-menu';
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '../ui/tooltip';
import { AlertCircle, ChevronDown, MoreVertical } from 'lucide-react';

interface AdminStats {
  totalUsers: number;
  totalInstructors: number;
  totalCourses: number;
  totalRevenue: number;
  pendingVerifications: number;
  activeSubscriptions: number;
  monthlyGrowth: {
    users: number;
    revenue: number;
    courses: number;
  };
  systemHealth: {
    uptime: number;
    responseTime: number;
    errorRate: number;
  };
}

interface RecentActivity {
  id: string;
  type: 'user_registration' | 'course_submission' | 'payment' | 'report' | 'verification';
  description: string;
  timestamp: string;
  status: 'pending' | 'completed' | 'failed';
  priority: 'low' | 'medium' | 'high';
}

interface PendingItem {
  id: string;
  type: 'course_verification' | 'instructor_application' | 'content_report' | 'payment_dispute' | 'user_report' | 'refund_request';
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  submittedBy: string;
  submittedAt: string;
}

interface SystemAlert {
  id: string;
  message: string;
  type: 'error' | 'warning' | 'info';
  timestamp: string;
  resolved: boolean;
}

interface AdminDashboardProps {
  stats: AdminStats;
  recentActivity: RecentActivity[];
  pendingItems: PendingItem[];
  systemAlerts: SystemAlert[];
  onViewUsers: () => void;
  onViewInstructors: () => void;
  onViewCourses: () => void;
  onViewReports: () => void;
  onHandlePendingItem: (itemId: string, action: 'approve' | 'reject' | 'view') => void;
  onResolveAlert: (alertId: string) => void;
  onViewAnalytics: () => void;
  loading?: boolean;
}

const AdminDashboard: React.FC<AdminDashboardProps> = ({
  stats,
  recentActivity,
  pendingItems,
  systemAlerts,
  onViewUsers,
  onViewInstructors,
  onViewCourses,
  onViewReports,
  onHandlePendingItem,
  onResolveAlert,
  onViewAnalytics,
  loading = false
}) => {
  const [selectedTimeRange, setSelectedTimeRange] = useState('7d');

  const getGrowthIcon = (growth: number) => {
    return growth >= 0 ? (
      <FaArrowUp className="text-green-500" />
    ) : (
      <FaArrowDown className="text-red-500" />
    );
  };

  const getActivityIcon = (type: string) => {
    const iconClass = "text-base";
    switch (type) {
      case 'user_registration': return <FaUser className={`${iconClass} text-blue-500`} />;
      case 'course_submission': return <FaBook className={`${iconClass} text-purple-500`} />;
      case 'payment': return <FaMoneyBillWave className={`${iconClass} text-green-500`} />;
      case 'report': return <FaFlag className={`${iconClass} text-yellow-500`} />;
      case 'verification': return <FaCheckCircle className={`${iconClass} text-green-500`} />;
      default: return <FaClock className={`${iconClass} text-gray-500`} />;
    }
  };

  const formatTimeAgo = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  const renderStatsCards = () => (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <Card className="h-full bg-gradient-to-br from-blue-500 to-blue-600 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300">
        <CardContent className="p-6">
          <div className="flex justify-between items-start">
            <div>
              <div className="text-sm opacity-90 mb-2">Total Users</div>
              <div className="text-3xl font-bold mb-1">
                {stats.totalUsers.toLocaleString()}
              </div>
              <div className="flex items-center gap-1">
                {getGrowthIcon(stats.monthlyGrowth.users)}
                <span className="text-white/90 text-xs">
                  {Math.abs(stats.monthlyGrowth.users)}% this month
                </span>
              </div>
            </div>
            <div className="bg-white/20 rounded-full p-3 flex items-center justify-center">
              <FaUsers className="text-2xl" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="h-full bg-gradient-to-br from-green-500 to-green-600 text-white rounded-xl shadow-lg">
        <CardContent className="p-6">
          <div className="text-center">
            <div className="text-sm opacity-90 mb-2">Total Instructors</div>
            <div className="text-3xl font-bold">{stats.totalInstructors.toLocaleString()}</div>
          </div>
        </CardContent>
      </Card>

      <Card className="h-full bg-gradient-to-br from-purple-500 to-purple-600 text-white rounded-xl shadow-lg">
        <CardContent className="p-6">
          <div className="text-center">
            <div className="text-sm opacity-90 mb-2">Total Courses</div>
            <div className="text-3xl font-bold">{stats.totalCourses.toLocaleString()}</div>
          </div>
        </CardContent>
      </Card>

      <Card className="h-full bg-gradient-to-br from-yellow-500 to-yellow-600 text-white rounded-xl shadow-lg">
        <CardContent className="p-6">
          <div className="text-center">
            <div className="text-sm opacity-90 mb-2">Total Revenue</div>
            <div className="text-3xl font-bold">${stats.totalRevenue.toLocaleString()}</div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderRecentActivity = () => (
    <Card>
      <CardHeader>
        <CardTitle>Recent Activity</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {recentActivity.slice(0, 5).map((activity) => (
            <div key={activity.id} className="flex items-center gap-3 p-3 rounded-lg bg-gray-50">
              {getActivityIcon(activity.type)}
              <div className="flex-1">
                <p className="text-sm font-medium">{activity.description}</p>
                <p className="text-xs text-gray-500">{formatTimeAgo(activity.timestamp)}</p>
              </div>
              <Badge variant={activity.status === 'completed' ? 'default' : 'secondary'}>
                {activity.status}
              </Badge>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );

  const renderPendingItems = () => (
    <Card>
      <CardHeader>
        <CardTitle>Pending Items</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {pendingItems.slice(0, 5).map((item) => (
            <div key={item.id} className="flex items-center justify-between p-3 rounded-lg bg-gray-50">
              <div>
                <p className="text-sm font-medium">{item.title}</p>
                <p className="text-xs text-gray-500">{item.description}</p>
              </div>
              <div className="flex gap-2">
                <Button size="sm" onClick={() => onHandlePendingItem(item.id, 'approve')}>
                  Approve
                </Button>
                <Button size="sm" variant="outline" onClick={() => onHandlePendingItem(item.id, 'reject')}>
                  Reject
                </Button>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600"></div>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className="space-y-6">
        {/* Stats Cards */}
        {renderStatsCards()}

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {renderRecentActivity()}
          {renderPendingItems()}
        </div>

        {/* System Alerts */}
        {systemAlerts.length > 0 && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">System Alerts</h3>
            {systemAlerts.map((alert) => (
              <Alert key={alert.id} className="border-l-4 border-l-red-500">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="font-medium">{alert.message}</p>
                      <p className="text-sm text-gray-600">{formatTimeAgo(alert.timestamp)}</p>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => onResolveAlert(alert.id)}
                    >
                      Resolve
                    </Button>
                  </div>
                </AlertDescription>
              </Alert>
            ))}
          </div>
        )}
      </div>
    </TooltipProvider>
  );
};

export default AdminDashboard;
