import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import { useAuth } from '../../contexts/AuthContext';
import {
  BookOpen,
  User,
  Heart,
  Clock,
  Calendar,
  MessageCircle,
  Edit,
  CreditCard,
  Shield,
  HelpCircle,
  Bell,
  FileText,
  Settings,
  Award,
  TrendingUp,
  Users,
  Star,
  GraduationCap,
  Home,
  PlayCircle,
  Target,
  Trophy,
  Bookmark
} from 'lucide-react';

interface UserSidebarProps {
  collapsed?: boolean;
  onToggle?: () => void;
}

const UserSidebar: React.FC<UserSidebarProps> = ({
  collapsed = false,
  onToggle
}) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { authState } = useAuth();
  const { user } = authState;

  // Helper function to get user's display name
  const getUserDisplayName = () => {
    if (!user) return 'User Name';
    const firstName = user.Firstname ? String(user.Firstname).trim() : '';
    const lastName = user.Lastname ? String(user.Lastname).trim() : '';
    const fullName = `${firstName} ${lastName}`.trim();
    return fullName || (user.Username ? String(user.Username) : 'User Name');
  };

  // Helper function to get user's role display
  const getUserRoleDisplay = () => {
    if (!user) return 'Student';
    const role = user.Profil || user.Role;
    if (!role) return 'Student';
    const roleStr = String(role);
    switch (roleStr.toLowerCase()) {
      case 'admin': return 'Administrator';
      case 'instructor': return 'Instructor';
      case 'user': return 'Student';
      default: return roleStr.charAt(0).toUpperCase() + roleStr.slice(1).toLowerCase();
    }
  };

  // Get current selected menu key based on pathname
  const getSelectedKey = () => {
    const path = location.pathname;
    if (path === '/user') return 'dashboard';
    if (path.startsWith('/user/progress')) return 'progress';
    if (path.startsWith('/user/courses') && !path.includes('/save')) return 'courses';
    if (path.startsWith('/user/courses/save')) return 'courses-save';
    if (path.startsWith('/user/library')) return 'library';
    if (path.startsWith('/user/analytics')) return 'analytics';
    if (path.startsWith('/user/achievements')) return 'achievements';
    if (path.startsWith('/user/messages')) return 'messages';
    if (path.startsWith('/user/calendar')) return 'calendar';
    if (path.startsWith('/user/reviews')) return 'reviews';
    if (path.startsWith('/user/profile')) return 'profile';
    if (path.startsWith('/user/account')) return 'account';
    if (path.startsWith('/user/billing')) return 'billing';
    if (path.startsWith('/user/security')) return 'security';
    if (path.startsWith('/user/notifications')) return 'notifications';
    if (path.startsWith('/user/help')) return 'help';
    if (path.startsWith('/user/feedback')) return 'feedback';
    if (path.startsWith('/user/settings')) return 'settings';
    return 'dashboard';
  };

  // Enhanced menu items with better organization - using Lucide icons
  const menuItems = [
    {
      key: 'dashboard-group',
      label: collapsed ? '' : 'Learning Hub',
      type: 'group',
      children: [
        {
          key: 'dashboard',
          icon: <Home className="w-5 h-5" />,
          label: 'Dashboard',
        },
        {
          key: 'progress',
          icon: <TrendingUp className="w-5 h-5" />,
          label: 'My Progress',
        },
      ],
    },
    {
      key: 'learning-group',
      label: collapsed ? '' : 'Learning',
      type: 'group',
      children: [
        {
          key: 'courses',
          icon: <BookOpen className="w-5 h-5" />,
          label: 'My Courses',
        },
        {
          key: 'courses-save',
          icon: <Bookmark className="w-5 h-5" />,
          label: 'Saved Courses',
        },
        {
          key: 'library',
          icon: <PlayCircle className="w-5 h-5" />,
          label: 'Library',
        },
        {
          key: 'analytics',
          icon: <Target className="w-5 h-5" />,
          label: 'Learning Analytics',
        },
        {
          key: 'achievements',
          icon: <Trophy className="w-5 h-5" />,
          label: 'Achievements',
        },
      ],
    },
    {
      key: 'community-group',
      label: collapsed ? '' : 'Community',
      type: 'group',
      children: [
        {
          key: 'messages',
          icon: <MessageCircle className="w-5 h-5" />,
          label: 'Messages',
        },
        {
          key: 'calendar',
          icon: <Calendar className="w-5 h-5" />,
          label: 'Calendar',
        },
        {
          key: 'reviews',
          icon: <Star className="w-5 h-5" />,
          label: 'Reviews',
        },
      ],
    },
    {
      key: 'account-group',
      label: collapsed ? '' : 'Account',
      type: 'group',
      children: [
        {
          key: 'profile',
          icon: <User className="w-5 h-5" />,
          label: 'Profile',
        },
        {
          key: 'account',
          icon: <Edit className="w-5 h-5" />,
          label: 'Account Settings',
        },
        {
          key: 'billing',
          icon: <CreditCard className="w-5 h-5" />,
          label: 'Billing',
        },
        {
          key: 'security',
          icon: <Shield className="w-5 h-5" />,
          label: 'Security',
        },
        {
          key: 'notifications',
          icon: <Bell className="w-5 h-5" />,
          label: 'Notifications',
        },
      ],
    },
    {
      key: 'support-group',
      label: collapsed ? '' : 'Support',
      type: 'group',
      children: [
        {
          key: 'help',
          icon: <HelpCircle className="w-5 h-5" />,
          label: 'Help Center',
        },
        {
          key: 'feedback',
          icon: <FileText className="w-5 h-5" />,
          label: 'Feedback',
        },
        {
          key: 'settings',
          icon: <Settings className="w-5 h-5" />,
          label: 'Settings',
        },
      ],
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    const routes: { [key: string]: string } = {
      dashboard: '/user',
      progress: '/user/progress',
      courses: '/user/courses',
      'courses-save': '/user/courses/save',
      library: '/user/library',
      analytics: '/user/analytics',
      achievements: '/user/achievements',
      messages: '/user/messages',
      calendar: '/user/calendar',
      reviews: '/user/reviews',
      profile: '/user/profile',
      account: '/user/account',
      billing: '/user/billing',
      security: '/user/security',
      notifications: '/user/notifications',
      help: '/user/help',
      feedback: '/user/feedback',
      settings: '/user/settings',
    };

    if (routes[key]) {
      navigate(routes[key]);
    }
  };

  return (
    <div className="h-full flex flex-col bg-gray-900">
      {/* Enhanced Header */}
      <div className={cn(
        "border-b border-gray-600 bg-gradient-to-br from-red-600 to-red-700 transition-all duration-300",
        collapsed ? "px-2 py-4" : "px-4 py-6"
      )}>
        {!collapsed ? (
          <div className="space-y-3 w-full">
            <div className="flex items-center space-x-3">
              <Avatar className="w-10 h-10 bg-white/20 text-white">
                <AvatarImage
                  src={user?.Photo?.Hashname ? `${import.meta.env.VITE_API_URL || 'http://localhost:3200'}/${user.Photo.Hashname}` : undefined}
                  alt={`${user?.Firstname || user?.Username || 'User'} profile picture`}
                />
                <AvatarFallback className="bg-white/20 text-white">
                  {user?.Username?.charAt(0).toUpperCase() || <User className="w-5 h-5" />}
                </AvatarFallback>
              </Avatar>
              <div>
                <h5 className="text-white font-nunito-bold text-sm m-0 leading-tight">
                  {getUserDisplayName()}
                </h5>
                <p className="text-white/80 font-nunito-medium text-xs m-0 leading-tight">
                  {getUserRoleDisplay()}
                </p>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Avatar className="w-8 h-8 bg-white/20 text-white mx-auto">
                    <AvatarImage 
                      src={user?.Photo?.Hashname ? `http://localhost:3200/${user.Photo.Hashname}` : undefined} 
                    />
                    <AvatarFallback className="bg-white/20 text-white">
                      {user?.Username?.charAt(0).toUpperCase() || <User className="w-4 h-4" />}
                    </AvatarFallback>
                  </Avatar>
                </TooltipTrigger>
                <TooltipContent side="right">
                  <p>{getUserDisplayName()}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        )}
      </div>

      {/* Enhanced Navigation Menu */}
      <div className="flex-1 overflow-auto">
        <nav className="p-2">
          {menuItems.map((group) => (
            <div key={group.key} className="mb-6">
              {!collapsed && group.label && (
                <h6 className="text-xs font-nunito-semibold text-white/60 uppercase tracking-wider mb-3 px-3">
                  {group.label}
                </h6>
              )}
              <div className="space-y-1">
                {group.children?.map((item) => {
                  const isActive = getSelectedKey() === item.key;
                  return (
                    <TooltipProvider key={item.key}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            className={cn(
                              "w-full justify-start h-11 px-3 text-white/80 hover:text-white hover:bg-white/10 font-nunito-medium transition-all duration-200",
                              isActive && "bg-red-500 text-white hover:bg-red-600",
                              collapsed && "px-2 justify-center"
                            )}
                            onClick={() => handleMenuClick({ key: item.key })}
                          >
                            {item.icon}
                            {!collapsed && (
                              <span className="ml-3 text-sm">{item.label}</span>
                            )}
                          </Button>
                        </TooltipTrigger>
                        {collapsed && (
                          <TooltipContent side="right">
                            <p>{item.label}</p>
                          </TooltipContent>
                        )}
                      </Tooltip>
                    </TooltipProvider>
                  );
                })}
              </div>
            </div>
          ))}
        </nav>
      </div>

      {/* Footer - Only show when not collapsed */}
      {!collapsed && (
        <div className="p-4 border-t border-gray-600 bg-black/20">
          <div className="space-y-1 w-full text-center">
            <p className="text-white/60 text-xs font-nunito-medium">
              BrainMaker Academy
            </p>
            <p className="text-white/40 text-xs font-nunito-regular">
              Student Portal v2.1
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserSidebar;
