import React from 'react';
import { CourseCard } from './course-card';
import { Button } from './button';
import { Badge } from './badge';
import { cn } from '@/lib/utils';

interface Course {
  id: string;
  title: string;
  slug: string;
  description?: string;
  image?: string;
  instructor: {
    name: string;
    avatar?: string;
  };
  price: {
    current: number;
    original?: number;
    currency: string;
  };
  rating: {
    average: number;
    count: number;
  };
  duration?: string;
  studentsCount?: number;
  lessonsCount?: number;
  level?: 'Beginner' | 'Intermediate' | 'Advanced';
  category?: string;
  isBestseller?: boolean;
  isFree?: boolean;
  isNew?: boolean;
}

interface Category {
  id: string;
  name: string;
  slug: string;
  count?: number;
}

interface CourseGridProps {
  courses: Course[];
  categories?: Category[];
  selectedCategory?: string;
  onCategoryChange?: (categoryId: string | null) => void;
  isLoading?: boolean;
  showFilters?: boolean;
  showFavorites?: boolean;
  onFavoriteToggle?: (courseId: string) => void;
  favoriteIds?: string[];
  title?: string;
  subtitle?: string;
  className?: string;
}

export function CourseGrid({
  courses,
  categories = [],
  selectedCategory,
  onCategoryChange,
  isLoading = false,
  showFilters = true,
  showFavorites = false,
  onFavoriteToggle,
  favoriteIds = [],
  title = "Popular Courses",
  subtitle = "Discover our most popular courses across different categories",
  className
}: CourseGridProps) {
  
  const LoadingSkeleton = () => (
    <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
      {Array.from({ length: 8 }).map((_, i) => (
        <div key={i} className="space-y-4">
          <div className="aspect-video animate-pulse rounded-lg bg-muted" />
          <div className="space-y-2">
            <div className="h-4 animate-pulse rounded bg-muted" />
            <div className="h-4 w-3/4 animate-pulse rounded bg-muted" />
            <div className="h-4 w-1/2 animate-pulse rounded bg-muted" />
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <section className={cn("py-16", className)}>
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="mb-12 text-center">
          <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
            {title}
          </h2>
          <p className="mx-auto max-w-2xl text-lg text-muted-foreground">
            {subtitle}
          </p>
        </div>

        {/* Category Filters */}
        {showFilters && categories.length > 0 && (
          <div className="mb-8">
            <div className="flex flex-wrap justify-center gap-2">
              <Button
                variant={!selectedCategory ? "default" : "outline"}
                size="sm"
                onClick={() => onCategoryChange?.(null)}
              >
                All Courses
              </Button>
              {categories.map((category) => (
                <Button
                  key={category.id}
                  variant={selectedCategory === category.id ? "default" : "outline"}
                  size="sm"
                  onClick={() => onCategoryChange?.(category.id)}
                  className="relative"
                >
                  {category.name}
                  {category.count && (
                    <Badge 
                      variant="secondary" 
                      className="ml-2 h-5 px-1.5 text-xs"
                    >
                      {category.count}
                    </Badge>
                  )}
                </Button>
              ))}
            </div>
          </div>
        )}

        {/* Course Grid */}
        {isLoading ? (
          <LoadingSkeleton />
        ) : courses.length > 0 ? (
          <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {courses.map((course) => (
              <CourseCard
                key={course.id}
                course={course}
                showFavorite={showFavorites}
                onFavoriteToggle={onFavoriteToggle}
                isFavorite={favoriteIds.includes(course.id)}
              />
            ))}
          </div>
        ) : (
          <div className="py-16 text-center">
            <div className="mx-auto max-w-md">
              <div className="mb-4 text-6xl">📚</div>
              <h3 className="mb-2 text-xl font-semibold">No courses found</h3>
              <p className="text-muted-foreground">
                {selectedCategory 
                  ? "No courses found in this category. Try selecting a different category."
                  : "No courses available at the moment. Please check back later."
                }
              </p>
              {selectedCategory && (
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={() => onCategoryChange?.(null)}
                >
                  View All Courses
                </Button>
              )}
            </div>
          </div>
        )}

        {/* Load More Button */}
        {courses.length > 0 && courses.length % 12 === 0 && (
          <div className="mt-12 text-center">
            <Button variant="outline" size="lg">
              Load More Courses
            </Button>
          </div>
        )}
      </div>
    </section>
  );
}
