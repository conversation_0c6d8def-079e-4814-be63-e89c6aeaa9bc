import React from 'react';
import { Loader2, Alert<PERSON>ircle, RefreshCw, Wifi, WifiOff } from 'lucide-react';
import { Button } from './button';
import { Card, CardContent, CardHeader, CardTitle } from './card';
import { Skeleton } from './skeleton';
import { cn } from '../../lib/utils';

// Loading spinner with customizable size and color
interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  color?: 'primary' | 'secondary' | 'muted';
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  className,
  color = 'primary'
}) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8',
    xl: 'h-12 w-12'
  };

  const colorClasses = {
    primary: 'text-red-600',
    secondary: 'text-gray-600',
    muted: 'text-gray-400'
  };

  return (
    <Loader2 
      className={cn(
        'animate-spin',
        sizeClasses[size],
        colorClasses[color],
        className
      )} 
    />
  );
};

// Full page loading overlay
interface LoadingOverlayProps {
  message?: string;
  description?: string;
  showSpinner?: boolean;
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  message = 'Loading...',
  description,
  showSpinner = true
}) => {
  return (
    <div className="fixed inset-0 bg-white/80 backdrop-blur-sm z-50 flex items-center justify-center">
      <div className="text-center space-y-4">
        {showSpinner && <LoadingSpinner size="xl" />}
        <div>
          <h3 className="text-lg font-nunito-semibold text-gray-900">{message}</h3>
          {description && (
            <p className="text-sm text-gray-600 mt-2">{description}</p>
          )}
        </div>
      </div>
    </div>
  );
};

// Inline loading state
interface InlineLoadingProps {
  message?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const InlineLoading: React.FC<InlineLoadingProps> = ({
  message = 'Loading...',
  size = 'md',
  className
}) => {
  return (
    <div className={cn('flex items-center space-x-2', className)}>
      <LoadingSpinner size={size} />
      <span className="text-sm text-gray-600">{message}</span>
    </div>
  );
};

// Card skeleton loader
interface CardSkeletonProps {
  lines?: number;
  showHeader?: boolean;
  showAvatar?: boolean;
  className?: string;
}

export const CardSkeleton: React.FC<CardSkeletonProps> = ({
  lines = 3,
  showHeader = true,
  showAvatar = false,
  className
}) => {
  return (
    <Card className={className}>
      {showHeader && (
        <CardHeader>
          <div className="flex items-center space-x-3">
            {showAvatar && <Skeleton className="h-10 w-10 rounded-full" />}
            <div className="space-y-2 flex-1">
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-3 w-1/2" />
            </div>
          </div>
        </CardHeader>
      )}
      <CardContent>
        <div className="space-y-2">
          {Array.from({ length: lines }).map((_, i) => (
            <Skeleton 
              key={i} 
              className={cn(
                'h-3',
                i === lines - 1 ? 'w-2/3' : 'w-full'
              )} 
            />
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

// Table skeleton loader
interface TableSkeletonProps {
  rows?: number;
  columns?: number;
  showHeader?: boolean;
  className?: string;
}

export const TableSkeleton: React.FC<TableSkeletonProps> = ({
  rows = 5,
  columns = 4,
  showHeader = true,
  className
}) => {
  return (
    <div className={cn('space-y-3', className)}>
      {showHeader && (
        <div className="flex space-x-4">
          {Array.from({ length: columns }).map((_, i) => (
            <Skeleton key={i} className="h-4 flex-1" />
          ))}
        </div>
      )}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="flex space-x-4">
          {Array.from({ length: columns }).map((_, colIndex) => (
            <Skeleton key={colIndex} className="h-8 flex-1" />
          ))}
        </div>
      ))}
    </div>
  );
};

// Error state component
interface ErrorStateProps {
  title?: string;
  message?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  showIcon?: boolean;
  variant?: 'error' | 'warning' | 'info';
  className?: string;
}

export const ErrorState: React.FC<ErrorStateProps> = ({
  title = 'Something went wrong',
  message = 'An error occurred while loading this content.',
  action,
  showIcon = true,
  variant = 'error',
  className
}) => {
  const iconMap = {
    error: AlertCircle,
    warning: AlertCircle,
    info: AlertCircle
  };

  const colorMap = {
    error: 'text-red-600',
    warning: 'text-yellow-600',
    info: 'text-blue-600'
  };

  const Icon = iconMap[variant];

  return (
    <div className={cn('text-center py-8 px-4', className)}>
      {showIcon && (
        <Icon className={cn('h-12 w-12 mx-auto mb-4', colorMap[variant])} />
      )}
      <h3 className="text-lg font-nunito-semibold text-gray-900 mb-2">{title}</h3>
      <p className="text-gray-600 mb-4 max-w-md mx-auto">{message}</p>
      {action && (
        <Button onClick={action.onClick} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          {action.label}
        </Button>
      )}
    </div>
  );
};

// Empty state component
interface EmptyStateProps {
  title?: string;
  message?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  icon?: React.ReactNode;
  className?: string;
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  title = 'No data found',
  message = 'There is no data to display at the moment.',
  action,
  icon,
  className
}) => {
  return (
    <div className={cn('text-center py-12 px-4', className)}>
      {icon && (
        <div className="mb-4 flex justify-center">
          {icon}
        </div>
      )}
      <h3 className="text-lg font-nunito-semibold text-gray-900 mb-2">{title}</h3>
      <p className="text-gray-600 mb-4 max-w-md mx-auto">{message}</p>
      {action && (
        <Button onClick={action.onClick}>
          {action.label}
        </Button>
      )}
    </div>
  );
};

// Network status indicator
interface NetworkStatusProps {
  isOnline?: boolean;
  className?: string;
}

export const NetworkStatus: React.FC<NetworkStatusProps> = ({
  isOnline = navigator.onLine,
  className
}) => {
  if (isOnline) return null;

  return (
    <div className={cn(
      'fixed top-0 left-0 right-0 bg-red-600 text-white text-center py-2 z-50',
      className
    )}>
      <div className="flex items-center justify-center space-x-2">
        <WifiOff className="h-4 w-4" />
        <span className="text-sm font-medium">You are currently offline</span>
      </div>
    </div>
  );
};

// Progressive loading component
interface ProgressiveLoadingProps {
  stages: Array<{
    message: string;
    duration?: number;
  }>;
  currentStage: number;
  className?: string;
}

export const ProgressiveLoading: React.FC<ProgressiveLoadingProps> = ({
  stages,
  currentStage,
  className
}) => {
  const currentMessage = stages[Math.min(currentStage, stages.length - 1)]?.message || 'Loading...';

  return (
    <div className={cn('text-center space-y-4', className)}>
      <LoadingSpinner size="lg" />
      <div>
        <p className="text-sm text-gray-600">{currentMessage}</p>
        <div className="mt-2 w-48 mx-auto bg-gray-200 rounded-full h-1">
          <div 
            className="bg-red-600 h-1 rounded-full transition-all duration-300"
            style={{ 
              width: `${((currentStage + 1) / stages.length) * 100}%` 
            }}
          />
        </div>
      </div>
    </div>
  );
};

// Retry component
interface RetryComponentProps {
  onRetry: () => void;
  error?: string;
  attempts?: number;
  maxAttempts?: number;
  className?: string;
}

export const RetryComponent: React.FC<RetryComponentProps> = ({
  onRetry,
  error = 'Failed to load data',
  attempts = 0,
  maxAttempts = 3,
  className
}) => {
  const canRetry = attempts < maxAttempts;

  return (
    <div className={cn('text-center py-8', className)}>
      <AlertCircle className="h-8 w-8 text-red-600 mx-auto mb-4" />
      <h3 className="text-lg font-nunito-semibold text-gray-900 mb-2">
        {canRetry ? 'Loading Failed' : 'Maximum Retries Exceeded'}
      </h3>
      <p className="text-gray-600 mb-4">{error}</p>
      {attempts > 0 && (
        <p className="text-sm text-gray-500 mb-4">
          Attempt {attempts} of {maxAttempts}
        </p>
      )}
      {canRetry && (
        <Button onClick={onRetry} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Try Again
        </Button>
      )}
    </div>
  );
};
