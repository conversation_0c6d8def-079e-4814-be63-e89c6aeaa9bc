import * as React from "react"
import { ChevronDown } from "lucide-react"
import { cn } from "@/lib/utils"

interface AccordionContextType {
  openItems: string[]
  toggleItem: (value: string) => void
  type: 'single' | 'multiple'
}

const AccordionContext = React.createContext<AccordionContextType | undefined>(undefined)

const useAccordion = () => {
  const context = React.useContext(AccordionContext)
  if (!context) {
    throw new Error("useAccordion must be used within an Accordion component")
  }
  return context
}

interface AccordionProps {
  type?: 'single' | 'multiple'
  defaultValue?: string | string[]
  value?: string | string[]
  onValueChange?: (value: string | string[]) => void
  children: React.ReactNode
  className?: string
  collapsible?: boolean
}

const Accordion: React.FC<AccordionProps> = ({
  type = 'single',
  defaultValue,
  value: controlledValue,
  onValueChange,
  children,
  className,
  collapsible = true
}) => {
  const getInitialValue = () => {
    if (controlledValue !== undefined) {
      return Array.isArray(controlledValue) ? controlledValue : [controlledValue]
    }
    if (defaultValue !== undefined) {
      return Array.isArray(defaultValue) ? defaultValue : [defaultValue]
    }
    return []
  }

  const [openItems, setOpenItems] = React.useState<string[]>(getInitialValue)

  React.useEffect(() => {
    if (controlledValue !== undefined) {
      setOpenItems(Array.isArray(controlledValue) ? controlledValue : [controlledValue])
    }
  }, [controlledValue])

  const toggleItem = (value: string) => {
    let newOpenItems: string[]

    if (type === 'single') {
      if (openItems.includes(value)) {
        newOpenItems = collapsible ? [] : [value]
      } else {
        newOpenItems = [value]
      }
    } else {
      if (openItems.includes(value)) {
        newOpenItems = openItems.filter(item => item !== value)
      } else {
        newOpenItems = [...openItems, value]
      }
    }

    if (controlledValue === undefined) {
      setOpenItems(newOpenItems)
    }

    const returnValue = type === 'single' ? newOpenItems[0] || '' : newOpenItems
    onValueChange?.(returnValue)
  }

  return (
    <AccordionContext.Provider value={{ openItems, toggleItem, type }}>
      <div className={cn("space-y-2", className)}>
        {children}
      </div>
    </AccordionContext.Provider>
  )
}

interface AccordionItemProps {
  value: string
  children: React.ReactNode
  className?: string
}

const AccordionItem: React.FC<AccordionItemProps> = ({
  value,
  children,
  className
}) => {
  return (
    <div className={cn("border rounded-lg", className)} data-value={value}>
      {children}
    </div>
  )
}

interface AccordionTriggerProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode
}

const AccordionTrigger = React.forwardRef<HTMLButtonElement, AccordionTriggerProps>(
  ({ className, children, ...props }, ref) => {
    const { openItems, toggleItem } = useAccordion()
    const item = React.useContext(AccordionItemContext)
    
    if (!item) {
      throw new Error("AccordionTrigger must be used within an AccordionItem")
    }

    const isOpen = openItems.includes(item.value)

    return (
      <button
        ref={ref}
        className={cn(
          "flex flex-1 items-center justify-between py-4 px-6 font-medium transition-all hover:bg-muted/50 [&[data-state=open]>svg]:rotate-180",
          className
        )}
        onClick={() => toggleItem(item.value)}
        data-state={isOpen ? "open" : "closed"}
        {...props}
      >
        {children}
        <ChevronDown className="h-4 w-4 shrink-0 transition-transform duration-200" />
      </button>
    )
  }
)
AccordionTrigger.displayName = "AccordionTrigger"

interface AccordionContentProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
}

const AccordionContent = React.forwardRef<HTMLDivElement, AccordionContentProps>(
  ({ className, children, ...props }, ref) => {
    const { openItems } = useAccordion()
    const item = React.useContext(AccordionItemContext)
    
    if (!item) {
      throw new Error("AccordionContent must be used within an AccordionItem")
    }

    const isOpen = openItems.includes(item.value)

    return (
      <div
        ref={ref}
        className={cn(
          "overflow-hidden transition-all duration-200",
          isOpen ? "animate-accordion-down" : "animate-accordion-up"
        )}
        data-state={isOpen ? "open" : "closed"}
        {...props}
      >
        {isOpen && (
          <div className={cn("px-6 pb-4 pt-0", className)}>
            {children}
          </div>
        )}
      </div>
    )
  }
)
AccordionContent.displayName = "AccordionContent"

// Context for AccordionItem
const AccordionItemContext = React.createContext<{ value: string } | undefined>(undefined)

// Enhanced AccordionItem with context
const EnhancedAccordionItem: React.FC<AccordionItemProps> = ({ value, children, className }) => {
  return (
    <AccordionItemContext.Provider value={{ value }}>
      <AccordionItem value={value} className={className}>
        {children}
      </AccordionItem>
    </AccordionItemContext.Provider>
  )
}

export {
  Accordion,
  EnhancedAccordionItem as AccordionItem,
  AccordionTrigger,
  AccordionContent,
}
