import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ChevronLeft, ChevronRight, ArrowRight } from "lucide-react";
import { cn } from "@/lib/utils";

// Import banner images
import homeBanner1 from '../../assets/img/home_banner1.jpg';
import homeBanner from '../../assets/img/home_banner.png';
import home1Banner from '../../assets/img/home1_banner.png';
import learning from '../../assets/img/learning.jpg';
import learning1 from '../../assets/img/learning1.jpg';

interface HeroSlide {
  id: number;
  image: string;
  title: string;
  subtitle: string;
  primaryCta: string;
  secondaryCta: string;
  badge?: string;
}

const heroSlides: HeroSlide[] = [
  {
    id: 1,
    image: homeBanner1,
    title: "Learn by doing",
    subtitle: "Get access to the best online courses for creative professionals. Interact with the top professionals and discover the creative world's best-kept secrets.",
    primaryCta: "Start exploring",
    secondaryCta: "Browse courses",
    badge: "New"
  },
  {
    id: 2,
    image: learning,
    title: "Master new skills",
    subtitle: "Join thousands of students learning from industry experts. Build your portfolio with hands-on projects and real-world experience.",
    primaryCta: "Explore courses",
    secondaryCta: "View catalog"
  },
  {
    id: 3,
    image: learning1,
    title: "Advance your career",
    subtitle: "Learn from the best professionals in the industry. Get certified and take your career to the next level with our comprehensive courses.",
    primaryCta: "Get started",
    secondaryCta: "Learn more"
  },
  {
    id: 4,
    image: home1Banner,
    title: "Creative excellence",
    subtitle: "Discover courses designed by creative professionals. From design to development, master the skills that matter in today's digital world.",
    primaryCta: "Start learning",
    secondaryCta: "View courses"
  }
];

export function HeroSection() {
  const [currentSlide, setCurrentSlide] = useState(0);

  // Auto-play functionality
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
    }, 5000);

    return () => clearInterval(timer);
  }, []);

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + heroSlides.length) % heroSlides.length);
  };

  const currentSlideData = heroSlides[currentSlide];

  return (
    <section className="relative h-[600px] overflow-hidden">
      {/* Background Image with Overlay */}
      <div 
        className="absolute inset-0 bg-cover bg-center transition-all duration-1000 ease-in-out"
        style={{
          backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url(${currentSlideData.image})`
        }}
      />
      
      {/* Content */}
      <div className="relative z-10 flex h-full items-center justify-center">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            {/* Badge */}
            {currentSlideData.badge && (
              <Badge variant="secondary" className="mb-4 bg-white/10 text-white border-white/20">
                {currentSlideData.badge}
              </Badge>
            )}
            
            {/* Title */}
            <h1 className="mb-6 text-4xl font-bold tracking-tight text-white sm:text-5xl md:text-6xl">
              {currentSlideData.title}
            </h1>
            
            {/* Subtitle */}
            <p className="mx-auto mb-8 max-w-2xl text-lg text-white/90 sm:text-xl">
              {currentSlideData.subtitle}
            </p>
            
            {/* CTA Buttons */}
            <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
              <Button size="lg" className="bg-primary hover:bg-primary/90">
                {currentSlideData.primaryCta}
              </Button>
              <Button size="lg" variant="outline" className="border-white/50 bg-white/10 text-white hover:bg-white/20">
                {currentSlideData.secondaryCta}
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Arrows */}
      <Button
        variant="outline"
        size="icon"
        className="absolute left-4 top-1/2 -translate-y-1/2 border-white/50 bg-white/10 text-white hover:bg-white/20"
        onClick={prevSlide}
      >
        <ChevronLeft className="h-4 w-4" />
      </Button>
      
      <Button
        variant="outline"
        size="icon"
        className="absolute right-4 top-1/2 -translate-y-1/2 border-white/50 bg-white/10 text-white hover:bg-white/20"
        onClick={nextSlide}
      >
        <ChevronRight className="h-4 w-4" />
      </Button>

      {/* Slide Indicators */}
      <div className="absolute bottom-6 left-1/2 flex -translate-x-1/2 space-x-2">
        {heroSlides.map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={cn(
              "h-2 w-2 rounded-full transition-all",
              index === currentSlide
                ? "bg-white w-8"
                : "bg-white/50 hover:bg-white/75"
            )}
          />
        ))}
      </div>
    </section>
  );
}
