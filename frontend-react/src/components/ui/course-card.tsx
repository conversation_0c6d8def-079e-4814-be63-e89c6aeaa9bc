import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Star, Clock, Users, BookOpen, Heart } from "lucide-react";
import { cn } from "@/lib/utils";

interface CourseCardProps {
  course: {
    id: string;
    title: string;
    slug: string;
    description?: string;
    image?: string;
    instructor: {
      name: string;
      avatar?: string;
    };
    price: {
      current: number;
      original?: number;
      currency: string;
    };
    rating: {
      average: number;
      count: number;
    };
    duration?: string;
    studentsCount?: number;
    lessonsCount?: number;
    level?: 'Beginner' | 'Intermediate' | 'Advanced';
    category?: string;
    isBestseller?: boolean;
    isFree?: boolean;
    isNew?: boolean;
  };
  variant?: 'default' | 'compact' | 'featured';
  showFavorite?: boolean;
  onFavoriteToggle?: (courseId: string) => void;
  isFavorite?: boolean;
  className?: string;
}

export function CourseCard({
  course,
  variant = 'default',
  showFavorite = false,
  onFavoriteToggle,
  isFavorite = false,
  className
}: CourseCardProps) {
  const formatPrice = (price: number, currency: string) => {
    if (price === 0) return 'Free';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(price);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={cn(
          "h-3 w-3",
          i < Math.floor(rating) 
            ? "fill-yellow-400 text-yellow-400" 
            : "text-gray-300"
        )}
      />
    ));
  };

  return (
    <Card className={cn("group overflow-hidden transition-all hover:shadow-lg", className)}>
      <Link to={`/courses/${course.slug}`} className="block">
        {/* Course Image */}
        <div className="relative aspect-video overflow-hidden">
          {course.image ? (
            <img
              src={course.image}
              alt={course.title}
              className="h-full w-full object-cover transition-transform group-hover:scale-105"
            />
          ) : (
            <div className="flex h-full w-full items-center justify-center bg-muted">
              <BookOpen className="h-12 w-12 text-muted-foreground" />
            </div>
          )}
          
          {/* Badges */}
          <div className="absolute left-3 top-3 flex flex-col gap-1">
            {course.isBestseller && (
              <Badge variant="default" className="bg-orange-500 hover:bg-orange-600">
                Bestseller
              </Badge>
            )}
            {course.isNew && (
              <Badge variant="default" className="bg-green-500 hover:bg-green-600">
                New
              </Badge>
            )}
            {course.isFree && (
              <Badge variant="secondary">
                Free
              </Badge>
            )}
          </div>

          {/* Favorite Button */}
          {showFavorite && (
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-3 top-3 h-8 w-8 bg-white/80 hover:bg-white"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                onFavoriteToggle?.(course.id);
              }}
            >
              <Heart className={cn("h-4 w-4", isFavorite && "fill-red-500 text-red-500")} />
            </Button>
          )}

          {/* Level Badge */}
          {course.level && (
            <Badge 
              variant="outline" 
              className="absolute bottom-3 right-3 bg-white/90"
            >
              {course.level}
            </Badge>
          )}
        </div>
      </Link>

      <CardContent className="p-4">
        <Link to={`/courses/${course.slug}`}>
          {/* Category */}
          {course.category && (
            <p className="mb-2 text-xs font-medium text-primary">
              {course.category}
            </p>
          )}

          {/* Title */}
          <h3 className="mb-2 line-clamp-2 font-semibold leading-tight group-hover:text-primary">
            {course.title}
          </h3>

          {/* Instructor */}
          <p className="mb-3 text-sm text-muted-foreground">
            By {course.instructor.name}
          </p>

          {/* Rating */}
          <div className="mb-3 flex items-center gap-2">
            <div className="flex items-center gap-1">
              {renderStars(course.rating.average)}
            </div>
            <span className="text-sm font-medium">
              {course.rating.average.toFixed(1)}
            </span>
            <span className="text-sm text-muted-foreground">
              ({course.rating.count.toLocaleString()})
            </span>
          </div>

          {/* Course Stats */}
          <div className="mb-4 flex items-center gap-4 text-xs text-muted-foreground">
            {course.duration && (
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                <span>{course.duration}</span>
              </div>
            )}
            {course.studentsCount && (
              <div className="flex items-center gap-1">
                <Users className="h-3 w-3" />
                <span>{course.studentsCount.toLocaleString()}</span>
              </div>
            )}
            {course.lessonsCount && (
              <div className="flex items-center gap-1">
                <BookOpen className="h-3 w-3" />
                <span>{course.lessonsCount} lessons</span>
              </div>
            )}
          </div>
        </Link>
      </CardContent>

      <CardFooter className="p-4 pt-0">
        <div className="flex w-full items-center justify-between">
          {/* Price */}
          <div className="flex items-center gap-2">
            <span className="text-lg font-bold">
              {formatPrice(course.price.current, course.price.currency)}
            </span>
            {course.price.original && course.price.original > course.price.current && (
              <span className="text-sm text-muted-foreground line-through">
                {formatPrice(course.price.original, course.price.currency)}
              </span>
            )}
          </div>

          {/* Enroll Button */}
          <Button size="sm" asChild>
            <Link to={`/courses/${course.slug}`}>
              {course.isFree ? 'Start Free' : 'Enroll Now'}
            </Link>
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
}
