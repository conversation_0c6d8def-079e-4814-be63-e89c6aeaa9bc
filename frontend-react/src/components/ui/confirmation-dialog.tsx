import React, { useState } from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from './alert-dialog';
import { But<PERSON> } from './button';
import { Input } from './input';
import { Label } from './label';
import { Loader2, AlertTriangle, Info, CheckCircle, XCircle } from 'lucide-react';
import { cn } from '../../lib/utils';

// Confirmation dialog types
export interface ConfirmationOptions {
  title: string;
  description: string;
  confirmText?: string;
  cancelText?: string;
  variant?: 'default' | 'destructive' | 'warning' | 'info';
  requireConfirmation?: boolean;
  confirmationText?: string;
  onConfirm: () => Promise<void> | void;
  onCancel?: () => void;
}

// Enhanced confirmation dialog component
interface ConfirmationDialogProps extends ConfirmationOptions {
  trigger: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export const ConfirmationDialog: React.FC<ConfirmationDialogProps> = ({
  trigger,
  title,
  description,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  variant = 'default',
  requireConfirmation = false,
  confirmationText = 'DELETE',
  onConfirm,
  onCancel,
  open,
  onOpenChange,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [confirmationInput, setConfirmationInput] = useState('');
  const [internalOpen, setInternalOpen] = useState(false);

  const isOpen = open !== undefined ? open : internalOpen;
  const setOpen = onOpenChange || setInternalOpen;

  const handleConfirm = async () => {
    if (requireConfirmation && confirmationInput !== confirmationText) {
      return;
    }

    setIsLoading(true);
    try {
      await onConfirm();
      setOpen(false);
      setConfirmationInput('');
    } catch (error) {
      console.error('Confirmation action failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    onCancel?.();
    setOpen(false);
    setConfirmationInput('');
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      setConfirmationInput('');
    }
    setOpen(newOpen);
  };

  const getIcon = () => {
    switch (variant) {
      case 'destructive':
        return <XCircle className="h-6 w-6 text-red-600" />;
      case 'warning':
        return <AlertTriangle className="h-6 w-6 text-yellow-600" />;
      case 'info':
        return <Info className="h-6 w-6 text-blue-600" />;
      default:
        return <CheckCircle className="h-6 w-6 text-green-600" />;
    }
  };

  const isConfirmDisabled = requireConfirmation 
    ? confirmationInput !== confirmationText 
    : false;

  return (
    <AlertDialog open={isOpen} onOpenChange={handleOpenChange}>
      <AlertDialogTrigger asChild>
        {trigger}
      </AlertDialogTrigger>
      <AlertDialogContent className="sm:max-w-md">
        <AlertDialogHeader>
          <div className="flex items-center space-x-3">
            {getIcon()}
            <AlertDialogTitle className="text-lg font-nunito-semibold">
              {title}
            </AlertDialogTitle>
          </div>
          <AlertDialogDescription className="text-gray-600 mt-2">
            {description}
          </AlertDialogDescription>
        </AlertDialogHeader>

        {requireConfirmation && (
          <div className="space-y-2">
            <Label htmlFor="confirmation-input" className="text-sm font-medium">
              Type <span className="font-mono font-bold">{confirmationText}</span> to confirm:
            </Label>
            <Input
              id="confirmation-input"
              value={confirmationInput}
              onChange={(e) => setConfirmationInput(e.target.value)}
              placeholder={confirmationText}
              className={cn(
                'font-mono',
                confirmationInput === confirmationText && 'border-green-500 focus:border-green-500'
              )}
              disabled={isLoading}
              autoComplete="off"
            />
          </div>
        )}

        <AlertDialogFooter className="flex space-x-2">
          <AlertDialogCancel 
            onClick={handleCancel}
            disabled={isLoading}
          >
            {cancelText}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={isLoading || isConfirmDisabled}
            className={cn(
              variant === 'destructive' && 'bg-red-600 hover:bg-red-700 focus:ring-red-500',
              variant === 'warning' && 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500'
            )}
          >
            {isLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
            {confirmText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

// Hook for programmatic confirmation dialogs
export function useConfirmation() {
  const [dialogState, setDialogState] = useState<{
    isOpen: boolean;
    options: ConfirmationOptions | null;
  }>({
    isOpen: false,
    options: null,
  });

  const confirm = (options: ConfirmationOptions): Promise<boolean> => {
    return new Promise((resolve) => {
      setDialogState({
        isOpen: true,
        options: {
          ...options,
          onConfirm: async () => {
            try {
              await options.onConfirm();
              resolve(true);
            } catch (error) {
              resolve(false);
            }
          },
          onCancel: () => {
            options.onCancel?.();
            resolve(false);
          },
        },
      });
    });
  };

  const ConfirmationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => (
    <>
      {children}
      {dialogState.options && (
        <ConfirmationDialog
          trigger={<div />}
          open={dialogState.isOpen}
          onOpenChange={(open) => {
            if (!open) {
              setDialogState({ isOpen: false, options: null });
            }
          }}
          {...dialogState.options}
        />
      )}
    </>
  );

  return { confirm, ConfirmationProvider };
}

// Quick confirmation dialog variants
export const DeleteConfirmationDialog: React.FC<{
  trigger: React.ReactNode;
  itemName: string;
  onConfirm: () => Promise<void> | void;
  requireConfirmation?: boolean;
}> = ({ trigger, itemName, onConfirm, requireConfirmation = false }) => (
  <ConfirmationDialog
    trigger={trigger}
    title="Delete Item"
    description={`Are you sure you want to delete "${itemName}"? This action cannot be undone.`}
    confirmText="Delete"
    cancelText="Cancel"
    variant="destructive"
    requireConfirmation={requireConfirmation}
    confirmationText="DELETE"
    onConfirm={onConfirm}
  />
);

export const SaveConfirmationDialog: React.FC<{
  trigger: React.ReactNode;
  onConfirm: () => Promise<void> | void;
  hasUnsavedChanges?: boolean;
}> = ({ trigger, onConfirm, hasUnsavedChanges = true }) => (
  <ConfirmationDialog
    trigger={trigger}
    title="Save Changes"
    description={
      hasUnsavedChanges
        ? "You have unsaved changes. Do you want to save them?"
        : "Do you want to save your changes?"
    }
    confirmText="Save"
    cancelText="Discard"
    variant="default"
    onConfirm={onConfirm}
  />
);

export const LogoutConfirmationDialog: React.FC<{
  trigger: React.ReactNode;
  onConfirm: () => Promise<void> | void;
}> = ({ trigger, onConfirm }) => (
  <ConfirmationDialog
    trigger={trigger}
    title="Sign Out"
    description="Are you sure you want to sign out? You will need to sign in again to access your account."
    confirmText="Sign Out"
    cancelText="Cancel"
    variant="warning"
    onConfirm={onConfirm}
  />
);

export const PublishConfirmationDialog: React.FC<{
  trigger: React.ReactNode;
  itemType: string;
  onConfirm: () => Promise<void> | void;
}> = ({ trigger, itemType, onConfirm }) => (
  <ConfirmationDialog
    trigger={trigger}
    title={`Publish ${itemType}`}
    description={`Are you sure you want to publish this ${itemType.toLowerCase()}? It will be visible to all users.`}
    confirmText="Publish"
    cancelText="Cancel"
    variant="info"
    onConfirm={onConfirm}
  />
);

export const UnpublishConfirmationDialog: React.FC<{
  trigger: React.ReactNode;
  itemType: string;
  onConfirm: () => Promise<void> | void;
}> = ({ trigger, itemType, onConfirm }) => (
  <ConfirmationDialog
    trigger={trigger}
    title={`Unpublish ${itemType}`}
    description={`Are you sure you want to unpublish this ${itemType.toLowerCase()}? It will no longer be visible to users.`}
    confirmText="Unpublish"
    cancelText="Cancel"
    variant="warning"
    onConfirm={onConfirm}
  />
);
