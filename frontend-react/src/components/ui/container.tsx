import * as React from "react"
import { cn } from "@/lib/utils"

interface ContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  children: React.ReactNode
}

const Container = React.forwardRef<HTMLDivElement, ContainerProps>(
  ({ className, size = 'lg', children, ...props }, ref) => {
    const getSizeClasses = () => {
      switch (size) {
        case 'sm':
          return 'max-w-2xl'
        case 'md':
          return 'max-w-4xl'
        case 'lg':
          return 'max-w-6xl'
        case 'xl':
          return 'max-w-7xl'
        case 'full':
          return 'max-w-full'
        default:
          return 'max-w-6xl'
      }
    }

    return (
      <div
        ref={ref}
        className={cn(
          "container mx-auto px-4 sm:px-6 lg:px-8",
          getSizeClasses(),
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)
Container.displayName = "Container"

// Section component for consistent spacing
interface SectionProps extends React.HTMLAttributes<HTMLElement> {
  children: React.ReactNode
  spacing?: 'sm' | 'md' | 'lg' | 'xl'
  background?: 'default' | 'muted' | 'accent'
}

const Section = React.forwardRef<HTMLElement, SectionProps>(
  ({ className, children, spacing = 'lg', background = 'default', ...props }, ref) => {
    const getSpacingClasses = () => {
      switch (spacing) {
        case 'sm':
          return 'py-8'
        case 'md':
          return 'py-12'
        case 'lg':
          return 'py-16'
        case 'xl':
          return 'py-24'
        default:
          return 'py-16'
      }
    }

    const getBackgroundClasses = () => {
      switch (background) {
        case 'muted':
          return 'bg-muted/50'
        case 'accent':
          return 'bg-accent/50'
        default:
          return 'bg-background'
      }
    }

    return (
      <section
        ref={ref}
        className={cn(
          getSpacingClasses(),
          getBackgroundClasses(),
          className
        )}
        {...props}
      >
        {children}
      </section>
    )
  }
)
Section.displayName = "Section"

// Grid component for responsive layouts
interface GridProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  cols?: 1 | 2 | 3 | 4 | 5 | 6
  gap?: 'sm' | 'md' | 'lg' | 'xl'
  responsive?: boolean
}

const Grid = React.forwardRef<HTMLDivElement, GridProps>(
  ({ className, children, cols = 3, gap = 'md', responsive = true, ...props }, ref) => {
    const getColsClasses = () => {
      if (!responsive) {
        return `grid-cols-${cols}`
      }
      
      switch (cols) {
        case 1:
          return 'grid-cols-1'
        case 2:
          return 'grid-cols-1 md:grid-cols-2'
        case 3:
          return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
        case 4:
          return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
        case 5:
          return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5'
        case 6:
          return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6'
        default:
          return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
      }
    }

    const getGapClasses = () => {
      switch (gap) {
        case 'sm':
          return 'gap-4'
        case 'md':
          return 'gap-6'
        case 'lg':
          return 'gap-8'
        case 'xl':
          return 'gap-12'
        default:
          return 'gap-6'
      }
    }

    return (
      <div
        ref={ref}
        className={cn(
          "grid",
          getColsClasses(),
          getGapClasses(),
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)
Grid.displayName = "Grid"

// Flex component for flexible layouts
interface FlexProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  direction?: 'row' | 'col' | 'row-reverse' | 'col-reverse'
  align?: 'start' | 'center' | 'end' | 'stretch' | 'baseline'
  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly'
  wrap?: boolean
  gap?: 'sm' | 'md' | 'lg' | 'xl'
}

const Flex = React.forwardRef<HTMLDivElement, FlexProps>(
  ({ 
    className, 
    children, 
    direction = 'row', 
    align = 'start', 
    justify = 'start', 
    wrap = false,
    gap = 'md',
    ...props 
  }, ref) => {
    const getDirectionClass = () => `flex-${direction}`
    
    const getAlignClass = () => {
      switch (align) {
        case 'start':
          return 'items-start'
        case 'center':
          return 'items-center'
        case 'end':
          return 'items-end'
        case 'stretch':
          return 'items-stretch'
        case 'baseline':
          return 'items-baseline'
        default:
          return 'items-start'
      }
    }

    const getJustifyClass = () => {
      switch (justify) {
        case 'start':
          return 'justify-start'
        case 'center':
          return 'justify-center'
        case 'end':
          return 'justify-end'
        case 'between':
          return 'justify-between'
        case 'around':
          return 'justify-around'
        case 'evenly':
          return 'justify-evenly'
        default:
          return 'justify-start'
      }
    }

    const getGapClass = () => {
      switch (gap) {
        case 'sm':
          return 'gap-2'
        case 'md':
          return 'gap-4'
        case 'lg':
          return 'gap-6'
        case 'xl':
          return 'gap-8'
        default:
          return 'gap-4'
      }
    }

    return (
      <div
        ref={ref}
        className={cn(
          "flex",
          getDirectionClass(),
          getAlignClass(),
          getJustifyClass(),
          getGapClass(),
          wrap && 'flex-wrap',
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)
Flex.displayName = "Flex"

export { Container, Section, Grid, Flex }
