import React from 'react';
import { <PERSON>, CardContent, CardHeader } from "./card";
import { Badge } from "./badge";
import { Button } from "./button";
import { Avatar, AvatarFallback, AvatarImage } from "./avatar";
import { Star, Users, BookOpen, Award, MapPin, Calendar } from "lucide-react";
import { cn } from "@/lib/utils";

interface InstructorProfileProps {
  instructor: {
    id: string;
    name: string;
    title: string;
    avatar?: string;
    bio: string;
    location?: string;
    joinedDate?: string;
    rating: {
      average: number;
      count: number;
    };
    stats: {
      studentsCount: number;
      coursesCount: number;
      reviewsCount: number;
    };
    specialties: string[];
    socialLinks?: {
      website?: string;
      linkedin?: string;
      twitter?: string;
      github?: string;
    };
    isVerified?: boolean;
    isTopInstructor?: boolean;
  };
  variant?: 'default' | 'compact' | 'detailed';
  showFollowButton?: boolean;
  isFollowing?: boolean;
  onFollowToggle?: (instructorId: string) => void;
  className?: string;
}



export function InstructorProfile({
  instructor,
  variant = 'default',
  showFollowButton = false,
  isFollowing = false,
  onFollowToggle,
  className
}: InstructorProfileProps) {
  
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={cn(
          "h-4 w-4",
          i < Math.floor(rating) 
            ? "fill-yellow-400 text-yellow-400" 
            : "text-gray-300"
        )}
      />
    ));
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  if (variant === 'compact') {
    return (
      <div className={cn("flex items-center gap-3", className)}>
        <Avatar className="h-12 w-12">
          <AvatarImage src={instructor.avatar} alt={instructor.name} />
          <AvatarFallback>{getInitials(instructor.name)}</AvatarFallback>
        </Avatar>
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <h4 className="font-semibold truncate">{instructor.name}</h4>
            {instructor.isVerified && (
              <Badge variant="secondary" className="h-5 px-1.5 text-xs">
                <Award className="h-3 w-3 mr-1" />
                Verified
              </Badge>
            )}
          </div>
          <p className="text-sm text-muted-foreground truncate">{instructor.title}</p>
          <div className="flex items-center gap-1 mt-1">
            {renderStars(instructor.rating.average)}
            <span className="text-xs text-muted-foreground ml-1">
              {instructor.rating.average.toFixed(1)} ({instructor.rating.count})
            </span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardHeader className="text-center">
        <div className="flex flex-col items-center gap-4">
          <div className="relative">
            <Avatar className="h-24 w-24">
              <AvatarImage src={instructor.avatar} alt={instructor.name} />
              <AvatarFallback className="text-lg">{getInitials(instructor.name)}</AvatarFallback>
            </Avatar>
            {instructor.isTopInstructor && (
              <Badge 
                className="absolute -bottom-2 left-1/2 -translate-x-1/2 bg-gradient-to-r from-yellow-400 to-orange-500"
              >
                <Award className="h-3 w-3 mr-1" />
                Top Instructor
              </Badge>
            )}
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center justify-center gap-2">
              <h3 className="text-xl font-bold">{instructor.name}</h3>
              {instructor.isVerified && (
                <Badge variant="secondary">
                  <Award className="h-3 w-3 mr-1" />
                  Verified
                </Badge>
              )}
            </div>
            <p className="text-muted-foreground">{instructor.title}</p>
            
            {/* Rating */}
            <div className="flex items-center justify-center gap-2">
              <div className="flex items-center gap-1">
                {renderStars(instructor.rating.average)}
              </div>
              <span className="font-medium">{instructor.rating.average.toFixed(1)}</span>
              <span className="text-sm text-muted-foreground">
                ({instructor.rating.count.toLocaleString()} reviews)
              </span>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Stats */}
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="flex items-center justify-center mb-1">
              <Users className="h-4 w-4 text-muted-foreground" />
            </div>
            <div className="font-bold">{instructor.stats.studentsCount.toLocaleString()}</div>
            <div className="text-xs text-muted-foreground">Students</div>
          </div>
          <div>
            <div className="flex items-center justify-center mb-1">
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </div>
            <div className="font-bold">{instructor.stats.coursesCount}</div>
            <div className="text-xs text-muted-foreground">Courses</div>
          </div>
          <div>
            <div className="flex items-center justify-center mb-1">
              <Star className="h-4 w-4 text-muted-foreground" />
            </div>
            <div className="font-bold">{instructor.stats.reviewsCount.toLocaleString()}</div>
            <div className="text-xs text-muted-foreground">Reviews</div>
          </div>
        </div>

        {/* Bio */}
        <div>
          <h4 className="font-semibold mb-2">About</h4>
          <p className="text-sm text-muted-foreground leading-relaxed">
            {instructor.bio}
          </p>
        </div>

        {/* Specialties */}
        {instructor.specialties.length > 0 && (
          <div>
            <h4 className="font-semibold mb-2">Specialties</h4>
            <div className="flex flex-wrap gap-2">
              {instructor.specialties.map((specialty, index) => (
                <Badge key={index} variant="outline">
                  {specialty}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Additional Info */}
        <div className="space-y-2 text-sm text-muted-foreground">
          {instructor.location && (
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              <span>{instructor.location}</span>
            </div>
          )}
          {instructor.joinedDate && (
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              <span>Joined {instructor.joinedDate}</span>
            </div>
          )}
        </div>

        {/* Follow Button */}
        {showFollowButton && (
          <Button
            variant={isFollowing ? "outline" : "default"}
            className="w-full"
            onClick={() => onFollowToggle?.(instructor.id)}
          >
            {isFollowing ? "Following" : "Follow"}
          </Button>
        )}
      </CardContent>
    </Card>
  );
}


