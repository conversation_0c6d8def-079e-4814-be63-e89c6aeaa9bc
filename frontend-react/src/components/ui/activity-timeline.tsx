import { Card, CardContent } from "./card";
import { Badge } from "./badge";
import { Avatar, AvatarFallback, AvatarImage } from "./avatar";
import {
  BookOpen,
  Award,
  Star,
  Clock,
  CheckCircle,
  PlayCircle,
  Download
} from "lucide-react";
import { cn } from "@/lib/utils";

interface ActivityItem {
  id: string;
  type: 'course_completed' | 'lesson_watched' | 'certificate_earned' | 'review_posted' | 'course_enrolled' | 'assignment_submitted';
  title: string;
  description?: string;
  timestamp: string;
  metadata?: {
    courseName?: string;
    lessonName?: string;
    certificateName?: string;
    rating?: number;
    progress?: number;
    [key: string]: any;
  };
  user?: {
    name: string;
    avatar?: string;
  };
}

interface ActivityTimelineProps {
  activities: ActivityItem[];
  showUserInfo?: boolean;
  variant?: 'default' | 'compact';
  className?: string;
}

export function ActivityTimeline({
  activities,
  showUserInfo = false,
  variant = 'default',
  className
}: ActivityTimelineProps) {
  
  const getActivityIcon = (type: ActivityItem['type']) => {
    switch (type) {
      case 'course_completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'lesson_watched':
        return <PlayCircle className="h-4 w-4 text-blue-500" />;
      case 'certificate_earned':
        return <Award className="h-4 w-4 text-yellow-500" />;
      case 'review_posted':
        return <Star className="h-4 w-4 text-orange-500" />;
      case 'course_enrolled':
        return <BookOpen className="h-4 w-4 text-purple-500" />;
      case 'assignment_submitted':
        return <Download className="h-4 w-4 text-indigo-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getActivityColor = (type: ActivityItem['type']) => {
    switch (type) {
      case 'course_completed':
        return 'border-green-200 bg-green-50';
      case 'lesson_watched':
        return 'border-blue-200 bg-blue-50';
      case 'certificate_earned':
        return 'border-yellow-200 bg-yellow-50';
      case 'review_posted':
        return 'border-orange-200 bg-orange-50';
      case 'course_enrolled':
        return 'border-purple-200 bg-purple-50';
      case 'assignment_submitted':
        return 'border-indigo-200 bg-indigo-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else if (diffInHours < 168) {
      return `${Math.floor(diffInHours / 24)}d ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  if (variant === 'compact') {
    return (
      <div className={cn("space-y-3", className)}>
        {activities.map((activity) => (
          <div key={activity.id} className="flex items-start gap-3">
            <div className={cn(
              "flex h-8 w-8 items-center justify-center rounded-full border-2",
              getActivityColor(activity.type)
            )}>
              {getActivityIcon(activity.type)}
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium">{activity.title}</p>
              {activity.description && (
                <p className="text-xs text-muted-foreground">{activity.description}</p>
              )}
              <p className="text-xs text-muted-foreground mt-1">
                {formatTimestamp(activity.timestamp)}
              </p>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className={cn("space-y-4", className)}>
      {activities.map((activity, index) => (
        <div key={activity.id} className="relative">
          {/* Timeline line */}
          {index < activities.length - 1 && (
            <div className="absolute left-6 top-12 h-full w-0.5 bg-border" />
          )}
          
          <Card className="relative">
            <CardContent className="p-4">
              <div className="flex items-start gap-4">
                {/* Activity Icon */}
                <div className={cn(
                  "flex h-12 w-12 items-center justify-center rounded-full border-2",
                  getActivityColor(activity.type)
                )}>
                  {getActivityIcon(activity.type)}
                </div>

                {/* Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h4 className="font-medium">{activity.title}</h4>
                      {activity.description && (
                        <p className="text-sm text-muted-foreground mt-1">
                          {activity.description}
                        </p>
                      )}
                      
                      {/* Metadata */}
                      {activity.metadata && (
                        <div className="mt-2 space-y-1">
                          {activity.metadata.courseName && (
                            <Badge variant="outline" className="text-xs">
                              {activity.metadata.courseName}
                            </Badge>
                          )}
                          {activity.metadata.rating && (
                            <div className="flex items-center gap-1">
                              <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                              <span className="text-xs">{activity.metadata.rating}/5</span>
                            </div>
                          )}
                          {activity.metadata.progress && (
                            <div className="flex items-center gap-2">
                              <div className="h-1.5 w-20 bg-muted rounded-full overflow-hidden">
                                <div 
                                  className="h-full bg-primary transition-all"
                                  style={{ width: `${activity.metadata.progress}%` }}
                                />
                              </div>
                              <span className="text-xs text-muted-foreground">
                                {activity.metadata.progress}%
                              </span>
                            </div>
                          )}
                        </div>
                      )}
                    </div>

                    {/* User Info */}
                    {showUserInfo && activity.user && (
                      <div className="flex items-center gap-2 ml-4">
                        <Avatar className="h-6 w-6">
                          <AvatarImage src={activity.user.avatar} alt={activity.user.name} />
                          <AvatarFallback className="text-xs">
                            {getInitials(activity.user.name)}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-xs text-muted-foreground">
                          {activity.user.name}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Timestamp */}
                  <p className="text-xs text-muted-foreground mt-2">
                    {formatTimestamp(activity.timestamp)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      ))}
    </div>
  );
}
