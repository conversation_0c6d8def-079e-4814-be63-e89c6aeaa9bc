import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import {
  Search,
  User,
  ShoppingCart,
  Bell,
  LogOut,
  Settings,
  BookOpen,
  Menu,
  X,
  ChevronDown,
  GraduationCap,
  Users,
  Phone,
  Info
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useAuth } from '../../contexts/AuthContext';
import { useCart } from '../../contexts/CartContext';

interface ModernHeaderProps {
  className?: string;
}

export function ModernHeader({ className }: ModernHeaderProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isScrolled, setIsScrolled] = useState(false);
  const navigate = useNavigate();

  // Auth and cart state
  const { authState, logout } = useAuth();
  const { cart } = useCart();
  const { user, isAuthenticated } = authState;

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navigationItems = [
    {
      label: "Courses",
      href: "/courses",
      icon: BookOpen,
      hasDropdown: true,
      dropdownItems: [
        { label: "All Courses", href: "/courses" },
        { label: "Web Development", href: "/courses/web-development" },
        { label: "Data Science", href: "/courses/data-science" },
        { label: "Design", href: "/courses/design" },
      ]
    },
    {
      label: "Categories",
      href: "/categories",
      icon: GraduationCap,
      hasDropdown: true,
      dropdownItems: [
        { label: "Technology", href: "/categories/technology" },
        { label: "Business", href: "/categories/business" },
        { label: "Creative", href: "/categories/creative" },
      ]
    },
    { label: "Instructors", href: "/instructors", icon: Users },
  ];

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  const getUserInitials = () => {
    if (!user?.Username) return 'U';
    return user.Username.charAt(0).toUpperCase();
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/courses?search=${encodeURIComponent(searchQuery.trim())}`);
      setIsSearchOpen(false);
      setSearchQuery('');
    }
  };

  return (
    <header className={cn(
      "sticky top-0 z-50 w-full border-b transition-all duration-200",
      isScrolled
        ? "bg-background/95 backdrop-blur-md shadow-sm supports-[backdrop-filter]:bg-background/60"
        : "bg-background/80 backdrop-blur-sm",
      className
    )}>
      <div className="container mx-auto flex h-16 items-center justify-between px-4 lg:px-6">
        {/* Logo */}
        <div className="flex items-center">
          <Link to="/" className="flex items-center group">
            <div className="relative">
              <img
                src="/assets/img/brain-maker-logo.png"
                alt="BrainMaker Academy"
                className="h-10 w-auto transition-all duration-300 group-hover:scale-110 group-hover:brightness-110"
                onError={(e) => {
                  // Fallback to text logo if image fails
                  e.currentTarget.style.display = 'none';
                  e.currentTarget.nextElementSibling?.classList.remove('hidden');
                }}
              />
              <div className="hidden h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-br from-primary to-primary/80 text-primary-foreground shadow-lg transition-all duration-300 group-hover:shadow-xl group-hover:scale-110">
                <span className="text-lg font-bold">B</span>
              </div>
            </div>
          </Link>
        </div>

        {/* Desktop Navigation */}
        <nav className="hidden lg:flex items-center space-x-1">
          {navigationItems.map((item) => (
            item.hasDropdown ? (
              <DropdownMenu key={item.label}>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="flex items-center gap-2 text-sm font-medium hover:bg-muted/50 transition-colors">
                    {item.icon && <item.icon className="h-4 w-4" />}
                    <span className="hidden xl:inline">{item.label}</span>
                    <ChevronDown className="h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start" className="w-48">
                  {item.dropdownItems?.map((dropdownItem) => (
                    <DropdownMenuItem key={dropdownItem.label} asChild>
                      <Link to={dropdownItem.href} className="w-full">
                        {dropdownItem.label}
                      </Link>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <Button key={item.label} variant="ghost" asChild className="text-sm font-medium hover:bg-muted/50 transition-colors">
                <Link to={item.href} className="flex items-center gap-2">
                  {item.icon && <item.icon className="h-4 w-4" />}
                  <span className="hidden xl:inline">{item.label}</span>
                </Link>
              </Button>
            )
          ))}
        </nav>

        {/* Right Side Actions */}
        <div className="flex items-center space-x-2">
          {/* Search - Desktop */}
          <div className="hidden md:flex items-center">
            <form onSubmit={handleSearch} className="relative">
              <Search className="absolute left-3 top-1/2 h-4 w-4 text-muted-foreground transform -translate-y-1/2" />
              <Input
                type="text"
                placeholder="Search courses..."
                value={searchQuery}
                onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => setSearchQuery(e.target.value)}
                className="w-64 pl-10 pr-4 h-9 text-sm bg-muted/50 border-0 focus-visible:ring-1"
              />
            </form>
          </div>

          {/* Search - Mobile */}
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden"
            onClick={() => setIsSearchOpen(true)}
          >
            <Search className="h-4 w-4" />
            <span className="sr-only">Search</span>
          </Button>

          {/* Mobile Search Sheet */}
          <Sheet open={isSearchOpen} onOpenChange={setIsSearchOpen}>
            <SheetContent side="top" className="h-auto">
              <SheetHeader>
                <SheetTitle>Search Courses</SheetTitle>
                <SheetDescription>
                  Find the perfect course to advance your skills
                </SheetDescription>
              </SheetHeader>
              <form onSubmit={handleSearch} className="mt-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 h-4 w-4 text-muted-foreground transform -translate-y-1/2" />
                  <Input
                    type="text"
                    placeholder="What do you want to learn?"
                    value={searchQuery}
                    onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => setSearchQuery(e.target.value)}
                    className="w-full pl-10 pr-4"
                    autoFocus
                  />
                </div>
                <Button type="submit" className="w-full mt-4">
                  Search Courses
                </Button>
              </form>
            </SheetContent>
          </Sheet>

          {/* Notifications - only show if authenticated */}
          {isAuthenticated && (
            <Button variant="ghost" size="icon" className="relative hidden sm:flex hover:bg-muted">
              <Bell className="h-4 w-4" />
              <Badge className="absolute -right-1 -top-1 h-5 w-5 rounded-full p-0 text-xs bg-red-500 hover:bg-red-600">
                3
              </Badge>
              <span className="sr-only">Notifications</span>
            </Button>
          )}

          {/* Cart */}
          <Button
            variant="ghost"
            size="icon"
            className="relative hover:bg-muted transition-colors"
            onClick={() => navigate('/cart')}
          >
            <ShoppingCart className="h-4 w-4" />
            {cart.ItemCount > 0 && (
              <Badge className="absolute -right-1 -top-1 h-5 w-5 rounded-full p-0 text-xs bg-primary hover:bg-primary/80">
                {cart.ItemCount}
              </Badge>
            )}
            <span className="sr-only">Shopping cart ({cart.ItemCount} items)</span>
          </Button>

          {/* User Menu or Auth Buttons */}
          {isAuthenticated ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={user?.Photo?.Hashname || user?.photoFormated} alt={user?.Username} />
                    <AvatarFallback className="bg-primary text-primary-foreground">
                      {getUserInitials()}
                    </AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <DropdownMenuLabel className="font-normal">
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium leading-none">{user?.Username}</p>
                    <p className="text-xs leading-none text-muted-foreground">
                      {user?.Email}
                    </p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => navigate('/user')}>
                  <User className="mr-2 h-4 w-4" />
                  <span>Dashboard</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => navigate('/user/courses')}>
                  <BookOpen className="mr-2 h-4 w-4" />
                  <span>My Courses</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => navigate('/user/settings')}>
                  <Settings className="mr-2 h-4 w-4" />
                  <span>Settings</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleLogout}>
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Log out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/auth/login')}
                className="hidden sm:flex hover:bg-muted"
              >
                Log in
              </Button>
              <Button
                size="sm"
                onClick={() => navigate('/auth/register')}
                className="bg-primary hover:bg-primary/90"
              >
                Sign up
              </Button>
            </div>
          )}

          {/* Mobile Menu Button */}
          <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
            <SheetTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="lg:hidden"
              >
                <Menu className="h-5 w-5" />
                <span className="sr-only">Toggle menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[300px] sm:w-[400px]">
              <SheetHeader>
                <SheetTitle>Navigation</SheetTitle>
                <SheetDescription>
                  Explore courses and manage your learning journey
                </SheetDescription>
              </SheetHeader>
              <nav className="flex flex-col space-y-4 mt-6">
                {navigationItems.map((item) => (
                  <div key={item.label}>
                    <Button
                      variant="ghost"
                      className="w-full justify-start text-left"
                      onClick={() => {
                        navigate(item.href);
                        setIsMobileMenuOpen(false);
                      }}
                    >
                      {item.icon && <item.icon className="mr-2 h-4 w-4" />}
                      {item.label}
                    </Button>
                    {item.dropdownItems && (
                      <div className="ml-6 mt-2 space-y-2">
                        {item.dropdownItems.map((dropdownItem) => (
                          <Button
                            key={dropdownItem.label}
                            variant="ghost"
                            size="sm"
                            className="w-full justify-start text-muted-foreground"
                            onClick={() => {
                              navigate(dropdownItem.href);
                              setIsMobileMenuOpen(false);
                            }}
                          >
                            {dropdownItem.label}
                          </Button>
                        ))}
                      </div>
                    )}
                  </div>
                ))}

                {!isAuthenticated && (
                  <div className="border-t pt-4 space-y-2">
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => {
                        navigate('/auth/login');
                        setIsMobileMenuOpen(false);
                      }}
                    >
                      Log in
                    </Button>
                    <Button
                      className="w-full"
                      onClick={() => {
                        navigate('/auth/register');
                        setIsMobileMenuOpen(false);
                      }}
                    >
                      Sign up
                    </Button>
                  </div>
                )}
              </nav>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  );
}
