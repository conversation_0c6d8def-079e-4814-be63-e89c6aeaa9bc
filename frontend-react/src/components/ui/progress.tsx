import * as React from "react"
import { cn } from "@/lib/utils"

const Progress = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    value?: number
    max?: number
    variant?: 'default' | 'success' | 'warning' | 'danger'
    size?: 'sm' | 'default' | 'lg'
    showValue?: boolean
    label?: string
  }
>(({ className, value = 0, max = 100, variant = 'default', size = 'default', showValue = false, label, ...props }, ref) => {
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100)

  const getVariantStyles = () => {
    switch (variant) {
      case 'success':
        return 'bg-green-500'
      case 'warning':
        return 'bg-yellow-500'
      case 'danger':
        return 'bg-red-500'
      default:
        return 'bg-primary'
    }
  }

  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return 'h-1'
      case 'lg':
        return 'h-3'
      default:
        return 'h-2'
    }
  }

  return (
    <div className="space-y-2">
      {(label || showValue) && (
        <div className="flex items-center justify-between text-sm">
          {label && <span className="font-medium">{label}</span>}
          {showValue && (
            <span className="text-muted-foreground">
              {Math.round(percentage)}%
            </span>
          )}
        </div>
      )}
      <div
        ref={ref}
        className={cn(
          "relative w-full overflow-hidden rounded-full bg-secondary",
          getSizeStyles(),
          className
        )}
        {...props}
      >
        <div
          className={cn(
            "h-full w-full flex-1 transition-all duration-300 ease-in-out",
            getVariantStyles()
          )}
          style={{ transform: `translateX(-${100 - percentage}%)` }}
        />
      </div>
    </div>
  )
})
Progress.displayName = "Progress"

// Circular Progress Component
interface CircularProgressProps {
  value: number
  max?: number
  size?: number
  strokeWidth?: number
  variant?: 'default' | 'success' | 'warning' | 'danger'
  showValue?: boolean
  label?: string
  className?: string
}

export function CircularProgress({
  value,
  max = 100,
  size = 120,
  strokeWidth = 8,
  variant = 'default',
  showValue = true,
  label,
  className
}: CircularProgressProps) {
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100)
  const radius = (size - strokeWidth) / 2
  const circumference = radius * 2 * Math.PI
  const strokeDasharray = circumference
  const strokeDashoffset = circumference - (percentage / 100) * circumference

  const getVariantColor = () => {
    switch (variant) {
      case 'success':
        return '#22c55e'
      case 'warning':
        return '#eab308'
      case 'danger':
        return '#ef4444'
      default:
        return 'hsl(var(--primary))'
    }
  }

  return (
    <div className={cn("relative inline-flex items-center justify-center", className)}>
      <svg
        width={size}
        height={size}
        className="transform -rotate-90"
      >
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="hsl(var(--muted))"
          strokeWidth={strokeWidth}
          fill="none"
        />
        {/* Progress circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={getVariantColor()}
          strokeWidth={strokeWidth}
          fill="none"
          strokeLinecap="round"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          className="transition-all duration-300 ease-in-out"
        />
      </svg>
      
      {/* Center content */}
      <div className="absolute inset-0 flex flex-col items-center justify-center text-center">
        {showValue && (
          <span className="text-2xl font-bold">
            {Math.round(percentage)}%
          </span>
        )}
        {label && (
          <span className="text-xs text-muted-foreground mt-1">
            {label}
          </span>
        )}
      </div>
    </div>
  )
}

// Progress with Steps
interface ProgressStepsProps {
  steps: Array<{
    id: string
    label: string
    completed: boolean
  }>
  currentStep?: number
  variant?: 'default' | 'success' | 'warning' | 'danger'
  className?: string
}

export function ProgressSteps({
  steps,
  currentStep = 0,
  variant = 'default',
  className
}: ProgressStepsProps) {
  const getVariantStyles = () => {
    switch (variant) {
      case 'success':
        return 'bg-green-500 border-green-500 text-green-500'
      case 'warning':
        return 'bg-yellow-500 border-yellow-500 text-yellow-500'
      case 'danger':
        return 'bg-red-500 border-red-500 text-red-500'
      default:
        return 'bg-primary border-primary text-primary'
    }
  }

  return (
    <div className={cn("flex items-center", className)}>
      {steps.map((step, index) => (
        <React.Fragment key={step.id}>
          <div className="flex flex-col items-center">
            <div
              className={cn(
                "flex h-8 w-8 items-center justify-center rounded-full border-2 text-sm font-medium",
                step.completed || index <= currentStep
                  ? getVariantStyles()
                  : "border-muted bg-background text-muted-foreground"
              )}
            >
              {step.completed ? (
                <svg className="h-4 w-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              ) : (
                index + 1
              )}
            </div>
            <span className="mt-2 text-xs text-center max-w-20">
              {step.label}
            </span>
          </div>
          
          {index < steps.length - 1 && (
            <div
              className={cn(
                "h-0.5 w-12 mx-2",
                index < currentStep ? getVariantStyles() : "bg-muted"
              )}
            />
          )}
        </React.Fragment>
      ))}
    </div>
  )
}

export { Progress }
