import React, { useState } from 'react';
import mediaService from '../../services/media.service';
import { Button } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Progress } from '../ui/progress';
import { Alert, AlertDescription } from '../ui/alert';
import { Loader2 } from 'lucide-react';

/**
 * Debug Test Component for Media Upload
 * This component tests the media service directly to identify issues
 */
const MediaUploadDebugTest: React.FC = () => {
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `${timestamp}: ${message}`]);
    console.log(message);
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setUploading(true);
    setProgress(0);
    setResult(null);
    setError(null);
    setLogs([]);

    try {
      addLog(`Starting upload for: ${file.name} (${file.size} bytes, ${file.type})`);

      const uploadedFile = await mediaService.uploadFile(file, {
        onProgress: (progressData) => {
          setProgress(progressData.percentage);
          addLog(`Upload progress: ${progressData.percentage}%`);
        },
        maxSize: 100 * 1024 * 1024, // 100MB
        allowedTypes: ['image/jpeg', 'image/png', 'image/webp', 'video/mp4', 'video/webm']
      });

      addLog('✅ Upload completed successfully!');
      addLog(`Media ID: ${uploadedFile.mediaId}`);
      addLog(`Filename: ${uploadedFile.filename}`);
      addLog(`URL: ${uploadedFile.url}`);
      
      setResult(uploadedFile);

      // Test if the file is accessible
      addLog('Testing file accessibility...');
      try {
        const response = await fetch(uploadedFile.url);
        if (response.ok) {
          addLog('✅ File is accessible via URL');
        } else {
          addLog(`❌ File not accessible: ${response.status} ${response.statusText}`);
        }
      } catch (fetchError) {
        addLog(`❌ File accessibility test failed: ${fetchError}`);
      }

    } catch (uploadError: any) {
      addLog(`❌ Upload failed: ${uploadError.message}`);
      setError(uploadError.message);
      console.error('Upload error:', uploadError);
    } finally {
      setUploading(false);
    }
  };

  const testDirectApiCall = async () => {
    setLogs([]);
    addLog('Testing direct API call to /api/medias...');

    try {
      const testData = {
        Name: `direct-test-${Date.now()}.png`,
        Hashname: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==',
        Extension: 'image/png',
        Size: 32,
        SubDir: 'courses/covers'
      };

      const response = await fetch('http://localhost:3200/api/medias', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testData)
      });

      if (response.ok) {
        const data = await response.json();
        addLog('✅ Direct API call successful!');
        addLog(`Response: ${JSON.stringify(data, null, 2)}`);
      } else {
        addLog(`❌ Direct API call failed: ${response.status} ${response.statusText}`);
        const errorText = await response.text();
        addLog(`Error details: ${errorText}`);
      }
    } catch (error: any) {
      addLog(`❌ Direct API call error: ${error.message}`);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Media Upload Debug Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="debug-file-upload" className="block text-sm font-medium">
              Select a file to test upload:
            </label>
            <input
              id="debug-file-upload"
              type="file"
              onChange={handleFileUpload}
              disabled={uploading}
              accept="image/*,video/*"
              className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
            />
          </div>

          <div className="flex gap-2">
            <Button onClick={testDirectApiCall} variant="outline">
              Test Direct API Call
            </Button>
          </div>

          {uploading && (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="text-sm">Uploading... {progress}%</span>
              </div>
              <Progress value={progress} className="w-full" />
            </div>
          )}

          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {result && (
            <Alert>
              <AlertDescription>
                <div className="space-y-2">
                  <div><strong>Upload Successful!</strong></div>
                  <div><strong>Media ID:</strong> {result.mediaId}</div>
                  <div><strong>Filename:</strong> {result.filename}</div>
                  <div><strong>Original Name:</strong> {result.originalUsername}</div>
                  <div><strong>Size:</strong> {result.size} bytes</div>
                  <div><strong>Type:</strong> {result.mimeType}</div>
                  <div><strong>SubDir:</strong> {result.subDir}</div>
                  <div>
                    <strong>URL:</strong>{' '}
                    <a 
                      href={result.url} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline"
                    >
                      {result.url}
                    </a>
                  </div>
                </div>
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Debug Logs</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-gray-100 p-4 rounded-md max-h-96 overflow-y-auto">
            <pre className="text-sm whitespace-pre-wrap">
              {logs.length > 0 ? logs.join('\n') : 'No logs yet. Upload a file to see debug information.'}
            </pre>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default MediaUploadDebugTest;
