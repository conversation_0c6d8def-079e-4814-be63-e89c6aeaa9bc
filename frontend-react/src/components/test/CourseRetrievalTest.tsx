import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import courseService from '@/services/course.service';
import type { Course } from '@/models/course.model';

/**
 * Test component to verify frontend course retrieval with backend media handling
 * This component tests the complete workflow of fetching courses and displaying media
 */
const CourseRetrievalTest: React.FC = () => {
  const [courseSlug, setCourseSlug] = useState('d364109feb9c4332ba97561a5413ecc6'); // Default test course
  const [course, setCourse] = useState<Course | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [testResults, setTestResults] = useState<{
    coverImageTest: boolean;
    videoTest: boolean;
    urlFormatTest: boolean;
  } | null>(null);

  /**
   * Test course retrieval by slug
   */
  const testCourseRetrieval = async () => {
    if (!courseSlug.trim()) {
      setError('Please enter a course slug');
      return;
    }

    setLoading(true);
    setError(null);
    setCourse(null);
    setTestResults(null);

    try {
      console.log('🔍 Testing course retrieval for slug:', courseSlug);
      
      const retrievedCourse = await courseService.getBySlug(courseSlug);
      setCourse(retrievedCourse);

      // Run tests on the retrieved course
      const results = {
        coverImageTest: !!(retrievedCourse.CoverImage?.Hashname),
        videoTest: !!(retrievedCourse.PresentationVideo?.Hashname),
        urlFormatTest: checkUrlFormat(retrievedCourse)
      };

      setTestResults(results);

      console.log('✅ Course retrieval test completed:', {
        course: retrievedCourse,
        testResults: results
      });

    } catch (err) {
      console.error('❌ Course retrieval failed:', err);
      setError(err instanceof Error ? err.message : 'Failed to retrieve course');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Check if URLs are properly formatted
   */
  const checkUrlFormat = (course: Course): boolean => {
    const coverImageUrl = course.CoverImage?.Hashname;
    const videoUrl = course.PresentationVideo?.Hashname;

    const isValidUrl = (url: string) => {
      return url.startsWith('http://') || url.startsWith('https://');
    };

    const coverValid = !coverImageUrl || isValidUrl(coverImageUrl);
    const videoValid = !videoUrl || isValidUrl(videoUrl);

    return coverValid && videoValid;
  };

  /**
   * Test image loading
   */
  const testImageLoad = (url: string): Promise<boolean> => {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => resolve(true);
      img.onerror = () => resolve(false);
      img.src = url;
    });
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🧪 Frontend Course Retrieval Test
            <Badge variant="outline">Development Tool</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Input
              placeholder="Enter course slug to test"
              value={courseSlug}
              onChange={(e) => setCourseSlug(e.target.value)}
              className="flex-1"
            />
            <Button 
              onClick={testCourseRetrieval}
              disabled={loading}
            >
              {loading ? 'Testing...' : 'Test Retrieval'}
            </Button>
          </div>

          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md text-red-700">
              ❌ Error: {error}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Test Results */}
      {testResults && (
        <Card>
          <CardHeader>
            <CardTitle>🎯 Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center">
                <div className={`text-2xl ${testResults.coverImageTest ? 'text-green-600' : 'text-red-600'}`}>
                  {testResults.coverImageTest ? '✅' : '❌'}
                </div>
                <div className="text-sm font-medium">Cover Image</div>
              </div>
              <div className="text-center">
                <div className={`text-2xl ${testResults.videoTest ? 'text-green-600' : 'text-red-600'}`}>
                  {testResults.videoTest ? '✅' : '❌'}
                </div>
                <div className="text-sm font-medium">Presentation Video</div>
              </div>
              <div className="text-center">
                <div className={`text-2xl ${testResults.urlFormatTest ? 'text-green-600' : 'text-red-600'}`}>
                  {testResults.urlFormatTest ? '✅' : '❌'}
                </div>
                <div className="text-sm font-medium">URL Format</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Course Details */}
      {course && (
        <Card>
          <CardHeader>
            <CardTitle>📚 Course Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="font-semibold text-lg">{course.Title}</h3>
              <p className="text-gray-600">ID: {course.Id} | Slug: {course.Slug}</p>
            </div>

            <Separator />

            {/* Cover Image Section */}
            {course.CoverImage && (
              <div>
                <h4 className="font-medium mb-2">📸 Cover Image</h4>
                <div className="bg-gray-50 p-3 rounded-md space-y-2">
                  <p><strong>Name:</strong> {course.CoverImage.Name}</p>
                  <p><strong>URL:</strong> <code className="text-sm bg-white px-2 py-1 rounded">{course.CoverImage.Hashname}</code></p>
                  <p><strong>SubDir:</strong> {course.CoverImage.SubDir}</p>
                  <div className="mt-3">
                    <img 
                      src={course.CoverImage.Hashname} 
                      alt={course.Title}
                      className="max-w-xs h-auto rounded border"
                      onError={(e) => {
                        e.currentTarget.style.border = '2px solid red';
                        e.currentTarget.alt = 'Failed to load image';
                      }}
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Presentation Video Section */}
            {course.PresentationVideo && (
              <div>
                <h4 className="font-medium mb-2">🎥 Presentation Video</h4>
                <div className="bg-gray-50 p-3 rounded-md space-y-2">
                  <p><strong>Name:</strong> {course.PresentationVideo.Name}</p>
                  <p><strong>URL:</strong> <code className="text-sm bg-white px-2 py-1 rounded">{course.PresentationVideo.Hashname}</code></p>
                  <p><strong>SubDir:</strong> {course.PresentationVideo.SubDir}</p>
                  <div className="mt-3">
                    <video 
                      controls 
                      className="max-w-xs h-auto rounded border"
                      onError={(e) => {
                        e.currentTarget.style.border = '2px solid red';
                      }}
                    >
                      <source src={course.PresentationVideo.Hashname} type={`video/${course.PresentationVideo.Extension}`} />
                      Your browser does not support the video tag.
                    </video>
                  </div>
                </div>
              </div>
            )}

            {/* Raw Course Data */}
            <details className="mt-4">
              <summary className="cursor-pointer font-medium">🔍 Raw Course Data (Click to expand)</summary>
              <pre className="mt-2 p-3 bg-gray-100 rounded-md text-xs overflow-auto">
                {JSON.stringify(course, null, 2)}
              </pre>
            </details>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default CourseRetrievalTest;
