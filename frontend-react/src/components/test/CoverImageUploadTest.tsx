import React, { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useToast } from '../../hooks/use-toast';
import mediaService from '../../services/media.service';
import courseService from '../../services/course.service';

// shadcn/ui components
import { Button } from '../ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Progress } from '../ui/progress';
import { Alert, AlertDescription } from '../ui/alert';

// Icons
import { Upload, CheckCircle, AlertCircle, Loader2, ImageIcon } from 'lucide-react';

interface MediaFile {
  Id?: number;
  Hashname: string;
  SubDir: string;
  Type: string;
  Size: number;
  OriginalUsername: string;
  Slug?: string;
}

interface TestCourse {
  Id?: number;
  Title: string;
  Resume: string;
  Language: string;
  Format: number;
  Free: boolean;
  Categories: any[];
  Level: number[];
  Goals: string[];
  Prerequisites: string[];
  Keywords: string[];
  CoverImage?: MediaFile;
  CreatedBy?: any;
}

const CoverImageUploadTest: React.FC = () => {
  const { authState } = useAuth();
  const { user } = authState;
  const { toast } = useToast();

  // Upload states
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadedFile, setUploadedFile] = useState<any>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  
  // Course creation states
  const [creatingCourse, setCreatingCourse] = useState(false);
  const [createdCourse, setCreatedCourse] = useState<any>(null);
  
  // Test results
  const [testResults, setTestResults] = useState<string[]>([]);
  const [errors, setErrors] = useState<string[]>([]);

  const addTestResult = (message: string) => {
    setTestResults(prev => [...prev, `✅ ${message}`]);
    console.log('✅', message);
  };

  const addError = (message: string) => {
    setErrors(prev => [...prev, `❌ ${message}`]);
    console.error('❌', message);
  };

  const handleFileUpload = async (file: File) => {
    try {
      setUploading(true);
      setUploadProgress(0);
      setTestResults([]);
      setErrors([]);

      addTestResult(`Starting upload for file: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`);

      // Test 1: File validation
      if (!file.type.startsWith('image/')) {
        throw new Error('File must be an image');
      }
      addTestResult('File type validation passed');

      if (file.size > 100 * 1024 * 1024) {
        throw new Error('File size exceeds 100MB limit');
      }
      addTestResult('File size validation passed');

      // Test 2: Media service upload
      const uploadedFile = await mediaService.uploadFile(file, {
        onProgress: (progress) => {
          setUploadProgress(progress.percentage);
          console.log(`Upload progress: ${progress.percentage}%`);
        },
        maxSize: 100 * 1024 * 1024,
        allowedTypes: ['image/jpeg', 'image/png', 'image/webp']
      });

      addTestResult('Media service upload completed');
      addTestResult(`Uploaded file ID: ${uploadedFile.id}`);
      addTestResult(`Uploaded filename: ${uploadedFile.filename}`);
      addTestResult(`File URL: ${uploadedFile.url}`);

      setUploadedFile(uploadedFile);
      setImagePreview(uploadedFile.url);

      // Test 3: Create MediaFile object for course
      const mediaFileForCourse: MediaFile = {
        Id: parseInt(uploadedFile.id) || 0,
        Hashname: uploadedFile.filename,
        SubDir: `${user?.Slug}/content/coverimage`,
        Type: uploadedFile.mimeType,
        Size: uploadedFile.size,
        OriginalUsername: uploadedFile.originalUsername,
        Slug: uploadedFile.id
      };

      addTestResult('MediaFile object created for course');
      console.log('MediaFile for course:', mediaFileForCourse);

      toast({
        title: "Upload Success",
        description: "Cover image uploaded successfully!"
      });

    } catch (error: any) {
      console.error('Upload error:', error);
      addError(`Upload failed: ${error.message}`);
      toast({
        title: "Upload Error",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  const testCourseCreation = async () => {
    if (!uploadedFile) {
      addError('No uploaded file available for course creation test');
      return;
    }

    try {
      setCreatingCourse(true);
      addTestResult('Starting course creation with cover image...');

      // Test 4: Create a test course with the uploaded cover image
      const testCourse: TestCourse = {
        Title: `Test Course ${Date.now()}`,
        Resume: 'This is a test course created to verify cover image upload functionality.',
        Language: 'en',
        Format: 2, // VIDEO
        Free: true,
        Categories: [], // We'll skip categories for this test
        Level: [1], // Beginner
        Goals: ['Test goal'],
        Prerequisites: ['Test prerequisite'],
        Keywords: ['test'],
        CoverImage: {
          Id: parseInt(uploadedFile.id) || 0,
          Hashname: uploadedFile.filename,
          SubDir: `${user?.Slug}/content/coverimage`,
          Type: uploadedFile.mimeType,
          Size: uploadedFile.size,
          OriginalUsername: uploadedFile.originalName,
          Slug: uploadedFile.id
        },
        CreatedBy: { Id: user?.Id }
      };

      addTestResult('Test course object prepared');
      console.log('Test course data:', testCourse);
      addTestResult(`Sending CoverImage with ID: ${testCourse.CoverImage?.Id}`);
      addTestResult(`Sending CoverImage with Hashname: ${testCourse.CoverImage?.Hashname}`);

      // Test 5: Submit course to backend
      const result = await courseService.add(testCourse);
      
      addTestResult('Course created successfully in backend');
      addTestResult(`Created course ID: ${result.Id}`);
      addTestResult(`Created course slug: ${result.Slug}`);

      console.log('Backend response:', result);
      addTestResult(`Backend response keys: ${Object.keys(result).join(', ')}`);

      if (result.CoverImage) {
        addTestResult(`Course cover image ID: ${result.CoverImage.Id}`);
        addTestResult(`Course cover image filename: ${result.CoverImage.Hashname}`);
        addTestResult(`Cover image object: ${JSON.stringify(result.CoverImage)}`);
      } else {
        addError('Course was created but cover image was not properly linked');
        addError(`Expected CoverImage but got: ${result.CoverImage}`);
      }

      setCreatedCourse(result);

      // Test 6: Verify media file exists in backend
      try {
        const mediaFile = await mediaService.getById(uploadedFile.id);
        addTestResult(`Media file verified in backend: ${mediaFile.id}`);
      } catch (mediaError: any) {
        addError(`Failed to verify media file: ${mediaError.message}`);
      }

      toast({
        title: "Course Creation Success",
        description: "Test course created with cover image!"
      });

    } catch (error: any) {
      console.error('Course creation error:', error);
      addError(`Course creation failed: ${error.message}`);
      if (error.response?.data) {
        addError(`Backend response: ${JSON.stringify(error.response.data)}`);
      }
      toast({
        title: "Course Creation Error",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setCreatingCourse(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-brand-red-primary">
            <ImageIcon className="mr-2 h-5 w-5" />
            Cover Image Upload Test
          </CardTitle>
          <CardDescription>
            Test the complete flow: File Upload → Media Service → Course Creation → Database Storage
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* File Upload Section */}
          <div className="space-y-4">
            <Label className="text-sm font-medium">
              Select Cover Image for Testing
            </Label>
            <div className="border-2 border-dashed border-brand-red-primary rounded-lg p-6 text-center hover:bg-gray-50 transition-colors">
              <input
                type="file"
                accept="image/*"
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    handleFileUpload(file);
                  }
                }}
                disabled={uploading}
                className="hidden"
                id="test-image-upload"
              />
              <label htmlFor="test-image-upload" className={`cursor-pointer ${uploading ? 'pointer-events-none' : ''}`}>
                {uploading ? (
                  <div className="space-y-2">
                    <Loader2 className="h-12 w-12 text-brand-red-primary mx-auto animate-spin" />
                    <p className="text-sm font-medium text-gray-700">Uploading image...</p>
                    <Progress value={uploadProgress} className="w-full" />
                    <p className="text-xs text-gray-500">{Math.round(uploadProgress)}% complete</p>
                  </div>
                ) : imagePreview ? (
                  <div className="space-y-2">
                    <img src={imagePreview} alt="Uploaded preview" className="max-h-32 mx-auto rounded" />
                    <p className="text-sm text-green-600 flex items-center justify-center">
                      <CheckCircle className="mr-1 h-4 w-4" />
                      Image uploaded successfully!
                    </p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <Upload className="h-12 w-12 text-brand-red-primary mx-auto" />
                    <p className="text-sm font-medium text-gray-700">
                      Click to select test image
                    </p>
                    <p className="text-xs text-gray-500">
                      JPG, PNG, or WebP format, max 100MB
                    </p>
                  </div>
                )}
              </label>
            </div>
          </div>

          {/* Course Creation Test */}
          {uploadedFile && (
            <div className="space-y-4">
              <Button
                onClick={testCourseCreation}
                disabled={creatingCourse}
                className="w-full bg-green-600 hover:bg-green-700 text-white"
              >
                {creatingCourse ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating Test Course...
                  </>
                ) : (
                  'Test Course Creation with Cover Image'
                )}
              </Button>
            </div>
          )}

          {/* Test Results */}
          {(testResults.length > 0 || errors.length > 0) && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Test Results</h3>
              
              {testResults.length > 0 && (
                <Alert className="border-green-200 bg-green-50">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <AlertDescription className="text-green-800">
                    <div className="space-y-1">
                      {testResults.map((result, index) => (
                        <div key={index} className="text-sm">{result}</div>
                      ))}
                    </div>
                  </AlertDescription>
                </Alert>
              )}

              {errors.length > 0 && (
                <Alert className="border-red-200 bg-red-50">
                  <AlertCircle className="h-4 w-4 text-red-600" />
                  <AlertDescription className="text-red-800">
                    <div className="space-y-1">
                      {errors.map((error, index) => (
                        <div key={index} className="text-sm">{error}</div>
                      ))}
                    </div>
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}

          {/* Created Course Info */}
          {createdCourse && (
            <Alert className="border-blue-200 bg-blue-50">
              <AlertCircle className="h-4 w-4 text-blue-600" />
              <AlertDescription className="text-blue-800">
                <div className="font-medium mb-2">Course Created Successfully!</div>
                <div className="text-sm space-y-1">
                  <div><strong>Course ID:</strong> {createdCourse.Id}</div>
                  <div><strong>Course Slug:</strong> {createdCourse.Slug}</div>
                  <div><strong>Title:</strong> {createdCourse.Title}</div>
                  {createdCourse.CoverImage && (
                    <>
                      <div><strong>Cover Image ID:</strong> {createdCourse.CoverImage.Id}</div>
                      <div><strong>Cover Image File:</strong> {createdCourse.CoverImage.Hashname}</div>
                    </>
                  )}
                </div>
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default CoverImageUploadTest;
