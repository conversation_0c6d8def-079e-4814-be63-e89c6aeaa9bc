import React from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Star, Users, BookOpen, User, ExternalLink } from 'lucide-react';
import { FaFacebook, FaTwitter, FaLinkedin, FaGlobe } from 'react-icons/fa';
import { formatImageUrl } from '../../utils/imageUtils';
import type { Instructor } from '../../models';

interface InstructorCardProps {
  instructor: Instructor;
  stats?: {
    NumberOfCourse: number;
    NumberOfStudent: number;
  };
  variant?: 'grid' | 'list' | 'compact';
  showSocialLinks?: boolean;
  className?: string;
}

const InstructorCard: React.FC<InstructorCardProps> = ({
  instructor,
  stats = { NumberOfCourse: 0, NumberOfStudent: 0 },
  variant = 'grid',
  showSocialLinks = false,
  className = ''
}) => {
  // Debug logging to verify the data structure (can be removed in production)
  console.log('✅ InstructorCard - Instructor data:', {
    name: `${instructor.User?.Firstname || ''} ${instructor.User?.Lastname || ''}`.trim(),
    bio: instructor.Bio || 'No bio',
    specialization: instructor.Specialization || 'No specialization',
    hasUser: !!instructor.User
  });

  // Helper functions
  const getInstructorName = () => {
    if (!instructor.User) {
      console.log('❌ No User object found for instructor:', instructor);
      return 'Unknown Instructor';
    }
    // Use correct field names from User model: Firstname and Lastname (not FirstName/LastName)
    const firstName = instructor.User.Firstname || '';
    const lastName = instructor.User.Lastname || '';
    const fullName = `${firstName} ${lastName}`.trim();
    const finalName = fullName || instructor.User.Username || 'Unknown Instructor';

    console.log('👤 Name calculation:', {
      firstName,
      lastName,
      fullName,
      username: instructor.User.Username,
      finalName
    });

    return finalName;
  };

  const getInstructorBio = () => {
    // Bio can come from instructor.Bio or instructor.User.About
    return instructor.Bio ||
           instructor.User?.About ||
           'Experienced instructor passionate about sharing knowledge and helping students achieve their learning goals.';
  };

  const getInstructorSpecialization = () => {
    // Specialization from instructor record, fallback to user profile
    return instructor.Specialization ||
           instructor.User?.Profil ||
           'Professional Instructor';
  };

  const getInstructorInitials = () => {
    const name = getInstructorName();
    return name.split(' ').map(n => n.charAt(0)).join('').toUpperCase().substring(0, 2);
  };

  const getInstructorRating = () => {
    // TODO: Calculate from actual reviews when available
    return 4.5 + (Math.random() * 0.8 - 0.4); // Random between 4.1 and 4.9
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < Math.floor(rating)
            ? 'fill-yellow-400 text-yellow-400'
            : i < rating
            ? 'fill-yellow-200 text-yellow-400'
            : 'text-gray-300'
        }`}
      />
    ));
  };

  const instructorName = getInstructorName();
  const instructorBio = getInstructorBio();
  const instructorSpecialization = getInstructorSpecialization();
  const instructorInitials = getInstructorInitials();
  const rating = getInstructorRating();

  // Compact variant for carousels and small displays
  if (variant === 'compact') {
    return (
      <Link to={`/instructors/${instructor.User?.Slug || instructor.Id}`}>
        <Card className={`hover:shadow-lg transition-all duration-300 hover:-translate-y-1 ${className}`}>
          <CardContent className="p-4 text-center">
            <Avatar className="w-16 h-16 mx-auto mb-3 border-2 border-red-100">
              {instructor.User?.Photo?.Hashname ? (
                <AvatarImage
                  src={formatImageUrl(instructor.User.Photo.Hashname)}
                  alt={instructorName}
                />
              ) : (
                <AvatarFallback className="bg-red-100 text-red-600 font-semibold">
                  {instructorInitials}
                </AvatarFallback>
              )}
            </Avatar>
            <h4 className="font-semibold text-gray-900 mb-1 text-sm">
              {instructorName}
            </h4>
            <p className="text-xs text-gray-600 mb-2">
              {instructorSpecialization}
            </p>
            <div className="text-xs text-gray-500">
              <span className="font-medium text-red-600">{stats.NumberOfCourse}</span> courses
            </div>
          </CardContent>
        </Card>
      </Link>
    );
  }

  // List variant for list view
  if (variant === 'list') {
    return (
      <Card className={`hover:shadow-md transition-shadow ${className}`}>
        <CardContent className="p-6">
          <div className="flex items-start space-x-6">
            <Avatar className="w-20 h-20 border-2 border-red-100">
              {instructor.User?.Photo?.Hashname ? (
                <AvatarImage
                  src={formatImageUrl(instructor.User.Photo.Hashname)}
                  alt={instructorName}
                />
              ) : (
                <AvatarFallback className="bg-red-100 text-red-600 text-lg font-semibold">
                  {instructorInitials}
                </AvatarFallback>
              )}
            </Avatar>
            
            <div className="flex-1">
              <div className="flex items-start justify-between">
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-1">
                    {instructorName}
                  </h3>
                  <p className="text-gray-600 mb-2">
                    {instructorSpecialization}
                  </p>
                  
                  {/* Rating */}
                  <div className="flex items-center gap-1 mb-3">
                    {renderStars(rating)}
                    <span className="text-sm text-gray-600 ml-1">({rating.toFixed(1)})</span>
                  </div>
                </div>
                
                <Link to={`/instructors/${instructor.User?.Slug || instructor.Id}`}>
                  <Button className="bg-red-600 hover:bg-red-700">
                    View Profile
                  </Button>
                </Link>
              </div>
              
              {/* Bio */}
              <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                {instructorBio}
              </p>
              
              {/* Stats and Specialization */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-6 text-sm text-gray-600">
                  <div className="flex items-center gap-1">
                    <Users className="h-4 w-4" />
                    <span>{stats.NumberOfStudent?.toLocaleString() || 0} students</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <BookOpen className="h-4 w-4" />
                    <span>{stats.NumberOfCourse || 0} courses</span>
                  </div>
                </div>
                
                {instructorSpecialization !== 'Professional Instructor' && (
                  <Badge variant="secondary" className="bg-red-50 text-red-700 border-red-200">
                    {instructorSpecialization}
                  </Badge>
                )}
              </div>

              {/* Social Links */}
              {showSocialLinks && instructor.User && (
                <div className="flex items-center space-x-3 mt-4 pt-4 border-t">
                  {instructor.User.FacebookAddress && (
                    <a
                      href={instructor.User.FacebookAddress}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-700"
                    >
                      <FaFacebook className="w-4 h-4" />
                    </a>
                  )}
                  {instructor.User.TwitterAddress && (
                    <a
                      href={instructor.User.TwitterAddress}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-400 hover:text-blue-500"
                    >
                      <FaTwitter className="w-4 h-4" />
                    </a>
                  )}
                  {instructor.User.LinkedInAddress && (
                    <a
                      href={instructor.User.LinkedInAddress}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-700 hover:text-blue-800"
                    >
                      <FaLinkedin className="w-4 h-4" />
                    </a>
                  )}
                  {instructor.User.WebsiteUrl && (
                    <a
                      href={instructor.User.WebsiteUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-gray-600 hover:text-gray-700"
                    >
                      <FaGlobe className="w-4 h-4" />
                    </a>
                  )}
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Default grid variant
  return (
    <Card className={`hover:shadow-lg transition-all duration-300 hover:-translate-y-1 ${className}`}>
      <CardContent className="p-6">
        <div className="text-center mb-4">
          <Avatar className="w-24 h-24 mx-auto mb-4 border-4 border-red-100">
            {instructor.User?.Photo?.Hashname ? (
              <AvatarImage
                src={formatImageUrl(instructor.User.Photo.Hashname)}
                alt={instructorName}
              />
            ) : (
              <AvatarFallback className="bg-red-100 text-red-600 text-xl font-bold">
                {instructorInitials}
              </AvatarFallback>
            )}
          </Avatar>
          <h3 className="text-xl font-bold text-gray-900 mb-1">
            {instructorName}
          </h3>
          <p className="text-sm text-gray-600 mb-3">
            {instructorSpecialization}
          </p>

          {/* Rating */}
          <div className="flex items-center justify-center gap-1 mb-4">
            {renderStars(rating)}
            <span className="text-sm text-gray-600 ml-1">({rating.toFixed(1)})</span>
          </div>
        </div>

        {/* Bio */}
        <p className="text-gray-600 text-sm mb-4 line-clamp-3 leading-relaxed">
          {instructorBio}
        </p>

        {/* Stats */}
        <div className="grid grid-cols-2 gap-4 mb-4 p-3 bg-gray-50 rounded-lg">
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 text-red-600 mb-1">
              <Users className="h-4 w-4" />
            </div>
            <div className="text-lg font-bold text-gray-900">
              {stats.NumberOfStudent?.toLocaleString() || 0}
            </div>
            <div className="text-xs text-gray-600">Students</div>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 text-red-600 mb-1">
              <BookOpen className="h-4 w-4" />
            </div>
            <div className="text-lg font-bold text-gray-900">
              {stats.NumberOfCourse || 0}
            </div>
            <div className="text-xs text-gray-600">Courses</div>
          </div>
        </div>

        {/* Specialization Badge */}
        {instructorSpecialization !== 'Professional Instructor' && (
          <div className="flex flex-wrap gap-1 mb-4">
            <Badge variant="secondary" className="bg-red-50 text-red-700 border-red-200">
              {instructorSpecialization}
            </Badge>
          </div>
        )}

        {/* Action Button */}
        <Link to={`/instructors/${instructor.User?.Slug || instructor.Id}`}>
          <Button className="w-full bg-red-600 hover:bg-red-700 transition-colors">
            <User className="w-4 h-4 mr-2" />
            View Profile
          </Button>
        </Link>

        {/* Social Links */}
        {showSocialLinks && instructor.User && (
          <div className="flex items-center justify-center space-x-3 mt-4 pt-4 border-t">
            {instructor.User.FacebookAddress && (
              <a
                href={instructor.User.FacebookAddress}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-700"
              >
                <FaFacebook className="w-4 h-4" />
              </a>
            )}
            {instructor.User.TwitterAddress && (
              <a
                href={instructor.User.TwitterAddress}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-400 hover:text-blue-500"
              >
                <FaTwitter className="w-4 h-4" />
              </a>
            )}
            {instructor.User.LinkedInAddress && (
              <a
                href={instructor.User.LinkedInAddress}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-700 hover:text-blue-800"
              >
                <FaLinkedin className="w-4 h-4" />
              </a>
            )}
            {instructor.User.WebsiteUrl && (
              <a
                href={instructor.User.WebsiteUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-600 hover:text-gray-700"
              >
                <FaGlobe className="w-4 h-4" />
              </a>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default InstructorCard;
