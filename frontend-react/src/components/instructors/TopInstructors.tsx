import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Loader2, ChevronRight, Star, TrendingUp } from 'lucide-react';
import { FaChalkboardTeacher, FaUsers, FaArrowRight } from 'react-icons/fa';
import { useInstructors, useCourseInstructors } from '../../hooks';
import InstructorCard from './InstructorCard';
import type { Instructor } from '../../models';

interface TopInstructorsProps {
  limit?: number;
  title?: string;
  subtitle?: string;
  showViewAll?: boolean;
  variant?: 'grid' | 'carousel';
  className?: string;
}

const TopInstructors: React.FC<TopInstructorsProps> = ({
  limit = 6,
  title = "Meet Our Top Instructors",
  subtitle = "Learn from industry experts who are passionate about sharing their knowledge",
  showViewAll = true,
  variant = 'grid',
  className = ''
}) => {
  const { data: instructors, loading: instructorsLoading } = useInstructors();
  const { data: instructorStats, loading: statsLoading } = useCourseInstructors();

  const loading = instructorsLoading || statsLoading;

  // Helper function to get instructor stats
  const getInstructorStats = (instructorId: number) => {
    return instructorStats?.find(stat => stat.Instructor?.Id === instructorId) || {
      NumberOfCourse: 0,
      NumberOfStudent: 0
    };
  };

  // Get top instructors based on student count and course count
  const topInstructors = React.useMemo(() => {
    if (!instructors || !instructorStats) return [];

    const instructorsWithStats = instructors.map(instructor => {
      const stats = getInstructorStats(instructor.Id);
      return {
        ...instructor,
        totalScore: (stats.NumberOfStudent || 0) + (stats.NumberOfCourse || 0) * 10 // Weight courses more
      };
    });

    return instructorsWithStats
      .sort((a, b) => b.totalScore - a.totalScore)
      .slice(0, limit);
  }, [instructors, instructorStats, limit]);

  if (loading) {
    return (
      <div className={`py-16 ${className}`}>
        <div className="max-w-6xl mx-auto px-6">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-red-600" />
            <p className="text-gray-600">Loading top instructors...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!topInstructors.length) {
    return (
      <div className={`py-16 ${className}`}>
        <div className="max-w-6xl mx-auto px-6">
          <div className="text-center">
            <FaChalkboardTeacher className="h-16 w-16 mx-auto mb-6 text-gray-400" />
            <h3 className="text-2xl font-medium text-gray-900 mb-2">No instructors available</h3>
            <p className="text-gray-500">Check back later for our expert instructors.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <section className={`py-16 bg-gray-50 ${className}`}>
      <div className="max-w-6xl mx-auto px-6">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
              <TrendingUp className="w-8 h-8 text-red-600" />
            </div>
          </div>
          <h2 className="text-4xl font-bold text-gray-900 mb-4">{title}</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            {subtitle}
          </p>
          
          {/* Quick Stats */}
          <div className="flex justify-center space-x-8 text-gray-600 mb-8">
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {instructorStats?.length || 0}
              </div>
              <div className="text-sm">Expert Instructors</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {instructorStats?.reduce((sum, stat) => sum + (stat.NumberOfCourse || 0), 0).toLocaleString() || 0}
              </div>
              <div className="text-sm">Total Courses</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {instructorStats?.reduce((sum, stat) => sum + (stat.NumberOfStudent || 0), 0).toLocaleString() || 0}
              </div>
              <div className="text-sm">Students Taught</div>
            </div>
          </div>
        </div>

        {/* Instructors Grid */}
        {variant === 'grid' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            {topInstructors.map((instructor) => (
              <InstructorCard
                key={instructor.Id}
                instructor={instructor}
                stats={getInstructorStats(instructor.Id)}
                variant="grid"
                showSocialLinks={false}
              />
            ))}
          </div>
        ) : (
          // Carousel variant
          <div className="flex overflow-x-auto space-x-6 pb-4 mb-12 scrollbar-hide">
            {topInstructors.map((instructor) => (
              <div key={instructor.Id} className="flex-none w-80">
                <InstructorCard
                  instructor={instructor}
                  stats={getInstructorStats(instructor.Id)}
                  variant="grid"
                  showSocialLinks={false}
                />
              </div>
            ))}
          </div>
        )}

        {/* Call to Action */}
        <div className="text-center">
          {showViewAll && (
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/instructors">
                <Button size="lg" className="bg-red-600 hover:bg-red-700">
                  <FaUsers className="w-5 h-5 mr-2" />
                  View All Instructors
                  <ChevronRight className="w-4 h-4 ml-2" />
                </Button>
              </Link>
              <Link to="/become-instructor">
                <Button size="lg" variant="outline" className="border-red-600 text-red-600 hover:bg-red-50">
                  <FaChalkboardTeacher className="w-5 h-5 mr-2" />
                  Become an Instructor
                </Button>
              </Link>
            </div>
          )}
        </div>

        {/* Featured Instructor Highlight */}
        {topInstructors.length > 0 && (
          <div className="mt-16">
            <Card className="bg-gradient-to-r from-red-50 to-red-100 border-red-200">
              <CardContent className="p-8">
                <div className="flex flex-col md:flex-row items-center space-y-6 md:space-y-0 md:space-x-8">
                  <div className="flex-1 text-center md:text-left">
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">
                      🌟 Featured Instructor of the Month
                    </h3>
                    <p className="text-gray-600 mb-6">
                      Meet {topInstructors[0].User?.FirstName || 'our featured instructor'}, 
                      one of our most popular instructors with {getInstructorStats(topInstructors[0].Id).NumberOfStudent?.toLocaleString() || 0} students 
                      and {getInstructorStats(topInstructors[0].Id).NumberOfCourse || 0} courses.
                    </p>
                    <Link to={`/instructors/${topInstructors[0].User?.Slug || topInstructors[0].Id}`}>
                      <Button className="bg-red-600 hover:bg-red-700">
                        View Profile
                        <FaArrowRight className="w-4 h-4 ml-2" />
                      </Button>
                    </Link>
                  </div>
                  <div className="flex-none">
                    <InstructorCard
                      instructor={topInstructors[0]}
                      stats={getInstructorStats(topInstructors[0].Id)}
                      variant="compact"
                      className="w-64"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </section>
  );
};

export default TopInstructors;
