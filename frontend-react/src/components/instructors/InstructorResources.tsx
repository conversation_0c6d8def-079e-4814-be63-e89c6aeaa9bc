import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  BookOpen, 
  Video, 
  FileText, 
  HelpCircle, 
  MessageCircle, 
  Users, 
  TrendingUp,
  Award,
  Clock,
  Download,
  ExternalLink,
  Play,
  Star
} from 'lucide-react';
import { 
  FaBook, 
  FaVideo, 
  FaQuestionCircle, 
  FaComments, 
  FaUsers, 
  FaChartLine,
  FaTrophy,
  FaClock,
  FaDownload,
  FaExternalLinkAlt,
  FaPlay,
  FaStar,
  FaGraduationCap,
  FaLightbulb,
  FaRocket
} from 'react-icons/fa';

interface ResourceItem {
  id: string;
  title: string;
  description: string;
  type: 'guide' | 'video' | 'template' | 'faq' | 'community';
  icon: React.ReactNode;
  link: string;
  isExternal?: boolean;
  duration?: string;
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  popular?: boolean;
}

interface InstructorResourcesProps {
  variant?: 'full' | 'compact';
  showHeader?: boolean;
  className?: string;
}

const InstructorResources: React.FC<InstructorResourcesProps> = ({
  variant = 'full',
  showHeader = true,
  className = ''
}) => {
  const resources: ResourceItem[] = [
    {
      id: '1',
      title: 'Getting Started as an Instructor',
      description: 'Complete guide to creating your first course and setting up your instructor profile.',
      type: 'guide',
      icon: <FaRocket className="w-5 h-5" />,
      link: '/instructor-guide/getting-started',
      duration: '15 min read',
      difficulty: 'beginner',
      popular: true
    },
    {
      id: '2',
      title: 'Course Creation Best Practices',
      description: 'Learn how to structure your courses for maximum student engagement and success.',
      type: 'video',
      icon: <FaVideo className="w-5 h-5" />,
      link: '/instructor-guide/course-creation',
      duration: '25 min',
      difficulty: 'intermediate',
      popular: true
    },
    {
      id: '3',
      title: 'Video Recording Setup Guide',
      description: 'Technical guide for setting up your recording equipment and software.',
      type: 'guide',
      icon: <Video className="w-5 h-5" />,
      link: '/instructor-guide/video-setup',
      duration: '20 min read',
      difficulty: 'beginner'
    },
    {
      id: '4',
      title: 'Course Outline Template',
      description: 'Ready-to-use template for planning and structuring your course content.',
      type: 'template',
      icon: <FaDownload className="w-5 h-5" />,
      link: '/downloads/course-outline-template.pdf',
      isExternal: true,
      difficulty: 'beginner'
    },
    {
      id: '5',
      title: 'Student Engagement Strategies',
      description: 'Proven techniques to keep your students motivated and engaged throughout your course.',
      type: 'guide',
      icon: <FaLightbulb className="w-5 h-5" />,
      link: '/instructor-guide/engagement',
      duration: '18 min read',
      difficulty: 'intermediate',
      popular: true
    },
    {
      id: '6',
      title: 'Instructor Community Forum',
      description: 'Connect with other instructors, share experiences, and get support from the community.',
      type: 'community',
      icon: <FaComments className="w-5 h-5" />,
      link: '/instructor-community',
      difficulty: 'beginner'
    },
    {
      id: '7',
      title: 'Marketing Your Course',
      description: 'Learn how to effectively promote your course and attract more students.',
      type: 'video',
      icon: <FaChartLine className="w-5 h-5" />,
      link: '/instructor-guide/marketing',
      duration: '30 min',
      difficulty: 'advanced'
    },
    {
      id: '8',
      title: 'Frequently Asked Questions',
      description: 'Find answers to common questions about teaching on our platform.',
      type: 'faq',
      icon: <FaQuestionCircle className="w-5 h-5" />,
      link: '/instructor-faq',
      difficulty: 'beginner'
    }
  ];

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'guide': return 'bg-blue-50 text-blue-700 border-blue-200';
      case 'video': return 'bg-red-50 text-red-700 border-red-200';
      case 'template': return 'bg-green-50 text-green-700 border-green-200';
      case 'faq': return 'bg-yellow-50 text-yellow-700 border-yellow-200';
      case 'community': return 'bg-purple-50 text-purple-700 border-purple-200';
      default: return 'bg-gray-50 text-gray-700 border-gray-200';
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-800';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (variant === 'compact') {
    return (
      <div className={`py-8 ${className}`}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {resources.slice(0, 4).map((resource) => (
            <Card key={resource.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-4">
                <div className="flex items-center space-x-3 mb-2">
                  <div className="text-red-600">
                    {resource.icon}
                  </div>
                  <h4 className="font-semibold text-sm text-gray-900 line-clamp-1">
                    {resource.title}
                  </h4>
                </div>
                <p className="text-xs text-gray-600 mb-3 line-clamp-2">
                  {resource.description}
                </p>
                <Link 
                  to={resource.link}
                  target={resource.isExternal ? '_blank' : undefined}
                  rel={resource.isExternal ? 'noopener noreferrer' : undefined}
                >
                  <Button size="sm" variant="outline" className="w-full text-xs">
                    {resource.type === 'template' ? 'Download' : 'Learn More'}
                    {resource.isExternal && <ExternalLink className="w-3 h-3 ml-1" />}
                  </Button>
                </Link>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <section className={`py-16 bg-white ${className}`}>
      <div className="max-w-6xl mx-auto px-6">
        {showHeader && (
          <div className="text-center mb-12">
            <div className="flex justify-center mb-6">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                <FaGraduationCap className="w-8 h-8 text-red-600" />
              </div>
            </div>
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Instructor Resources</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Everything you need to create amazing courses and succeed as an instructor on our platform.
            </p>
          </div>
        )}

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
          <Card>
            <CardContent className="p-6 text-center">
              <FaBook className="w-8 h-8 mx-auto mb-3 text-red-600" />
              <h3 className="text-2xl font-bold text-gray-900">15+</h3>
              <p className="text-gray-600">Guides & Tutorials</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <FaVideo className="w-8 h-8 mx-auto mb-3 text-red-600" />
              <h3 className="text-2xl font-bold text-gray-900">8+</h3>
              <p className="text-gray-600">Video Tutorials</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <FaDownload className="w-8 h-8 mx-auto mb-3 text-red-600" />
              <h3 className="text-2xl font-bold text-gray-900">5+</h3>
              <p className="text-gray-600">Templates</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <FaUsers className="w-8 h-8 mx-auto mb-3 text-red-600" />
              <h3 className="text-2xl font-bold text-gray-900">24/7</h3>
              <p className="text-gray-600">Community Support</p>
            </CardContent>
          </Card>
        </div>

        {/* Resources Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {resources.map((resource) => (
            <Card key={resource.id} className="hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="text-red-600">
                      {resource.icon}
                    </div>
                    <div>
                      <Badge variant="secondary" className={getTypeColor(resource.type)}>
                        {resource.type}
                      </Badge>
                      {resource.popular && (
                        <Badge variant="secondary" className="ml-2 bg-orange-50 text-orange-700 border-orange-200">
                          <Star className="w-3 h-3 mr-1" />
                          Popular
                        </Badge>
                      )}
                    </div>
                  </div>
                  {resource.difficulty && (
                    <Badge variant="secondary" className={getDifficultyColor(resource.difficulty)}>
                      {resource.difficulty}
                    </Badge>
                  )}
                </div>

                <h3 className="text-lg font-bold text-gray-900 mb-2">
                  {resource.title}
                </h3>
                
                <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                  {resource.description}
                </p>

                {resource.duration && (
                  <div className="flex items-center text-sm text-gray-500 mb-4">
                    <Clock className="w-4 h-4 mr-1" />
                    {resource.duration}
                  </div>
                )}

                <Link 
                  to={resource.link}
                  target={resource.isExternal ? '_blank' : undefined}
                  rel={resource.isExternal ? 'noopener noreferrer' : undefined}
                >
                  <Button className="w-full bg-red-600 hover:bg-red-700">
                    {resource.type === 'video' && <Play className="w-4 h-4 mr-2" />}
                    {resource.type === 'template' && <Download className="w-4 h-4 mr-2" />}
                    {resource.type === 'community' && <MessageCircle className="w-4 h-4 mr-2" />}
                    {resource.type === 'faq' && <HelpCircle className="w-4 h-4 mr-2" />}
                    {resource.type === 'guide' && <BookOpen className="w-4 h-4 mr-2" />}
                    
                    {resource.type === 'template' ? 'Download' : 
                     resource.type === 'video' ? 'Watch Now' :
                     resource.type === 'community' ? 'Join Community' :
                     'Learn More'}
                    
                    {resource.isExternal && <ExternalLink className="w-4 h-4 ml-2" />}
                  </Button>
                </Link>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <Card className="bg-gradient-to-r from-red-50 to-red-100 border-red-200">
            <CardContent className="py-12">
              <FaTrophy className="w-16 h-16 mx-auto mb-6 text-red-600" />
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Ready to Start Teaching?</h3>
              <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
                Join thousands of successful instructors who are sharing their knowledge and earning money on our platform.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link to="/become-instructor">
                  <Button size="lg" className="bg-red-600 hover:bg-red-700">
                    <FaGraduationCap className="w-5 h-5 mr-2" />
                    Become an Instructor
                  </Button>
                </Link>
                <Link to="/instructor-community">
                  <Button size="lg" variant="outline" className="border-red-600 text-red-600 hover:bg-red-50">
                    <FaComments className="w-5 h-5 mr-2" />
                    Join Community
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default InstructorResources;
