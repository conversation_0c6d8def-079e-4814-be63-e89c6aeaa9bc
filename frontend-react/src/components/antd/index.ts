// shadcn/ui Compatibility Layer
// This file provides a drop-in replacement for Ant Design components using shadcn/ui
// All components are now mapped to their shadcn/ui equivalents

import React from 'react';
import { cn } from '../../lib/utils';

// Import shadcn/ui components
import {
  Button as ShadcnButton,
  Card as ShadcnCard,
  CardHeader,
  CardTitle,
  CardContent,
  Alert as ShadcnAlert,
  Badge as ShadcnBadge,
  Avatar as ShadcnAvatar,
  Input as ShadcnInput,
  Label as ShadcnLabel,
  Textarea as ShadcnTextarea,
  Select as ShadcnSelect,
  Checkbox as ShadcnCheckbox,
  Switch as ShadcnSwitch,
  Dialog as ShadcnDialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DropdownMenu as ShadcnDropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  Popover as ShadcnPopover,
  <PERSON>lt<PERSON> as <PERSON>hadcn<PERSON>ooltip,
  <PERSON>ltipTrigger,
  <PERSON><PERSON><PERSON>Content,
  <PERSON>ltipProvider,
  <PERSON><PERSON> as <PERSON>hadcnTabs,
  <PERSON><PERSON>rdion as <PERSON>hadcnAccordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
  Breadcrumb as ShadcnBreadcrumb,
  Progress as ShadcnProgress,
  Separator as ShadcnSeparator,
  Sheet as ShadcnSheet
} from '../ui';

// Import individual components
import { Slider } from '../ui/slider';
import { Pagination as ShadcnPagination } from '../ui/pagination';

// Import Lucide icons as replacements for Ant Design icons
import {
  AlertCircle,
  Info,
  CheckCircle,
  XCircle,
  Loader2,
  Calendar as CalendarIcon,
  Clock,
  User,
  Menu as MenuIcon,
  ArrowUp,
  Settings,
  Mail,
  Globe,
  Users,
  Plus,
  Edit,
  Eye,
  EyeOff,
  Download,
  Save,
  Book,
  FileText,
  MessageCircle,
  Bell,
  DollarSign,
  BarChart3,
  Shield,
  Database,
  Rocket,
  Crown,
  Gift,
  Lightbulb,
  Zap,
  TrendingUp,
  TrendingDown,
  Heart,
  BookOpen,
  HelpCircle,
  UserPlus,
  Folder,
  Video,
  Trophy,
  Star as StarIcon,
  ArrowDown,
  Minus,
  X as XIcon,
  Play,
  Upload as UploadLucide,
  Check,
  Share2,
  MoreHorizontal,
  Search as SearchIcon,
  List as ListIcon,
  Grid3X3,
  ShoppingCart,
  Trash2,
  CreditCard
} from 'lucide-react';

// ===== ENHANCED COMPONENT MAPPINGS =====

// Enhanced Button with Ant Design prop compatibility
export const Button = ({ type, size, icon, loading, children, ghost, htmlType, iconPosition, ...props }: any) => {
  const variant = type === 'primary' ? 'default' : type === 'danger' ? 'destructive' : 'outline';
  const sizeMap: { [key: string]: string } = { large: 'lg', small: 'sm', default: 'default' };
  const mappedSize = sizeMap[size as string] || 'default';

  // Filter out antd-specific props that shouldn't be passed to DOM
  const { ...domProps } = props;

  return React.createElement(ShadcnButton, {
    variant,
    size: mappedSize,
    disabled: loading,
    type: htmlType || 'button',
    ...domProps
  }, [
    loading && React.createElement(Loader2, { key: 'loader', className: "mr-2 h-4 w-4 animate-spin" }),
    icon && React.createElement('span', { key: 'icon', className: "mr-2" }, icon),
    children
  ].filter(Boolean));
};

// Enhanced TextArea with Ant Design prop compatibility
const TextAreaComponent = ({ showCount, maxLength, ...props }: any) => {
  const [value, setValue] = React.useState(props.value || props.defaultValue || '');

  React.useEffect(() => {
    if (props.value !== undefined) {
      setValue(props.value);
    }
  }, [props.value]);

  const handleChange = (e: any) => {
    const newValue = e.target.value;
    if (maxLength && newValue.length > maxLength) {
      return; // Don't allow exceeding maxLength
    }
    setValue(newValue);
    props.onChange?.(e);
  };

  return React.createElement('div', { classUsername: "relative" }, [
    React.createElement(ShadcnTextarea, {
      key: 'textarea',
      ...props,
      value,
      maxLength,
      onChange: handleChange
    }),
    showCount && React.createElement('div', {
      key: 'count',
      classUsername: "absolute bottom-2 right-2 text-xs text-gray-500 bg-white px-1 rounded"
    }, `${value.length}${maxLength ? `/${maxLength}` : ''}`)
  ]);
};

// Enhanced Input with Ant Design prop compatibility
export const Input = Object.assign(
  ({ size, addonBefore, addonAfter, prefix, suffix, ...props }: any) => {
    const sizeClass = size === 'large' ? 'h-12' : size === 'small' ? 'h-8' : 'h-10';

    if (addonBefore || addonAfter || prefix || suffix) {
      return React.createElement('div', { classUsername: "flex items-center" }, [
        addonBefore && React.createElement('span', { key: 'before', classUsername: "px-3 bg-gray-100 border border-r-0 rounded-l" }, addonBefore),
        React.createElement('div', { key: 'input-wrapper', classUsername: "relative flex-1" }, [
          prefix && React.createElement('span', { key: 'prefix', classUsername: "absolute left-3 top-1/2 transform -translate-y-1/2" }, prefix),
          React.createElement(ShadcnInput, {
            key: 'input',
            classUsername: cn(sizeClass, prefix && "pl-10", suffix && "pr-10"),
            ...props
          }),
          suffix && React.createElement('span', { key: 'suffix', classUsername: "absolute right-3 top-1/2 transform -translate-y-1/2" }, suffix)
        ].filter(Boolean)),
        addonAfter && React.createElement('span', { key: 'after', classUsername: "px-3 bg-gray-100 border border-l-0 rounded-r" }, addonAfter)
      ].filter(Boolean));
    }

    return React.createElement(ShadcnInput, { classUsername: sizeClass, ...props });
  },
  {
    TextArea: TextAreaComponent,
    Search: ({ onSearch, ...props }: any) => {
      return React.createElement(ShadcnInput, {
        type: "search",
        onChange: (e: any) => onSearch?.(e.target.value),
        ...props
      });
    },
    Password: ({ visibilityToggle = true, ...props }: any) => {
      const [visible, setVisible] = React.useState(false);

      return React.createElement('div', { classUsername: "relative" }, [
        React.createElement(ShadcnInput, {
          key: 'input',
          type: visible ? "text" : "password",
          ...props
        }),
        visibilityToggle && React.createElement('button', {
          key: 'toggle',
          type: 'button',
          classUsername: "absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",
          onClick: () => setVisible(!visible)
        }, visible ? React.createElement(Eye, { className: "h-4 w-4" }) : React.createElement(EyeOff, { className: "h-4 w-4" }))
      ]);
    }
  }
);

// Enhanced Select with Ant Design prop compatibility
export const Select = Object.assign(
  ({ onChange, value, defaultValue, placeholder, children, style, ...props }: any) => {
    return React.createElement('div', { style },
      React.createElement(ShadcnSelect, {
        value,
        defaultValue,
        onValueChange: onChange,
        ...props
      }, children)
    );
  },
  {
    Option: ({ children, value, ...props }: any) => {
      return React.createElement('option', { value, ...props }, children);
    }
  }
);

// Enhanced Alert with Ant Design prop compatibility
export const Alert = ({ type, message, description, showIcon, closable, onClose, ...props }: any) => {
  const variant = type === 'error' ? 'destructive' : 'default';

  return React.createElement(ShadcnAlert, { variant, ...props }, [
    showIcon && React.createElement(AlertCircle, { key: 'icon', className: "h-4 w-4" }),
    React.createElement('div', { key: 'content' }, [
      message && React.createElement('div', { key: 'message', classUsername: "font-medium" }, message),
      description && React.createElement('div', { key: 'description', classUsername: "text-sm" }, description)
    ].filter(Boolean)),
    closable && React.createElement('button', {
      key: 'close',
      onClick: onClose,
      classUsername: "absolute top-2 right-2"
    }, React.createElement(XIcon, { className: "h-4 w-4" }))
  ].filter(Boolean));
};

// Enhanced Form with Ant Design compatibility
export const Form = Object.assign(
  ({ children, onFinish, form, layout = 'vertical', ...props }: any) => {
    return React.createElement('form', {
      onSubmit: (e: any) => {
        e.preventDefault();
        const formData = new FormData(e.target);
        const values = Object.fromEntries(formData.entries());
        onFinish?.(values);
      },
      classUsername: cn("space-y-4", layout === 'horizontal' && "space-y-0 space-x-4 flex"),
      ...props
    }, children);
  },
  {
    Item: ({ label, name, rules, children, ...props }: any) => {
      return React.createElement('div', { classUsername: "space-y-2", ...props }, [
        label && React.createElement(ShadcnLabel, { key: 'label', htmlFor: name }, label),
        React.createElement('div', { key: 'control' },
          React.isValidElement(children)
            ? React.cloneElement(children as React.ReactElement, { name, id: name })
            : children
        )
      ]);
    },

    Group: ({ children, className, ...props }: any) => {
      return React.createElement('div', {
        classUsername: cn("space-y-2", className),
        ...props
      }, children);
    },

    Label: ({ children, className, ...props }: any) => {
      return React.createElement(ShadcnLabel, {
        classUsername: cn("text-sm font-medium", className),
        ...props
      }, children);
    },

    Control: ({ type = 'text', className, as, rows, ...props }: any) => {
      if (as === 'textarea') {
        return React.createElement('textarea', {
          rows,
          classUsername: cn("w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-vertical", className),
          ...props
        });
      }
      return React.createElement(ShadcnInput, {
        type,
        classUsername: cn("w-full", className),
        ...props
      });
    },

    Select: ({ children, className, ...props }: any) => {
      return React.createElement('select', {
        classUsername: cn("w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500", className),
        ...props
      }, children);
    },
    useForm: () => {
      const [formData, setFormData] = React.useState<any>({});

      const formInstance = {
        getFieldsValue: () => formData,
        getFieldValue: (name: string) => formData[name],
        setFieldsValue: (values: any) => {
          setFormData((prev: any) => ({ ...prev, ...values }));
        },
        validateFields: () => Promise.resolve(formData),
        resetFields: () => setFormData({})
      };
      return [formInstance];
    }
  }
);

// Enhanced Table with Ant Design compatibility
export const Table = ({ columns = [], dataSource = [], pagination, loading, ...props }: any) => {
  if (loading) {
    return React.createElement('div', { classUsername: "flex justify-center p-8" },
      React.createElement(Loader2, { className: "h-8 w-8 animate-spin" })
    );
  }

  return React.createElement('div', { classUsername: "w-full", ...props }, [
    React.createElement('table', { key: 'table', classUsername: "w-full border-collapse border border-gray-200" }, [
      React.createElement('thead', { key: 'head' },
        React.createElement('tr', { classUsername: "bg-gray-50" },
          columns.map((col: any, index: number) =>
            React.createElement('th', {
              key: col.key || col.dataIndex || index,
              classUsername: "border border-gray-200 px-4 py-2 text-left font-medium"
            }, col.title)
          )
        )
      ),
      React.createElement('tbody', { key: 'body' },
        dataSource.map((record: any, rowIndex: number) =>
          React.createElement('tr', { key: record.key || rowIndex, classUsername: "hover:bg-gray-50" },
            columns.map((col: any, colIndex: number) =>
              React.createElement('td', {
                key: `${rowIndex}-${colIndex}`,
                classUsername: "border border-gray-200 px-4 py-2"
              }, col.render ? col.render(record[col.dataIndex], record, rowIndex) : record[col.dataIndex])
            )
          )
        )
      )
    ]),
    pagination && React.createElement('div', { key: 'pagination', className: "mt-4 flex justify-center" },
      React.createElement(ShadcnPagination, pagination)
    )
  ]);
};

// Enhanced Card with Ant Design prop compatibility
export const Card = Object.assign(
  ({ title, children, hoverable, loading, size, extra, bordered, ...props }: any) => {
    const sizeClasses: { [key: string]: string } = {
      small: 'text-sm',
      default: '',
      large: 'text-lg'
    };

    const className = cn(
      hoverable && "hover:shadow-lg transition-shadow",
      size && sizeClasses[size],
      props.className
    );

    const cardContent = loading
      ? React.createElement('div', { classUsername: "animate-pulse" }, children)
      : children;

    // Filter out Ant Design specific props
    const { actions, cover, bodyStyle, headStyle, ...domProps } = props;

    return React.createElement(ShadcnCard, { ...domProps, className }, [
      (title || extra) && React.createElement(CardHeader, { key: 'header', className: 'flex flex-row items-center justify-between' }, [
        title && React.createElement(CardTitle, { key: 'title' }, title),
        extra && React.createElement('div', { key: 'extra' }, extra)
      ]),
      React.createElement(CardContent, { key: 'content' }, cardContent)
    ]);
  },
  {
    Header: ({ children, ...props }: any) => {
      return React.createElement('div', {
        classUsername: "px-6 py-4 border-b border-gray-200 font-medium",
        ...props
      }, children);
    },
    Body: ({ children, ...props }: any) => {
      return React.createElement('div', {
        classUsername: "p-6",
        ...props
      }, children);
    },
    Title: ({ children, className, ...props }: any) => {
      return React.createElement('h3', {
        classUsername: cn("text-lg font-semibold", className),
        ...props
      }, children);
    },
    Footer: ({ children, className, ...props }: any) => {
      return React.createElement('div', {
        classUsername: cn("px-6 py-4 border-t border-gray-200 bg-gray-50", className),
        ...props
      }, children);
    },
    Meta: ({ title, description, avatar, ...props }: any) => {
      return React.createElement('div', { classUsername: "flex items-start space-x-3", ...props }, [
        avatar && React.createElement('div', { key: 'avatar' }, avatar),
        React.createElement('div', { key: 'content' }, [
          title && React.createElement('div', { key: 'title', classUsername: "font-medium" }, title),
          description && React.createElement('div', { key: 'desc', classUsername: "text-sm text-gray-500" }, description)
        ])
      ]);
    }
  }
);

// Enhanced Badge with Ant Design prop compatibility
export const Badge = ({ status, text, color, count, children, ...props }: any) => {
  if (status || text) {
    const statusColors: { [key: string]: string } = {
      success: 'bg-green-500',
      processing: 'bg-blue-500',
      default: 'bg-gray-500',
      error: 'bg-red-500',
      warning: 'bg-yellow-500'
    };

    return React.createElement('span', { classUsername: "inline-flex items-center", ...props }, [
      React.createElement('span', {
        key: 'dot',
        classUsername: cn("w-2 h-2 rounded-full mr-2", statusColors[status as string] || 'bg-gray-500')
      }),
      text && React.createElement('span', { key: 'text' }, text)
    ]);
  }

  if (count !== undefined) {
    return React.createElement('span', { classUsername: "relative inline-block", ...props }, [
      children,
      count > 0 && React.createElement('span', {
        key: 'count',
        classUsername: "absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center"
      }, count)
    ]);
  }

  return React.createElement(ShadcnBadge, {
    variant: color === 'red' ? 'destructive' : 'default',
    ...props
  }, children);
};
// Enhanced Avatar with Ant Design prop compatibility
export const Avatar = React.forwardRef<HTMLDivElement, any>(({ src, alt, size, icon, children, ...props }, ref) => {
  const sizeClasses: { [key: string]: string } = {
    small: 'h-6 w-6',
    default: 'h-8 w-8',
    large: 'h-12 w-12'
  };

  const sizeClass = sizeClasses[size as string] || sizeClasses.default;

  if (src) {
    return React.createElement('img', {
      ref,
      src,
      alt: alt || '',
      classUsername: cn("rounded-full object-cover", sizeClass),
      ...props
    });
  }

  return React.createElement(ShadcnAvatar, {
    ref,
    className: cn(sizeClass),
    ...props
  }, [
    icon && React.createElement('span', { key: 'icon' }, icon),
    children && React.createElement('span', { key: 'children' }, children)
  ].filter(Boolean));
});
export const Label = ShadcnLabel;
// Checkbox with Group support
// Checkbox Group Props
interface CheckboxGroupProps {
  options?: Array<{ label: string; value: string; disabled?: boolean }>;
  value?: string[];
  onChange?: (checkedValues: string[]) => void;
  disabled?: boolean;
  children?: React.ReactNode;
}

// Checkbox Group Component
const AntdCheckboxGroup: React.FC<CheckboxGroupProps> = ({ options, value = [], onChange, disabled, children }) => {
  const handleChange = (optionValue: string, checked: boolean) => {
    if (!onChange) return;

    if (checked) {
      onChange([...value, optionValue]);
    } else {
      onChange(value.filter(v => v !== optionValue));
    }
  };

  if (children) {
    return React.createElement('div', { classUsername: 'space-y-2' }, children);
  }

  return React.createElement('div', { classUsername: 'space-y-2' },
    options?.map(option =>
      React.createElement(ShadcnCheckbox, {
        key: option.value,
        checked: value.includes(option.value),
        onCheckedChange: (checked: boolean) => handleChange(option.value, !!checked),
        disabled: disabled || option.disabled
      }, option.label)
    )
  );
};

// Create Checkbox component with Group property
const AntdCheckbox: any = React.forwardRef<any, any>((props, ref) => {
  return React.createElement(ShadcnCheckbox, { ...props, ref });
});

// Attach Group to the component
AntdCheckbox.Group = AntdCheckboxGroup;

// Export as Checkbox
export const Checkbox = AntdCheckbox;



// Bootstrap-style components for compatibility
export const Container = ({ children, ...props }: any) =>
  React.createElement('div', { className: 'container mx-auto px-4', ...props }, children);

// Nav component for Bootstrap compatibility - removed duplicate, using enhanced version below

// Nav.Item and Nav.Link will be added to the enhanced Nav component below

// Switch with Ant Design prop compatibility
export const Switch = ({ checkedChildren, unCheckedChildren, onChange, ...props }: {
  checkedChildren?: React.ReactNode;
  unCheckedChildren?: React.ReactNode;
  onChange?: (checked: boolean) => void;
  checked?: boolean;
  [key: string]: any;
}) => {
  return React.createElement('div', { classUsername: "flex items-center space-x-2" }, [
    React.createElement(ShadcnSwitch, {
      key: 'switch',
      onCheckedChange: onChange,
      ...props
    }),
    (checkedChildren || unCheckedChildren) && React.createElement('span', {
      key: 'label',
      classUsername: "text-sm text-gray-600"
    }, props.checked ? checkedChildren : unCheckedChildren)
  ]);
};
export { Slider };

// Pagination wrapper for Ant Design compatibility
export const Pagination = (props: any) => {
  return React.createElement(ShadcnPagination, props);
};

// Layout Components
export const Divider = ({ type = 'horizontal', ...props }: any) => {
  const orientation = type === 'vertical' ? 'vertical' : 'horizontal';
  return React.createElement(ShadcnSeparator, { orientation, ...props });
};

// Enhanced Modal with Ant Design prop compatibility
export const Modal = Object.assign(
  ({
    open,
    visible,
    onCancel,
    onOk,
    title,
    children,
    footer,
    width,
    isOpen,
    onClose,
    size,
    ...props
  }: any) => {
    const isModalOpen = open || visible || isOpen;
    const handleClose = onCancel || onClose;

    return React.createElement(ShadcnDialog, {
      open: isModalOpen,
      onOpenChange: (open: boolean) => !open && handleClose?.(),
      ...props,
      children: React.createElement(DialogContent, {
        className: cn("max-w-lg", size === 'large' && "max-w-4xl"),
        style: width ? { width } : undefined,
        children: [
          title && React.createElement(DialogHeader, { key: 'header' },
            React.createElement(DialogTitle, {}, title)
          ),
          React.createElement('div', { key: 'body', className: "py-4" }, children),
          footer && React.createElement(DialogFooter, {
            key: 'footer',
            className: "flex justify-end space-x-2"
          }, footer)
        ].filter(Boolean)
      })
    });
  },
  {
    Header: ({ children, onClose, ...props }: any) => {
      return React.createElement(DialogHeader, { ...props }, [
        children,
        onClose && React.createElement('button', {
          key: 'close',
          onClick: onClose,
          className: "absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100"
        }, React.createElement(XIcon, { className: "h-4 w-4" }))
      ]);
    },
    Title: ({ children, ...props }: any) => {
      return React.createElement(DialogTitle, props, children);
    },
    Body: ({ children, ...props }: any) => {
      return React.createElement('div', { classUsername: "py-4", ...props }, children);
    },
    Footer: ({ children, ...props }: any) => {
      return React.createElement(DialogFooter, { classUsername: "flex justify-end space-x-2", ...props }, children);
    },
    confirm: ({ title, content, onOk, onCancel, okText = 'OK', cancelText = 'Cancel' }: any) => {
      const confirmed = window.confirm(`${title}\n\n${content}`);
      if (confirmed) {
        onOk?.();
      } else {
        onCancel?.();
      }
    }
  }
);

export const Drawer = ShadcnSheet;

// Navigation Components
// Enhanced Tabs with Ant Design prop compatibility
export const Tabs = Object.assign(
  ({ activeKey, defaultActiveKey, onChange, children, ...props }: any) => {
    return React.createElement(ShadcnTabs, {
      value: activeKey || defaultActiveKey,
      onValueChange: onChange,
      ...props
    }, children);
  },
  {
    TabPane: ({ tab, key: tabKey, children, ...props }: any) => {
      return React.createElement('div', {
        'data-tab': tabKey,
        'data-label': tab,
        ...props
      }, children);
    }
  }
);

// TabPane component for Ant Design compatibility
export const TabPane = ({ tab, key: tabKey, children, ...props }: any) => {
  return React.createElement('div', {
    'data-tab': tabKey,
    'data-label': tab,
    ...props
  }, children);
};
export const Breadcrumb = ShadcnBreadcrumb;
// Enhanced Dropdown with Ant Design prop compatibility
export const Dropdown = ({ overlay, trigger = ['hover'], children, ...props }: any) => {
  return React.createElement(ShadcnDropdownMenu, props, [
    React.createElement(DropdownMenuTrigger, { key: 'trigger', asChild: true }, children),
    React.createElement(DropdownMenuContent, { key: 'content' }, overlay)
  ]);
};

// Feedback Components - Ant Design compatible Progress
export const Progress = ({ percent = 0, strokeColor, size, format, type, trailColor, ...props }: any) => {
  // Handle different size formats - map antd sizes to shadcn sizes
  const normalizedSize = typeof size === 'number' ? 'default' : size;
  const sizeClass = normalizedSize === 'small' ? 'sm' : normalizedSize === 'large' ? 'lg' : 'default';

  // Ensure we use valid shadcn size values
  const validSize = ['sm', 'default', 'lg'].includes(sizeClass) ? sizeClass : 'default';

  if (type === 'circle') {
    // Circle progress
    const radius = 45;
    const circumference = 2 * Math.PI * radius;
    const strokeDasharray = circumference;
    const strokeDashoffset = circumference - (percent / 100) * circumference;

    // Filter out Ant Design specific props
    const { showInfo, status, strokeLinecap, strokeWidth, success, ...domProps } = props;

    return React.createElement('div', {
      classUsername: "inline-flex items-center justify-center relative",
      style: { width: typeof size === 'number' ? size : 100, height: typeof size === 'number' ? size : 100 },
      ...domProps
    }, [
      React.createElement('svg', {
        key: 'svg',
        width: typeof size === 'number' ? size : 100,
        height: typeof size === 'number' ? size : 100,
        classUsername: "transform -rotate-90"
      }, [
        React.createElement('circle', {
          key: 'bg',
          cx: "50%",
          cy: "50%",
          r: radius,
          stroke: "#f3f4f6",
          strokeWidth: "8",
          fill: "transparent"
        }),
        React.createElement('circle', {
          key: 'progress',
          cx: "50%",
          cy: "50%",
          r: radius,
          stroke: strokeColor || "#3b82f6",
          strokeWidth: "8",
          fill: "transparent",
          strokeDasharray,
          strokeDashoffset,
          style: { transition: 'stroke-dashoffset 0.3s ease' }
        })
      ]),
      format && React.createElement('div', {
        key: 'text',
        classUsername: "absolute text-sm font-medium"
      }, format(percent))
    ]);
  }

  // Line progress (default) - use shadcn Progress with converted props
  // Filter out Ant Design specific props for linear progress
  const { showInfo, status, strokeLinecap, strokeWidth, success, ...linearDomProps } = props;

  return React.createElement(ShadcnProgress, {
    value: percent,
    max: 100,
    size: validSize,
    classUsername: props.className,
    ...linearDomProps
  });
};

// Enhanced Tooltip with Ant Design prop compatibility
export const Tooltip = ({ title, children, ...props }: any) => {
  return React.createElement(TooltipProvider, { children:
    React.createElement(ShadcnTooltip, props, [
      React.createElement(TooltipTrigger, { key: 'trigger', asChild: true }, children),
      React.createElement(TooltipContent, { key: 'content' }, title)
    ])
  });
};

export const Popover = ShadcnPopover;

// Enhanced Collapse with Ant Design prop compatibility
export const Collapse = Object.assign(
  ({ accordion, items, children, ...props }: any) => {
    if (items) {
      // New API with items prop
      return React.createElement(ShadcnAccordion, {
        type: accordion ? "single" : "multiple",
        collapsible: true,
        ...props
      }, items.map((item: any, index: number) =>
        React.createElement(AccordionItem, {
          key: item.key || index,
          value: String(item.key || index),
          children: [
            React.createElement(AccordionTrigger, { key: 'trigger', children: item.label }),
            React.createElement(AccordionContent, { key: 'content', children: item.children })
          ]
        })
      ));
    }

    // Legacy API with children
    return React.createElement(ShadcnAccordion, {
      type: accordion ? "single" : "multiple",
      collapsible: true,
      ...props
    }, children);
  },
  {
    Panel: ({ header, children, key, ...props }: any) => {
      return React.createElement('div', {
        classUsername: "border border-gray-200 rounded mb-2",
        'data-key': key,
        ...props
      }, [
        React.createElement('div', {
          key: 'header',
          classUsername: "px-4 py-3 bg-gray-50 border-b border-gray-200 cursor-pointer font-medium"
        }, header),
        React.createElement('div', {
          key: 'content',
          classUsername: "px-4 py-3"
        }, children)
      ]);
    }
  }
);

// ===== MISSING COMPONENT PLACEHOLDERS =====

// Typography components
export const Typography = {
  Title: ({ children, level = 1, ...props }: any) => {
    const Tag = `h${level}` as keyof JSX.IntrinsicElements;
    const className = cn(
      "font-semibold",
      level === 1 && "text-3xl",
      level === 2 && "text-2xl",
      level === 3 && "text-xl"
    );
    return React.createElement(Tag, { className, ...props }, children);
  },
  Text: ({ children, strong, delete: deleteText, ...props }: any) => {
    const className = cn(
      strong && "font-bold",
      deleteText && "line-through",
      props.className
    );
    return React.createElement('span', { ...props, className }, children);
  },
  Paragraph: ({ children, ...props }: any) => React.createElement('p', props, children)
};

// Layout components
export const Space = Object.assign(
  ({ children, direction = 'horizontal', size = 'small', wrap, ...props }: any) => {
    const className = cn(
      "flex",
      direction === 'vertical' ? "flex-col" : "flex-row",
      "gap-2",
      wrap && "flex-wrap"
    );
    return React.createElement('div', { className, ...props }, children);
  },
  {
    Compact: ({ children, ...props }: any) => {
      return React.createElement('div', {
        classUsername: "flex",
        ...props
      }, children);
    }
  }
);

export const Row = ({ children, gutter, ...props }: any) => {
  const className = cn("flex flex-wrap", gutter && `gap-${gutter}`);
  return React.createElement('div', { className, ...props }, children);
};

export const Col = ({ children, span, ...props }: any) => {
  const className = cn(span && `flex-${span}`);
  return React.createElement('div', { className, ...props }, children);
};

export const TextArea = TextAreaComponent;

// InputNumber component
export const InputNumber = ({ min, max, step = 1, onChange, ...props }: any) => {
  return React.createElement(ShadcnInput, {
    type: "number",
    min,
    max,
    step,
    onChange: (e: any) => onChange?.(parseFloat(e.target.value) || 0),
    ...props
  });
};

// Date/Time components (placeholder - would need proper implementation)
export const DatePicker = Object.assign(
  ({ onChange, ...props }: any) => {
    return React.createElement(ShadcnInput, {
      type: "date",
      onChange: (e: any) => onChange?.(e.target.value),
      ...props
    });
  },
  {
    RangePicker: ({ onChange, ...props }: any) => {
      return React.createElement('div', { classUsername: "flex space-x-2" }, [
        React.createElement(ShadcnInput, {
          key: 'start',
          type: "date",
          placeholder: "Start date",
          onChange: (e: any) => onChange?.([e.target.value, null]),
          ...props
        }),
        React.createElement(ShadcnInput, {
          key: 'end',
          type: "date",
          placeholder: "End date",
          onChange: (e: any) => onChange?.([null, e.target.value]),
          ...props
        })
      ]);
    }
  }
);

export const TimePicker = ({ onChange, ...props }: any) => {
  return React.createElement(ShadcnInput, {
    type: "time",
    onChange: (e: any) => onChange?.(e.target.value),
    ...props
  });
};

// Other components (placeholders)
export const Upload = Object.assign(
  ({ children, ...props }: any) => {
    return React.createElement('div', {
      classUsername: "border-2 border-dashed border-gray-300 rounded-lg p-4 text-center cursor-pointer hover:border-gray-400",
      ...props
    }, [
      React.createElement(UploadLucide, {
        key: 'icon',
        className: "mx-auto h-8 w-8 text-gray-400 mb-2"
      }),
      children || "Click to upload"
    ]);
  },
  {
    Dragger: ({ children, ...props }: any) => {
      return React.createElement('div', {
        classUsername: "border-2 border-dashed border-blue-300 rounded-lg p-8 text-center cursor-pointer hover:border-blue-400 bg-blue-50 hover:bg-blue-100 transition-colors",
        ...props
      }, [
        React.createElement(UploadLucide, {
          key: 'icon',
          className: "mx-auto h-12 w-12 text-blue-400 mb-4"
        }),
        children || React.createElement('div', { key: 'text' }, [
          React.createElement('p', { key: 'title', className: "text-lg font-medium text-gray-900" }, "Drag & drop files here"),
          React.createElement('p', { key: 'subtitle', className: "text-sm text-gray-500 mt-1" }, "or click to browse")
        ])
      ]);
    }
  }
);

export const Spin = ({ children, spinning = false, ...props }: any) => {
  return React.createElement('div', { className: "relative", ...props }, [
    spinning && React.createElement('div', {
      key: 'spinner',
      className: "absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-10"
    }, React.createElement(Loader2, { className: "h-6 w-6 animate-spin" })),
    children
  ].filter(Boolean));
};

// Enhanced Tag with Ant Design prop compatibility
export const Tag = ({ size, color, icon, children, ...props }: any) => {
  const sizeClasses: { [key: string]: string } = {
    small: 'text-xs px-1 py-0.5',
    default: 'text-sm px-2 py-1',
    large: 'text-base px-3 py-1.5'
  };

  const colorClasses: { [key: string]: string } = {
    red: 'bg-red-100 text-red-800 border-red-200',
    orange: 'bg-orange-100 text-orange-800 border-orange-200',
    yellow: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    green: 'bg-green-100 text-green-800 border-green-200',
    blue: 'bg-blue-100 text-blue-800 border-blue-200',
    purple: 'bg-purple-100 text-purple-800 border-purple-200',
    gray: 'bg-gray-100 text-gray-800 border-gray-200'
  };

  const sizeClass = sizeClasses[size as string] || sizeClasses.default;
  const colorClass = colorClasses[color as string] || 'bg-gray-100 text-gray-800 border-gray-200';

  return React.createElement('span', {
    className: cn("inline-flex items-center rounded border", sizeClass, colorClass),
    ...props
  }, [
    icon && React.createElement('span', { key: 'icon', className: "mr-1" }, icon),
    children
  ].filter(Boolean));
};
export const Rate = ({ value = 0, ...props }: any) => {
  return React.createElement('div', { classUsername: "flex", ...props },
    [1, 2, 3, 4, 5].map((star) =>
      React.createElement(StarIcon, {
        key: star,
        className: cn("h-4 w-4", star <= value ? "fill-yellow-400 text-yellow-400" : "text-gray-300")
      })
    )
  );
};

// Message and notification functions
export const message = {
  success: (content: string) => console.log('Success:', content),
  error: (content: string) => console.error('Error:', content),
  warning: (content: string) => console.warn('Warning:', content),
  info: (content: string) => console.info('Info:', content)
};

export const notification = message;

// Layout Components
export const Layout = Object.assign(
  ({ children, className, ...props }: any) => {
    return React.createElement('div', {
      classUsername: cn("min-h-screen flex flex-col", className),
      ...props
    }, children);
  },
  {
    Header: ({ children, className, ...props }: any) => {
      return React.createElement('header', {
        classUsername: cn("bg-white border-b border-gray-200 px-6 py-4", className),
        ...props
      }, children);
    },
    Footer: ({ children, className, ...props }: any) => {
      return React.createElement('footer', {
        classUsername: cn("bg-gray-50 border-t border-gray-200 px-6 py-4 mt-auto", className),
        ...props
      }, children);
    },
    Content: ({ children, className, ...props }: any) => {
      return React.createElement('main', {
        classUsername: cn("flex-1 p-6", className),
        ...props
      }, children);
    },
    Sider: ({ children, collapsed, width = 256, className, collapsible, trigger, reverseArrow, ...props }: any) => {
      // Filter out Ant Design specific props that shouldn't go to DOM
      const { onCollapse, onBreakpoint, breakpoint, ...domProps } = props;

      return React.createElement('aside', {
        classUsername: cn("bg-gray-50 border-r border-gray-200 transition-all duration-300", className),
        style: { width: collapsed ? 80 : width },
        ...domProps
      }, children);
    }
  }
);

// Export Layout subcomponents separately for direct import
export const Header = Layout.Header;
export const Sider = Layout.Sider;
export const Content = Layout.Content;

// Menu Components
export const Menu = Object.assign(
  ({ children, mode = 'vertical', theme = 'light', selectedKeys, inlineCollapsed, ...props }: any) => {
    const className = cn(
      "flex",
      mode === 'horizontal' ? "flex-row space-x-1" : "flex-col space-y-1",
      theme === 'dark' ? "bg-gray-800 text-white" : "bg-white"
    );
    // Filter out Ant Design specific props that shouldn't go to DOM
    const { onSelect, onOpenChange, openKeys, defaultOpenKeys, defaultSelectedKeys, ...domProps } = props;
    return React.createElement('nav', { className, ...domProps }, children);
  },
  {
    Item: ({ children, key, icon, ...props }: any) => {
      return React.createElement('div', {
        classUsername: "flex items-center px-3 py-2 rounded hover:bg-gray-100 cursor-pointer",
        'data-key': key,
        ...props
      }, [
        icon && React.createElement('span', { key: 'icon', classUsername: "mr-2" }, icon),
        children
      ]);
    },
    SubMenu: ({ children, title, icon, ...props }: any) => {
      return React.createElement('div', { classUsername: "space-y-1", ...props }, [
        React.createElement('div', {
          key: 'title',
          classUsername: "flex items-center px-3 py-2 font-medium text-gray-700"
        }, [
          icon && React.createElement('span', { key: 'icon', classUsername: "mr-2" }, icon),
          title
        ]),
        React.createElement('div', { key: 'items', classUsername: "ml-4 space-y-1" }, children)
      ]);
    },
    ItemGroup: ({ children, title, ...props }: any) => {
      return React.createElement('div', { classUsername: "space-y-1", ...props }, [
        title && React.createElement('div', {
          key: 'title',
          classUsername: "px-3 py-1 text-xs font-medium text-gray-500 uppercase"
        }, title),
        children
      ]);
    },
    Divider: () => React.createElement('hr', { classUsername: "my-2 border-gray-200" })
  }
);

// Missing components
export const ConfigProvider = ({ children, ...props }: any) => {
  return React.createElement('div', props, children);
};

export const App = Object.assign(
  ({ children, ...props }: any) => {
    return React.createElement('div', props, children);
  },
  {
    useApp: () => ({
      message,
      notification,
      modal: Modal
    })
  }
);

export const Empty = Object.assign(
  ({ description = "No data", image, ...props }: any) => {
    return React.createElement('div', {
      classUsername: "flex flex-col items-center justify-center py-8 text-gray-500",
      ...props
    }, [
      image || React.createElement('div', { key: 'icon', classUsername: "text-4xl mb-2" }, "📭"),
      React.createElement('p', { key: 'desc' }, description)
    ]);
  },
  {
    PRESENTED_IMAGE_SIMPLE: React.createElement('div', { classUsername: "text-2xl mb-2" }, "📄")
  }
);

export const Statistic = ({ title, value, ...props }: any) => {
  return React.createElement('div', { classUsername: "text-center", ...props }, [
    React.createElement('div', { key: 'title', classUsername: "text-sm text-gray-500" }, title),
    React.createElement('div', { key: 'value', classUsername: "text-2xl font-bold" }, value)
  ]);
};

export const Popconfirm = ({ children, title, onConfirm, ...props }: any) => {
  const handleClick = () => {
    if (window.confirm(title)) {
      onConfirm?.();
    }
  };
  return React.createElement('span', { onClick: handleClick, ...props }, children);
};

export const Result = ({ status = 'info', title, subTitle, extra, icon, ...props }: any) => {
  const statusIcons: { [key: string]: any } = {
    success: CheckCircle,
    error: XCircle,
    info: Info,
    warning: AlertCircle,
    '404': AlertCircle,
    '403': Shield,
    '500': XCircle
  };

  const statusColors: { [key: string]: string } = {
    success: 'text-green-500',
    error: 'text-red-500',
    info: 'text-blue-500',
    warning: 'text-yellow-500',
    '404': 'text-gray-500',
    '403': 'text-red-500',
    '500': 'text-red-500'
  };

  const IconComponent = statusIcons[status as string] || Info;
  const colorClass = statusColors[status as string] || 'text-blue-500';

  return React.createElement('div', {
    classUsername: "flex flex-col items-center justify-center py-12 text-center",
    ...props
  }, [
    React.createElement('div', { key: 'icon', classUsername: `mb-4 ${colorClass}` },
      icon || React.createElement(IconComponent, { classUsername: "h-16 w-16" })
    ),
    title && React.createElement('h3', { key: 'title', classUsername: "text-xl font-semibold mb-2" }, title),
    subTitle && React.createElement('p', { key: 'subtitle', classUsername: "text-gray-600 mb-4" }, subTitle),
    extra && React.createElement('div', { key: 'extra' }, extra)
  ].filter(Boolean));
};

// Sub-components and Advanced Components
export const Search = ({ onSearch, ...props }: any) => {
  return React.createElement(ShadcnInput, {
    type: "search",
    onChange: (e: any) => onSearch?.(e.target.value),
    ...props
  });
};

export const Option = ({ children, value, ...props }: any) => {
  return React.createElement('option', { value, ...props }, children);
};

// TypeScript types for compatibility
export type ColumnsType<T = any> = Array<{
  key?: string;
  title?: string;
  dataIndex?: string;
  render?: (value: any, record: T, index: number) => React.ReactNode;
  width?: number | string;
  fixed?: 'left' | 'right';
  sorter?: boolean | ((a: T, b: T) => number);
  filters?: Array<{ text: string; value: any }>;
  onFilter?: (value: any, record: T) => boolean;
  align?: 'left' | 'center' | 'right';
}>;

// Icon exports (using Lucide icons)
export const MenuUnfoldOutlined = MenuIcon;

// List Component
export const List = Object.assign(
  ({ dataSource = [], renderItem, loading, ...props }: any) => {
    if (loading) {
      return React.createElement('div', { classUsername: "flex justify-center p-4" },
        React.createElement(Loader2, { className: "h-6 w-6 animate-spin" })
      );
    }

    return React.createElement('div', { classUsername: "space-y-2", ...props },
      dataSource.map((item: any, index: number) =>
        React.createElement('div', { key: index, classUsername: "border-b border-gray-100 pb-2" },
          renderItem ? renderItem(item, index) : item
        )
      )
    );
  },
  {
    Item: Object.assign(
      ({ children, actions, ...props }: any) => {
        return React.createElement('div', {
          classUsername: "flex items-center justify-between p-3 hover:bg-gray-50",
          ...props
        }, [
          React.createElement('div', { key: 'content', classUsername: "flex-1" }, children),
          actions && React.createElement('div', { key: 'actions', classUsername: "flex space-x-2" }, actions)
        ]);
      },
      {
        Meta: ({ avatar, title, description, ...props }: any) => {
          return React.createElement('div', { classUsername: "flex items-start space-x-3", ...props }, [
            avatar && React.createElement('div', { key: 'avatar' }, avatar),
            React.createElement('div', { key: 'content' }, [
              title && React.createElement('div', { key: 'title', classUsername: "font-medium" }, title),
              description && React.createElement('div', { key: 'desc', classUsername: "text-sm text-gray-500" }, description)
            ])
          ]);
        }
      }
    )
  }
);

// Steps Component
export const Steps = Object.assign(
  ({ current = 0, children, direction = 'horizontal', ...props }: any) => {
    const className = cn(
      "flex",
      direction === 'vertical' ? "flex-col space-y-4" : "flex-row space-x-4"
    );

    return React.createElement('div', { className, ...props },
      React.Children.map(children, (child, index) =>
        React.cloneElement(child, {
          ...child.props,
          status: index < current ? 'finish' : index === current ? 'process' : 'wait',
          stepNumber: index + 1
        })
      )
    );
  },
  {
    Step: ({ title, description, status = 'wait', stepNumber, icon, ...props }: any) => {
      const statusColors: { [key: string]: string } = {
        wait: 'bg-gray-200 text-gray-500',
        process: 'bg-blue-500 text-white',
        finish: 'bg-green-500 text-white',
        error: 'bg-red-500 text-white'
      };

      return React.createElement('div', { classUsername: "flex items-center", ...props }, [
        React.createElement('div', {
          key: 'icon',
          classUsername: cn("w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium", statusColors[status as string])
        }, icon || stepNumber),
        React.createElement('div', { key: 'content', classUsername: "ml-3" }, [
          React.createElement('div', { key: 'title', classUsername: "font-medium" }, title),
          description && React.createElement('div', { key: 'desc', classUsername: "text-sm text-gray-500" }, description)
        ])
      ]);
    }
  }
);

// Radio Component
export const Radio = Object.assign(
  ({ children, ...props }: any) => {
    return React.createElement('label', { classUsername: "flex items-center space-x-2" }, [
      React.createElement('input', { key: 'input', type: 'radio', ...props }),
      React.createElement('span', { key: 'label' }, children)
    ]);
  },
  {
    Group: ({ children, value, onChange, ...props }: any) => {
      return React.createElement('div', { classUsername: "space-y-2", ...props },
        React.Children.map(children, (child) =>
          React.cloneElement(child, {
            ...child.props,
            checked: child.props.value === value,
            onChange: (e: any) => onChange?.(e.target.value)
          })
        )
      );
    },
    Button: ({ children, value, ...props }: any) => {
      return React.createElement('button', {
        classUsername: "px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500",
        type: "button",
        ...props
      }, children);
    }
  }
);
export const MenuFoldOutlined = MenuIcon;
export const UserOutlined = User;
export const DashboardOutlined = BarChart3;
export const TeamOutlined = Users;
export const BookOutlined = Book;
export const BarChartOutlined = BarChart3;
export const BellOutlined = Bell;
export const MessageOutlined = MessageCircle;
export const DollarOutlined = DollarSign;
export const SettingOutlined = Settings;
export const SafetyCertificateOutlined = Shield;
export const CalendarOutlined = CalendarIcon;
export const CreditCardOutlined = DollarSign;
export const MailOutlined = Mail;
export const ExclamationCircleOutlined = AlertCircle;
export const InboxOutlined = Download;
export const DownloadOutlined = Download;
export const BankOutlined = CreditCard;
export const FileTextOutlined = FileText;
export const SafetyOutlined = Shield;
export const DatabaseOutlined = Database;
export const RocketOutlined = Rocket;
export const CrownOutlined = Crown;
export const GiftOutlined = Gift;
export const BulbOutlined = Lightbulb;
export const ThunderboltOutlined = Zap;
export const EyeOutlined = Eye;
export const LineChartOutlined = TrendingUp;
export const ControlOutlined = Settings;
export const MonitorOutlined = Globe;

// Add missing icon exports
export const HeartOutlined = Heart;
export const ReadOutlined = BookOpen;
export const ClockCircleOutlined = Clock;
export const EditOutlined = Edit;
export const QuestionCircleOutlined = HelpCircle;
export const UserAddOutlined = UserPlus;
export const FolderOutlined = Folder;
export const PlusOutlined = Plus;
export const VideoCameraOutlined = Video;
export const TrophyOutlined = Trophy;
export const StarOutlined = StarIcon;
export const ArrowUpOutlined = ArrowUp;
export const ArrowDownOutlined = ArrowDown;
export const MinusOutlined = Minus;
export const SaveOutlined = Save;
export const CloseOutlined = XIcon;
export const PlayCircleOutlined = Play;
export const UploadOutlined = UploadLucide;
export const StarFilled = StarIcon;
export const RiseOutlined = TrendingUp;
export const FallOutlined = TrendingDown;

// Add missing icons that are causing "Cannot find name" errors (batch fix - only new ones)
export const UnorderedListOutlined = ListIcon;
export const AppstoreOutlined = Grid3X3;
export const ShoppingCartOutlined = ShoppingCart;
export const SearchOutlined = SearchIcon;
export const CheckOutlined = Check;
export const CheckCircleOutlined = CheckCircle;
export const ShareAltOutlined = Share2;
export const MoreOutlined = MoreHorizontal;
export const BookFilled = Book;
export const DeleteOutlined = Trash2;
// Note: BookOutlined, ClockCircleOutlined, StarOutlined, PlayCircleOutlined, CloseOutlined already exist above

// Add missing types
export interface MenuProps {
  items?: any[];
  onClick?: (info: any) => void;
  mode?: 'horizontal' | 'vertical' | 'inline';
  theme?: 'light' | 'dark';
  selectedKeys?: string[];
  openKeys?: string[];
  onOpenChange?: (openKeys: string[]) => void;
}

// Add missing components
export const Affix = ({ children, offsetTop, ...props }: any) => {
  const style = offsetTop ? { top: `${offsetTop}px` } : {};
  return React.createElement('div', {
    classUsername: "fixed z-50",
    style: { top: 0, ...style, ...props.style },
    ...props
  }, children);
};
// Timeline is defined later with enhanced functionality
export const Skeleton = ({ loading = false, children, ...props }: any) => {
  if (loading) {
    return React.createElement('div', { classUsername: "animate-pulse space-y-2", ...props }, [
      React.createElement('div', { key: '1', classUsername: "h-4 bg-gray-200 rounded w-3/4" }),
      React.createElement('div', { key: '2', classUsername: "h-4 bg-gray-200 rounded w-1/2" }),
      React.createElement('div', { key: '3', classUsername: "h-4 bg-gray-200 rounded w-5/6" })
    ]);
  }
  return children;
};
export const Image = ({ src, alt, ...props }: any) => React.createElement('img', { src, alt, classUsername: "max-w-full h-auto", ...props });
export const Descriptions = Object.assign(
  ({ children, ...props }: any) => React.createElement('div', { classUsername: "space-y-2", ...props }, children),
  {
    Item: ({ label, span, children, ...props }: any) => {
      return React.createElement('div', {
        classUsername: "flex justify-between py-1 border-b border-gray-100",
        ...props
      }, [
        React.createElement('dt', { key: 'label', classUsername: "font-medium text-gray-600" }, label),
        React.createElement('dd', { key: 'value', classUsername: "text-gray-900" }, children)
      ]);
    }
  }
);

// Card is already defined above
export const Flex = ({ children, justify, align, gap, wrap, vertical, className, ...props }: any) => {
  const flexClasses = [
    'flex',
    vertical ? 'flex-col' : 'flex-row',
    justify === 'center' ? 'justify-center' :
    justify === 'space-between' ? 'justify-between' :
    justify === 'space-around' ? 'justify-around' :
    justify === 'flex-end' ? 'justify-end' : 'justify-start',
    align === 'center' ? 'items-center' :
    align === 'flex-end' ? 'items-end' :
    align === 'stretch' ? 'items-stretch' : 'items-start',
    gap ? `gap-${gap}` : '',
    wrap ? 'flex-wrap' : '',
    className
  ].filter(Boolean).join(' ');

  return React.createElement('div', { classUsername: flexClasses, ...props }, children);
};

// Theme object for compatibility
export const theme = {
  useToken: () => ({
    token: {
      colorPrimary: '#1890ff',
      colorSuccess: '#52c41a',
      colorWarning: '#faad14',
      colorError: '#ff4d4f',
      colorInfo: '#1890ff',
      colorBgContainer: '#ffffff',
      colorBorder: '#d9d9d9',
      borderRadius: 6,
      fontSize: 14,
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
    }
  })
};

// Grid system
export const Grid = {
  useBreakpoint: () => ({
    xs: false,
    sm: false,
    md: true,
    lg: true,
    xl: true,
    xxl: true
  })
};

// Upload types for compatibility
export interface UploadFile {
  uid: string;
  name: string;
  status?: 'uploading' | 'done' | 'error' | 'removed';
  url?: string;
  thumbUrl?: string;
  size?: number;
  type?: string;
  percent?: number;
  originFileObj?: File;
  response?: any;
  error?: any;
  linkProps?: any;
  xhr?: any;
}

export interface UploadProps {
  accept?: string;
  action?: string;
  beforeUpload?: (file: File, fileList: File[]) => boolean | Promise<boolean>;
  customRequest?: (options: any) => void;
  data?: any;
  defaultFileList?: UploadFile[];
  directory?: boolean;
  disabled?: boolean;
  fileList?: UploadFile[];
  headers?: any;
  listType?: 'text' | 'picture' | 'picture-card';
  multiple?: boolean;
  name?: string;
  onChange?: (info: { file: UploadFile; fileList: UploadFile[] }) => void;
  onDrop?: (event: React.DragEvent<HTMLDivElement>) => void;
  onPreview?: (file: UploadFile) => void;
  onRemove?: (file: UploadFile) => boolean | Promise<boolean>;
  showUploadList?: boolean | any;
  supportServerRender?: boolean;
  withCredentials?: boolean;
  openFileDialogOnClick?: boolean;
  transformFile?: (file: File) => string | Blob | File | Promise<string | Blob | File>;
  children?: React.ReactNode;
}

// Enhanced Nav with Ant Design prop compatibility
export const Nav = Object.assign(
  ({ children, variant = 'tabs', className, ...props }: any) => {
    const navClasses = cn(
      "flex",
      variant === 'tabs' ? "border-b border-gray-200" : "",
      variant === 'pills' ? "space-x-1" : "",
      className
    );

    return React.createElement('nav', {
      classUsername: navClasses,
      ...props
    }, children);
  },
  {
    Item: ({ children, className, ...props }: any) => {
      return React.createElement('div', {
        classUsername: cn("nav-item", className),
        ...props
      }, children);
    },
    Link: ({ children, eventKey, active, className, ...props }: any) => {
      return React.createElement('button', {
        classUsername: cn(
          "px-4 py-2 text-sm font-medium transition-colors",
          active ? "text-blue-600 border-b-2 border-blue-600" : "text-gray-500 hover:text-gray-700",
          className
        ),
        'data-event-key': eventKey,
        ...props
      }, children);
    }
  }
);

// Enhanced Timeline with Ant Design prop compatibility
export const Timeline = Object.assign(
  ({ children, className, ...props }: any) => {
    return React.createElement('div', {
      classUsername: cn("relative", className),
      ...props
    }, children);
  },
  {
    Item: ({ children, color = 'blue', className, ...props }: any) => {
      const colorClasses = {
        blue: 'bg-blue-500',
        green: 'bg-green-500',
        red: 'bg-red-500',
        gray: 'bg-gray-500'
      };

      return React.createElement('div', {
        classUsername: cn("relative flex items-start pb-4", className),
        ...props
      }, [
        React.createElement('div', {
          key: 'dot',
          classUsername: cn("w-3 h-3 rounded-full mt-1.5 mr-4", (colorClasses as any)[color] || colorClasses.blue)
        }),
        React.createElement('div', {
          key: 'content',
          classUsername: "flex-1"
        }, children)
      ]);
    }
  }
);

// Enhanced Calendar with Ant Design prop compatibility
export const Calendar = ({ value, onSelect, dateCellRender, monthCellRender, mode, className, ...props }: any) => {
  // Simple calendar wrapper that accepts Ant Design props but renders a basic calendar
  return React.createElement('div', {
    classUsername: cn("border rounded-lg p-4", className),
    ...props
  }, [
    React.createElement('div', {
      key: 'calendar-header',
      classUsername: "text-center mb-4 font-semibold"
    }, value ? value.format('MMMM YYYY') : 'Calendar'),
    React.createElement('div', {
      key: 'calendar-body',
      classUsername: "text-center text-gray-500"
    }, 'Calendar component - replace with proper implementation'),
    React.createElement('button', {
      key: 'calendar-button',
      classUsername: "mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",
      onClick: () => onSelect && onSelect(value)
    }, 'Select Date')
  ]);
};

