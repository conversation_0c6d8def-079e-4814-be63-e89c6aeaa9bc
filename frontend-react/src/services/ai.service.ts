import { typeSafeApi } from '../utils/typeSafeApi';
import { z } from 'zod';
import { withRetry, handleError } from '../utils/errorHandling';

// AI Service Types
export interface AIMessage {
  id: string;
  type: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  context?: AIContext;
  metadata?: {
    model?: string;
    tokens?: number;
    confidence?: number;
    sources?: string[];
  };
}

export interface AIContext {
  courseId?: string;
  lessonId?: string;
  pageUrl?: string;
  selectedText?: string;
  pageTitle?: string;
  userRole?: string;
  conversationHistory?: AIMessage[];
}

export interface AIRequest {
  message: string;
  context?: AIContext;
  options?: {
    temperature?: number;
    maxTokens?: number;
    model?: string;
    includeHistory?: boolean;
  };
}

export interface AIResponse {
  id: string;
  content: string;
  timestamp: string;
  metadata: {
    model: string;
    tokens: number;
    confidence: number;
    processingTime: number;
    sources?: string[];
  };
  suggestions?: string[];
  relatedTopics?: string[];
}

// Validation schemas
const aiMessageSchema = z.object({
  id: z.string(),
  type: z.enum(['user', 'assistant', 'system']),
  content: z.string(),
  timestamp: z.string(),
  context: z.object({
    courseId: z.string().optional(),
    lessonId: z.string().optional(),
    pageUrl: z.string().optional(),
    selectedText: z.string().optional(),
    pageTitle: z.string().optional(),
    userRole: z.string().optional(),
  }).optional(),
  metadata: z.object({
    model: z.string().optional(),
    tokens: z.number().optional(),
    confidence: z.number().optional(),
    sources: z.array(z.string()).optional(),
  }).optional(),
});

const aiResponseSchema = z.object({
  id: z.string(),
  content: z.string(),
  timestamp: z.string(),
  metadata: z.object({
    model: z.string(),
    tokens: z.number(),
    confidence: z.number(),
    processingTime: z.number(),
    sources: z.array(z.string()).optional(),
  }),
  suggestions: z.array(z.string()).optional(),
  relatedTopics: z.array(z.string()).optional(),
});

const conversationSchema = z.object({
  id: z.string(),
  userId: z.string(),
  title: z.string(),
  messages: z.array(aiMessageSchema),
  createdAt: z.string(),
  updatedAt: z.string(),
  metadata: z.object({
    totalTokens: z.number(),
    averageConfidence: z.number(),
    topics: z.array(z.string()),
  }).optional(),
});

/**
 * AI Service for handling AI assistant interactions
 */
class AIService {
  private readonly baseURL = '/ai';

  /**
   * Send a message to the AI assistant
   */
  async sendMessage(request: AIRequest): Promise<AIResponse> {
    return withRetry(async () => {
      try {
        const response = await typeSafeApi.post(
          `${this.baseURL}/chat`,
          {
            message: request.message,
            context: request.context,
            options: {
              temperature: 0.7,
              maxTokens: 1000,
              model: 'gpt-4',
              includeHistory: true,
              ...request.options,
            },
          },
          aiResponseSchema
        );

        return response;
      } catch (error) {
        throw handleError(error, {
          context: { method: 'sendMessage', request },
          showToast: false,
        });
      }
    }, {
      maxAttempts: 3,
      baseDelay: 1000,
      retryCondition: (error) => (error.status && error.status >= 500) || error.code === 'NETWORK_ERROR',
    });
  }

  /**
   * Get conversation history
   */
  async getConversations(userId: string, limit = 10): Promise<any[]> {
    return withRetry(async () => {
      try {
        const response = await typeSafeApi.get(
          `${this.baseURL}/conversations?userId=${userId}&limit=${limit}`,
          z.array(conversationSchema)
        );

        return response;
      } catch (error) {
        throw handleError(error, {
          context: { method: 'getConversations', userId, limit },
          showToast: false,
        });
      }
    });
  }

  /**
   * Save conversation
   */
  async saveConversation(conversation: {
    userId: string;
    title: string;
    messages: AIMessage[];
  }): Promise<string> {
    return withRetry(async () => {
      try {
        const response = await typeSafeApi.post(
          `${this.baseURL}/conversations`,
          conversation,
          z.object({ id: z.string() })
        );

        return response.id;
      } catch (error) {
        throw handleError(error, {
          context: { method: 'saveConversation', conversation },
          showToast: false,
        });
      }
    });
  }

  /**
   * Get AI suggestions based on context
   */
  async getSuggestions(context: AIContext): Promise<string[]> {
    return withRetry(async () => {
      try {
        const response = await typeSafeApi.post(
          `${this.baseURL}/suggestions`,
          { context },
          z.object({ suggestions: z.array(z.string()) })
        );

        return response.suggestions;
      } catch (error) {
        console.warn('Failed to get AI suggestions:', error);
        return this.getFallbackSuggestions(context);
      }
    });
  }

  /**
   * Submit feedback for AI response
   */
  async submitFeedback(messageId: string, feedback: {
    rating: 'positive' | 'negative';
    comment?: string;
    category?: string;
  }): Promise<void> {
    return withRetry(async () => {
      try {
        await typeSafeApi.post(
          `${this.baseURL}/feedback`,
          {
            messageId,
            ...feedback,
          },
          z.object({ success: z.boolean() })
        );
      } catch (error) {
        console.warn('Failed to submit feedback:', error);
        // Don't throw error for feedback submission failures
      }
    });
  }

  /**
   * Get AI analytics and usage stats
   */
  async getAnalytics(userId: string, timeframe = '30d'): Promise<{
    totalMessages: number;
    averageResponseTime: number;
    topTopics: string[];
    satisfactionScore: number;
  }> {
    return withRetry(async () => {
      try {
        const response = await typeSafeApi.get(
          `${this.baseURL}/analytics?userId=${userId}&timeframe=${timeframe}`,
          z.object({
            totalMessages: z.number(),
            averageResponseTime: z.number(),
            topTopics: z.array(z.string()),
            satisfactionScore: z.number(),
          })
        );

        return response;
      } catch (error) {
        throw handleError(error, {
          context: { method: 'getAnalytics', userId, timeframe },
          showToast: false,
        });
      }
    });
  }

  /**
   * Generate study plan using AI
   */
  async generateStudyPlan(request: {
    subject: string;
    level: string;
    timeAvailable: number;
    goals: string[];
    currentKnowledge?: string;
  }): Promise<{
    plan: {
      week: number;
      topics: string[];
      activities: string[];
      estimatedHours: number;
    }[];
    resources: string[];
    milestones: string[];
  }> {
    return withRetry(async () => {
      try {
        const response = await typeSafeApi.post(
          `${this.baseURL}/study-plan`,
          request,
          z.object({
            plan: z.array(z.object({
              week: z.number(),
              topics: z.array(z.string()),
              activities: z.array(z.string()),
              estimatedHours: z.number(),
            })),
            resources: z.array(z.string()),
            milestones: z.array(z.string()),
          })
        );

        return response;
      } catch (error) {
        throw handleError(error, {
          context: { method: 'generateStudyPlan', request },
          showToast: false,
        });
      }
    });
  }

  /**
   * Fallback suggestions when API is unavailable
   */
  private getFallbackSuggestions(context: AIContext): string[] {
    const suggestions = [
      'Explain this concept in simple terms',
      'Give me practice questions',
      'Suggest study strategies',
      'Help me understand this better',
    ];

    if (context.selectedText) {
      suggestions.unshift(`Explain: "${context.selectedText}"`);
    }

    if (context.courseId) {
      suggestions.push('What should I study next in this course?');
    }

    return suggestions.slice(0, 4);
  }

  /**
   * Mock AI response for development/fallback
   */
  generateMockResponse(message: string, context?: AIContext): AIResponse {
    const responses = {
      explain: "I'd be happy to explain this concept! Let me break it down into simple terms for you. This topic involves several key components that work together...",
      practice: "Here are some practice questions to help you master this topic:\n\n1. What are the main principles?\n2. How would you apply this in a real scenario?\n3. What are the potential challenges?",
      strategies: "Here are some effective study strategies I recommend:\n\n• Use active recall techniques\n• Create mind maps\n• Practice spaced repetition\n• Form study groups",
      code: "Let me help you understand this code step by step:\n\n1. First, we initialize the variables\n2. Then we process the data\n3. Finally, we return the result",
      default: "I understand you're asking about this topic. Based on the context, I can provide you with a comprehensive explanation and some helpful resources to deepen your understanding.",
    };

    const lowerMessage = message.toLowerCase();
    let content = responses.default;

    if (lowerMessage.includes('explain')) content = responses.explain;
    else if (lowerMessage.includes('practice') || lowerMessage.includes('question')) content = responses.practice;
    else if (lowerMessage.includes('study') || lowerMessage.includes('strategy')) content = responses.strategies;
    else if (lowerMessage.includes('code') || lowerMessage.includes('programming')) content = responses.code;

    return {
      id: Date.now().toString(),
      content,
      timestamp: new Date().toISOString(),
      metadata: {
        model: 'mock-ai',
        tokens: content.length,
        confidence: 0.85,
        processingTime: 1200,
      },
      suggestions: this.getFallbackSuggestions(context || {}),
      relatedTopics: ['Study Tips', 'Learning Strategies', 'Academic Success'],
    };
  }
}

// Export singleton instance
export const aiService = new AIService();
export default aiService;
