/**
 * Media Preview Service
 * Handles localStorage-based media previews for course creation
 * Uses base64 encoding for reliable cross-browser storage
 */

export interface MediaPreview {
  id: string;
  filename: string;
  mimeType: string;
  size: number;
  blobUrl: string;
  timestamp: number;
  type: 'image' | 'video';
}

class MediaPreviewService {
  private readonly STORAGE_KEY = 'brainmaker-media-previews';
  private readonly MAX_STORAGE_SIZE = 1024 * 1024; // 1MB limit for metadata only
  private readonly CLEANUP_THRESHOLD = 24 * 60 * 60 * 1000; // 24 hours
  private readonly blobCache = new Map<string, string>(); // In-memory blob URL cache

  /**
   * Store media file for preview using blob URLs
   */
  public async storeMediaPreview(file: File): Promise<MediaPreview> {
    try {
      console.log('📦 Creating media preview:', file.name, this.formatFileSize(file.size));

      // Create blob URL for immediate preview
      const blobUrl = URL.createObjectURL(file);

      const preview: MediaPreview = {
        id: this.generateId(),
        filename: file.name,
        mimeType: file.type,
        size: file.size,
        blobUrl,
        timestamp: Date.now(),
        type: file.type.startsWith('image/') ? 'image' : 'video'
      };

      // Store blob URL in memory cache
      this.blobCache.set(preview.id, blobUrl);

      // Store only metadata in localStorage (no large base64 data)
      this.savePreviewMetadata(preview);

      console.log('✅ Media preview created:', preview.id);
      return preview;

    } catch (error) {
      console.error('❌ Failed to create media preview:', error);
      throw error;
    }
  }

  /**
   * Get media preview by ID
   */
  public getMediaPreview(id: string): MediaPreview | null {
    try {
      const previews = this.getAllPreviews();
      return previews.find(p => p.id === id) || null;
    } catch (error) {
      console.error('Failed to get media preview:', error);
      return null;
    }
  }

  /**
   * Get blob URL for preview display
   */
  public getPreviewUrl(id: string): string | null {
    // First check memory cache
    const cachedUrl = this.blobCache.get(id);
    if (cachedUrl) {
      return cachedUrl;
    }

    // If not in cache, check if metadata exists
    const preview = this.getMediaPreview(id);
    if (preview && preview.blobUrl) {
      // Re-add to cache if it exists
      this.blobCache.set(id, preview.blobUrl);
      return preview.blobUrl;
    }

    return null;
  }

  /**
   * Remove media preview and cleanup blob URL
   */
  public removeMediaPreview(id: string): void {
    try {
      // Get the preview to access blob URL
      const preview = this.getMediaPreview(id);

      // Cleanup blob URL
      if (preview && preview.blobUrl) {
        URL.revokeObjectURL(preview.blobUrl);
      }

      // Remove from memory cache
      this.blobCache.delete(id);

      // Remove from localStorage
      const previews = this.getAllPreviews();
      const filtered = previews.filter(p => p.id !== id);
      this.savePreviewsMetadata(filtered);

      console.log('🗑️ Media preview removed:', id);
    } catch (error) {
      console.error('Failed to remove media preview:', error);
    }
  }

  /**
   * Clear all previews and cleanup blob URLs
   */
  public clearAllPreviews(): void {
    try {
      // Get all previews to cleanup blob URLs
      const previews = this.getAllPreviews();

      // Cleanup all blob URLs
      previews.forEach(preview => {
        if (preview.blobUrl) {
          URL.revokeObjectURL(preview.blobUrl);
        }
      });

      // Clear memory cache
      this.blobCache.clear();

      // Clear localStorage
      localStorage.removeItem(this.STORAGE_KEY);

      console.log('🧹 All media previews cleared');
    } catch (error) {
      console.error('Failed to clear previews:', error);
    }
  }

  /**
   * Cleanup old previews and their blob URLs
   */
  public cleanupOldPreviews(): void {
    try {
      const previews = this.getAllPreviews();
      const now = Date.now();
      const oldPreviews = previews.filter(p =>
        (now - p.timestamp) >= this.CLEANUP_THRESHOLD
      );
      const validPreviews = previews.filter(p =>
        (now - p.timestamp) < this.CLEANUP_THRESHOLD
      );

      // Cleanup blob URLs for old previews
      oldPreviews.forEach(preview => {
        if (preview.blobUrl) {
          URL.revokeObjectURL(preview.blobUrl);
        }
        this.blobCache.delete(preview.id);
      });

      if (oldPreviews.length > 0) {
        this.savePreviewsMetadata(validPreviews);
        console.log(`🧹 Cleaned up ${oldPreviews.length} old previews`);
      }
    } catch (error) {
      console.error('Failed to cleanup old previews:', error);
    }
  }

  /**
   * Get storage usage info
   */
  public getStorageInfo(): { used: number; available: number; count: number } {
    try {
      const previews = this.getAllPreviews();
      const used = JSON.stringify(previews).length;
      const available = this.MAX_STORAGE_SIZE - used;
      
      return {
        used,
        available,
        count: previews.length
      };
    } catch (error) {
      return { used: 0, available: this.MAX_STORAGE_SIZE, count: 0 };
    }
  }

  // Private methods

  private savePreviewMetadata(preview: MediaPreview): void {
    const previews = this.getAllPreviews();
    previews.push(preview);
    this.savePreviewsMetadata(previews);
  }

  private savePreviewsMetadata(previews: MediaPreview[]): void {
    try {
      // Only store metadata, not blob URLs (they're in memory cache)
      const metadata = previews.map(p => ({
        id: p.id,
        filename: p.filename,
        mimeType: p.mimeType,
        size: p.size,
        timestamp: p.timestamp,
        type: p.type,
        blobUrl: '' // Don't store blob URL in localStorage
      }));

      const dataSize = JSON.stringify(metadata).length;
      if (dataSize > this.MAX_STORAGE_SIZE) {
        console.warn('Metadata storage limit exceeded, cleaning up old previews');
        this.cleanupOldPreviews();
        return;
      }

      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(metadata));
    } catch (error) {
      console.error('Failed to save preview metadata to localStorage:', error);
      throw error;
    }
  }

  private generateId(): string {
    return `preview_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getAllPreviews(): MediaPreview[] {
    try {
      const data = localStorage.getItem(this.STORAGE_KEY);
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error('Failed to parse previews from localStorage:', error);
      return [];
    }
  }



  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// Create singleton instance
const mediaPreviewService = new MediaPreviewService();

// Cleanup old previews on service initialization
mediaPreviewService.cleanupOldPreviews();

export default mediaPreviewService;
