import apiService from './api';

export interface EnrollmentStatus {
  hasAccess: boolean;
  enrollmentDate?: string;
  completionPercentage?: number;
  lastAccessedDate?: string;
  certificateUrl?: string;
}

export interface EnrollmentData {
  courseId: number;
  userId: number;
  enrollmentType: 'free' | 'paid' | 'subscription';
  paymentId?: string;
  orderId?: number;
}

/**
 * Service for handling course enrollment operations
 */
class EnrollmentService {
  private rootURL = '/enrollments';

  /**
   * Check if user has access to a course
   * @param courseId - The course ID
   * @param userId - The user ID (optional, uses current user if not provided)
   * @returns Promise with enrollment status
   */
  public async checkCourseAccess(courseId: number, userId?: number): Promise<EnrollmentStatus> {
    try {
      const url = userId 
        ? `${this.rootURL}/check-access/${courseId}/${userId}`
        : `${this.rootURL}/check-access/${courseId}`;
      
      const response = await apiService.get<EnrollmentStatus>(url);
      return response;
    } catch (error) {
      console.error('Error checking course access:', error);
      // Return default no access status
      return { hasAccess: false };
    }
  }

  /**
   * Enroll user in a free course
   * @param courseId - The course ID
   * @returns Promise with enrollment result
   */
  public async enrollInFreeCourse(courseId: number): Promise<EnrollmentStatus> {
    try {
      const response = await apiService.post<EnrollmentStatus>(`${this.rootURL}/enroll-free`, {
        courseId
      });
      return response;
    } catch (error) {
      console.error('Error enrolling in free course:', error);
      throw error;
    }
  }

  /**
   * Get user's enrolled courses
   * @param userId - The user ID (optional, uses current user if not provided)
   * @returns Promise with enrolled courses
   */
  public async getUserEnrollments(userId?: number): Promise<any[]> {
    try {
      const url = userId 
        ? `${this.rootURL}/user/${userId}`
        : `${this.rootURL}/user`;
      
      const response = await apiService.get<any[]>(url);
      return response;
    } catch (error) {
      console.error('Error fetching user enrollments:', error);
      return [];
    }
  }

  /**
   * Get enrollment details for a specific course
   * @param courseId - The course ID
   * @param userId - The user ID (optional, uses current user if not provided)
   * @returns Promise with enrollment details
   */
  public async getEnrollmentDetails(courseId: number, userId?: number): Promise<EnrollmentStatus | null> {
    try {
      const url = userId 
        ? `${this.rootURL}/details/${courseId}/${userId}`
        : `${this.rootURL}/details/${courseId}`;
      
      const response = await apiService.get<EnrollmentStatus>(url);
      return response;
    } catch (error) {
      console.error('Error fetching enrollment details:', error);
      return null;
    }
  }

  /**
   * Update enrollment progress
   * @param courseId - The course ID
   * @param progress - Progress data
   * @returns Promise with updated enrollment
   */
  public async updateProgress(courseId: number, progress: {
    completionPercentage?: number;
    lastAccessedDate?: string;
    currentLessonId?: number;
  }): Promise<EnrollmentStatus> {
    try {
      const response = await apiService.put<EnrollmentStatus>(`${this.rootURL}/progress/${courseId}`, progress);
      return response;
    } catch (error) {
      console.error('Error updating enrollment progress:', error);
      throw error;
    }
  }

  /**
   * Complete a course enrollment (mark as completed)
   * @param courseId - The course ID
   * @returns Promise with completion result
   */
  public async completeCourse(courseId: number): Promise<EnrollmentStatus> {
    try {
      const response = await apiService.post<EnrollmentStatus>(`${this.rootURL}/complete/${courseId}`, {});
      return response;
    } catch (error) {
      console.error('Error completing course:', error);
      throw error;
    }
  }

  /**
   * Get course certificate if available
   * @param courseId - The course ID
   * @returns Promise with certificate URL or null
   */
  public async getCertificate(courseId: number): Promise<string | null> {
    try {
      const response = await apiService.get<{ certificateUrl: string }>(`${this.rootURL}/certificate/${courseId}`);
      return response.certificateUrl;
    } catch (error) {
      console.error('Error fetching certificate:', error);
      return null;
    }
  }

  /**
   * Unenroll from a course (if allowed)
   * @param courseId - The course ID
   * @returns Promise with unenrollment result
   */
  public async unenrollFromCourse(courseId: number): Promise<boolean> {
    try {
      await apiService.delete(`${this.rootURL}/unenroll/${courseId}`);
      return true;
    } catch (error) {
      console.error('Error unenrolling from course:', error);
      return false;
    }
  }

  /**
   * Check if user has active subscription that covers the course
   * @param courseId - The course ID (optional)
   * @returns Promise with subscription status
   */
  public async checkSubscriptionAccess(courseId?: number): Promise<{
    hasActiveSubscription: boolean;
    subscriptionType?: string;
    expiryDate?: string;
    coversAllCourses?: boolean;
  }> {
    try {
      const url = courseId 
        ? `${this.rootURL}/subscription-access/${courseId}`
        : `${this.rootURL}/subscription-access`;
      
      const response = await apiService.get<{
        hasActiveSubscription: boolean;
        subscriptionType?: string;
        expiryDate?: string;
        coversAllCourses?: boolean;
      }>(url);
      return response;
    } catch (error) {
      console.error('Error checking subscription access:', error);
      return { hasActiveSubscription: false };
    }
  }
}

// Create and export a singleton instance
const enrollmentService = new EnrollmentService();
export default enrollmentService;
