import apiService from './api';
import { environment } from '../config/environment';
import type { Course, CourseResponse } from '../models';
import { validateApiResponse, apiSchemas, commonSchemas } from '../utils/validation';
import { withRetry, handleError, createAppError } from '../utils/errorHandling';
import { z } from 'zod';

// Enhanced validation schemas for course operations
const courseValidationSchemas = {
  courseList: z.array(apiSchemas.course),
  courseDetail: apiSchemas.course,
  courseCreate: z.object({
    Title: commonSchemas.nonEmptyString,
    Description: z.string().min(10, 'Description must be at least 10 characters'),
    Price: z.number().min(0, 'Price cannot be negative').optional(),
    CategoryId: z.number().positive('Category is required').optional(),
    Format: commonSchemas.nonEmptyString.optional(),
    Level: z.enum(['Beginner', 'Intermediate', 'Advanced']).optional(),
    Language: commonSchemas.nonEmptyString.optional(),
  }),
  courseUpdate: z.object({
    Id: z.number().positive(),
    Title: commonSchemas.nonEmptyString.optional(),
    Description: z.string().min(10).optional(),
    Price: z.number().min(0).optional(),
    IsPublished: z.boolean().optional(),
  }),
  courseQuery: z.object({
    take: z.number().min(1).max(100).optional(),
    skip: z.number().min(0).optional(),
    justPublished: z.boolean().optional(),
    search: z.string().optional(),
    categoryId: z.number().positive().optional(),
    instructorId: z.number().positive().optional(),
  }),
};

/**
 * Enhanced Course Service with robust error handling and validation
 */
class CourseService {
  private readonly rootURL = '/courses';

  /**
   * Get all courses with enhanced validation and error handling
   * @param take - Number of courses to take
   * @param skip - Number of courses to skip
   * @param justPublished - Filter only published courses
   * @returns Promise with the courses
   */
  public async getAll(take = 10, skip = 0, justPublished = true): Promise<Course[]> {
    // Validate input parameters
    const queryValidation = courseValidationSchemas.courseQuery.safeParse({
      take,
      skip,
      justPublished
    });

    if (!queryValidation.success) {
      throw createAppError('Invalid query parameters', {
        code: 'VALIDATION_ERROR',
        details: queryValidation.error.errors
      });
    }

    return withRetry(async () => {
      try {
        // Backend expects 'justPublished' string for filtering, not boolean
        const publishedParam = justPublished ? 'justPublished' : '';
        const response = await apiService.get<Course[]>(`${this.rootURL}/${take}/${skip}?justPublished=${publishedParam}`);

        // Validate API response
        const validatedResponse = validateApiResponse(
          courseValidationSchemas.courseList,
          response,
          { fallback: [], logErrors: true }
        );

        return this.formatCourses(validatedResponse || []);
      } catch (error) {
        throw handleError(error, {
          context: { method: 'getAll', take, skip, justPublished },
          showToast: false // Let the calling component handle UI feedback
        });
      }
    }, {
      maxAttempts: 3,
      baseDelay: 1000,
      retryCondition: (error) => (error.status && error.status >= 500) || error.code === 'NETWORK_ERROR'
    });
  }

  /**
   * Get course count with enhanced error handling
   * @returns Promise with the total count
   */
  public async getCount(): Promise<number> {
    return withRetry(async () => {
      try {
        const response = await apiService.get<number>(`${this.rootURL}/count`);

        // Validate response is a number
        const count = z.number().min(0).safeParse(response);
        if (!count.success) {
          console.warn('Invalid course count response:', response);
          return 0;
        }

        return count.data;
      } catch (error) {
        handleError(error, {
          context: { method: 'getCount' },
          showToast: false
        });
        return 0; // Return fallback value
      }
    }, {
      maxAttempts: 2,
      baseDelay: 500
    });
  }

  /**
   * Get trending courses
   * @param take - Number of courses to take
   * @returns Promise with the trending courses (formatted with proper URLs)
   */
  public async getTrending(take = environment.paginationTake.COURSES_TRENDING_TAKE): Promise<Course[]> {
    try {
      const courses = await apiService.get<Course[]>(`${this.rootURL}/trending?take=${take}`);
      return this.formatCourses(courses);
    } catch (error) {
      console.error('Error fetching trending courses:', error);
      return [];
    }
  }

  /**
   * Get courses for you
   * @param take - Number of courses to take
   * @returns Promise with the courses for you (formatted with proper URLs)
   */
  public async getForYou(take = environment.paginationTake.COURSES_FOR_YOU_TAKE): Promise<Course[]> {
    try {
      const courses = await apiService.get<Course[]>(`${this.rootURL}/for-you?take=${take}`);
      return this.formatCourses(courses);
    } catch (error) {
      console.error('Error fetching courses for you:', error);
      return [];
    }
  }

  /**
   * Get course by ID
   * @param id - The course ID
   * @returns Promise with the course (formatted with proper URLs)
   */
  public async getById(id: number): Promise<Course> {
    try {
      const course = await apiService.get<Course>(`${this.rootURL}/${id}`);
      return this.formatCourse(course);
    } catch (error) {
      console.error('Error fetching course by ID:', error);
      throw error;
    }
  }

  /**
   * Get course by slug (public access)
   * @param slug - The course slug
   * @returns Promise with the course (formatted with proper URLs)
   */
  public async getBySlug(slug: string): Promise<Course> {
    try {
      const course = await apiService.get<Course>(`${this.rootURL}/public/${slug}`);

      // Format the course to ensure proper URL construction
      return this.formatCourse(course);
    } catch (error) {
      console.error('Error fetching course by slug:', error);
      throw error;
    }
  }

  /**
   * Get course by slug with full structure for management (private access)
   * @param slug - The course slug
   * @returns Promise with the course including sections and content
   */
  public async getBySlugForManagement(slug: string): Promise<Course> {
    try {
      console.log('🔍 Fetching course by slug for management:', slug);

      // Try multiple approaches to get the course data
      let course: Course | null = null;
      let lastError: any = null;

      // Approach 1: Try the public endpoint with authentication
      try {
        console.log('🌐 Trying public endpoint:', `${this.rootURL}/public/${slug}`);
        course = await apiService.get<Course>(`${this.rootURL}/public/${slug}`);
        console.log('✅ Public endpoint successful:', course);
      } catch (publicError) {
        console.warn('⚠️ Public endpoint failed:', publicError);
        lastError = publicError;
      }

      // Approach 2: If public endpoint fails, try direct ID-based access if we can get the ID
      if (!course) {
        try {
          // First, try to get course by ID if the slug is actually an ID
          const courseId = parseInt(slug);
          if (!isNaN(courseId)) {
            console.log('🌐 Trying ID-based endpoint:', `${this.rootURL}/${courseId}`);
            course = await apiService.get<Course>(`${this.rootURL}/${courseId}`);
            console.log('✅ ID-based endpoint successful:', course);
          }
        } catch (idError) {
          console.warn('⚠️ ID-based endpoint failed:', idError);
          lastError = idError;
        }
      }

      // Approach 3: Try to get all user courses and find the matching one
      if (!course) {
        try {
          console.log('🌐 Trying to find course in user courses...');
          const userCourses = await this.getInstructorCourses();
          course = userCourses.find(c => c.Slug === slug || c.Id?.toString() === slug) || null;
          if (course) {
            console.log('✅ Found course in user courses:', course);
          }
        } catch (userCoursesError) {
          console.warn('⚠️ User courses approach failed:', userCoursesError);
          lastError = userCoursesError;
        }
      }

      // Validate that we got a proper course object
      if (!course || !course.Id) {
        console.error('❌ All approaches failed to fetch course');
        throw new Error(`Course not found: ${slug}. Last error: ${lastError?.message || 'Unknown error'}`);
      }

      console.log('✅ Course fetched successfully:', course);

      // Format the course to ensure proper URL construction
      return this.formatCourse(course);

    } catch (error) {
      console.error('❌ Error fetching course by slug:', error);
      console.error('❌ Error details:', {
        slug,
        error: error instanceof Error ? error.message : error
      });
      throw error;
    }
  }

  /**
   * Get courses by category
   * @param categoryId - The category ID
   * @param take - Number of courses to take
   * @param skip - Number of courses to skip
   * @returns Promise with the courses (formatted with proper URLs)
   */
  public async getByCategory(categoryId: number, take = 10, skip = 0): Promise<Course[]> {
    try {
      const courses = await apiService.get<Course[]>(`${this.rootURL}/category/${categoryId}?take=${take}&skip=${skip}`);
      return this.formatCourses(Array.isArray(courses) ? courses : []);
    } catch (error) {
      console.error('Error fetching courses by category:', error);
      return [];
    }
  }

  /**
   * Get courses by category slug (matches backend API)
   * @param categorySlug - The category slug
   * @param take - Number of courses to take
   * @param skip - Number of courses to skip
   * @returns Promise with courses and count
   */
  public async getByCategorySlug(categorySlug: string, take = 10, skip = 0): Promise<{ Courses: Course[]; Count: number }> {
    try {
      const response = await apiService.get<{ Courses: Course[]; Count: number }>(`${this.rootURL}/categories/${categorySlug}/${take}/${skip}`);

      // Format the courses to ensure proper URL construction
      const formattedCourses = this.formatCourses(response.Courses || []);

      return {
        Courses: formattedCourses,
        Count: response.Count
      };
    } catch (error) {
      console.error('Error fetching courses by category:', error);
      return { Courses: [], Count: 0 };
    }
  }

  /**
   * Get courses created by the current instructor
   * @returns Promise with the instructor's courses
   */
  public async getInstructorCourses(): Promise<Course[]> {
    try {
      // This endpoint should return courses for the authenticated instructor
      const response = await apiService.get<Course[]>(`${this.rootURL}/instructor`);
      const courses = Array.isArray(response) ? response : [];
      return this.formatCourses(courses);
    } catch (error) {
      console.error('Error fetching instructor courses:', error);
      // Fallback: try to get courses by user ID if we have it
      try {
        const authState = JSON.parse(localStorage.getItem('authState') || '{}');
        const userId = authState.user?.Id;
        if (userId) {
          const userCourses = await apiService.get<Course[]>(`${this.rootURL}/users/${userId}`);
          const courses = Array.isArray(userCourses) ? userCourses : [];
          return this.formatCourses(courses);
        }
      } catch (fallbackError) {
        console.error('Fallback instructor courses fetch failed:', fallbackError);
      }
      return [];
    }
  }

  /**
   * Get courses by user
   * @param userId - The user ID
   * @param take - Number of courses to take
   * @param skip - Number of courses to skip
   * @returns Promise with the courses
   */
  public async getByUser(userId: number, take = 10, skip = 0): Promise<Course[]> {
    return apiService.get<Course[]>(`${this.rootURL}/user/${userId}?take=${take}&skip=${skip}`);
  }

  /**
   * Search courses
   * @param query - The search query
   * @param take - Number of courses to take
   * @param skip - Number of courses to skip
   * @returns Promise with the courses (formatted with proper URLs)
   */
  public async search(query: string, take = 10, skip = 0): Promise<Course[]> {
    try {
      const courses = await apiService.get<Course[]>(`${this.rootURL}/search?q=${query}&take=${take}&skip=${skip}`);
      return this.formatCourses(Array.isArray(courses) ? courses : []);
    } catch (error) {
      console.error('Error searching courses:', error);
      return [];
    }
  }

  /**
   * Add a new course
   * @param course - The course to add
   * @returns Promise with the created course
   */
  public async add(course: Course): Promise<Course> {
    try {
      // Set default values exactly like Angular
      course.Published = false;
      course.Archived = false;

      // console.log('the course is : ', course);

      // Get origin exactly like Angular
      let origin: string;
      if (location.port) {
        origin = `${location.protocol}//${location.hostname}:${location.port}`;
      } else {
        origin = `${location.protocol}//${location.hostname}`;
      }

      // Send request with origin and body structure exactly like Angular
      const response = await apiService.post<Course>(this.rootURL, { origin, body: course });

      // console.log('✅ Course created successfully:', response);
      return response;
    } catch (error: any) {
      console.error('❌ Error creating course:', error);
      console.error('❌ Error response:', error.response?.data);
      console.error('❌ Error status:', error.response?.status);
      console.error('❌ Error message:', error.message);

      // Provide more specific error messages
      if (error.response?.data?.message) {
        throw new Error(error.response.data.message);
      } else if (error.response?.status === 500) {
        throw new Error('Server error occurred while creating course. Please check the course data and try again.');
      } else {
        throw error;
      }
    }
  }

  /**
   * Edit a course
   * @param course - The course to edit
   * @returns Promise with the updated course
   */
  public async edit(course: Course): Promise<Course> {
    try {
      if (!course.Id && !course.Slug) {
        throw new Error('Course ID or slug is required for editing');
      }

      console.log('📝 Updating course with data:', course);

      // Use slug for editing like the old Angular app
      const slug = course.Slug;
      if (!slug) {
        throw new Error('Course slug is required for editing');
      }

      const result = await apiService.put<Course>(`${this.rootURL}/${slug}`, course);

      console.log('✅ Course updated successfully:', result);

      return result;
    } catch (error) {
      console.error('❌ Error updating course:', error);
      throw error;
    }
  }

  /**
   * Update a course (alias for edit method)
   * @param slug - The course slug
   * @param updates - Partial course data to update
   * @returns Promise with the updated course
   */
  public async update(slug: string, updates: Partial<Course>): Promise<Course> {
    try {
      console.log('📝 Updating course with slug:', slug, 'and data:', updates);

      const result = await apiService.put<Course>(`${this.rootURL}/${slug}`, updates);

      console.log('✅ Course updated successfully:', result);

      return result;
    } catch (error) {
      console.error('❌ Error updating course:', error);
      throw error;
    }
  }

  /**
   * Delete a course
   * @param course - The course to delete
   * @returns Promise with the deletion result
   */
  public async delete(course: Course): Promise<CourseResponse> {
    if (!course.Id && !course.Slug) {
      throw new Error('Course ID or slug is required for deletion');
    }

    const id = course.Id || course.Slug;
    return apiService.delete<CourseResponse>(`${this.rootURL}/${id}`);
  }

  /**
   * Get courses pending verification
   * @param take - Number of courses to take
   * @param skip - Number of courses to skip
   * @returns Promise with the courses pending verification
   */
  public async getCoursesToVerify(take = 10, skip = 0): Promise<Course[]> {
    try {
      const response = await apiService.get<Course[]>(`${this.rootURL}/to-verify/${take}/${skip}`);
      return Array.isArray(response) ? response : [];
    } catch (error) {
      console.error('Error fetching courses to verify:', error);
      return [];
    }
  }

  /**
   * Get count of courses pending verification
   * @returns Promise with the count
   */
  public async getCoursesToVerifyCount(): Promise<number> {
    const response = await apiService.get<{ Count: number }>(`${this.rootURL}/to-verify/count`);
    return response.Count;
  }

  /**
   * Approve a course
   * @param courseId - The course ID
   * @returns Promise with the approved course
   */
  public async approveCourse(courseId: number): Promise<Course> {
    try {
      return await apiService.put<Course>(`${this.rootURL}/${courseId}/approve`, {});
    } catch (error) {
      console.error(`Error approving course ${courseId}:`, error);
      throw error;
    }
  }

  /**
   * Reject a course
   * @param courseId - The course ID
   * @param reason - The rejection reason
   * @returns Promise with the rejected course
   */
  public async rejectCourse(courseId: number, reason: string): Promise<Course> {
    try {
      return await apiService.put<Course>(`${this.rootURL}/${courseId}/reject`, { reason });
    } catch (error) {
      console.error(`Error rejecting course ${courseId}:`, error);
      throw error;
    }
  }

  /**
   * Get reported courses
   * @param take - Number of courses to take
   * @param skip - Number of courses to skip
   * @returns Promise with the reported courses
   */
  public async getReportedCourses(take = 10, skip = 0): Promise<Course[]> {
    try {
      const response = await apiService.get<Course[]>(`${this.rootURL}/reported/${take}/${skip}`);
      return Array.isArray(response) ? response : [];
    } catch (error) {
      console.error('Error fetching reported courses:', error);
      return [];
    }
  }

  /**
   * Get count of reported courses
   * @returns Promise with the count
   */
  public async getReportedCoursesCount(): Promise<number> {
    try {
      const response = await apiService.get<{ Count: number }>(`${this.rootURL}/reported/count`);
      return response.Count || 0;
    } catch (error) {
      console.error('Error fetching reported courses count:', error);
      return 0;
    }
  }

  /**
   * Get archived courses
   * @param take - Number of courses to take
   * @param skip - Number of courses to skip
   * @returns Promise with the archived courses
   */
  public async getArchivedCourses(take = 10, skip = 0): Promise<Course[]> {
    try {
      const response = await apiService.get<Course[]>(`${this.rootURL}/archived/${take}/${skip}`);
      return Array.isArray(response) ? response : [];
    } catch (error) {
      console.error('Error fetching archived courses:', error);
      return [];
    }
  }

  /**
   * Get count of archived courses
   * @returns Promise with the count
   */
  public async getArchivedCoursesCount(): Promise<number> {
    try {
      const response = await apiService.get<{ Count: number }>(`${this.rootURL}/archived/count`);
      return response.Count || 0;
    } catch (error) {
      console.error('Error fetching archived courses count:', error);
      return 0;
    }
  }

  /**
   * Get all user courses
   * @param userSlug - User slug
   * @returns Promise with user's courses
   */
  public async getAllUserCourses(userSlug: string): Promise<Course[]> {
    try {
      const response = await apiService.get<Course[]>(`${this.rootURL}/users/${userSlug}`);
      return this.formatCourses(Array.isArray(response) ? response : []);
    } catch (error) {
      console.error('Error fetching user courses:', error);
      return [];
    }
  }

  /**
   * Get courses by instructor
   * @param instructorSlug - Instructor slug
   * @returns Promise with instructor's courses
   */
  public async getCoursesByInstructor(instructorSlug: string): Promise<Course[]> {
    try {
      const response = await apiService.get<Course[]>(`${this.rootURL}/instructors/${instructorSlug}`);
      return this.formatCourses(Array.isArray(response) ? response : []);
    } catch (error) {
      console.error('Error fetching instructor courses:', error);
      return [];
    }
  }

  /**
   * Get instructor courses with pagination (matches backend endpoint)
   * @param instructorSlug - The instructor slug
   * @param take - Number of courses to take
   * @param skip - Number of courses to skip
   * @returns Promise with paginated instructor courses
   */
  public async getInstructorCoursesWithPagination(
    instructorSlug: string,
    take: number = 10,
    skip: number = 0
  ): Promise<{ courses: Course[]; countTotal: number }> {
    try {
      const response = await apiService.get<{ courses: Course[]; countTotal: number }>(
        `${this.rootURL}/users/${instructorSlug}/pagninate/${take}/${skip}`
      );
      return {
        courses: this.formatCourses(response.courses || []),
        countTotal: response.countTotal || 0
      };
    } catch (error) {
      console.error('Error fetching paginated instructor courses:', error);
      return { courses: [], countTotal: 0 };
    }
  }

  /**
   * Get course learners count (matches backend endpoint)
   * @param courseSlug - The course slug
   * @returns Promise with learners count
   */
  public async getCourseLearners(courseSlug: string): Promise<number> {
    try {
      return await apiService.get<number>(`${this.rootURL}/learners/${courseSlug}/count`);
    } catch (error) {
      console.error('Error fetching course learners count:', error);
      return 0;
    }
  }

  /**
   * Get all learners of a course (matches backend endpoint)
   * @param courseSlug - The course slug
   * @returns Promise with course learners
   */
  public async getAllLearnersOfCourse(courseSlug: string): Promise<any[]> {
    try {
      return await apiService.get<any[]>(`${this.rootURL}/learners/${courseSlug}`);
    } catch (error) {
      console.error('Error fetching course learners:', error);
      return [];
    }
  }

  /**
   * Get course views analytics (matches backend endpoint)
   * @param courseSlug - The course slug
   * @returns Promise with course views
   */
  public async getCourseViews(courseSlug: string): Promise<any[]> {
    try {
      return await apiService.get<any[]>(`${this.rootURL}/${courseSlug}/views`);
    } catch (error) {
      console.error('Error fetching course views:', error);
      return [];
    }
  }

  /**
   * Submit course for admin approval (instructor workflow)
   * @param slug - The course slug
   * @returns Promise with the submitted course
   */
  public async submitCourseForApproval(slug: string): Promise<Course> {
    try {
      console.log('📤 Submitting course for admin approval:', slug);

      // Get current course data
      const currentCourse = await this.getBySlugForManagement(slug);
      console.log('📋 Current course data:', currentCourse);

      // Validate course completeness before submission
      const validationErrors = this.validateCourseForSubmission(currentCourse);
      if (validationErrors.length > 0) {
        throw new Error(`Course validation failed: ${validationErrors.join(', ')}`);
      }

      // Submit course for approval (set Published to false but mark as submitted)
      const submissionData = {
        Id: currentCourse.Id,
        Title: currentCourse.Title,
        Resume: currentCourse.Resume,
        Keywords: currentCourse.Keywords,
        Price: currentCourse.Price,
        NewPrice: currentCourse.NewPrice,
        Free: currentCourse.Free,
        Language: currentCourse.Language,
        Prerequisites: currentCourse.Prerequisites,
        Goals: currentCourse.Goals,
        Message: currentCourse.Message,
        Format: currentCourse.Format,
        Congratulation: currentCourse.Congratulation,
        Level: currentCourse.Level,
        Currency: currentCourse.Currency,
        Published: false, // Not published yet - waiting for admin approval
        Archived: false,
        SubmittedForApproval: true, // Custom flag to indicate submission
        SubmissionDate: new Date().toISOString()
      };

      console.log('📤 Sending submission data:', submissionData);
      const response = await apiService.put<Course>(`${this.rootURL}/${currentCourse.Id}`, submissionData);

      console.log('✅ Course submitted for approval successfully!');
      return response;
    } catch (error) {
      console.error('❌ Failed to submit course for approval:', error);
      throw error;
    }
  }

  /**
   * Validate course completeness before submission
   * @param course - The course to validate
   * @returns Array of validation error messages
   */
  private validateCourseForSubmission(course: Course): string[] {
    const errors: string[] = [];

    // Basic course information
    if (!course.Title?.trim()) {
      errors.push('Course title is required');
    }

    if (!course.Resume?.trim()) {
      errors.push('Course description is required');
    }

    if (!course.Categories || course.Categories.length === 0) {
      errors.push('At least one category is required');
    }

    if (!course.Language) {
      errors.push('Course language is required');
    }

    if (!course.Level || course.Level.length === 0) {
      errors.push('Course level is required');
    }

    // Course content validation
    if (!course.Sections || course.Sections.length === 0) {
      errors.push('At least one course section is required');
    } else {
      // Check if sections have content
      const sectionsWithContent = course.Sections.filter(section =>
        section.Contents && section.Contents.length > 0
      );

      if (sectionsWithContent.length === 0) {
        errors.push('At least one section must have content');
      }
    }

    // Pricing validation
    if (!course.Free) {
      if (!course.Price || course.Price <= 0) {
        errors.push('Course price is required for paid courses');
      }

      if (!course.Currency) {
        errors.push('Currency is required for paid courses');
      }
    }

    return errors;
  }

  /**
   * Get course views count (matches backend endpoint)
   * @param courseSlug - The course slug
   * @returns Promise with course views count
   */
  public async getCourseViewsCount(courseSlug: string): Promise<number> {
    try {
      return await apiService.get<number>(`${this.rootURL}/${courseSlug}/views/count`);
    } catch (error) {
      console.error('Error fetching course views count:', error);
      return 0;
    }
  }

  /**
   * Get instructor learners count (matches backend endpoint)
   * @param instructorSlug - The instructor slug
   * @returns Promise with instructor learners count
   */
  public async getInstructorLearnersCount(instructorSlug: string): Promise<{ Learners: number }> {
    try {
      return await apiService.get<{ Learners: number }>(`${this.rootURL}/instructor/${instructorSlug}/learners`);
    } catch (error) {
      console.error('Error fetching instructor learners count:', error);
      return { Learners: 0 };
    }
  }

  /**
   * Download course exercise files (matches backend endpoint)
   * @param courseSlug - The course slug
   * @returns Promise with download response
   */
  public async downloadExerciseFiles(courseSlug: string): Promise<Blob> {
    try {
      return await apiService.getBlob(`${this.rootURL}/${courseSlug}/exercises/download`);
    } catch (error) {
      console.error('Error downloading exercise files:', error);
      throw error;
    }
  }

  /**
   * Get all instructors with course statistics
   * @returns Promise with instructor statistics
   */
  public async getAllInstructors(): Promise<Array<{ NumberOfCourse: number; Instructor: any; NumberOfStudent: number }>> {
    try {
      const response = await apiService.get<Array<{ NumberOfCourse: number; Instructor: any; NumberOfStudent: number }>>(`${this.rootURL}/instructors`);
      return Array.isArray(response) ? response : [];
    } catch (error) {
      console.error('Error fetching instructor statistics:', error);
      return [];
    }
  }

  /**
   * Search courses with filters
   * @param param - Pagination parameters
   * @param search - Search criteria
   * @returns Promise with search results
   */
  public async getByFilter(param: { take: number; skip: number }, search: any): Promise<any> {
    try {
      const response = await apiService.post<any>(`${this.rootURL}/${param.take}/${param.skip}`, search);
      if (response.Courses) {
        response.Courses = this.formatCourses(response.Courses);
      }
      return response;
    } catch (error) {
      console.error('Error searching courses:', error);
      return { Courses: [], Count: 0 };
    }
  }

  /**
   * Format single course data (add computed fields, format image URLs)
   * Updated to work gracefully with the new backend media storage structure
   * @param course - Raw course data from API
   * @returns Formatted course data
   */
  private formatCourse(course: Course): Course {
    if (!course) return course;

    // Extract media IDs from media objects for frontend compatibility
    if (course.CoverImage?.Id && !course.coverImageId) {
      (course as any).coverImageId = course.CoverImage.Id;
    }

    if (course.PresentationVideo?.Id && !course.presentationVideoId) {
      (course as any).presentationVideoId = course.PresentationVideo.Id;
    }

    // Add computed fields for media existence
    (course as any).hasCoverImage = !!course.CoverImage;
    (course as any).hasPresentationVideo = !!course.PresentationVideo;

    // Debug logging for media formatting (only in development)
    if (environment.development && (course.CoverImage || course.PresentationVideo)) {
      console.log('🎨 Formatting course media:', {
        courseId: course.Id,
        courseTitle: course.Title,
        hasCoverImage: !!course.CoverImage,
        hasVideo: !!course.PresentationVideo,
        coverImageId: (course as any).coverImageId,
        presentationVideoId: (course as any).presentationVideoId,
        originalCoverHashname: course.CoverImage?.Hashname,
        originalVideoHashname: course.PresentationVideo?.Hashname
      });
    }

    // Format cover image URL
    if (course.CoverImage?.Hashname) {
      const originalHashname = course.CoverImage.Hashname;
      course.CoverImage.Hashname = this.constructMediaUrl(originalHashname);

      if (environment.development) {
        console.log('📸 Cover image URL formatted:', {
          original: originalHashname,
          formatted: course.CoverImage.Hashname
        });
      }
    }

    // Format presentation video URL
    if (course.PresentationVideo?.Hashname) {
      const originalHashname = course.PresentationVideo.Hashname;
      course.PresentationVideo.Hashname = this.constructMediaUrl(originalHashname);

      if (environment.development) {
        console.log('🎥 Presentation video URL formatted:', {
          original: originalHashname,
          formatted: course.PresentationVideo.Hashname
        });
      }
    }

    // Format creator photo
    if (course.CreatedBy?.Photo?.Hashname) {
      course.CreatedBy.Photo.Hashname = this.constructMediaUrl(course.CreatedBy.Photo.Hashname);
    }

    // Format categories photos
    if (course.Categories) {
      course.Categories = course.Categories.map(category => {
        if (category.Photo?.Hashname) {
          category.Photo.Hashname = this.constructMediaUrl(category.Photo.Hashname);
        }
        return category;
      });
    }

    return course;
  }

  /**
   * Construct proper media URL from hashname
   * Handles both relative paths and full URLs consistently
   * Updated to work with the new organized directory structure
   * @param hashname - The hashname/filename from the API
   * @returns Complete URL for the media file
   */
  private constructMediaUrl(hashname: string): string {
    if (!hashname) return '';

    // If already a complete URL, return as-is
    if (hashname.startsWith('http://') || hashname.startsWith('https://')) {
      return hashname;
    }

    // Remove leading slash if present to avoid double slashes
    const cleanHashname = hashname.startsWith('/') ? hashname.substring(1) : hashname;

    // NEW ORGANIZED DIRECTORY STRUCTURE:
    // Backend now returns hashnames in the format:
    // "upload/courses/courseSlug/cover/cvr_18af6c7318624bf885f97a4fedf09bdf-cover.png"
    // "upload/courses/courseSlug/video/vid_4e2a2c913d30409585127e268d9eaa8f-video.mp4"
    // "upload/profiles/userSlug/prf_abc123def456-profile.jpg"
    //
    // LEGACY SUPPORT (for backward compatibility):
    // "upload/D2174571/content/coverimage/cvr_18af6c7318624bf885f97a4fedf09bdf-database-test-cover.png"
    // "upload/D2174571/content/presentation/prs_4e2a2c913d30409585127e268d9eaa8f-database-test-video.mp4"
    //
    // The backend serves these files directly from the root with the full path
    // For development: http://localhost:3200/[hashname]
    // For production: https://api.brainmaker.academy/[hashname]
    return `${environment.path}/${cleanHashname}`;
  }

  /**
   * Format array of courses
   * @param courses - Array of raw course data from API
   * @returns Array of formatted course data
   */
  private formatCourses(courses: Course[]): Course[] {
    return courses.map(course => this.formatCourse(course));
  }

  /**
   * Get platform statistics for homepage
   * @returns Promise with platform statistics
   */
  public async getPlatformStats(): Promise<{
    totalStudents: number;
    totalCourses: number;
    totalInstructors: number;
    averageRating: number;
  }> {
    try {
      const response = await apiService.get<{
        totalStudents: number;
        totalCourses: number;
        totalInstructors: number;
        averageRating: number;
      }>(`${this.rootURL}/platform-stats`);

      return response;
    } catch (error) {
      console.error('Error fetching platform statistics:', error);
      return {
        totalStudents: 125000,
        totalCourses: 5000,
        totalInstructors: 1200,
        averageRating: 4.8
      };
    }
  }

  /**
   * Get featured course for homepage
   * @returns Promise with featured course
   */
  public async getFeaturedCourse(): Promise<any> {
    try {
      const response = await apiService.get<any>(`${this.rootURL}/featured-course`);
      return response;
    } catch (error) {
      console.error('Error fetching featured course:', error);
      return {
        id: 1,
        title: "Complete Web Development Bootcamp",
        slug: "complete-web-development-bootcamp",
        price: 89.99,
        originalPrice: 199.99,
        instructor: {
          firstname: "John",
          lastname: "Doe"
        },
        rating: 4.8,
        studentCount: 15000
      };
    }
  }

  // ==================== ADMIN COURSE MANAGEMENT ====================

  /**
   * Get free and paid course counts (admin functionality)
   * @returns Promise with course counts
   */
  public async getFreeAndPaidCourseCounts(): Promise<{ Free: number; Paid: number; Count: number }> {
    try {
      const response = await apiService.get<{ Free: number; Paid: number; Count: number }>(`${this.rootURL}/countFreeAndPaidCourse`);
      return response;
    } catch (error) {
      console.error('Error fetching free and paid course counts:', error);
      return { Free: 0, Paid: 0, Count: 0 };
    }
  }

  /**
   * Get courses pending verification with count (admin functionality)
   * @param take - Number of courses to fetch
   * @param skip - Number of courses to skip
   * @returns Promise with courses to verify and total count
   */
  public async getCoursesToVerifyWithCount(take = 10, skip = 0): Promise<{ Count: number; Courses: Course[] }> {
    try {
      const response = await apiService.get<{ Count: number; Courses: Course[] }>(`${this.rootURL}/all/to/verify/${take}/${skip}`);
      if (response.Courses) {
        response.Courses = this.formatCourses(response.Courses);
      }
      return response;
    } catch (error) {
      console.error('Error fetching courses to verify:', error);
      return { Count: 0, Courses: [] };
    }
  }

  /**
   * Count courses pending verification (admin functionality)
   * @returns Promise with count
   */
  public async countCoursesToVerify(): Promise<number> {
    try {
      const response = await apiService.get<number>(`${this.rootURL}/count/all/to/verify`);
      return response;
    } catch (error) {
      console.error('Error counting courses to verify:', error);
      return 0;
    }
  }

  /**
   * Get archived courses with count (admin functionality)
   * @param take - Number of courses to fetch
   * @param skip - Number of courses to skip
   * @returns Promise with archived courses and total count
   */
  public async getArchivedCoursesWithCount(take = 10, skip = 0): Promise<{ Count: number; Courses: Course[] }> {
    try {
      const response = await apiService.get<{ Count: number; Courses: Course[] }>(`${this.rootURL}/all/archived/courses/${take}/${skip}`);
      if (response.Courses) {
        response.Courses = this.formatCourses(response.Courses);
      }
      return response;
    } catch (error) {
      console.error('Error fetching archived courses:', error);
      return { Count: 0, Courses: [] };
    }
  }

  /**
   * Count archived courses (admin functionality)
   * @returns Promise with count
   */
  public async countArchivedCourses(): Promise<number> {
    try {
      const response = await apiService.get<number>(`${this.rootURL}/count/all/archived/courses`);
      return response;
    } catch (error) {
      console.error('Error counting archived courses:', error);
      return 0;
    }
  }

  /**
   * Get reported courses with count (admin functionality)
   * @param take - Number of courses to fetch
   * @param skip - Number of courses to skip
   * @returns Promise with reported courses and total count
   */
  public async getReportedCoursesWithCount(take = 10, skip = 0): Promise<{ Count: number; Courses: Course[] }> {
    try {
      const response = await apiService.get<{ Count: number; Courses: Course[] }>(`${this.rootURL}/all/to/reported/${take}/${skip}`);
      if (response.Courses) {
        response.Courses = this.formatCourses(response.Courses);
      }
      return response;
    } catch (error) {
      console.error('Error fetching reported courses:', error);
      return { Count: 0, Courses: [] };
    }
  }

  /**
   * Count reported courses (admin functionality)
   * @returns Promise with count
   */
  public async countReportedCourses(): Promise<number> {
    try {
      const response = await apiService.get<number>(`${this.rootURL}/count/all/reported/courses`);
      return response;
    } catch (error) {
      console.error('Error counting reported courses:', error);
      return 0;
    }
  }

  /**
   * Get recommended courses for user
   * @param slug - User slug
   * @param take - Number of courses to fetch
   * @returns Promise with recommended courses
   */
  public async getRecommendedCourses(slug: string, take = 10): Promise<Course[]> {
    try {
      const response = await apiService.get<Course[]>(`${this.rootURL}/recommanded/${slug}/${take}`);
      return this.formatCourses(Array.isArray(response) ? response : []);
    } catch (error) {
      console.error('Error fetching recommended courses:', error);
      return [];
    }
  }

  /**
   * Get course reports (admin functionality)
   * @param courseSlug - Course slug
   * @returns Promise with course reports
   */
  public async getCourseReports(courseSlug: string): Promise<any[]> {
    try {
      const response = await apiService.get<any[]>(`${this.rootURL}/${courseSlug}/get/reports`);
      return Array.isArray(response) ? response : [];
    } catch (error) {
      console.error('Error fetching course reports:', error);
      return [];
    }
  }

  /**
   * Get users who reported a course (admin functionality)
   * @param courseSlug - Course slug
   * @returns Promise with users who reported the course
   */
  public async getUsersWhoReportedCourse(courseSlug: string): Promise<any[]> {
    try {
      const response = await apiService.get<any[]>(`${this.rootURL}/${courseSlug}/reports/users`);
      return Array.isArray(response) ? response : [];
    } catch (error) {
      console.error('Error fetching users who reported course:', error);
      return [];
    }
  }

  /**
   * Count users who reported a course (admin functionality)
   * @param courseSlug - Course slug
   * @returns Promise with count
   */
  public async countUsersWhoReportedCourse(courseSlug: string): Promise<number> {
    try {
      const response = await apiService.get<number>(`${this.rootURL}/${courseSlug}/reports/countUsers`);
      return response;
    } catch (error) {
      console.error('Error counting users who reported course:', error);
      return 0;
    }
  }

  /**
   * Search courses to verify (admin functionality)
   * @param take - Number of courses to fetch
   * @param skip - Number of courses to skip
   * @param search - Search criteria
   * @returns Promise with search results
   */
  public async searchCoursesToVerify(take: number, skip: number, search: any): Promise<{ Count: number; Courses: Course[] }> {
    try {
      const response = await apiService.post<{ Count: number; Courses: Course[] }>(`${this.rootURL}/all/to/verify/${take}/${skip}`, search);
      if (response.Courses) {
        response.Courses = this.formatCourses(response.Courses);
      }
      return response;
    } catch (error) {
      console.error('Error searching courses to verify:', error);
      return { Count: 0, Courses: [] };
    }
  }

  /**
   * Search archived courses (admin functionality)
   * @param take - Number of courses to fetch
   * @param skip - Number of courses to skip
   * @param search - Search criteria
   * @returns Promise with search results
   */
  public async searchArchivedCourses(take: number, skip: number, search: any): Promise<{ Count: number; Courses: Course[] }> {
    try {
      const response = await apiService.post<{ Count: number; Courses: Course[] }>(`${this.rootURL}/all/archived/courses/${take}/${skip}`, search);
      if (response.Courses) {
        response.Courses = this.formatCourses(response.Courses);
      }
      return response;
    } catch (error) {
      console.error('Error searching archived courses:', error);
      return { Count: 0, Courses: [] };
    }
  }

  /**
   * Search reported courses (admin functionality)
   * @param take - Number of courses to fetch
   * @param skip - Number of courses to skip
   * @param search - Search criteria
   * @returns Promise with search results
   */
  public async searchReportedCourses(take: number, skip: number, search: any): Promise<{ Count: number; Courses: Course[] }> {
    try {
      const response = await apiService.post<{ Count: number; Courses: Course[] }>(`${this.rootURL}/all/to/reported/${take}/${skip}`, search);
      if (response.Courses) {
        response.Courses = this.formatCourses(response.Courses);
      }
      return response;
    } catch (error) {
      console.error('Error searching reported courses:', error);
      return { Count: 0, Courses: [] };
    }
  }

  /**
   * Get featured courses
   * @param limit - Number of courses to return
   * @param category - Optional category filter
   * @returns Promise with featured courses
   */
  public async getFeaturedCourses(limit: number = 8, category?: string): Promise<any[]> {
    try {
      const params = new URLSearchParams({ limit: limit.toString() });
      if (category) params.append('category', category);

      const response = await apiService.get<any[]>(`${this.rootURL}/featured?${params}`);
      return Array.isArray(response) ? response : [];
    } catch (error) {
      console.error('Error fetching featured courses:', error);
      return [];
    }
  }

  /**
   * Get bestseller courses
   * @param limit - Number of courses to return
   * @param category - Optional category filter
   * @returns Promise with bestseller courses
   */
  public async getBestsellerCourses(limit: number = 8, category?: string): Promise<any[]> {
    try {
      const params = new URLSearchParams({ limit: limit.toString() });
      if (category) params.append('category', category);

      const response = await apiService.get<any[]>(`${this.rootURL}/bestsellers?${params}`);
      return Array.isArray(response) ? response : [];
    } catch (error) {
      console.error('Error fetching bestseller courses:', error);
      return [];
    }
  }

  /**
   * Get new courses
   * @param limit - Number of courses to return
   * @param category - Optional category filter
   * @returns Promise with new courses
   */
  public async getNewCourses(limit: number = 8, category?: string): Promise<any[]> {
    try {
      const params = new URLSearchParams({ limit: limit.toString() });
      if (category) params.append('category', category);

      const response = await apiService.get<any[]>(`${this.rootURL}/new?${params}`);
      return Array.isArray(response) ? response : [];
    } catch (error) {
      console.error('Error fetching new courses:', error);
      return [];
    }
  }

  /**
   * Get trending courses
   * @param limit - Number of courses to return
   * @param category - Optional category filter
   * @returns Promise with trending courses
   */
  public async getTrendingCourses(limit: number = 8, category?: string): Promise<any[]> {
    try {
      const params = new URLSearchParams({ limit: limit.toString() });
      if (category) params.append('category', category);

      const response = await apiService.get<any[]>(`${this.rootURL}/trending?${params}`);
      return Array.isArray(response) ? response : [];
    } catch (error) {
      console.error('Error fetching trending courses:', error);
      return [];
    }
  }
}

// Create a singleton instance
const courseService = new CourseService();
export default courseService;
