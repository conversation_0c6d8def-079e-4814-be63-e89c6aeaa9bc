import React from 'react';
import { <PERSON>, <PERSON>po<PERSON>, Button, Row, Col } from '../components/antd';
import ResponsiveHeader from '../components/layout/ResponsiveHeader';
import ResponsiveFooter from '../components/layout/ResponsiveFooter';

const { Title, Text } = Typography;

const ResponsiveLayoutDemo: React.FC = () => {
  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      {/* Responsive Header */}
      <ResponsiveHeader />
      
      {/* Main Content */}
      <main className="flex-1 py-responsive">
        <div className="container-responsive">
          
          {/* Hero Section */}
          <section className="text-center py-responsive mb-8">
            <Title level={1} className="text-responsive mb-4">
              Responsive Design Demo
            </Title>
            <Text className="text-responsive text-gray-600 max-w-2xl mx-auto block">
              This page demonstrates the responsive design system implemented for BrainMaker Academy.
              Resize your browser window or view on different devices to see the responsive behavior.
            </Text>
          </section>

          {/* Responsive Grid Demo */}
          <section className="mb-12">
            <Title level={2} className="text-responsive mb-6">
              Responsive Grid System
            </Title>
            <div className="grid-responsive">
              <Card className="p-6">
                <Title level={4} className="text-red-600 mb-3">Mobile First</Title>
                <Text className="text-gray-600">
                  Our design starts with mobile and progressively enhances for larger screens.
                </Text>
              </Card>
              <Card className="p-6">
                <Title level={4} className="text-red-600 mb-3">Flexible Layouts</Title>
                <Text className="text-gray-600">
                  Layouts adapt seamlessly from 1 column on mobile to 4 columns on desktop.
                </Text>
              </Card>
              <Card className="p-6">
                <Title level={4} className="text-red-600 mb-3">Touch Friendly</Title>
                <Text className="text-gray-600">
                  All interactive elements meet minimum touch target requirements.
                </Text>
              </Card>
              <Card className="p-6">
                <Title level={4} className="text-red-600 mb-3">Performance</Title>
                <Text className="text-gray-600">
                  Optimized for fast loading and smooth interactions across all devices.
                </Text>
              </Card>
            </div>
          </section>

          {/* Breakpoint Demo */}
          <section className="mb-12">
            <Title level={2} className="text-responsive mb-6">
              Breakpoint Visualization
            </Title>
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12} md={8} lg={6}>
                <Card className="text-center p-6 bg-red-50 border-red-200">
                  <div className="w-8 h-8 bg-red-500 rounded-full mx-auto mb-3"></div>
                  <Title level={5} className="text-red-600 mb-2">Mobile</Title>
                  <Text className="text-sm text-gray-600">&lt; 768px</Text>
                  <div className="mt-3">
                    <div className="block sm:hidden">
                      <span className="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
                        Active
                      </span>
                    </div>
                  </div>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={8} lg={6}>
                <Card className="text-center p-6 bg-blue-50 border-blue-200">
                  <div className="w-8 h-8 bg-blue-500 rounded-full mx-auto mb-3"></div>
                  <Title level={5} className="text-blue-600 mb-2">Tablet</Title>
                  <Text className="text-sm text-gray-600">768px - 991px</Text>
                  <div className="mt-3">
                    <div className="hidden sm:block md:hidden">
                      <span className="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
                        Active
                      </span>
                    </div>
                  </div>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={8} lg={6}>
                <Card className="text-center p-6 bg-green-50 border-green-200">
                  <div className="w-8 h-8 bg-green-500 rounded-full mx-auto mb-3"></div>
                  <Title level={5} className="text-green-600 mb-2">Desktop</Title>
                  <Text className="text-sm text-gray-600">992px - 1199px</Text>
                  <div className="mt-3">
                    <div className="hidden md:block lg:hidden">
                      <span className="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
                        Active
                      </span>
                    </div>
                  </div>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={8} lg={6}>
                <Card className="text-center p-6 bg-purple-50 border-purple-200">
                  <div className="w-8 h-8 bg-purple-500 rounded-full mx-auto mb-3"></div>
                  <Title level={5} className="text-purple-600 mb-2">Large</Title>
                  <Text className="text-sm text-gray-600">≥ 1200px</Text>
                  <div className="mt-3">
                    <div className="hidden lg:block">
                      <span className="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
                        Active
                      </span>
                    </div>
                  </div>
                </Card>
              </Col>
            </Row>
          </section>

          {/* Component Demo */}
          <section className="mb-12">
            <Title level={2} className="text-responsive mb-6">
              Responsive Components
            </Title>
            <Row gutter={[24, 24]}>
              <Col xs={24} lg={12}>
                <Card className="p-6">
                  <Title level={4} className="mb-4">Header Behavior</Title>
                  <ul className="space-y-2 text-gray-600">
                    <li>• <strong>Mobile:</strong> Compact logo, hamburger menu</li>
                    <li>• <strong>Tablet:</strong> Medium logo, horizontal nav</li>
                    <li>• <strong>Desktop:</strong> Full logo, complete navigation</li>
                  </ul>
                </Card>
              </Col>
              <Col xs={24} lg={12}>
                <Card className="p-6">
                  <Title level={4} className="mb-4">Footer Behavior</Title>
                  <ul className="space-y-2 text-gray-600">
                    <li>• <strong>Mobile:</strong> Single column layout</li>
                    <li>• <strong>Tablet:</strong> Two column layout</li>
                    <li>• <strong>Desktop:</strong> Four column layout</li>
                  </ul>
                </Card>
              </Col>
            </Row>
          </section>

          {/* Action Buttons */}
          <section className="text-center py-responsive">
            <Title level={3} className="text-responsive mb-6">
              Test the Responsive Design
            </Title>
            <div className="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
              <Button 
                type="primary" 
                size="large"
                className="w-full sm:w-auto bg-red-600 hover:bg-red-700 border-red-600"
              >
                View on Mobile
              </Button>
              <Button 
                size="large"
                className="w-full sm:w-auto"
              >
                View on Tablet
              </Button>
              <Button 
                size="large"
                className="w-full sm:w-auto"
              >
                View on Desktop
              </Button>
            </div>
            <Text className="text-sm text-gray-500 mt-4 block">
              Use your browser's developer tools to simulate different device sizes
            </Text>
          </section>

        </div>
      </main>

      {/* Responsive Footer */}
      <ResponsiveFooter />
    </div>
  );
};

export default ResponsiveLayoutDemo;
