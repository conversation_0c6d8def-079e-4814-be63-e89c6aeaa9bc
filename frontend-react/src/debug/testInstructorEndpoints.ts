import instructorService from '../services/instructor.service';
import courseService from '../services/course.service';

/**
 * Debug function to test instructor endpoints and understand data structure
 */
export async function testInstructorEndpoints() {
  console.log('🔍 Testing Instructor Endpoints...');
  
  try {
    // Test 1: Get all instructors
    console.log('\n📋 Testing instructorService.getAll()...');
    const instructors = await instructorService.getAll();
    console.log('Instructors response:', {
      type: typeof instructors,
      isArray: Array.isArray(instructors),
      length: instructors?.length || 0,
      sample: instructors?.[0] || null,
      allData: instructors
    });

    // Test 2: Get instructor statistics from course service
    console.log('\n📊 Testing courseService.getAllInstructors()...');
    const instructorStats = await courseService.getAllInstructors();
    console.log('Instructor stats response:', {
      type: typeof instructorStats,
      isArray: Array.isArray(instructorStats),
      length: instructorStats?.length || 0,
      sample: instructorStats?.[0] || null,
      allData: instructorStats
    });

    // Test 3: Compare data consistency
    console.log('\n🔄 Data Consistency Check:');
    console.log({
      instructorsCount: instructors?.length || 0,
      instructorStatsCount: instructorStats?.length || 0,
      instructorsHasData: !!(instructors && instructors.length > 0),
      instructorStatsHasData: !!(instructorStats && instructorStats.length > 0),
      dataConsistent: (instructors?.length || 0) === (instructorStats?.length || 0)
    });

    // Test 4: Check instructor data structure
    if (instructors && instructors.length > 0) {
      console.log('\n👤 Instructor Data Structure:');
      const instructor = instructors[0];
      console.log({
        id: instructor.Id,
        user: instructor.User ? {
          id: instructor.User.Id,
          username: instructor.User.Username,
          firstName: instructor.User.FirstName,
          lastName: instructor.User.LastName,
          slug: instructor.User.Slug,
          photo: instructor.User.Photo
        } : null,
        bio: instructor.Bio,
        specialization: instructor.Specialization,
        fullStructure: instructor
      });
    }

    // Test 5: Check instructor stats data structure
    if (instructorStats && instructorStats.length > 0) {
      console.log('\n📈 Instructor Stats Data Structure:');
      const stat = instructorStats[0];
      console.log({
        numberOfCourse: stat.NumberOfCourse,
        numberOfStudent: stat.NumberOfStudent,
        instructor: stat.Instructor ? {
          id: stat.Instructor.Id,
          user: stat.Instructor.User
        } : null,
        fullStructure: stat
      });
    }

    // Test 6: Try to match instructors with their stats
    console.log('\n🔗 Matching Instructors with Stats:');
    if (instructors && instructorStats) {
      instructors.forEach(instructor => {
        const matchingStat = instructorStats.find(stat => stat.Instructor?.Id === instructor.Id);
        console.log(`Instructor ${instructor.Id} (${instructor.User?.Username || 'Unknown'}):`, {
          hasMatchingStat: !!matchingStat,
          courses: matchingStat?.NumberOfCourse || 0,
          students: matchingStat?.NumberOfStudent || 0
        });
      });
    }

    return {
      instructors,
      instructorStats,
      success: true
    };

  } catch (error) {
    console.error('❌ Error testing instructor endpoints:', error);
    return {
      instructors: null,
      instructorStats: null,
      success: false,
      error
    };
  }
}

/**
 * Function to run the test from browser console
 */
export function runInstructorEndpointTest() {
  testInstructorEndpoints().then(result => {
    console.log('🎯 Test Results:', result);
  });
}

// Make it available globally for browser console testing
if (typeof window !== 'undefined') {
  (window as any).testInstructorEndpoints = runInstructorEndpointTest;
}
