/* ===== BRAINMAKER UNIFIED THEME SYSTEM ===== */
/* Consolidates all CSS into one powerful theme using brand colors */

/* Import Nunito Font from Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Nunito:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

/* Import Tailwind CSS */
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /* ===== BRAINMAKER BRAND COLORS ===== */
  :root {
    /* Core Brand Colors */
    --brand-red-primary: #dc3545;
    --brand-red-secondary: #c82333;
    --brand-red-light: #f8d7da;
    --brand-red-dark: #721c24;
    --brand-white: #ffffff;
    --brand-black: #000000;
    --brand-gray-dark: #1a1a1a;
    --brand-gray-medium: #4a4a4a;
    --brand-gray-light: #f5f5f5;

    /* ===== LIGHT THEME (DEFAULT) ===== */
    --color-background: var(--brand-white);
    --color-background-secondary: #f8f9fa;
    --color-background-tertiary: var(--brand-gray-light);
    --color-surface: var(--brand-white);
    --color-surface-hover: #f8f9fa;
    --color-surface-active: #e9ecef;
    
    /* Borders */
    --color-border: #dee2e6;
    --color-border-light: #e9ecef;
    --color-border-strong: #adb5bd;
    
    /* Text Colors */
    --color-text-primary: #212529;
    --color-text-secondary: #6c757d;
    --color-text-tertiary: #adb5bd;
    --color-text-inverse: var(--brand-white);
    --color-text-brand: var(--brand-red-primary);
    
    /* Interactive Colors */
    --color-primary: var(--brand-red-primary);
    --color-primary-hover: var(--brand-red-secondary);
    --color-primary-active: var(--brand-red-dark);
    --color-primary-light: var(--brand-red-light);
    
    /* Status Colors */
    --color-success: #198754;
    --color-success-light: #d1e7dd;
    --color-warning: #fd7e14;
    --color-warning-light: #fff3cd;
    --color-error: var(--brand-red-primary);
    --color-error-light: var(--brand-red-light);
    --color-info: #0dcaf0;
    --color-info-light: #cff4fc;

    /* Shadow System */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-brand: 0 4px 14px 0 rgba(220, 53, 69, 0.15);

    /* Spacing Scale */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    --space-24: 6rem;

    /* Border Radius */
    --radius-xs: 0.125rem;
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --radius-full: 9999px;

    /* Typography Scale */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;

    /* Font Families - Nunito Official Font */
    --font-primary: 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-heading: 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;

    /* Font Weights - Nunito Available Weights */
    --font-weight-extralight: 200;
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    --font-weight-black: 900;

    /* Line Heights */
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.625;
    --line-height-loose: 2;

    /* Z-Index Scale */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;

    /* Tailwind CSS Variables for shadcn/ui compatibility */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 355 78% 60%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 355 78% 60%;
    --radius: 0.5rem;
  }

  /* ===== DARK THEME ===== */
  .dark {
    /* Dark Theme Colors */
    --color-background: var(--brand-black);
    --color-background-secondary: var(--brand-gray-dark);
    --color-background-tertiary: #2a2a2a;
    --color-surface: var(--brand-gray-dark);
    --color-surface-hover: #2a2a2a;
    --color-surface-active: #3a3a3a;
    
    /* Dark Borders */
    --color-border: #3a3a3a;
    --color-border-light: #2a2a2a;
    --color-border-strong: #4a4a4a;
    
    /* Dark Text Colors */
    --color-text-primary: var(--brand-white);
    --color-text-secondary: #b0b0b0;
    --color-text-tertiary: #808080;
    --color-text-inverse: var(--brand-black);
    --color-text-brand: var(--brand-red-primary);

    /* Tailwind Dark Theme Variables */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 355 78% 60%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 355 78% 60%;
  }

  /* ===== BASE STYLES ===== */
  * {
    @apply border-border;
  }

  html {
    font-family: var(--font-primary);
    line-height: var(--line-height-normal);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  body {
    @apply bg-background text-foreground;
    background-color: var(--color-background);
    color: var(--color-text-primary);
    margin: 0;
    min-height: 100vh;
    font-family: var(--font-primary);
  }

  #root {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    width: 100%;
  }

  /* ===== TYPOGRAPHY ===== */
  h1, h2, h3, h4, h5, h6 {
    color: var(--color-text-primary);
    font-family: var(--font-heading);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
    margin-bottom: var(--space-4);
  }

  h1 { font-size: var(--font-size-4xl); font-weight: var(--font-weight-bold); }
  h2 { font-size: var(--font-size-3xl); }
  h3 { font-size: var(--font-size-2xl); }
  h4 { font-size: var(--font-size-xl); }
  h5 { font-size: var(--font-size-lg); }
  h6 { font-size: var(--font-size-base); }

  p {
    margin-bottom: var(--space-4);
    color: var(--color-text-secondary);
    line-height: var(--line-height-relaxed);
  }

  a {
    color: var(--color-primary);
    text-decoration: none;
    transition: all 0.2s ease;
  }

  a:hover {
    color: var(--color-primary-hover);
    text-decoration: none;
  }

  /* ===== NUNITO TYPOGRAPHY SHOWCASE ===== */
  .text-nunito-light {
    font-family: var(--font-primary);
    font-weight: var(--font-weight-light);
  }

  .text-nunito-medium {
    font-family: var(--font-primary);
    font-weight: var(--font-weight-medium);
  }

  .text-nunito-semibold {
    font-family: var(--font-primary);
    font-weight: var(--font-weight-semibold);
  }

  .text-nunito-bold {
    font-family: var(--font-primary);
    font-weight: var(--font-weight-bold);
  }

  .text-nunito-extrabold {
    font-family: var(--font-primary);
    font-weight: var(--font-weight-extrabold);
  }

  .text-nunito-black {
    font-family: var(--font-primary);
    font-weight: var(--font-weight-black);
  }

  /* Font weight utility classes */
  .font-nunito-light {
    font-family: var(--font-primary);
    font-weight: var(--font-weight-light);
  }

  .font-nunito-medium {
    font-family: var(--font-primary);
    font-weight: var(--font-weight-medium);
  }

  .font-nunito-semibold {
    font-family: var(--font-primary);
    font-weight: var(--font-weight-semibold);
  }

  .font-nunito-bold {
    font-family: var(--font-primary);
    font-weight: var(--font-weight-bold);
  }

  .font-nunito-extrabold {
    font-family: var(--font-primary);
    font-weight: var(--font-weight-extrabold);
  }

  .font-nunito-black {
    font-family: var(--font-primary);
    font-weight: var(--font-weight-black);
  }
}

@layer components {
  /* ===== BUTTON SYSTEM ===== */
  .btn-brand {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg border border-transparent transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
    font-weight: var(--font-weight-semibold);
    border-radius: var(--radius-lg);
  }

  .btn-brand-primary {
    @apply btn-brand;
    background-color: var(--color-primary);
    color: var(--brand-white);
    border-color: var(--color-primary);
  }

  .btn-brand-primary:hover {
    background-color: var(--color-primary-hover);
    border-color: var(--color-primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-brand);
  }

  .btn-brand-secondary {
    @apply btn-brand;
    background-color: var(--color-surface);
    color: var(--color-text-primary);
    border-color: var(--color-border);
  }

  .btn-brand-secondary:hover {
    background-color: var(--color-surface-hover);
    border-color: var(--color-border-strong);
  }

  .btn-brand-outline {
    @apply btn-brand;
    background-color: transparent;
    color: var(--color-primary);
    border-color: var(--color-primary);
  }

  .btn-brand-outline:hover {
    background-color: var(--color-primary);
    color: var(--brand-white);
  }

  .btn-brand-ghost {
    @apply btn-brand;
    background-color: transparent;
    color: var(--color-text-primary);
    border-color: transparent;
  }

  .btn-brand-ghost:hover {
    background-color: var(--color-surface-hover);
  }

  /* ===== CARD SYSTEM ===== */
  .card-brand {
    background-color: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    transition: all 0.2s ease;
  }

  .card-brand:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
  }

  .card-brand-interactive:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-4px);
    border-color: var(--color-primary);
  }

  /* ===== INPUT SYSTEM ===== */
  .input-brand {
    @apply block w-full px-3 py-2 border rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-1;
    background-color: var(--color-surface);
    border-color: var(--color-border);
    color: var(--color-text-primary);
    border-radius: var(--radius-lg);
  }

  .input-brand:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
  }

  .input-brand::placeholder {
    color: var(--color-text-tertiary);
  }

  /* ===== NAVBAR SYSTEM ===== */
  .navbar-brand {
    background-color: var(--color-surface);
    border-bottom: 1px solid var(--color-border);
    box-shadow: var(--shadow-sm);
    padding: var(--space-3) var(--space-4);
  }

  .nav-link-brand {
    color: var(--color-text-secondary);
    font-weight: var(--font-weight-medium);
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-md);
    transition: all 0.2s ease;
  }

  .nav-link-brand:hover {
    color: var(--color-primary);
    background-color: var(--color-surface-hover);
  }

  .nav-link-brand.active {
    color: var(--color-primary);
    background-color: var(--color-primary-light);
    font-weight: var(--font-weight-semibold);
  }

  /* ===== SIDEBAR SYSTEM ===== */
  .sidebar-brand {
    background-color: var(--color-surface);
    border-right: 1px solid var(--color-border);
    height: 100vh;
    position: sticky;
    top: 0;
    overflow-y: auto;
    padding: var(--space-6) 0;
  }

  .sidebar-brand .nav-link {
    color: var(--color-text-secondary);
    font-weight: var(--font-weight-medium);
    padding: var(--space-3) var(--space-4);
    margin: var(--space-1) var(--space-3);
    border-radius: var(--radius-lg);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: var(--space-3);
  }

  .sidebar-brand .nav-link:hover {
    background-color: var(--color-surface-hover);
    color: var(--color-primary);
  }

  .sidebar-brand .nav-link.active {
    background-color: var(--color-primary-light);
    color: var(--color-primary);
    font-weight: var(--font-weight-semibold);
  }

  /* ===== TABLE SYSTEM ===== */
  .table-brand {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background-color: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-xl);
    overflow: hidden;
  }

  .table-brand th {
    background-color: var(--color-background-secondary);
    color: var(--color-text-primary);
    font-weight: var(--font-weight-semibold);
    padding: var(--space-4);
    text-align: left;
    border-bottom: 1px solid var(--color-border);
  }

  .table-brand td {
    padding: var(--space-4);
    border-bottom: 1px solid var(--color-border-light);
    color: var(--color-text-secondary);
  }

  .table-brand tbody tr:hover {
    background-color: var(--color-surface-hover);
  }

  .table-brand tbody tr:last-child td {
    border-bottom: none;
  }

  /* ===== BADGE SYSTEM ===== */
  .badge-brand {
    display: inline-flex;
    align-items: center;
    padding: var(--space-1) var(--space-3);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    border-radius: var(--radius-full);
    transition: all 0.2s ease;
  }

  .badge-brand-primary {
    background-color: var(--color-primary);
    color: var(--brand-white);
  }

  .badge-brand-secondary {
    background-color: var(--color-background-secondary);
    color: var(--color-text-secondary);
  }

  .badge-brand-success {
    background-color: var(--color-success);
    color: var(--brand-white);
  }

  .badge-brand-warning {
    background-color: var(--color-warning);
    color: var(--brand-white);
  }

  .badge-brand-error {
    background-color: var(--color-error);
    color: var(--brand-white);
  }

  /* ===== FORM SYSTEM ===== */
  .form-group-brand {
    margin-bottom: var(--space-6);
  }

  .form-label-brand {
    display: block;
    font-weight: var(--font-weight-medium);
    color: var(--color-text-primary);
    margin-bottom: var(--space-2);
    font-size: var(--font-size-sm);
  }

  .form-help-brand {
    font-size: var(--font-size-xs);
    color: var(--color-text-tertiary);
    margin-top: var(--space-1);
  }

  .form-error-brand {
    font-size: var(--font-size-xs);
    color: var(--color-error);
    margin-top: var(--space-1);
  }

  /* ===== MODAL SYSTEM ===== */
  .modal-brand {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: var(--z-modal);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-4);
  }

  .modal-backdrop-brand {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
  }

  .modal-content-brand {
    position: relative;
    background-color: var(--color-surface);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
  }

  .modal-header-brand {
    padding: var(--space-6);
    border-bottom: 1px solid var(--color-border);
  }

  .modal-body-brand {
    padding: var(--space-6);
  }

  .modal-footer-brand {
    padding: var(--space-6);
    border-top: 1px solid var(--color-border);
    display: flex;
    justify-content: flex-end;
    gap: var(--space-3);
  }
}

@layer utilities {
  /* ===== LINE CLAMP UTILITIES ===== */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* ===== ANIMATION UTILITIES ===== */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  .animate-bounce-subtle {
    animation: bounceSubtle 2s infinite;
  }

  .animate-pulse-slow {
    animation: pulseGlow 3s ease-in-out infinite;
  }

  /* ===== TEXT UTILITIES ===== */
  .text-gradient-brand {
    background: linear-gradient(135deg, var(--brand-red-primary), var(--brand-red-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-gradient-dark {
    background: linear-gradient(135deg, var(--brand-black), var(--brand-gray-dark));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* ===== SPACING UTILITIES ===== */
  .space-brand-1 { margin: var(--space-1); }
  .space-brand-2 { margin: var(--space-2); }
  .space-brand-3 { margin: var(--space-3); }
  .space-brand-4 { margin: var(--space-4); }
  .space-brand-6 { margin: var(--space-6); }
  .space-brand-8 { margin: var(--space-8); }

  .p-brand-1 { padding: var(--space-1); }
  .p-brand-2 { padding: var(--space-2); }
  .p-brand-3 { padding: var(--space-3); }
  .p-brand-4 { padding: var(--space-4); }
  .p-brand-6 { padding: var(--space-6); }
  .p-brand-8 { padding: var(--space-8); }

  /* ===== SHADOW UTILITIES ===== */
  .shadow-brand-xs { box-shadow: var(--shadow-xs); }
  .shadow-brand-sm { box-shadow: var(--shadow-sm); }
  .shadow-brand-md { box-shadow: var(--shadow-md); }
  .shadow-brand-lg { box-shadow: var(--shadow-lg); }
  .shadow-brand-xl { box-shadow: var(--shadow-xl); }
  .shadow-brand { box-shadow: var(--shadow-brand); }

  /* ===== BORDER UTILITIES ===== */
  .border-brand { border-color: var(--color-border); }
  .border-brand-strong { border-color: var(--color-border-strong); }
  .border-brand-primary { border-color: var(--color-primary); }

  .rounded-brand-sm { border-radius: var(--radius-sm); }
  .rounded-brand-md { border-radius: var(--radius-md); }
  .rounded-brand-lg { border-radius: var(--radius-lg); }
  .rounded-brand-xl { border-radius: var(--radius-xl); }
  .rounded-brand-2xl { border-radius: var(--radius-2xl); }
  .rounded-brand-full { border-radius: var(--radius-full); }

  /* ===== BACKGROUND UTILITIES ===== */
  .bg-brand-primary { background-color: var(--color-primary); }
  .bg-brand-surface { background-color: var(--color-surface); }
  .bg-brand-background { background-color: var(--color-background); }
  .bg-brand-secondary { background-color: var(--color-background-secondary); }

  /* ===== TEXT COLOR UTILITIES ===== */
  .text-brand-primary { color: var(--color-text-primary); }
  .text-brand-secondary { color: var(--color-text-secondary); }
  .text-brand-tertiary { color: var(--color-text-tertiary); }
  .text-brand-red { color: var(--color-primary); }
  .text-brand-white { color: var(--brand-white); }
  .text-brand-black { color: var(--brand-black); }
}

/* ===== KEYFRAME ANIMATIONS ===== */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceSubtle {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-5px);
  }
  60% {
    transform: translateY(-3px);
  }
}

@keyframes pulseGlow {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  :root {
    --space-4: 0.75rem;
    --space-6: 1rem;
    --space-8: 1.5rem;
    --font-size-4xl: 1.875rem;
    --font-size-3xl: 1.5rem;
    --font-size-2xl: 1.25rem;
  }

  .sidebar-brand {
    position: static;
    height: auto;
    border-right: none;
    border-bottom: 1px solid var(--color-border);
  }

  .modal-content-brand {
    margin: var(--space-4);
    max-width: calc(100vw - 2rem);
  }

  .table-brand {
    font-size: var(--font-size-sm);
  }

  .table-brand th,
  .table-brand td {
    padding: var(--space-2) var(--space-3);
  }
}

@media (max-width: 480px) {
  :root {
    --space-4: 0.5rem;
    --space-6: 0.75rem;
    --space-8: 1rem;
  }

  .btn-brand {
    padding: var(--space-2) var(--space-4);
    font-size: var(--font-size-sm);
  }

  .modal-header-brand,
  .modal-body-brand,
  .modal-footer-brand {
    padding: var(--space-4);
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .sidebar-brand,
  .navbar-brand,
  .btn-brand,
  .modal-brand {
    display: none !important;
  }

  .card-brand {
    box-shadow: none;
    border: 1px solid #000;
  }

  body {
    background: white !important;
    color: black !important;
  }
}

/* ===== COMPONENT STYLES (from components.css) ===== */

/* Course Components */
.education_block_grid {
  transition: all 0.3s ease;
  border-radius: var(--radius-lg);
  overflow: hidden;
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
}

.education_block_grid:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
  border-color: var(--color-primary);
}

.education_block_grid .card-img-top {
  height: 200px;
  object-fit: cover;
}

.education_block_grid .bl-title {
  height: 50px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  color: var(--color-text-primary);
  font-weight: var(--font-weight-semibold);
}

.education_block_footer {
  border-top: 1px solid var(--color-border-light);
  padding: var(--space-4);
  background-color: var(--color-background-secondary);
}

.education_block_time {
  font-size: var(--font-size-sm);
  color: var(--color-text-tertiary);
}

.star-rating-interactive {
  cursor: pointer;
  color: var(--color-warning);
}

/* Instructor Components */
.instructor-card {
  transition: all 0.3s ease;
  border-radius: var(--radius-xl);
  overflow: hidden;
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
}

.instructor-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
  border-color: var(--color-primary);
}

.instructor-avatar {
  border: 3px solid var(--brand-white);
  box-shadow: var(--shadow-md);
}

.instructor-bio {
  height: 60px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  color: var(--color-text-secondary);
}

.ribbon {
  width: 150px;
  height: 150px;
  overflow: hidden;
  position: absolute;
}

.ribbon span {
  position: absolute;
  display: block;
  width: 225px;
  padding: var(--space-2) 0;
  background-color: var(--color-primary);
  box-shadow: var(--shadow-sm);
  color: var(--brand-white);
  font-size: var(--font-size-xs);
  text-transform: uppercase;
  text-align: center;
  font-weight: var(--font-weight-semibold);
}

.ribbon-top-right {
  top: -10px;
  right: -10px;
}

.ribbon-top-right span {
  left: -25px;
  top: 30px;
  transform: rotate(45deg);
}

/* Forum Components */
.forum-topic-card {
  transition: all 0.2s ease;
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
}

.forum-topic-card:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--color-border-strong);
}

.participant-name-badge {
  position: absolute;
  top: var(--space-2);
  left: var(--space-2);
  background-color: rgba(0, 0, 0, 0.7);
  color: var(--brand-white);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.forum-comment .solution-badge {
  position: absolute;
  top: -10px;
  right: var(--space-5);
  z-index: 1;
}

.comment-content {
  word-break: break-word;
  color: var(--color-text-primary);
}

.comment-actions {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
}

/* Meeting Components */
.meeting-card {
  transition: all 0.2s ease;
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
}

.meeting-card:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--color-primary);
}

.pulse-badge {
  animation: pulseGlow 1.5s infinite;
}

/* Video Conference Components */
.video-conference-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 56px);
  position: relative;
  background-color: var(--brand-black);
}

.video-area {
  flex: 1;
  background-color: var(--brand-gray-dark);
  overflow: hidden;
  position: relative;
}

.meeting-controls {
  height: 80px;
  background-color: var(--brand-gray-medium);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-4);
}

.control-btn {
  width: 40px;
  height: 40px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-full);
  border: none;
  background-color: var(--color-surface);
  color: var(--color-text-primary);
  transition: all 0.2s ease;
}

.control-btn:hover {
  background-color: var(--color-primary);
  color: var(--brand-white);
  transform: scale(1.1);
}

.participants-grid {
  display: grid;
  gap: var(--space-2);
  padding: var(--space-2);
  height: 100%;
}

.grid-2x2 {
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
}

.grid-3x3 {
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
}

.grid-4x4 {
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(4, 1fr);
}

.participant-container {
  position: relative;
  border-radius: var(--radius-lg);
  overflow: hidden;
  background-color: var(--brand-gray-medium);
}

.video-container {
  width: 100%;
  height: 100%;
  background-color: var(--brand-gray-medium);
  position: relative;
}

.video-placeholder, .no-video-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--brand-white);
  font-size: var(--font-size-lg);
}

.participant-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.7);
  color: var(--brand-white);
  padding: var(--space-2);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sidebar-panel {
  position: absolute;
  top: 0;
  right: 0;
  width: 300px;
  height: calc(100% - 80px);
  background-color: var(--color-surface);
  z-index: var(--z-dropdown);
  border-left: 1px solid var(--color-border);
}

.screen-share-layout .screen-share-main {
  padding: var(--space-2);
}

.screen-content {
  width: 100%;
  height: 100%;
  background-color: var(--brand-gray-medium);
  position: relative;
  border-radius: var(--radius-lg);
}

.screen-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--brand-white);
  font-size: var(--font-size-xl);
}

/* Rich Text Editor */
.rich-text-editor .editor-content:empty:before {
  content: attr(data-placeholder);
  color: var(--color-text-tertiary);
  font-style: italic;
}

.rich-text-editor .editor-content:focus {
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
  border-color: var(--color-primary);
  outline: none;
}

.rich-text-editor .editor-content h1,
.rich-text-editor .editor-content h2,
.rich-text-editor .editor-content h3 {
  margin-top: var(--space-2);
  margin-bottom: var(--space-2);
  color: var(--color-text-primary);
}

.rich-text-editor .editor-content p {
  margin-bottom: var(--space-2);
  color: var(--color-text-secondary);
}

.rich-text-editor .editor-content ul,
.rich-text-editor .editor-content ol {
  margin-bottom: var(--space-2);
  padding-left: var(--space-8);
  color: var(--color-text-secondary);
}

.rich-text-editor .editor-content img {
  max-width: 100%;
  height: auto;
  border-radius: var(--radius-md);
}

.rich-text-editor .editor-content a {
  color: var(--color-primary);
  text-decoration: underline;
}

.rich-text-editor .editor-content a:hover {
  color: var(--color-primary-hover);
}

.rich-text-editor .editor-toolbar {
  border-bottom: 1px solid var(--color-border);
  background-color: var(--color-background-secondary);
  padding: var(--space-2);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.rich-text-editor .editor-toolbar .btn-group {
  margin-right: var(--space-2);
}

/* User Profile Dashboard */
.dashboard-navbar-parent {
  background: linear-gradient(45deg, var(--color-primary), var(--brand-red-secondary));
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.dashboard-navbar {
  color: var(--brand-white);
  padding: var(--space-6);
}

.d-user-avater {
  padding: var(--space-8) 0;
  text-align: center;
}

.d-navigation .nav-link {
  color: rgba(255, 255, 255, 0.8);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md);
  margin-bottom: var(--space-2);
  transition: all 0.3s ease;
  display: block;
  text-decoration: none;
  font-weight: var(--font-weight-medium);
}

.d-navigation .nav-link:hover,
.d-navigation .nav-link.active {
  background-color: rgba(255, 255, 255, 0.15);
  color: var(--brand-white);
  transform: translateX(5px);
}

.d-navigation .nav-link.active {
  font-weight: var(--font-weight-semibold);
  background-color: rgba(255, 255, 255, 0.2);
}

/* ===== ENHANCED RESPONSIVE SYSTEM (from responsive.css) ===== */

/* Additional Breakpoints */
:root {
  /* Enhanced Breakpoints */
  --breakpoint-xs: 320px;
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-xxl: 1600px;

  /* Container max-widths */
  --container-sm: 540px;
  --container-md: 720px;
  --container-lg: 960px;
  --container-xl: 1140px;
  --container-xxl: 1320px;

  /* Header heights */
  --header-height-mobile: 56px;
  --header-height-tablet: 64px;
  --header-height-desktop: 72px;

  /* Responsive spacing */
  --space-mobile-xs: 0.25rem;
  --space-mobile-sm: 0.5rem;
  --space-mobile-md: 0.75rem;
  --space-mobile-lg: 1rem;
  --space-mobile-xl: 1.5rem;

  --space-tablet-xs: 0.5rem;
  --space-tablet-sm: 0.75rem;
  --space-tablet-md: 1rem;
  --space-tablet-lg: 1.5rem;
  --space-tablet-xl: 2rem;

  --space-desktop-xs: 0.75rem;
  --space-desktop-sm: 1rem;
  --space-desktop-md: 1.5rem;
  --space-desktop-lg: 2rem;
  --space-desktop-xl: 3rem;
}

/* Enhanced Container System */
.container-responsive {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--space-4);
  padding-right: var(--space-4);
}

@media (min-width: 576px) {
  .container-responsive {
    max-width: var(--container-sm);
    padding-left: var(--space-6);
    padding-right: var(--space-6);
  }
}

@media (min-width: 768px) {
  .container-responsive {
    max-width: var(--container-md);
    padding-left: var(--space-8);
    padding-right: var(--space-8);
  }
}

@media (min-width: 992px) {
  .container-responsive {
    max-width: var(--container-lg);
  }
}

@media (min-width: 1200px) {
  .container-responsive {
    max-width: var(--container-xl);
    padding-left: var(--space-10);
    padding-right: var(--space-10);
  }
}

@media (min-width: 1600px) {
  .container-responsive {
    max-width: var(--container-xxl);
    padding-left: var(--space-12);
    padding-right: var(--space-12);
  }
}

/* Enhanced Header System */
.header-responsive {
  position: sticky;
  top: 0;
  z-index: var(--z-fixed);
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--color-border);
  transition: all 0.3s ease;
  animation: slideInFromTop 0.6s ease-out;
}

.header-responsive.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: var(--shadow-lg);
  border-bottom-color: var(--color-border-strong);
}

.dark .header-responsive {
  background: rgba(26, 26, 26, 0.95);
}

.dark .header-responsive.scrolled {
  background: rgba(26, 26, 26, 0.98);
}

/* Enhanced Navigation */
.nav-link-enhanced {
  position: relative;
  overflow: hidden;
  color: var(--color-text-secondary);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
}

.nav-link-enhanced::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--color-primary), var(--brand-red-secondary));
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.nav-link-enhanced:hover,
.nav-link-enhanced.active {
  color: var(--color-primary);
  background-color: var(--color-primary-light);
}

.nav-link-enhanced:hover::before,
.nav-link-enhanced.active::before {
  width: 80%;
}

/* Enhanced Button System */
.btn-gradient {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--brand-red-secondary) 100%);
  position: relative;
  overflow: hidden;
  color: var(--brand-white);
  border: none;
  font-weight: var(--font-weight-semibold);
  transition: all 0.3s ease;
}

.btn-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
  transition: left 0.5s;
}

.btn-gradient:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-brand);
}

.btn-gradient:hover::before {
  left: 100%;
}

/* Mobile Menu System */
.mobile-menu-panel {
  animation: slideInFromRight 0.3s ease-out;
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
}

.mobile-menu-item {
  animation: fadeInStagger 0.4s ease-out forwards;
  opacity: 0;
  padding: var(--space-3);
  border-bottom: 1px solid var(--color-border-light);
}

.mobile-menu-item:nth-child(1) { animation-delay: 0.1s; }
.mobile-menu-item:nth-child(2) { animation-delay: 0.15s; }
.mobile-menu-item:nth-child(3) { animation-delay: 0.2s; }
.mobile-menu-item:nth-child(4) { animation-delay: 0.25s; }
.mobile-menu-item:nth-child(5) { animation-delay: 0.3s; }

.mobile-menu-item:last-child {
  border-bottom: none;
}

/* Logo System */
.logo-responsive {
  height: 32px;
  width: auto;
  transition: all 0.3s ease;
}

.logo-hover-effect {
  position: relative;
  overflow: hidden;
}

.logo-hover-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s;
}

.logo-hover-effect:hover::before {
  left: 100%;
}

/* Action Buttons */
.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: none;
  background: transparent;
  color: var(--color-text-tertiary);
  cursor: pointer;
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: var(--color-surface-hover);
  color: var(--color-primary);
  transform: scale(1.1);
}

.action-btn:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Auth Buttons */
.auth-btn {
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-lg);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.auth-btn.login {
  color: var(--color-text-primary);
  background: transparent;
  border-color: var(--color-border);
}

.auth-btn.login:hover {
  background: var(--color-surface-hover);
  border-color: var(--color-primary);
  color: var(--color-primary);
}

.auth-btn.signup {
  color: var(--brand-white);
  background: var(--color-primary);
}

.auth-btn.signup:hover {
  background: var(--color-primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-brand);
}

/* ===== ENHANCED ANIMATIONS ===== */
@keyframes slideInFromTop {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeInStagger {
  from {
    transform: translateX(20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* ===== RESPONSIVE UTILITIES ===== */

/* Responsive Text */
.text-responsive {
  font-size: var(--font-size-sm);
}

@media (min-width: 768px) {
  .text-responsive {
    font-size: var(--font-size-base);
  }
}

@media (min-width: 992px) {
  .text-responsive {
    font-size: var(--font-size-lg);
  }
}

/* Responsive Spacing */
.p-responsive {
  padding: var(--space-mobile-md);
}

@media (min-width: 768px) {
  .p-responsive {
    padding: var(--space-tablet-md);
  }
}

@media (min-width: 992px) {
  .p-responsive {
    padding: var(--space-desktop-md);
  }
}

/* Responsive Grid */
.grid-responsive {
  display: grid;
  gap: var(--space-4);
  grid-template-columns: 1fr;
}

@media (min-width: 768px) {
  .grid-responsive {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-6);
  }
}

@media (min-width: 992px) {
  .grid-responsive {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-8);
  }
}

@media (min-width: 1200px) {
  .grid-responsive {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Visibility Utilities */
.hide-mobile {
  display: none;
}

.hide-tablet {
  display: block;
}

.hide-desktop {
  display: block;
}

@media (min-width: 768px) {
  .hide-mobile {
    display: block;
  }

  .hide-tablet {
    display: none;
  }

  .logo-responsive {
    height: 40px;
  }

  .action-btn {
    width: 40px;
    height: 40px;
  }
}

@media (min-width: 992px) {
  .hide-desktop {
    display: none;
  }

  .logo-responsive {
    height: 44px;
  }

  .action-btn {
    width: 44px;
    height: 44px;
  }
}

@media (min-width: 1200px) {
  .logo-responsive {
    height: 48px;
  }
}

/* ===== ACCESSIBILITY & PERFORMANCE ===== */

/* Focus styles for accessibility */
.action-btn:focus,
.auth-btn:focus,
.nav-link-enhanced:focus,
.mobile-menu-btn:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .header-responsive {
    border-bottom-width: 2px;
  }

  .action-btn,
  .mobile-menu-btn {
    border: 1px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Safe area support for mobile devices */
@supports (padding: max(0px)) {
  .header-responsive {
    padding-left: max(var(--space-4), env(safe-area-inset-left));
    padding-right: max(var(--space-4), env(safe-area-inset-right));
  }

  .footer-responsive {
    padding-left: max(var(--space-4), env(safe-area-inset-left));
    padding-right: max(var(--space-4), env(safe-area-inset-right));
    padding-bottom: max(var(--space-4), env(safe-area-inset-bottom));
  }
}

/* ===== LEGACY COMPATIBILITY ===== */

/* Ant Design dropdown fixes */
.ant-dropdown {
  z-index: 99999 !important;
}

.ant-dropdown-menu {
  z-index: 99999 !important;
  position: fixed !important;
}

/* Ensure header doesn't clip dropdowns */
header {
  overflow: visible !important;
}

header .container {
  overflow: visible !important;
}

.ant-dropdown-trigger {
  position: relative;
}

.ant-dropdown-placement-bottomLeft,
.ant-dropdown-placement-bottomRight {
  z-index: 99999 !important;
}

/* ===== ENHANCED TABLE SYSTEM ===== */

/* Table Container */
.table-brand-container {
  position: relative;
  background: var(--color-surface);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  border: 1px solid var(--color-border);
}

.table-brand-scroll-container {
  position: relative;
  overflow: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--color-border-strong) var(--color-background-secondary);
}

/* Custom Scrollbar Styling */
.table-brand-scroll-container::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.table-brand-scroll-container::-webkit-scrollbar-track {
  background: var(--color-background-secondary);
  border-radius: var(--radius-sm);
}

.table-brand-scroll-container::-webkit-scrollbar-thumb {
  background: var(--color-border-strong);
  border-radius: var(--radius-sm);
  transition: background 0.2s ease;
}

.table-brand-scroll-container::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary);
}

/* Enhanced Table Styles */
.table-brand-enhanced {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background-color: var(--color-surface);
  font-size: var(--font-size-sm);
}

.table-brand-enhanced th {
  background: linear-gradient(135deg, var(--color-background-secondary), var(--color-background-tertiary));
  color: var(--color-text-primary);
  font-weight: var(--font-weight-semibold);
  padding: var(--space-4) var(--space-6);
  text-align: left;
  border-bottom: 2px solid var(--color-border);
  position: sticky;
  top: 0;
  z-index: 10;
  white-space: nowrap;
}

.table-brand-enhanced td {
  padding: var(--space-4) var(--space-6);
  border-bottom: 1px solid var(--color-border-light);
  color: var(--color-text-secondary);
  vertical-align: middle;
}

.table-brand-enhanced tbody tr {
  transition: all 0.2s ease;
}

.table-brand-enhanced tbody tr:hover {
  background-color: var(--color-surface-hover);
  transform: scale(1.01);
}

.table-brand-enhanced tbody tr:last-child td {
  border-bottom: none;
}

/* Table Actions */
.table-action-btn {
  @apply btn-brand;
  padding: var(--space-1) var(--space-2);
  font-size: var(--font-size-xs);
  min-width: auto;
}

.table-action-btn.edit {
  background-color: var(--color-info);
  color: var(--brand-white);
}

.table-action-btn.delete {
  background-color: var(--color-error);
  color: var(--brand-white);
}

.table-action-btn.view {
  background-color: var(--color-success);
  color: var(--brand-white);
}

/* Table Status Badges */
.table-status-badge {
  @apply badge-brand;
  font-size: var(--font-size-xs);
  padding: var(--space-1) var(--space-2);
}

.table-status-badge.active {
  background-color: var(--color-success);
  color: var(--brand-white);
}

.table-status-badge.inactive {
  background-color: var(--color-text-tertiary);
  color: var(--brand-white);
}

.table-status-badge.pending {
  background-color: var(--color-warning);
  color: var(--brand-white);
}

.table-status-badge.error {
  background-color: var(--color-error);
  color: var(--brand-white);
}

/* Responsive Table */
@media (max-width: 768px) {
  .table-brand-enhanced {
    font-size: var(--font-size-xs);
  }

  .table-brand-enhanced th,
  .table-brand-enhanced td {
    padding: var(--space-2) var(--space-3);
  }

  .table-action-btn {
    padding: var(--space-1);
    font-size: 10px;
  }
}
