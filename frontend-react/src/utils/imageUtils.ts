import { environment } from '../config/environment';

/**
 * Utility functions for handling image URLs and media files
 */

/**
 * Format image URL from hashname
 * @param hashname - The hashname/filename from the API
 * @returns Complete URL for the image file
 */
export function formatImageUrl(hashname: string): string {
  if (!hashname) return '';

  // If already a complete URL, return as-is
  if (hashname.startsWith('http://') || hashname.startsWith('https://')) {
    return hashname;
  }

  // Remove leading slash if present to avoid double slashes
  const cleanHashname = hashname.startsWith('/') ? hashname.substring(1) : hashname;

  // Backend serves static files directly from the root
  // The hashname should contain the full path (e.g., "public/upload/courses/covers/filename.jpg")
  // or just the filename if it's in the root upload directory

  // For development: http://localhost:3200/[hashname]
  // For production: https://api.brainmaker.academy/[hashname]
  return `${environment.path}/${cleanHashname}`;
}

/**
 * Get image URL with fallback
 * @param hashname - The hashname/filename from the API
 * @param fallback - Fallback URL if hashname is empty
 * @returns Complete URL for the image file or fallback
 */
export function getImageUrl(hashname?: string, fallback?: string): string {
  if (hashname) {
    return formatImageUrl(hashname);
  }
  return fallback || '';
}

/**
 * Check if a string is a valid image URL
 * @param url - URL to check
 * @returns True if the URL appears to be an image
 */
export function isImageUrl(url: string): boolean {
  if (!url) return false;
  
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg', '.bmp'];
  const lowerUrl = url.toLowerCase();
  
  return imageExtensions.some(ext => lowerUrl.includes(ext));
}

/**
 * Get optimized image URL with size parameters
 * @param hashname - The hashname/filename from the API
 * @param width - Desired width
 * @param height - Desired height
 * @returns Optimized image URL
 */
export function getOptimizedImageUrl(hashname: string, width?: number, height?: number): string {
  const baseUrl = formatImageUrl(hashname);
  
  if (!width && !height) return baseUrl;
  
  const params = new URLSearchParams();
  if (width) params.append('w', width.toString());
  if (height) params.append('h', height.toString());
  
  return `${baseUrl}?${params.toString()}`;
}

/**
 * Generate placeholder image URL
 * @param width - Image width
 * @param height - Image height
 * @param text - Placeholder text
 * @returns Placeholder image URL
 */
export function getPlaceholderImageUrl(width: number = 300, height: number = 200, text?: string): string {
  const placeholderText = text || `${width}x${height}`;
  return `https://via.placeholder.com/${width}x${height}/e5e7eb/6b7280?text=${encodeURIComponent(placeholderText)}`;
}

/**
 * Extract filename from hashname or URL
 * @param hashname - The hashname or URL
 * @returns Filename without path
 */
export function extractFilename(hashname: string): string {
  if (!hashname) return '';
  
  // Remove URL parameters
  const cleanUrl = hashname.split('?')[0];
  
  // Extract filename from path
  const parts = cleanUrl.split('/');
  return parts[parts.length - 1];
}

/**
 * Get file extension from hashname or URL
 * @param hashname - The hashname or URL
 * @returns File extension (e.g., 'jpg', 'png')
 */
export function getFileExtension(hashname: string): string {
  const filename = extractFilename(hashname);
  const parts = filename.split('.');
  return parts.length > 1 ? parts[parts.length - 1].toLowerCase() : '';
}
