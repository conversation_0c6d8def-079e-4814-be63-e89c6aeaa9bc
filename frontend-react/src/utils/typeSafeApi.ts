import { z } from 'zod';
import { withRetry, handleError, createAppError } from './errorHandling';
import { validateApiResponse } from './validation';
import type { RetryConfig } from './errorHandling';
import type { ValidationResult } from './validation';
import apiService from '../services/api';

// Type-safe API configuration
export interface TypeSafeApiConfig {
  retry?: Partial<RetryConfig>;
  validateResponse?: boolean;
  showErrors?: boolean;
  context?: Record<string, any>;
}

// Default configuration
const DEFAULT_CONFIG: TypeSafeApiConfig = {
  retry: {
    maxAttempts: 3,
    baseDelay: 1000,
    retryCondition: (error) => (error.status && error.status >= 500) || error.code === 'NETWORK_ERROR'
  },
  validateResponse: true,
  showErrors: false,
  context: {}
};

// Type-safe API wrapper class
export class TypeSafeApi {
  private config: TypeSafeApiConfig;

  constructor(config: Partial<TypeSafeApiConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  /**
   * Type-safe GET request
   */
  async get<T>(
    url: string,
    schema: z.ZodSchema<T>,
    config: Partial<TypeSafeApiConfig> = {}
  ): Promise<T> {
    const mergedConfig = { ...this.config, ...config };
    
    return withRetry(async () => {
      try {
        const response = await apiService.get(url);
        
        if (mergedConfig.validateResponse) {
          const validated = validateApiResponse(schema, response, {
            logErrors: true
          });
          
          if (validated === null) {
            throw createAppError('Response validation failed', {
              code: 'VALIDATION_ERROR',
              details: { url, response }
            });
          }
          
          return validated;
        }
        
        return response as T;
      } catch (error) {
        throw handleError(error, {
          context: { method: 'GET', url, ...mergedConfig.context },
          showToast: mergedConfig.showErrors
        });
      }
    }, mergedConfig.retry || {});
  }

  /**
   * Type-safe POST request
   */
  async post<TRequest, TResponse>(
    url: string,
    data: TRequest,
    responseSchema: z.ZodSchema<TResponse>,
    requestSchema?: z.ZodSchema<TRequest>,
    config: Partial<TypeSafeApiConfig> = {}
  ): Promise<TResponse> {
    const mergedConfig = { ...this.config, ...config };
    
    // Validate request data if schema provided
    if (requestSchema) {
      const requestValidation = requestSchema.safeParse(data);
      if (!requestValidation.success) {
        throw createAppError('Request validation failed', {
          code: 'VALIDATION_ERROR',
          details: requestValidation.error.errors
        });
      }
    }

    return withRetry(async () => {
      try {
        const response = await apiService.post(url, data);
        
        if (mergedConfig.validateResponse) {
          const validated = validateApiResponse(responseSchema, response, {
            logErrors: true
          });
          
          if (validated === null) {
            throw createAppError('Response validation failed', {
              code: 'VALIDATION_ERROR',
              details: { url, response }
            });
          }
          
          return validated;
        }
        
        return response as TResponse;
      } catch (error) {
        throw handleError(error, {
          context: { method: 'POST', url, data, ...mergedConfig.context },
          showToast: mergedConfig.showErrors
        });
      }
    }, mergedConfig.retry || {});
  }

  /**
   * Type-safe PUT request
   */
  async put<TRequest, TResponse>(
    url: string,
    data: TRequest,
    responseSchema: z.ZodSchema<TResponse>,
    requestSchema?: z.ZodSchema<TRequest>,
    config: Partial<TypeSafeApiConfig> = {}
  ): Promise<TResponse> {
    const mergedConfig = { ...this.config, ...config };
    
    // Validate request data if schema provided
    if (requestSchema) {
      const requestValidation = requestSchema.safeParse(data);
      if (!requestValidation.success) {
        throw createAppError('Request validation failed', {
          code: 'VALIDATION_ERROR',
          details: requestValidation.error.errors
        });
      }
    }

    return withRetry(async () => {
      try {
        const response = await apiService.put(url, data);
        
        if (mergedConfig.validateResponse) {
          const validated = validateApiResponse(responseSchema, response, {
            logErrors: true
          });
          
          if (validated === null) {
            throw createAppError('Response validation failed', {
              code: 'VALIDATION_ERROR',
              details: { url, response }
            });
          }
          
          return validated;
        }
        
        return response as TResponse;
      } catch (error) {
        throw handleError(error, {
          context: { method: 'PUT', url, data, ...mergedConfig.context },
          showToast: mergedConfig.showErrors
        });
      }
    }, mergedConfig.retry || {});
  }

  /**
   * Type-safe DELETE request
   */
  async delete<T>(
    url: string,
    responseSchema: z.ZodSchema<T>,
    config: Partial<TypeSafeApiConfig> = {}
  ): Promise<T> {
    const mergedConfig = { ...this.config, ...config };
    
    return withRetry(async () => {
      try {
        const response = await apiService.delete(url);
        
        if (mergedConfig.validateResponse) {
          const validated = validateApiResponse(responseSchema, response, {
            logErrors: true
          });
          
          if (validated === null) {
            throw createAppError('Response validation failed', {
              code: 'VALIDATION_ERROR',
              details: { url, response }
            });
          }
          
          return validated;
        }
        
        return response as T;
      } catch (error) {
        throw handleError(error, {
          context: { method: 'DELETE', url, ...mergedConfig.context },
          showToast: mergedConfig.showErrors
        });
      }
    }, mergedConfig.retry || {});
  }

  /**
   * Batch requests with type safety
   */
  async batch<T>(
    requests: Array<{
      method: 'GET' | 'POST' | 'PUT' | 'DELETE';
      url: string;
      data?: any;
      schema: z.ZodSchema<T>;
    }>,
    config: Partial<TypeSafeApiConfig> = {}
  ): Promise<T[]> {
    const mergedConfig = { ...this.config, ...config };
    
    return withRetry(async () => {
      try {
        const promises = requests.map(async (request) => {
          switch (request.method) {
            case 'GET':
              return this.get(request.url, request.schema, { ...mergedConfig, showErrors: false });
            case 'POST':
              return this.post(request.url, request.data, request.schema, undefined, { ...mergedConfig, showErrors: false });
            case 'PUT':
              return this.put(request.url, request.data, request.schema, undefined, { ...mergedConfig, showErrors: false });
            case 'DELETE':
              return this.delete(request.url, request.schema, { ...mergedConfig, showErrors: false });
            default:
              throw createAppError(`Unsupported method: ${request.method}`, {
                code: 'INVALID_METHOD'
              });
          }
        });

        return Promise.all(promises);
      } catch (error) {
        throw handleError(error, {
          context: { method: 'BATCH', requestCount: requests.length, ...mergedConfig.context },
          showToast: mergedConfig.showErrors
        });
      }
    }, mergedConfig.retry || {});
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<TypeSafeApiConfig>): void {
    this.config = { ...this.config, ...config };
  }
}

// Default instance
export const typeSafeApi = new TypeSafeApi();

// Utility functions for common patterns
export const createTypeSafeService = (baseConfig: Partial<TypeSafeApiConfig> = {}) => {
  return new TypeSafeApi(baseConfig);
};

// Type-safe pagination helper
export interface PaginatedRequest {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export const createPaginatedSchema = <T>(itemSchema: z.ZodSchema<T>) => {
  return z.object({
    data: z.array(itemSchema),
    total: z.number(),
    page: z.number(),
    limit: z.number(),
    totalPages: z.number(),
    hasNext: z.boolean(),
    hasPrev: z.boolean(),
  });
};

// Type-safe query builder
export class QueryBuilder {
  private params: Record<string, any> = {};

  where(field: string, value: any): this {
    this.params[field] = value;
    return this;
  }

  limit(count: number): this {
    this.params.limit = count;
    return this;
  }

  offset(count: number): this {
    this.params.offset = count;
    return this;
  }

  orderBy(field: string, direction: 'asc' | 'desc' = 'asc'): this {
    this.params.sortBy = field;
    this.params.sortOrder = direction;
    return this;
  }

  search(term: string): this {
    this.params.search = term;
    return this;
  }

  build(): string {
    const searchParams = new URLSearchParams();
    
    Object.entries(this.params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value));
      }
    });

    return searchParams.toString();
  }

  reset(): this {
    this.params = {};
    return this;
  }
}

export const query = () => new QueryBuilder();
