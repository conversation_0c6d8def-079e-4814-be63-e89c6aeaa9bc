import React, { useEffect, useRef, useCallback } from 'react';

// Focus management utilities
export function useFocusTrap(isActive: boolean = true) {
  const containerRef = useRef<HTMLElement>(null);

  useEffect(() => {
    if (!isActive || !containerRef.current) return;

    const container = containerRef.current;
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          lastElement?.focus();
          e.preventDefault();
        }
      } else {
        if (document.activeElement === lastElement) {
          firstElement?.focus();
          e.preventDefault();
        }
      }
    };

    const handleEscapeKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        const closeButton = container.querySelector('[data-close-button]') as HTMLElement;
        closeButton?.click();
      }
    };

    container.addEventListener('keydown', handleTabKey);
    container.addEventListener('keydown', handleEscapeKey);

    // Focus first element when trap becomes active
    firstElement?.focus();

    return () => {
      container.removeEventListener('keydown', handleTabKey);
      container.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isActive]);

  return containerRef;
}

// Auto-focus hook
export function useAutoFocus(shouldFocus: boolean = true, delay: number = 0) {
  const elementRef = useRef<HTMLElement>(null);

  useEffect(() => {
    if (shouldFocus && elementRef.current) {
      const focusElement = () => {
        elementRef.current?.focus();
      };

      if (delay > 0) {
        const timeoutId = setTimeout(focusElement, delay);
        return () => clearTimeout(timeoutId);
      } else {
        focusElement();
      }
    }
  }, [shouldFocus, delay]);

  return elementRef;
}

// Focus restoration hook
export function useFocusRestore() {
  const previousActiveElement = useRef<HTMLElement | null>(null);

  const saveFocus = useCallback(() => {
    previousActiveElement.current = document.activeElement as HTMLElement;
  }, []);

  const restoreFocus = useCallback(() => {
    if (previousActiveElement.current) {
      previousActiveElement.current.focus();
      previousActiveElement.current = null;
    }
  }, []);

  return { saveFocus, restoreFocus };
}

// Keyboard navigation hook
export function useKeyboardNavigation(
  items: any[],
  onSelect: (index: number) => void,
  isActive: boolean = true
) {
  const [activeIndex, setActiveIndex] = React.useState(-1);

  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (!isActive) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setActiveIndex(prev => (prev + 1) % items.length);
        break;
      case 'ArrowUp':
        e.preventDefault();
        setActiveIndex(prev => (prev - 1 + items.length) % items.length);
        break;
      case 'Enter':
      case ' ':
        e.preventDefault();
        if (activeIndex >= 0) {
          onSelect(activeIndex);
        }
        break;
      case 'Home':
        e.preventDefault();
        setActiveIndex(0);
        break;
      case 'End':
        e.preventDefault();
        setActiveIndex(items.length - 1);
        break;
    }
  }, [items.length, activeIndex, onSelect, isActive]);

  useEffect(() => {
    if (isActive) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [handleKeyDown, isActive]);

  return { activeIndex, setActiveIndex };
}

// Screen reader announcements
export function useScreenReader() {
  const announce = useCallback((message: string, priority: 'polite' | 'assertive' = 'polite') => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', priority);
    announcement.setAttribute('aria-atomic', 'true');
    announcement.setAttribute('class', 'sr-only');
    announcement.textContent = message;

    document.body.appendChild(announcement);

    // Remove after announcement
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  }, []);

  return { announce };
}

// Reduced motion detection
export function useReducedMotion() {
  const [prefersReducedMotion, setPrefersReducedMotion] = React.useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (e: MediaQueryListEvent) => {
      setPrefersReducedMotion(e.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return prefersReducedMotion;
}

// High contrast detection
export function useHighContrast() {
  const [prefersHighContrast, setPrefersHighContrast] = React.useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-contrast: high)');
    setPrefersHighContrast(mediaQuery.matches);

    const handleChange = (e: MediaQueryListEvent) => {
      setPrefersHighContrast(e.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return prefersHighContrast;
}

// ARIA utilities
export const ariaUtils = {
  // Generate unique IDs for ARIA relationships
  generateId: (prefix: string = 'aria') => {
    return `${prefix}-${Math.random().toString(36).substr(2, 9)}`;
  },

  // Create ARIA describedby relationships
  createDescribedBy: (ids: string[]) => {
    return ids.filter(Boolean).join(' ');
  },

  // Create ARIA labelledby relationships
  createLabelledBy: (ids: string[]) => {
    return ids.filter(Boolean).join(' ');
  },

  // Get ARIA attributes for form fields
  getFormFieldAria: (options: {
    id: string;
    labelId?: string;
    descriptionId?: string;
    errorId?: string;
    required?: boolean;
    invalid?: boolean;
  }) => {
    const describedBy = ariaUtils.createDescribedBy([
      options.descriptionId,
      options.errorId
    ].filter(Boolean) as string[]);

    return {
      id: options.id,
      'aria-labelledby': options.labelId,
      'aria-describedby': describedBy || undefined,
      'aria-required': options.required,
      'aria-invalid': options.invalid,
    };
  },
};

// Color contrast utilities
export const colorUtils = {
  // Calculate relative luminance
  getLuminance: (r: number, g: number, b: number) => {
    const [rs, gs, bs] = [r, g, b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });
    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
  },

  // Calculate contrast ratio
  getContrastRatio: (color1: [number, number, number], color2: [number, number, number]) => {
    const lum1 = colorUtils.getLuminance(...color1);
    const lum2 = colorUtils.getLuminance(...color2);
    const brightest = Math.max(lum1, lum2);
    const darkest = Math.min(lum1, lum2);
    return (brightest + 0.05) / (darkest + 0.05);
  },

  // Check if contrast meets WCAG standards
  meetsWCAG: (color1: [number, number, number], color2: [number, number, number], level: 'AA' | 'AAA' = 'AA') => {
    const ratio = colorUtils.getContrastRatio(color1, color2);
    return level === 'AA' ? ratio >= 4.5 : ratio >= 7;
  },
};

// Skip link utility (to be used in TSX files)
export const createSkipLinkProps = (href: string, className: string = '') => ({
  href,
  className: `
    sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4
    bg-blue-600 text-white px-4 py-2 rounded-md z-50
    focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
    ${className}
  `.trim(),
});

// Visually hidden utility
export const createVisuallyHiddenProps = () => ({
  className: 'sr-only',
});

// Focus indicator utility
export const createFocusIndicatorProps = (className: string = '') => ({
  className: `focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2 rounded ${className}`.trim(),
});

// Accessibility testing utilities
export const a11yTest = {
  // Check for missing alt text
  checkImages: () => {
    const images = document.querySelectorAll('img');
    const missingAlt = Array.from(images).filter(img => !img.alt);
    if (missingAlt.length > 0) {
      console.warn('Images missing alt text:', missingAlt);
    }
    return missingAlt.length === 0;
  },

  // Check for proper heading hierarchy
  checkHeadings: () => {
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    const levels = Array.from(headings).map(h => parseInt(h.tagName.charAt(1)));
    
    for (let i = 1; i < levels.length; i++) {
      if (levels[i] > levels[i - 1] + 1) {
        console.warn('Heading hierarchy issue: skipped from h' + levels[i - 1] + ' to h' + levels[i]);
        return false;
      }
    }
    return true;
  },

  // Check for form labels
  checkFormLabels: () => {
    const inputs = document.querySelectorAll('input, select, textarea');
    const unlabeled = Array.from(inputs).filter(input => {
      const id = input.id;
      const ariaLabel = input.getAttribute('aria-label');
      const ariaLabelledBy = input.getAttribute('aria-labelledby');
      const label = id ? document.querySelector(`label[for="${id}"]`) : null;
      
      return !ariaLabel && !ariaLabelledBy && !label;
    });
    
    if (unlabeled.length > 0) {
      console.warn('Form elements missing labels:', unlabeled);
    }
    return unlabeled.length === 0;
  },
};
