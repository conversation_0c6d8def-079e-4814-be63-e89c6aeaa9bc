import { toast } from 'sonner';

// Safe JSON stringify to handle cyclic references
const safeStringify = (obj: any): string => {
  const seen = new WeakSet();
  return JSON.stringify(obj, (key, val) => {
    if (val != null && typeof val === 'object') {
      if (seen.has(val)) {
        return '[Circular]';
      }
      seen.add(val);
    }
    return val;
  });
};

// Error throttling to prevent spam
const errorThrottle = new Map<string, number>();
const THROTTLE_DURATION = 5000; // 5 seconds

const shouldThrottleError = (errorKey: string): boolean => {
  const now = Date.now();
  const lastLogged = errorThrottle.get(errorKey);

  if (!lastLogged || now - lastLogged > THROTTLE_DURATION) {
    errorThrottle.set(errorKey, now);
    return false;
  }

  return true;
};

// Enhanced notification system
export interface NotificationOptions {
  title?: string;
  description?: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
  persistent?: boolean;
  type?: 'success' | 'error' | 'warning' | 'info' | 'loading';
}

export const notify = {
  success: (message: string, options?: Omit<NotificationOptions, 'type'>) => {
    toast.success(options?.title || message, {
      description: options?.description,
      duration: options?.duration,
      action: options?.action,
    });
  },

  error: (message: string, options?: Omit<NotificationOptions, 'type'>) => {
    toast.error(options?.title || message, {
      description: options?.description,
      duration: options?.persistent ? Infinity : options?.duration,
      action: options?.action,
    });
  },

  warning: (message: string, options?: Omit<NotificationOptions, 'type'>) => {
    toast.warning(options?.title || message, {
      description: options?.description,
      duration: options?.duration,
      action: options?.action,
    });
  },

  info: (message: string, options?: Omit<NotificationOptions, 'type'>) => {
    toast.info(options?.title || message, {
      description: options?.description,
      duration: options?.duration,
      action: options?.action,
    });
  },

  loading: (message: string, options?: Omit<NotificationOptions, 'type'>) => {
    return toast.loading(options?.title || message, {
      description: options?.description,
    });
  },

  promise: <T>(
    promise: Promise<T>,
    messages: {
      loading: string;
      success: string | ((data: T) => string);
      error: string | ((error: any) => string);
    }
  ) => {
    return toast.promise(promise, messages);
  },

  // Enhanced notification methods
  apiSuccess: (action: string, options?: Omit<NotificationOptions, 'type'>) => {
    notify.success(`${action} successful`, {
      description: options?.description || `The ${action.toLowerCase()} was completed successfully.`,
      ...options
    });
  },

  apiError: (action: string, error: any, options?: Omit<NotificationOptions, 'type'>) => {
    const message = error?.message || `Failed to ${action.toLowerCase()}`;
    notify.error(`${action} failed`, {
      description: message,
      persistent: true,
      action: {
        label: 'Retry',
        onClick: () => window.location.reload()
      },
      ...options
    });
  },

  formValidation: (message: string, field?: string) => {
    notify.warning('Validation Error', {
      description: field ? `${field}: ${message}` : message,
      duration: 5000
    });
  },

  networkError: (options?: Omit<NotificationOptions, 'type'>) => {
    notify.error('Network Error', {
      description: 'Unable to connect to the server. Please check your internet connection.',
      persistent: true,
      action: {
        label: 'Retry',
        onClick: () => window.location.reload()
      },
      ...options
    });
  },

  maintenance: (options?: Omit<NotificationOptions, 'type'>) => {
    notify.warning('Maintenance Mode', {
      description: 'The system is currently under maintenance. Some features may be unavailable.',
      persistent: true,
      ...options
    });
  },

  sessionExpired: (options?: Omit<NotificationOptions, 'type'>) => {
    notify.warning('Session Expired', {
      description: 'Your session has expired. Please log in again.',
      persistent: true,
      action: {
        label: 'Login',
        onClick: () => window.location.href = '/auth/login'
      },
      ...options
    });
  }
};

// Enhanced error types
export interface AppError extends Error {
  code?: string;
  status?: number;
  details?: any;
  retryable?: boolean;
  timestamp?: number;
  context?: Record<string, any>;
}

export interface RetryConfig {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffFactor: number;
  retryCondition?: (error: AppError) => boolean;
}

export interface ErrorHandlerConfig {
  showToast?: boolean;
  logError?: boolean;
  reportError?: boolean;
  fallbackValue?: any;
  context?: Record<string, any>;
}

// Default retry configuration
export const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxAttempts: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  backoffFactor: 2,
  retryCondition: (error) => {
    // Retry on network errors, timeouts, and 5xx server errors
    return !!(
      error.code === 'NETWORK_ERROR' ||
      error.code === 'TIMEOUT' ||
      error.code === 'ECONNABORTED' ||
      (error.status && error.status >= 500)
    );
  }
};

// Enhanced error creation
export const createAppError = (
  message: string,
  options: Partial<AppError> = {}
): AppError => {
  const error = new Error(message) as AppError;
  error.code = options.code;
  error.status = options.status;
  error.details = options.details;
  error.retryable = options.retryable ?? false;
  error.timestamp = Date.now();
  error.context = options.context;
  return error;
};

// Transform various error types to AppError
export const normalizeError = (error: any, context?: Record<string, any>): AppError => {
  if (error instanceof Error && 'code' in error) {
    const appError = error as AppError;
    return {
      ...appError,
      context: { ...appError.context, ...context },
      timestamp: appError.timestamp || Date.now()
    } as AppError;
  }

  if (error?.response) {
    // Axios error
    return createAppError(
      error.response.data?.message || error.message || 'Request failed',
      {
        code: error.code,
        status: error.response.status,
        details: error.response.data,
        retryable: error.response.status >= 500 || error.code === 'NETWORK_ERROR',
        context
      }
    );
  }

  if (typeof error === 'string') {
    return createAppError(error, { context });
  }

  return createAppError('An unexpected error occurred', {
    details: error,
    context
  });
};

// Exponential backoff delay calculation
export const calculateDelay = (attempt: number, config: RetryConfig): number => {
  const delay = config.baseDelay * Math.pow(config.backoffFactor, attempt - 1);
  return Math.min(delay, config.maxDelay);
};

// Sleep utility for delays
export const sleep = (ms: number): Promise<void> => 
  new Promise(resolve => setTimeout(resolve, ms));

// Retry mechanism with exponential backoff
export const withRetry = async <T>(
  operation: () => Promise<T>,
  config: Partial<RetryConfig> = {}
): Promise<T> => {
  const retryConfig = { ...DEFAULT_RETRY_CONFIG, ...config };
  let lastError: AppError;

  for (let attempt = 1; attempt <= retryConfig.maxAttempts; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = normalizeError(error, { attempt, maxAttempts: retryConfig.maxAttempts });
      
      // Don't retry if it's the last attempt or error is not retryable
      if (attempt === retryConfig.maxAttempts || 
          !retryConfig.retryCondition?.(lastError)) {
        throw lastError;
      }

      // Wait before retrying
      const delay = calculateDelay(attempt, retryConfig);
      console.warn(`Retry attempt ${attempt}/${retryConfig.maxAttempts} after ${delay}ms:`, lastError.message);
      await sleep(delay);
    }
  }

  throw lastError!;
};

// Circuit breaker pattern
export class CircuitBreaker {
  private failures = 0;
  private lastFailureTime = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';

  constructor(
    private threshold: number = 5,
    private resetTimeout: number = 30000
  ) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.resetTimeout) {
        this.state = 'HALF_OPEN';
      } else {
        throw createAppError('Circuit breaker is OPEN', { code: 'CIRCUIT_BREAKER_OPEN' });
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess(): void {
    this.failures = 0;
    this.state = 'CLOSED';
  }

  private onFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();
    
    if (this.failures >= this.threshold) {
      this.state = 'OPEN';
    }
  }

  getState(): string {
    return this.state;
  }
}

// Global error handler
export const handleError = (
  error: any,
  config: ErrorHandlerConfig = {}
): AppError => {
  const normalizedError = normalizeError(error, config.context);

  // Create error key for throttling
  const errorKey = `${normalizedError.code || 'UNKNOWN'}_${normalizedError.status || 'NO_STATUS'}_${normalizedError.message}`;

  // Log error with throttling and cyclic object protection
  if (config.logError !== false && !shouldThrottleError(errorKey)) {
    try {
      console.error('Error handled:', {
        message: normalizedError.message,
        code: normalizedError.code,
        status: normalizedError.status,
        context: safeStringify(normalizedError.context),
        stack: normalizedError.stack
      });
    } catch (logError) {
      // Fallback logging if there are cyclic references
      console.error('Error handled (simplified):', {
        message: normalizedError.message,
        code: normalizedError.code,
        status: normalizedError.status
      });
    }
  }

  // Show toast notification
  if (config.showToast !== false) {
    const message = getUserFriendlyMessage(normalizedError);
    notify.error(message, {
      description: normalizedError.code ? `Error code: ${normalizedError.code}` : undefined,
      action: normalizedError.retryable ? {
        label: 'Retry',
        onClick: () => window.location.reload()
      } : undefined,
      persistent: !!(normalizedError.status && normalizedError.status >= 500)
    });
  }

  // Report error to monitoring service
  if (config.reportError !== false) {
    reportErrorToService(normalizedError);
  }

  return normalizedError;
};

// Convert technical errors to user-friendly messages
export const getUserFriendlyMessage = (error: AppError): string => {
  const errorMessages: Record<string, string> = {
    'NETWORK_ERROR': 'Unable to connect to the server. Please check your internet connection.',
    'TIMEOUT': 'The request took too long to complete. Please try again.',
    'ECONNABORTED': 'The connection was interrupted. Please try again.',
    'CIRCUIT_BREAKER_OPEN': 'Service is temporarily unavailable. Please try again later.',
    'VALIDATION_ERROR': 'Please check your input and try again.',
    'UNAUTHORIZED': 'You need to log in to access this feature.',
    'FORBIDDEN': 'You don\'t have permission to perform this action.',
    'NOT_FOUND': 'The requested resource was not found.',
    'CONFLICT': 'This action conflicts with existing data.',
    'RATE_LIMITED': 'Too many requests. Please wait a moment and try again.'
  };

  if (error.code && errorMessages[error.code]) {
    return errorMessages[error.code];
  }

  if (error.status) {
    if (error.status >= 500) {
      return 'A server error occurred. Our team has been notified.';
    }
    if (error.status >= 400) {
      return error.message || 'There was a problem with your request.';
    }
  }

  return error.message || 'An unexpected error occurred. Please try again.';
};

// Report error to monitoring service
const reportErrorToService = (error: AppError): void => {
  // Only report in production
  if (import.meta.env.DEV) return;

  try {
    // Send to analytics/monitoring service
    if (typeof gtag !== 'undefined') {
      gtag('event', 'exception', {
        description: error.message,
        fatal: false,
        custom_map: {
          error_code: error.code,
          error_status: error.status,
          error_context: JSON.stringify(error.context)
        }
      });
    }

    // Could also send to Sentry, LogRocket, etc.
  } catch (reportingError) {
    console.warn('Failed to report error:', reportingError);
  }
};

// Graceful degradation helper
export const withFallback = async <T>(
  operation: () => Promise<T>,
  fallback: T,
  config: ErrorHandlerConfig = {}
): Promise<T> => {
  try {
    return await operation();
  } catch (error) {
    handleError(error, { ...config, showToast: false });
    return fallback;
  }
};

declare global {
  function gtag(...args: any[]): void;
}
