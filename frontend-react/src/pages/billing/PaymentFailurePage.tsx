import React from 'react';
import { <PERSON>, useNavigate } from 'react-router-dom';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  XCircle, 
  ArrowLeft, 
  Mail, 
  HelpCircle,
  RefreshCw
} from 'lucide-react';

const PaymentFailurePage: React.FC = () => {
  const navigate = useNavigate();

  const commonIssues = [
    'Insufficient funds in your account',
    'Card has expired or been declined',
    'Incorrect card details entered',
    'Bank security restrictions',
    'Network connection issues'
  ];

  const solutions = [
    'Check your card details and try again',
    'Try a different payment method',
    'Contact your bank to authorize the transaction',
    'Use a different card or PayPal',
    'Contact our support team for assistance'
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Failure Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-red-500 rounded-full mb-6">
            <XCircle className="h-10 w-10 text-white" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Payment Failed
          </h1>
          <p className="text-lg text-gray-600 mb-8">
            We couldn't process your payment. Please try again or use a different payment method.
          </p>

          <Alert className="mb-8 max-w-2xl mx-auto">
            <HelpCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Don't worry!</strong> Your card has not been charged. We'll help you resolve this issue quickly.
            </AlertDescription>
          </Alert>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button onClick={() => navigate(-1)} className="bg-red-600 hover:bg-red-700 text-white">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Try Again
            </Button>
            <Link to="/contact">
              <Button variant="outline">
                <Mail className="mr-2 h-4 w-4" />
                Contact Support
              </Button>
            </Link>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <HelpCircle className="h-5 w-5 text-red-600" />
                Possible Reasons
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {commonIssues.map((issue, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <span className="text-red-600 mt-1">•</span>
                    <span className="text-gray-700">{issue}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <RefreshCw className="h-5 w-5 text-green-600" />
                What You Can Do
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {solutions.map((solution, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <span className="text-green-600 mt-1">•</span>
                    <span className="text-gray-700">{solution}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default PaymentFailurePage;
