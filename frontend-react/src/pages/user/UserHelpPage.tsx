import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  HelpCircle, 
  Search, 
  BookOpen, 
  MessageCircle, 
  Video, 
  FileText,
  ExternalLink,
  ChevronRight
} from 'lucide-react';

const UserHelpPage: React.FC = () => {
  const helpCategories = [
    {
      title: "Getting Started",
      description: "Learn the basics of using BrainMaker",
      icon: <BookOpen className="w-6 h-6" />,
      articles: 12,
      color: "bg-blue-500"
    },
    {
      title: "Course Management",
      description: "How to enroll, access, and manage your courses",
      icon: <Video className="w-6 h-6" />,
      articles: 8,
      color: "bg-green-500"
    },
    {
      title: "Account & Billing",
      description: "Manage your account settings and billing",
      icon: <FileText className="w-6 h-6" />,
      articles: 6,
      color: "bg-purple-500"
    },
    {
      title: "Technical Support",
      description: "Troubleshooting and technical issues",
      icon: <HelpCircle className="w-6 h-6" />,
      articles: 15,
      color: "bg-red-500"
    }
  ];

  const popularArticles = [
    "How to enroll in a course",
    "Accessing course materials",
    "Managing your profile",
    "Payment and billing questions",
    "Technical requirements",
    "Certificate completion"
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold text-gray-900">Help Center</h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Find answers to your questions and get the help you need
        </p>
        
        {/* Search */}
        <div className="max-w-md mx-auto relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input 
            placeholder="Search for help articles..." 
            className="pl-10 pr-4 py-2"
          />
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-6 text-center">
            <MessageCircle className="w-8 h-8 text-blue-500 mx-auto mb-3" />
            <h3 className="font-semibold mb-2">Contact Support</h3>
            <p className="text-sm text-gray-600 mb-4">Get help from our support team</p>
            <Button variant="outline" size="sm">
              Start Chat
            </Button>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-6 text-center">
            <Video className="w-8 h-8 text-green-500 mx-auto mb-3" />
            <h3 className="font-semibold mb-2">Video Tutorials</h3>
            <p className="text-sm text-gray-600 mb-4">Watch step-by-step guides</p>
            <Button variant="outline" size="sm">
              Watch Now
            </Button>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-6 text-center">
            <FileText className="w-8 h-8 text-purple-500 mx-auto mb-3" />
            <h3 className="font-semibold mb-2">Documentation</h3>
            <p className="text-sm text-gray-600 mb-4">Browse detailed guides</p>
            <Button variant="outline" size="sm">
              Read Docs
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Help Categories */}
      <div>
        <h2 className="text-2xl font-bold mb-6">Browse by Category</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {helpCategories.map((category, index) => (
            <Card key={index} className="cursor-pointer hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start space-x-4">
                  <div className={`${category.color} text-white p-3 rounded-lg`}>
                    {category.icon}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-semibold text-lg">{category.title}</h3>
                      <ChevronRight className="w-5 h-5 text-gray-400" />
                    </div>
                    <p className="text-gray-600 mb-3">{category.description}</p>
                    <Badge variant="secondary">
                      {category.articles} articles
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Popular Articles */}
      <div>
        <h2 className="text-2xl font-bold mb-6">Popular Articles</h2>
        <Card>
          <CardContent className="p-6">
            <div className="space-y-3">
              {popularArticles.map((article, index) => (
                <div key={index} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                  <span className="text-gray-700 hover:text-red-600 cursor-pointer">
                    {article}
                  </span>
                  <ExternalLink className="w-4 h-4 text-gray-400" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Contact Section */}
      <Card className="bg-gray-50">
        <CardContent className="p-6 text-center">
          <h3 className="text-xl font-semibold mb-2">Still need help?</h3>
          <p className="text-gray-600 mb-4">
            Can't find what you're looking for? Our support team is here to help.
          </p>
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button>
              <MessageCircle className="w-4 h-4 mr-2" />
              Contact Support
            </Button>
            <Button variant="outline">
              <Video className="w-4 h-4 mr-2" />
              Schedule Call
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default UserHelpPage;
