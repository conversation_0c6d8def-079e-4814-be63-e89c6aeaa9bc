import React, { useState, useEffect } from 'react';
import { Bell, Mail, Smartphone, Settings, Clock, Shield } from 'lucide-react';
import SEOHelmet from '../../components/common/SEOHelmet';
import { useAuth } from '../../contexts/AuthContext';
import { notificationsService } from '../../services/notifications.service';
import type { NotificationPreferences } from '../../types/notifications.types';
import { toast } from 'sonner';

const UserNotificationSettingsPage: React.FC = () => {
  const { authState } = useAuth();
  const user = authState.user;

  const [preferences, setPreferences] = useState<NotificationPreferences | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    loadPreferences();
  }, [user?.Id]);

  const loadPreferences = async () => {
    if (!user?.Id) return;

    try {
      setLoading(true);
      const userPreferences = await notificationsService.getUserPreferences(user.Id);
      setPreferences(userPreferences);
    } catch (error) {
      console.error('Error loading notification preferences:', error);
      toast.error('Failed to load notification preferences');
      // Set default preferences if loading fails
      setPreferences({
        Id: 0,
        UserId: user.Id,
        EmailNotifications: true,
        PushNotifications: true,
        SmsNotifications: false,
        Categories: {
          courses: { email: true, push: true, sms: false },
          messages: { email: true, push: true, sms: false },
          achievements: { email: true, push: true, sms: false },
          reminders: { email: true, push: true, sms: false },
          social: { email: false, push: true, sms: false },
          billing: { email: true, push: false, sms: false },
        },
        QuietHours: {
          enabled: false,
          startTime: '22:00',
          endTime: '08:00',
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        },
        Frequency: 'immediate',
        UpdatedAt: new Date().toISOString(),
      });
    } finally {
      setLoading(false);
    }
  };

  const handlePreferenceChange = (key: keyof NotificationPreferences, value: any) => {
    if (!preferences) return;

    setPreferences(prev => ({
      ...prev!,
      [key]: value
    }));
    setHasChanges(true);
  };

  const handleCategoryChange = (category: string, type: 'email' | 'push' | 'sms', value: boolean) => {
    if (!preferences) return;

    setPreferences(prev => ({
      ...prev!,
      Categories: {
        ...prev!.Categories,
        [category]: {
          ...prev!.Categories[category],
          [type]: value
        }
      }
    }));
    setHasChanges(true);
  };

  const handleQuietHoursChange = (key: keyof NotificationPreferences['QuietHours'], value: any) => {
    if (!preferences) return;

    setPreferences(prev => ({
      ...prev!,
      QuietHours: {
        ...prev!.QuietHours,
        [key]: value
      }
    }));
    setHasChanges(true);
  };

  const savePreferences = async () => {
    if (!user?.Id || !preferences) return;

    try {
      setSaving(true);
      await notificationsService.updateUserPreferences(user.Id, preferences);
      toast.success('Notification preferences saved successfully');
      setHasChanges(false);
    } catch (error) {
      console.error('Error saving notification preferences:', error);
      toast.error('Failed to save notification preferences');
    } finally {
      setSaving(false);
    }
  };

  const resetToDefaults = async () => {
    if (!user?.Id) return;

    const defaultPreferences: NotificationPreferences = {
      Id: preferences?.Id || 0,
      UserId: user.Id,
      EmailNotifications: true,
      PushNotifications: true,
      SmsNotifications: false,
      Categories: {
        courses: { email: true, push: true, sms: false },
        messages: { email: true, push: true, sms: false },
        achievements: { email: true, push: true, sms: false },
        reminders: { email: true, push: true, sms: false },
        social: { email: false, push: true, sms: false },
        billing: { email: true, push: false, sms: false },
      },
      QuietHours: {
        enabled: false,
        startTime: '22:00',
        endTime: '08:00',
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      },
      Frequency: 'immediate',
      UpdatedAt: new Date().toISOString(),
    };

    setPreferences(defaultPreferences);
    setHasChanges(true);
    toast.info('Preferences reset to defaults');
  };

  if (loading) {
    return (
      <div className="p-6 max-w-4xl mx-auto">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Loading notification preferences...</span>
        </div>
      </div>
    );
  }

  if (!preferences) {
    return (
      <div className="p-6 max-w-4xl mx-auto">
        <div className="text-center py-12">
          <Bell className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Unable to load preferences</h3>
          <p className="text-gray-500 mb-4">There was an error loading your notification preferences.</p>
          <button
            onClick={loadPreferences}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <SEOHelmet
        title="Notification Settings - BrainMaker Academy"
        description="Manage your notification preferences and settings."
        keywords="notification settings, preferences, email notifications, push notifications, BrainMaker Academy"
      />

      <div className="p-6 max-w-4xl mx-auto">
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <Bell className="w-8 h-8 text-blue-600" />
            <h1 className="text-3xl font-bold">Notification Settings</h1>
          </div>
          <p className="text-gray-600 text-base">
            Customize how and when you receive notifications to stay informed without being overwhelmed.
          </p>
        </div>

        <div className="space-y-6">
          {/* General Notification Methods */}
          <div className="bg-white rounded-lg border p-6">
            <div className="flex items-center gap-2 mb-4">
              <Settings className="w-5 h-5 text-gray-500" />
              <h3 className="text-lg font-semibold">Notification Methods</h3>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between py-3 border-b">
                <div className="flex items-center gap-3">
                  <Mail className="w-5 h-5 text-gray-500" />
                  <div>
                    <div className="font-medium">Email Notifications</div>
                    <div className="text-sm text-gray-500">Receive notifications via email</div>
                  </div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    className="sr-only peer"
                    checked={preferences.EmailNotifications}
                    onChange={(e) => handlePreferenceChange('EmailNotifications', e.target.checked)}
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>

              <div className="flex items-center justify-between py-3 border-b">
                <div className="flex items-center gap-3">
                  <Smartphone className="w-5 h-5 text-gray-500" />
                  <div>
                    <div className="font-medium">Push Notifications</div>
                    <div className="text-sm text-gray-500">Receive push notifications on your device</div>
                  </div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    className="sr-only peer"
                    checked={preferences.PushNotifications}
                    onChange={(e) => handlePreferenceChange('PushNotifications', e.target.checked)}
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>

              <div className="flex items-center justify-between py-3">
                <div className="flex items-center gap-3">
                  <Bell className="w-5 h-5 text-gray-500" />
                  <div>
                    <div className="font-medium">SMS Notifications</div>
                    <div className="text-sm text-gray-500">Receive important notifications via SMS</div>
                  </div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    className="sr-only peer"
                    checked={preferences.SmsNotifications}
                    onChange={(e) => handlePreferenceChange('SmsNotifications', e.target.checked)}
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>
            </div>
          </div>

          {/* Notification Categories */}
          <div className="bg-white rounded-lg border p-6">
            <div className="flex items-center gap-2 mb-4">
              <Shield className="w-5 h-5 text-gray-500" />
              <h3 className="text-lg font-semibold">Notification Categories</h3>
            </div>

            <div className="space-y-4">
              {Object.entries(preferences.Categories).map(([category, settings]) => (
                <div key={category} className="border-b pb-4 last:border-b-0">
                  <div className="font-medium mb-2 capitalize">{category}</div>
                  <div className="grid grid-cols-3 gap-4">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={settings.email}
                        onChange={(e) => handleCategoryChange(category, 'email', e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm">Email</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={settings.push}
                        onChange={(e) => handleCategoryChange(category, 'push', e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm">Push</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={settings.sms}
                        onChange={(e) => handleCategoryChange(category, 'sms', e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm">SMS</span>
                    </label>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Frequency & Quiet Hours */}
          <div className="bg-white rounded-lg border p-6">
            <div className="flex items-center gap-2 mb-4">
              <Clock className="w-5 h-5 text-gray-500" />
              <h3 className="text-lg font-semibold">Frequency & Timing</h3>
            </div>

            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Notification Frequency
                </label>
                <select
                  value={preferences.Frequency}
                  onChange={(e) => handlePreferenceChange('Frequency', e.target.value as any)}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="immediate">Immediate</option>
                  <option value="hourly">Hourly Digest</option>
                  <option value="daily">Daily Digest</option>
                  <option value="weekly">Weekly Digest</option>
                </select>
              </div>

              <div>
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <div className="font-medium">Quiet Hours</div>
                    <div className="text-sm text-gray-500">Disable notifications during specific hours</div>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      className="sr-only peer"
                      checked={preferences.QuietHours.enabled}
                      onChange={(e) => handleQuietHoursChange('enabled', e.target.checked)}
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>

                {preferences.QuietHours.enabled && (
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Start Time
                      </label>
                      <input
                        type="time"
                        value={preferences.QuietHours.startTime}
                        onChange={(e) => handleQuietHoursChange('startTime', e.target.value)}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        End Time
                      </label>
                      <input
                        type="time"
                        value={preferences.QuietHours.endTime}
                        onChange={(e) => handleQuietHoursChange('endTime', e.target.value)}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Save Button */}
          <div className="flex justify-between">
            <button
              type="button"
              className="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
              onClick={resetToDefaults}
            >
              Reset to Defaults
            </button>
            <button
              type="button"
              className={`px-6 py-2 rounded-md transition-colors ${
                hasChanges && !saving
                  ? 'bg-blue-600 text-white hover:bg-blue-700'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }`}
              disabled={!hasChanges || saving}
              onClick={savePreferences}
            >
              {saving ? 'Saving...' : 'Save Settings'}
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default UserNotificationSettingsPage;
