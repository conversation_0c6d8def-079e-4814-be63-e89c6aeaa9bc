import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { 
  MessageSquare, 
  Star, 
  ThumbsUp, 
  ThumbsDown, 
  Lightbulb,
  Bug,
  Heart,
  Send
} from 'lucide-react';

const UserFeedbackPage: React.FC = () => {
  const [feedbackType, setFeedbackType] = useState('general');
  const [rating, setRating] = useState(0);
  const [submitted, setSubmitted] = useState(false);

  const feedbackTypes = [
    {
      id: 'general',
      label: 'General Feedback',
      description: 'Share your overall experience',
      icon: <MessageSquare className="w-5 h-5" />,
      color: 'bg-blue-500'
    },
    {
      id: 'feature',
      label: 'Feature Request',
      description: 'Suggest new features or improvements',
      icon: <Lightbulb className="w-5 h-5" />,
      color: 'bg-yellow-500'
    },
    {
      id: 'bug',
      label: 'Bug Report',
      description: 'Report technical issues or bugs',
      icon: <Bug className="w-5 h-5" />,
      color: 'bg-red-500'
    },
    {
      id: 'compliment',
      label: 'Compliment',
      description: 'Share what you love about BrainMaker',
      icon: <Heart className="w-5 h-5" />,
      color: 'bg-green-500'
    }
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitted(true);
    // Here you would typically send the feedback to your backend
  };

  const renderStars = () => {
    return (
      <div className="flex space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            onClick={() => setRating(star)}
            className={`p-1 rounded transition-colors ${
              star <= rating ? 'text-yellow-400' : 'text-gray-300 hover:text-yellow-300'
            }`}
          >
            <Star className="w-6 h-6 fill-current" />
          </button>
        ))}
      </div>
    );
  };

  if (submitted) {
    return (
      <div className="max-w-2xl mx-auto">
        <Card className="text-center">
          <CardContent className="p-12">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <ThumbsUp className="w-8 h-8 text-green-600" />
            </div>
            <h2 className="text-2xl font-bold mb-2">Thank You!</h2>
            <p className="text-gray-600 mb-6">
              Your feedback has been submitted successfully. We appreciate you taking the time to help us improve BrainMaker.
            </p>
            <Button onClick={() => setSubmitted(false)}>
              Submit Another Feedback
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold text-gray-900">Share Your Feedback</h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Help us improve BrainMaker by sharing your thoughts, suggestions, and experiences
        </p>
      </div>

      {/* Feedback Type Selection */}
      <div>
        <h2 className="text-xl font-semibold mb-4">What type of feedback do you have?</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {feedbackTypes.map((type) => (
            <Card 
              key={type.id}
              className={`cursor-pointer transition-all ${
                feedbackType === type.id 
                  ? 'ring-2 ring-red-500 bg-red-50' 
                  : 'hover:shadow-md'
              }`}
              onClick={() => setFeedbackType(type.id)}
            >
              <CardContent className="p-4">
                <div className="flex items-start space-x-3">
                  <div className={`${type.color} text-white p-2 rounded-lg`}>
                    {type.icon}
                  </div>
                  <div>
                    <h3 className="font-semibold">{type.label}</h3>
                    <p className="text-sm text-gray-600">{type.description}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Feedback Form */}
      <Card>
        <CardHeader>
          <CardTitle>Your Feedback</CardTitle>
          <CardDescription>
            Please provide as much detail as possible to help us understand your feedback
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Rating */}
            <div className="space-y-2">
              <Label>Overall Rating</Label>
              <div className="flex items-center space-x-3">
                {renderStars()}
                <span className="text-sm text-gray-600">
                  {rating > 0 ? `${rating} out of 5 stars` : 'Click to rate'}
                </span>
              </div>
            </div>

            {/* Subject */}
            <div className="space-y-2">
              <Label htmlFor="subject">Subject</Label>
              <Input 
                id="subject"
                placeholder="Brief summary of your feedback"
                required
              />
            </div>

            {/* Message */}
            <div className="space-y-2">
              <Label htmlFor="message">Message</Label>
              <Textarea 
                id="message"
                placeholder="Please provide detailed feedback..."
                rows={6}
                required
              />
            </div>

            {/* Priority (for bug reports) */}
            {feedbackType === 'bug' && (
              <div className="space-y-2">
                <Label>Priority Level</Label>
                <Select value="medium" onValueChange={() => {}}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select priority level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low - Minor issue</SelectItem>
                    <SelectItem value="medium">Medium - Affects functionality</SelectItem>
                    <SelectItem value="high">High - Blocks important features</SelectItem>
                    <SelectItem value="critical">Critical - System unusable</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Contact Information */}
            <div className="space-y-2">
              <Label htmlFor="email">Email (Optional)</Label>
              <Input 
                id="email"
                type="email"
                placeholder="<EMAIL>"
              />
              <p className="text-xs text-gray-500">
                Provide your email if you'd like us to follow up on your feedback
              </p>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end">
              <Button type="submit" className="flex items-center space-x-2">
                <Send className="w-4 h-4" />
                <span>Submit Feedback</span>
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Additional Info */}
      <Card className="bg-blue-50">
        <CardContent className="p-6">
          <h3 className="font-semibold mb-2">💡 Tips for Great Feedback</h3>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• Be specific about what you experienced</li>
            <li>• Include steps to reproduce issues</li>
            <li>• Mention your browser and device if reporting bugs</li>
            <li>• Suggest solutions if you have ideas</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
};

export default UserFeedbackPage;
