import React, { useState } from 'react';
import { useN<PERSON><PERSON>, Link } from 'react-router-dom';
import { useCart } from '../../contexts/CartContext';
import { useAuth } from '../../contexts/AuthContext';
import { ShoppingCart, Trash2, ArrowRight, Loader2, ShoppingBag } from 'lucide-react';

// shadcn/ui components
import { Button } from '../../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '../../components/ui/avatar';
import { Separator } from '../../components/ui/separator';

const CartPage: React.FC = () => {
  const navigate = useNavigate();
  const { authState } = useAuth();
  const { cart, loading, removeFromCart, clearCart } = useCart();

  // Local loading states for individual operations
  const [removingItems, setRemovingItems] = useState<Set<number>>(new Set());
  const [clearingCart, setClearingCart] = useState(false);

  // Redirect to login if not authenticated
  if (!authState.isAuthenticated) {
    navigate('/auth/login', {
      state: { from: { pathname: '/cart' } }
    });
    return null;
  }

  const handleRemoveItem = async (itemId: number) => {
    setRemovingItems(prev => new Set(prev).add(itemId));
    try {
      await removeFromCart(itemId);
    } catch (error) {
      console.error('Error removing item:', error);
    } finally {
      setRemovingItems(prev => {
        const newSet = new Set(prev);
        newSet.delete(itemId);
        return newSet;
      });
    }
  };

  const handleClearCart = async () => {
    setClearingCart(true);
    try {
      await clearCart();
    } catch (error) {
      console.error('Error clearing cart:', error);
    } finally {
      setClearingCart(false);
    }
  };

  const handleCheckout = () => {
    navigate('/checkout');
  };

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-red-600" />
          <p className="text-gray-600">Loading your cart...</p>
        </div>
      </div>
    );
  }

  // Get cart items and total
  const cartItems = cart?.Items || [];
  const totalPrice = cart?.TotalPrice || 0;

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-6xl mx-auto px-6">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex flex-col items-center space-y-6">
            <Avatar className="w-20 h-20 bg-red-600">
              <ShoppingCart className="w-10 h-10 text-white" />
            </Avatar>
            <div className="space-y-2">
              <h1 className="text-4xl font-bold text-gray-900">Shopping Cart</h1>
              <p className="text-gray-600">Review your selected courses and proceed to checkout</p>
            </div>
          </div>
        </div>

        {/* Empty Cart */}
        {cartItems.length === 0 ? (
          <div className="text-center py-16">
            <ShoppingBag className="w-24 h-24 text-gray-300 mx-auto mb-6" />
            <h3 className="text-2xl font-semibold text-gray-700 mb-4">Your cart is empty</h3>
            <p className="text-gray-500 mb-8">Discover amazing courses and add them to your cart</p>
            <Link to="/courses">
              <Button className="bg-red-600 hover:bg-red-700 text-white px-8 py-3 text-lg">
                <ArrowRight className="w-5 h-5 mr-2" />
                Browse Courses
              </Button>
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Cart Items */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle className="text-xl">Cart Items ({cartItems.length})</CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleClearCart}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Clear All
                  </Button>
                </CardHeader>
                <CardContent className="space-y-4">
                  {cartItems.map((item: any) => (
                    <div key={item.Id} className="border rounded-lg p-4">
                      <div className="flex items-center space-x-4">
                        <Avatar className="w-16 h-16">
                          <AvatarImage src={item.Course?.CoverImage?.Hashname} alt={item.Course?.Title} />
                          <AvatarFallback>
                            <ShoppingCart className="w-8 h-8" />
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <h4 className="font-semibold text-gray-900">{item.Course?.Title || 'Course'}</h4>
                          <p className="text-sm text-gray-600">
                            by {item.Course?.CreatedBy?.Firstname} {item.Course?.CreatedBy?.Lastname}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-xl font-bold text-red-600">
                            ${(item.Price || item.Course?.Price || 0).toFixed(2)}
                          </p>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveItem(item.Id)}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>

            {/* Order Summary */}
            <div className="lg:col-span-1">
              <Card className="sticky top-6">
                <CardHeader>
                  <CardTitle className="text-xl">Order Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Subtotal:</span>
                      <span className="font-semibold">${totalPrice.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Tax:</span>
                      <span className="font-semibold">$0.00</span>
                    </div>
                    <Separator />
                    <div className="flex justify-between text-lg font-bold">
                      <span>Total:</span>
                      <span className="text-red-600">${totalPrice.toFixed(2)}</span>
                    </div>
                  </div>

                  <Button
                    onClick={handleCheckout}
                    className="w-full bg-red-600 hover:bg-red-700 text-white py-3 text-lg"
                  >
                    <ArrowRight className="w-5 h-5 mr-2" />
                    Proceed to Checkout
                  </Button>

                  <div className="text-center">
                    <Link to="/courses" className="text-red-600 hover:text-red-700 text-sm">
                      Continue Shopping
                    </Link>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CartPage;
