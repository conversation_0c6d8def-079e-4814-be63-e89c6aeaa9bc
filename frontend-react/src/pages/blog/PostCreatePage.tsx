import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { 
  FaPlus, 
  FaTrash, 
  FaSave, 
  FaEye, 
  FaImage, 
  FaVideo, 
  FaCode, 
  FaText,
  FaArrowUp,
  FaArrowDown
} from 'react-icons/fa';
import { Plus, Trash2, Save, Eye, Image, Video, Code, Type, ArrowUp, ArrowDown } from 'lucide-react';
import postService from '../../services/post.service';
import categoryService from '../../services/category.service';
import { useAuth } from '../../contexts/AuthContext';
import type { Post, PostSection, PostCreateRequest, PostUpdateRequest, PostCategory } from '../../models/post.model';
import { POST_CREATE_MODE, POST_EDIT_MODE } from '../../models/post.model';
import SEOHelmet from '../../components/common/SEOHelmet';

const PostCreatePage: React.FC = () => {
  const navigate = useNavigate();
  const { slug } = useParams<{ slug?: string }>();
  const { toast } = useToast();
  const { authState } = useAuth();

  // Form state
  const [post, setPost] = useState<Post>({
    Title: '',
    Content: '',
    Sections: [],
    KeyWords: '',
    Tags: [],
    Categories: [],
    Status: 'draft'
  });

  const [sections, setSections] = useState<PostSection[]>([]);
  const [categories, setCategories] = useState<PostCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [mode, setMode] = useState(POST_CREATE_MODE);
  const [tagInput, setTagInput] = useState('');

  // Check authentication
  useEffect(() => {
    if (!authState.isAuthenticated) {
      navigate('/auth/login', { 
        state: { from: { pathname: '/blog/create' } }
      });
    }
  }, [authState.isAuthenticated, navigate]);

  // Load categories
  useEffect(() => {
    const loadCategories = async () => {
      try {
        const categoriesData = await categoryService.getAll();
        setCategories(categoriesData);
      } catch (error) {
        console.error('Error loading categories:', error);
      }
    };
    loadCategories();
  }, []);

  // Load post for editing
  useEffect(() => {
    if (slug) {
      setMode(POST_EDIT_MODE);
      loadPost(slug);
    }
  }, [slug]);

  const loadPost = async (postSlug: string) => {
    try {
      setLoading(true);
      const postData = await postService.getBySlug(postSlug);
      setPost(postData);
      setSections(postData.Sections || []);
    } catch (error) {
      console.error('Error loading post:', error);
      toast({
        title: 'Error',
        description: 'Failed to load post for editing',
        variant: 'destructive'
      });
      navigate('/blog');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof Post, value: any) => {
    setPost(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addTag = () => {
    if (tagInput.trim() && !post.Tags?.includes(tagInput.trim())) {
      handleInputChange('Tags', [...(post.Tags || []), tagInput.trim()]);
      setTagInput('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    handleInputChange('Tags', post.Tags?.filter(tag => tag !== tagToRemove) || []);
  };

  const addSection = (type: PostSection['Type'] = 'text') => {
    const newSection: PostSection = {
      Title: '',
      Content: '',
      Type: type,
      Order: sections.length
    };
    setSections([...sections, newSection]);
  };

  const updateSection = (index: number, field: keyof PostSection, value: any) => {
    const updatedSections = sections.map((section, i) => 
      i === index ? { ...section, [field]: value } : section
    );
    setSections(updatedSections);
  };

  const removeSection = (index: number) => {
    setSections(sections.filter((_, i) => i !== index));
  };

  const moveSectionUp = (index: number) => {
    if (index > 0) {
      const newSections = [...sections];
      [newSections[index - 1], newSections[index]] = [newSections[index], newSections[index - 1]];
      setSections(newSections);
    }
  };

  const moveSectionDown = (index: number) => {
    if (index < sections.length - 1) {
      const newSections = [...sections];
      [newSections[index], newSections[index + 1]] = [newSections[index + 1], newSections[index]];
      setSections(newSections);
    }
  };

  const handleSave = async (status: 'draft' | 'published' = 'draft') => {
    if (!post.Title.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Post title is required',
        variant: 'destructive'
      });
      return;
    }

    try {
      setSaving(true);
      
      const postData = {
        ...post,
        Sections: sections.map((section, index) => ({
          ...section,
          Order: index
        })),
        Status: status
      };

      let savedPost: Post;
      if (mode === POST_EDIT_MODE && post.Slug) {
        savedPost = await postService.update(postData as PostUpdateRequest);
      } else {
        savedPost = await postService.create(postData as PostCreateRequest);
      }

      toast({
        title: 'Success',
        description: `Post ${status === 'published' ? 'published' : 'saved as draft'} successfully`,
      });

      navigate(`/blog/${savedPost.Slug}`);
    } catch (error) {
      console.error('Error saving post:', error);
      toast({
        title: 'Error',
        description: 'Failed to save post. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setSaving(false);
    }
  };

  const handlePreview = () => {
    // TODO: Implement preview functionality
    toast({
      title: 'Preview',
      description: 'Preview functionality coming soon',
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading post...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <SEOHelmet
        title={mode === POST_EDIT_MODE ? 'Edit Post' : 'Create New Post'}
        description="Create and publish blog posts with rich content sections"
      />

      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              {mode === POST_EDIT_MODE ? 'Edit Post' : 'Create New Post'}
            </h1>
            <p className="text-gray-600">
              {mode === POST_EDIT_MODE ? 'Update your post content and settings' : 'Share your knowledge with the community'}
            </p>
          </div>

          {/* Main Form */}
          <div className="space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="title">Title *</Label>
                  <Input
                    id="title"
                    value={post.Title}
                    onChange={(e) => handleInputChange('Title', e.target.value)}
                    placeholder="Enter post title..."
                    className="mt-1"
                  />
                </div>

                <div>
                  <Label htmlFor="keywords">Keywords</Label>
                  <Input
                    id="keywords"
                    value={post.KeyWords || ''}
                    onChange={(e) => handleInputChange('KeyWords', e.target.value)}
                    placeholder="SEO keywords, separated by commas..."
                    className="mt-1"
                  />
                </div>

                {/* Tags */}
                <div>
                  <Label>Tags</Label>
                  <div className="flex gap-2 mt-1">
                    <Input
                      value={tagInput}
                      onChange={(e) => setTagInput(e.target.value)}
                      placeholder="Add a tag..."
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                    />
                    <Button type="button" onClick={addTag} size="sm">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {post.Tags?.map((tag, index) => (
                      <Badge key={index} variant="secondary" className="flex items-center gap-1">
                        {tag}
                        <button
                          onClick={() => removeTag(tag)}
                          className="ml-1 hover:text-red-600"
                        >
                          ×
                        </button>
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Categories */}
                <div>
                  <Label>Categories</Label>
                  <Select
                    onValueChange={(value) => {
                      const categoryId = parseInt(value);
                      const category = categories.find(c => c.Id === categoryId);
                      if (category && !post.Categories?.find(c => c.Id === categoryId)) {
                        handleInputChange('Categories', [...(post.Categories || []), category]);
                      }
                    }}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select categories..." />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category.Id} value={category.Id?.toString() || ''}>
                          {category.Name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {post.Categories?.map((category, index) => (
                      <Badge key={index} variant="outline" className="flex items-center gap-1">
                        {category.Name}
                        <button
                          onClick={() => {
                            handleInputChange('Categories', 
                              post.Categories?.filter(c => c.Id !== category.Id) || []
                            );
                          }}
                          className="ml-1 hover:text-red-600"
                        >
                          ×
                        </button>
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Content Sections */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  Content Sections
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => addSection('text')}
                    >
                      <Type className="h-4 w-4 mr-1" />
                      Text
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => addSection('image')}
                    >
                      <Image className="h-4 w-4 mr-1" />
                      Image
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => addSection('code')}
                    >
                      <Code className="h-4 w-4 mr-1" />
                      Code
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {sections.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <Type className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p>No content sections yet. Add your first section above.</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {sections.map((section, index) => (
                      <div key={index} className="border rounded-lg p-4 bg-white">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-2">
                            {section.Type === 'text' && <Type className="h-4 w-4" />}
                            {section.Type === 'image' && <Image className="h-4 w-4" />}
                            {section.Type === 'code' && <Code className="h-4 w-4" />}
                            <span className="text-sm font-medium capitalize">{section.Type} Section</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => moveSectionUp(index)}
                              disabled={index === 0}
                            >
                              <ArrowUp className="h-4 w-4" />
                            </Button>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => moveSectionDown(index)}
                              disabled={index === sections.length - 1}
                            >
                              <ArrowDown className="h-4 w-4" />
                            </Button>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeSection(index)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>

                        <div className="space-y-3">
                          <Input
                            value={section.Title || ''}
                            onChange={(e) => updateSection(index, 'Title', e.target.value)}
                            placeholder="Section title (optional)..."
                          />
                          <Textarea
                            value={section.Content}
                            onChange={(e) => updateSection(index, 'Content', e.target.value)}
                            placeholder={`Enter ${section.Type} content...`}
                            rows={section.Type === 'code' ? 8 : 4}
                            className={section.Type === 'code' ? 'font-mono' : ''}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Actions */}
            <div className="flex justify-between items-center">
              <Button
                type="button"
                variant="outline"
                onClick={() => navigate('/blog')}
              >
                Cancel
              </Button>

              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handlePreview}
                >
                  <Eye className="h-4 w-4 mr-1" />
                  Preview
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => handleSave('draft')}
                  disabled={saving}
                >
                  <Save className="h-4 w-4 mr-1" />
                  Save Draft
                </Button>
                <Button
                  type="button"
                  onClick={() => handleSave('published')}
                  disabled={saving}
                >
                  {saving ? 'Publishing...' : 'Publish'}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default PostCreatePage;
