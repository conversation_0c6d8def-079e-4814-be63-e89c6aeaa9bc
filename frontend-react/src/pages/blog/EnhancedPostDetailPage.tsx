import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useToast } from '@/hooks/use-toast';
import { 
  Calendar, 
  User, 
  Tag, 
  Eye, 
  ArrowLeft, 
  Share2,
  ThumbsUp,
  ThumbsDown,
  MessageCircle,
  Star,
  Edit,
  Trash2,
  Reply
} from 'lucide-react';
import postService from '../../services/post.service';
import { useAuth } from '../../contexts/AuthContext';
import type { Post, PostComment, PostOpinion, PostRating } from '../../models/post.model';
import { LIKE_TYPE, DISLIKE_TYPE, VIEW_TYPE } from '../../models/post.model';
import SEOHelmet from '../../components/common/SEOHelmet';

const EnhancedPostDetailPage: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { authState } = useAuth();

  // State
  const [post, setPost] = useState<Post | null>(null);
  const [comments, setComments] = useState<PostComment[]>([]);
  const [userOpinion, setUserOpinion] = useState<PostOpinion | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [commentText, setCommentText] = useState('');
  const [replyingTo, setReplyingTo] = useState<number | null>(null);
  const [replyText, setReplyText] = useState('');
  const [submittingComment, setSubmittingComment] = useState(false);

  // Load post data
  useEffect(() => {
    if (slug) {
      loadPostData();
    }
  }, [slug]);

  // Track view when post loads
  useEffect(() => {
    if (post && authState.isAuthenticated) {
      trackView();
    }
  }, [post, authState.isAuthenticated]);

  const loadPostData = async () => {
    if (!slug) {
      setError('Post not found');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      
      // Load post, comments, and user opinion in parallel
      const [postData, commentsData, opinionData] = await Promise.all([
        postService.getBySlug(slug),
        postService.getComments(slug),
        authState.isAuthenticated ? postService.getUserOpinion(slug) : Promise.resolve(null)
      ]);

      setPost(postData);
      setComments(commentsData);
      setUserOpinion(opinionData);
      
    } catch (err) {
      console.error('Error loading post data:', err);
      setError('Failed to load post. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const trackView = async () => {
    if (!post?.Slug) return;
    
    try {
      await postService.addOpinion(post.Slug, VIEW_TYPE);
      setPost(prev => prev ? {
        ...prev,
        ViewsCount: (prev.ViewsCount || 0) + 1
      } : null);
    } catch (error) {
      console.error('Error tracking view:', error);
    }
  };

  // Opinion handlers
  const handleLike = async () => {
    if (!authState.isAuthenticated) {
      toast({
        title: 'Authentication Required',
        description: 'Please log in to like posts',
        variant: 'destructive'
      });
      return;
    }

    if (!post?.Slug) return;

    try {
      if (userOpinion?.Type === LIKE_TYPE) {
        await postService.removeOpinion(post.Slug, LIKE_TYPE);
        setUserOpinion(null);
        setPost(prev => prev ? {
          ...prev,
          LikesCount: Math.max(0, (prev.LikesCount || 0) - 1)
        } : null);
      } else {
        if (userOpinion?.Type === DISLIKE_TYPE) {
          await postService.removeOpinion(post.Slug, DISLIKE_TYPE);
          setPost(prev => prev ? {
            ...prev,
            DislikesCount: Math.max(0, (prev.DislikesCount || 0) - 1)
          } : null);
        }
        
        const opinion = await postService.addOpinion(post.Slug, LIKE_TYPE);
        setUserOpinion(opinion);
        setPost(prev => prev ? {
          ...prev,
          LikesCount: (prev.LikesCount || 0) + 1
        } : null);
      }
    } catch (error) {
      console.error('Error handling like:', error);
      toast({
        title: 'Error',
        description: 'Failed to update like. Please try again.',
        variant: 'destructive'
      });
    }
  };

  const handleDislike = async () => {
    if (!authState.isAuthenticated) {
      toast({
        title: 'Authentication Required',
        description: 'Please log in to dislike posts',
        variant: 'destructive'
      });
      return;
    }

    if (!post?.Slug) return;

    try {
      if (userOpinion?.Type === DISLIKE_TYPE) {
        await postService.removeOpinion(post.Slug, DISLIKE_TYPE);
        setUserOpinion(null);
        setPost(prev => prev ? {
          ...prev,
          DislikesCount: Math.max(0, (prev.DislikesCount || 0) - 1)
        } : null);
      } else {
        if (userOpinion?.Type === LIKE_TYPE) {
          await postService.removeOpinion(post.Slug, LIKE_TYPE);
          setPost(prev => prev ? {
            ...prev,
            LikesCount: Math.max(0, (prev.LikesCount || 0) - 1)
          } : null);
        }
        
        const opinion = await postService.addOpinion(post.Slug, DISLIKE_TYPE);
        setUserOpinion(opinion);
        setPost(prev => prev ? {
          ...prev,
          DislikesCount: (prev.DislikesCount || 0) + 1
        } : null);
      }
    } catch (error) {
      console.error('Error handling dislike:', error);
      toast({
        title: 'Error',
        description: 'Failed to update dislike. Please try again.',
        variant: 'destructive'
      });
    }
  };

  // Comment handlers
  const handleCommentSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!commentText.trim() || !post?.Slug) return;

    if (!authState.isAuthenticated) {
      toast({
        title: 'Authentication Required',
        description: 'Please log in to comment',
        variant: 'destructive'
      });
      return;
    }

    try {
      setSubmittingComment(true);
      const newComment = await postService.addComment(post.Slug, commentText.trim());
      setComments(prev => [newComment, ...prev]);
      setCommentText('');
      
      toast({
        title: 'Success',
        description: 'Comment added successfully',
      });
    } catch (error) {
      console.error('Error submitting comment:', error);
      toast({
        title: 'Error',
        description: 'Failed to submit comment. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setSubmittingComment(false);
    }
  };

  const handleReplySubmit = async (parentCommentId: number) => {
    if (!replyText.trim() || !post?.Slug) return;

    if (!authState.isAuthenticated) {
      toast({
        title: 'Authentication Required',
        description: 'Please log in to reply',
        variant: 'destructive'
      });
      return;
    }

    try {
      const reply = await postService.addComment(post.Slug, replyText.trim(), parentCommentId);
      
      setComments(prev => prev.map(comment => 
        comment.Id === parentCommentId 
          ? { ...comment, Replies: [...(comment.Replies || []), reply] }
          : comment
      ));
      
      setReplyText('');
      setReplyingTo(null);
      
      toast({
        title: 'Success',
        description: 'Reply added successfully',
      });
    } catch (error) {
      console.error('Error submitting reply:', error);
      toast({
        title: 'Error',
        description: 'Failed to submit reply. Please try again.',
        variant: 'destructive'
      });
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getUserInitials = (user?: { Firstname?: string; Lastname?: string }) => {
    if (!user) return 'U';
    return `${user.Firstname?.[0] || ''}${user.Lastname?.[0] || ''}`.toUpperCase() || 'U';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading post...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  if (!post) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <Alert>
            <AlertDescription>Post not found</AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  return (
    <>
      <SEOHelmet
        title={post.Title}
        description={post.Content?.substring(0, 160).replace(/<[^>]*>/g, '') || ''}
        keywords={(post.Tags || []).join(', ')}
        ogType="article"
      />

      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Back Button */}
          <div className="mb-6">
            <Link to="/blog">
              <Button variant="ghost" className="flex items-center gap-2">
                <ArrowLeft className="h-4 w-4" />
                Back to Blog
              </Button>
            </Link>
          </div>

          {/* Main Post */}
          <Card className="mb-8">
            <CardHeader>
              <div className="space-y-4">
                <h1 className="text-3xl font-bold text-gray-900">{post.Title}</h1>
                
                {/* Post Meta */}
                <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
                  <div className="flex items-center gap-1">
                    <User className="h-4 w-4" />
                    <span>{post.User?.Firstname} {post.User?.Lastname}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    <span>{formatDate(post.CreatedAt)}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Eye className="h-4 w-4" />
                    <span>{post.ViewsCount || 0} views</span>
                  </div>
                </div>

                {/* Tags */}
                {post.Tags && post.Tags.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {post.Tags.map((tag, index) => (
                      <Badge key={index} variant="secondary">
                        <Tag className="h-3 w-3 mr-1" />
                        {tag}
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
            </CardHeader>
            
            <CardContent>
              {/* Post Content */}
              <div className="prose max-w-none mb-6">
                {post.Sections && post.Sections.length > 0 ? (
                  post.Sections.map((section, index) => (
                    <div key={index} className="mb-6">
                      {section.Title && (
                        <h3 className="text-xl font-semibold mb-3">{section.Title}</h3>
                      )}
                      <div className="whitespace-pre-wrap">{section.Content}</div>
                    </div>
                  ))
                ) : (
                  <div className="whitespace-pre-wrap">{post.Content}</div>
                )}
              </div>

              {/* Post Actions */}
              <div className="flex items-center justify-between pt-6 border-t">
                <div className="flex items-center gap-4">
                  <Button
                    variant={userOpinion?.Type === LIKE_TYPE ? "default" : "outline"}
                    size="sm"
                    onClick={handleLike}
                    className="flex items-center gap-2"
                  >
                    <ThumbsUp className="h-4 w-4" />
                    {post.LikesCount || 0}
                  </Button>
                  
                  <Button
                    variant={userOpinion?.Type === DISLIKE_TYPE ? "default" : "outline"}
                    size="sm"
                    onClick={handleDislike}
                    className="flex items-center gap-2"
                  >
                    <ThumbsDown className="h-4 w-4" />
                    {post.DislikesCount || 0}
                  </Button>
                  
                  <Button variant="outline" size="sm" className="flex items-center gap-2">
                    <MessageCircle className="h-4 w-4" />
                    {comments.length} comments
                  </Button>
                </div>

                <div className="flex items-center gap-2">
                  {authState.user?.Slug === post.User?.Slug && (
                    <Link to={`/blog/edit/${post.Slug}`}>
                      <Button variant="outline" size="sm">
                        <Edit className="h-4 w-4 mr-1" />
                        Edit
                      </Button>
                    </Link>
                  )}
                  
                  <Button variant="outline" size="sm">
                    <Share2 className="h-4 w-4 mr-1" />
                    Share
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Comments Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageCircle className="h-5 w-5" />
                Comments ({comments.length})
              </CardTitle>
            </CardHeader>
            
            <CardContent>
              {/* Add Comment Form */}
              {authState.isAuthenticated ? (
                <form onSubmit={handleCommentSubmit} className="mb-6">
                  <Textarea
                    value={commentText}
                    onChange={(e) => setCommentText(e.target.value)}
                    placeholder="Write a comment..."
                    className="mb-3"
                    rows={3}
                  />
                  <Button 
                    type="submit" 
                    disabled={!commentText.trim() || submittingComment}
                  >
                    {submittingComment ? 'Posting...' : 'Post Comment'}
                  </Button>
                </form>
              ) : (
                <div className="mb-6 p-4 bg-gray-50 rounded-lg text-center">
                  <p className="text-gray-600 mb-2">Please log in to comment</p>
                  <Link to="/auth/login">
                    <Button variant="outline">Log In</Button>
                  </Link>
                </div>
              )}

              {/* Comments List */}
              <div className="space-y-4">
                {comments.length === 0 ? (
                  <p className="text-gray-500 text-center py-8">No comments yet. Be the first to comment!</p>
                ) : (
                  comments.map((comment) => (
                    <div key={comment.Id} className="border rounded-lg p-4">
                      <div className="flex items-start gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={comment.User?.Photo?.Hashname ? `http://localhost:3200/${comment.User.Photo.Hashname}` : undefined} />
                          <AvatarFallback>{getUserInitials(comment.User)}</AvatarFallback>
                        </Avatar>
                        
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="font-medium text-sm">
                              {comment.User?.Firstname} {comment.User?.Lastname}
                            </span>
                            <span className="text-xs text-gray-500">
                              {formatDate(comment.CreatedAt)}
                            </span>
                          </div>
                          
                          <p className="text-gray-700 mb-2">{comment.Content}</p>
                          
                          {authState.isAuthenticated && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setReplyingTo(comment.Id!)}
                              className="text-xs"
                            >
                              <Reply className="h-3 w-3 mr-1" />
                              Reply
                            </Button>
                          )}

                          {/* Reply Form */}
                          {replyingTo === comment.Id && (
                            <div className="mt-3 pl-4 border-l-2 border-gray-200">
                              <Textarea
                                value={replyText}
                                onChange={(e) => setReplyText(e.target.value)}
                                placeholder="Write a reply..."
                                className="mb-2"
                                rows={2}
                              />
                              <div className="flex gap-2">
                                <Button 
                                  size="sm"
                                  onClick={() => handleReplySubmit(comment.Id!)}
                                  disabled={!replyText.trim()}
                                >
                                  Reply
                                </Button>
                                <Button 
                                  variant="outline" 
                                  size="sm"
                                  onClick={() => {
                                    setReplyingTo(null);
                                    setReplyText('');
                                  }}
                                >
                                  Cancel
                                </Button>
                              </div>
                            </div>
                          )}

                          {/* Replies */}
                          {comment.Replies && comment.Replies.length > 0 && (
                            <div className="mt-3 pl-4 border-l-2 border-gray-200 space-y-3">
                              {comment.Replies.map((reply) => (
                                <div key={reply.Id} className="flex items-start gap-3">
                                  <Avatar className="h-6 w-6">
                                    <AvatarImage src={reply.User?.Photo?.Hashname ? `http://localhost:3200/${reply.User.Photo.Hashname}` : undefined} />
                                    <AvatarFallback className="text-xs">{getUserInitials(reply.User)}</AvatarFallback>
                                  </Avatar>
                                  
                                  <div className="flex-1">
                                    <div className="flex items-center gap-2 mb-1">
                                      <span className="font-medium text-xs">
                                        {reply.User?.Firstname} {reply.User?.Lastname}
                                      </span>
                                      <span className="text-xs text-gray-500">
                                        {formatDate(reply.CreatedAt)}
                                      </span>
                                    </div>
                                    <p className="text-gray-700 text-sm">{reply.Content}</p>
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
};

export default EnhancedPostDetailPage;
