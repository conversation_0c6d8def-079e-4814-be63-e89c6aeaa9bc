import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from '@/hooks/use-toast';
import { 
  ArrowLeft, 
  ThumbsUp, 
  ThumbsDown, 
  MessageCircle, 
  Eye, 
  Clock, 
  User, 
  Tag, 
  Check,
  Edit,
  Trash2,
  Reply,
  Award,
  AlertCircle,
  Send
} from 'lucide-react';
import forumService from '../../services/forum.service';
import { useAuth } from '../../contexts/AuthContext';
import type { ForumSubject, ForumComment, ForumVote } from '../../models/forum.model';
import SEOHelmet from '../../components/common/SEOHelmet';

const EnhancedForumDetailPage: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { authState } = useAuth();

  // State
  const [subject, setSubject] = useState<ForumSubject | null>(null);
  const [comments, setComments] = useState<ForumComment[]>([]);
  const [answers, setAnswers] = useState<ForumSubject[]>([]);
  const [userVote, setUserVote] = useState<ForumVote | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [commentText, setCommentText] = useState('');
  const [answerText, setAnswerText] = useState('');
  const [replyingTo, setReplyingTo] = useState<number | null>(null);
  const [replyText, setReplyText] = useState('');
  const [submittingComment, setSubmittingComment] = useState(false);
  const [submittingAnswer, setSubmittingAnswer] = useState(false);

  // Load subject data
  useEffect(() => {
    if (slug) {
      loadSubjectData();
    }
  }, [slug]);

  const loadSubjectData = async () => {
    if (!slug) return;

    try {
      setLoading(true);
      setError(null);

      // Load subject, comments, and answers in parallel
      const [subjectData, commentsData, answersData] = await Promise.all([
        forumService.getForumSubjectBySlug(slug),
        forumService.getForumComments(0, 50, 0), // Will need subject ID
        forumService.getForumAnswers(slug, 20, 0)
      ]);

      if (!subjectData) {
        setError('Forum topic not found');
        return;
      }

      setSubject(subjectData);
      setAnswers(answersData);

      // Load comments with correct subject ID
      if (subjectData.Id) {
        const actualComments = await forumService.getForumComments(subjectData.Id, 50, 0);
        setComments(actualComments);
      }

      // Load user's vote if authenticated
      if (authState.isAuthenticated && authState.user?.Slug) {
        const vote = await forumService.getUserSubjectVote(slug, authState.user.Slug);
        setUserVote(vote);
      }

    } catch (err) {
      console.error('Error loading subject data:', err);
      setError('Failed to load forum topic');
    } finally {
      setLoading(false);
    }
  };

  // Voting handlers
  const handleVote = async (voteType: number) => {
    if (!authState.isAuthenticated) {
      toast({
        title: 'Authentication Required',
        description: 'Please log in to vote',
        variant: 'destructive'
      });
      return;
    }

    if (!subject?.Id) return;

    try {
      // Remove existing vote if same type
      if (userVote && userVote.UpDown === voteType) {
        await forumService.removeVote(userVote.Slug!);
        setUserVote(null);
        
        // Update local vote counts
        setSubject(prev => prev ? {
          ...prev,
          UpVotes: voteType === 1 ? (prev.UpVotes || 0) - 1 : prev.UpVotes,
          DownVotes: voteType === -1 ? (prev.DownVotes || 0) - 1 : prev.DownVotes
        } : null);
        
        return;
      }

      // Add new vote
      const newVote = await forumService.voteOnSubject({
        SubjectId: subject.Id,
        UpDown: voteType
      });

      setUserVote(newVote);

      // Update local vote counts
      setSubject(prev => prev ? {
        ...prev,
        UpVotes: voteType === 1 ? (prev.UpVotes || 0) + 1 : (userVote?.UpDown === 1 ? (prev.UpVotes || 0) - 1 : prev.UpVotes),
        DownVotes: voteType === -1 ? (prev.DownVotes || 0) + 1 : (userVote?.UpDown === -1 ? (prev.DownVotes || 0) - 1 : prev.DownVotes)
      } : null);

    } catch (error) {
      console.error('Error voting:', error);
      toast({
        title: 'Error',
        description: 'Failed to submit vote',
        variant: 'destructive'
      });
    }
  };

  // Comment handlers
  const handleCommentSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!commentText.trim() || !subject?.Id) return;

    if (!authState.isAuthenticated) {
      toast({
        title: 'Authentication Required',
        description: 'Please log in to comment',
        variant: 'destructive'
      });
      return;
    }

    try {
      setSubmittingComment(true);
      
      const newComment = await forumService.addForumComment({
        Body: commentText.trim(),
        SubjectId: subject.Id
      });

      setComments(prev => [newComment, ...prev]);
      setCommentText('');
      
      toast({
        title: 'Success',
        description: 'Comment added successfully',
      });
    } catch (error) {
      console.error('Error submitting comment:', error);
      toast({
        title: 'Error',
        description: 'Failed to submit comment',
        variant: 'destructive'
      });
    } finally {
      setSubmittingComment(false);
    }
  };

  // Answer handlers
  const handleAnswerSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!answerText.trim() || !subject?.Id) return;

    if (!authState.isAuthenticated) {
      toast({
        title: 'Authentication Required',
        description: 'Please log in to answer',
        variant: 'destructive'
      });
      return;
    }

    try {
      setSubmittingAnswer(true);
      
      const newAnswer = await forumService.createForumSubject({
        Title: `Re: ${subject.Title}`,
        Body: answerText.trim(),
        ParentId: subject.Id
      });

      setAnswers(prev => [newAnswer, ...prev]);
      setAnswerText('');
      
      toast({
        title: 'Success',
        description: 'Answer posted successfully',
      });
    } catch (error) {
      console.error('Error submitting answer:', error);
      toast({
        title: 'Error',
        description: 'Failed to submit answer',
        variant: 'destructive'
      });
    } finally {
      setSubmittingAnswer(false);
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getUserInitials = (user?: { Firstname?: string; Lastname?: string }) => {
    if (!user) return 'U';
    return `${user.Firstname?.[0] || ''}${user.Lastname?.[0] || ''}`.toUpperCase() || 'U';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading forum topic...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !subject) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error || 'Forum topic not found'}</AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  return (
    <>
      <SEOHelmet
        title={subject.Title}
        description={subject.Body?.substring(0, 160).replace(/<[^>]*>/g, '') || ''}
        ogType="article"
      />

      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Back Button */}
          <div className="mb-6">
            <Link to="/forum">
              <Button variant="ghost" className="flex items-center gap-2">
                <ArrowLeft className="h-4 w-4" />
                Back to Forum
              </Button>
            </Link>
          </div>

          {/* Main Subject */}
          <Card className="mb-8">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h1 className="text-2xl font-bold text-gray-900 mb-4">{subject.Title}</h1>
                  
                  {/* Subject Meta */}
                  <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-4">
                    <div className="flex items-center gap-1">
                      <User className="h-4 w-4" />
                      <span>{subject.User?.Firstname} {subject.User?.Lastname}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      <span>{formatDate(subject.CreatedAt)}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Eye className="h-4 w-4" />
                      <span>{subject.ViewsCount || 0} views</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <MessageCircle className="h-4 w-4" />
                      <span>{comments.length + answers.length} responses</span>
                    </div>
                  </div>

                  {/* Tags */}
                  {subject.Tags && subject.Tags.length > 0 && (
                    <div className="flex flex-wrap gap-2 mb-4">
                      {subject.Tags.map((tag, index) => (
                        <Badge key={index} variant="secondary">
                          <Tag className="h-3 w-3 mr-1" />
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>

                {/* Voting */}
                <div className="flex flex-col items-center gap-2 ml-6">
                  <Button
                    variant={userVote?.UpDown === 1 ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleVote(1)}
                    className="flex items-center gap-1"
                  >
                    <ThumbsUp className="h-4 w-4" />
                    {subject.UpVotes || 0}
                  </Button>
                  
                  <Button
                    variant={userVote?.UpDown === -1 ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleVote(-1)}
                    className="flex items-center gap-1"
                  >
                    <ThumbsDown className="h-4 w-4" />
                    {subject.DownVotes || 0}
                  </Button>
                </div>
              </div>
            </CardHeader>
            
            <CardContent>
              {/* Subject Content */}
              <div className="prose max-w-none mb-6">
                <div className="whitespace-pre-wrap">{subject.Body}</div>
              </div>

              {/* Course Link */}
              {subject.Course && (
                <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex items-center gap-2">
                    <Award className="h-5 w-5 text-blue-600" />
                    <span className="font-medium text-blue-900">Related Course:</span>
                    <Link 
                      to={`/courses/${subject.Course.Slug}`}
                      className="text-blue-600 hover:text-blue-800 underline"
                    >
                      {subject.Course.Title}
                    </Link>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Answer Form */}
          {authState.isAuthenticated && (
            <Card className="mb-8">
              <CardHeader>
                <CardTitle>Your Answer</CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleAnswerSubmit}>
                  <Textarea
                    value={answerText}
                    onChange={(e) => setAnswerText(e.target.value)}
                    placeholder="Write your answer here..."
                    className="mb-4"
                    rows={6}
                  />
                  <Button 
                    type="submit" 
                    disabled={!answerText.trim() || submittingAnswer}
                    className="bg-red-600 hover:bg-red-700"
                  >
                    {submittingAnswer ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Posting...
                      </>
                    ) : (
                      <>
                        <Send className="h-4 w-4 mr-2" />
                        Post Answer
                      </>
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>
          )}

          {/* Answers */}
          {answers.length > 0 && (
            <Card className="mb-8">
              <CardHeader>
                <CardTitle>{answers.length} Answer{answers.length !== 1 ? 's' : ''}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {answers.map((answer) => (
                    <div key={answer.Id} className="border-b border-gray-200 pb-6 last:border-b-0">
                      <div className="flex items-start gap-4">
                        <Avatar className="h-10 w-10">
                          <AvatarImage 
                            src={answer.User?.Photo?.Hashname ? `http://localhost:3200/${answer.User.Photo.Hashname}` : undefined} 
                          />
                          <AvatarFallback>{getUserInitials(answer.User)}</AvatarFallback>
                        </Avatar>
                        
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <span className="font-medium text-sm">
                              {answer.User?.Firstname} {answer.User?.Lastname}
                            </span>
                            <span className="text-xs text-gray-500">
                              {formatDate(answer.CreatedAt)}
                            </span>
                            {answer.Id === subject.AcceptedAnswer?.Id && (
                              <Badge variant="default" className="bg-green-600">
                                <Check className="h-3 w-3 mr-1" />
                                Accepted
                              </Badge>
                            )}
                          </div>
                          
                          <div className="prose max-w-none text-gray-700">
                            <div className="whitespace-pre-wrap">{answer.Body}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Comments */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageCircle className="h-5 w-5" />
                Comments ({comments.length})
              </CardTitle>
            </CardHeader>
            
            <CardContent>
              {/* Add Comment Form */}
              {authState.isAuthenticated ? (
                <form onSubmit={handleCommentSubmit} className="mb-6">
                  <Textarea
                    value={commentText}
                    onChange={(e) => setCommentText(e.target.value)}
                    placeholder="Add a comment..."
                    className="mb-3"
                    rows={3}
                  />
                  <Button 
                    type="submit" 
                    disabled={!commentText.trim() || submittingComment}
                    size="sm"
                  >
                    {submittingComment ? 'Posting...' : 'Post Comment'}
                  </Button>
                </form>
              ) : (
                <div className="mb-6 p-4 bg-gray-50 rounded-lg text-center">
                  <p className="text-gray-600 mb-2">Please log in to comment</p>
                  <Link to="/auth/login">
                    <Button variant="outline" size="sm">Log In</Button>
                  </Link>
                </div>
              )}

              {/* Comments List */}
              <div className="space-y-4">
                {comments.length === 0 ? (
                  <p className="text-gray-500 text-center py-8">No comments yet. Be the first to comment!</p>
                ) : (
                  comments.map((comment) => (
                    <div key={comment.Id} className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                      <Avatar className="h-8 w-8">
                        <AvatarImage 
                          src={comment.User?.Photo?.Hashname ? `http://localhost:3200/${comment.User.Photo.Hashname}` : undefined} 
                        />
                        <AvatarFallback className="text-xs">{getUserInitials(comment.User)}</AvatarFallback>
                      </Avatar>
                      
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-medium text-sm">
                            {comment.User?.Firstname} {comment.User?.Lastname}
                          </span>
                          <span className="text-xs text-gray-500">
                            {formatDate(comment.CreatedAt)}
                          </span>
                        </div>
                        <p className="text-gray-700 text-sm">{comment.Body}</p>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
};

export default EnhancedForumDetailPage;
