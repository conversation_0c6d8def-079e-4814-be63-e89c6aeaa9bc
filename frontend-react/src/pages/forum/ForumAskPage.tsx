import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { useToast } from '@/hooks/use-toast';
import { 
  ArrowLeft, 
  Plus, 
  X, 
  HelpCircle, 
  BookOpen, 
  Tag, 
  Send,
  AlertCircle
} from 'lucide-react';
import forumService from '../../services/forum.service';
import categoryService from '../../services/category.service';
import courseService from '../../services/course.service';
import { useAuth } from '../../contexts/AuthContext';
import type { ForumCreateRequest, ForumCategory } from '../../models/forum.model';
import SEOHelmet from '../../components/common/SEOHelmet';

const ForumAskPage: React.FC = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { authState } = useAuth();

  // Form state
  const [formData, setFormData] = useState<ForumCreateRequest>({
    Title: '',
    Body: '',
    TagList: '',
    Categories: [],
    CourseId: undefined
  });

  const [categories, setCategories] = useState<ForumCategory[]>([]);
  const [courses, setCourses] = useState<any[]>([]);
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Check authentication
  useEffect(() => {
    if (!authState.isAuthenticated) {
      navigate('/auth/login', { 
        state: { from: { pathname: '/forum/ask' } }
      });
    }
  }, [authState.isAuthenticated, navigate]);

  // Load categories and courses
  useEffect(() => {
    const loadData = async () => {
      try {
        const [categoriesData, coursesData] = await Promise.all([
          categoryService.getAll(),
          courseService.getAll()
        ]);
        
        setCategories(categoriesData);
        setCourses(coursesData);
      } catch (error) {
        console.error('Error loading data:', error);
        setError('Failed to load categories and courses');
      }
    };

    loadData();
  }, []);

  const handleInputChange = (field: keyof ForumCreateRequest, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addTag = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      const newTags = [...tags, tagInput.trim()];
      setTags(newTags);
      setFormData(prev => ({
        ...prev,
        TagList: newTags.join(',')
      }));
      setTagInput('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    const newTags = tags.filter(tag => tag !== tagToRemove);
    setTags(newTags);
    setFormData(prev => ({
      ...prev,
      TagList: newTags.join(',')
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.Title.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Question title is required',
        variant: 'destructive'
      });
      return;
    }

    if (!formData.Body.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Question description is required',
        variant: 'destructive'
      });
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const subject = await forumService.createForumSubject(formData);
      
      toast({
        title: 'Success',
        description: 'Your question has been posted successfully!',
      });

      // Navigate to the created subject
      navigate(`/forum/${subject.Slug}`);
    } catch (error) {
      console.error('Error creating forum subject:', error);
      setError('Failed to post your question. Please try again.');
      toast({
        title: 'Error',
        description: 'Failed to post your question. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  if (!authState.isAuthenticated) {
    return null; // Will redirect in useEffect
  }

  return (
    <>
      <SEOHelmet
        title="Ask a Question - Forum"
        description="Ask a question to the community and get help from fellow learners and instructors"
      />

      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-8">
            <Button
              variant="ghost"
              onClick={() => navigate('/forum')}
              className="mb-4"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Forum
            </Button>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <HelpCircle className="h-8 w-8 text-white" />
              </div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Ask a Question
              </h1>
              <p className="text-gray-600">
                Get help from the community by asking a detailed question
              </p>
            </div>
          </div>

          {/* Form */}
          <Card>
            <CardHeader>
              <CardTitle>Question Details</CardTitle>
            </CardHeader>
            
            <CardContent>
              {error && (
                <Alert variant="destructive" className="mb-6">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Title */}
                <div>
                  <Label htmlFor="title">Question Title *</Label>
                  <Input
                    id="title"
                    value={formData.Title}
                    onChange={(e) => handleInputChange('Title', e.target.value)}
                    placeholder="What's your question? Be specific and clear..."
                    className="mt-1"
                    required
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Write a clear, descriptive title that summarizes your question
                  </p>
                </div>

                {/* Body */}
                <div>
                  <Label htmlFor="body">Question Description *</Label>
                  <Textarea
                    id="body"
                    value={formData.Body}
                    onChange={(e) => handleInputChange('Body', e.target.value)}
                    placeholder="Provide detailed information about your question. Include what you've tried, what you expected, and what actually happened..."
                    className="mt-1"
                    rows={8}
                    required
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Include relevant details, code snippets, error messages, or screenshots
                  </p>
                </div>

                {/* Course Selection */}
                <div>
                  <Label>Related Course (Optional)</Label>
                  <Select
                    value={formData.CourseId?.toString() || ''}
                    onValueChange={(value) => handleInputChange('CourseId', value ? parseInt(value) : undefined)}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select a course if your question is course-specific" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">No specific course</SelectItem>
                      {courses.map((course) => (
                        <SelectItem key={course.Id} value={course.Id.toString()}>
                          <div className="flex items-center gap-2">
                            <BookOpen className="h-4 w-4" />
                            {course.Title}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Categories */}
                <div>
                  <Label>Categories</Label>
                  <Select
                    onValueChange={(value) => {
                      const categoryId = parseInt(value);
                      if (!formData.Categories?.includes(categoryId)) {
                        handleInputChange('Categories', [...(formData.Categories || []), categoryId]);
                      }
                    }}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select categories for your question" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  
                  {formData.Categories && formData.Categories.length > 0 && (
                    <div className="flex flex-wrap gap-2 mt-2">
                      {formData.Categories.map((categoryId) => {
                        const category = categories.find(c => c.id === categoryId.toString());
                        return category ? (
                          <Badge key={categoryId} variant="secondary" className="flex items-center gap-1">
                            {category.name}
                            <button
                              type="button"
                              onClick={() => {
                                handleInputChange('Categories', 
                                  formData.Categories?.filter(id => id !== categoryId) || []
                                );
                              }}
                              className="ml-1 hover:text-red-600"
                            >
                              <X className="h-3 w-3" />
                            </button>
                          </Badge>
                        ) : null;
                      })}
                    </div>
                  )}
                </div>

                {/* Tags */}
                <div>
                  <Label>Tags</Label>
                  <div className="flex gap-2 mt-1">
                    <Input
                      value={tagInput}
                      onChange={(e) => setTagInput(e.target.value)}
                      placeholder="Add tags to help categorize your question..."
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                    />
                    <Button type="button" onClick={addTag} size="sm">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  {tags.length > 0 && (
                    <div className="flex flex-wrap gap-2 mt-2">
                      {tags.map((tag, index) => (
                        <Badge key={index} variant="outline" className="flex items-center gap-1">
                          <Tag className="h-3 w-3" />
                          {tag}
                          <button
                            type="button"
                            onClick={() => removeTag(tag)}
                            className="ml-1 hover:text-red-600"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </Badge>
                      ))}
                    </div>
                  )}
                  
                  <p className="text-sm text-gray-500 mt-1">
                    Add relevant tags to help others find and answer your question
                  </p>
                </div>

                <Separator />

                {/* Submit */}
                <div className="flex justify-between items-center">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => navigate('/forum')}
                  >
                    Cancel
                  </Button>
                  
                  <Button
                    type="submit"
                    disabled={loading}
                    className="bg-red-600 hover:bg-red-700"
                  >
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Posting...
                      </>
                    ) : (
                      <>
                        <Send className="h-4 w-4 mr-2" />
                        Post Question
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>

          {/* Tips */}
          <Card className="mt-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5 text-blue-600" />
                Tips for Getting Great Answers
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• <strong>Be specific:</strong> Include exact error messages, code snippets, or screenshots</li>
                <li>• <strong>Show your work:</strong> Explain what you've already tried</li>
                <li>• <strong>Use clear language:</strong> Write in a way that's easy to understand</li>
                <li>• <strong>Add context:</strong> Mention your experience level and learning goals</li>
                <li>• <strong>Tag appropriately:</strong> Use relevant tags to help the right people find your question</li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
};

export default ForumAskPage;
