import { useState, use<PERSON>emo } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, ChevronLeft, ChevronRight } from "lucide-react";
import {
  FaSearch,
  FaStar,
  FaUserTie,
  FaChalkboardTeacher,
  FaGraduationCap,
  FaUsers,
  FaFacebook,
  FaTwitter,
  FaLinkedin,
  FaGlobe,
  Fa<PERSON>ook,
  <PERSON>a<PERSON>lay,
  Fa<PERSON>lock,
  Fa<PERSON>ilter,
  FaSort
} from 'react-icons/fa';
import { Search, Star, BookOpen } from 'lucide-react';
import { useCourseInstructors, useCategories } from '../../hooks';
import SEOHelmet from '../../components/common/SEOHelmet';
import { InstructorCard } from '../../components/instructors';
// import { testInstructorEndpoints } from '../../debug/testInstructorEndpoints';
import type { Instructor, Category } from '../../models';

const InstructorsPage = () => {
  // State management matching Angular structure
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [sortField, setSortField] = useState('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(12);
  const [activeTab, setActiveTab] = useState('all');

  // Fetch real data using the same approach as admin page (which works correctly)
  const { data: instructorStats, loading: statsLoading } = useCourseInstructors();
  const { data: categories, loading: categoriesLoading } = useCategories();

  // Use instructorStats as the primary data source since it contains the real instructor data
  // This matches the successful approach used in AdminInstructorsPage
  const instructors = instructorStats?.map(stat => stat.Instructor).filter(Boolean) || [];
  const instructorsLoading = statsLoading;
  const error = null; // Remove error from unused useInstructors hook

  const loading = instructorsLoading || statsLoading || categoriesLoading;

  // Run endpoint test on component mount (disabled for production)
  // useEffect(() => {
  //   testInstructorEndpoints();
  // }, []);

  // Create a unified instructors list using the working admin approach
  const unifiedInstructors = useMemo(() => {
    // Now instructors is already extracted from instructorStats, so just use it directly
    if (instructors && instructors.length > 0) {
      console.log('✅ Using real instructor data (same as admin page):', instructors);
      return instructors;
    }

    console.log('❌ No instructor data available');
    return [];
  }, [instructors]);

  // Debug logging to verify our new approach
  console.log('🔍 Debug - Instructors data (using admin approach):', {
    instructorsCount: instructors?.length || 0,
    instructorStatsCount: instructorStats?.length || 0,
    loading: instructorsLoading,
    sampleInstructor: instructors?.[0] || null,
    sampleInstructorName: instructors?.[0]?.Firstname && instructors?.[0]?.Lastname
      ? `${instructors[0].Firstname} ${instructors[0].Lastname}`
      : 'No name available'
  });

  // Calculate comprehensive statistics from instructor data
  const totalStats = useMemo(() => {
    if (!instructorStats) return {
      totalInstructors: 0,
      totalCourses: 0,
      totalStudents: 0,
      averageRating: 0,
      totalExperience: 0
    };

    const totalCourses = instructorStats.reduce((sum, stat) => sum + (stat.NumberOfCourse || 0), 0);
    const totalStudents = instructorStats.reduce((sum, stat) => sum + (stat.NumberOfStudent || 0), 0);

    // Use unified instructors count for consistency (should match instructorStats.length)
    const totalInstructors = unifiedInstructors.length;

    console.log('📊 Stats calculation (using admin approach):', {
      unifiedInstructorsLength: unifiedInstructors.length,
      instructorStatsLength: instructorStats.length,
      totalInstructorsUsed: totalInstructors,
      totalCourses,
      totalStudents,
      dataConsistent: unifiedInstructors.length === instructorStats.length
    });

    return {
      totalInstructors,
      totalCourses,
      totalStudents,
      averageRating: 4.5, // Default since we don't have rating data yet
      totalExperience: totalInstructors * 3 // Estimated average experience
    };
  }, [instructorStats, unifiedInstructors]);

  // Helper functions (updated to work with User objects directly)
  const getInstructorStats = (instructorId?: number) => {
    if (!instructorId) return { NumberOfCourse: 0, NumberOfStudent: 0 };
    return instructorStats?.find(stat => stat.Instructor?.Id === instructorId) || {
      NumberOfCourse: 0,
      NumberOfStudent: 0
    };
  };

  const getInstructorName = (user: any) => {
    // Now instructor is a User object directly (not instructor.User)
    if (!user) return 'Unknown Instructor';
    const firstName = user.Firstname || '';
    const lastName = user.Lastname || '';
    const fullName = `${firstName} ${lastName}`.trim();
    return fullName || user.Username || 'Unknown Instructor';
  };

  const getInstructorBio = (user: any) => {
    // Bio comes from User.About field
    return user?.About ||
           'Experienced instructor passionate about sharing knowledge and helping students achieve their learning goals.';
  };

  const getInstructorSpecialization = (user: any) => {
    // Specialization comes from User.Profil field
    return user?.Profil ||
           'Professional Instructor';
  };

  const getInstructorRating = (_instructor: any) => {
    // TODO: Calculate from actual reviews when available
    return 4.5 + (Math.random() * 0.8 - 0.4); // Random between 4.1 and 4.9
  };

  // Convert User object to Instructor format expected by InstructorCard
  const convertUserToInstructor = (user: any) => {
    return {
      Id: user.Id,
      Bio: user.About, // Map User.About to Instructor.Bio
      Specialization: user.Profil, // Map User.Profil to Instructor.Specialization
      Rating: 4.5,
      User: user, // Include the full User object
      CreatedAt: user.CreatedAt,
      UpdatedAt: user.UpdatedAt
    };
  };

  // Advanced filtering and sorting
  const filteredAndSortedInstructors = useMemo(() => {
    console.log('🔍 Debug - Filtering instructors:', {
      unifiedInstructorsCount: unifiedInstructors.length,
      searchTerm,
      selectedCategory: selectedCategory?.Title || 'none',
      unifiedInstructors
    });

    if (!unifiedInstructors || unifiedInstructors.length === 0) {
      console.log('❌ Debug - No unified instructors data available');
      return [];
    }

    let filtered = unifiedInstructors.filter((user: any) => {
      const instructorName = getInstructorName(user);
      const instructorBio = getInstructorBio(user);
      const instructorSpecialization = getInstructorSpecialization(user);

      const matchesSearch = searchTerm === '' ||
        instructorName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        instructorBio.toLowerCase().includes(searchTerm.toLowerCase()) ||
        instructorSpecialization.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesCategory = !selectedCategory ||
        instructorSpecialization.toLowerCase().includes(selectedCategory.Title?.toLowerCase() || '');

      console.log('🔍 Debug - Instructor filter check (User object):', {
        instructorName,
        instructorBio: instructorBio.substring(0, 50) + '...',
        instructorSpecialization,
        matchesSearch,
        matchesCategory,
        searchTerm,
        selectedCategoryTitle: selectedCategory?.Title,
        userFields: Object.keys(user)
      });

      return matchesSearch && matchesCategory;
    });

    console.log('✅ Debug - Filtered instructors:', {
      originalCount: unifiedInstructors.length,
      filteredCount: filtered.length,
      filtered: filtered.map((i: any) => getInstructorName(i))
    });

    // Sort instructors (working with User objects)
    filtered.sort((a: any, b: any) => {
      let aValue, bValue;

      switch (sortField) {
        case 'name':
          aValue = getInstructorName(a).toLowerCase();
          bValue = getInstructorName(b).toLowerCase();
          break;
        case 'courses':
          aValue = getInstructorStats(a.Id).NumberOfCourse || 0;
          bValue = getInstructorStats(b.Id).NumberOfCourse || 0;
          break;
        case 'students':
          aValue = getInstructorStats(a.Id).NumberOfStudent || 0;
          bValue = getInstructorStats(b.Id).NumberOfStudent || 0;
          break;
        case 'rating':
          aValue = 4.5; // Default rating since we don't have rating data
          bValue = 4.5;
          break;
        default:
          aValue = getInstructorName(a).toLowerCase();
          bValue = getInstructorName(b).toLowerCase();
      }

      if (typeof aValue === 'string') {
        return sortDirection === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
      } else {
        return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
      }
    });

    return filtered;
  }, [unifiedInstructors, searchTerm, selectedCategory, sortField, sortDirection]);

  // Pagination
  const totalPages = Math.ceil(filteredAndSortedInstructors.length / itemsPerPage);
  const paginatedInstructors = filteredAndSortedInstructors.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  console.log('🔍 Debug - Final render data:', {
    totalStats,
    filteredAndSortedInstructorsCount: filteredAndSortedInstructors.length,
    paginatedInstructorsCount: paginatedInstructors.length,
    totalPages,
    currentPage,
    loading,
    sampleInstructor: paginatedInstructors[0] || null,
    allPaginatedInstructors: paginatedInstructors
  });



  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-6xl mx-auto px-6">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-brand-red-primary" />
              <p className="text-muted-foreground">Loading instructors...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-6xl mx-auto px-6">
          <div className="text-center py-12">
            <FaUserTie className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Unable to load instructors</h3>
            <p className="text-gray-500">Please try refreshing the page.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <SEOHelmet
        title="Expert Instructors | Brainmaker"
        description="Meet our expert instructors who are passionate about sharing their knowledge and helping you succeed in your learning journey."
        keywords="instructors, teachers, experts, online learning, education, courses"
      />

      <div className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <div className="bg-gradient-to-r from-red-600 to-red-700 text-white py-16">
          <div className="max-w-6xl mx-auto px-6">
            <div className="text-center">
              <div className="flex justify-center mb-6">
                <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center">
                  <FaChalkboardTeacher className="w-10 h-10 text-white" />
                </div>
              </div>
              <h1 className="text-5xl font-bold mb-4">Meet Our Expert Instructors</h1>
              <p className="text-xl text-red-100 max-w-3xl mx-auto mb-8">
                Learn from industry professionals who are passionate about sharing their knowledge and helping you succeed in your learning journey.
              </p>
              <div className="flex justify-center space-x-8 text-red-100">
                <div className="text-center">
                  <div className="text-3xl font-bold">{totalStats.totalInstructors}</div>
                  <div className="text-sm">Expert Instructors</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold">{totalStats.totalCourses.toLocaleString()}</div>
                  <div className="text-sm">Courses Available</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold">{totalStats.totalStudents.toLocaleString()}</div>
                  <div className="text-sm">Students Taught</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-6xl mx-auto px-6 py-12">

          {/* Advanced Search and Filters */}
          <Card className="mb-8">
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                {/* Search Input */}
                <div className="relative md:col-span-2">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search instructors by name, bio, or specialization..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>

                {/* Category Filter */}
                <Select
                  value={selectedCategory?.Id?.toString() || 'all'}
                  onValueChange={(value) => {
                    if (value === 'all') {
                      setSelectedCategory(null);
                    } else {
                      const category = categories?.find(cat => cat.Id?.toString() === value);
                      setSelectedCategory(category || null);
                    }
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All Categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {categories?.filter(category => category.Id).map(category => (
                      <SelectItem key={category.Id} value={category.Id!.toString()}>
                        {category.Title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {/* Sort Options */}
                <Select value={`${sortField}-${sortDirection}`} onValueChange={(value) => {
                  const [field, direction] = value.split('-');
                  setSortField(field);
                  setSortDirection(direction as 'asc' | 'desc');
                }}>
                  <SelectTrigger>
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="name-asc">Name (A-Z)</SelectItem>
                    <SelectItem value="name-desc">Name (Z-A)</SelectItem>
                    <SelectItem value="courses-desc">Most Courses</SelectItem>
                    <SelectItem value="courses-asc">Least Courses</SelectItem>
                    <SelectItem value="students-desc">Most Students</SelectItem>
                    <SelectItem value="students-asc">Least Students</SelectItem>
                    <SelectItem value="rating-desc">Highest Rated</SelectItem>
                    <SelectItem value="rating-asc">Lowest Rated</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Filter Summary */}
              <div className="flex items-center justify-between mt-4 pt-4 border-t">
                <div className="text-sm text-gray-600">
                  Showing {paginatedInstructors.length} of {filteredAndSortedInstructors.length} instructors
                  {selectedCategory && (
                    <Badge variant="secondary" className="ml-2">
                      {selectedCategory.Title}
                      <button
                        onClick={() => setSelectedCategory(null)}
                        className="ml-1 hover:text-red-600"
                      >
                        ×
                      </button>
                    </Badge>
                  )}
                </div>

                {/* View Mode Toggle */}
                <div className="flex items-center space-x-2">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                  >
                    Grid
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                  >
                    List
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Detailed Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6 text-center">
                <FaUsers className="w-8 h-8 mx-auto mb-3 text-red-600" />
                <h3 className="text-2xl font-bold text-gray-900">{totalStats.totalInstructors}</h3>
                <p className="text-gray-600">Expert Instructors</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6 text-center">
                <BookOpen className="w-8 h-8 mx-auto mb-3 text-red-600" />
                <h3 className="text-2xl font-bold text-gray-900">{totalStats.totalCourses.toLocaleString()}</h3>
                <p className="text-gray-600">Courses Available</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6 text-center">
                <FaGraduationCap className="w-8 h-8 mx-auto mb-3 text-red-600" />
                <h3 className="text-2xl font-bold text-gray-900">{totalStats.totalStudents.toLocaleString()}</h3>
                <p className="text-gray-600">Students Taught</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6 text-center">
                <Star className="w-8 h-8 mx-auto mb-3 text-red-600" />
                <h3 className="text-2xl font-bold text-gray-900">{totalStats.averageRating.toFixed(1)}</h3>
                <p className="text-gray-600">Average Rating</p>
              </CardContent>
            </Card>
          </div>

          {/* Instructors Display */}
          {paginatedInstructors.length === 0 ? (
            <div className="text-center py-16">
              <FaUserTie className="h-16 w-16 mx-auto mb-6 text-gray-400" />
              <h3 className="text-2xl font-medium text-gray-900 mb-2">No instructors found</h3>
              <p className="text-gray-500 mb-6">
                {searchTerm || selectedCategory
                  ? 'Try adjusting your search or filter criteria.'
                  : 'No instructors are currently available.'}
              </p>
              {(searchTerm || selectedCategory) && (
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchTerm('');
                    setSelectedCategory(null);
                  }}
                >
                  Clear Filters
                </Button>
              )}
            </div>
          ) : (
            <>
              {/* Grid View */}
              {viewMode === 'grid' && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                  {paginatedInstructors.map((user) => (
                    <InstructorCard
                      key={user.Id}
                      instructor={convertUserToInstructor(user)}
                      stats={getInstructorStats(user.Id)}
                      variant="grid"
                      showSocialLinks={false}
                    />
                  ))}
                </div>
              )}

              {/* List View */}
              {viewMode === 'list' && (
                <div className="space-y-4 mb-8">
                  {paginatedInstructors.map((user) => (
                    <InstructorCard
                      key={user.Id}
                      instructor={convertUserToInstructor(user)}
                      stats={getInstructorStats(user.Id)}
                      variant="list"
                      showSocialLinks={true}
                    />
                  ))}
                </div>
              )}

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-center space-x-2 mb-8">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    disabled={currentPage === 1}
                  >
                    <ChevronLeft className="h-4 w-4" />
                    Previous
                  </Button>

                  <div className="flex items-center space-x-1">
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      const pageNum = i + 1;
                      return (
                        <Button
                          key={pageNum}
                          variant={currentPage === pageNum ? "default" : "outline"}
                          size="sm"
                          onClick={() => setCurrentPage(pageNum)}
                          className={currentPage === pageNum ? "bg-red-600 hover:bg-red-700" : ""}
                        >
                          {pageNum}
                        </Button>
                      );
                    })}
                    {totalPages > 5 && (
                      <>
                        <span className="px-2">...</span>
                        <Button
                          variant={currentPage === totalPages ? "default" : "outline"}
                          size="sm"
                          onClick={() => setCurrentPage(totalPages)}
                          className={currentPage === totalPages ? "bg-red-600 hover:bg-red-700" : ""}
                        >
                          {totalPages}
                        </Button>
                      </>
                    )}
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                    disabled={currentPage === totalPages}
                  >
                    Next
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </>
          )}

          {/* Call to Action */}
          <div className="mt-16">
            <Card className="bg-gradient-to-r from-red-50 to-red-100 border-red-200">
              <CardContent className="text-center py-12">
                <FaChalkboardTeacher className="h-16 w-16 mx-auto mb-6 text-red-600" />
                <h3 className="text-2xl font-bold text-gray-900 mb-4">Want to become an instructor?</h3>
                <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
                  Share your expertise with thousands of students worldwide and earn money doing what you love.
                  Join our community of expert instructors and make a difference in education.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link to="/become-instructor">
                    <Button size="lg" className="bg-red-600 hover:bg-red-700">
                      <FaGraduationCap className="w-5 h-5 mr-2" />
                      Start Teaching Today
                    </Button>
                  </Link>
                  <Link to="/instructor-resources">
                    <Button size="lg" variant="outline" className="border-red-600 text-red-600 hover:bg-red-50">
                      <FaBook className="w-5 h-5 mr-2" />
                      Learn More
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </>
  );
};

export default InstructorsPage;