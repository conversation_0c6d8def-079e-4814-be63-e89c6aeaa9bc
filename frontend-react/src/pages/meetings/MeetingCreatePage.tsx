import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { useToast } from '@/hooks/use-toast';
import { 
  ArrowLeft, 
  Calendar, 
  Clock, 
  Users, 
  Video, 
  Mic,
  Settings,
  Send,
  Plus,
  X,
  AlertCircle
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import SEOHelmet from '../../components/common/SEOHelmet';

interface MeetingFormData {
  topic: string;
  description: string;
  startDate: string;
  startTime: string;
  duration: number;
  timezone: string;
  password: string;
  waitingRoom: boolean;
  recordMeeting: boolean;
  muteParticipants: boolean;
  allowScreenShare: boolean;
  invitees: string[];
}

const MeetingCreatePage: React.FC = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { authState } = useAuth();

  const [formData, setFormData] = useState<MeetingFormData>({
    topic: '',
    description: '',
    startDate: '',
    startTime: '',
    duration: 60,
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    password: '',
    waitingRoom: true,
    recordMeeting: false,
    muteParticipants: true,
    allowScreenShare: true,
    invitees: []
  });

  const [inviteeEmail, setInviteeEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Check authentication
  useEffect(() => {
    if (!authState.isAuthenticated) {
      navigate('/auth/login', { 
        state: { from: { pathname: '/meetings/create' } }
      });
    }
  }, [authState.isAuthenticated, navigate]);

  // Set default date and time
  useEffect(() => {
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    setFormData(prev => ({
      ...prev,
      startDate: tomorrow.toISOString().split('T')[0],
      startTime: '10:00'
    }));
  }, []);

  const handleInputChange = (field: keyof MeetingFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addInvitee = () => {
    if (inviteeEmail.trim() && !formData.invitees.includes(inviteeEmail.trim())) {
      const email = inviteeEmail.trim();
      // Basic email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (emailRegex.test(email)) {
        handleInputChange('invitees', [...formData.invitees, email]);
        setInviteeEmail('');
      } else {
        toast({
          title: 'Invalid Email',
          description: 'Please enter a valid email address',
          variant: 'destructive'
        });
      }
    }
  };

  const removeInvitee = (emailToRemove: string) => {
    handleInputChange('invitees', formData.invitees.filter(email => email !== emailToRemove));
  };

  const generatePassword = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let password = '';
    for (let i = 0; i < 8; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    handleInputChange('password', password);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.topic.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Meeting topic is required',
        variant: 'destructive'
      });
      return;
    }

    if (!formData.startDate || !formData.startTime) {
      toast({
        title: 'Validation Error',
        description: 'Meeting date and time are required',
        variant: 'destructive'
      });
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // TODO: Implement real meeting creation API call
      // const meeting = await meetingService.createMeeting(formData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast({
        title: 'Success',
        description: 'Meeting created successfully!',
      });

      // Navigate to meetings list
      navigate('/meetings');
    } catch (error) {
      console.error('Error creating meeting:', error);
      setError('Failed to create meeting. Please try again.');
      toast({
        title: 'Error',
        description: 'Failed to create meeting. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  if (!authState.isAuthenticated) {
    return null; // Will redirect in useEffect
  }

  return (
    <>
      <SEOHelmet
        title="Create Meeting"
        description="Schedule a new video meeting with participants"
      />

      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-8">
            <Button
              variant="ghost"
              onClick={() => navigate('/meetings')}
              className="mb-4"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Meetings
            </Button>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <Video className="h-8 w-8 text-white" />
              </div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Create New Meeting
              </h1>
              <p className="text-gray-600">
                Schedule a video meeting and invite participants
              </p>
            </div>
          </div>

          {/* Form */}
          <Card>
            <CardHeader>
              <CardTitle>Meeting Details</CardTitle>
            </CardHeader>
            
            <CardContent>
              {error && (
                <Alert variant="destructive" className="mb-6">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Basic Information */}
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="topic">Meeting Topic *</Label>
                    <Input
                      id="topic"
                      value={formData.topic}
                      onChange={(e) => handleInputChange('topic', e.target.value)}
                      placeholder="Enter meeting topic..."
                      className="mt-1"
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      placeholder="Add meeting description or agenda..."
                      className="mt-1"
                      rows={3}
                    />
                  </div>
                </div>

                <Separator />

                {/* Schedule */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium flex items-center gap-2">
                    <Calendar className="h-5 w-5" />
                    Schedule
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="startDate">Date *</Label>
                      <Input
                        id="startDate"
                        type="date"
                        value={formData.startDate}
                        onChange={(e) => handleInputChange('startDate', e.target.value)}
                        className="mt-1"
                        required
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="startTime">Time *</Label>
                      <Input
                        id="startTime"
                        type="time"
                        value={formData.startTime}
                        onChange={(e) => handleInputChange('startTime', e.target.value)}
                        className="mt-1"
                        required
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="duration">Duration (minutes)</Label>
                      <Select
                        value={formData.duration.toString()}
                        onValueChange={(value) => handleInputChange('duration', parseInt(value))}
                      >
                        <SelectTrigger className="mt-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="30">30 minutes</SelectItem>
                          <SelectItem value="60">1 hour</SelectItem>
                          <SelectItem value="90">1.5 hours</SelectItem>
                          <SelectItem value="120">2 hours</SelectItem>
                          <SelectItem value="180">3 hours</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                <Separator />

                {/* Security */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    Security & Settings
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="password">Meeting Password</Label>
                      <div className="flex gap-2 mt-1">
                        <Input
                          id="password"
                          value={formData.password}
                          onChange={(e) => handleInputChange('password', e.target.value)}
                          placeholder="Optional password..."
                        />
                        <Button
                          type="button"
                          variant="outline"
                          onClick={generatePassword}
                        >
                          Generate
                        </Button>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Enable Waiting Room</Label>
                        <p className="text-sm text-gray-500">Host must admit participants</p>
                      </div>
                      <Switch
                        checked={formData.waitingRoom}
                        onCheckedChange={(checked) => handleInputChange('waitingRoom', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Record Meeting</Label>
                        <p className="text-sm text-gray-500">Automatically record the meeting</p>
                      </div>
                      <Switch
                        checked={formData.recordMeeting}
                        onCheckedChange={(checked) => handleInputChange('recordMeeting', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Mute Participants on Entry</Label>
                        <p className="text-sm text-gray-500">Participants join muted</p>
                      </div>
                      <Switch
                        checked={formData.muteParticipants}
                        onCheckedChange={(checked) => handleInputChange('muteParticipants', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Allow Screen Sharing</Label>
                        <p className="text-sm text-gray-500">Participants can share their screen</p>
                      </div>
                      <Switch
                        checked={formData.allowScreenShare}
                        onCheckedChange={(checked) => handleInputChange('allowScreenShare', checked)}
                      />
                    </div>
                  </div>
                </div>

                <Separator />

                {/* Invitees */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    Invite Participants
                  </h3>
                  
                  <div className="flex gap-2">
                    <Input
                      value={inviteeEmail}
                      onChange={(e) => setInviteeEmail(e.target.value)}
                      placeholder="Enter email address..."
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addInvitee())}
                    />
                    <Button type="button" onClick={addInvitee}>
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  {formData.invitees.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {formData.invitees.map((email, index) => (
                        <Badge key={index} variant="secondary" className="flex items-center gap-1">
                          {email}
                          <button
                            type="button"
                            onClick={() => removeInvitee(email)}
                            className="ml-1 hover:text-red-600"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>

                <Separator />

                {/* Submit */}
                <div className="flex justify-between items-center">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => navigate('/meetings')}
                  >
                    Cancel
                  </Button>
                  
                  <Button
                    type="submit"
                    disabled={loading}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Creating...
                      </>
                    ) : (
                      <>
                        <Send className="h-4 w-4 mr-2" />
                        Create Meeting
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
};

export default MeetingCreatePage;
