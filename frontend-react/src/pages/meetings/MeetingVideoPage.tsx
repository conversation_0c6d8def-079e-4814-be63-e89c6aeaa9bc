import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from '@/hooks/use-toast';
import { 
  Video, 
  VideoOff, 
  Mic, 
  MicOff,
  PhoneOff,
  Monitor,
  MessageSquare,
  Users,
  Settings,
  MoreVertical,
  Maximize,
  Volume2,
  VolumeX
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import SEOHelmet from '../../components/common/SEOHelmet';

interface Participant {
  id: string;
  name: string;
  isHost: boolean;
  videoEnabled: boolean;
  audioEnabled: boolean;
  isScreenSharing: boolean;
}

interface MeetingState {
  displayName: string;
  videoEnabled: boolean;
  audioEnabled: boolean;
}

const MeetingVideoPage: React.FC = () => {
  const { meetingId } = useParams<{ meetingId: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();
  const { authState } = useAuth();

  // Get initial state from navigation
  const initialState = location.state as MeetingState;

  // Video refs
  const localVideoRef = useRef<HTMLVideoElement>(null);
  const remoteVideosRef = useRef<{ [key: string]: HTMLVideoElement }>({});

  // State
  const [participants, setParticipants] = useState<Participant[]>([
    {
      id: 'host',
      name: 'John Doe (Host)',
      isHost: true,
      videoEnabled: true,
      audioEnabled: true,
      isScreenSharing: false
    },
    {
      id: 'participant1',
      name: 'Jane Smith',
      isHost: false,
      videoEnabled: true,
      audioEnabled: false,
      isScreenSharing: false
    }
  ]);

  const [localState, setLocalState] = useState({
    videoEnabled: initialState?.videoEnabled ?? true,
    audioEnabled: initialState?.audioEnabled ?? true,
    screenSharing: false,
    chatOpen: false,
    participantsOpen: false
  });

  const [meetingInfo, setMeetingInfo] = useState({
    topic: 'React Development Workshop',
    duration: '00:15:32',
    participantCount: participants.length + 1
  });

  const [localStream, setLocalStream] = useState<MediaStream | null>(null);

  // Initialize local video stream
  useEffect(() => {
    initializeLocalStream();
    return () => {
      // Cleanup streams when component unmounts
      if (localStream) {
        localStream.getTracks().forEach(track => track.stop());
      }
    };
  }, []);

  const initializeLocalStream = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: localState.videoEnabled,
        audio: localState.audioEnabled
      });

      setLocalStream(stream);

      if (localVideoRef.current) {
        localVideoRef.current.srcObject = stream;
      }
    } catch (error) {
      console.error('Error accessing media devices:', error);
      toast({
        title: 'Media Access Error',
        description: 'Could not access camera or microphone',
        variant: 'destructive'
      });
    }
  };

  const toggleVideo = async () => {
    if (localStream) {
      const videoTrack = localStream.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.enabled = !localState.videoEnabled;
        setLocalState(prev => ({ ...prev, videoEnabled: !prev.videoEnabled }));
      }
    }
  };

  const toggleAudio = async () => {
    if (localStream) {
      const audioTrack = localStream.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !localState.audioEnabled;
        setLocalState(prev => ({ ...prev, audioEnabled: !prev.audioEnabled }));
      }
    }
  };

  const toggleScreenShare = async () => {
    try {
      if (!localState.screenSharing) {
        const screenStream = await navigator.mediaDevices.getDisplayMedia({
          video: true,
          audio: true
        });
        
        // Replace video track with screen share
        if (localStream && localVideoRef.current) {
          const videoTrack = screenStream.getVideoTracks()[0];
          const sender = localStream.getVideoTracks()[0];
          
          // In a real implementation, you'd replace the track in the peer connection
          localVideoRef.current.srcObject = screenStream;
          
          videoTrack.onended = () => {
            // Screen share ended
            setLocalState(prev => ({ ...prev, screenSharing: false }));
            initializeLocalStream(); // Restore camera
          };
        }
        
        setLocalState(prev => ({ ...prev, screenSharing: true }));
      } else {
        // Stop screen sharing and restore camera
        setLocalState(prev => ({ ...prev, screenSharing: false }));
        initializeLocalStream();
      }
    } catch (error) {
      console.error('Error with screen sharing:', error);
      toast({
        title: 'Screen Share Error',
        description: 'Could not start screen sharing',
        variant: 'destructive'
      });
    }
  };

  const leaveMeeting = () => {
    // Cleanup streams
    if (localStream) {
      localStream.getTracks().forEach(track => track.stop());
    }

    toast({
      title: 'Left Meeting',
      description: 'You have left the meeting',
    });

    navigate('/meetings');
  };

  const toggleChat = () => {
    setLocalState(prev => ({ ...prev, chatOpen: !prev.chatOpen }));
  };

  const toggleParticipants = () => {
    setLocalState(prev => ({ ...prev, participantsOpen: !prev.participantsOpen }));
  };

  return (
    <>
      <SEOHelmet
        title={`Meeting - ${meetingInfo.topic}`}
        description="Video conference meeting"
      />

      <div className="h-screen bg-black flex flex-col">
        {/* Header */}
        <div className="bg-gray-900 text-white p-4 flex items-center justify-between">
          <div>
            <h1 className="text-lg font-semibold">{meetingInfo.topic}</h1>
            <p className="text-sm text-gray-300">Duration: {meetingInfo.duration}</p>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="bg-green-600 text-white">
              <Users className="h-3 w-3 mr-1" />
              {meetingInfo.participantCount}
            </Badge>
          </div>
        </div>

        {/* Video Grid */}
        <div className="flex-1 p-4">
          <div className="h-full grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Local Video */}
            <Card className="relative bg-gray-800 overflow-hidden">
              <CardContent className="p-0 h-full">
                <div className="relative h-full">
                  {localState.videoEnabled ? (
                    <video
                      ref={localVideoRef}
                      autoPlay
                      muted
                      playsInline
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full bg-gray-700 flex items-center justify-center">
                      <div className="text-center text-white">
                        <VideoOff className="h-12 w-12 mx-auto mb-2" />
                        <p className="text-sm">Camera Off</p>
                      </div>
                    </div>
                  )}
                  
                  {/* Local Video Overlay */}
                  <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm">
                    You {localState.screenSharing && '(Screen)'}
                  </div>
                  
                  {/* Audio Indicator */}
                  <div className="absolute top-2 right-2">
                    {localState.audioEnabled ? (
                      <Volume2 className="h-4 w-4 text-green-400" />
                    ) : (
                      <VolumeX className="h-4 w-4 text-red-400" />
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Remote Participants */}
            {participants.map((participant) => (
              <Card key={participant.id} className="relative bg-gray-800 overflow-hidden">
                <CardContent className="p-0 h-full">
                  <div className="relative h-full">
                    {participant.videoEnabled ? (
                      <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                        <div className="text-center text-white">
                          <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-2">
                            <span className="text-2xl font-bold">
                              {participant.name.charAt(0)}
                            </span>
                          </div>
                          <p className="text-sm">{participant.name}</p>
                        </div>
                      </div>
                    ) : (
                      <div className="w-full h-full bg-gray-700 flex items-center justify-center">
                        <div className="text-center text-white">
                          <VideoOff className="h-12 w-12 mx-auto mb-2" />
                          <p className="text-sm">{participant.name}</p>
                        </div>
                      </div>
                    )}
                    
                    {/* Participant Info */}
                    <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm">
                      {participant.name}
                      {participant.isHost && ' (Host)'}
                    </div>
                    
                    {/* Audio Indicator */}
                    <div className="absolute top-2 right-2">
                      {participant.audioEnabled ? (
                        <Volume2 className="h-4 w-4 text-green-400" />
                      ) : (
                        <VolumeX className="h-4 w-4 text-red-400" />
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Controls */}
        <div className="bg-gray-900 p-4">
          <div className="flex items-center justify-center gap-4">
            {/* Audio Control */}
            <Button
              variant={localState.audioEnabled ? "secondary" : "destructive"}
              size="lg"
              onClick={toggleAudio}
              className="rounded-full w-12 h-12 p-0"
            >
              {localState.audioEnabled ? (
                <Mic className="h-5 w-5" />
              ) : (
                <MicOff className="h-5 w-5" />
              )}
            </Button>

            {/* Video Control */}
            <Button
              variant={localState.videoEnabled ? "secondary" : "destructive"}
              size="lg"
              onClick={toggleVideo}
              className="rounded-full w-12 h-12 p-0"
            >
              {localState.videoEnabled ? (
                <Video className="h-5 w-5" />
              ) : (
                <VideoOff className="h-5 w-5" />
              )}
            </Button>

            {/* Screen Share */}
            <Button
              variant={localState.screenSharing ? "default" : "secondary"}
              size="lg"
              onClick={toggleScreenShare}
              className="rounded-full w-12 h-12 p-0"
            >
              <Monitor className="h-5 w-5" />
            </Button>

            {/* Chat */}
            <Button
              variant="secondary"
              size="lg"
              onClick={toggleChat}
              className="rounded-full w-12 h-12 p-0"
            >
              <MessageSquare className="h-5 w-5" />
            </Button>

            {/* Participants */}
            <Button
              variant="secondary"
              size="lg"
              onClick={toggleParticipants}
              className="rounded-full w-12 h-12 p-0"
            >
              <Users className="h-5 w-5" />
            </Button>

            {/* Settings */}
            <Button
              variant="secondary"
              size="lg"
              className="rounded-full w-12 h-12 p-0"
            >
              <Settings className="h-5 w-5" />
            </Button>

            {/* More Options */}
            <Button
              variant="secondary"
              size="lg"
              className="rounded-full w-12 h-12 p-0"
            >
              <MoreVertical className="h-5 w-5" />
            </Button>

            {/* Leave Meeting */}
            <Button
              variant="destructive"
              size="lg"
              onClick={leaveMeeting}
              className="rounded-full w-12 h-12 p-0 ml-4"
            >
              <PhoneOff className="h-5 w-5" />
            </Button>
          </div>
        </div>

        {/* Chat Sidebar */}
        {localState.chatOpen && (
          <div className="fixed right-0 top-0 h-full w-80 bg-white shadow-lg z-50">
            <div className="p-4 border-b">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold">Meeting Chat</h3>
                <Button variant="ghost" size="sm" onClick={toggleChat}>
                  ×
                </Button>
              </div>
            </div>
            <div className="flex-1 p-4">
              <p className="text-gray-500 text-center">Chat functionality coming soon...</p>
            </div>
          </div>
        )}

        {/* Participants Sidebar */}
        {localState.participantsOpen && (
          <div className="fixed right-0 top-0 h-full w-80 bg-white shadow-lg z-50">
            <div className="p-4 border-b">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold">Participants ({meetingInfo.participantCount})</h3>
                <Button variant="ghost" size="sm" onClick={toggleParticipants}>
                  ×
                </Button>
              </div>
            </div>
            <div className="flex-1 p-4">
              <div className="space-y-2">
                <div className="flex items-center gap-3 p-2 rounded-lg bg-gray-50">
                  <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                    Y
                  </div>
                  <div className="flex-1">
                    <p className="font-medium">You</p>
                    <p className="text-xs text-gray-500">Host</p>
                  </div>
                </div>
                {participants.map((participant) => (
                  <div key={participant.id} className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50">
                    <div className="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                      {participant.name.charAt(0)}
                    </div>
                    <div className="flex-1">
                      <p className="font-medium">{participant.name}</p>
                      {participant.isHost && <p className="text-xs text-gray-500">Host</p>}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default MeetingVideoPage;
