import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { useToast } from '@/hooks/use-toast';
import { 
  Video, 
  VideoOff, 
  Mic, 
  MicOff,
  Settings,
  Users,
  Clock,
  Shield,
  AlertCircle,
  CheckCircle,
  Loader2
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import SEOHelmet from '../../components/common/SEOHelmet';

interface MeetingInfo {
  id: string;
  topic: string;
  host: string;
  startTime: string;
  duration: string;
  participants: number;
  requiresPassword: boolean;
  waitingRoom: boolean;
}

const MeetingJoinPage: React.FC = () => {
  const { meetingId } = useParams<{ meetingId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { authState } = useAuth();

  // State
  const [meetingInfo, setMeetingInfo] = useState<MeetingInfo | null>(null);
  const [displayName, setDisplayName] = useState('');
  const [password, setPassword] = useState('');
  const [videoEnabled, setVideoEnabled] = useState(true);
  const [audioEnabled, setAudioEnabled] = useState(true);
  const [loading, setLoading] = useState(true);
  const [joining, setJoining] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [devicePermissions, setDevicePermissions] = useState({
    camera: false,
    microphone: false,
    checking: true
  });

  // Load meeting info
  useEffect(() => {
    if (meetingId) {
      loadMeetingInfo();
    }
  }, [meetingId]);

  // Set default display name from auth
  useEffect(() => {
    if (authState.user) {
      setDisplayName(`${authState.user.Firstname || ''} ${authState.user.Lastname || ''}`.trim());
    }
  }, [authState.user]);

  // Check device permissions
  useEffect(() => {
    checkDevicePermissions();
  }, []);

  const loadMeetingInfo = async () => {
    if (!meetingId) return;

    try {
      setLoading(true);
      setError(null);

      // TODO: Implement real API call to get meeting info
      // const info = await meetingService.getMeetingInfo(meetingId);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock meeting info
      setMeetingInfo({
        id: meetingId,
        topic: 'React Development Workshop',
        host: 'John Doe',
        startTime: '2024-01-15 10:00 AM',
        duration: '1 hour',
        participants: 5,
        requiresPassword: true,
        waitingRoom: true
      });

    } catch (err) {
      console.error('Error loading meeting info:', err);
      setError('Failed to load meeting information');
    } finally {
      setLoading(false);
    }
  };

  const checkDevicePermissions = async () => {
    try {
      setDevicePermissions(prev => ({ ...prev, checking: true }));

      // Check camera permission
      try {
        const videoStream = await navigator.mediaDevices.getUserMedia({ video: true });
        videoStream.getTracks().forEach(track => track.stop());
        setDevicePermissions(prev => ({ ...prev, camera: true }));
      } catch (error) {
        console.log('Camera permission denied or not available');
        setDevicePermissions(prev => ({ ...prev, camera: false }));
      }

      // Check microphone permission
      try {
        const audioStream = await navigator.mediaDevices.getUserMedia({ audio: true });
        audioStream.getTracks().forEach(track => track.stop());
        setDevicePermissions(prev => ({ ...prev, microphone: true }));
      } catch (error) {
        console.log('Microphone permission denied or not available');
        setDevicePermissions(prev => ({ ...prev, microphone: false }));
      }

    } catch (error) {
      console.error('Error checking device permissions:', error);
    } finally {
      setDevicePermissions(prev => ({ ...prev, checking: false }));
    }
  };

  const handleJoinMeeting = async () => {
    if (!displayName.trim()) {
      toast({
        title: 'Display Name Required',
        description: 'Please enter your display name',
        variant: 'destructive'
      });
      return;
    }

    if (meetingInfo?.requiresPassword && !password.trim()) {
      toast({
        title: 'Password Required',
        description: 'This meeting requires a password',
        variant: 'destructive'
      });
      return;
    }

    try {
      setJoining(true);
      setError(null);

      // TODO: Implement real meeting join logic
      // const joinResult = await meetingService.joinMeeting({
      //   meetingId,
      //   displayName,
      //   password,
      //   videoEnabled,
      //   audioEnabled
      // });

      // Simulate join process
      await new Promise(resolve => setTimeout(resolve, 2000));

      toast({
        title: 'Joining Meeting',
        description: 'Connecting to the meeting room...',
      });

      // Navigate to video meeting room
      navigate(`/meetings/${meetingId}/video`, {
        state: {
          displayName,
          videoEnabled,
          audioEnabled
        }
      });

    } catch (error) {
      console.error('Error joining meeting:', error);
      setError('Failed to join meeting. Please check your credentials and try again.');
      toast({
        title: 'Join Failed',
        description: 'Failed to join meeting. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setJoining(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Loading meeting information...</p>
        </div>
      </div>
    );
  }

  if (error && !meetingInfo) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
            <div className="mt-4 text-center">
              <Button onClick={() => navigate('/meetings')} variant="outline">
                Back to Meetings
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <>
      <SEOHelmet
        title={`Join Meeting - ${meetingInfo?.topic || 'Meeting'}`}
        description="Join a video meeting"
      />

      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Meeting Info */}
          <Card className="mb-8">
            <CardHeader className="text-center">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <Video className="h-8 w-8 text-white" />
              </div>
              <CardTitle className="text-2xl">{meetingInfo?.topic}</CardTitle>
              <p className="text-gray-600">Hosted by {meetingInfo?.host}</p>
            </CardHeader>
            
            <CardContent>
              <div className="grid grid-cols-2 gap-4 text-center">
                <div className="p-4 bg-gray-50 rounded-lg">
                  <Clock className="h-6 w-6 mx-auto mb-2 text-gray-600" />
                  <p className="text-sm font-medium">{meetingInfo?.startTime}</p>
                  <p className="text-xs text-gray-500">Duration: {meetingInfo?.duration}</p>
                </div>
                <div className="p-4 bg-gray-50 rounded-lg">
                  <Users className="h-6 w-6 mx-auto mb-2 text-gray-600" />
                  <p className="text-sm font-medium">{meetingInfo?.participants} Participants</p>
                  <p className="text-xs text-gray-500">Currently in meeting</p>
                </div>
              </div>

              {meetingInfo?.waitingRoom && (
                <Alert className="mt-4">
                  <Shield className="h-4 w-4" />
                  <AlertDescription>
                    This meeting has a waiting room enabled. The host will admit you after you join.
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>

          {/* Join Form */}
          <Card>
            <CardHeader>
              <CardTitle>Join Meeting</CardTitle>
            </CardHeader>
            
            <CardContent className="space-y-6">
              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {/* Display Name */}
              <div>
                <Label htmlFor="displayName">Display Name *</Label>
                <Input
                  id="displayName"
                  value={displayName}
                  onChange={(e) => setDisplayName(e.target.value)}
                  placeholder="Enter your name as it will appear to others"
                  className="mt-1"
                  required
                />
              </div>

              {/* Password */}
              {meetingInfo?.requiresPassword && (
                <div>
                  <Label htmlFor="password">Meeting Password *</Label>
                  <Input
                    id="password"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter meeting password"
                    className="mt-1"
                    required
                  />
                </div>
              )}

              <Separator />

              {/* Device Settings */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Audio & Video Settings
                </h3>

                {/* Device Permissions Status */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-2">
                      <Video className="h-4 w-4" />
                      <span className="text-sm">Camera Access</span>
                    </div>
                    {devicePermissions.checking ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : devicePermissions.camera ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <AlertCircle className="h-4 w-4 text-red-600" />
                    )}
                  </div>

                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-2">
                      <Mic className="h-4 w-4" />
                      <span className="text-sm">Microphone Access</span>
                    </div>
                    {devicePermissions.checking ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : devicePermissions.microphone ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <AlertCircle className="h-4 w-4 text-red-600" />
                    )}
                  </div>
                </div>

                {/* Audio/Video Controls */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {videoEnabled ? <Video className="h-4 w-4" /> : <VideoOff className="h-4 w-4" />}
                      <Label>Join with video</Label>
                    </div>
                    <Switch
                      checked={videoEnabled}
                      onCheckedChange={setVideoEnabled}
                      disabled={!devicePermissions.camera}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {audioEnabled ? <Mic className="h-4 w-4" /> : <MicOff className="h-4 w-4" />}
                      <Label>Join with audio</Label>
                    </div>
                    <Switch
                      checked={audioEnabled}
                      onCheckedChange={setAudioEnabled}
                      disabled={!devicePermissions.microphone}
                    />
                  </div>
                </div>

                {(!devicePermissions.camera || !devicePermissions.microphone) && (
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      Some devices are not accessible. Please check your browser permissions and refresh the page.
                    </AlertDescription>
                  </Alert>
                )}
              </div>

              <Separator />

              {/* Join Button */}
              <div className="flex justify-center">
                <Button
                  onClick={handleJoinMeeting}
                  disabled={joining || !displayName.trim()}
                  size="lg"
                  className="bg-blue-600 hover:bg-blue-700 px-8"
                >
                  {joining ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Joining...
                    </>
                  ) : (
                    <>
                      <Video className="h-4 w-4 mr-2" />
                      Join Meeting
                    </>
                  )}
                </Button>
              </div>

              <div className="text-center">
                <Button
                  variant="ghost"
                  onClick={() => navigate('/meetings')}
                  className="text-sm"
                >
                  Back to Meetings
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
};

export default MeetingJoinPage;
