import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, Settings, Shield, BarChart3, Target } from 'lucide-react';
import SEOHelmet from '../../components/common/SEOHelmet';

const CookiesPage: React.FC = () => {
  const lastUpdated = "January 15, 2024";

  const cookieTypes = [
    {
      title: "Essential Cookies",
      icon: Shield,
      description: "Required for basic platform functionality",
      examples: [
        "Authentication and login status",
        "Shopping cart contents",
        "Security and fraud prevention",
        "Basic platform preferences"
      ],
      canDisable: false
    },
    {
      title: "Performance Cookies",
      icon: BarChart3,
      description: "Help us understand how you use our platform",
      examples: [
        "Page load times and performance metrics",
        "Error tracking and debugging",
        "Feature usage statistics",
        "Platform optimization data"
      ],
      canDisable: true
    },
    {
      title: "Functional Cookies",
      icon: Settings,
      description: "Remember your preferences and settings",
      examples: [
        "Language and region preferences",
        "Display settings and themes",
        "Course progress and bookmarks",
        "Personalized recommendations"
      ],
      canDisable: true
    },
    {
      title: "Marketing Cookies",
      icon: Target,
      description: "Used to deliver relevant advertisements",
      examples: [
        "Targeted course recommendations",
        "Social media integration",
        "Third-party advertising",
        "Marketing campaign tracking"
      ],
      canDisable: true
    }
  ];

  return (
    <>
      <SEOHelmet
        title="Cookie Policy - BrainMaker Academy"
        description="Learn about how BrainMaker Academy uses cookies and similar technologies to improve your learning experience."
        keywords="cookies, tracking, privacy, web storage, preferences"
      />

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div className="text-center">
              <Cookie className="w-16 h-16 text-red-600 mx-auto mb-4" />
              <h1 className="text-4xl font-bold text-gray-900 mb-4">Cookie Policy</h1>
              <p className="text-lg text-gray-600">
                Learn how we use cookies and similar technologies to enhance your 
                learning experience on BrainMaker Academy.
              </p>
              <p className="text-sm text-gray-500 mt-4">
                Last updated: {lastUpdated}
              </p>
            </div>
          </div>
        </div>

        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Introduction */}
          <Card className="mb-8">
            <CardContent className="pt-6">
              <p className="text-lg text-gray-700 leading-relaxed mb-4">
                This Cookie Policy explains how BrainMaker Academy uses cookies and similar 
                technologies when you visit our website and use our services. It explains 
                what these technologies are, why we use them, and your rights to control 
                their use.
              </p>
              <p className="text-gray-700 leading-relaxed">
                By continuing to use our platform, you consent to our use of cookies as 
                described in this policy. You can change your cookie preferences at any 
                time through your browser settings or our cookie preference center.
              </p>
            </CardContent>
          </Card>

          {/* What are Cookies */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-3 text-xl text-gray-900">
                <Cookie className="w-6 h-6 text-red-600" />
                What are Cookies?
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 mb-4">
                Cookies are small text files that are stored on your device when you visit 
                a website. They help websites remember information about your visit, such 
                as your preferred language and other settings.
              </p>
              <p className="text-gray-700">
                We also use similar technologies like web beacons, pixels, and local storage 
                to enhance your experience and understand how our platform is used.
              </p>
            </CardContent>
          </Card>

          {/* Cookie Types */}
          <div className="space-y-6 mb-8">
            <h2 className="text-2xl font-bold text-gray-900">Types of Cookies We Use</h2>
            {cookieTypes.map((type, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-3 text-lg text-gray-900">
                      <type.icon className="w-5 h-5 text-red-600" />
                      {type.title}
                    </div>
                    <div className="flex items-center gap-2">
                      {type.canDisable ? (
                        <span className="text-sm text-green-600 bg-green-100 px-2 py-1 rounded">
                          Optional
                        </span>
                      ) : (
                        <span className="text-sm text-red-600 bg-red-100 px-2 py-1 rounded">
                          Required
                        </span>
                      )}
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700 mb-4">{type.description}</p>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">Examples:</h4>
                    <ul className="space-y-1">
                      {type.examples.map((example, exampleIndex) => (
                        <li key={exampleIndex} className="flex items-start gap-2">
                          <div className="w-1.5 h-1.5 bg-red-600 rounded-full mt-2 flex-shrink-0" />
                          <span className="text-gray-600 text-sm">{example}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Managing Cookies */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-3 text-xl text-gray-900">
                <Settings className="w-6 h-6 text-red-600" />
                Managing Your Cookie Preferences
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 mb-4">
                You have several options for managing cookies:
              </p>
              <div className="space-y-4">
                <div className="border-l-4 border-red-600 pl-4">
                  <h4 className="font-semibold text-gray-900 mb-2">Browser Settings</h4>
                  <p className="text-gray-700 text-sm">
                    Most browsers allow you to control cookies through their settings. 
                    You can block all cookies, accept only first-party cookies, or 
                    delete existing cookies.
                  </p>
                </div>
                <div className="border-l-4 border-red-600 pl-4">
                  <h4 className="font-semibold text-gray-900 mb-2">Cookie Preference Center</h4>
                  <p className="text-gray-700 text-sm">
                    Use our cookie preference center to customize which types of 
                    cookies you want to accept or reject.
                  </p>
                </div>
                <div className="border-l-4 border-red-600 pl-4">
                  <h4 className="font-semibold text-gray-900 mb-2">Opt-out Tools</h4>
                  <p className="text-gray-700 text-sm">
                    Some third-party services provide opt-out tools for their 
                    tracking technologies.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Third Party Cookies */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-3 text-xl text-gray-900">
                <Target className="w-6 h-6 text-red-600" />
                Third-Party Cookies
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 mb-4">
                We may allow trusted third-party services to place cookies on our 
                platform for the following purposes:
              </p>
              <ul className="space-y-2">
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-red-600 rounded-full mt-2 flex-shrink-0" />
                  <span className="text-gray-700">Analytics and performance monitoring</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-red-600 rounded-full mt-2 flex-shrink-0" />
                  <span className="text-gray-700">Social media integration and sharing</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-red-600 rounded-full mt-2 flex-shrink-0" />
                  <span className="text-gray-700">Payment processing and security</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-red-600 rounded-full mt-2 flex-shrink-0" />
                  <span className="text-gray-700">Customer support and live chat</span>
                </li>
              </ul>
            </CardContent>
          </Card>

          {/* Contact Section */}
          <Card className="bg-gradient-to-r from-red-50 to-red-100 border-red-200">
            <CardContent className="pt-6">
              <div className="text-center">
                <Settings className="w-12 h-12 text-red-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Cookie Preferences</h3>
                <p className="text-gray-700 mb-6">
                  Want to change your cookie settings? You can manage your preferences 
                  or contact us with any questions about our cookie usage.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button className="bg-red-600 hover:bg-red-700 text-white">
                    Manage Cookie Preferences
                  </Button>
                  <Button variant="outline" className="border-red-600 text-red-600 hover:bg-red-600 hover:text-white">
                    Contact Us
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
};

export default CookiesPage;
