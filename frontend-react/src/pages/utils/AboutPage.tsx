import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Users, Target, Award, Globe, Heart, BookOpen } from 'lucide-react';
import SEOHelmet from '../../components/common/SEOHelmet';

const AboutPage: React.FC = () => {
  const stats = [
    { icon: Users, label: 'Active Students', value: '50,000+', color: 'text-blue-600' },
    { icon: BookOpen, label: 'Courses Available', value: '2,500+', color: 'text-green-600' },
    { icon: Award, label: 'Expert Instructors', value: '1,200+', color: 'text-purple-600' },
    { icon: Globe, label: 'Countries Served', value: '150+', color: 'text-red-600' }
  ];

  const values = [
    {
      title: 'Quality Education',
      description: 'We believe in providing high-quality, practical education that prepares learners for real-world challenges.',
      icon: Award
    },
    {
      title: 'Accessibility',
      description: 'Making quality education accessible to everyone, regardless of location or background.',
      icon: Globe
    },
    {
      title: 'Community',
      description: 'Building a supportive learning community where students and instructors can thrive together.',
      icon: Users
    },
    {
      title: 'Innovation',
      description: 'Continuously improving our platform with the latest technology and teaching methodologies.',
      icon: Target
    }
  ];

  return (
    <>
      <SEOHelmet
        title="About BrainMaker Academy"
        description="Learn about BrainMaker Academy's mission to provide world-class online education. Discover our story, values, and commitment to learner success."
        keywords="about brainmaker, online education, e-learning platform, mission, values, team"
      />

      <div className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <div className="bg-gradient-to-r from-red-600 to-red-800 text-white py-20">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                About BrainMaker Academy
              </h1>
              <p className="text-xl md:text-2xl text-red-100 max-w-3xl mx-auto">
                Empowering minds through expert-led online education since 2020
              </p>
            </div>
          </div>
        </div>

        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          {/* Mission Section */}
          <Card className="mb-12">
            <CardHeader>
              <CardTitle className="text-3xl text-center text-gray-900">Our Mission</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-lg text-gray-700 text-center max-w-4xl mx-auto leading-relaxed">
                At BrainMaker Academy, we're dedicated to democratizing education by providing 
                high-quality, accessible online learning experiences. We believe that everyone 
                deserves the opportunity to learn, grow, and achieve their professional goals, 
                regardless of their location or circumstances.
              </p>
            </CardContent>
          </Card>

          {/* Stats Section */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
            {stats.map((stat, index) => (
              <Card key={index} className="text-center">
                <CardContent className="pt-6">
                  <stat.icon className={`w-12 h-12 ${stat.color} mx-auto mb-4`} />
                  <div className="text-3xl font-bold text-gray-900 mb-2">{stat.value}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Story Section */}
          <Card className="mb-12">
            <CardHeader>
              <CardTitle className="text-3xl text-center text-gray-900">Our Story</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <p className="text-lg text-gray-700 leading-relaxed">
                Founded in 2020, BrainMaker Academy emerged from a simple yet powerful vision: 
                to make world-class education accessible to learners everywhere. Our founders, 
                experienced educators and technology professionals, recognized the transformative 
                potential of online learning.
              </p>
              <p className="text-lg text-gray-700 leading-relaxed">
                What started as a small platform with a handful of courses has grown into a 
                comprehensive learning ecosystem serving over 50,000 students worldwide. We've 
                partnered with industry experts, renowned instructors, and leading companies to 
                create courses that are not just educational, but truly transformational.
              </p>
              <p className="text-lg text-gray-700 leading-relaxed">
                Today, BrainMaker Academy continues to innovate in the online education space, 
                incorporating cutting-edge technology, interactive learning experiences, and 
                personalized learning paths to help our students achieve their goals.
              </p>
            </CardContent>
          </Card>

          {/* Values Section */}
          <div className="mb-12">
            <h2 className="text-3xl font-bold text-center text-gray-900 mb-8">Our Values</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {values.map((value, index) => (
                <Card key={index}>
                  <CardContent className="pt-6">
                    <div className="flex items-start space-x-4">
                      <value.icon className="w-8 h-8 text-red-600 flex-shrink-0 mt-1" />
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">{value.title}</h3>
                        <p className="text-gray-700">{value.description}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Contact CTA */}
          <Card className="bg-gradient-to-r from-red-50 to-red-100 border-red-200">
            <CardContent className="pt-8 text-center">
              <Heart className="w-12 h-12 text-red-600 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Join Our Learning Community</h3>
              <p className="text-lg text-gray-700 mb-6 max-w-2xl mx-auto">
                Ready to start your learning journey? Join thousands of students who are 
                already transforming their careers with BrainMaker Academy.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a 
                  href="/courses" 
                  className="bg-red-600 hover:bg-red-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
                >
                  Browse Courses
                </a>
                <a 
                  href="/contact" 
                  className="border border-red-600 text-red-600 hover:bg-red-600 hover:text-white px-8 py-3 rounded-lg font-semibold transition-colors"
                >
                  Contact Us
                </a>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
};

export default AboutPage;
