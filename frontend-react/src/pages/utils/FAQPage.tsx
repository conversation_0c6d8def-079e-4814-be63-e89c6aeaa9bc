import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { FaSearch, FaQuestionCircle, FaQuestion, FaEnvelope } from 'react-icons/fa';
import { ChevronDown, Search, HelpCircle, Mail } from 'lucide-react';

const FAQPage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [activeCategory, setActiveCategory] = useState('general');
  const [openFAQ, setOpenFAQ] = useState<number | null>(null);

  // FAQ categories
  const faqCategories = [
    { id: 'general', name: 'General Questions' },
    { id: 'account', name: 'Account & Profile' },
    { id: 'courses', name: 'Courses & Learning' },
    { id: 'payment', name: 'Payment & Billing' },
    { id: 'technical', name: 'Technical Issues' },
    { id: 'instructors', name: 'For Instructors' }
  ];

  // FAQ data
  const faqs = [
    {
      id: 1,
      question: 'What is BrainMaker?',
      answer: 'BrainMaker is an online learning platform that offers a wide range of courses in various subjects including programming, design, business, and more. Our mission is to provide high-quality education that is accessible to everyone.',
      category: 'general'
    },
    {
      id: 2,
      question: 'How do I create an account?',
      answer: 'To create an account, click on the "Sign Up" button in the top right corner of the page. You can sign up using your email address or through your Google or Facebook account. Follow the prompts to complete your registration.',
      category: 'account'
    },
    {
      id: 3,
      question: 'How do I reset my password?',
      answer: 'If you forgot your password, click on the "Sign In" button, then click on "Forgot Password". Enter your email address, and we\'ll send you a link to reset your password. Follow the instructions in the email to create a new password.',
      category: 'account'
    },
    {
      id: 4,
      question: 'How do I enroll in a course?',
      answer: 'To enroll in a course, browse our course catalog, select the course you\'re interested in, and click the "Enroll Now" button. You\'ll need to complete the payment process to gain access to the course materials.',
      category: 'courses'
    },
    {
      id: 5,
      question: 'Can I get a refund?',
      answer: 'Yes, we offer a 30-day money-back guarantee for most courses. To request a refund, go to your account dashboard, find the course, and click on "Request Refund". Refunds are processed within 5-7 business days.',
      category: 'payment'
    },
    {
      id: 6,
      question: 'What payment methods do you accept?',
      answer: 'We accept all major credit cards (Visa, MasterCard, American Express), PayPal, and bank transfers. All payments are processed securely through our encrypted payment system.',
      category: 'payment'
    },
    {
      id: 7,
      question: 'How do I become an instructor?',
      answer: 'To become an instructor, click on "Teach on BrainMaker" in the footer. You\'ll need to submit an application with information about your expertise, teaching experience, and the courses you want to create. Our team will review your application within 3-5 business days.',
      category: 'instructors'
    },
    {
      id: 8,
      question: 'I\'m having trouble accessing my course',
      answer: 'If you\'re having trouble accessing your course, try clearing your browser cache and cookies, or try using a different browser. If the problem persists, please contact our support team with details about the issue.',
      category: 'technical'
    },
    {
      id: 9,
      question: 'Do you offer certificates?',
      answer: 'Yes, we offer certificates of completion for most courses. Once you complete all the course requirements, you\'ll receive a digital certificate that you can download and share on your professional profiles.',
      category: 'courses'
    },
    {
      id: 10,
      question: 'Can I access courses on mobile devices?',
      answer: 'Yes, our platform is fully responsive and works on all devices including smartphones and tablets. You can also download our mobile app for iOS and Android for a better mobile learning experience.',
      category: 'technical'
    }
  ];

  // Filter FAQs based on search term and category
  const filteredFAQs = faqs.filter(faq => {
    const matchesSearch = faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = activeCategory === 'all' || faq.category === activeCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-6xl mx-auto px-6">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <Avatar className="w-16 h-16 bg-blue-600">
              <AvatarFallback>
                <HelpCircle className="w-8 h-8 text-white" />
              </AvatarFallback>
            </Avatar>
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Find answers to common questions about BrainMaker. Can't find what you're looking for? Contact our support team.
          </p>
        </div>

        {/* Search */}
        <div className="mb-8">
          <div className="relative max-w-md mx-auto">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search FAQs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Categories and FAQs */}
        <Tabs value={activeCategory} onValueChange={setActiveCategory}>
          <TabsList className="grid w-full grid-cols-3 lg:grid-cols-6">
            {faqCategories.map(category => (
              <TabsTrigger key={category.id} value={category.id} className="text-xs">
                {category.name}
              </TabsTrigger>
            ))}
          </TabsList>

          {faqCategories.map(category => (
            <TabsContent key={category.id} value={category.id} className="mt-8">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FaQuestion className="w-5 h-5 text-blue-600" />
                    {category.name}
                    <Badge variant="secondary">
                      {filteredFAQs.filter(faq => faq.category === category.id).length}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {filteredFAQs.filter(faq => faq.category === category.id).length === 0 ? (
                    <div className="text-center py-12">
                      <HelpCircle className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No FAQs found</h3>
                      <p className="text-gray-500">No questions match your search in this category.</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {filteredFAQs
                        .filter(faq => faq.category === category.id)
                        .map((faq) => (
                          <Collapsible
                            key={faq.id}
                            open={openFAQ === faq.id}
                            onOpenChange={(open) => setOpenFAQ(open ? faq.id : null)}
                          >
                            <CollapsibleTrigger className="flex items-center justify-between w-full p-4 text-left bg-white hover:bg-gray-50 rounded-lg border transition-colors">
                              <span className="font-medium pr-4">{faq.question}</span>
                              <ChevronDown className={`w-4 h-4 transition-transform flex-shrink-0 ${openFAQ === faq.id ? 'rotate-180' : ''}`} />
                            </CollapsibleTrigger>
                            <CollapsibleContent className="px-4 pb-4">
                              <p className="text-gray-600 leading-relaxed">{faq.answer}</p>
                            </CollapsibleContent>
                          </Collapsible>
                        ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          ))}
        </Tabs>

        {/* Contact Support */}
        <div className="mt-16">
          <Card>
            <CardContent className="text-center py-12">
              <Mail className="h-12 w-12 mx-auto mb-4 text-blue-600" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Still have questions?</h3>
              <p className="text-gray-600 mb-6">
                Can't find the answer you're looking for? Our support team is here to help.
              </p>
              <Button>
                <FaEnvelope className="w-4 h-4 mr-2" />
                Contact Support
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default FAQPage;