import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Shield, Eye, Lock, UserCheck, FileText, Mail } from 'lucide-react';
import SEOHelmet from '../../components/common/SEOHelmet';

const PrivacyPolicyPage: React.FC = () => {
  const lastUpdated = "January 15, 2024";

  const sections = [
    {
      title: "Information We Collect",
      icon: Eye,
      content: [
        "Personal information you provide when creating an account (name, email, profile information)",
        "Course progress and learning data to track your educational journey",
        "Payment information processed securely through our payment partners",
        "Technical information about your device and how you use our platform",
        "Communications you send to us through support channels"
      ]
    },
    {
      title: "How We Use Your Information",
      icon: UserCheck,
      content: [
        "Provide and improve our educational services and platform functionality",
        "Personalize your learning experience and recommend relevant courses",
        "Process payments and manage your account and subscriptions",
        "Send important updates about courses, platform changes, and account activity",
        "Provide customer support and respond to your inquiries",
        "Ensure platform security and prevent fraud or abuse"
      ]
    },
    {
      title: "Information Sharing",
      icon: Shield,
      content: [
        "We do not sell your personal information to third parties",
        "Course instructors may see your progress in their courses for educational purposes",
        "We may share anonymized, aggregated data for research and improvement purposes",
        "We may disclose information when required by law or to protect our rights",
        "Service providers who help us operate the platform under strict confidentiality agreements"
      ]
    },
    {
      title: "Data Security",
      icon: Lock,
      content: [
        "We use industry-standard encryption to protect your data in transit and at rest",
        "Regular security audits and monitoring to detect and prevent unauthorized access",
        "Secure payment processing through PCI-compliant payment processors",
        "Employee access to personal data is limited and monitored",
        "Regular backups and disaster recovery procedures to protect your information"
      ]
    },
    {
      title: "Your Rights",
      icon: FileText,
      content: [
        "Access and download your personal data at any time through your account settings",
        "Correct or update your personal information in your profile",
        "Delete your account and associated data (subject to legal retention requirements)",
        "Opt out of marketing communications while still receiving essential service updates",
        "Request information about how your data is processed and stored"
      ]
    }
  ];

  return (
    <>
      <SEOHelmet
        title="Privacy Policy - BrainMaker Academy"
        description="Learn how BrainMaker Academy collects, uses, and protects your personal information. Our commitment to your privacy and data security."
        keywords="privacy policy, data protection, personal information, security, GDPR"
      />

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div className="text-center">
              <Shield className="w-16 h-16 text-red-600 mx-auto mb-4" />
              <h1 className="text-4xl font-bold text-gray-900 mb-4">Privacy Policy</h1>
              <p className="text-lg text-gray-600">
                Your privacy is important to us. This policy explains how we collect, 
                use, and protect your information.
              </p>
              <p className="text-sm text-gray-500 mt-4">
                Last updated: {lastUpdated}
              </p>
            </div>
          </div>
        </div>

        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Introduction */}
          <Card className="mb-8">
            <CardContent className="pt-6">
              <p className="text-lg text-gray-700 leading-relaxed mb-4">
                At BrainMaker Academy, we are committed to protecting your privacy and ensuring 
                the security of your personal information. This Privacy Policy explains how we 
                collect, use, disclose, and safeguard your information when you use our online 
                learning platform.
              </p>
              <p className="text-gray-700 leading-relaxed">
                By using our services, you agree to the collection and use of information in 
                accordance with this policy. We encourage you to read this policy carefully 
                and contact us if you have any questions.
              </p>
            </CardContent>
          </Card>

          {/* Policy Sections */}
          <div className="space-y-8">
            {sections.map((section, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="flex items-center gap-3 text-xl text-gray-900">
                    <section.icon className="w-6 h-6 text-red-600" />
                    {section.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    {section.content.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-start gap-3">
                        <div className="w-2 h-2 bg-red-600 rounded-full mt-2 flex-shrink-0" />
                        <span className="text-gray-700">{item}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Cookies Section */}
          <Card className="mt-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-3 text-xl text-gray-900">
                <FileText className="w-6 h-6 text-red-600" />
                Cookies and Tracking
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 mb-4">
                We use cookies and similar tracking technologies to enhance your experience 
                on our platform. These help us:
              </p>
              <ul className="space-y-2 mb-4">
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-red-600 rounded-full mt-2 flex-shrink-0" />
                  <span className="text-gray-700">Remember your login status and preferences</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-red-600 rounded-full mt-2 flex-shrink-0" />
                  <span className="text-gray-700">Analyze platform usage to improve our services</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-red-600 rounded-full mt-2 flex-shrink-0" />
                  <span className="text-gray-700">Provide personalized content and recommendations</span>
                </li>
              </ul>
              <p className="text-gray-700">
                You can control cookie settings through your browser preferences. However, 
                disabling certain cookies may affect platform functionality.
              </p>
            </CardContent>
          </Card>

          {/* Contact Section */}
          <Card className="mt-8 bg-gradient-to-r from-red-50 to-red-100 border-red-200">
            <CardContent className="pt-6">
              <div className="text-center">
                <Mail className="w-12 h-12 text-red-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Questions About Privacy?</h3>
                <p className="text-gray-700 mb-6">
                  If you have any questions about this Privacy Policy or how we handle your 
                  personal information, please don't hesitate to contact us.
                </p>
                <div className="space-y-2 text-sm text-gray-600">
                  <p>Email: <EMAIL></p>
                  <p>Address: 580 rue de Fontenay, Laval, QC</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
};

export default PrivacyPolicyPage;
