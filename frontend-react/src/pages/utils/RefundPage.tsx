import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { RefreshCw, Clock, CreditCard, CheckCircle, XCircle, Mail } from 'lucide-react';
import SEOHelmet from '../../components/common/SEOHelmet';

const RefundPage: React.FC = () => {
  const lastUpdated = "January 15, 2024";

  const refundScenarios = [
    {
      title: "Course Purchases",
      icon: CheckCircle,
      eligible: true,
      timeframe: "30 days",
      conditions: [
        "Course was purchased within the last 30 days",
        "You have completed less than 30% of the course content",
        "No certificates have been issued for the course",
        "Request is made through proper channels"
      ]
    },
    {
      title: "Subscription Services",
      icon: XCircle,
      eligible: false,
      timeframe: "Not applicable",
      conditions: [
        "Monthly and annual subscriptions are non-refundable",
        "You can cancel anytime to prevent future charges",
        "Access continues until the end of current billing period",
        "Partial refunds may be considered for exceptional circumstances"
      ]
    },
    {
      title: "Bundle Purchases",
      icon: CheckCircle,
      eligible: true,
      timeframe: "30 days",
      conditions: [
        "Entire bundle must be returned (individual courses cannot be refunded)",
        "Less than 30% completion across all courses in bundle",
        "No certificates issued for any course in the bundle",
        "Original payment method must be available for refund"
      ]
    },
    {
      title: "Gift Purchases",
      icon: CheckCircle,
      eligible: true,
      timeframe: "30 days from purchase",
      conditions: [
        "Refund period starts from original purchase date, not gift redemption",
        "Gift recipient must not have started the course",
        "Refund goes back to original purchaser's payment method",
        "Gift codes cannot be refunded once redeemed and started"
      ]
    }
  ];

  const refundProcess = [
    {
      step: 1,
      title: "Submit Request",
      description: "Contact our support team with your refund request and order details"
    },
    {
      step: 2,
      title: "Review Process",
      description: "We'll review your request within 2-3 business days"
    },
    {
      step: 3,
      title: "Approval Decision",
      description: "You'll receive an email with our decision and next steps"
    },
    {
      step: 4,
      title: "Refund Processing",
      description: "Approved refunds are processed within 5-7 business days"
    }
  ];

  return (
    <>
      <SEOHelmet
        title="Refund Policy - BrainMaker Academy"
        description="Learn about BrainMaker Academy's refund policy for courses, subscriptions, and other services. Understand your refund rights and process."
        keywords="refund policy, money back guarantee, course refunds, cancellation, returns"
      />

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div className="text-center">
              <RefreshCw className="w-16 h-16 text-red-600 mx-auto mb-4" />
              <h1 className="text-4xl font-bold text-gray-900 mb-4">Refund Policy</h1>
              <p className="text-lg text-gray-600">
                We want you to be completely satisfied with your learning experience. 
                Learn about our refund policy and process.
              </p>
              <p className="text-sm text-gray-500 mt-4">
                Last updated: {lastUpdated}
              </p>
            </div>
          </div>
        </div>

        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Introduction */}
          <Card className="mb-8">
            <CardContent className="pt-6">
              <p className="text-lg text-gray-700 leading-relaxed mb-4">
                At BrainMaker Academy, we're committed to your learning success. If you're 
                not completely satisfied with your purchase, we offer refunds under certain 
                conditions outlined in this policy.
              </p>
              <p className="text-gray-700 leading-relaxed">
                Please read this policy carefully to understand your rights and the 
                conditions under which refunds are available. For questions not covered 
                here, please contact our support team.
              </p>
            </CardContent>
          </Card>

          {/* 30-Day Guarantee */}
          <Card className="mb-8 bg-gradient-to-r from-green-50 to-green-100 border-green-200">
            <CardContent className="pt-6">
              <div className="text-center">
                <CheckCircle className="w-12 h-12 text-green-600 mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-gray-900 mb-4">30-Day Money-Back Guarantee</h3>
                <p className="text-lg text-gray-700 mb-4">
                  We offer a 30-day money-back guarantee on most course purchases. 
                  If you're not satisfied, request a refund within 30 days of purchase.
                </p>
                <div className="inline-flex items-center gap-2 text-green-700 bg-green-200 px-4 py-2 rounded-full">
                  <Clock className="w-4 h-4" />
                  <span className="font-semibold">30 days to request a refund</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Refund Scenarios */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Refund Eligibility</h2>
            <div className="space-y-6">
              {refundScenarios.map((scenario, index) => (
                <Card key={index}>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center gap-3 text-lg text-gray-900">
                        <scenario.icon className={`w-5 h-5 ${scenario.eligible ? 'text-green-600' : 'text-red-600'}`} />
                        {scenario.title}
                      </div>
                      <div className="flex items-center gap-2">
                        {scenario.eligible ? (
                          <span className="text-sm text-green-600 bg-green-100 px-3 py-1 rounded-full">
                            Refundable
                          </span>
                        ) : (
                          <span className="text-sm text-red-600 bg-red-100 px-3 py-1 rounded-full">
                            Non-refundable
                          </span>
                        )}
                        <span className="text-sm text-gray-600 bg-gray-100 px-3 py-1 rounded-full">
                          {scenario.timeframe}
                        </span>
                      </div>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {scenario.conditions.map((condition, conditionIndex) => (
                        <li key={conditionIndex} className="flex items-start gap-3">
                          <div className={`w-2 h-2 rounded-full mt-2 flex-shrink-0 ${scenario.eligible ? 'bg-green-600' : 'bg-red-600'}`} />
                          <span className="text-gray-700 text-sm">{condition}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Refund Process */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-3 text-xl text-gray-900">
                <RefreshCw className="w-6 h-6 text-red-600" />
                Refund Process
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {refundProcess.map((step, index) => (
                  <div key={index} className="flex items-start gap-4">
                    <div className="flex-shrink-0 w-8 h-8 bg-red-600 text-white rounded-full flex items-center justify-center font-semibold">
                      {step.step}
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-1">{step.title}</h4>
                      <p className="text-gray-700 text-sm">{step.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Exceptions */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-3 text-xl text-gray-900">
                <XCircle className="w-6 h-6 text-red-600" />
                Refund Exceptions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 mb-4">
                Refunds may not be available in the following situations:
              </p>
              <ul className="space-y-2">
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-red-600 rounded-full mt-2 flex-shrink-0" />
                  <span className="text-gray-700">Requests made after the 30-day refund period</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-red-600 rounded-full mt-2 flex-shrink-0" />
                  <span className="text-gray-700">Courses where you've completed more than 30% of the content</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-red-600 rounded-full mt-2 flex-shrink-0" />
                  <span className="text-gray-700">Courses where certificates have been issued</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-red-600 rounded-full mt-2 flex-shrink-0" />
                  <span className="text-gray-700">Purchases made with promotional credits or gift cards</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-red-600 rounded-full mt-2 flex-shrink-0" />
                  <span className="text-gray-700">Violation of our Terms of Service</span>
                </li>
              </ul>
            </CardContent>
          </Card>

          {/* Payment Processing */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-3 text-xl text-gray-900">
                <CreditCard className="w-6 h-6 text-red-600" />
                Refund Processing
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="border-l-4 border-red-600 pl-4">
                  <h4 className="font-semibold text-gray-900 mb-2">Processing Time</h4>
                  <p className="text-gray-700 text-sm">
                    Approved refunds are processed within 5-7 business days. The time 
                    it takes to appear in your account depends on your payment provider.
                  </p>
                </div>
                <div className="border-l-4 border-red-600 pl-4">
                  <h4 className="font-semibold text-gray-900 mb-2">Refund Method</h4>
                  <p className="text-gray-700 text-sm">
                    Refunds are issued to the original payment method used for the purchase. 
                    We cannot process refunds to different payment methods.
                  </p>
                </div>
                <div className="border-l-4 border-red-600 pl-4">
                  <h4 className="font-semibold text-gray-900 mb-2">Currency</h4>
                  <p className="text-gray-700 text-sm">
                    Refunds are processed in the same currency as the original purchase. 
                    Exchange rate fluctuations may affect the final amount.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Contact Section */}
          <Card className="bg-gradient-to-r from-red-50 to-red-100 border-red-200">
            <CardContent className="pt-6">
              <div className="text-center">
                <Mail className="w-12 h-12 text-red-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Need a Refund?</h3>
                <p className="text-gray-700 mb-6">
                  If you'd like to request a refund or have questions about our refund policy, 
                  our support team is here to help.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button className="bg-red-600 hover:bg-red-700 text-white">
                    Request Refund
                  </Button>
                  <Button variant="outline" className="border-red-600 text-red-600 hover:bg-red-600 hover:text-white">
                    Contact Support
                  </Button>
                </div>
                <div className="mt-4 space-y-1 text-sm text-gray-600">
                  <p>Email: <EMAIL></p>
                  <p>Response time: Within 24 hours</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
};

export default RefundPage;
