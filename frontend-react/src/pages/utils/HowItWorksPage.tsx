import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  Search, 
  Play, 
  Award, 
  Users, 
  BookOpen, 
  CheckCircle, 
  Star,
  Clock,
  Download,
  MessageCircle,
  Target,
  TrendingUp
} from 'lucide-react';
import SEOHelmet from '../../components/common/SEOHelmet';

const HowItWorksPage: React.FC = () => {
  const steps = [
    {
      number: '01',
      title: 'Browse & Discover',
      description: 'Explore our extensive library of courses across various subjects and skill levels.',
      icon: Search,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      number: '02',
      title: 'Enroll & Learn',
      description: 'Choose your course and start learning immediately with our interactive content.',
      icon: Play,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      number: '03',
      title: 'Practice & Apply',
      description: 'Complete assignments, quizzes, and projects to reinforce your learning.',
      icon: Target,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    },
    {
      number: '04',
      title: 'Earn & Achieve',
      description: 'Get certified and showcase your new skills to advance your career.',
      icon: Award,
      color: 'text-red-600',
      bgColor: 'bg-red-100'
    }
  ];

  const features = [
    {
      title: 'Expert-Led Courses',
      description: 'Learn from industry professionals with real-world experience',
      icon: Users,
      stats: '1,200+ Instructors'
    },
    {
      title: 'Comprehensive Content',
      description: 'Access to thousands of hours of high-quality video content',
      icon: BookOpen,
      stats: '2,500+ Courses'
    },
    {
      title: 'Flexible Learning',
      description: 'Study at your own pace with lifetime access to materials',
      icon: Clock,
      stats: '24/7 Access'
    },
    {
      title: 'Interactive Community',
      description: 'Connect with fellow learners and get support when you need it',
      icon: MessageCircle,
      stats: '50,000+ Students'
    },
    {
      title: 'Practical Projects',
      description: 'Build real-world projects to showcase in your portfolio',
      icon: Target,
      stats: '500+ Projects'
    },
    {
      title: 'Career Growth',
      description: 'Track your progress and advance your career with new skills',
      icon: TrendingUp,
      stats: '95% Success Rate'
    }
  ];

  const learningPath = [
    {
      phase: 'Foundation',
      title: 'Start with Basics',
      description: 'Begin with fundamental concepts and build a strong foundation',
      duration: '1-2 weeks',
      activities: ['Video lectures', 'Reading materials', 'Basic exercises']
    },
    {
      phase: 'Practice',
      title: 'Hands-on Learning',
      description: 'Apply what you\'ve learned through practical exercises and projects',
      duration: '2-4 weeks',
      activities: ['Coding exercises', 'Mini projects', 'Peer reviews']
    },
    {
      phase: 'Mastery',
      title: 'Advanced Application',
      description: 'Master advanced concepts and complete capstone projects',
      duration: '2-3 weeks',
      activities: ['Complex projects', 'Case studies', 'Portfolio building']
    },
    {
      phase: 'Certification',
      title: 'Earn Your Certificate',
      description: 'Complete final assessments and earn your course certificate',
      duration: '1 week',
      activities: ['Final exam', 'Project submission', 'Certificate generation']
    }
  ];

  return (
    <>
      <SEOHelmet
        title="How It Works - BrainMaker Academy"
        description="Discover how BrainMaker Academy works. Learn about our step-by-step learning process, from course discovery to certification."
        keywords="how it works, learning process, online education, course structure, certification"
      />

      <div className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <div className="bg-gradient-to-r from-red-600 to-red-800 text-white py-20">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              How BrainMaker Academy Works
            </h1>
            <p className="text-xl text-red-100 max-w-3xl mx-auto">
              Discover our proven learning methodology that has helped thousands of students 
              achieve their goals and advance their careers.
            </p>
          </div>
        </div>

        {/* Steps Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Your Learning Journey in 4 Simple Steps
            </h2>
            <p className="text-xl text-gray-600">
              From discovery to certification, we guide you every step of the way
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {steps.map((step, index) => {
              const IconComponent = step.icon;
              return (
                <Card key={index} className="text-center relative">
                  <CardHeader>
                    <div className="relative">
                      <div className={`w-20 h-20 ${step.bgColor} rounded-full flex items-center justify-center mx-auto mb-4`}>
                        <IconComponent className={`h-10 w-10 ${step.color}`} />
                      </div>
                      <Badge 
                        variant="secondary" 
                        className="absolute -top-2 -right-2 bg-red-600 text-white"
                      >
                        {step.number}
                      </Badge>
                    </div>
                    <CardTitle className="text-xl">{step.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600">{step.description}</p>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>

        {/* Features Section */}
        <div className="bg-white py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Why Choose Our Platform?
              </h2>
              <p className="text-xl text-gray-600">
                Everything you need for successful online learning
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {features.map((feature, index) => {
                const IconComponent = feature.icon;
                return (
                  <Card key={index} className="hover:shadow-lg transition-shadow">
                    <CardContent className="pt-6">
                      <div className="flex items-start gap-4">
                        <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center flex-shrink-0">
                          <IconComponent className="h-6 w-6 text-red-600" />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 mb-2">
                            {feature.title}
                          </h3>
                          <p className="text-gray-600 mb-3">
                            {feature.description}
                          </p>
                          <Badge variant="secondary" className="text-xs">
                            {feature.stats}
                          </Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        </div>

        {/* Learning Path Section */}
        <div className="bg-gray-50 py-20">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Your Typical Learning Path
              </h2>
              <p className="text-xl text-gray-600">
                See how our structured approach helps you master new skills
              </p>
            </div>

            <div className="space-y-8">
              {learningPath.map((phase, index) => (
                <Card key={index} className="overflow-hidden">
                  <CardContent className="p-0">
                    <div className="flex flex-col md:flex-row">
                      <div className="bg-red-600 text-white p-6 md:w-1/4">
                        <Badge variant="secondary" className="bg-white text-red-600 mb-2">
                          {phase.phase}
                        </Badge>
                        <h3 className="text-xl font-bold mb-2">{phase.title}</h3>
                        <p className="text-red-100 text-sm">{phase.duration}</p>
                      </div>
                      <div className="p-6 md:w-3/4">
                        <p className="text-gray-700 mb-4">{phase.description}</p>
                        <div className="flex flex-wrap gap-2">
                          {phase.activities.map((activity, actIndex) => (
                            <Badge key={actIndex} variant="outline">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              {activity}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>

        {/* Success Stories */}
        <div className="bg-white py-20">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Success by the Numbers
              </h2>
              <p className="text-xl text-gray-600">
                See the impact of our learning platform
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div className="text-center">
                <div className="text-4xl font-bold text-red-600 mb-2">50K+</div>
                <p className="text-gray-600">Active Students</p>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-red-600 mb-2">2.5K+</div>
                <p className="text-gray-600">Courses Available</p>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-red-600 mb-2">95%</div>
                <p className="text-gray-600">Completion Rate</p>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-red-600 mb-2">4.8/5</div>
                <p className="text-gray-600">Average Rating</p>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-red-600 to-red-800 text-white py-20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl font-bold mb-6">
              Ready to Start Your Learning Journey?
            </h2>
            <p className="text-xl text-red-100 mb-8">
              Join thousands of students who have transformed their careers with BrainMaker Academy
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/courses">
                <Button size="lg" variant="secondary" className="bg-white text-red-600 hover:bg-gray-100">
                  Browse Courses
                </Button>
              </Link>
              <Link to="/auth/register">
                <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-red-600">
                  Sign Up Free
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default HowItWorksPage;
