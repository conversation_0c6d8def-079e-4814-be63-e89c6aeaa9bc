import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { FileText, Scale, Shield, Users, CreditCard, AlertTriangle } from 'lucide-react';
import SEOHelmet from '../../components/common/SEOHelmet';

const TermsPage: React.FC = () => {
  const lastUpdated = "January 15, 2024";

  const sections = [
    {
      title: "Acceptance of Terms",
      icon: Scale,
      content: [
        "By accessing and using BrainMaker Academy, you accept and agree to be bound by these Terms of Service",
        "If you do not agree to these terms, you may not use our services",
        "We may update these terms from time to time, and continued use constitutes acceptance of changes",
        "You must be at least 13 years old to use our platform, or have parental consent"
      ]
    },
    {
      title: "User Accounts and Responsibilities",
      icon: Users,
      content: [
        "You are responsible for maintaining the confidentiality of your account credentials",
        "You must provide accurate and complete information when creating your account",
        "You are responsible for all activities that occur under your account",
        "You must notify us immediately of any unauthorized use of your account",
        "One person may not maintain multiple accounts without our permission"
      ]
    },
    {
      title: "Course Access and Usage",
      icon: FileText,
      content: [
        "Course access is granted for personal, non-commercial educational use only",
        "You may not share, distribute, or resell course content without explicit permission",
        "Course materials are protected by copyright and intellectual property laws",
        "We reserve the right to modify or discontinue courses at any time",
        "Lifetime access means access for as long as the course remains available on our platform"
      ]
    },
    {
      title: "Payment and Refunds",
      icon: CreditCard,
      content: [
        "All payments are processed securely through our authorized payment partners",
        "Prices are subject to change, but you'll pay the price displayed at time of purchase",
        "Refunds are available within 30 days of purchase if you're not satisfied with a course",
        "Subscription fees are non-refundable except as required by law",
        "We reserve the right to refuse service or cancel orders in our sole discretion"
      ]
    },
    {
      title: "Prohibited Conduct",
      icon: AlertTriangle,
      content: [
        "You may not use our platform for any illegal or unauthorized purpose",
        "Harassment, abuse, or inappropriate behavior toward other users is prohibited",
        "You may not attempt to gain unauthorized access to our systems or other user accounts",
        "Posting spam, malware, or other harmful content is strictly forbidden",
        "You may not interfere with or disrupt the platform's functionality"
      ]
    },
    {
      title: "Intellectual Property",
      icon: Shield,
      content: [
        "All platform content, including courses, is owned by BrainMaker Academy or our content partners",
        "You may not reproduce, distribute, or create derivative works without permission",
        "User-generated content remains your property, but you grant us license to use it",
        "We respect intellectual property rights and will respond to valid DMCA notices",
        "Our trademarks and logos may not be used without explicit written permission"
      ]
    }
  ];

  return (
    <>
      <SEOHelmet
        title="Terms of Service - BrainMaker Academy"
        description="Read BrainMaker Academy's Terms of Service. Understand your rights and responsibilities when using our online learning platform."
        keywords="terms of service, user agreement, legal terms, platform rules, conditions"
      />

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div className="text-center">
              <Scale className="w-16 h-16 text-red-600 mx-auto mb-4" />
              <h1 className="text-4xl font-bold text-gray-900 mb-4">Terms of Service</h1>
              <p className="text-lg text-gray-600">
                Please read these terms carefully before using our platform. 
                They govern your use of BrainMaker Academy services.
              </p>
              <p className="text-sm text-gray-500 mt-4">
                Last updated: {lastUpdated}
              </p>
            </div>
          </div>
        </div>

        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Introduction */}
          <Card className="mb-8">
            <CardContent className="pt-6">
              <p className="text-lg text-gray-700 leading-relaxed mb-4">
                Welcome to BrainMaker Academy. These Terms of Service ("Terms") govern your 
                use of our website, mobile applications, and online learning services 
                (collectively, the "Platform").
              </p>
              <p className="text-gray-700 leading-relaxed">
                These Terms constitute a legally binding agreement between you and BrainMaker 
                Academy. By using our Platform, you acknowledge that you have read, understood, 
                and agree to be bound by these Terms.
              </p>
            </CardContent>
          </Card>

          {/* Terms Sections */}
          <div className="space-y-8">
            {sections.map((section, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="flex items-center gap-3 text-xl text-gray-900">
                    <section.icon className="w-6 h-6 text-red-600" />
                    {section.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    {section.content.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-start gap-3">
                        <div className="w-2 h-2 bg-red-600 rounded-full mt-2 flex-shrink-0" />
                        <span className="text-gray-700">{item}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Limitation of Liability */}
          <Card className="mt-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-3 text-xl text-gray-900">
                <Shield className="w-6 h-6 text-red-600" />
                Limitation of Liability
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 mb-4">
                To the fullest extent permitted by law, BrainMaker Academy shall not be liable for:
              </p>
              <ul className="space-y-2 mb-4">
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-red-600 rounded-full mt-2 flex-shrink-0" />
                  <span className="text-gray-700">Any indirect, incidental, special, or consequential damages</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-red-600 rounded-full mt-2 flex-shrink-0" />
                  <span className="text-gray-700">Loss of profits, data, or business opportunities</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-red-600 rounded-full mt-2 flex-shrink-0" />
                  <span className="text-gray-700">Damages arising from platform downtime or technical issues</span>
                </li>
              </ul>
              <p className="text-gray-700">
                Our total liability shall not exceed the amount you paid for the specific 
                service that gave rise to the claim.
              </p>
            </CardContent>
          </Card>

          {/* Governing Law */}
          <Card className="mt-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-3 text-xl text-gray-900">
                <Scale className="w-6 h-6 text-red-600" />
                Governing Law and Disputes
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 mb-4">
                These Terms are governed by the laws of Quebec, Canada, without regard to 
                conflict of law principles. Any disputes arising from these Terms or your 
                use of the Platform will be resolved through:
              </p>
              <ul className="space-y-2">
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-red-600 rounded-full mt-2 flex-shrink-0" />
                  <span className="text-gray-700">Good faith negotiation between the parties</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-red-600 rounded-full mt-2 flex-shrink-0" />
                  <span className="text-gray-700">Binding arbitration if negotiation fails</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-red-600 rounded-full mt-2 flex-shrink-0" />
                  <span className="text-gray-700">Courts of Quebec for matters that cannot be arbitrated</span>
                </li>
              </ul>
            </CardContent>
          </Card>

          {/* Contact Section */}
          <Card className="mt-8 bg-gradient-to-r from-red-50 to-red-100 border-red-200">
            <CardContent className="pt-6">
              <div className="text-center">
                <FileText className="w-12 h-12 text-red-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Questions About These Terms?</h3>
                <p className="text-gray-700 mb-6">
                  If you have any questions about these Terms of Service, please contact 
                  our legal team for clarification.
                </p>
                <div className="space-y-2 text-sm text-gray-600">
                  <p>Email: <EMAIL></p>
                  <p>Address: 580 rue de Fontenay, Laval, QC</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
};

export default TermsPage;
