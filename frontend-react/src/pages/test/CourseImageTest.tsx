import React, { useState, useEffect } from 'react';
import courseService from '../../services/course.service';
import type { Course } from '../../models';

const CourseImageTest: React.FC = () => {
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchCourses = async () => {
      try {
        const coursesData = await courseService.getAll(5, 0, true);
        setCourses(coursesData);
        console.log('🧪 Test - Fetched courses:', coursesData);
      } catch (error) {
        console.error('Error fetching courses:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCourses();
  }, []);

  if (loading) {
    return <div className="p-8">Loading...</div>;
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-6">Course Image Test</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {courses.map((course) => (
          <div key={course.Id} className="border rounded-lg p-4">
            <h3 className="font-semibold mb-2">{course.Title}</h3>
            <div className="mb-2">
              <strong>Cover Image Hashname:</strong>
              <br />
              <code className="text-sm bg-gray-100 p-1 rounded">
                {course.CoverImage?.Hashname || 'No cover image'}
              </code>
            </div>
            {course.CoverImage?.Hashname && (
              <div className="mb-2">
                <strong>Image Preview:</strong>
                <br />
                <img
                  src={course.CoverImage.Hashname}
                  alt={course.Title}
                  className="w-full h-32 object-cover rounded mt-2"
                  onLoad={() => console.log('✅ Image loaded:', course.CoverImage?.Hashname)}
                  onError={(e) => {
                    console.error('❌ Image failed to load:', course.CoverImage?.Hashname);
                    console.error('Error details:', e);
                  }}
                />
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default CourseImageTest;
