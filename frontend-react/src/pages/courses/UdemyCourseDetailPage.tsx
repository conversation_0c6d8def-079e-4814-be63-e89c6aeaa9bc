import { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import {
  Star,
  Play,
  Clock,
  Calendar,
  ChevronLeft,
  Heart,
  Shield,
  Smartphone,
  User,
  BookOpen,
  Users,
  Award,
  CheckCircle,
  PlayCircle,
  FileText,
  Video,
  Monitor,
  Infinity,
  Trophy,
  Target,
  Zap,
  ShoppingCart,
  Plus,
  Globe,
  Download,
  Tv,
  ChevronDown,
  ChevronUp,
  Share2,
  Gift,
  MessageSquare,
  BarChart3,
  Award as Certificate,
  Headphones
} from 'lucide-react';

import { useCourseBySlug } from '../../hooks/useCourses';
import { useCourseAccess, useFreeEnrollment, useSubscriptionAccess } from '../../hooks/useEnrollment';
import { useCart } from '../../contexts/CartContext';
import { useAuth } from '../../contexts/AuthContext';
import { useToast } from '../../hooks/use-toast';
import type { Course } from '../../models/course.model';
import { ContentType } from '../../models/course.model';

// shadcn/ui components
import { Button } from '../../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Badge } from '../../components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { Alert, AlertDescription } from '../../components/ui/alert';
import { Avatar, AvatarFallback, AvatarImage } from '../../components/ui/avatar';
import { Skeleton } from '../../components/ui/skeleton';
import { Progress } from '../../components/ui/progress';

// Transform course data for display
const transformCourseForDetail = (course: Course) => {
  // Calculate pricing
  const price = course.NewPrice || course.Price || 0;
  const originalPrice = course.NewPrice ? course.Price : null;
  const hasDiscount = !!course.NewPrice && course.NewPrice < (course.Price || 0);

  // Get instructor info
  const instructor = course.CreatedBy?.Firstname && course.CreatedBy?.Lastname
    ? `${course.CreatedBy.Firstname} ${course.CreatedBy.Lastname}`
    : course.CreatedBy?.Username || 'Unknown Instructor';

  // Calculate ratings
  const ratings = course.Ratings || [];
  const averageRating = ratings.length > 0
    ? ratings.reduce((sum, rating) => sum + (rating.Rating || 0), 0) / ratings.length
    : 0;

  // Format last updated
  const lastUpdated = course.UpdatedAt
    ? new Date(course.UpdatedAt).toLocaleDateString('en-US', { month: 'long', year: 'numeric' })
    : 'Recently';

  // Get course image
  const courseImage = course.CoverImage?.Hashname
    ? `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000'}/uploads/${course.CoverImage.Hashname}`
    : null;

  // Calculate duration
  const totalMinutes = course.Sections?.reduce((total, section) => {
    return total + (section.Contents?.reduce((sectionTotal, content) => {
      return sectionTotal + (content.Duration || 0);
    }, 0) || 0);
  }, 0) || 0;

  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;
  const duration = hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;

  // Get course levels
  const levels = course.Level?.map(level => {
    switch (level) {
      case 1: return 'Beginner';
      case 2: return 'Intermediate';
      case 3: return 'Advanced';
      default: return 'All Levels';
    }
  }).join(', ') || 'All Levels';

  return {
    id: course.Id || 0,
    slug: course.Slug || '',
    title: course.Title || 'Untitled Course',
    description: course.Resume ? course.Resume.replace(/<[^>]*>/g, '') : 'No description available',
    longDescription: course.Message || course.Resume?.replace(/<[^>]*>/g, '') || 'No detailed description available',
    price,
    originalPrice,
    hasDiscount,
    instructor,
    rating: Number(averageRating.toFixed(1)),
    reviewCount: ratings.length,
    studentCount: course.Students || 0,
    lastUpdated,
    image: courseImage,
    duration,
    level: levels,
    sections: course.Sections || [],
    categories: course.Categories || [],
    prerequisites: course.Prerequisites || [],
    goals: course.Goals || [],
    language: course.Language || 'en',
    currency: course.Currency || 'USD',
    free: course.Free || false
  };
};

const UdemyCourseDetailPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('overview');
  const [isWishlisted, setIsWishlisted] = useState(false);
  const [isEnrolling, setIsEnrolling] = useState(false);
  const [expandedSections, setExpandedSections] = useState<Set<number>>(new Set());
  const [showAllReviews, setShowAllReviews] = useState(false);

  // Use real API hook to get course by slug
  const { data: courseData, loading, error } = useCourseBySlug(id || null);

  // Transform course data
  const course = courseData ? transformCourseForDetail(courseData) : null;

  // Cart and auth context
  const { cart, addToCart } = useCart();
  const { authState } = useAuth();

  // Helper functions for Udemy-style layout
  const toggleSection = (sectionId: number) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(sectionId)) {
      newExpanded.delete(sectionId);
    } else {
      newExpanded.add(sectionId);
    }
    setExpandedSections(newExpanded);
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  const renderStarRating = (rating: number, size: 'sm' | 'md' | 'lg' = 'md') => {
    const sizeClasses = {
      sm: 'h-3 w-3',
      md: 'h-4 w-4',
      lg: 'h-5 w-5'
    };
    
    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`${sizeClasses[size]} ${
              star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  // Enrollment hooks
  const { enrollmentStatus, loading: enrollmentLoading } = useCourseAccess(courseData?.Id || null);
  const { subscriptionStatus } = useSubscriptionAccess(courseData?.Id);
  const { enrollInFreeCourse, loading: freeEnrollLoading } = useFreeEnrollment();

  // Handle loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-white">
        {/* Header Skeleton */}
        <div className="bg-gray-900 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2 space-y-4">
                <Skeleton className="h-4 w-32 bg-gray-700" />
                <Skeleton className="h-12 w-3/4 bg-gray-700" />
                <Skeleton className="h-6 w-full bg-gray-700" />
                <Skeleton className="h-4 w-1/2 bg-gray-700" />
              </div>
              <div className="hidden lg:block">
                <Skeleton className="h-64 w-full bg-gray-700" />
              </div>
            </div>
          </div>
        </div>
        {/* Content Skeleton */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2 space-y-6">
              <Skeleton className="h-8 w-32" />
              <Skeleton className="h-64 w-full" />
              <div className="space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </div>
            </div>
            <div className="hidden lg:block">
              <Skeleton className="h-96 w-full" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Handle error state
  if (error || !course) {
    return (
      <div className="min-h-screen bg-white py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <Button
              variant="ghost"
              onClick={() => navigate('/courses')}
              className="mb-8 text-purple-600 hover:text-purple-700 hover:bg-purple-50"
            >
              <ChevronLeft className="mr-2 h-4 w-4" />
              Back to Courses
            </Button>

            <Alert className="max-w-md mx-auto">
              <AlertDescription className="text-center">
                <div className="mb-4">
                  <BookOpen className="mx-auto h-12 w-12 text-gray-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Course Not Found</h3>
                <p className="text-gray-600">
                  The course you're looking for doesn't exist or has been removed.
                </p>
              </AlertDescription>
            </Alert>
          </div>
        </div>
      </div>
    );
  }

  // Check if user has access to the course
  const hasAccess = enrollmentStatus?.hasAccess ||
                   subscriptionStatus?.hasActiveSubscription ||
                   false;

  const isInCart = cart.Items?.some(item => item.Course?.Id === courseData?.Id) || false;

  // Handle enrollment/add to cart
  const handleEnroll = async () => {
    if (!authState.isAuthenticated) {
      // Redirect to login
      navigate('/auth/login', {
        state: { from: { pathname: `/courses/${course?.slug}` } }
      });
      return;
    }

    if (!course || !courseData) return;

    if (hasAccess) {
      // User already has access - go directly to course
      navigate(`/courses/${course.slug}/learn`);
      return;
    }

    if (course.free) {
      // Free course - enroll directly
      setIsEnrolling(true);
      try {
        await enrollInFreeCourse(courseData.Id!);
        toast({
          title: "Enrollment Successful!",
          description: `You have successfully enrolled in ${course.title}`,
        });
        // Navigate to course content
        navigate(`/courses/${course.slug}/learn`);
      } catch (error) {
        console.error('Enrollment failed:', error);
        toast({
          title: "Enrollment Failed",
          description: "There was an error enrolling in the course. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsEnrolling(false);
      }
    } else {
      // Paid course - add to cart or go to cart
      if (isInCart) {
        navigate('/cart');
      } else {
        try {
          await addToCart(courseData.Id!);
          toast({
            title: "Added to Cart",
            description: `${course.title} has been added to your cart`,
          });
        } catch (error) {
          console.error('Add to cart failed:', error);
          toast({
            title: "Failed to Add to Cart",
            description: "There was an error adding the course to your cart. Please try again.",
            variant: "destructive",
          });
        }
      }
    }
  };

  const getInitials = (name: string) => {
    const parts = name.split(' ');
    return parts.length > 1
      ? `${parts[0].charAt(0)}${parts[parts.length - 1].charAt(0)}`.toUpperCase()
      : name.substring(0, 2).toUpperCase();
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Udemy-style Header Section */}
      <div className="bg-gray-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Breadcrumb */}
          <nav className="flex items-center space-x-2 text-sm mb-6">
            <Link to="/courses" className="text-red-400 hover:text-red-300">
              Development
            </Link>
            <span className="text-gray-400">›</span>
            <Link to="/courses" className="text-red-400 hover:text-red-300">
              Web Development
            </Link>
            <span className="text-gray-400">›</span>
            <span className="text-gray-300">{course.categories[0]?.Name || 'Course'}</span>
          </nav>

          {/* Course Info - Full Width */}
          <div className="space-y-4">
            {/* Course Title */}
            <h1 className="text-3xl lg:text-4xl font-bold leading-tight">
              {course.title}
            </h1>

            {/* Course Stats */}
            <div className="flex flex-wrap items-center gap-6 text-sm">
              <div className="flex items-center">
                <span className="text-yellow-400 font-bold mr-1">{course.rating}</span>
                {renderStarRating(course.rating, 'sm')}
                <span className="ml-2 text-red-400 underline cursor-pointer">
                  ({course.reviewCount.toLocaleString()} ratings)
                </span>
              </div>
              <div className="text-gray-300">
                {course.studentCount.toLocaleString()} students
              </div>
            </div>

            {/* Instructor Info */}
            <div className="flex items-center space-x-2">
              <span className="text-gray-300">Created by</span>
              <Link
                to={`/instructors/${course.instructor.toLowerCase().replace(/\s+/g, '-')}`}
                className="text-red-400 hover:text-red-300 underline font-medium"
              >
                {course.instructor}
              </Link>
            </div>

            {/* Course Meta */}
            <div className="flex flex-wrap items-center gap-6 text-sm text-gray-400">
              <div className="flex items-center">
                <Calendar className="mr-1 h-4 w-4" />
                Last updated {course.lastUpdated}
              </div>
              <div className="flex items-center">
                <Globe className="mr-1 h-4 w-4" />
                {course.language === 'en' ? 'English' : course.language}
              </div>
            </div>
          </div>

        </div>
      </div>



      {/* Mobile Course Card - Full Screen */}
      <div className="lg:hidden">
        <Card className="mx-4 mb-6 overflow-hidden bg-white rounded-lg">
          {/* Video Preview Section */}
          <div className="relative">
            <div className="aspect-video bg-gray-900">
              {course.image ? (
                <img
                  src={course.image}
                  alt={course.title}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="flex items-center justify-center h-full">
                  <BookOpen className="h-16 w-16 text-gray-400" />
                </div>
              )}
            </div>
            {/* Play Button Overlay */}
            <div className="absolute inset-0 flex items-center justify-center">
              <Button
                size="lg"
                className="bg-white/90 hover:bg-white text-gray-900 rounded-full h-16 w-16 p-0"
              >
                <Play className="h-6 w-6 ml-1" />
              </Button>
            </div>
            {/* Course Title Overlay */}
            <div className="absolute top-4 left-4 right-4">
              <p className="text-white text-sm font-medium bg-black/50 px-3 py-1 rounded backdrop-blur-sm">
                {course.title}
              </p>
            </div>
          </div>

          {/* Course Card Content */}
          <div className="p-6">
            <div className="space-y-4">
              {/* Price */}
              <div className="flex items-center space-x-2">
                {course.hasDiscount ? (
                  <>
                    <span className="text-3xl font-bold text-gray-900">
                      {course.currency}{course.price}
                    </span>
                    <span className="text-lg text-gray-500 line-through">
                      {course.currency}{course.originalPrice}
                    </span>
                  </>
                ) : course.free ? (
                  <span className="text-3xl font-bold text-gray-900">Free</span>
                ) : (
                  <span className="text-3xl font-bold text-gray-900">
                    {course.currency}{course.price}
                  </span>
                )}
              </div>

              {/* Action Buttons */}
              <div className="space-y-3">
                {hasAccess ? (
                  <Button
                    onClick={() => navigate(`/courses/${course.slug}/learn`)}
                    className="w-full bg-red-600 hover:bg-red-700 text-white py-3 text-lg font-semibold"
                  >
                    Go to course
                  </Button>
                ) : course.free ? (
                  <Button
                    onClick={handleEnroll}
                    disabled={isEnrolling}
                    className="w-full bg-red-600 hover:bg-red-700 text-white py-3 text-lg font-semibold"
                  >
                    {isEnrolling ? 'Enrolling...' : 'Enroll for free'}
                  </Button>
                ) : (
                  <>
                    <Button
                      onClick={handleEnroll}
                      className="w-full bg-red-600 hover:bg-red-700 text-white py-3 text-lg font-semibold"
                    >
                      {isInCart ? 'Go to cart' : 'Add to cart'}
                    </Button>
                    <Button
                      variant="outline"
                      className="w-full border-gray-900 text-gray-900 hover:bg-gray-50 py-3"
                    >
                      Buy now
                    </Button>
                  </>
                )}
              </div>

              {/* Secondary Actions */}
              <div className="space-y-2">
                {/* Wishlist */}
                <Button
                  variant="ghost"
                  onClick={() => setIsWishlisted(!isWishlisted)}
                  className="w-full justify-center text-gray-700 hover:bg-gray-50 py-3"
                >
                  <Heart className={`mr-2 h-5 w-5 ${isWishlisted ? 'fill-current text-red-500' : ''}`} />
                  {isWishlisted ? 'Remove from wishlist' : 'Add to wishlist'}
                </Button>

                {/* Share */}
                <Button
                  variant="ghost"
                  className="w-full justify-center text-gray-700 hover:bg-gray-50 py-3"
                >
                  <Share2 className="mr-2 h-5 w-5" />
                  Share
                </Button>

                {/* Gift */}
                <Button
                  variant="ghost"
                  className="w-full justify-center text-gray-700 hover:bg-gray-50 py-3"
                >
                  <Gift className="mr-2 h-5 w-5" />
                  Gift this course
                </Button>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Course Content */}
          <div className="lg:col-span-2">
            <div className="space-y-8">
              {/* Course Description */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-2xl font-bold">About this course</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="prose max-w-none">
                    <p className="text-gray-700 leading-relaxed text-lg">
                      {course.description}
                    </p>
                    {course.longDescription !== course.description && (
                      <div className="mt-4 pt-4 border-t border-gray-200">
                        <p className="text-gray-700 leading-relaxed">
                          {course.longDescription}
                        </p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* What you'll learn */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-2xl font-bold">What you'll learn</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {course.goals.length > 0 ? (
                      course.goals.map((goal, index) => (
                        <div key={index} className="flex items-start space-x-3">
                          <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-700">{goal}</span>
                        </div>
                      ))
                    ) : (
                      <div className="col-span-2 text-gray-500 text-center py-4">
                        Learning objectives will be added soon.
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Course Content */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-2xl font-bold">Course content</CardTitle>
                  <p className="text-gray-600">
                    {course.sections.length} sections • {course.sections.reduce((total, section) => total + (section.Contents?.length || 0), 0)} lectures • {course.duration} total length
                  </p>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {course.sections.map((section, index) => (
                      <div key={section.Id} className="border border-gray-200 rounded-lg">
                        <button
                          onClick={() => toggleSection(section.Id!)}
                          className="w-full px-4 py-3 text-left flex items-center justify-between hover:bg-gray-50"
                        >
                          <div className="flex items-center space-x-3">
                            {expandedSections.has(section.Id!) ? (
                              <ChevronUp className="h-4 w-4" />
                            ) : (
                              <ChevronDown className="h-4 w-4" />
                            )}
                            <span className="font-medium">{section.Title}</span>
                          </div>
                          <div className="text-sm text-gray-500">
                            {section.Contents?.length || 0} lectures • {formatDuration(section.Contents?.reduce((total, content) => total + (content.Duration || 0), 0) || 0)}
                          </div>
                        </button>

                        {expandedSections.has(section.Id!) && (
                          <div className="px-4 pb-3 border-t border-gray-100">
                            <div className="space-y-2 pt-3">
                              {section.Contents?.map((content, contentIndex) => (
                                <div key={content.Id} className="flex items-center justify-between py-2 text-sm">
                                  <div className="flex items-center space-x-3">
                                    {content.Type === ContentType.VIDEO ? (
                                      <PlayCircle className="h-4 w-4 text-gray-400" />
                                    ) : content.Type === ContentType.QUIZ ? (
                                      <FileText className="h-4 w-4 text-gray-400" />
                                    ) : (
                                      <BookOpen className="h-4 w-4 text-gray-400" />
                                    )}
                                    <span className="text-gray-700">{content.Title}</span>
                                  </div>
                                  <span className="text-gray-500">{formatDuration(content.Duration || 0)}</span>
                                </div>
                              )) || (
                                <div className="text-gray-500 text-center py-2">
                                  No content available
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Requirements */}
              {course.prerequisites.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-2xl font-bold">Requirements</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {course.prerequisites.map((prerequisite, index) => (
                        <li key={index} className="flex items-start space-x-3">
                          <div className="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
                          <span className="text-gray-700">{prerequisite}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              )}

              {/* Instructor */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-2xl font-bold">Instructor</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-start space-x-4">
                    <Avatar className="h-16 w-16">
                      <AvatarFallback className="bg-purple-100 text-purple-600 text-lg font-semibold">
                        {getInitials(course.instructor)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold text-purple-600 mb-2">
                        {course.instructor}
                      </h3>
                      <p className="text-gray-600 mb-4">Course Instructor</p>
                      <div className="flex items-center space-x-6 text-sm text-gray-600">
                        <div className="flex items-center space-x-1">
                          <Star className="h-4 w-4 text-yellow-400" />
                          <span>{course.rating} Instructor Rating</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <MessageSquare className="h-4 w-4" />
                          <span>{course.reviewCount} Reviews</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Users className="h-4 w-4" />
                          <span>{course.studentCount.toLocaleString()} Students</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Right Column - Course Features (Desktop Only) */}
          <div className="hidden lg:block">
            <div className="space-y-6">
              {/* Course Preview Card */}
              <div className="sticky top-8">
                <Card className="overflow-hidden bg-white">
                  <div className="relative">
                    <div className="aspect-video bg-gray-900">
                      {course.image ? (
                        <img
                          src={course.image}
                          alt={course.title}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="flex items-center justify-center h-full">
                          <BookOpen className="h-16 w-16 text-gray-400" />
                        </div>
                      )}
                    </div>
                    {/* Play Button Overlay */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <Button
                        size="lg"
                        className="bg-white/90 hover:bg-white text-gray-900 rounded-full h-16 w-16 p-0"
                      >
                        <Play className="h-6 w-6 ml-1" />
                      </Button>
                    </div>
                  </div>

                  {/* Course Card Content */}
                  <div className="p-6">
                    <div className="space-y-4">
                      {/* Price */}
                      <div className="flex items-center space-x-2">
                        {course.hasDiscount ? (
                          <>
                            <span className="text-3xl font-bold text-gray-900">
                              {course.currency}{course.price}
                            </span>
                            <span className="text-lg text-gray-500 line-through">
                              {course.currency}{course.originalPrice}
                            </span>
                          </>
                        ) : course.free ? (
                          <span className="text-3xl font-bold text-gray-900">Free</span>
                        ) : (
                          <span className="text-3xl font-bold text-gray-900">
                            {course.currency}{course.price}
                          </span>
                        )}
                      </div>

                      {/* Action Buttons */}
                      <div className="space-y-3">
                        {hasAccess ? (
                          <Button
                            onClick={() => navigate(`/courses/${course.slug}/learn`)}
                            className="w-full bg-red-600 hover:bg-red-700 text-white py-3 text-lg font-semibold"
                          >
                            Go to course
                          </Button>
                        ) : course.free ? (
                          <Button
                            onClick={handleEnroll}
                            disabled={isEnrolling}
                            className="w-full bg-red-600 hover:bg-red-700 text-white py-3 text-lg font-semibold"
                          >
                            {isEnrolling ? 'Enrolling...' : 'Enroll for free'}
                          </Button>
                        ) : (
                          <>
                            <Button
                              onClick={handleEnroll}
                              className="w-full bg-red-600 hover:bg-red-700 text-white py-3 text-lg font-semibold"
                            >
                              {isInCart ? 'Go to cart' : 'Add to cart'}
                            </Button>
                            <Button
                              variant="outline"
                              className="w-full border-gray-900 text-gray-900 hover:bg-gray-50 py-3"
                            >
                              Buy now
                            </Button>
                          </>
                        )}
                      </div>

                      {/* Secondary Actions */}
                      <div className="space-y-2">
                        {/* Wishlist */}
                        <Button
                          variant="ghost"
                          onClick={() => setIsWishlisted(!isWishlisted)}
                          className="w-full justify-center text-gray-700 hover:bg-gray-50"
                        >
                          <Heart className={`mr-2 h-4 w-4 ${isWishlisted ? 'fill-current text-red-500' : ''}`} />
                          {isWishlisted ? 'Remove from wishlist' : 'Add to wishlist'}
                        </Button>

                        {/* Share */}
                        <Button
                          variant="ghost"
                          className="w-full justify-center text-gray-700 hover:bg-gray-50"
                        >
                          <Share2 className="mr-2 h-4 w-4" />
                          Share
                        </Button>

                        {/* Gift */}
                        <Button
                          variant="ghost"
                          className="w-full justify-center text-gray-700 hover:bg-gray-50"
                        >
                          <Gift className="mr-2 h-4 w-4" />
                          Gift this course
                        </Button>
                      </div>
                    </div>
                  </div>
                </Card>
              </div>


            </div>
          </div>
        </div>
      </div>


    </div>
  );
};

export default UdemyCourseDetailPage;
