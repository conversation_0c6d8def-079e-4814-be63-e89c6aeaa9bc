import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Search, Filter, Grid3X3, List } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import CourseCard from '../../components/common/CourseCard';
import SEOHelmet from '../../components/common/SEOHelmet';
import courseService from '../../services/course.service';
import categoryService from '../../services/category.service';
import type { Course, Category } from '../../models';

const CoursesPage: React.FC = () => {
  // State management
  const [courses, setCourses] = useState<Course[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCourses, setTotalCourses] = useState(0);
  const [searchParams, setSearchParams] = useSearchParams();

  const pageSize = 12; // Number of courses per page

  // Initialize from URL parameters
  useEffect(() => {
    const search = searchParams.get('search') || '';
    const category = searchParams.get('category') || 'all';
    const page = parseInt(searchParams.get('page') || '1');

    setSearchTerm(search);
    setSelectedCategory(category);
    setCurrentPage(page);
  }, [searchParams]);

  // Fetch categories on mount
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const categoriesData = await categoryService.getAll();
        setCategories(categoriesData);
      } catch (error) {
        console.error('Error fetching categories:', error);
      }
    };

    fetchCategories();
  }, []);

  // Fetch courses when filters change
  useEffect(() => {
    const fetchCourses = async () => {
      setLoading(true);
      try {
        const skip = (currentPage - 1) * pageSize;
        let coursesData: Course[] = [];

        if (searchTerm.trim()) {
          // Search functionality
          coursesData = await courseService.search(searchTerm.trim(), pageSize, skip);
          setTotalCourses(coursesData.length); // Search doesn't return total count
        } else if (selectedCategory !== 'all') {
          // Category filtering
          const result = await courseService.getByCategorySlug(selectedCategory, pageSize, skip);
          coursesData = result.Courses || [];
          setTotalCourses(result.Count || 0);
        } else {
          // Default: get all published courses
          coursesData = await courseService.getAll(pageSize, skip, true);
          const count = await courseService.getCount();
          setTotalCourses(count);
        }

        setCourses(coursesData);
      } catch (error) {
        console.error('Error fetching courses:', error);
        setCourses([]);
      } finally {
        setLoading(false);
      }
    };

    fetchCourses();
  }, [currentPage, searchTerm, selectedCategory, pageSize]);

  // Handle search
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);

    const params = new URLSearchParams(searchParams);
    if (value.trim()) {
      params.set('search', value);
    } else {
      params.delete('search');
    }
    params.set('page', '1');
    setSearchParams(params);
  };

  // Handle category filter
  const handleCategoryChange = (value: string) => {
    setSelectedCategory(value);
    setCurrentPage(1);

    const params = new URLSearchParams(searchParams);
    if (value !== 'all') {
      params.set('category', value);
    } else {
      params.delete('category');
    }
    params.set('page', '1');
    setSearchParams(params);
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setCurrentPage(page);

    const params = new URLSearchParams(searchParams);
    params.set('page', page.toString());
    setSearchParams(params);

    // Scroll to top
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Clear all filters
  const clearFilters = () => {
    setSearchTerm('');
    setSelectedCategory('all');
    setCurrentPage(1);
    setSearchParams({});
  };

  const totalPages = Math.ceil(totalCourses / pageSize);
  const hasFilters = searchTerm.trim() || selectedCategory !== 'all';

  return (
    <>
      <SEOHelmet
        title="All Courses - Brainmaker Academy"
        description="Explore our comprehensive collection of online courses. Learn new skills with expert-led content across various categories."
      />

      <div className="min-h-screen bg-gray-50">
        {/* Header Section */}
        <div className="bg-white border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="text-center mb-8">
              <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                All Courses
              </h1>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Choose from {totalCourses.toLocaleString()} online courses with new additions published every month
              </p>
            </div>

            {/* Search and Filters */}
            <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
              {/* Search Bar */}
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  type="text"
                  placeholder="Search courses..."
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10 pr-4 py-2 w-full"
                />
              </div>

              {/* Filters */}
              <div className="flex items-center gap-4">
                {/* Category Filter */}
                <Select value={selectedCategory} onValueChange={handleCategoryChange}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="All Categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category.Id} value={category.Slug || category.Id?.toString() || ''}>
                        {category.Title || category.Name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {/* View Mode Toggle */}
                <div className="flex border border-gray-200 rounded-lg">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                    className="rounded-r-none"
                  >
                    <Grid3X3 className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                    className="rounded-l-none"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>

                {/* Clear Filters */}
                {hasFilters && (
                  <Button variant="outline" onClick={clearFilters}>
                    <Filter className="h-4 w-4 mr-2" />
                    Clear
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {loading ? (
            /* Loading State */
            <div className={`grid gap-6 ${
              viewMode === 'grid'
                ? 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4'
                : 'grid-cols-1'
            }`}>
              {[...Array(8)].map((_, index) => (
                <Card key={index} className="animate-pulse">
                  <div className="aspect-video bg-gray-200 rounded-t-lg" />
                  <CardContent className="p-4 space-y-3">
                    <div className="h-4 bg-gray-200 rounded w-3/4" />
                    <div className="h-3 bg-gray-200 rounded w-1/2" />
                    <div className="h-3 bg-gray-200 rounded w-1/4" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : courses.length === 0 ? (
            /* Empty State */
            <div className="text-center py-16">
              <div className="text-6xl mb-4 opacity-30">📚</div>
              <h3 className="text-xl font-semibold text-gray-700 mb-2">
                {hasFilters ? 'No courses found' : 'No courses available'}
              </h3>
              <p className="text-gray-500 mb-6">
                {hasFilters
                  ? 'Try adjusting your search criteria or browse our categories'
                  : 'Check back soon for new courses'
                }
              </p>
              {hasFilters && (
                <Button onClick={clearFilters} className="bg-red-600 hover:bg-red-700">
                  Clear Filters
                </Button>
              )}
            </div>
          ) : (
            /* Courses Grid */
            <>
              <div className={`grid gap-6 ${
                viewMode === 'grid'
                  ? 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4'
                  : 'grid-cols-1 max-w-4xl mx-auto'
              }`}>
                {courses
                  .filter(course => course.Slug) // Only show courses with valid slugs
                  .map((course) => (
                    <CourseCard
                      key={course.Id}
                      course={{
                        // Pass the course data with proper typing
                        // The course service has already formatted all URLs
                        ...course,
                        Slug: course.Slug!, // Ensure Slug is not undefined (filtered above)
                        Title: course.Title || 'Untitled Course',
                        // Add computed fields for compatibility
                        ReviewsCount: course.Ratings?.length || 0,
                        StudentsCount: 0, // Default value since not available in Course model
                        averageRating: course.Rating || 0,
                        reviewCount: course.Ratings?.length || 0
                      }}
                      showFavoriteButton={true}
                      className={viewMode === 'list' ? 'flex-row' : ''}
                    />
                  ))}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center items-center mt-12 space-x-2">
                  <Button
                    variant="outline"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage <= 1}
                  >
                    Previous
                  </Button>

                  {[...Array(Math.min(5, totalPages))].map((_, index) => {
                    const page = index + 1;
                    return (
                      <Button
                        key={page}
                        variant={currentPage === page ? 'default' : 'outline'}
                        onClick={() => handlePageChange(page)}
                        className="w-10"
                      >
                        {page}
                      </Button>
                    );
                  })}

                  <Button
                    variant="outline"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage >= totalPages}
                  >
                    Next
                  </Button>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default CoursesPage;