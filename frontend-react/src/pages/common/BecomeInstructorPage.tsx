import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Dialog, DialogContent, DialogDescription, DialogFooter, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { CheckCircle, User, GraduationCap, Upload, FileText, Plus, X, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import SEOHelmet from '../../components/common/SEOHelmet';
import instructorService from '../../services/instructor.service';

// Form validation schema
const formSchema = z.object({
  firstName: z.string().min(2, 'First name must be at least 2 characters').max(50, 'First name must be less than 50 characters'),
  lastName: z.string().min(2, 'Last name must be at least 2 characters').max(50, 'Last name must be less than 50 characters'),
  email: z.string().email('Please enter a valid email address').max(100, 'Email must be less than 100 characters'),
  phone: z.string().min(10, 'Please enter a valid phone number').max(20, 'Phone number must be less than 20 characters'),
  country: z.string().min(1, 'Please select your country').max(50, 'Country must be less than 50 characters'),
  city: z.string().min(2, 'Please enter your city').max(50, 'City must be less than 50 characters'),
  title: z.string().min(5, 'Professional title must be at least 5 characters').max(100, 'Title must be less than 100 characters'),
  bio: z.string().min(100, 'Bio must be at least 100 characters').max(1000, 'Bio must be less than 1000 characters'),
  experience: z.string().min(1, 'Please select your experience level'),
  linkedinUrl: z.string().url('Please enter a valid LinkedIn URL').optional().or(z.literal('')),
  websiteUrl: z.string().url('Please enter a valid website URL').optional().or(z.literal('')),
  whyTeach: z.string().min(50, 'Please provide at least 50 characters explaining why you want to teach').max(1000, 'Response must be less than 1000 characters'),
  courseIdeas: z.string().min(50, 'Please provide at least 50 characters describing your course ideas').max(1000, 'Response must be less than 1000 characters')
});

const BecomeInstructorPage = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [selectedExpertise, setSelectedExpertise] = useState<string[]>([]);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [submissionStatus, setSubmissionStatus] = useState<'idle' | 'submitting' | 'success' | 'error'>('idle');

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      country: '',
      city: '',
      title: '',
      bio: '',
      experience: '',
      linkedinUrl: '',
      websiteUrl: '',
      whyTeach: '',
      courseIdeas: ''
    }
  });

  const steps = [
    { title: 'Personal Info', icon: User, description: 'Basic information' },
    { title: 'Professional', icon: GraduationCap, description: 'Experience & expertise' },
    { title: 'Teaching', icon: FileText, description: 'Teaching motivation' },
    { title: 'Review', icon: CheckCircle, description: 'Submit application' }
  ];

  const expertiseOptions = [
    'Web Development', 'Mobile Development', 'Data Science', 'Machine Learning',
    'Artificial Intelligence', 'Cybersecurity', 'Cloud Computing', 'DevOps',
    'UI/UX Design', 'Digital Marketing', 'Business Analytics', 'Project Management',
    'Photography', 'Video Editing', 'Graphic Design', 'Music Production',
    'Language Learning', 'Mathematics', 'Science', 'Other'
  ];

  const experienceOptions = [
    { value: 'beginner', label: '1-2 years' },
    { value: 'intermediate', label: '3-5 years' },
    { value: 'advanced', label: '6-10 years' },
    { value: 'expert', label: '10+ years' }
  ];

  const countries = [
    'United States', 'Canada', 'United Kingdom', 'Australia', 
    'Germany', 'France', 'India', 'Other'
  ];

  const handleNext = async () => {
    const fieldsToValidate = getFieldsForStep(currentStep);
    const isValid = await form.trigger(fieldsToValidate);
    if (isValid) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handlePrev = () => {
    setCurrentStep(prev => prev - 1);
  };

  const getFieldsForStep = (step: number) => {
    switch (step) {
      case 0: return ['firstName', 'lastName', 'email', 'phone', 'country', 'city'] as const;
      case 1: return ['title', 'bio', 'experience'] as const;
      case 2: return ['whyTeach', 'courseIdeas'] as const;
      default: return [] as const;
    }
  };

  const handleSubmitClick = () => {
    setShowConfirmDialog(true);
  };

  const handleConfirmSubmit = async () => {
    const data = form.getValues();
    setShowConfirmDialog(false);

    try {
      setLoading(true);
      setSubmissionStatus('submitting');

      // Map form data to backend expected format
      const finalData = {
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        phone: data.phone,
        country: data.country,
        city: data.city,
        title: data.title,
        bio: data.bio,
        experience: data.experience,
        expertise: selectedExpertise,
        linkedinUrl: data.linkedinUrl || '',
        websiteUrl: data.websiteUrl || '',
        whyTeach: data.whyTeach,
        courseIdeas: data.courseIdeas,
        // Add education and teaching experience if needed
        education: [],
        teachingExperience: data.whyTeach, // Map to existing field
        previousPlatforms: '',
        sampleCourseOutline: data.courseIdeas
      };

      console.log('Submitting application data:', finalData);

      const response = await instructorService.apply(finalData);
      setSubmissionStatus('success');

      // Enhanced success message with next steps
      toast.success(
        'Application submitted successfully! 🎉\n\n' +
        'What happens next:\n' +
        '• Our team will review your application within 3-5 business days\n' +
        '• You\'ll receive an email confirmation shortly\n' +
        '• If approved, you\'ll get login credentials and onboarding instructions\n\n' +
        'Thank you for your interest in teaching with BrainMaker!',
        {
          duration: 8000
        }
      );

      // Redirect after a short delay to let user read the message
      setTimeout(() => {
        navigate('/', {
          state: {
            message: 'Your instructor application has been submitted successfully!'
          }
        });
      }, 2000);

    } catch (error: any) {
      console.error('Error submitting application:', error);
      setSubmissionStatus('error');

      // More detailed error handling
      let errorMessage = 'Failed to submit application. Please try again.';

      if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      toast.error(
        `Submission Failed: ${errorMessage}. Please check your information and try again. If the problem persists, contact support.`,
        {
          duration: 6000
        }
      );
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (data: z.infer<typeof formSchema>) => {
    // This is called by the form, but we'll handle submission through the confirmation dialog
    handleSubmitClick();
  };

  const toggleExpertise = (expertise: string) => {
    setSelectedExpertise(prev => 
      prev.includes(expertise) 
        ? prev.filter(e => e !== expertise)
        : [...prev, expertise]
    );
  };

  const renderPersonalInfo = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-2xl font-bold text-gray-900 mb-2">Personal Information</h3>
        <p className="text-gray-600 mb-6">
          Tell us about yourself. This information will help us understand your background.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="firstName">First Name *</Label>
          <Input
            id="firstName"
            placeholder="Enter your first name"
            {...form.register('firstName')}
          />
          {form.formState.errors.firstName && (
            <p className="text-sm text-red-600">{form.formState.errors.firstName.message}</p>
          )}
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="lastName">Last Name *</Label>
          <Input
            id="lastName"
            placeholder="Enter your last name"
            {...form.register('lastName')}
          />
          {form.formState.errors.lastName && (
            <p className="text-sm text-red-600">{form.formState.errors.lastName.message}</p>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="email">Email Address *</Label>
          <Input
            id="email"
            type="email"
            placeholder="Enter your email address"
            {...form.register('email')}
          />
          {form.formState.errors.email && (
            <p className="text-sm text-red-600">{form.formState.errors.email.message}</p>
          )}
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="phone">Phone Number *</Label>
          <Input
            id="phone"
            placeholder="Enter your phone number"
            {...form.register('phone')}
          />
          {form.formState.errors.phone && (
            <p className="text-sm text-red-600">{form.formState.errors.phone.message}</p>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="country">Country *</Label>
          <Select onValueChange={(value) => form.setValue('country', value)}>
            <SelectTrigger>
              <SelectValue placeholder="Select your country" />
            </SelectTrigger>
            <SelectContent>
              {countries.map(country => (
                <SelectItem key={country} value={country}>{country}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          {form.formState.errors.country && (
            <p className="text-sm text-red-600">{form.formState.errors.country.message}</p>
          )}
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="city">City *</Label>
          <Input
            id="city"
            placeholder="Enter your city"
            {...form.register('city')}
          />
          {form.formState.errors.city && (
            <p className="text-sm text-red-600">{form.formState.errors.city.message}</p>
          )}
        </div>
      </div>
    </div>
  );

  const renderProfessionalInfo = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-2xl font-bold text-gray-900 mb-2">Professional Information</h3>
        <p className="text-gray-600 mb-6">
          Share your professional background and expertise.
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="title">Professional Title *</Label>
        <Input
          id="title"
          placeholder="e.g., Senior Software Engineer, UX Designer"
          {...form.register('title')}
        />
        {form.formState.errors.title && (
          <p className="text-sm text-red-600">{form.formState.errors.title.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="bio">Professional Bio *</Label>
        <Textarea
          id="bio"
          placeholder="Tell us about your professional background, achievements, and what makes you qualified to teach..."
          rows={4}
          {...form.register('bio')}
        />
        {form.formState.errors.bio && (
          <p className="text-sm text-red-600">{form.formState.errors.bio.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="experience">Years of Experience *</Label>
        <Select onValueChange={(value) => form.setValue('experience', value)}>
          <SelectTrigger>
            <SelectValue placeholder="Select your experience level" />
          </SelectTrigger>
          <SelectContent>
            {experienceOptions.map(option => (
              <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
            ))}
          </SelectContent>
        </Select>
        {form.formState.errors.experience && (
          <p className="text-sm text-red-600">{form.formState.errors.experience.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label>Areas of Expertise</Label>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
          {expertiseOptions.map(expertise => (
            <Badge
              key={expertise}
              variant={selectedExpertise.includes(expertise) ? "default" : "outline"}
              className={`cursor-pointer text-center justify-center py-2 ${
                selectedExpertise.includes(expertise) 
                  ? 'bg-red-600 hover:bg-red-700 text-white' 
                  : 'hover:bg-gray-100'
              }`}
              onClick={() => toggleExpertise(expertise)}
            >
              {expertise}
            </Badge>
          ))}
        </div>
        {selectedExpertise.length === 0 && (
          <p className="text-sm text-red-600">Please select at least one area of expertise</p>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="linkedinUrl">LinkedIn Profile (Optional)</Label>
          <Input
            id="linkedinUrl"
            placeholder="https://linkedin.com/in/yourprofile"
            {...form.register('linkedinUrl')}
          />
          {form.formState.errors.linkedinUrl && (
            <p className="text-sm text-red-600">{form.formState.errors.linkedinUrl.message}</p>
          )}
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="websiteUrl">Personal Website (Optional)</Label>
          <Input
            id="websiteUrl"
            placeholder="https://yourwebsite.com"
            {...form.register('websiteUrl')}
          />
          {form.formState.errors.websiteUrl && (
            <p className="text-sm text-red-600">{form.formState.errors.websiteUrl.message}</p>
          )}
        </div>
      </div>
    </div>
  );

  const renderTeachingInfo = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-2xl font-bold text-gray-900 mb-2">Teaching Motivation</h3>
        <p className="text-gray-600 mb-6">
          Tell us why you want to teach and what courses you'd like to create.
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="whyTeach">Why do you want to teach? *</Label>
        <Textarea
          id="whyTeach"
          placeholder="Share your motivation for teaching and how you want to impact learners..."
          rows={4}
          {...form.register('whyTeach')}
        />
        {form.formState.errors.whyTeach && (
          <p className="text-sm text-red-600">{form.formState.errors.whyTeach.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="courseIdeas">Course Ideas *</Label>
        <Textarea
          id="courseIdeas"
          placeholder="Describe the courses you'd like to create, topics you'd cover, and your teaching approach..."
          rows={4}
          {...form.register('courseIdeas')}
        />
        {form.formState.errors.courseIdeas && (
          <p className="text-sm text-red-600">{form.formState.errors.courseIdeas.message}</p>
        )}
      </div>
    </div>
  );

  const renderReview = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-2xl font-bold text-gray-900 mb-2">Review Your Application</h3>
        <p className="text-gray-600 mb-6">
          Please review your information before submitting your instructor application.
        </p>
      </div>

      <Alert>
        <CheckCircle className="h-4 w-4" />
        <AlertDescription>
          By submitting this application, you agree to our instructor terms and conditions.
          We will review your application and contact you within 5-7 business days.
        </AlertDescription>
      </Alert>

      <div className="bg-gray-50 p-6 rounded-lg space-y-4">
        <div>
          <h4 className="font-semibold text-gray-900">Personal Information</h4>
          <p className="text-gray-600">{form.watch('firstName')} {form.watch('lastName')}</p>
          <p className="text-gray-600">{form.watch('email')}</p>
          <p className="text-gray-600">{form.watch('city')}, {form.watch('country')}</p>
        </div>

        <div>
          <h4 className="font-semibold text-gray-900">Professional Background</h4>
          <p className="text-gray-600">{form.watch('title')}</p>
          <p className="text-gray-600">Experience: {experienceOptions.find(e => e.value === form.watch('experience'))?.label}</p>
        </div>

        <div>
          <h4 className="font-semibold text-gray-900">Areas of Expertise</h4>
          <div className="flex flex-wrap gap-2 mt-2">
            {selectedExpertise.map(expertise => (
              <Badge key={expertise} variant="secondary">{expertise}</Badge>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <>
      <SEOHelmet
        title="Become an Instructor - BrainMaker Academy"
        description="Join BrainMaker Academy as an instructor and share your expertise with thousands of learners worldwide."
        keywords="become instructor, teach online, create courses, online education, instructor application"
      />

      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">Become an Instructor</h1>
            <p className="text-xl text-gray-600">
              Share your knowledge and inspire learners around the world
            </p>
          </div>

          {/* Progress Steps */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              {steps.map((step, index) => (
                <div key={index} className="flex items-center">
                  <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                    index <= currentStep
                      ? 'bg-red-600 border-red-600 text-white'
                      : 'border-gray-300 text-gray-500'
                  }`}>
                    {(() => {
                      const IconComponent = step.icon;
                      return <IconComponent className="w-5 h-5" />;
                    })()}
                  </div>
                  {index < steps.length - 1 && (
                    <div className={`flex-1 h-0.5 mx-4 ${
                      index < currentStep ? 'bg-red-600' : 'bg-gray-300'
                    }`} />
                  )}
                </div>
              ))}
            </div>
            <Progress value={(currentStep / (steps.length - 1)) * 100} className="h-2" />
          </div>

          {/* Form */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {(() => {
                  const IconComponent = steps[currentStep].icon;
                  return <IconComponent className="w-6 h-6 text-red-600" />;
                })()}
                {steps[currentStep].title}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={form.handleSubmit(handleSubmit)}>
                {currentStep === 0 && renderPersonalInfo()}
                {currentStep === 1 && renderProfessionalInfo()}
                {currentStep === 2 && renderTeachingInfo()}
                {currentStep === 3 && renderReview()}
                
                <Separator className="my-8" />
                
                <div className="flex justify-between">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handlePrev}
                    disabled={currentStep === 0}
                  >
                    Previous
                  </Button>
                  
                  {currentStep < steps.length - 1 ? (
                    <Button
                      type="button"
                      onClick={handleNext}
                      className="bg-red-600 hover:bg-red-700 text-white"
                    >
                      Next
                    </Button>
                  ) : (
                    <Button
                      type="submit"
                      disabled={loading || selectedExpertise.length === 0 || submissionStatus === 'submitting'}
                      className="bg-red-600 hover:bg-red-700 text-white min-w-[180px]"
                    >
                      {submissionStatus === 'submitting' ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          Submitting...
                        </>
                      ) : submissionStatus === 'success' ? (
                        <>
                          <CheckCircle className="w-4 h-4 mr-2" />
                          Submitted!
                        </>
                      ) : (
                        'Submit Application'
                      )}
                    </Button>
                  )}
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Confirmation Dialog */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <CheckCircle className="w-5 h-5 text-red-600" />
              Confirm Application Submission
            </DialogTitle>
            <DialogDescription className="text-left space-y-3 pt-2">
              <p>
                You're about to submit your instructor application to BrainMaker Academy.
                Please review the following information:
              </p>

              <div className="bg-gray-50 p-4 rounded-lg space-y-2 text-sm">
                <div><strong>Name:</strong> {form.watch('firstName')} {form.watch('lastName')}</div>
                <div><strong>Email:</strong> {form.watch('email')}</div>
                <div><strong>Experience:</strong> {experienceOptions.find(e => e.value === form.watch('experience'))?.label}</div>
                <div><strong>Expertise Areas:</strong> {selectedExpertise.length} selected</div>
              </div>

              <div className="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-400">
                <p className="text-sm text-blue-800">
                  <strong>What happens next:</strong>
                </p>
                <ul className="text-sm text-blue-700 mt-1 space-y-1">
                  <li>• You'll receive an email confirmation immediately</li>
                  <li>• Our team will review your application within 3-5 business days</li>
                  <li>• If approved, you'll receive login credentials and onboarding instructions</li>
                  <li>• You can then start creating and publishing courses</li>
                </ul>
              </div>

              <p className="text-sm text-gray-600">
                <strong>Note:</strong> Once submitted, you cannot edit your application.
                Make sure all information is accurate.
              </p>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="gap-2">
            <Button
              variant="outline"
              onClick={() => setShowConfirmDialog(false)}
              disabled={submissionStatus === 'submitting'}
            >
              Review Again
            </Button>
            <Button
              onClick={handleConfirmSubmit}
              disabled={submissionStatus === 'submitting'}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              {submissionStatus === 'submitting' ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Submitting...
                </>
              ) : (
                'Confirm & Submit'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default BecomeInstructorPage;
