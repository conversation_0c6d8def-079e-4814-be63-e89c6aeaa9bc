import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import {
  FaArrowLeft,
  FaCheck,
  FaTimes,
  FaUser,
  FaCalendarAlt,
  FaClock,
  FaBook,
  FaGraduationCap,
  FaDollarSign,
  FaLanguage,
  FaTag,
  FaList,
  FaVideo,
  FaImage,
  FaExclamationTriangle,
  FaShieldAlt,
  FaStar,
  FaUsers,
  FaEye,
  FaHeart,
  FaComments,
  FaDownload,
  FaPlay,
  FaFileAlt,
  FaChartBar,
  FaFlag,
  FaLock,
  FaGlobe,
  FaEnvelope,
  FaPhone,
  FaMapMarkerAlt,
  FaIdCard,
  FaCertificate,
  FaHistory
} from 'react-icons/fa';

// shadcn/ui imports
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Al<PERSON>, AlertDescription } from "@/components/ui/alert";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { toast } from 'sonner';

import { useAdminCourse, useApproveCourse, useRejectCourse } from '../../hooks/useAdmin';
import type { AdminCourse } from '../../services/admin.service';
import SEOHelmet from '../../components/common/SEOHelmet';

const AdminCourseVerificationDetailsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [showApproveModal, setShowApproveModal] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [rejectReason, setRejectReason] = useState('');

  // Use hooks for data fetching and mutations
  const { data: course, isLoading: loading, error } = useAdminCourse(id || '');
  const approveMutation = useApproveCourse();
  const rejectMutation = useRejectCourse();

  const handleApprove = async () => {
    if (!course) return;

    try {
      await approveMutation.mutateAsync(course.slug || course.id);
      setShowApproveModal(false);
      toast.success(`Course "${course.title}" has been approved successfully!`);
      navigate('/admin/course-verification');
    } catch (error: any) {
      console.error('Error approving course:', error);
      toast.error(`Failed to approve course "${course.title}". ${error?.message || 'Please try again.'}`);
    }
  };

  const handleReject = async () => {
    if (!course || !rejectReason.trim()) {
      toast.error('Please provide a reason for rejection.');
      return;
    }

    console.log('🚫 Starting course rejection:', {
      courseId: course.id,
      courseSlug: course.slug,
      courseTitle: course.title,
      reason: rejectReason,
      fullCourseObject: course
    });

    // Validate that we have at least one valid identifier
    if ((!course.id || course.id === '' || course.id === 'undefined') &&
        (!course.slug || course.slug === '' || course.slug === 'undefined')) {
      toast.error('Cannot reject course: Invalid course identifiers. Please refresh the page and try again.');
      return;
    }

    try {
      const result = await rejectMutation.mutateAsync({
        courseId: course.id, // Use course ID directly
        slug: course.slug,   // Provide slug as fallback
        reason: rejectReason
      });

      console.log('✅ Course rejection successful:', result);

      setShowRejectModal(false);
      setRejectReason('');
      toast.success(`Course "${course.title}" has been rejected.`);
      navigate('/admin/course-verification');
    } catch (error: any) {
      console.error('❌ Error rejecting course:', error);
      console.error('Error details:', {
        message: error?.message,
        response: error?.response?.data,
        status: error?.response?.status,
        statusText: error?.response?.statusText
      });

      const errorMessage = error?.response?.data?.message ||
                          error?.message ||
                          'An unexpected error occurred. Please try again.';

      toast.error(`Failed to reject course "${course.title}": ${errorMessage}`);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return 'Invalid Date';
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { variant: 'secondary' as const, color: 'bg-yellow-100 text-yellow-800', label: 'Pending Review' },
      approved: { variant: 'default' as const, color: 'bg-green-100 text-green-800', label: 'Approved' },
      rejected: { variant: 'destructive' as const, color: 'bg-red-100 text-red-800', label: 'Rejected' },
      draft: { variant: 'outline' as const, color: 'bg-gray-100 text-gray-800', label: 'Draft' },
      archived: { variant: 'secondary' as const, color: 'bg-gray-100 text-gray-800', label: 'Archived' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    return (
      <Badge variant={config.variant} className={config.color}>
        {config.label}
      </Badge>
    );
  };

  if (loading) {
    return (
      <div className="p-6 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto"></div>
        <div className="mt-4">
          <p className="text-gray-500">Loading course details...</p>
        </div>
      </div>
    );
  }

  if (error || !course) {
    return (
      <div className="p-6">
        <Alert variant="destructive">
          <FaExclamationTriangle className="h-4 w-4" />
          <AlertDescription>
            Failed to load course details. Please try again later.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <>
      <SEOHelmet
        title={`Course Verification - ${course.title} - Admin Dashboard`}
        description={`Review and verify course: ${course.title}`}
        keywords="course verification, admin, education, review"
      />

      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/admin/course-verification')}
              className="flex items-center gap-2"
            >
              <FaArrowLeft className="h-4 w-4" />
              Back to Verification Queue
            </Button>
            <Separator orientation="vertical" className="h-6" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Course Verification</h1>
              <p className="text-gray-500">Review course details and make approval decision</p>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            {course.status === 'pending' && (
              <>
                <Button
                  onClick={() => setShowRejectModal(true)}
                  variant="outline"
                  className="text-red-600 border-red-200 hover:bg-red-50"
                >
                  <FaTimes className="h-4 w-4 mr-2" />
                  Reject
                </Button>
                <Button
                  onClick={() => setShowApproveModal(true)}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <FaCheck className="h-4 w-4 mr-2" />
                  Approve
                </Button>
              </>
            )}
          </div>
        </div>

        {/* Course Status Alert */}
        <Alert className="border-l-4 border-l-blue-500">
          <FaShieldAlt className="h-4 w-4" />
          <AlertDescription>
            <div className="flex items-center justify-between">
              <span>Course Status: {getStatusBadge(course.status)}</span>
              <span className="text-sm text-gray-500">
                Submitted: {formatDate(course.createdAt)}
              </span>
            </div>
          </AlertDescription>
        </Alert>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Course Information */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FaBook className="h-5 w-5" />
                  Course Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h3 className="text-xl font-semibold mb-2">{course.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{course.description}</p>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center gap-2">
                    <FaDollarSign className="h-4 w-4 text-green-600" />
                    <span className="font-medium">Price:</span>
                    <span className="text-green-600 font-semibold">${course.price.toFixed(2)}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <FaClock className="h-4 w-4 text-blue-600" />
                    <span className="font-medium">Duration:</span>
                    <span>{course.duration} minutes</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <FaLanguage className="h-4 w-4 text-purple-600" />
                    <span className="font-medium">Language:</span>
                    <span>{(course as any).Language || 'English'}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <FaGraduationCap className="h-4 w-4 text-indigo-600" />
                    <span className="font-medium">Level:</span>
                    <span>{(course as any).Level ? `Level ${(course as any).Level.join(', ')}` : 'Not specified'}</span>
                  </div>
                </div>

                {course.thumbnail && (
                  <div>
                    <Label className="text-sm font-medium mb-2 block">Course Thumbnail</Label>
                    <img
                      src={course.thumbnail}
                      alt={course.title}
                      className="w-full max-w-md h-48 object-cover rounded-lg border shadow-sm"
                    />
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Course Content Details */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FaList className="h-5 w-5" />
                  Course Content & Requirements
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Prerequisites */}
                {(course as any).Prerequisites && (course as any).Prerequisites.length > 0 && (
                  <div>
                    <Label className="text-sm font-medium mb-2 block">Prerequisites</Label>
                    <ul className="list-disc list-inside space-y-1 text-gray-600">
                      {(course as any).Prerequisites.map((prereq: string, index: number) => (
                        <li key={index}>{prereq}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Learning Goals */}
                {(course as any).Goals && (course as any).Goals.length > 0 && (
                  <div>
                    <Label className="text-sm font-medium mb-2 block">What Students Will Learn</Label>
                    <ul className="list-disc list-inside space-y-1 text-gray-600">
                      {(course as any).Goals.map((goal: string, index: number) => (
                        <li key={index}>{goal}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Keywords/Tags */}
                {(course as any).Keywords && (course as any).Keywords.length > 0 && (
                  <div>
                    <Label className="text-sm font-medium mb-2 block">Keywords & Tags</Label>
                    <div className="flex flex-wrap gap-2">
                      {(course as any).Keywords.map((keyword: string, index: number) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {keyword}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Course Format */}
                <div>
                  <Label className="text-sm font-medium mb-2 block">Course Format</Label>
                  <div className="flex items-center gap-2">
                    {(course as any).Format === 2 ? (
                      <>
                        <FaVideo className="h-4 w-4 text-red-600" />
                        <span>Video Course</span>
                      </>
                    ) : (course as any).Format === 1 ? (
                      <>
                        <FaBook className="h-4 w-4 text-blue-600" />
                        <span>PDF/Text Course</span>
                      </>
                    ) : (course as any).Format === 3 ? (
                      <>
                        <FaBook className="h-4 w-4 text-green-600" />
                        <span>E-book Course</span>
                      </>
                    ) : (
                      <>
                        <FaList className="h-4 w-4 text-gray-600" />
                        <span>Mixed Format</span>
                      </>
                    )}
                  </div>
                </div>

                {/* Course Messages */}
                {(course as any).Message && (
                  <div>
                    <Label className="text-sm font-medium mb-2 block">Welcome Message</Label>
                    <div className="bg-blue-50 p-3 rounded-lg border-l-4 border-blue-400">
                      <p className="text-gray-700 text-sm">{(course as any).Message}</p>
                    </div>
                  </div>
                )}

                {/* Congratulation Message */}
                {(course as any).Congratulation && (
                  <div>
                    <Label className="text-sm font-medium mb-2 block">Completion Message</Label>
                    <div className="bg-green-50 p-3 rounded-lg border-l-4 border-green-400">
                      <p className="text-gray-700 text-sm">{(course as any).Congratulation}</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Instructor Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FaUser className="h-5 w-5" />
                  Instructor Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start gap-4">
                  {course.instructor.profileImage ? (
                    <img
                      src={course.instructor.profileImage}
                      alt={`${course.instructor.firstUsername} ${course.instructor.lastUsername}`}
                      className="w-20 h-20 rounded-full object-cover border-2 border-gray-200"
                    />
                  ) : (
                    <div className="w-20 h-20 rounded-full bg-gray-200 flex items-center justify-center">
                      <FaUser className="h-8 w-8 text-gray-400" />
                    </div>
                  )}
                  <div className="flex-1">
                    <h4 className="font-semibold text-lg mb-1">
                      {course.instructor.firstUsername} {course.instructor.lastUsername}
                    </h4>
                    <div className="flex items-center gap-2 mb-2">
                      <FaEnvelope className="h-4 w-4 text-gray-500" />
                      <span className="text-gray-600">{course.instructor.email}</span>
                    </div>
                    <div className="flex items-center gap-4 text-sm">
                      <Badge variant="outline">
                        {course.instructor.role}
                      </Badge>
                      <Badge variant={course.instructor.status === 'active' ? 'default' : 'secondary'}>
                        {course.instructor.status}
                      </Badge>
                    </div>
                  </div>
                </div>

                {/* Additional Instructor Details */}
                <Separator />
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium mb-2 block">Account Information</Label>
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center gap-2">
                        <FaIdCard className="h-4 w-4 text-gray-500" />
                        <span>ID: {course.instructor.id}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <FaCalendarAlt className="h-4 w-4 text-gray-500" />
                        <span>Member since: {formatDate(course.instructor.createdAt)}</span>
                      </div>
                      {(course.instructor as any).LastLogin && (
                        <div className="flex items-center gap-2">
                          <FaHistory className="h-4 w-4 text-gray-500" />
                          <span>Last login: {formatDate((course.instructor as any).LastLogin)}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  <div>
                    <Label className="text-sm font-medium mb-2 block">Contact & Profile</Label>
                    <div className="space-y-2 text-sm">
                      {(course.instructor as any).Phone && (
                        <div className="flex items-center gap-2">
                          <FaPhone className="h-4 w-4 text-gray-500" />
                          <span>{(course.instructor as any).Phone}</span>
                        </div>
                      )}
                      {(course.instructor as any).Location && (
                        <div className="flex items-center gap-2">
                          <FaMapMarkerAlt className="h-4 w-4 text-gray-500" />
                          <span>{(course.instructor as any).Location}</span>
                        </div>
                      )}
                      {(course.instructor as any).Website && (
                        <div className="flex items-center gap-2">
                          <FaGlobe className="h-4 w-4 text-gray-500" />
                          <a href={(course.instructor as any).Website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                            Website
                          </a>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Instructor Bio */}
                {(course.instructor as any).Bio && (
                  <div>
                    <Label className="text-sm font-medium mb-2 block">Instructor Bio</Label>
                    <div className="bg-gray-50 p-3 rounded-lg">
                      <p className="text-sm text-gray-700">{(course.instructor as any).Bio}</p>
                    </div>
                  </div>
                )}

                {/* Instructor Statistics */}
                <div>
                  <Label className="text-sm font-medium mb-2 block">Instructor Statistics</Label>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="text-center p-2 bg-blue-50 rounded">
                      <div className="font-semibold text-blue-600">{(course.instructor as any).CoursesCount || 0}</div>
                      <div className="text-xs text-gray-600">Courses</div>
                    </div>
                    <div className="text-center p-2 bg-green-50 rounded">
                      <div className="font-semibold text-green-600">{(course.instructor as any).StudentsCount || 0}</div>
                      <div className="text-xs text-gray-600">Students</div>
                    </div>
                    <div className="text-center p-2 bg-yellow-50 rounded">
                      <div className="font-semibold text-yellow-600">{(course.instructor as any).Rating || 0}</div>
                      <div className="text-xs text-gray-600">Rating</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Course Statistics */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FaChartBar className="h-5 w-5" />
                  Course Statistics & Engagement
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center p-3 bg-blue-50 rounded-lg">
                    <FaUsers className="h-6 w-6 text-blue-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-blue-600">{course.studentsCount}</div>
                    <div className="text-xs text-gray-600">Students</div>
                  </div>
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <FaEye className="h-6 w-6 text-green-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-green-600">{(course as any).Views || 0}</div>
                    <div className="text-xs text-gray-600">Views</div>
                  </div>
                  <div className="text-center p-3 bg-yellow-50 rounded-lg">
                    <FaStar className="h-6 w-6 text-yellow-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-yellow-600">{course.rating.toFixed(1)}</div>
                    <div className="text-xs text-gray-600">Rating</div>
                  </div>
                  <div className="text-center p-3 bg-purple-50 rounded-lg">
                    <FaHeart className="h-6 w-6 text-purple-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-purple-600">{(course as any).Likes || 0}</div>
                    <div className="text-xs text-gray-600">Likes</div>
                  </div>
                </div>

                {/* Additional Metrics */}
                <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="flex items-center gap-3 p-3 border rounded-lg">
                    <FaComments className="h-5 w-5 text-gray-600" />
                    <div>
                      <div className="font-semibold">{(course as any).CommentsCount || 0}</div>
                      <div className="text-xs text-gray-500">Comments</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-3 border rounded-lg">
                    <FaDownload className="h-5 w-5 text-gray-600" />
                    <div>
                      <div className="font-semibold">{(course as any).Downloads || 0}</div>
                      <div className="text-xs text-gray-500">Downloads</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-3 border rounded-lg">
                    <FaCertificate className="h-5 w-5 text-gray-600" />
                    <div>
                      <div className="font-semibold">{(course as any).CompletionRate || 0}%</div>
                      <div className="text-xs text-gray-500">Completion</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Course Sections */}
            {(course as any).Sections && (course as any).Sections.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FaList className="h-5 w-5" />
                    Course Curriculum ({(course as any).Sections.length} sections)
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {(course as any).Sections.map((section: any, index: number) => (
                      <div key={section.Id || index} className="border rounded-lg p-4 bg-gray-50">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h4 className="font-medium text-gray-900 mb-1">
                              {index + 1}. {section.Title}
                            </h4>
                            {section.Description && (
                              <p className="text-sm text-gray-600 mb-2">{section.Description}</p>
                            )}
                            <div className="flex items-center gap-4 text-xs text-gray-500">
                              {section.Contents && section.Contents.length > 0 && (
                                <span>{section.Contents.length} lesson{section.Contents.length !== 1 ? 's' : ''}</span>
                              )}
                              {section.Duration && (
                                <span className="flex items-center gap-1">
                                  <FaClock className="h-3 w-3" />
                                  {section.Duration} min
                                </span>
                              )}
                              {section.Order !== undefined && (
                                <span>Order: {section.Order}</span>
                              )}
                            </div>

                            {/* Section Contents Preview */}
                            {section.Contents && section.Contents.length > 0 && (
                              <div className="mt-2 pl-4 border-l-2 border-gray-200">
                                <div className="text-xs text-gray-500 mb-1">Lessons:</div>
                                {section.Contents.slice(0, 3).map((content: any, contentIndex: number) => (
                                  <div key={content.Id || contentIndex} className="text-xs text-gray-600 mb-1 flex items-center gap-2">
                                    {content.Type === 'video' ? <FaPlay className="h-3 w-3" /> : <FaFileAlt className="h-3 w-3" />}
                                    <span>{content.Title}</span>
                                    {content.Duration && <span className="text-gray-400">({content.Duration}min)</span>}
                                  </div>
                                ))}
                                {section.Contents.length > 3 && (
                                  <div className="text-xs text-gray-400">...and {section.Contents.length - 3} more</div>
                                )}
                              </div>
                            )}
                          </div>
                          <div className="flex flex-col gap-1">
                            <Badge variant="outline" className="text-xs">
                              {section.IsActive ? 'Active' : 'Inactive'}
                            </Badge>
                            {section.IsFree && (
                              <Badge variant="secondary" className="text-xs bg-green-100 text-green-800">
                                Free Preview
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar Information */}
          <div className="space-y-6">
            {/* Category & Metadata */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FaTag className="h-5 w-5" />
                  Category & Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-sm font-medium">Category</Label>
                  <Badge variant="secondary" className="ml-2">
                    {course.category.name}
                  </Badge>
                </div>

                <div>
                  <Label className="text-sm font-medium">Privacy</Label>
                  <Badge variant={course.isPrivate ? "outline" : "default"} className="ml-2">
                    {course.isPrivate ? "Private" : "Public"}
                  </Badge>
                </div>

                <div>
                  <Label className="text-sm font-medium">Course Type</Label>
                  <Badge variant="outline" className="ml-2">
                    {(course as any).Free ? "Free" : "Paid"}
                  </Badge>
                </div>

                {(course as any).Currency && (
                  <div>
                    <Label className="text-sm font-medium">Currency</Label>
                    <p className="text-sm font-medium">{(course as any).Currency}</p>
                  </div>
                )}

                {(course as any).NewPrice && (course as any).NewPrice !== course.price && (
                  <div>
                    <Label className="text-sm font-medium">Original Price</Label>
                    <div className="flex items-center gap-2">
                      <span className="text-sm line-through text-gray-500">${(course as any).NewPrice.toFixed(2)}</span>
                      <Badge variant="destructive" className="text-xs">Sale</Badge>
                    </div>
                  </div>
                )}

                <div>
                  <Label className="text-sm font-medium">Students Enrolled</Label>
                  <p className="text-lg font-semibold">{course.studentsCount}</p>
                </div>

                <div>
                  <Label className="text-sm font-medium">Rating</Label>
                  <div className="flex items-center gap-1">
                    <span className="text-lg font-semibold">{course.rating.toFixed(1)}</span>
                    <FaStar className="h-4 w-4 text-yellow-500" />
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-medium">Course ID</Label>
                  <p className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">{course.id}</p>
                </div>

                <div>
                  <Label className="text-sm font-medium">Course Slug</Label>
                  <p className="text-sm font-mono bg-gray-100 px-2 py-1 rounded break-all">{course.slug}</p>
                </div>
              </CardContent>
            </Card>

            {/* Media Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FaImage className="h-5 w-5" />
                  Course Media
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-sm font-medium">Cover Image</Label>
                  <div className="flex items-center gap-2 text-sm">
                    {course.thumbnail ? (
                      <Badge variant="default" className="bg-green-100 text-green-800">
                        ✓ Uploaded
                      </Badge>
                    ) : (
                      <Badge variant="destructive">
                        ✗ Missing
                      </Badge>
                    )}
                  </div>
                </div>

                {(course as any).PresentationVideo && (
                  <div>
                    <Label className="text-sm font-medium">Presentation Video</Label>
                    <Badge variant="default" className="ml-2 bg-green-100 text-green-800">
                      ✓ Uploaded
                    </Badge>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Verification Checklist */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FaShieldAlt className="h-5 w-5" />
                  Verification Checklist
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    {course.title && course.title.length >= 10 ? (
                      <Badge variant="default" className="bg-green-100 text-green-800 text-xs">✓</Badge>
                    ) : (
                      <Badge variant="destructive" className="text-xs">✗</Badge>
                    )}
                    <span>Title is descriptive (10+ chars)</span>
                  </div>

                  <div className="flex items-center gap-2">
                    {course.description && course.description.length >= 50 ? (
                      <Badge variant="default" className="bg-green-100 text-green-800 text-xs">✓</Badge>
                    ) : (
                      <Badge variant="destructive" className="text-xs">✗</Badge>
                    )}
                    <span>Description is detailed (50+ chars)</span>
                  </div>

                  <div className="flex items-center gap-2">
                    {course.thumbnail ? (
                      <Badge variant="default" className="bg-green-100 text-green-800 text-xs">✓</Badge>
                    ) : (
                      <Badge variant="destructive" className="text-xs">✗</Badge>
                    )}
                    <span>Has cover image</span>
                  </div>

                  <div className="flex items-center gap-2">
                    {course.price >= 0 ? (
                      <Badge variant="default" className="bg-green-100 text-green-800 text-xs">✓</Badge>
                    ) : (
                      <Badge variant="destructive" className="text-xs">✗</Badge>
                    )}
                    <span>Price is set</span>
                  </div>

                  <div className="flex items-center gap-2">
                    {(course as any).Sections && (course as any).Sections.length > 0 ? (
                      <Badge variant="default" className="bg-green-100 text-green-800 text-xs">✓</Badge>
                    ) : (
                      <Badge variant="destructive" className="text-xs">✗</Badge>
                    )}
                    <span>Has course content</span>
                  </div>

                  <div className="flex items-center gap-2">
                    {course.instructor.email && course.instructor.firstUsername && course.instructor.lastUsername ? (
                      <Badge variant="default" className="bg-green-100 text-green-800 text-xs">✓</Badge>
                    ) : (
                      <Badge variant="destructive" className="text-xs">✗</Badge>
                    )}
                    <span>Instructor profile complete</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Technical Details */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FaIdCard className="h-5 w-5" />
                  Technical Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-sm font-medium">System Information</Label>
                  <div className="space-y-2 text-sm mt-2">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Course ID:</span>
                      <span className="font-mono bg-gray-100 px-2 py-1 rounded text-xs">{course.id}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Slug:</span>
                      <span className="font-mono bg-gray-100 px-2 py-1 rounded text-xs break-all">{course.slug}</span>
                    </div>
                    {(course as any).Version && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">Version:</span>
                        <span className="font-mono bg-gray-100 px-2 py-1 rounded text-xs">{(course as any).Version}</span>
                      </div>
                    )}
                  </div>
                </div>

                <Separator />

                <div>
                  <Label className="text-sm font-medium">Publishing Status</Label>
                  <div className="space-y-2 text-sm mt-2">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Published:</span>
                      <Badge variant={!(course as any).isPrivate ? "default" : "outline"}>
                        {!(course as any).isPrivate ? "Yes" : "No"}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Archived:</span>
                      <Badge variant={(course as any).Archived ? "secondary" : "outline"}>
                        {(course as any).Archived ? "Yes" : "No"}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Featured:</span>
                      <Badge variant={(course as any).Featured ? "default" : "outline"}>
                        {(course as any).Featured ? "Yes" : "No"}
                      </Badge>
                    </div>
                  </div>
                </div>

                <Separator />

                <div>
                  <Label className="text-sm font-medium">Content Settings</Label>
                  <div className="space-y-2 text-sm mt-2">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Downloadable:</span>
                      <Badge variant={(course as any).Downloadable ? "default" : "outline"}>
                        {(course as any).Downloadable ? "Yes" : "No"}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Certificate:</span>
                      <Badge variant={(course as any).Certificate ? "default" : "outline"}>
                        {(course as any).Certificate ? "Enabled" : "Disabled"}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Forum:</span>
                      <Badge variant={(course as any).Forum ? "default" : "outline"}>
                        {(course as any).Forum ? "Enabled" : "Disabled"}
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Timeline */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FaCalendarAlt className="h-5 w-5" />
                  Timeline & History
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-start gap-3 p-3 border-l-4 border-blue-400 bg-blue-50">
                    <FaCalendarAlt className="h-4 w-4 text-blue-600 mt-0.5" />
                    <div>
                      <Label className="text-sm font-medium text-blue-800">Created</Label>
                      <p className="text-sm text-blue-700">{formatDate(course.createdAt)}</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3 p-3 border-l-4 border-green-400 bg-green-50">
                    <FaHistory className="h-4 w-4 text-green-600 mt-0.5" />
                    <div>
                      <Label className="text-sm font-medium text-green-800">Last Updated</Label>
                      <p className="text-sm text-green-700">{formatDate(course.updatedAt)}</p>
                    </div>
                  </div>

                  {(course as any).LastPublishedDate && (
                    <div className="flex items-start gap-3 p-3 border-l-4 border-purple-400 bg-purple-50">
                      <FaGlobe className="h-4 w-4 text-purple-600 mt-0.5" />
                      <div>
                        <Label className="text-sm font-medium text-purple-800">Last Published</Label>
                        <p className="text-sm text-purple-700">{formatDate((course as any).LastPublishedDate)}</p>
                      </div>
                    </div>
                  )}

                  {(course as any).SubmittedForReview && (
                    <div className="flex items-start gap-3 p-3 border-l-4 border-yellow-400 bg-yellow-50">
                      <FaShieldAlt className="h-4 w-4 text-yellow-600 mt-0.5" />
                      <div>
                        <Label className="text-sm font-medium text-yellow-800">Submitted for Review</Label>
                        <p className="text-sm text-yellow-700">{formatDate((course as any).SubmittedForReview)}</p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Activity Log Preview */}
                {(course as any).ActivityLog && (course as any).ActivityLog.length > 0 && (
                  <div>
                    <Label className="text-sm font-medium mb-2 block">Recent Activity</Label>
                    <div className="space-y-2 max-h-32 overflow-y-auto">
                      {(course as any).ActivityLog.slice(0, 5).map((activity: any, index: number) => (
                        <div key={index} className="text-xs p-2 bg-gray-50 rounded border-l-2 border-gray-300">
                          <div className="flex justify-between items-start">
                            <span className="text-gray-700">{activity.Action}</span>
                            <span className="text-gray-500">{formatDate(activity.Date)}</span>
                          </div>
                          {activity.Details && (
                            <p className="text-gray-600 mt-1">{activity.Details}</p>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Additional Information Sections */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Course Reviews & Feedback */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FaComments className="h-5 w-5" />
                Student Reviews & Feedback
              </CardTitle>
            </CardHeader>
            <CardContent>
              {(course as any).Reviews && (course as any).Reviews.length > 0 ? (
                <div className="space-y-4">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-yellow-600">{course.rating.toFixed(1)}</div>
                      <div className="flex items-center justify-center gap-1">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <FaStar
                            key={star}
                            className={`h-4 w-4 ${star <= Math.round(course.rating) ? 'text-yellow-500' : 'text-gray-300'}`}
                          />
                        ))}
                      </div>
                      <div className="text-xs text-gray-500">{(course as any).Reviews.length} reviews</div>
                    </div>
                    <div className="flex-1">
                      {[5, 4, 3, 2, 1].map((rating) => {
                        const count = (course as any).Reviews.filter((r: any) => Math.round(r.Rating) === rating).length;
                        const percentage = (course as any).Reviews.length > 0 ? (count / (course as any).Reviews.length) * 100 : 0;
                        return (
                          <div key={rating} className="flex items-center gap-2 text-xs">
                            <span>{rating}★</span>
                            <div className="flex-1 bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-yellow-500 h-2 rounded-full"
                                style={{ width: `${percentage}%` }}
                              />
                            </div>
                            <span className="w-8 text-right">{count}</span>
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  <div className="space-y-3 max-h-64 overflow-y-auto">
                    {(course as any).Reviews.slice(0, 5).map((review: any, index: number) => (
                      <div key={index} className="border rounded-lg p-3 bg-gray-50">
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                              <FaUser className="h-4 w-4 text-gray-600" />
                            </div>
                            <div>
                              <div className="font-medium text-sm">{review.StudentName || 'Anonymous'}</div>
                              <div className="flex items-center gap-1">
                                {[1, 2, 3, 4, 5].map((star) => (
                                  <FaStar
                                    key={star}
                                    className={`h-3 w-3 ${star <= review.Rating ? 'text-yellow-500' : 'text-gray-300'}`}
                                  />
                                ))}
                              </div>
                            </div>
                          </div>
                          <span className="text-xs text-gray-500">{formatDate(review.Date)}</span>
                        </div>
                        <p className="text-sm text-gray-700">{review.Comment}</p>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <FaComments className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                  <p>No reviews yet</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Reports & Issues */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FaFlag className="h-5 w-5" />
                Reports & Issues
              </CardTitle>
            </CardHeader>
            <CardContent>
              {(course as any).Reports && (course as any).Reports.length > 0 ? (
                <div className="space-y-3">
                  <Alert variant="destructive">
                    <FaExclamationTriangle className="h-4 w-4" />
                    <AlertDescription>
                      This course has {(course as any).Reports.length} report{(course as any).Reports.length !== 1 ? 's' : ''}
                    </AlertDescription>
                  </Alert>

                  <div className="space-y-3 max-h-64 overflow-y-auto">
                    {(course as any).Reports.map((report: any, index: number) => (
                      <div key={index} className="border border-red-200 rounded-lg p-3 bg-red-50">
                        <div className="flex items-start justify-between mb-2">
                          <div>
                            <Badge variant="destructive" className="text-xs mb-1">
                              {report.Type || 'General Report'}
                            </Badge>
                            <div className="text-sm font-medium">{report.Title || 'Report'}</div>
                          </div>
                          <span className="text-xs text-gray-500">{formatDate(report.Date)}</span>
                        </div>
                        <p className="text-sm text-gray-700">{report.Description}</p>
                        <div className="text-xs text-gray-500 mt-2">
                          Reported by: {report.ReporterName || 'Anonymous'}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <FaShieldAlt className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                  <p>No reports or issues</p>
                  <Badge variant="default" className="mt-2 bg-green-100 text-green-800">
                    Clean Record
                  </Badge>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Content Quality Analysis */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FaChartBar className="h-5 w-5" />
              Content Quality Analysis
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* Content Completeness */}
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-3 relative">
                  <div className="w-full h-full rounded-full border-4 border-gray-200 relative">
                    <div
                      className="absolute top-0 left-0 w-full h-full rounded-full border-4 border-blue-500"
                      style={{
                        clipPath: `polygon(50% 50%, 50% 0%, ${50 + (course.description ? 25 : 0) + (course.thumbnail ? 25 : 0) + ((course as any).Sections?.length > 0 ? 25 : 0) + ((course as any).Goals?.length > 0 ? 25 : 0)}% 0%, 100% 100%, 0% 100%)`
                      }}
                    />
                  </div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-sm font-bold text-blue-600">
                      {Math.round((
                        (course.description ? 25 : 0) +
                        (course.thumbnail ? 25 : 0) +
                        ((course as any).Sections?.length > 0 ? 25 : 0) +
                        ((course as any).Goals?.length > 0 ? 25 : 0)
                      ))}%
                    </span>
                  </div>
                </div>
                <div className="text-sm font-medium">Content Complete</div>
                <div className="text-xs text-gray-500">Description, Image, Sections, Goals</div>
              </div>

              {/* Educational Value */}
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-3 bg-green-100 rounded-full flex items-center justify-center">
                  <FaGraduationCap className="h-8 w-8 text-green-600" />
                </div>
                <div className="text-sm font-medium">Educational Value</div>
                <Badge variant={((course as any).Goals?.length > 0 && (course as any).Prerequisites?.length > 0) ? "default" : "outline"}>
                  {((course as any).Goals?.length > 0 && (course as any).Prerequisites?.length > 0) ? "High" : "Medium"}
                </Badge>
              </div>

              {/* Technical Quality */}
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-3 bg-purple-100 rounded-full flex items-center justify-center">
                  <FaVideo className="h-8 w-8 text-purple-600" />
                </div>
                <div className="text-sm font-medium">Technical Quality</div>
                <Badge variant={(course.thumbnail && (course as any).Sections?.length > 0) ? "default" : "outline"}>
                  {(course.thumbnail && (course as any).Sections?.length > 0) ? "Good" : "Needs Review"}
                </Badge>
              </div>

              {/* Market Readiness */}
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-3 bg-yellow-100 rounded-full flex items-center justify-center">
                  <FaDollarSign className="h-8 w-8 text-yellow-600" />
                </div>
                <div className="text-sm font-medium">Market Ready</div>
                <Badge variant={(course.price > 0 && course.category.name !== 'Uncategorized' && course.description.length > 50) ? "default" : "outline"}>
                  {(course.price > 0 && course.category.name !== 'Uncategorized' && course.description.length > 50) ? "Ready" : "Pending"}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Approval Modal */}
        <Dialog open={showApproveModal} onOpenChange={setShowApproveModal}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Approve Course</DialogTitle>
            </DialogHeader>
            <div className="py-4">
              <Alert>
                <AlertDescription>
                  <div>
                    <p>
                      Are you sure you want to approve the course <strong>"{course.title}"</strong>?
                    </p>
                    <p className="mb-0">
                      Once approved, it will be published and available to students for enrollment.
                    </p>
                  </div>
                </AlertDescription>
              </Alert>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowApproveModal(false)}>
                Cancel
              </Button>
              <Button 
                onClick={handleApprove}
                disabled={approveMutation.isPending}
                className="bg-green-600 hover:bg-green-700"
              >
                {approveMutation.isPending ? 'Approving...' : 'Approve Course'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Rejection Modal */}
        <Dialog open={showRejectModal} onOpenChange={setShowRejectModal}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Reject Course</DialogTitle>
            </DialogHeader>
            <div className="py-4 space-y-4">
              <Alert variant="destructive">
                <AlertDescription>
                  Are you sure you want to reject the course "{course.title}"?
                </AlertDescription>
              </Alert>

              <div className="space-y-2">
                <Label htmlFor="rejectReason" className="text-sm font-medium">
                  Reason for Rejection <span className="text-red-500">*</span>
                </Label>
                <Textarea
                  id="rejectReason"
                  placeholder="Please provide a detailed reason for rejecting this course..."
                  value={rejectReason}
                  onChange={(e) => {
                    console.log('📝 Rejection reason updated:', e.target.value);
                    setRejectReason(e.target.value);
                  }}
                  rows={4}
                  className="min-h-[100px]"
                />
              </div>
            </div>
            <DialogFooter className="flex items-center justify-between">
              <div className="text-sm text-gray-500">
                {rejectReason.trim().length === 0 ? (
                  <span className="text-red-500">Reason is required</span>
                ) : rejectReason.trim().length < 10 ? (
                  <span className="text-yellow-500">Please provide more detail ({rejectReason.trim().length}/10 min)</span>
                ) : (
                  <span className="text-green-500">✓ Reason provided ({rejectReason.trim().length} characters)</span>
                )}
              </div>
              <div className="flex gap-2">
                <Button variant="outline" onClick={() => {
                  console.log('❌ Rejection cancelled by user');
                  setShowRejectModal(false);
                  setRejectReason('');
                }}>
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => {
                    console.log('🚫 Reject button clicked with reason:', rejectReason);
                    handleReject();
                  }}
                  disabled={!rejectReason.trim() || rejectReason.trim().length < 10 || rejectMutation.isPending}
                >
                  {rejectMutation.isPending ? 'Rejecting...' : 'Reject Course'}
                </Button>
              </div>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </>
  );
};

export default AdminCourseVerificationDetailsPage;
