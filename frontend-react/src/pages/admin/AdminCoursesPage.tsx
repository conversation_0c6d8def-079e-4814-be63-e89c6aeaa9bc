import React, { useState, useMemo } from 'react';
import {
  FaGraduationCap,
  FaPlus,
  FaArchive,
  FaCheckCircle,
  FaClock,
  FaDownload,
  FaSync,
  FaSearch,
  FaChartLine,
  FaBookOpen,
  FaExclamationTriangle,
  FaBook
} from 'react-icons/fa';

// Enhanced styles for the courses page
const coursesStyles = `
  .courses-card-hover:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 32px rgba(0,0,0,0.15) !important;
    transition: all 0.3s ease;
  }

  .courses-stats-card {
    animation: slideInUp 0.6s ease-out;
  }

  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .ant-statistic-content {
    display: flex;
    align-items: center;
    gap: 12px;
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = coursesStyles;
  document.head.appendChild(styleSheet);
}
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Loader2 } from "lucide-react";
import AdminCoursesGrid from '../../components/admin/data/AdminCoursesGrid';
import { useAdminCourses, useAdminCategories, useAdminCourseStats } from '../../hooks/useAdmin';
import type { AdminCourse } from '../../services/admin.service';
import SEOHelmet from '../../components/common/SEOHelmet';
import { useNavigate } from 'react-router-dom';

const AdminCoursesPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [filters, setFilters] = useState({
    status: '',
    category: '',
    search: ''
  });

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  // API hooks - using real services only
  const { data: categoriesData, isLoading: categoriesLoading } = useAdminCategories();
  const { data: coursesData, isLoading: coursesLoading, refetch: refetchCourses } = useAdminCourses({
    page: currentPage,
    limit: pageSize,
    search: searchQuery,
    status: filters.status || undefined,
    category: filters.category || undefined
  });

  // Get real course stats from backend
  const { data: statsData, isLoading: statsLoading, refetch: refetchStats } = useAdminCourseStats();

  // Use real stats from backend
  const stats = useMemo(() => {
    return statsData || {
      totalCourses: 0,
      publishedCourses: 0,
      pendingCourses: 0,
      archivedCourses: 0
    };
  }, [statsData]);

  // Filter courses based on search and status
  const filteredCourses = useMemo(() => {
    let courses = coursesData?.data || [];

    if (searchQuery) {
      courses = courses.filter(course =>
        course.Title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        course.Description?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    if (statusFilter !== 'all') {
      courses = courses.filter(course => {
        switch (statusFilter) {
          case 'published': return course.Published;
          case 'pending': return !course.Published && !course.Archived;
          case 'archived': return course.Archived;
          default: return true;
        }
      });
    }

    return courses;
  }, [coursesData?.data, searchQuery, statusFilter]);

  // Get categories for filter dropdown
  const categories = useMemo(() => {
    return categoriesData || [];
  }, [categoriesData]);

  // Event handlers
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setFilters(prev => ({ ...prev, search: query }));
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setCurrentPage(1); // Reset to first page when filtering
  };

  const handlePaginationChange = (page: number, size: number) => {
    setCurrentPage(page);
    setPageSize(size);
  };

  const handleRefresh = async () => {
    await Promise.all([
      refetchCourses(),
      refetchStats()
    ]);
  };

  const handleCourseEdit = (course: AdminCourse) => {
    // Navigate to admin course edit page
    console.log('Edit course:', course);
    navigate(`/admin/courses/${course.id}/edit`);
  };

  const handleExportCourses = () => {
    const courses = coursesData?.data || [];
    const csvContent = courses.map(course =>
      `${course.title},${course.instructor.firstUsername} ${course.instructor.lastUsername},${course.category.name},${course.status},${course.price},${course.studentsCount}`
    ).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'courses.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  // Loading state
  if (coursesLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-3">Loading courses...</span>
      </div>
    );
  }

  return (
    <>
      <SEOHelmet
        title="Course Management - Admin"
        description="Manage courses, reviews, and approvals across the platform."
      />

      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="bg-gradient-to-r from-purple-600 to-purple-800 rounded-2xl p-8 mb-8 text-white shadow-xl relative overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute top-0 right-0 w-48 h-full opacity-10">
              <div className="w-full h-full bg-gradient-to-br from-white/20 to-transparent"></div>
            </div>

            <div className="flex justify-between items-center relative">
              <div>
                <h1 className="text-3xl font-bold flex items-center gap-3 mb-2">
                  <FaGraduationCap className="text-4xl" />
                  Course Management
                </h1>
                <p className="text-purple-100">
                  Manage courses, reviews, and approvals across the platform
                </p>
              </div>

              <div className="flex gap-3">
                <Button
                  onClick={handleRefresh}
                  disabled={coursesLoading || statsLoading}
                  variant="outline"
                  className="bg-white/10 border-white/20 text-white hover:bg-white/20"
                >
                  <FaSync className="mr-2" />
                  Refresh
                </Button>
                <Button
                  onClick={handleExportCourses}
                  disabled={!coursesData?.data?.length}
                  variant="outline"
                  className="bg-white/10 border-white/20 text-white hover:bg-white/20"
                >
                  <FaDownload className="mr-2" />
                  Export
                </Button>
              </div>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Courses</p>
                    <p className="text-2xl font-bold">{stats.totalCourses}</p>
                  </div>
                  <FaBook className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Published</p>
                    <p className="text-2xl font-bold">{stats.publishedCourses}</p>
                  </div>
                  <FaCheckCircle className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Pending</p>
                    <p className="text-2xl font-bold">{stats.pendingCourses}</p>
                  </div>
                  <FaClock className="h-8 w-8 text-yellow-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Archived</p>
                    <p className="text-2xl font-bold">{stats.archivedCourses}</p>
                  </div>
                  <FaArchive className="h-8 w-8 text-gray-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Filters and Actions */}
          <Card className="mb-6">
            <CardContent className="pt-6">
              <div className="flex flex-col sm:flex-row gap-4 justify-between">
                <div className="flex gap-4">
                  <div className="relative">
                    <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search courses..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10 w-64"
                    />
                  </div>

                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="published">Published</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="archived">Archived</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Button onClick={() => navigate('/admin/courses/create')}>
                  <FaPlus className="mr-2" />
                  Add Course
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Courses Grid */}
          <AdminCoursesGrid
            courses={filteredCourses}
            loading={coursesLoading}
            onRefresh={handleRefresh}
          />
        </div>
      </div>
    </>
  );
};

export default AdminCoursesPage;
