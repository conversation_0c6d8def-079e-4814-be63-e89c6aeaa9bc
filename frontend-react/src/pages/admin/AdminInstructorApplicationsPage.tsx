import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardHeader,
  CardTitle,
  CardContent,
  Badge,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Tabs,
  TabsList,
  TabsTrigger,
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  AlertDialog,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Textarea,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
  Avatar,
  AvatarFallback
} from '../../components/ui';
import {
  FaEye,
  FaCheck,
  FaTimes,
  FaTrash,
  FaUser,
  FaEnvelope,
  FaPhone,
  FaGlobe,
  FaLinkedin,
  FaGraduationCap,
  FaSearch,
  FaChartLine,
  FaClipboardList,
  FaUsers,
  FaCheckCircle,
  FaExclamationTriangle,
  FaSpinner
} from 'react-icons/fa';
import { toast } from 'sonner';
import SEOHelmet from '../../components/common/SEOHelmet';
import instructorService from '../../services/instructor.service';
import { useAuth } from '../../contexts/AuthContext';

interface InstructorApplication {
  Id: number;
  Slug: string;
  FirstName: string;
  LastName: string;
  Email: string;
  Phone?: string;
  Country?: string;
  City?: string;
  Title?: string;
  Bio?: string;
  Experience: string;
  Expertise?: string[];
  LinkedinUrl?: string;
  WebsiteUrl?: string;
  Education?: Array<{
    degree: string;
    institution: string;
    year: string;
    field: string;
  }>;
  TeachingExperience?: string;
  PreviousPlatforms?: string;
  SampleCourseOutline?: string;
  Status: 'pending' | 'approved' | 'rejected';
  AdminNotes?: string;
  ReviewedAt?: string;
  CreatedAt: string;
  UpdatedAt: string;
}

const AdminInstructorApplicationsPage: React.FC = () => {
  const { authState } = useAuth();
  const { user } = authState;
  
  const [applications, setApplications] = useState<InstructorApplication[]>([]);
  const [allApplications, setAllApplications] = useState<InstructorApplication[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedApplication, setSelectedApplication] = useState<InstructorApplication | null>(null);
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [actionModalVisible, setActionModalVisible] = useState(false);
  const [actionType, setActionType] = useState<'approve' | 'reject'>('approve');
  const [adminNotes, setAdminNotes] = useState('');
  const [actionLoading, setActionLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('pending');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'name'>('newest');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [applicationToDelete, setApplicationToDelete] = useState<InstructorApplication | null>(null);

  useEffect(() => {
    fetchApplications();
  }, [activeTab]);

  const fetchApplications = async () => {
    try {
      setLoading(true);
      const data = await instructorService.getApplications();
      const applicationsArray = Array.isArray(data) ? data : [];
      setAllApplications(applicationsArray);
      filterAndSortApplications(applicationsArray, activeTab, searchTerm, sortBy);
    } catch (error) {
      console.error('Error fetching applications:', error);
      toast.error('Failed to fetch applications');
      setAllApplications([]);
    } finally {
      setLoading(false);
    }
  };

  const filterAndSortApplications = (
    apps: InstructorApplication[],
    tab: string,
    search: string,
    sort: string
  ) => {
    let filtered = apps;

    // Filter by status
    if (tab !== 'all') {
      filtered = filtered.filter(app => app.Status === tab);
    }

    // Filter by search term
    if (search) {
      const searchLower = search.toLowerCase();
      filtered = filtered.filter(app =>
        `${app.FirstName} ${app.LastName}`.toLowerCase().includes(searchLower) ||
        app.Email.toLowerCase().includes(searchLower) ||
        app.Title?.toLowerCase().includes(searchLower) ||
        app.Country?.toLowerCase().includes(searchLower)
      );
    }

    // Sort applications
    filtered.sort((a, b) => {
      switch (sort) {
        case 'oldest':
          return new Date(a.CreatedAt).getTime() - new Date(b.CreatedAt).getTime();
        case 'name':
          return `${a.FirstName} ${a.LastName}`.localeCompare(`${b.FirstName} ${b.LastName}`);
        default: // newest
          return new Date(b.CreatedAt).getTime() - new Date(a.CreatedAt).getTime();
      }
    });

    setApplications(filtered);
  };

  useEffect(() => {
    filterAndSortApplications(allApplications, activeTab, searchTerm, sortBy);
  }, [allApplications, activeTab, searchTerm, sortBy]);

  const handleViewApplication = (application: InstructorApplication) => {
    setSelectedApplication(application);
    setViewModalVisible(true);
  };

  const handleAction = (application: InstructorApplication, action: 'approve' | 'reject') => {
    setSelectedApplication(application);
    setActionType(action);
    setAdminNotes('');
    setActionModalVisible(true);
  };

  const handleActionSubmit = async () => {
    if (!selectedApplication || !user) return;

    if (actionType === 'reject' && !adminNotes.trim()) {
      toast.error('Admin notes are required for rejection');
      return;
    }

    try {
      setActionLoading(true);

      if (actionType === 'approve') {
        await instructorService.approveApplication(
          selectedApplication.Slug,
          user.Slug || '',
          adminNotes
        );
        toast.success('Application approved successfully! The applicant will receive login credentials via email.');
      } else {
        await instructorService.rejectApplication(
          selectedApplication.Slug,
          user.Slug || '',
          adminNotes
        );
        toast.success('Application rejected. The applicant has been notified.');
      }

      setActionModalVisible(false);
      setAdminNotes('');
      fetchApplications();
    } catch (error) {
      console.error('Error processing application:', error);
      toast.error('Failed to process application');
    } finally {
      setActionLoading(false);
    }
  };

  const handleDeleteApplication = (application: InstructorApplication) => {
    setApplicationToDelete(application);
    setDeleteDialogOpen(true);
  };

  const confirmDeleteApplication = async () => {
    if (!applicationToDelete) return;
    
    try {
      await instructorService.deleteApplication(applicationToDelete.Slug);
      toast.success('Application deleted successfully');
      fetchApplications();
    } catch (error) {
      console.error('Error deleting application:', error);
      toast.error('Failed to delete application');
    } finally {
      setDeleteDialogOpen(false);
      setApplicationToDelete(null);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="text-yellow-600 border-yellow-600">Pending</Badge>;
      case 'approved':
        return <Badge variant="default" className="bg-green-600 text-white">Approved</Badge>;
      case 'rejected':
        return <Badge variant="destructive">Rejected</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getStatsCards = () => {
    const stats = {
      total: allApplications.length,
      pending: allApplications.filter(app => app.Status === 'pending').length,
      approved: allApplications.filter(app => app.Status === 'approved').length,
      rejected: allApplications.filter(app => app.Status === 'rejected').length,
    };

    return (
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card className="applications-card-hover">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Applications</p>
                <p className="text-3xl font-bold">{stats.total}</p>
              </div>
              <FaClipboardList className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="applications-card-hover">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Pending Review</p>
                <p className="text-3xl font-bold text-yellow-600">{stats.pending}</p>
              </div>
              <FaExclamationTriangle className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="applications-card-hover">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Approved</p>
                <p className="text-3xl font-bold text-green-600">{stats.approved}</p>
              </div>
              <FaCheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="applications-card-hover">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Rejected</p>
                <p className="text-3xl font-bold text-red-600">{stats.rejected}</p>
              </div>
              <FaTimes className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  return (
    <TooltipProvider>
      <div className="min-h-screen bg-gray-50 p-6">
        <SEOHelmet
          title="Instructor Applications - Admin Dashboard"
          description="Manage instructor applications and review submissions"
        />

        <div className="max-w-7xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Instructor Applications</h1>
            <p className="text-gray-600">Review and manage instructor applications</p>
          </div>

          {getStatsCards()}

          <Card>
            <CardHeader>
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <CardTitle className="text-xl">Applications Management</CardTitle>
                <div className="flex flex-col sm:flex-row gap-4 w-full sm:w-auto">
                  <div className="relative">
                    <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search applications..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 w-full sm:w-64"
                    />
                  </div>
                  <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
                    <SelectTrigger className="w-full sm:w-40">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="newest">Newest First</SelectItem>
                      <SelectItem value="oldest">Oldest First</SelectItem>
                      <SelectItem value="name">Name A-Z</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="all">All ({allApplications.length})</TabsTrigger>
                  <TabsTrigger value="pending">
                    Pending ({allApplications.filter(app => app.Status === 'pending').length})
                  </TabsTrigger>
                  <TabsTrigger value="approved">
                    Approved ({allApplications.filter(app => app.Status === 'approved').length})
                  </TabsTrigger>
                  <TabsTrigger value="rejected">
                    Rejected ({allApplications.filter(app => app.Status === 'rejected').length})
                  </TabsTrigger>
                </TabsList>

                <div className="mt-6">
                  {loading ? (
                    <div className="flex justify-center items-center py-12">
                      <FaSpinner className="animate-spin h-8 w-8 text-blue-600" />
                      <span className="ml-2 text-gray-600">Loading applications...</span>
                    </div>
                  ) : applications.length === 0 ? (
                    <div className="text-center py-12">
                      <FaUsers className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No applications found</h3>
                      <p className="text-gray-500">
                        {activeTab === 'all' 
                          ? 'No instructor applications have been submitted yet.'
                          : `No ${activeTab} applications found.`
                        }
                      </p>
                    </div>
                  ) : (
                    <div className="border rounded-lg overflow-hidden">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead className="font-semibold">Applicant</TableHead>
                            <TableHead className="font-semibold">Contact</TableHead>
                            <TableHead className="font-semibold">Experience</TableHead>
                            <TableHead className="font-semibold">Status</TableHead>
                            <TableHead className="font-semibold">Applied</TableHead>
                            <TableHead className="font-semibold text-center">Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {applications.map((application) => (
                            <TableRow key={application.Id} className="hover:bg-gray-50">
                              <TableCell>
                                <div className="flex items-center gap-3">
                                  <Avatar className="h-10 w-10">
                                    <AvatarFallback className="bg-blue-100 text-blue-600">
                                      {application.FirstName?.charAt(0) || 'A'}
                                    </AvatarFallback>
                                  </Avatar>
                                  <div>
                                    <div className="font-semibold text-gray-900">
                                      {application.FirstName} {application.LastName}
                                    </div>
                                    <div className="text-sm text-gray-500">{application.Title}</div>
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="space-y-1">
                                  <div className="flex items-center gap-2 text-sm">
                                    <FaEnvelope className="h-3 w-3 text-gray-400" />
                                    <span>{application.Email}</span>
                                  </div>
                                  {application.Phone && (
                                    <div className="flex items-center gap-2 text-sm text-gray-500">
                                      <FaPhone className="h-3 w-3 text-gray-400" />
                                      <span>{application.Phone}</span>
                                    </div>
                                  )}
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="text-sm">
                                  <div className="font-medium">{application.Experience}</div>
                                  {application.Country && (
                                    <div className="text-gray-500">{application.Country}</div>
                                  )}
                                </div>
                              </TableCell>
                              <TableCell>
                                {getStatusBadge(application.Status)}
                              </TableCell>
                              <TableCell>
                                <div className="text-sm text-gray-500">
                                  {new Date(application.CreatedAt).toLocaleDateString()}
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="flex items-center justify-center gap-2">
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => handleViewApplication(application)}
                                        className="h-8 w-8 p-0"
                                      >
                                        <FaEye className="h-4 w-4" />
                                      </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>View Details</TooltipContent>
                                  </Tooltip>

                                  {application.Status === 'pending' && (
                                    <>
                                      <Tooltip>
                                        <TooltipTrigger asChild>
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => handleAction(application, 'approve')}
                                            className="h-8 w-8 p-0 text-green-600 hover:text-green-700 hover:bg-green-50"
                                          >
                                            <FaCheck className="h-4 w-4" />
                                          </Button>
                                        </TooltipTrigger>
                                        <TooltipContent>Approve</TooltipContent>
                                      </Tooltip>

                                      <Tooltip>
                                        <TooltipTrigger asChild>
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => handleAction(application, 'reject')}
                                            className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                                          >
                                            <FaTimes className="h-4 w-4" />
                                          </Button>
                                        </TooltipTrigger>
                                        <TooltipContent>Reject</TooltipContent>
                                      </Tooltip>
                                    </>
                                  )}

                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => handleDeleteApplication(application)}
                                        className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                                      >
                                        <FaTrash className="h-4 w-4" />
                                      </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>Delete</TooltipContent>
                                  </Tooltip>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  )}
                </div>
              </Tabs>
            </CardContent>
          </Card>
        </div>

        {/* View Application Modal */}
        <Dialog open={viewModalVisible} onOpenChange={setViewModalVisible}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="text-xl">
                Application Details - {selectedApplication?.FirstName} {selectedApplication?.LastName}
              </DialogTitle>
            </DialogHeader>
            
            {selectedApplication && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg flex items-center gap-2">
                        <FaUser className="h-5 w-5" />
                        Personal Information
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <span className="font-medium text-gray-600">Name:</span>
                        <p className="font-medium">{selectedApplication.FirstName} {selectedApplication.LastName}</p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-600">Email:</span>
                        <p>{selectedApplication.Email}</p>
                      </div>
                      {selectedApplication.Phone && (
                        <div>
                          <span className="font-medium text-gray-600">Phone:</span>
                          <p>{selectedApplication.Phone}</p>
                        </div>
                      )}
                      {selectedApplication.Country && (
                        <div>
                          <span className="font-medium text-gray-600">Location:</span>
                          <p>{selectedApplication.City ? `${selectedApplication.City}, ` : ''}{selectedApplication.Country}</p>
                        </div>
                      )}
                      {selectedApplication.Title && (
                        <div>
                          <span className="font-medium text-gray-600">Professional Title:</span>
                          <p>{selectedApplication.Title}</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg flex items-center gap-2">
                        <FaChartLine className="h-5 w-5" />
                        Professional Details
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <span className="font-medium text-gray-600">Experience Level:</span>
                        <p>{selectedApplication.Experience}</p>
                      </div>
                      {selectedApplication.Expertise && selectedApplication.Expertise.length > 0 && (
                        <div>
                          <span className="font-medium text-gray-600">Expertise:</span>
                          <div className="flex flex-wrap gap-2 mt-1">
                            {selectedApplication.Expertise.map((skill, index) => (
                              <Badge key={index} variant="secondary">{skill}</Badge>
                            ))}
                          </div>
                        </div>
                      )}
                      {selectedApplication.LinkedinUrl && (
                        <div>
                          <span className="font-medium text-gray-600">LinkedIn:</span>
                          <a 
                            href={selectedApplication.LinkedinUrl} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:underline flex items-center gap-1"
                          >
                            <FaLinkedin className="h-4 w-4" />
                            View Profile
                          </a>
                        </div>
                      )}
                      {selectedApplication.WebsiteUrl && (
                        <div>
                          <span className="font-medium text-gray-600">Website:</span>
                          <a 
                            href={selectedApplication.WebsiteUrl} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:underline flex items-center gap-1"
                          >
                            <FaGlobe className="h-4 w-4" />
                            Visit Website
                          </a>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>

                {selectedApplication.Bio && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Biography</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-gray-700 leading-relaxed">{selectedApplication.Bio}</p>
                    </CardContent>
                  </Card>
                )}

                {selectedApplication.Education && selectedApplication.Education.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg flex items-center gap-2">
                        <FaGraduationCap className="h-5 w-5" />
                        Education
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {selectedApplication.Education.map((edu, index) => (
                          <div key={index} className="border-l-4 border-blue-200 pl-4">
                            <h4 className="font-semibold">{edu.degree}</h4>
                            <p className="text-gray-600">{edu.institution}</p>
                            <p className="text-sm text-gray-500">{edu.field} • {edu.year}</p>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {selectedApplication.TeachingExperience && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Teaching Experience</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-gray-700 leading-relaxed">{selectedApplication.TeachingExperience}</p>
                    </CardContent>
                  </Card>
                )}

                {selectedApplication.PreviousPlatforms && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Previous Teaching Platforms</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-gray-700 leading-relaxed">{selectedApplication.PreviousPlatforms}</p>
                    </CardContent>
                  </Card>
                )}

                {selectedApplication.SampleCourseOutline && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Sample Course Outline</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">{selectedApplication.SampleCourseOutline}</p>
                    </CardContent>
                  </Card>
                )}

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Application Status</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center gap-3">
                      <span className="font-medium text-gray-600">Current Status:</span>
                      {getStatusBadge(selectedApplication.Status)}
                    </div>
                    <div>
                      <span className="font-medium text-gray-600">Applied:</span>
                      <p>{new Date(selectedApplication.CreatedAt).toLocaleString()}</p>
                    </div>
                    {selectedApplication.ReviewedAt && (
                      <div>
                        <span className="font-medium text-gray-600">Reviewed:</span>
                        <p>{new Date(selectedApplication.ReviewedAt).toLocaleString()}</p>
                      </div>
                    )}
                    {selectedApplication.AdminNotes && (
                      <div>
                        <span className="font-medium text-gray-600">Admin Notes:</span>
                        <p className="text-gray-700 bg-gray-50 p-3 rounded-md mt-1">{selectedApplication.AdminNotes}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* Action Modal (Approve/Reject) */}
        <Dialog open={actionModalVisible} onOpenChange={setActionModalVisible}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {actionType === 'approve' ? 'Approve Application' : 'Reject Application'}
              </DialogTitle>
            </DialogHeader>
            
            <div className="space-y-4">
              <div>
                <p className="text-sm text-gray-600 mb-2">
                  {actionType === 'approve' 
                    ? 'Are you sure you want to approve this instructor application? The applicant will receive login credentials via email.'
                    : 'Are you sure you want to reject this instructor application? Please provide a reason for rejection.'
                  }
                </p>
                
                {selectedApplication && (
                  <div className="bg-gray-50 p-3 rounded-md">
                    <p className="font-medium">{selectedApplication.FirstName} {selectedApplication.LastName}</p>
                    <p className="text-sm text-gray-600">{selectedApplication.Email}</p>
                  </div>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {actionType === 'approve' ? 'Notes (Optional)' : 'Reason for Rejection *'}
                </label>
                <Textarea
                  value={adminNotes}
                  onChange={(e) => setAdminNotes(e.target.value)}
                  placeholder={
                    actionType === 'approve' 
                      ? 'Add any notes for this approval...'
                      : 'Please explain why this application is being rejected...'
                  }
                  rows={4}
                  className="w-full"
                />
              </div>
            </div>

            <DialogFooter>
              <Button 
                variant="outline" 
                onClick={() => setActionModalVisible(false)}
                disabled={actionLoading}
              >
                Cancel
              </Button>
              <Button 
                variant={actionType === 'approve' ? 'default' : 'destructive'}
                onClick={handleActionSubmit}
                disabled={actionLoading}
              >
                {actionLoading && <FaSpinner className="animate-spin h-4 w-4 mr-2" />}
                {actionType === 'approve' ? 'Approve Application' : 'Reject Application'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <AlertDialog
          open={deleteDialogOpen}
          onOpenChange={setDeleteDialogOpen}
          title="Delete Application"
          description={`Are you sure you want to delete the application from ${applicationToDelete?.FirstName} ${applicationToDelete?.LastName}? This action cannot be undone.`}
          confirmText="Delete"
          cancelText="Cancel"
          variant="destructive"
          onConfirm={confirmDeleteApplication}
        />
      </div>
    </TooltipProvider>
  );
};

export default AdminInstructorApplicationsPage;
