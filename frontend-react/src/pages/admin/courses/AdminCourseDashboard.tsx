import React, { useState, useMemo } from 'react';
import { <PERSON>a<PERSON><PERSON>, <PERSON>aDollarSign, FaEye, FaEdit, FaTrash, FaPlus, FaGraduationCap } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useCourses, useCourseMutations } from '../../../hooks';
import type { Course } from '../../../models';

const AdminCourseDashboard: React.FC = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [courseToDelete, setCourseToDelete] = useState<Course | null>(null);

  // Fetch courses data
  const { data: courses, loading, error, refetch } = useCourses(100, 0, false);
  const { updateCourse, deleteCourse, approveCourse, rejectCourse } = useCourseMutations();

  // Calculate basic stats
  const stats = useMemo(() => {
    if (!courses || courses.length === 0) {
      return {
        totalCourses: 0,
        publishedCourses: 0,
        pendingCourses: 0,
        archivedCourses: 0
      };
    }

    const published = courses.filter(c => c.Published).length;
    const pending = courses.filter(c => !c.Published && !c.Archived).length;
    const archived = courses.filter(c => c.Archived).length;

    return {
      totalCourses: courses.length,
      publishedCourses: published,
      pendingCourses: pending,
      archivedCourses: archived
    };
  }, [courses]);

  // Filter courses based on search
  const filteredCourses = useMemo(() => {
    if (!courses) return [];
    if (!searchQuery) return courses;
    
    return courses.filter(course =>
      course.Title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      course.CreatedBy?.Firstname?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      course.CreatedBy?.Lastname?.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [courses, searchQuery]);

  const handleCourseAction = async (action: string, course: Course) => {
    try {
      switch (action) {
        case 'approve':
          await approveCourse.execute(course.Id!);
          break;
        case 'reject':
          await rejectCourse.execute({ courseId: course.Id!, reason: 'Rejected by admin' });
          break;
        case 'archive':
          await updateCourse.execute({ ...course, Archived: true });
          break;
        case 'delete':
          setCourseToDelete(course);
          setShowDeleteModal(true);
          return;
      }
      refetch();
    } catch (error) {
      console.error(`Failed to ${action} course:`, error);
    }
  };

  const confirmDelete = async () => {
    if (courseToDelete) {
      try {
        await deleteCourse.execute(courseToDelete);
        setShowDeleteModal(false);
        setCourseToDelete(null);
        refetch();
      } catch (error) {
        console.error('Failed to delete course:', error);
      }
    }
  };

  const getStatusBadge = (course: Course) => {
    if (course.Archived) return <Badge variant="secondary">Archived</Badge>;
    if (course.Published) return <Badge variant="default">Published</Badge>;
    return <Badge variant="outline">Pending</Badge>;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading courses...</div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>
          Failed to load courses. Please try again later.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <FaGraduationCap className="text-blue-600" />
            Course Management Dashboard
          </h1>
          <p className="text-gray-600 mt-1">
            Comprehensive course management with analytics and approval workflows
          </p>
        </div>
        <Button onClick={() => navigate('/admin/courses/create')} className="flex items-center gap-2">
          <FaPlus />
          Create Course
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Courses</CardTitle>
            <FaBook className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalCourses}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Published</CardTitle>
            <FaBook className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.publishedCourses}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Review</CardTitle>
            <FaBook className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.pendingCourses}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Archived</CardTitle>
            <FaBook className="h-4 w-4 text-gray-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-600">{stats.archivedCourses}</div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Search Courses</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <Input
              placeholder="Search courses by title or instructor..."
              value={searchQuery}
              onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => setSearchQuery(e.target.value)}
              className="flex-1"
            />
          </div>
        </CardContent>
      </Card>

      {/* Courses Table */}
      <Card>
        <CardHeader>
          <CardTitle>Courses ({filteredCourses.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Course</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Price</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Created</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredCourses.map((course) => (
                <TableRow key={course.Id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-8 bg-gray-100 rounded flex items-center justify-center">
                        <FaBook className="text-gray-400" />
                      </div>
                      <div>
                        <div className="font-medium">{course.Title}</div>
                        <div className="text-sm text-gray-500">
                          by {course.CreatedBy?.Firstname} {course.CreatedBy?.Lastname}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    {course.Categories?.[0]?.Title || course.Categories?.[0]?.Name || 'Uncategorized'}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <FaDollarSign className="text-gray-400" />
                      {course.Free ? 'Free' : `$${course.Price || 0}`}
                    </div>
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(course)}
                  </TableCell>
                  <TableCell>
                    {new Date(course.CreatedAt || '').toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => navigate(`/admin/courses/${course.Id}/view`)}
                      >
                        <FaEye />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => navigate(`/admin/courses/${course.Id}/edit`)}
                      >
                        <FaEdit />
                      </Button>
                      {!course.Published && !course.Archived && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleCourseAction('approve', course)}
                          className="text-green-600"
                        >
                          Approve
                        </Button>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleCourseAction('delete', course)}
                        className="text-red-600"
                      >
                        <FaTrash />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Delete Confirmation Modal */}
      <Dialog open={showDeleteModal} onOpenChange={setShowDeleteModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Delete</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <Alert variant="destructive">
              <AlertDescription>
                This action cannot be undone!
              </AlertDescription>
            </Alert>
            <p>
              Are you sure you want to delete the course "{courseToDelete?.Title}"?
              This will permanently remove the course and all associated data.
            </p>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowDeleteModal(false)}>
                Cancel
              </Button>
              <Button variant="destructive" onClick={confirmDelete}>
                Delete
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminCourseDashboard;
