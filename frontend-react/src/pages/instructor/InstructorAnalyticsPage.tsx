import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2 } from "lucide-react";
import {
  TrendingUp,
  Users,
  DollarSign,
  BookOpen,
  Star,
  Eye,
  Clock,
  Download,
  BarChart3,
  Pie<PERSON>hart,
  Calendar
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useConnectedUser, useCoursesByInstructor } from '../../hooks';
import { toast } from 'sonner';

const InstructorAnalyticsPage: React.FC = () => {
  const { authState } = useAuth();
  const { user } = authState;
  const [timeRange, setTimeRange] = useState('30d');
  const [selectedCourse, setSelectedCourse] = useState('all');

  // Fetch real data using hooks
  const { data: connectedUser, loading: userLoading } = useConnectedUser();
  const { data: instructorCourses, loading: coursesLoading, error: coursesError } = useCoursesByInstructor(connectedUser?.Slug || null);

  // Calculate real analytics data from API
  const overviewStats = React.useMemo(() => {
    if (!instructorCourses) return {
      totalRevenue: 0,
      totalStudents: 0,
      totalCourses: 0,
      avgRating: 0,
      completionRate: 0,
      refundRate: 0
    };

    const totalCourses = instructorCourses.length;
    // Note: Enrollments relationship not available in current Course model
    const totalStudents = 0; // Will be calculated when enrollments are available
    const totalRevenue = 0; // Will be calculated when enrollments are available

    const allRatings = instructorCourses.flatMap(course => course.Ratings || []);
    const avgRating = allRatings.length > 0
      ? allRatings.reduce((sum, rating) => sum + (rating.Rating || 0), 0) / allRatings.length
      : 0;

    const completedEnrollments = 0; // Will be calculated when enrollments are available
    const completionRate = 0; // Will be calculated when enrollments are available

    return {
      totalRevenue,
      totalStudents,
      totalCourses,
      avgRating: Math.round(avgRating * 10) / 10,
      completionRate: Math.round(completionRate),
      refundRate: 0 // Will be calculated when refund data is available
    };
  }, [instructorCourses]);

  // Generate revenue data from enrollments (simplified)
  const revenueData = React.useMemo(() => {
    if (!instructorCourses) return [];

    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
    return months.map(month => ({
      month,
      revenue: Math.floor(overviewStats.totalRevenue / 6), // Simplified distribution
      students: Math.floor(overviewStats.totalStudents / 6)
    }));
  }, [instructorCourses, overviewStats]);

  // Get top courses from real data
  const topCourses = React.useMemo(() => {
    if (!instructorCourses) return [];

    return instructorCourses
      .map(course => ({
        name: course.Title,
        students: 0, // Will be calculated when enrollments are available
        revenue: 0, // Will be calculated when enrollments are available
        rating: course.Ratings && course.Ratings.length > 0
          ? course.Ratings.reduce((sum, r) => sum + (r.Rating || 0), 0) / course.Ratings.length
          : 0
      }))
      .sort((a, b) => b.rating - a.rating) // Sort by rating instead of students for now
      .slice(0, 4);
  }, [instructorCourses]);

  // Generate student engagement data (simplified)
  const studentEngagement = React.useMemo(() => {
    return {
      avgWatchTime: 45, // Will be calculated when watch time data is available
      completionRate: overviewStats.completionRate,
      dropoffPoints: [
        { lesson: 'Introduction', dropoff: 5 },
        { lesson: 'Advanced Concepts', dropoff: 25 },
        { lesson: 'Final Project', dropoff: 15 }
      ]
    };
  }, [overviewStats]);

  // Loading state
  if (userLoading || coursesLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-brand-red-primary" />
            <p className="text-muted-foreground">Loading analytics data...</p>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (coursesError) {
    return (
      <div className="container mx-auto p-6">
        <Alert className="max-w-md mx-auto">
          <AlertDescription>
            Failed to load analytics data. Please try refreshing the page.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Analytics Dashboard</h1>
          <p className="text-muted-foreground">Track your course performance and earnings</p>
        </div>
        <div className="flex space-x-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${overviewStats.totalRevenue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              <TrendingUp className="inline w-3 h-3 mr-1" />
              +12% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Students</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overviewStats.totalStudents.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              <TrendingUp className="inline w-3 h-3 mr-1" />
              +8% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Rating</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overviewStats.avgRating}</div>
            <p className="text-xs text-muted-foreground">
              Across {overviewStats.totalCourses} courses
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completion Rate</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overviewStats.completionRate}%</div>
            <Progress value={overviewStats.completionRate} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Watch Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{studentEngagement.avgWatchTime}min</div>
            <p className="text-xs text-muted-foreground">
              Per session
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Refund Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overviewStats.refundRate}%</div>
            <p className="text-xs text-muted-foreground">
              Below industry average
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <Tabs defaultValue="revenue" className="space-y-4">
        <TabsList>
          <TabsTrigger value="revenue">Revenue</TabsTrigger>
          <TabsTrigger value="students">Students</TabsTrigger>
          <TabsTrigger value="courses">Course Performance</TabsTrigger>
          <TabsTrigger value="engagement">Engagement</TabsTrigger>
        </TabsList>

        <TabsContent value="revenue" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Revenue Trend</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {revenueData.map((data, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm font-medium">{data.month}</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-24 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-primary h-2 rounded-full" 
                            style={{ width: `${(data.revenue / 8000) * 100}%` }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium">${data.revenue.toLocaleString()}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Top Performing Courses</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {topCourses.map((course, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="font-medium text-sm">{course.name}</p>
                        <p className="text-xs text-muted-foreground">
                          {course.students} students • {course.rating} ⭐
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">${course.revenue.toLocaleString()}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="students" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Student Growth</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {revenueData.map((data, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm font-medium">{data.month}</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-24 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-500 h-2 rounded-full" 
                            style={{ width: `${(data.students / 250) * 100}%` }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium">{data.students} students</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Student Demographics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Beginners</span>
                      <span>45%</span>
                    </div>
                    <Progress value={45} />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Intermediate</span>
                      <span>35%</span>
                    </div>
                    <Progress value={35} />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Advanced</span>
                      <span>20%</span>
                    </div>
                    <Progress value={20} />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="courses" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Course Performance Metrics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {topCourses.map((course, index) => (
                  <div key={index} className="p-4 border rounded-lg">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h4 className="font-medium">{course.name}</h4>
                        <p className="text-sm text-muted-foreground">
                          {course.students} enrolled • {course.rating} ⭐ rating
                        </p>
                      </div>
                      <Badge variant="outline">${course.revenue.toLocaleString()}</Badge>
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <p className="text-muted-foreground">Completion Rate</p>
                        <p className="font-medium">78%</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Avg Rating</p>
                        <p className="font-medium">{course.rating}/5.0</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Revenue</p>
                        <p className="font-medium">${course.revenue.toLocaleString()}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="engagement" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Engagement Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Average Watch Time</span>
                    <span>{studentEngagement.avgWatchTime} minutes</span>
                  </div>
                  <Progress value={(studentEngagement.avgWatchTime / 60) * 100} />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Course Completion</span>
                    <span>{studentEngagement.completionRate}%</span>
                  </div>
                  <Progress value={studentEngagement.completionRate} />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Student Retention</span>
                    <span>85%</span>
                  </div>
                  <Progress value={85} />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Common Drop-off Points</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {studentEngagement.dropoffPoints.map((point, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <span className="text-sm font-medium">{point.lesson}</span>
                      <Badge variant="destructive">{point.dropoff}% drop-off</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default InstructorAnalyticsPage;
