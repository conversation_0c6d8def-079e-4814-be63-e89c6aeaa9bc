/**
 * Live Sessions Page for Instructors
 * Manage live classes, webinars, and virtual sessions
 */

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Calendar, Clock, Users, Video, Plus, Settings, Play, Pause, Square } from "lucide-react";
import SEOHelmet from '../../components/common/SEOHelmet';

interface LiveSession {
  id: string;
  title: string;
  courseTitle: string;
  scheduledTime: Date;
  duration: number;
  attendees: number;
  maxAttendees: number;
  status: 'scheduled' | 'live' | 'ended' | 'cancelled';
  meetingLink?: string;
}

const LiveSessionsPage: React.FC = () => {
  const [sessions] = useState<LiveSession[]>([
    {
      id: '1',
      title: 'Introduction to React Hooks',
      courseTitle: 'Complete React Development',
      scheduledTime: new Date('2025-07-10T14:00:00'),
      duration: 90,
      attendees: 45,
      maxAttendees: 100,
      status: 'scheduled',
      meetingLink: 'https://meet.brainmaker.com/react-hooks-intro'
    },
    {
      id: '2',
      title: 'Advanced State Management',
      courseTitle: 'Complete React Development',
      scheduledTime: new Date('2025-07-08T16:00:00'),
      duration: 120,
      attendees: 38,
      maxAttendees: 100,
      status: 'live',
      meetingLink: 'https://meet.brainmaker.com/state-management'
    }
  ]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'live': return 'bg-green-500';
      case 'scheduled': return 'bg-blue-500';
      case 'ended': return 'bg-gray-500';
      case 'cancelled': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const formatDateTime = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  return (
    <>
      <SEOHelmet 
        title="Live Sessions - Instructor Dashboard"
        description="Manage your live classes, webinars, and virtual sessions"
      />

      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-nunito-bold text-gray-900">Live Sessions</h1>
            <p className="text-gray-600 font-nunito-medium">Manage your live classes and webinars</p>
          </div>
          <Button className="bg-red-600 hover:bg-red-700 text-white font-nunito-semibold">
            <Plus className="w-4 h-4 mr-2" />
            Schedule Session
          </Button>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Calendar className="w-5 h-5 text-blue-600" />
                <div>
                  <p className="text-sm text-gray-600">Scheduled</p>
                  <p className="text-2xl font-nunito-bold text-gray-900">3</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Video className="w-5 h-5 text-green-600" />
                <div>
                  <p className="text-sm text-gray-600">Live Now</p>
                  <p className="text-2xl font-nunito-bold text-gray-900">1</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Users className="w-5 h-5 text-purple-600" />
                <div>
                  <p className="text-sm text-gray-600">Total Attendees</p>
                  <p className="text-2xl font-nunito-bold text-gray-900">83</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Clock className="w-5 h-5 text-orange-600" />
                <div>
                  <p className="text-sm text-gray-600">Hours This Week</p>
                  <p className="text-2xl font-nunito-bold text-gray-900">12</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sessions Tabs */}
        <Tabs defaultValue="upcoming" className="space-y-4">
          <TabsList>
            <TabsTrigger value="upcoming">Upcoming</TabsTrigger>
            <TabsTrigger value="live">Live Now</TabsTrigger>
            <TabsTrigger value="past">Past Sessions</TabsTrigger>
          </TabsList>

          <TabsContent value="upcoming" className="space-y-4">
            {sessions.filter(s => s.status === 'scheduled').map((session) => (
              <Card key={session.id}>
                <CardContent className="p-6">
                  <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-lg font-nunito-semibold text-gray-900">{session.title}</h3>
                        <Badge className={`${getStatusColor(session.status)} text-white`}>
                          {session.status.charAt(0).toUpperCase() + session.status.slice(1)}
                        </Badge>
                      </div>
                      <p className="text-gray-600 font-nunito-medium mb-2">{session.courseTitle}</p>
                      <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
                        <span className="flex items-center gap-1">
                          <Calendar className="w-4 h-4" />
                          {formatDateTime(session.scheduledTime)}
                        </span>
                        <span className="flex items-center gap-1">
                          <Clock className="w-4 h-4" />
                          {session.duration} minutes
                        </span>
                        <span className="flex items-center gap-1">
                          <Users className="w-4 h-4" />
                          {session.attendees}/{session.maxAttendees} registered
                        </span>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        <Settings className="w-4 h-4 mr-2" />
                        Settings
                      </Button>
                      <Button className="bg-green-600 hover:bg-green-700 text-white" size="sm">
                        <Play className="w-4 h-4 mr-2" />
                        Start Session
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>

          <TabsContent value="live" className="space-y-4">
            {sessions.filter(s => s.status === 'live').map((session) => (
              <Card key={session.id} className="border-green-200 bg-green-50">
                <CardContent className="p-6">
                  <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-lg font-nunito-semibold text-gray-900">{session.title}</h3>
                        <Badge className="bg-green-500 text-white animate-pulse">
                          🔴 LIVE
                        </Badge>
                      </div>
                      <p className="text-gray-600 font-nunito-medium mb-2">{session.courseTitle}</p>
                      <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
                        <span className="flex items-center gap-1">
                          <Users className="w-4 h-4" />
                          {session.attendees} attendees
                        </span>
                        <span className="flex items-center gap-1">
                          <Clock className="w-4 h-4" />
                          Started {formatDateTime(session.scheduledTime)}
                        </span>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        <Pause className="w-4 h-4 mr-2" />
                        Pause
                      </Button>
                      <Button variant="destructive" size="sm">
                        <Square className="w-4 h-4 mr-2" />
                        End Session
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>

          <TabsContent value="past">
            <Alert>
              <AlertDescription>
                Past sessions will be displayed here with recordings and analytics.
              </AlertDescription>
            </Alert>
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
};

export default LiveSessionsPage;
