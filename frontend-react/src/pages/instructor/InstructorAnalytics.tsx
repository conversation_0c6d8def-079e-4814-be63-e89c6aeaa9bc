import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import {
  ChartLine,
  Users,
  DollarSign,
  Star,
  Eye,
  Download,
  Calendar,
  GraduationCap,
  Clock,
  Heart,
  MessageCircle,
  Share,
  Filter,
  ArrowUp,
  ArrowDown,
  TrendingUp,
  TrendingDown
} from 'lucide-react';
import { useCoursesByInstructor } from '../../hooks';
import { useAuth } from '../../contexts/AuthContext';
import SEOHelmet from '../../components/common/SEOHelmet';

interface AnalyticsData {
  totalRevenue: number;
  monthlyRevenue: number;
  totalStudents: number;
  newStudents: number;
  averageRating: number;
  totalReviews: number;
  completionRate: number;
  engagementScore: number;
  courseViews: number;
  conversionRate: number;
}

interface CoursePerformance {
  courseId: number;
  title: string;
  students: number;
  revenue: number;
  rating: number;
  completionRate: number;
  views: number;
  trend: 'up' | 'down' | 'stable';
}

interface RevenueData {
  month: string;
  revenue: number;
  students: number;
}

const InstructorAnalytics: React.FC = () => {
  const { authState } = useAuth();
  const { user } = authState;
  const { data: courses } = useCoursesByInstructor(user?.Slug || null);

  const [selectedPeriod, setSelectedPeriod] = useState<'month'>('month');
  const [selectedCourse, setSelectedCourse] = useState<string>('all');

  // Mock analytics data - replace with actual API calls
  const [analytics, setAnalytics] = useState<AnalyticsData>({
    totalRevenue: 15420,
    monthlyRevenue: 2340,
    totalStudents: 1247,
    newStudents: 89,
    averageRating: 4.6,
    totalReviews: 324,
    completionRate: 78,
    engagementScore: 85,
    courseViews: 5678,
    conversionRate: 12.5
  });

  const [coursePerformance, setCoursePerformance] = useState<CoursePerformance[]>([
    {
      courseId: 1,
      title: 'React Fundamentals',
      students: 456,
      revenue: 6840,
      rating: 4.8,
      completionRate: 82,
      views: 2340,
      trend: 'up'
    },
    {
      courseId: 2,
      title: 'Advanced JavaScript',
      students: 321,
      revenue: 4815,
      rating: 4.5,
      completionRate: 75,
      views: 1890,
      trend: 'up'
    },
    {
      courseId: 3,
      title: 'Node.js Backend',
      students: 234,
      revenue: 3510,
      rating: 4.3,
      completionRate: 68,
      views: 1456,
      trend: 'down'
    }
  ]);

  const [revenueData, setRevenueData] = useState<RevenueData[]>([
    { month: 'Jan', revenue: 1200, students: 45 },
    { month: 'Feb', revenue: 1800, students: 67 },
    { month: 'Mar', revenue: 2100, students: 78 },
    { month: 'Apr', revenue: 2340, students: 89 },
    { month: 'May', revenue: 2800, students: 102 },
    { month: 'Jun', revenue: 3200, students: 118 }
  ]);

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <ArrowUp className="text-success" />;
      case 'down': return <ArrowDown className="text-danger" />;
      default: return <FaClock className="text-muted" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getPerformanceColor = (value: number, type: 'rating' | 'completion' | 'conversion') => {
    switch (type) {
      case 'rating':
        return value >= 4.5 ? 'success' : value >= 4.0 ? 'warning' : 'danger';
      case 'completion':
        return value >= 80 ? 'success' : value >= 60 ? 'warning' : 'danger';
      case 'conversion':
        return value >= 15 ? 'success' : value >= 10 ? 'warning' : 'danger';
      default:
        return 'primary';
    }
  };

  return (
    <>
      <SEOHelmet
        title="Analytics Dashboard - BrainMaker Academy"
        description="Track your course performance, earnings, and student engagement"
      />

      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
              <p className="text-gray-600 mt-2">Track your course performance and earnings</p>
            </div>
            <div className="flex gap-3">
              <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="week">Last 7 days</SelectItem>
                  <SelectItem value="month">Last 30 days</SelectItem>
                  <SelectItem value="quarter">Last 3 months</SelectItem>
                  <SelectItem value="year">Last 12 months</SelectItem>
                </SelectContent>
              </Select>

              <Select value={selectedCourse} onValueChange={setSelectedCourse}>
                <SelectTrigger className="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Courses</SelectItem>
                  {courses?.map(course => (
                    <SelectItem key={course.Id} value={course.Id!.toString()}>
                      {course.Title || 'Untitled'}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Button>
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>

          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                    <p className="text-2xl font-bold">{formatCurrency(analytics.totalRevenue)}</p>
                    <p className="text-sm text-green-600">
                      +{formatCurrency(analytics.monthlyRevenue)} this month
                    </p>
                  </div>
                  <DollarSign className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Students</p>
                    <p className="text-2xl font-bold">{analytics.totalStudents.toLocaleString()}</p>
                    <p className="text-sm text-blue-600">
                      +{analytics.newStudents} new this month
                    </p>
                  </div>
                  <Users className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Average Rating</p>
                    <p className="text-2xl font-bold">{analytics.averageRating.toFixed(1)}</p>
                    <p className="text-sm text-yellow-600">
                      {analytics.totalReviews} reviews
                    </p>
                  </div>
                  <Star className="h-8 w-8 text-yellow-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Completion Rate</p>
                    <p className="text-2xl font-bold">{analytics.completionRate}%</p>
                    <p className="text-sm text-purple-600">
                      {analytics.engagementScore}% engagement
                    </p>
                  </div>
                  <GraduationCap className="h-8 w-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Additional Insights */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardContent className="pt-6 text-center">
                <Heart className="h-12 w-12 text-red-500 mx-auto mb-4" />
                <h3 className="text-2xl font-bold">94%</h3>
                <p className="text-gray-600">Student Retention</p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6 text-center">
                <MessageCircle className="h-12 w-12 text-blue-500 mx-auto mb-4" />
                <h3 className="text-2xl font-bold">156</h3>
                <p className="text-gray-600">Q&A Interactions</p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6 text-center">
                <Share className="h-12 w-12 text-green-500 mx-auto mb-4" />
                <h3 className="text-2xl font-bold">23%</h3>
                <p className="text-gray-600">Referral Rate</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </>
  );
};

export default InstructorAnalytics;
