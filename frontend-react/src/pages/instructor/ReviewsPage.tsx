import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Loader2 } from "lucide-react";
import {
  Star,
  Users,
  MessageCircle,
  ThumbsUp,
  ThumbsDown,
  Flag,
  Search,
  Filter,
  Download,
  Trophy,
  Heart,
  CheckCircle,
  User
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useConnectedUser, useCoursesByInstructor } from '../../hooks';
import { toast } from 'sonner';

const ReviewsPage: React.FC = () => {
  const { authState } = useAuth();
  const { user } = authState;
  const [searchTerm, setSearchTerm] = useState('');
  const [ratingFilter, setRatingFilter] = useState('all');
  const [courseFilter, setCourseFilter] = useState('all');

  // Fetch real data using hooks
  const { data: connectedUser, loading: userLoading } = useConnectedUser();
  const { data: instructorCourses, loading: coursesLoading, error: coursesError } = useCoursesByInstructor(connectedUser?.Slug || null);

  // Calculate real reviews data from API data
  const reviewsData = React.useMemo(() => {
    if (!instructorCourses) return {
      overview: { totalReviews: 0, averageRating: 0, fiveStars: 0, fourStars: 0, threeStars: 0, twoStars: 0, oneStar: 0 },
      reviews: []
    };

    // Get all ratings from instructor's courses
    const allRatings = instructorCourses.flatMap(course =>
      (course.Ratings || []).map(rating => ({
        ...rating,
        courseName: course.Title,
        courseId: course.Id
      }))
    );

    // Calculate rating distribution
    const ratingCounts = { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 };
    allRatings.forEach(rating => {
      const stars = Math.round(rating.Rating || 0);
      if (stars >= 1 && stars <= 5) {
        ratingCounts[stars as keyof typeof ratingCounts]++;
      }
    });

    const totalReviews = allRatings.length;
    const averageRating = totalReviews > 0
      ? allRatings.reduce((sum, rating) => sum + (rating.Rating || 0), 0) / totalReviews
      : 0;

    // Transform ratings to reviews format
    const reviews = allRatings.map((rating, index) => ({
      id: rating.Id || index,
      studentName: rating.User?.Username || 'Anonymous Student',
      studentAvatar: rating.User?.Photo?.Hashname ? `http://localhost:3200/${rating.User.Photo.Hashname}` : null,
      rating: rating.Rating || 0,
      comment: rating.Comment || 'No comment provided',
      courseName: rating.courseName,
      date: new Date(rating.CreatedAt || Date.now()).toLocaleDateString(),
      helpful: 0, // Will be calculated when helpful votes are available
      verified: true // Assume verified for now
    }));

    return {
      overview: {
        totalReviews,
        averageRating: Math.round(averageRating * 10) / 10,
        fiveStars: Math.round((ratingCounts[5] / totalReviews) * 100) || 0,
        fourStars: Math.round((ratingCounts[4] / totalReviews) * 100) || 0,
        threeStars: Math.round((ratingCounts[3] / totalReviews) * 100) || 0,
        twoStars: Math.round((ratingCounts[2] / totalReviews) * 100) || 0,
        oneStar: Math.round((ratingCounts[1] / totalReviews) * 100) || 0
      },
      reviews
    };
  }, [instructorCourses]);

  // Filter reviews based on search and filters
  const filteredReviews = reviewsData.reviews.filter(review => {
    const matchesSearch = review.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         review.comment.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         review.courseName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRating = ratingFilter === 'all' || Math.round(review.rating).toString() === ratingFilter;
    const matchesCourse = courseFilter === 'all' || review.courseName === courseFilter;
    return matchesSearch && matchesRating && matchesCourse;
  });

  // Loading state
  if (userLoading || coursesLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-brand-red-primary" />
            <p className="text-muted-foreground">Loading reviews data...</p>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (coursesError) {
    return (
      <div className="container mx-auto p-6">
        <Alert className="max-w-md mx-auto">
          <AlertDescription>
            Failed to load reviews data. Please try refreshing the page.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < Math.floor(rating)
            ? 'fill-yellow-400 text-yellow-400'
            : i < rating
            ? 'fill-yellow-200 text-yellow-400'
            : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Reviews & Ratings</h1>
          <p className="text-muted-foreground">Manage and respond to student feedback</p>
        </div>
        <Button variant="outline">
          <Download className="h-4 w-4 mr-2" />
          Export Reviews
        </Button>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Total Reviews</p>
                <p className="text-2xl font-bold text-gray-900">
                  {reviewsData.overview.totalReviews.toLocaleString()}
                </p>
              </div>
              <MessageCircle className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Average Rating</p>
                <div className="flex items-center gap-2">
                  <p className="text-2xl font-bold text-gray-900">
                    {reviewsData.overview.averageRating}
                  </p>
                  <div className="flex">
                    {renderStars(reviewsData.overview.averageRating)}
                  </div>
                </div>
              </div>
              <Star className="w-8 h-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">5-Star Reviews</p>
                <p className="text-2xl font-bold text-gray-900">
                  {reviewsData.overview.fiveStars}%
                </p>
              </div>
              <Trophy className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Response Rate</p>
                <p className="text-2xl font-bold text-gray-900">
                  85%
                </p>
              </div>
              <Heart className="w-8 h-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Rating Distribution */}
      <Card>
        <CardHeader>
          <CardTitle>Rating Distribution</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[5, 4, 3, 2, 1].map((stars) => {
              const percentage = reviewsData.overview[`${stars === 1 ? 'oneStar' : stars === 2 ? 'twoStars' : stars === 3 ? 'threeStars' : stars === 4 ? 'fourStars' : 'fiveStars'}` as keyof typeof reviewsData.overview] as number;
              return (
                <div key={stars} className="flex items-center gap-4">
                  <div className="flex items-center gap-1 w-16">
                    <span className="text-sm font-medium">{stars}</span>
                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  </div>
                  <Progress value={percentage} className="flex-1" />
                  <span className="text-sm text-muted-foreground w-12">{percentage}%</span>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Reviews</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search reviews..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={ratingFilter} onValueChange={setRatingFilter}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="All Ratings" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Ratings</SelectItem>
                <SelectItem value="5">5 Stars</SelectItem>
                <SelectItem value="4">4 Stars</SelectItem>
                <SelectItem value="3">3 Stars</SelectItem>
                <SelectItem value="2">2 Stars</SelectItem>
                <SelectItem value="1">1 Star</SelectItem>
              </SelectContent>
            </Select>
            <Select value={courseFilter} onValueChange={setCourseFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="All Courses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Courses</SelectItem>
                {instructorCourses?.map(course => (
                  <SelectItem key={course.Id} value={course.Title}>
                    {course.Title}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Reviews List */}
          <div className="space-y-6">
            {filteredReviews.length === 0 ? (
              <div className="text-center py-12">
                <MessageCircle className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No reviews found</h3>
                <p className="text-gray-500">No reviews match your current filters.</p>
              </div>
            ) : (
              filteredReviews.map((review) => (
                <div key={review.id} className="border rounded-lg p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <Avatar>
                        {review.studentAvatar ? (
                          <AvatarImage src={review.studentAvatar} />
                        ) : (
                          <AvatarFallback>
                            <User className="h-4 w-4" />
                          </AvatarFallback>
                        )}
                      </Avatar>
                      <div>
                        <div className="flex items-center gap-2">
                          <h4 className="font-medium">{review.studentName}</h4>
                          {review.verified && (
                            <Badge variant="secondary" className="text-xs">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Verified
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-2 mt-1">
                          <div className="flex">
                            {renderStars(review.rating)}
                          </div>
                          <span className="text-sm text-muted-foreground">
                            {review.date} • {review.courseName}
                          </span>
                        </div>
                      </div>
                    </div>
                    <Button variant="ghost" size="sm">
                      <Flag className="h-4 w-4" />
                    </Button>
                  </div>

                  <p className="text-gray-700 mb-4">{review.comment}</p>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <Button variant="ghost" size="sm">
                        <ThumbsUp className="h-4 w-4 mr-1" />
                        Helpful ({review.helpful})
                      </Button>
                      <Button variant="ghost" size="sm">
                        <MessageCircle className="h-4 w-4 mr-1" />
                        Reply
                      </Button>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ReviewsPage;