import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2 } from "lucide-react";
import {
  Plus,
  Search,
  Filter,
  BookOpen,
  Users,
  Star,
  DollarSign,
  Eye,
  Edit,
  Trash2,
  MoreHorizontal
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useCoursesByInstructor, useConnectedUser } from '../../hooks';
import { toast } from 'sonner';
import courseService from '../../services/course.service';

const InstructorCoursesPage: React.FC = () => {
  const navigate = useNavigate();
  const { authState } = useAuth();
  const { user } = authState;
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [activeTab, setActiveTab] = useState('all');
  const [isDeleting, setIsDeleting] = useState<number | null>(null);

  // Fetch real data using hooks
  const { data: connectedUser, loading: userLoading } = useConnectedUser();
  const { data: instructorCourses, loading: coursesLoading, error: coursesError, refetch } = useCoursesByInstructor(connectedUser?.Slug || null);

  // Transform API data to match component expectations
  const courses = instructorCourses?.map(course => ({
    id: course.Id || 0,
    title: course.Title || 'Untitled Course',
    status: course.Published ? 'published' : (course.Archived ? 'archived' : 'draft'),
    students: 0, // Will be calculated from enrollments when available
    rating: course.Ratings && course.Ratings.length > 0
      ? course.Ratings.reduce((sum: number, r: any) => sum + (r.Rating || 0), 0) / course.Ratings.length
      : 0,
    reviews: course.Ratings?.length || 0,
    revenue: 0, // Will be calculated from enrollments when available
    progress: course.Published ? 100 : (course.Archived ? 0 : 75), // Estimate based on status
    thumbnail: '/images/default-course.jpg', // Will be updated when photo relationship is available
    lastUpdated: course.UpdatedAt ? new Date(course.UpdatedAt).toLocaleDateString() : 'Unknown',
    slug: course.Slug || '',
    description: course.Title || '',
    price: course.Price || 0,
    category: 'General' // Will be updated when category relationship is available
  })) || [];

  // Handle course deletion
  const handleDeleteCourse = async (courseId: number) => {
    if (!window.confirm('Are you sure you want to delete this course? This action cannot be undone.')) {
      return;
    }

    setIsDeleting(courseId);
    try {
      // For now, we'll use a placeholder - this will be implemented when the API method is available
      toast.success('Course deletion requested - feature coming soon');
      // await courseService.deleteCourse(courseId);
      // refetch(); // Refresh the courses list
    } catch (error) {
      console.error('Error deleting course:', error);
      toast.error('Failed to delete course');
    } finally {
      setIsDeleting(null);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'published':
        return <Badge variant="default">Published</Badge>;
      case 'draft':
        return <Badge variant="secondary">Draft</Badge>;
      case 'review':
        return <Badge variant="outline">Under Review</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const filteredCourses = courses.filter(course => {
    const matchesSearch = course.title.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterStatus === 'all' || course.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  // Loading state
  if (userLoading || coursesLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-brand-red-primary" />
            <p className="text-muted-foreground">Loading your courses...</p>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (coursesError) {
    return (
      <div className="container mx-auto p-6">
        <Alert className="max-w-md mx-auto">
          <AlertDescription>
            Failed to load courses. Please try refreshing the page.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">My Courses</h1>
          <p className="text-muted-foreground">Manage and track your course content</p>
        </div>
        <Button onClick={() => navigate('/instructor/courses/create')}>
          <Plus className="w-4 h-4 mr-2" />
          Create New Course
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Courses</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{courses.length}</div>
            <p className="text-xs text-muted-foreground">
              {courses.filter(c => c.status === 'published').length} published
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Students</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {courses.reduce((sum, course) => sum + course.students, 0).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Across all courses
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Rating</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {(courses.filter(c => c.rating > 0).reduce((sum, course) => sum + course.rating, 0) / 
                courses.filter(c => c.rating > 0).length || 0).toFixed(1)}
            </div>
            <p className="text-xs text-muted-foreground">
              From {courses.reduce((sum, course) => sum + course.reviews, 0)} reviews
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${courses.reduce((sum, course) => sum + course.revenue, 0).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              All time earnings
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="Search courses..."
              value={searchTerm}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        <Select value={filterStatus} onValueChange={setFilterStatus}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Courses</SelectItem>
            <SelectItem value="published">Published</SelectItem>
            <SelectItem value="draft">Draft</SelectItem>
            <SelectItem value="review">Under Review</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Courses Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredCourses.map((course) => (
          <Card key={course.id} className="overflow-hidden">
            <div className="aspect-video bg-gray-200 relative">
              <div className="absolute top-2 right-2">
                {getStatusBadge(course.status)}
              </div>
              <div className="absolute bottom-2 left-2 right-2">
                <Progress value={course.progress} className="h-2" />
              </div>
            </div>
            <CardHeader>
              <CardTitle className="line-clamp-2">{course.title}</CardTitle>
              <div className="flex items-center justify-between text-sm text-muted-foreground">
                <span>Updated {course.lastUpdated}</span>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="w-4 h-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center">
                  <Users className="w-4 h-4 mr-2 text-muted-foreground" />
                  {course.students.toLocaleString()} students
                </div>
                <div className="flex items-center">
                  <Star className="w-4 h-4 mr-2 text-muted-foreground" />
                  {course.rating > 0 ? `${course.rating} (${course.reviews})` : 'No ratings'}
                </div>
                <div className="flex items-center">
                  <DollarSign className="w-4 h-4 mr-2 text-muted-foreground" />
                  ${course.revenue.toLocaleString()}
                </div>
                <div className="flex items-center">
                  <Eye className="w-4 h-4 mr-2 text-muted-foreground" />
                  {course.progress}% complete
                </div>
              </div>
              
              <div className="flex space-x-2">
                <Button
                  size="sm"
                  className="flex-1"
                  onClick={() => navigate(`/instructor/courses/${course.slug}/edit`)}
                >
                  <Edit className="w-4 h-4 mr-2" />
                  Edit
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => navigate(`/courses/${course.slug}`)}
                  title="View Course"
                >
                  <Eye className="w-4 h-4" />
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleDeleteCourse(course.id)}
                  disabled={isDeleting === course.id}
                  title="Delete Course"
                  className="text-red-600 hover:text-red-700"
                >
                  {isDeleting === course.id ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <Trash2 className="w-4 h-4" />
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredCourses.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <BookOpen className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No courses found</h3>
            <p className="text-muted-foreground mb-4">
              {searchTerm || filterStatus !== 'all' 
                ? 'Try adjusting your search or filter criteria'
                : 'Get started by creating your first course'
              }
            </p>
            {!searchTerm && filterStatus === 'all' && (
              <Button onClick={() => navigate('/instructor/courses/create')}>
                <Plus className="w-4 h-4 mr-2" />
                Create Your First Course
              </Button>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default InstructorCoursesPage;
