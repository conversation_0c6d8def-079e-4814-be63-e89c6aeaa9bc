/**
 * Marketing Tools Page for Instructors
 * Course promotion, coupons, and marketing analytics
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { 
  TrendingUp, 
  Target, 
  Share2, 
  Gift, 
  BarChart3, 
  Plus, 
  Copy, 
  Eye, 
  Edit,
  Users,
  DollarSign,
  Calendar,
  Percent
} from "lucide-react";
import SEOHelmet from '../../components/common/SEOHelmet';

interface Coupon {
  id: string;
  code: string;
  courseUsername: string;
  discountType: 'percentage' | 'fixed';
  discountValue: number;
  usageCount: number;
  usageLimit: number;
  expiryDate: Date;
  status: 'active' | 'expired' | 'disabled';
}

interface MarketingCampaign {
  id: string;
  name: string;
  type: 'social' | 'email' | 'affiliate' | 'referral';
  courseUsername: string;
  clicks: number;
  conversions: number;
  revenue: number;
  status: 'active' | 'paused' | 'completed';
}

const MarketingPage: React.FC = () => {
  const [coupons] = useState<Coupon[]>([
    {
      id: '1',
      code: 'REACT50',
      courseUsername: 'Complete React Development',
      discountType: 'percentage',
      discountValue: 50,
      usageCount: 23,
      usageLimit: 100,
      expiryDate: new Date('2025-08-01T23:59:59'),
      status: 'active'
    },
    {
      id: '2',
      code: 'NEWSTUDENT',
      courseUsername: 'JavaScript Mastery',
      discountType: 'fixed',
      discountValue: 25,
      usageCount: 45,
      usageLimit: 50,
      expiryDate: new Date('2025-07-15T23:59:59'),
      status: 'active'
    }
  ]);

  const [campaigns] = useState<MarketingCampaign[]>([
    {
      id: '1',
      name: 'Summer React Bootcamp',
      type: 'social',
      courseUsername: 'Complete React Development',
      clicks: 1250,
      conversions: 89,
      revenue: 4450,
      status: 'active'
    },
    {
      id: '2',
      name: 'JavaScript Email Series',
      type: 'email',
      courseUsername: 'JavaScript Mastery',
      clicks: 890,
      conversions: 67,
      revenue: 3350,
      status: 'active'
    }
  ]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500';
      case 'paused': return 'bg-yellow-500';
      case 'expired': return 'bg-red-500';
      case 'disabled': return 'bg-gray-500';
      case 'completed': return 'bg-blue-500';
      default: return 'bg-gray-500';
    }
  };

  const getCampaignIcon = (type: string) => {
    switch (type) {
      case 'social': return '📱';
      case 'email': return '📧';
      case 'affiliate': return '🤝';
      case 'referral': return '👥';
      default: return '📊';
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    }).format(date);
  };

  const calculateConversionRate = (conversions: number, clicks: number) => {
    return clicks > 0 ? ((conversions / clicks) * 100).toFixed(1) : '0.0';
  };

  return (
    <>
      <SEOHelmet 
        title="Marketing Tools - Instructor Dashboard"
        description="Promote your courses with coupons, campaigns, and marketing analytics"
      />

      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-nunito-bold text-gray-900">Marketing Tools</h1>
            <p className="text-gray-600 font-nunito-medium">Promote your courses and track performance</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" className="font-nunito-semibold">
              <Plus className="w-4 h-4 mr-2" />
              Create Campaign
            </Button>
            <Button className="bg-red-600 hover:bg-red-700 text-white font-nunito-semibold">
              <Gift className="w-4 h-4 mr-2" />
              Create Coupon
            </Button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Target className="w-5 h-5 text-blue-600" />
                <div>
                  <p className="text-sm text-gray-600">Active Campaigns</p>
                  <p className="text-2xl font-nunito-bold text-gray-900">
                    {campaigns.filter(c => c.status === 'active').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Gift className="w-5 h-5 text-green-600" />
                <div>
                  <p className="text-sm text-gray-600">Active Coupons</p>
                  <p className="text-2xl font-nunito-bold text-gray-900">
                    {coupons.filter(c => c.status === 'active').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <TrendingUp className="w-5 h-5 text-purple-600" />
                <div>
                  <p className="text-sm text-gray-600">Total Conversions</p>
                  <p className="text-2xl font-nunito-bold text-gray-900">
                    {campaigns.reduce((sum, c) => sum + c.conversions, 0)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <DollarSign className="w-5 h-5 text-orange-600" />
                <div>
                  <p className="text-sm text-gray-600">Marketing Revenue</p>
                  <p className="text-2xl font-nunito-bold text-gray-900">
                    ${campaigns.reduce((sum, c) => sum + c.revenue, 0).toLocaleString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Marketing Tabs */}
        <Tabs defaultValue="campaigns" className="space-y-4">
          <TabsList>
            <TabsTrigger value="campaigns">Campaigns</TabsTrigger>
            <TabsTrigger value="coupons">Coupons</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="tools">Promotion Tools</TabsTrigger>
          </TabsList>

          <TabsContent value="campaigns" className="space-y-4">
            {campaigns.map((campaign) => (
              <Card key={campaign.id}>
                <CardContent className="p-6">
                  <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <span className="text-2xl">{getCampaignIcon(campaign.type)}</span>
                        <h3 className="text-lg font-nunito-semibold text-gray-900">{campaign.name}</h3>
                        <Badge className={`${getStatusColor(campaign.status)} text-white`}>
                          {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}
                        </Badge>
                      </div>
                      <p className="text-gray-600 font-nunito-medium mb-3">{campaign.courseUsername}</p>
                      
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">Clicks:</span>
                          <p className="text-lg font-nunito-semibold text-gray-900">{campaign.clicks.toLocaleString()}</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Conversions:</span>
                          <p className="text-lg font-nunito-semibold text-gray-900">{campaign.conversions}</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Conversion Rate:</span>
                          <p className="text-lg font-nunito-semibold text-gray-900">
                            {calculateConversionRate(campaign.conversions, campaign.clicks)}%
                          </p>
                        </div>
                        <div>
                          <span className="text-gray-500">Revenue:</span>
                          <p className="text-lg font-nunito-semibold text-gray-900">${campaign.revenue.toLocaleString()}</p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        <Eye className="w-4 h-4 mr-2" />
                        View
                      </Button>
                      <Button variant="outline" size="sm">
                        <Edit className="w-4 h-4 mr-2" />
                        Edit
                      </Button>
                      <Button variant="outline" size="sm">
                        <BarChart3 className="w-4 h-4 mr-2" />
                        Analytics
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>

          <TabsContent value="coupons" className="space-y-4">
            {coupons.map((coupon) => (
              <Card key={coupon.id}>
                <CardContent className="p-6">
                  <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <Gift className="w-6 h-6 text-green-600" />
                        <h3 className="text-lg font-nunito-semibold text-gray-900 font-mono">{coupon.code}</h3>
                        <Badge className={`${getStatusColor(coupon.status)} text-white`}>
                          {coupon.status.charAt(0).toUpperCase() + coupon.status.slice(1)}
                        </Badge>
                      </div>
                      <p className="text-gray-600 font-nunito-medium mb-3">{coupon.courseUsername}</p>
                      
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                        <div>
                          <span className="text-sm text-gray-500">Discount:</span>
                          <p className="text-lg font-nunito-semibold text-gray-900">
                            {coupon.discountType === 'percentage' ? `${coupon.discountValue}%` : `$${coupon.discountValue}`}
                          </p>
                        </div>
                        <div>
                          <span className="text-sm text-gray-500">Usage:</span>
                          <p className="text-lg font-nunito-semibold text-gray-900">
                            {coupon.usageCount}/{coupon.usageLimit}
                          </p>
                        </div>
                        <div>
                          <span className="text-sm text-gray-500">Expires:</span>
                          <p className="text-lg font-nunito-semibold text-gray-900">{formatDate(coupon.expiryDate)}</p>
                        </div>
                        <div>
                          <span className="text-sm text-gray-500">Remaining:</span>
                          <p className="text-lg font-nunito-semibold text-gray-900">
                            {coupon.usageLimit - coupon.usageCount}
                          </p>
                        </div>
                      </div>

                      <div className="mb-3">
                        <div className="flex justify-between items-center mb-1">
                          <span className="text-sm text-gray-600">Usage Progress</span>
                          <span className="text-sm text-gray-500">
                            {Math.round((coupon.usageCount / coupon.usageLimit) * 100)}%
                          </span>
                        </div>
                        <Progress value={(coupon.usageCount / coupon.usageLimit) * 100} className="h-2" />
                      </div>
                    </div>
                    
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        <Copy className="w-4 h-4 mr-2" />
                        Copy Code
                      </Button>
                      <Button variant="outline" size="sm">
                        <Share2 className="w-4 h-4 mr-2" />
                        Share
                      </Button>
                      <Button variant="outline" size="sm">
                        <Edit className="w-4 h-4 mr-2" />
                        Edit
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>

          <TabsContent value="analytics">
            <Card>
              <CardContent className="p-6 text-center">
                <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-nunito-semibold text-gray-900 mb-2">Marketing Analytics</h3>
                <p className="text-gray-600">Detailed analytics and performance metrics coming soon.</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="tools">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <Card>
                <CardContent className="p-6 text-center">
                  <Share2 className="w-8 h-8 text-blue-600 mx-auto mb-3" />
                  <h3 className="font-nunito-semibold mb-2">Social Media Kit</h3>
                  <p className="text-sm text-gray-600 mb-4">Ready-to-use social media posts and graphics</p>
                  <Button variant="outline" size="sm">Download Kit</Button>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-6 text-center">
                  <Users className="w-8 h-8 text-green-600 mx-auto mb-3" />
                  <h3 className="font-nunito-semibold mb-2">Referral Program</h3>
                  <p className="text-sm text-gray-600 mb-4">Set up referral rewards for your students</p>
                  <Button variant="outline" size="sm">Setup Referrals</Button>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-6 text-center">
                  <Target className="w-8 h-8 text-purple-600 mx-auto mb-3" />
                  <h3 className="font-nunito-semibold mb-2">Landing Pages</h3>
                  <p className="text-sm text-gray-600 mb-4">Create custom landing pages for your courses</p>
                  <Button variant="outline" size="sm">Create Page</Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
};

export default MarketingPage;
