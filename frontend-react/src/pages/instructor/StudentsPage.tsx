import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Loader2 } from "lucide-react";
import {
  Users,
  Search,
  MoreHorizontal,
  MessageCircle,
  Eye,
  Trophy,
  CheckCircle,
  AlertCircle,
  User,
  Mail
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useConnectedUser, useCoursesByInstructor, useUsersByRole } from '../../hooks';
import { toast } from 'sonner';

const StudentsPage: React.FC = () => {
  const { authState } = useAuth();
  const { user } = authState;
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedStudent, setSelectedStudent] = useState<any>(null);
  const [messageModalOpen, setMessageModalOpen] = useState(false);
  const [messageSubject, setMessageSubject] = useState('');
  const [messageContent, setMessageContent] = useState('');

  // Fetch real data using hooks
  const { data: connectedUser, loading: userLoading } = useConnectedUser();
  const { data: instructorCourses, loading: coursesLoading } = useCoursesByInstructor(connectedUser?.Slug || null);
  const { data: allUsers, loading: usersLoading } = useUsersByRole('user');

  // Transform API data to get students enrolled in instructor's courses
  const studentsData = React.useMemo(() => {
    if (!instructorCourses || !allUsers) return { students: [], overview: { totalStudents: 0, activeStudents: 0, completedCourses: 0, avgProgress: 0 } };

    // For now, we'll show all users as potential students since enrollment data isn't available in current Course model
    const students = allUsers.slice(0, 20).map((user, index) => ({
      id: user.Id || index,
      name: `${user.Firstname || ''} ${user.Lastname || ''}`.trim() || user.Username || 'Unknown User',
      email: user.Email || 'No email',
      avatar: user.Photo?.Hashname ? `http://localhost:3200/${user.Photo.Hashname}` : null,
      enrolledCourses: Math.floor(Math.random() * 3) + 1, // Simulated until enrollment data available
      completedCourses: Math.floor(Math.random() * 2),
      totalProgress: Math.floor(Math.random() * 100),
      lastActive: new Date(user.UpdatedAt || user.CreatedAt || Date.now()).toLocaleDateString(),
      status: Math.random() > 0.3 ? 'active' : 'inactive',
      joinDate: new Date(user.CreatedAt || Date.now()).toLocaleDateString()
    }));

    const overview = {
      totalStudents: students.length,
      activeStudents: students.filter(s => s.status === 'active').length,
      completedCourses: students.reduce((sum, s) => sum + s.completedCourses, 0),
      avgProgress: students.length > 0
        ? Math.round(students.reduce((sum, s) => sum + s.totalProgress, 0) / students.length)
        : 0
    };

    return { students, overview };
  }, [instructorCourses, allUsers]);

  // Filter students based on search and status
  const filteredStudents = studentsData.students.filter(student => {
    const matchesSearch = student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || student.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { variant: 'default' as const, className: 'bg-green-100 text-green-800' },
      inactive: { variant: 'secondary' as const, className: 'bg-gray-100 text-gray-800' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.active;
    return <Badge variant={config.variant} className={config.className}>{status}</Badge>;
  };

  const handleStudentAction = (action: string, student: any) => {
    switch (action) {
      case 'view':
        toast.success(`Viewing ${student.name}'s profile`);
        break;
      case 'message':
        setSelectedStudent(student);
        setMessageModalOpen(true);
        break;
      case 'progress':
        toast.success(`Viewing ${student.name}'s progress`);
        break;
      default:
        break;
    }
  };

  const handleSendMessage = () => {
    if (!messageSubject.trim() || !messageContent.trim()) {
      toast.error('Please fill in both subject and message');
      return;
    }

    toast.success(`Message sent to ${selectedStudent?.name}`);
    setMessageModalOpen(false);
    setMessageSubject('');
    setMessageContent('');
    setSelectedStudent(null);
  };

  // Loading state
  if (userLoading || coursesLoading || usersLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-brand-red-primary" />
            <p className="text-muted-foreground">Loading students data...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Students Management</h1>
          <p className="text-muted-foreground">Manage and track your students' progress</p>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Total Students</p>
                <p className="text-2xl font-bold text-gray-900">
                  {studentsData.overview.totalStudents.toLocaleString()}
                </p>
              </div>
              <Users className="w-8 h-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Active Students</p>
                <p className="text-2xl font-bold text-gray-900">
                  {studentsData.overview.activeStudents.toLocaleString()}
                </p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Course Completions</p>
                <p className="text-2xl font-bold text-gray-900">
                  {studentsData.overview.completedCourses.toLocaleString()}
                </p>
              </div>
              <Trophy className="w-8 h-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Avg. Progress</p>
                <p className="text-2xl font-bold text-gray-900">
                  {studentsData.overview.avgProgress}%
                </p>
              </div>
              <AlertCircle className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Students Table */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Students List</CardTitle>
            <div className="flex gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search students..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-64"
                />
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="All Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Student</TableHead>
                <TableHead>Courses</TableHead>
                <TableHead>Progress</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Last Active</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredStudents.map((student) => (
                <TableRow key={student.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar>
                        {student.avatar ? (
                          <AvatarImage src={student.avatar} />
                        ) : (
                          <AvatarFallback>
                            <User className="h-4 w-4" />
                          </AvatarFallback>
                        )}
                      </Avatar>
                      <div>
                        <p className="font-medium">{student.name}</p>
                        <p className="text-sm text-muted-foreground">{student.email}</p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <p className="text-sm">{student.enrolledCourses} enrolled</p>
                      <p className="text-sm text-muted-foreground">{student.completedCourses} completed</p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-2">
                      <Progress value={student.totalProgress} className="w-20" />
                      <p className="text-sm text-muted-foreground">{student.totalProgress}%</p>
                    </div>
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(student.status)}
                  </TableCell>
                  <TableCell>
                    <p className="text-sm text-muted-foreground">{student.lastActive}</p>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleStudentAction('view', student)}>
                          <Eye className="h-4 w-4 mr-2" />
                          View Profile
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleStudentAction('message', student)}>
                          <MessageCircle className="h-4 w-4 mr-2" />
                          Send Message
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleStudentAction('progress', student)}>
                          <Trophy className="h-4 w-4 mr-2" />
                          View Progress
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Message Dialog */}
      <Dialog open={messageModalOpen} onOpenChange={setMessageModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Send Message to {selectedStudent?.name}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Subject</label>
              <Input
                placeholder="Enter message subject"
                value={messageSubject}
                onChange={(e) => setMessageSubject(e.target.value)}
              />
            </div>
            <div>
              <label className="text-sm font-medium">Message</label>
              <Textarea
                placeholder="Type your message here..."
                rows={4}
                value={messageContent}
                onChange={(e) => setMessageContent(e.target.value)}
              />
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setMessageModalOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleSendMessage}>
                <Mail className="h-4 w-4 mr-2" />
                Send Message
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default StudentsPage;