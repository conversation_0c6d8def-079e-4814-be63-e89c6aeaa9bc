import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  DollarSign, 
  TrendingUp, 
  Calendar,
  Download,
  CreditCard,
  PieChart,
  BarChart3,
  Users,
  BookOpen,
  Eye,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react';

const InstructorEarningsPage: React.FC = () => {
  const [timeRange, setTimeRange] = useState('30d');
  const [selectedCourse, setSelectedCourse] = useState('all');

  // Mock earnings data
  const earningsOverview = {
    totalEarnings: 45678.50,
    thisMonth: 5234.75,
    lastMonth: 4892.30,
    pendingPayouts: 1234.50,
    availableBalance: 3456.78,
    nextPayoutDate: '2024-02-15'
  };

  const monthlyEarnings = [
    { month: 'Jan', earnings: 4500, students: 120, courses: 3 },
    { month: 'Feb', earnings: 5200, students: 145, courses: 3 },
    { month: 'Mar', earnings: 4800, students: 132, courses: 4 },
    { month: 'Apr', earnings: 6100, students: 178, courses: 4 },
    { month: 'May', earnings: 5900, students: 165, courses: 5 },
    { month: 'Jun', earnings: 7200, students: 201, courses: 5 }
  ];

  const courseEarnings = [
    { 
      title: 'Advanced React Development', 
      earnings: 15678.50, 
      students: 1234, 
      avgPrice: 49.99,
      growth: 12.5
    },
    { 
      title: 'JavaScript Fundamentals', 
      earnings: 12456.30, 
      students: 987, 
      avgPrice: 39.99,
      growth: 8.3
    },
    { 
      title: 'Node.js Backend Development', 
      earnings: 9876.20, 
      students: 756, 
      avgPrice: 59.99,
      growth: -2.1
    },
    { 
      title: 'TypeScript Mastery', 
      earnings: 7654.10, 
      students: 543, 
      avgPrice: 44.99,
      growth: 15.7
    }
  ];

  const payoutHistory = [
    { date: '2024-01-15', amount: 4892.30, status: 'completed', method: 'Bank Transfer' },
    { date: '2023-12-15', amount: 5123.45, status: 'completed', method: 'PayPal' },
    { date: '2023-11-15', amount: 4567.89, status: 'completed', method: 'Bank Transfer' },
    { date: '2023-10-15', amount: 3987.65, status: 'completed', method: 'PayPal' }
  ];

  const getGrowthIcon = (growth: number) => {
    return growth >= 0 ? (
      <ArrowUpRight className="w-4 h-4 text-green-500" />
    ) : (
      <ArrowDownRight className="w-4 h-4 text-red-500" />
    );
  };

  const getGrowthColor = (growth: number) => {
    return growth >= 0 ? 'text-green-500' : 'text-red-500';
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Earnings Dashboard</h1>
          <p className="text-muted-foreground">Track your course revenue and payouts</p>
        </div>
        <div className="flex space-x-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Earnings Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Earnings</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${earningsOverview.totalEarnings.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              All time revenue
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">This Month</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${earningsOverview.thisMonth.toLocaleString()}</div>
            <p className="text-xs text-green-600">
              +7% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Available Balance</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${earningsOverview.availableBalance.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Ready for payout
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Next Payout</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{earningsOverview.nextPayoutDate}</div>
            <p className="text-xs text-muted-foreground">
              ${earningsOverview.pendingPayouts.toLocaleString()} pending
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="courses">Course Breakdown</TabsTrigger>
          <TabsTrigger value="payouts">Payout History</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Monthly Earnings Trend</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {monthlyEarnings.map((data, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm font-medium">{data.month}</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-24 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-primary h-2 rounded-full" 
                            style={{ width: `${(data.earnings / 8000) * 100}%` }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium">${data.earnings.toLocaleString()}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Revenue Sources</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Course Sales</span>
                    <span>85%</span>
                  </div>
                  <Progress value={85} />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Subscriptions</span>
                    <span>12%</span>
                  </div>
                  <Progress value={12} />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Affiliate</span>
                    <span>3%</span>
                  </div>
                  <Progress value={3} />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="courses" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Course Performance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {courseEarnings.map((course, index) => (
                  <div key={index} className="p-4 border rounded-lg">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h4 className="font-medium">{course.title}</h4>
                        <p className="text-sm text-muted-foreground">
                          {course.students} students • ${course.avgPrice} avg price
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-lg">${course.earnings.toLocaleString()}</p>
                        <div className={`flex items-center text-sm ${getGrowthColor(course.growth)}`}>
                          {getGrowthIcon(course.growth)}
                          <span className="ml-1">{Math.abs(course.growth)}%</span>
                        </div>
                      </div>
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <p className="text-muted-foreground">Revenue</p>
                        <p className="font-medium">${course.earnings.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Students</p>
                        <p className="font-medium">{course.students.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Avg Price</p>
                        <p className="font-medium">${course.avgPrice}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="payouts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Payout History
                <Button variant="outline" size="sm">
                  <CreditCard className="w-4 h-4 mr-2" />
                  Request Payout
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {payoutHistory.map((payout, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                        <DollarSign className="w-5 h-5 text-primary" />
                      </div>
                      <div>
                        <p className="font-medium">${payout.amount.toLocaleString()}</p>
                        <p className="text-sm text-muted-foreground">{payout.date} • {payout.method}</p>
                      </div>
                    </div>
                    <Badge variant={payout.status === 'completed' ? 'default' : 'secondary'}>
                      {payout.status}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Performance Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Conversion Rate</span>
                  <span className="font-medium">3.2%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Avg Revenue per Student</span>
                  <span className="font-medium">$47.50</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Refund Rate</span>
                  <span className="font-medium">2.1%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Course Completion Rate</span>
                  <span className="font-medium">78%</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Growth Insights</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                  <p className="text-sm font-medium text-green-800">Best Performing Course</p>
                  <p className="text-sm text-green-600">TypeScript Mastery (+15.7% growth)</p>
                </div>
                <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <p className="text-sm font-medium text-blue-800">Highest Revenue</p>
                  <p className="text-sm text-blue-600">Advanced React Development</p>
                </div>
                <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <p className="text-sm font-medium text-yellow-800">Needs Attention</p>
                  <p className="text-sm text-yellow-600">Node.js Backend (-2.1% growth)</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default InstructorEarningsPage;
