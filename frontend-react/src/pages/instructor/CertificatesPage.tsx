/**
 * Certificates Page for Instructors
 * Manage course certificates and completion tracking
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Award, Download, Eye, Settings, Users, TrendingUp, Plus, FileText } from "lucide-react";
import SEOHelmet from '../../components/common/SEOHelmet';

interface Certificate {
  id: string;
  courseUsername: string;
  templateUsername: string;
  totalIssued: number;
  completionRate: number;
  status: 'active' | 'draft' | 'disabled';
  lastIssued: Date;
  averageCompletionTime: number; // in days
}

interface CertificateRequest {
  id: string;
  studentUsername: string;
  courseUsername: string;
  completedDate: Date;
  status: 'pending' | 'issued' | 'rejected';
  completionScore: number;
}

const CertificatesPage: React.FC = () => {
  const [certificates] = useState<Certificate[]>([
    {
      id: '1',
      courseUsername: 'Complete React Development',
      templateUsername: 'Professional Certificate',
      totalIssued: 156,
      completionRate: 78,
      status: 'active',
      lastIssued: new Date('2025-07-05T10:30:00'),
      averageCompletionTime: 45
    },
    {
      id: '2',
      courseUsername: 'JavaScript Mastery',
      templateUsername: 'Mastery Certificate',
      totalIssued: 89,
      completionRate: 65,
      status: 'active',
      lastIssued: new Date('2025-07-04T15:20:00'),
      averageCompletionTime: 38
    },
    {
      id: '3',
      courseUsername: 'Advanced Node.js',
      templateUsername: 'Expert Certificate',
      totalIssued: 0,
      completionRate: 0,
      status: 'draft',
      lastIssued: new Date(),
      averageCompletionTime: 0
    }
  ]);

  const [pendingRequests] = useState<CertificateRequest[]>([
    {
      id: '1',
      studentUsername: 'John Smith',
      courseUsername: 'Complete React Development',
      completedDate: new Date('2025-07-03T14:30:00'),
      status: 'pending',
      completionScore: 92
    },
    {
      id: '2',
      studentUsername: 'Sarah Johnson',
      courseUsername: 'JavaScript Mastery',
      completedDate: new Date('2025-07-02T16:45:00'),
      status: 'pending',
      completionScore: 88
    }
  ]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500';
      case 'draft': return 'bg-yellow-500';
      case 'disabled': return 'bg-gray-500';
      case 'pending': return 'bg-orange-500';
      case 'issued': return 'bg-blue-500';
      case 'rejected': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    }).format(date);
  };

  return (
    <>
      <SEOHelmet 
        title="Certificates - Instructor Dashboard"
        description="Manage course certificates and completion tracking"
      />

      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-nunito-bold text-gray-900">Certificates</h1>
            <p className="text-gray-600 font-nunito-medium">Manage course completion certificates</p>
          </div>
          <Button className="bg-red-600 hover:bg-red-700 text-white font-nunito-semibold">
            <Plus className="w-4 h-4 mr-2" />
            Create Template
          </Button>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Award className="w-5 h-5 text-yellow-600" />
                <div>
                  <p className="text-sm text-gray-600">Total Issued</p>
                  <p className="text-2xl font-nunito-bold text-gray-900">
                    {certificates.reduce((sum, c) => sum + c.totalIssued, 0)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <FileText className="w-5 h-5 text-blue-600" />
                <div>
                  <p className="text-sm text-gray-600">Pending Requests</p>
                  <p className="text-2xl font-nunito-bold text-gray-900">{pendingRequests.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <TrendingUp className="w-5 h-5 text-green-600" />
                <div>
                  <p className="text-sm text-gray-600">Avg Completion Rate</p>
                  <p className="text-2xl font-nunito-bold text-gray-900">
                    {Math.round(certificates.reduce((sum, c) => sum + c.completionRate, 0) / certificates.length)}%
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Users className="w-5 h-5 text-purple-600" />
                <div>
                  <p className="text-sm text-gray-600">Active Templates</p>
                  <p className="text-2xl font-nunito-bold text-gray-900">
                    {certificates.filter(c => c.status === 'active').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Certificates Tabs */}
        <Tabs defaultValue="templates" className="space-y-4">
          <TabsList>
            <TabsTrigger value="templates">Certificate Templates</TabsTrigger>
            <TabsTrigger value="requests">Pending Requests ({pendingRequests.length})</TabsTrigger>
            <TabsTrigger value="issued">Issued Certificates</TabsTrigger>
          </TabsList>

          <TabsContent value="templates" className="space-y-4">
            {certificates.map((certificate) => (
              <Card key={certificate.id}>
                <CardContent className="p-6">
                  <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <Award className="w-6 h-6 text-yellow-600" />
                        <h3 className="text-lg font-nunito-semibold text-gray-900">{certificate.courseUsername}</h3>
                        <Badge className={`${getStatusColor(certificate.status)} text-white`}>
                          {certificate.status.charAt(0).toUpperCase() + certificate.status.slice(1)}
                        </Badge>
                      </div>
                      <p className="text-gray-600 font-nunito-medium mb-3">{certificate.templateUsername}</p>
                      
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm text-gray-500">
                        <div>
                          <span className="font-medium">Total Issued:</span>
                          <p className="text-lg font-nunito-semibold text-gray-900">{certificate.totalIssued}</p>
                        </div>
                        <div>
                          <span className="font-medium">Completion Rate:</span>
                          <p className="text-lg font-nunito-semibold text-gray-900">{certificate.completionRate}%</p>
                        </div>
                        <div>
                          <span className="font-medium">Avg Time:</span>
                          <p className="text-lg font-nunito-semibold text-gray-900">{certificate.averageCompletionTime} days</p>
                        </div>
                        <div>
                          <span className="font-medium">Last Issued:</span>
                          <p className="text-lg font-nunito-semibold text-gray-900">{formatDate(certificate.lastIssued)}</p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        <Eye className="w-4 h-4 mr-2" />
                        Preview
                      </Button>
                      <Button variant="outline" size="sm">
                        <Settings className="w-4 h-4 mr-2" />
                        Edit
                      </Button>
                      <Button variant="outline" size="sm">
                        <Download className="w-4 h-4 mr-2" />
                        Export
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>

          <TabsContent value="requests" className="space-y-4">
            {pendingRequests.map((request) => (
              <Card key={request.id}>
                <CardContent className="p-6">
                  <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                          <Users className="w-5 h-5 text-gray-600" />
                        </div>
                        <div>
                          <h3 className="text-lg font-nunito-semibold text-gray-900">{request.studentUsername}</h3>
                          <p className="text-gray-600 font-nunito-medium">{request.courseUsername}</p>
                        </div>
                        <Badge className={`${getStatusColor(request.status)} text-white`}>
                          {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
                        </Badge>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-500">
                        <div>
                          <span className="font-medium">Completed:</span>
                          <p className="text-gray-900">{formatDate(request.completedDate)}</p>
                        </div>
                        <div>
                          <span className="font-medium">Final Score:</span>
                          <p className="text-gray-900">{request.completionScore}%</p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        <Eye className="w-4 h-4 mr-2" />
                        Review
                      </Button>
                      <Button className="bg-green-600 hover:bg-green-700 text-white" size="sm">
                        Issue Certificate
                      </Button>
                      <Button variant="destructive" size="sm">
                        Reject
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>

          <TabsContent value="issued">
            <Card>
              <CardContent className="p-6 text-center">
                <Award className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-nunito-semibold text-gray-900 mb-2">Issued Certificates</h3>
                <p className="text-gray-600">View and manage all issued certificates here.</p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
};

export default CertificatesPage;
