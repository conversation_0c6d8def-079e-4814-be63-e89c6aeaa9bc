import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Loader2, ArrowLeft } from 'lucide-react';
import { Button } from '../../components/ui/button';
import { Alert, AlertDescription } from '../../components/ui/alert';
import CoursePageEditor from '../../components/courses/CoursePageEditor';
import { useCourseBySlug, useCourseMutations } from '../../hooks';
import { useToast } from '../../hooks/use-toast';
import type { Course } from '../../models';

const CoursePageEditPage: React.FC = () => {
  const { courseSlug } = useParams<{ courseSlug: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();

  // Fetch course data
  const { data: course, loading: courseLoading, error: courseError } = useCourseBySlug(courseSlug || '');
  
  // Course mutations
  const { updateCourse } = useCourseMutations();

  const handleSave = async (updatedCourse: Partial<Course>) => {
    if (!course) return;

    try {
      console.log('🔄 Updating course page content:', updatedCourse);
      
      const result = await updateCourse.execute({
        ...course,
        ...updatedCourse
      });

      if (result) {
        toast({
          title: "Success",
          description: "Course page content updated successfully"
        });
        
        // Navigate back to course management
        navigate('/instructor/courses');
      }
    } catch (error) {
      console.error('❌ Error updating course:', error);
      toast({
        title: "Error",
        description: "Failed to update course. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleCancel = () => {
    navigate('/instructor/courses');
  };

  // Loading state
  if (courseLoading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <div className="text-center space-y-4">
          <Loader2 className="w-8 h-8 animate-spin text-red-600 mx-auto" />
          <p className="text-gray-600">Loading course data...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (courseError || !course) {
    return (
      <div className="max-w-2xl mx-auto p-6">
        <Alert variant="destructive">
          <AlertDescription>
            {courseError || 'Course not found. Please check the URL and try again.'}
          </AlertDescription>
        </Alert>
        
        <div className="mt-6 text-center">
          <Button onClick={() => navigate('/instructor/courses')} variant="outline">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Courses
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumb Navigation */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => navigate('/instructor/courses')}
              className="p-0 h-auto font-normal text-gray-600 hover:text-gray-900"
            >
              My Courses
            </Button>
            <span>/</span>
            <span className="text-gray-900 font-medium">{course.Title}</span>
            <span>/</span>
            <span className="text-gray-900 font-medium">Edit Page Content</span>
          </div>
        </div>
      </div>

      {/* Course Page Editor */}
      <CoursePageEditor
        course={course}
        onSave={handleSave}
        onCancel={handleCancel}
        loading={updateCourse.loading}
      />
    </div>
  );
};

export default CoursePageEditPage;
