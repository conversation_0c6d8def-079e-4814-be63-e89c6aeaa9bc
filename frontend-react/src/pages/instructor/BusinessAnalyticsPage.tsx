import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Loader2 } from "lucide-react";
import {
  BarChart3,
  Line<PERSON>hart,
  PieChart,
  TrendingUp,
  User,
  DollarSign,
  Book,
  Eye,
  Download,
  Calendar,
  TrendingDown,
  Star,
  MessageCircle
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useConnectedUser, useCoursesByInstructor } from '../../hooks';
import { toast } from 'sonner';

// Chart placeholder component
const ChartPlaceholder = ({ height = 300, title }: { height?: number; title: string }) => (
  <div
    className="flex items-center justify-center border-2 border-dashed border-gray-300 rounded-lg bg-gray-50"
    style={{ height }}
  >
    <div className="text-center">
      <BarChart3 className="h-8 w-8 mx-auto mb-2 text-gray-400" />
      <p className="text-sm text-gray-500">{title} Chart</p>
      <p className="text-xs text-gray-400">Coming Soon</p>
    </div>
  </div>
);

const BusinessAnalyticsPage: React.FC = () => {
  const { authState } = useAuth();
  const { user } = authState;
  const [timeRange, setTimeRange] = useState('30d');
  const [activeTab, setActiveTab] = useState('overview');

  // Fetch real data using hooks
  const { data: connectedUser, loading: userLoading } = useConnectedUser();
  const { data: instructorCourses, loading: coursesLoading, error: coursesError } = useCoursesByInstructor(connectedUser?.Slug || null);

  // Calculate real business analytics from API data
  const businessData = React.useMemo(() => {
    if (!instructorCourses) return {
      overview: { totalRevenue: 0, totalStudents: 0, conversionRate: 0, customerLifetimeValue: 0, churnRate: 0, marketShare: 0 },
      revenueData: [],
      marketingData: [],
      competitorAnalysis: []
    };

    const totalCourses = instructorCourses.length;
    const totalStudents = 0; // Will be calculated when enrollment data is available
    const totalRevenue = 0; // Will be calculated when enrollment data is available

    const allRatings = instructorCourses.flatMap(course => course.Ratings || []);
    const avgRating = allRatings.length > 0
      ? allRatings.reduce((sum, rating) => sum + (rating.Rating || 0), 0) / allRatings.length
      : 0;

    return {
      overview: {
        totalRevenue,
        totalStudents,
        conversionRate: 0, // Will be calculated when conversion data is available
        customerLifetimeValue: 0, // Will be calculated when customer data is available
        churnRate: 0, // Will be calculated when retention data is available
        marketShare: 0 // Will be calculated when market data is available
      },
      revenueData: [],
      marketingData: [],
      competitorAnalysis: [
        { metric: 'Course Price', us: 0, competitor1: 0, competitor2: 0, industry: 0 },
        { metric: 'Student Rating', us: avgRating, competitor1: 0, competitor2: 0, industry: 0 },
        { metric: 'Course Length', us: 0, competitor1: 0, competitor2: 0, industry: 0 },
        { metric: 'Completion Rate', us: 0, competitor1: 0, competitor2: 0, industry: 0 }
      ]
    };
  }, [instructorCourses]);

  // Loading state
  if (userLoading || coursesLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-brand-red-primary" />
            <p className="text-muted-foreground">Loading business analytics...</p>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (coursesError) {
    return (
      <div className="container mx-auto p-6">
        <Alert className="max-w-md mx-auto">
          <AlertDescription>
            Failed to load business analytics data. Please try refreshing the page.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Business Analytics</h1>
          <p className="text-muted-foreground">Advanced business insights and performance metrics</p>
        </div>
        <div className="flex gap-4">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Total Revenue</p>
                <p className="text-2xl font-bold text-gray-900">
                  ${businessData.overview.totalRevenue.toLocaleString()}
                </p>
                <p className="text-sm text-green-600 flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +12.5%
                </p>
              </div>
              <DollarSign className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Total Students</p>
                <p className="text-2xl font-bold text-gray-900">
                  {businessData.overview.totalStudents.toLocaleString()}
                </p>
                <p className="text-sm text-green-600 flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +8.2%
                </p>
              </div>
              <User className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Conversion Rate</p>
                <p className="text-2xl font-bold text-gray-900">
                  {businessData.overview.conversionRate}%
                </p>
                <p className="text-sm text-red-600 flex items-center mt-1">
                  <TrendingDown className="h-3 w-3 mr-1" />
                  -2.1%
                </p>
              </div>
              <Eye className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Customer LTV</p>
                <p className="text-2xl font-bold text-gray-900">
                  ${businessData.overview.customerLifetimeValue}
                </p>
                <p className="text-sm text-green-600 flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +15.3%
                </p>
              </div>
              <Star className="w-8 h-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Churn Rate</p>
                <p className="text-2xl font-bold text-gray-900">
                  {businessData.overview.churnRate}%
                </p>
                <p className="text-sm text-green-600 flex items-center mt-1">
                  <TrendingDown className="h-3 w-3 mr-1" />
                  -1.8%
                </p>
              </div>
              <TrendingDown className="w-8 h-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Market Share</p>
                <p className="text-2xl font-bold text-gray-900">
                  {businessData.overview.marketShare}%
                </p>
                <p className="text-sm text-green-600 flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +0.5%
                </p>
              </div>
              <PieChart className="w-8 h-8 text-indigo-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="revenue">Revenue</TabsTrigger>
          <TabsTrigger value="marketing">Marketing</TabsTrigger>
          <TabsTrigger value="competition">Competition</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Revenue Trend</CardTitle>
              </CardHeader>
              <CardContent>
                <ChartPlaceholder title="Revenue Trend" />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Student Growth</CardTitle>
              </CardHeader>
              <CardContent>
                <ChartPlaceholder title="Student Growth" />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="revenue" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Revenue Analytics</CardTitle>
            </CardHeader>
            <CardContent>
              <ChartPlaceholder title="Revenue Analytics" height={400} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="marketing" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Marketing Sources</CardTitle>
            </CardHeader>
            <CardContent>
              <ChartPlaceholder title="Marketing Sources" height={400} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="competition" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Competitive Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Metric</TableHead>
                    <TableHead>You</TableHead>
                    <TableHead>Competitor 1</TableHead>
                    <TableHead>Competitor 2</TableHead>
                    <TableHead>Industry Avg</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {businessData.competitorAnalysis.map((item, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">{item.metric}</TableCell>
                      <TableCell>
                        <Badge variant="default">{item.us}</Badge>
                      </TableCell>
                      <TableCell>{item.competitor1}</TableCell>
                      <TableCell>{item.competitor2}</TableCell>
                      <TableCell>{item.industry}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default BusinessAnalyticsPage;