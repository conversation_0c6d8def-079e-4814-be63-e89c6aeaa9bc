import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Loader2 } from "lucide-react";
import {
  DollarSign,
  TrendingUp,
  Download,
  CreditCard,
  Calendar,
  BookOpen,
  TrendingDown,
  BarChart3
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useConnectedUser, useCoursesByInstructor } from '../../hooks';
import { toast } from 'sonner';

// Chart placeholder component
const ChartPlaceholder = ({ height = 300, title }: { height?: number; title: string }) => (
  <div
    className="flex items-center justify-center border-2 border-dashed border-gray-300 rounded-lg bg-gray-50"
    style={{ height }}
  >
    <div className="text-center">
      <BarChart3 className="h-8 w-8 mx-auto mb-2 text-gray-400" />
      <p className="text-sm text-gray-500">{title} Chart</p>
      <p className="text-xs text-gray-400">Coming Soon</p>
    </div>
  </div>
);

const EarningsPage: React.FC = () => {
  const { authState } = useAuth();
  const { user } = authState;
  const [timeRange, setTimeRange] = useState('6m');
  const [activeTab, setActiveTab] = useState('overview');

  // Fetch real data using hooks
  const { data: connectedUser, loading: userLoading } = useConnectedUser();
  const { data: instructorCourses, loading: coursesLoading, error: coursesError } = useCoursesByInstructor(connectedUser?.Slug || null);

  // Calculate real earnings data from API data
  const earningsData = React.useMemo(() => {
    if (!instructorCourses) return {
      overview: { totalEarnings: 0, thisMonth: 0, pendingPayouts: 0, nextPayout: '', growthRate: 0 },
      monthlyData: [],
      courseEarnings: [],
      payoutHistory: []
    };

    // Calculate course earnings (will be real when enrollment data is available)
    const courseEarnings = instructorCourses.map(course => ({
      id: course.Id || 0,
      courseName: course.Title,
      totalEarnings: 0, // Will be calculated from enrollments
      thisMonth: 0, // Will be calculated from recent enrollments
      students: 0, // Will be calculated from enrollments
      price: course.Price || 0,
      commission: 70 // Standard commission rate
    }));

    // Generate monthly data (simplified until real enrollment data is available)
    const monthlyData = [
      { month: 'Aug', earnings: 0, students: 0 },
      { month: 'Sep', earnings: 0, students: 0 },
      { month: 'Oct', earnings: 0, students: 0 },
      { month: 'Nov', earnings: 0, students: 0 },
      { month: 'Dec', earnings: 0, students: 0 },
      { month: 'Jan', earnings: 0, students: 0 }
    ];

    return {
      overview: {
        totalEarnings: 0, // Will be calculated from all course earnings
        thisMonth: 0, // Will be calculated from current month earnings
        pendingPayouts: 0, // Will be calculated from pending payments
        nextPayout: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toLocaleDateString(), // Next week
        growthRate: 0 // Will be calculated from month-over-month growth
      },
      monthlyData,
      courseEarnings,
      payoutHistory: [
        {
          id: 1,
          date: '2024-01-01',
          amount: 0, // Will be calculated from actual payouts
          status: 'completed',
          method: 'Bank Transfer'
        },
        {
          id: 2,
          date: '2023-12-01',
          amount: 0, // Will be calculated from actual payouts
          status: 'completed',
          method: 'PayPal'
        }
      ]
    };
  }, [instructorCourses]);

  // Loading state
  if (userLoading || coursesLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-brand-red-primary" />
            <p className="text-muted-foreground">Loading earnings data...</p>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (coursesError) {
    return (
      <div className="container mx-auto p-6">
        <Alert className="max-w-md mx-auto">
          <AlertDescription>
            Failed to load earnings data. Please try refreshing the page.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      completed: { variant: 'default' as const, className: 'bg-green-100 text-green-800' },
      pending: { variant: 'secondary' as const, className: 'bg-yellow-100 text-yellow-800' },
      failed: { variant: 'destructive' as const, className: 'bg-red-100 text-red-800' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    return <Badge variant={config.variant} className={config.className}>{status}</Badge>;
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Earnings</h1>
          <p className="text-muted-foreground">Track your course earnings and payouts</p>
        </div>
        <div className="flex gap-4">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1m">Last month</SelectItem>
              <SelectItem value="3m">Last 3 months</SelectItem>
              <SelectItem value="6m">Last 6 months</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Total Earnings</p>
                <p className="text-2xl font-bold text-gray-900">
                  ${earningsData.overview.totalEarnings.toLocaleString()}
                </p>
                <p className="text-sm text-green-600 flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +{earningsData.overview.growthRate}%
                </p>
              </div>
              <DollarSign className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">This Month</p>
                <p className="text-2xl font-bold text-gray-900">
                  ${earningsData.overview.thisMonth.toLocaleString()}
                </p>
                <p className="text-sm text-green-600 flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +12.5%
                </p>
              </div>
              <Calendar className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Pending Payouts</p>
                <p className="text-2xl font-bold text-gray-900">
                  ${earningsData.overview.pendingPayouts.toLocaleString()}
                </p>
                <p className="text-sm text-yellow-600 flex items-center mt-1">
                  <TrendingDown className="h-3 w-3 mr-1" />
                  Pending
                </p>
              </div>
              <CreditCard className="w-8 h-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Next Payout</p>
                <p className="text-lg font-bold text-gray-900">
                  {earningsData.overview.nextPayout}
                </p>
                <p className="text-sm text-blue-600 flex items-center mt-1">
                  <Calendar className="h-3 w-3 mr-1" />
                  Scheduled
                </p>
              </div>
              <Calendar className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Active Courses</p>
                <p className="text-2xl font-bold text-gray-900">
                  {earningsData.courseEarnings.length}
                </p>
                <p className="text-sm text-green-600 flex items-center mt-1">
                  <BookOpen className="h-3 w-3 mr-1" />
                  Earning
                </p>
              </div>
              <BookOpen className="w-8 h-8 text-indigo-600" />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default EarningsPage;