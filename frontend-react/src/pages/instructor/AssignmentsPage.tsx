/**
 * Assignments Page for Instructors
 * Manage course assignments, submissions, and grading
 */

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { FileText, Clock, Users, CheckCircle, AlertCircle, Plus, Eye, Download } from "lucide-react";
import SEOHelmet from '../../components/common/SEOHelmet';

interface Assignment {
  id: string;
  title: string;
  courseTitle: string;
  dueDate: Date;
  totalSubmissions: number;
  pendingGrading: number;
  averageScore: number;
  status: 'active' | 'draft' | 'closed';
  type: 'essay' | 'project' | 'quiz' | 'presentation';
}

const AssignmentsPage: React.FC = () => {
  const [assignments] = useState<Assignment[]>([
    {
      id: '1',
      title: 'Build a React Todo App',
      courseTitle: 'Complete React Development',
      dueDate: new Date('2025-07-15T23:59:00'),
      totalSubmissions: 42,
      pendingGrading: 8,
      averageScore: 87,
      status: 'active',
      type: 'project'
    },
    {
      id: '2',
      title: 'JavaScript Fundamentals Essay',
      courseTitle: 'JavaScript Mastery',
      dueDate: new Date('2025-07-12T23:59:00'),
      totalSubmissions: 35,
      pendingGrading: 12,
      averageScore: 82,
      status: 'active',
      type: 'essay'
    },
    {
      id: '3',
      title: 'API Integration Project',
      courseTitle: 'Complete React Development',
      dueDate: new Date('2025-07-20T23:59:00'),
      totalSubmissions: 0,
      pendingGrading: 0,
      averageScore: 0,
      status: 'draft',
      type: 'project'
    }
  ]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500';
      case 'draft': return 'bg-yellow-500';
      case 'closed': return 'bg-gray-500';
      default: return 'bg-gray-500';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'project': return '🚀';
      case 'essay': return '📝';
      case 'quiz': return '❓';
      case 'presentation': return '📊';
      default: return '📄';
    }
  };

  const formatDueDate = (date: Date) => {
    const now = new Date();
    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) return 'Overdue';
    if (diffDays === 0) return 'Due today';
    if (diffDays === 1) return 'Due tomorrow';
    return `Due in ${diffDays} days`;
  };

  return (
    <>
      <SEOHelmet 
        title="Assignments - Instructor Dashboard"
        description="Manage course assignments, submissions, and grading"
      />

      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-nunito-bold text-gray-900">Assignments</h1>
            <p className="text-gray-600 font-nunito-medium">Manage assignments and track student submissions</p>
          </div>
          <Button className="bg-red-600 hover:bg-red-700 text-white font-nunito-semibold">
            <Plus className="w-4 h-4 mr-2" />
            Create Assignment
          </Button>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <FileText className="w-5 h-5 text-blue-600" />
                <div>
                  <p className="text-sm text-gray-600">Total Assignments</p>
                  <p className="text-2xl font-nunito-bold text-gray-900">{assignments.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <AlertCircle className="w-5 h-5 text-orange-600" />
                <div>
                  <p className="text-sm text-gray-600">Pending Grading</p>
                  <p className="text-2xl font-nunito-bold text-gray-900">
                    {assignments.reduce((sum, a) => sum + a.pendingGrading, 0)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Users className="w-5 h-5 text-green-600" />
                <div>
                  <p className="text-sm text-gray-600">Total Submissions</p>
                  <p className="text-2xl font-nunito-bold text-gray-900">
                    {assignments.reduce((sum, a) => sum + a.totalSubmissions, 0)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5 text-purple-600" />
                <div>
                  <p className="text-sm text-gray-600">Average Score</p>
                  <p className="text-2xl font-nunito-bold text-gray-900">
                    {Math.round(assignments.reduce((sum, a) => sum + a.averageScore, 0) / assignments.length)}%
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Assignments Tabs */}
        <Tabs defaultValue="all" className="space-y-4">
          <TabsList>
            <TabsTrigger value="all">All Assignments</TabsTrigger>
            <TabsTrigger value="active">Active</TabsTrigger>
            <TabsTrigger value="draft">Drafts</TabsTrigger>
            <TabsTrigger value="grading">Need Grading</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-4">
            {assignments.map((assignment) => (
              <Card key={assignment.id}>
                <CardContent className="p-6">
                  <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <span className="text-2xl">{getTypeIcon(assignment.type)}</span>
                        <h3 className="text-lg font-nunito-semibold text-gray-900">{assignment.title}</h3>
                        <Badge className={`${getStatusColor(assignment.status)} text-white`}>
                          {assignment.status.charAt(0).toUpperCase() + assignment.status.slice(1)}
                        </Badge>
                      </div>
                      <p className="text-gray-600 font-nunito-medium mb-3">{assignment.courseTitle}</p>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                          <Clock className="w-4 h-4" />
                          <span>{formatDueDate(assignment.dueDate)}</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                          <Users className="w-4 h-4" />
                          <span>{assignment.totalSubmissions} submissions</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                          <CheckCircle className="w-4 h-4" />
                          <span>Avg: {assignment.averageScore}%</span>
                        </div>
                      </div>

                      {assignment.pendingGrading > 0 && (
                        <div className="mb-3">
                          <div className="flex justify-between items-center mb-1">
                            <span className="text-sm text-gray-600">Grading Progress</span>
                            <span className="text-sm text-gray-500">
                              {assignment.totalSubmissions - assignment.pendingGrading}/{assignment.totalSubmissions} graded
                            </span>
                          </div>
                          <Progress 
                            value={((assignment.totalSubmissions - assignment.pendingGrading) / assignment.totalSubmissions) * 100} 
                            className="h-2"
                          />
                        </div>
                      )}
                    </div>
                    
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        <Eye className="w-4 h-4 mr-2" />
                        View
                      </Button>
                      {assignment.pendingGrading > 0 && (
                        <Button className="bg-orange-600 hover:bg-orange-700 text-white" size="sm">
                          Grade ({assignment.pendingGrading})
                        </Button>
                      )}
                      <Button variant="outline" size="sm">
                        <Download className="w-4 h-4 mr-2" />
                        Export
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>

          <TabsContent value="active">
            <div className="space-y-4">
              {assignments.filter(a => a.status === 'active').map((assignment) => (
                <Card key={assignment.id}>
                  <CardContent className="p-4">
                    <h3 className="font-nunito-semibold">{assignment.title}</h3>
                    <p className="text-gray-600">{assignment.courseTitle}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="draft">
            <div className="space-y-4">
              {assignments.filter(a => a.status === 'draft').map((assignment) => (
                <Card key={assignment.id}>
                  <CardContent className="p-4">
                    <h3 className="font-nunito-semibold">{assignment.title}</h3>
                    <p className="text-gray-600">{assignment.courseTitle}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="grading">
            <div className="space-y-4">
              {assignments.filter(a => a.pendingGrading > 0).map((assignment) => (
                <Card key={assignment.id}>
                  <CardContent className="p-4">
                    <h3 className="font-nunito-semibold">{assignment.title}</h3>
                    <p className="text-gray-600">{assignment.pendingGrading} submissions need grading</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
};

export default AssignmentsPage;
