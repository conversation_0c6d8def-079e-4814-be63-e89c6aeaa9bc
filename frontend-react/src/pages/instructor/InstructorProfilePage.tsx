import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardH<PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { 
  User, 
  Mail, 
  Phone,
  MapPin,
  Globe,
  Calendar,
  Star,
  BookOpen,
  Users,
  Award,
  Edit,
  Camera,
  Save,
  X
} from 'lucide-react';

const InstructorProfilePage: React.FC = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [profileData, setProfileData] = useState({
    firstUsername: '<PERSON>',
    lastUsername: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    location: 'San Francisco, CA',
    website: 'https://johnsmith.dev',
    bio: 'Experienced software developer with 10+ years in web development. Passionate about teaching and helping others learn programming.',
    expertise: ['React', 'JavaScript', 'Node.js', 'TypeScript', 'Python'],
    experience: '10+ years',
    education: 'Computer Science, Stanford University',
    languages: ['English', 'Spanish', 'French']
  });

  // Mock instructor stats
  const instructorStats = {
    totalStudents: 15420,
    totalCourses: 12,
    averageRating: 4.8,
    totalReviews: 2341,
    totalRevenue: 125000,
    joinDate: '2020-03-15'
  };

  const achievements = [
    { title: 'Top Instructor', description: 'Ranked in top 10% of instructors', icon: '🏆' },
    { title: 'Student Favorite', description: 'Highly rated by students', icon: '⭐' },
    { title: 'Course Creator', description: 'Created 10+ courses', icon: '📚' },
    { title: 'Expert Educator', description: '5+ years teaching experience', icon: '🎓' }
  ];

  const recentCourses = [
    { title: 'Advanced React Development', students: 3420, rating: 4.9 },
    { title: 'JavaScript Fundamentals', students: 5680, rating: 4.7 },
    { title: 'Node.js Backend Development', students: 2340, rating: 4.8 }
  ];

  const handleSave = () => {
    // Save profile data logic here
    setIsEditing(false);
  };

  const handleCancel = () => {
    // Reset form data logic here
    setIsEditing(false);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Instructor Profile</h1>
          <p className="text-muted-foreground">Manage your instructor profile and information</p>
        </div>
        {!isEditing ? (
          <Button onClick={() => setIsEditing(true)}>
            <Edit className="w-4 h-4 mr-2" />
            Edit Profile
          </Button>
        ) : (
          <div className="flex space-x-2">
            <Button onClick={handleSave}>
              <Save className="w-4 h-4 mr-2" />
              Save Changes
            </Button>
            <Button variant="outline" onClick={handleCancel}>
              <X className="w-4 h-4 mr-2" />
              Cancel
            </Button>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Profile Overview */}
        <div className="lg:col-span-1 space-y-6">
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col items-center space-y-4">
                <div className="relative">
                  <Avatar className="w-24 h-24">
                    <AvatarImage src="/api/placeholder/150/150" />
                    <AvatarFallback className="text-lg">
                      {profileData.firstUsername[0]}{profileData.lastUsername[0]}
                    </AvatarFallback>
                  </Avatar>
                  {isEditing && (
                    <Button size="sm" className="absolute -bottom-2 -right-2 rounded-full w-8 h-8 p-0">
                      <Camera className="w-4 h-4" />
                    </Button>
                  )}
                </div>
                <div className="text-center">
                  <h3 className="text-xl font-semibold">
                    {profileData.firstUsername} {profileData.lastUsername}
                  </h3>
                  <p className="text-muted-foreground">Senior Instructor</p>
                  <div className="flex items-center justify-center mt-2">
                    <Star className="w-4 h-4 text-yellow-500 mr-1" />
                    <span className="font-medium">{instructorStats.averageRating}</span>
                    <span className="text-muted-foreground ml-1">
                      ({instructorStats.totalReviews} reviews)
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Stats</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Users className="w-4 h-4 text-muted-foreground mr-2" />
                  <span className="text-sm">Total Students</span>
                </div>
                <span className="font-medium">{instructorStats.totalStudents.toLocaleString()}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <BookOpen className="w-4 h-4 text-muted-foreground mr-2" />
                  <span className="text-sm">Courses Created</span>
                </div>
                <span className="font-medium">{instructorStats.totalCourses}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Calendar className="w-4 h-4 text-muted-foreground mr-2" />
                  <span className="text-sm">Member Since</span>
                </div>
                <span className="font-medium">March 2020</span>
              </div>
            </CardContent>
          </Card>

          {/* Achievements */}
          <Card>
            <CardHeader>
              <CardTitle>Achievements</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-3">
                {achievements.map((achievement, index) => (
                  <div key={index} className="text-center p-3 border rounded-lg">
                    <div className="text-2xl mb-1">{achievement.icon}</div>
                    <h4 className="font-medium text-xs">{achievement.title}</h4>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="lg:col-span-2">
          <Tabs defaultValue="profile" className="space-y-4">
            <TabsList>
              <TabsTrigger value="profile">Profile Information</TabsTrigger>
              <TabsTrigger value="courses">My Courses</TabsTrigger>
              <TabsTrigger value="reviews">Reviews</TabsTrigger>
            </TabsList>

            <TabsContent value="profile" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Personal Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="firstName">First Name</Label>
                      {isEditing ? (
                        <Input
                          id="firstName"
                          value={profileData.firstUsername}
                          onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => setProfileData({...profileData, firstUsername: e.target.value})}
                        />
                      ) : (
                        <p className="text-sm">{profileData.firstUsername}</p>
                      )}
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="lastName">Last Name</Label>
                      {isEditing ? (
                        <Input
                          id="lastName"
                          value={profileData.lastUsername}
                          onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => setProfileData({...profileData, lastUsername: e.target.value})}
                        />
                      ) : (
                        <p className="text-sm">{profileData.lastUsername}</p>
                      )}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <div className="flex items-center">
                      <Mail className="w-4 h-4 text-muted-foreground mr-2" />
                      {isEditing ? (
                        <Input
                          id="email"
                          type="email"
                          value={profileData.email}
                          onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => setProfileData({...profileData, email: e.target.value})}
                          className="flex-1"
                        />
                      ) : (
                        <span className="text-sm">{profileData.email}</span>
                      )}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone</Label>
                    <div className="flex items-center">
                      <Phone className="w-4 h-4 text-muted-foreground mr-2" />
                      {isEditing ? (
                        <Input
                          id="phone"
                          value={profileData.phone}
                          onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => setProfileData({...profileData, phone: e.target.value})}
                          className="flex-1"
                        />
                      ) : (
                        <span className="text-sm">{profileData.phone}</span>
                      )}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="location">Location</Label>
                    <div className="flex items-center">
                      <MapPin className="w-4 h-4 text-muted-foreground mr-2" />
                      {isEditing ? (
                        <Input
                          id="location"
                          value={profileData.location}
                          onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => setProfileData({...profileData, location: e.target.value})}
                          className="flex-1"
                        />
                      ) : (
                        <span className="text-sm">{profileData.location}</span>
                      )}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="website">Website</Label>
                    <div className="flex items-center">
                      <Globe className="w-4 h-4 text-muted-foreground mr-2" />
                      {isEditing ? (
                        <Input
                          id="website"
                          value={profileData.website}
                          onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => setProfileData({...profileData, website: e.target.value})}
                          className="flex-1"
                        />
                      ) : (
                        <a href={profileData.website} className="text-sm text-blue-600 hover:underline">
                          {profileData.website}
                        </a>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Professional Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="bio">Bio</Label>
                    {isEditing ? (
                      <Textarea
                        id="bio"
                        value={profileData.bio}
                        onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => setProfileData({...profileData, bio: e.target.value})}
                        rows={4}
                      />
                    ) : (
                      <p className="text-sm">{profileData.bio}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label>Areas of Expertise</Label>
                    <div className="flex flex-wrap gap-2">
                      {profileData.expertise.map((skill, index) => (
                        <Badge key={index} variant="secondary">{skill}</Badge>
                      ))}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Experience</Label>
                      <p className="text-sm">{profileData.experience}</p>
                    </div>
                    <div className="space-y-2">
                      <Label>Education</Label>
                      <p className="text-sm">{profileData.education}</p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Languages</Label>
                    <div className="flex flex-wrap gap-2">
                      {profileData.languages.map((language, index) => (
                        <Badge key={index} variant="outline">{language}</Badge>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="courses" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Recent Courses</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {recentCourses.map((course, index) => (
                      <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                        <div>
                          <h4 className="font-medium">{course.title}</h4>
                          <p className="text-sm text-muted-foreground">
                            {course.students.toLocaleString()} students enrolled
                          </p>
                        </div>
                        <div className="flex items-center">
                          <Star className="w-4 h-4 text-yellow-500 mr-1" />
                          <span className="font-medium">{course.rating}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="reviews" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Recent Reviews</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">Recent student reviews will be displayed here.</p>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default InstructorProfilePage;
