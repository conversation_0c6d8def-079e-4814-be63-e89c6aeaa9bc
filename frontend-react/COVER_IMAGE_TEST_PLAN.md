# Cover Image Upload Test Plan

## 🎯 Objective
Test the complete flow of cover image handling from the UnifiedCourseCreator form to backend storage and database persistence.

## 📋 Test Scope

### 1. **Frontend Form Upload**
- File selection and validation
- Progress tracking during upload
- Error handling for invalid files
- Preview generation after upload

### 2. **Media Service Integration**
- Base64 conversion and compression
- API endpoint communication (`/utils/savefile` and `/medias`)
- Response handling and error recovery
- File metadata extraction

### 3. **Backend Processing**
- File storage in filesystem
- Database record creation in `medias` table
- Proper field mapping and validation
- Error responses and logging

### 4. **Course Creation Integration**
- Media file linking to course record
- Foreign key relationship (`coverImageId`)
- Course creation with media reference
- Database consistency verification

## 🧪 Test Components

### **Test Interface: `/test/cover-image-upload`**
- Interactive upload component
- Real-time progress tracking
- Detailed test result logging
- Error capture and display

### **Database Tables to Monitor:**
1. **`medias` table:**
   ```sql
   - Id (auto-increment)
   - Slug (UUID)
   - Name (original filename)
   - <PERSON><PERSON>ame (stored filename)
   - Extension (file type)
   - Size (file size in bytes)
   - SubDir (storage subdirectory)
   - CreatedAt/UpdatedAt (timestamps)
   ```

2. **`courses` table:**
   ```sql
   - Id (auto-increment)
   - Title, Resume, etc.
   - coverImageId (foreign key to medias.Id)
   - CreatedAt/UpdatedAt (timestamps)
   ```

## 🔍 Test Scenarios

### **Scenario 1: Valid Image Upload**
- **Input:** JPG/PNG/WebP image < 100MB
- **Expected:** Successful upload, preview display, database record creation
- **Verification:** Check `medias` table for new record

### **Scenario 2: Invalid File Type**
- **Input:** PDF, TXT, or other non-image file
- **Expected:** Validation error, upload rejection
- **Verification:** No database records created

### **Scenario 3: Oversized File**
- **Input:** Image > 100MB
- **Expected:** Size validation error
- **Verification:** No upload attempt, clear error message

### **Scenario 4: Course Creation with Cover Image**
- **Input:** Valid uploaded image + complete course data
- **Expected:** Course created with linked cover image
- **Verification:** 
  - New record in `courses` table
  - `coverImageId` properly set
  - Foreign key relationship intact

### **Scenario 5: Network/Backend Errors**
- **Input:** Valid file with simulated backend failure
- **Expected:** Graceful error handling, user notification
- **Verification:** No partial records, proper cleanup

## 📊 Test Data Flow

```
1. User selects image file
   ↓
2. Frontend validation (type, size)
   ↓
3. File compression (if needed)
   ↓
4. Base64 conversion
   ↓
5. API call to /utils/savefile or /medias
   ↓
6. Backend file storage + database insert
   ↓
7. Response with media file metadata
   ↓
8. Frontend updates course data with media reference
   ↓
9. Course creation API call with media reference
   ↓
10. Backend creates course with coverImageId link
    ↓
11. Database verification of relationships
```

## 🔧 Backend Endpoints to Test

### **Primary Upload Endpoint:**
- `POST /api/utils/savefile`
- **Payload:** `{ File: base64, Filename: string, SubDir: string }`
- **Response:** File metadata or error

### **Fallback Upload Endpoint:**
- `POST /api/medias`
- **Payload:** `{ Name, Hashname, Extension, Size, SubDir }`
- **Response:** Media record with Id

### **Course Creation Endpoint:**
- `POST /api/courses`
- **Payload:** `{ origin, body: Course }`
- **Response:** Created course with relationships

## 🎯 Success Criteria

### ✅ **Upload Success Indicators:**
1. File successfully uploaded to backend storage
2. Media record created in database with correct metadata
3. File accessible via generated URL
4. Progress tracking works correctly
5. Preview displays properly

### ✅ **Course Integration Success:**
1. Course created with cover image reference
2. Foreign key relationship established
3. Image displays in course details
4. No orphaned media records
5. Proper error handling throughout

### ✅ **Error Handling Success:**
1. Invalid files rejected with clear messages
2. Network errors handled gracefully
3. Partial uploads cleaned up properly
4. User receives appropriate feedback
5. No database inconsistencies

## 🚀 How to Run Tests

### **1. Start Backend Server:**
```bash
cd backend
npm run start:dev
```

### **2. Start Frontend Development Server:**
```bash
cd frontend-react
npm run dev
```

### **3. Access Test Interface:**
```
http://localhost:5174/test/cover-image-upload
```

### **4. Monitor Database:**
```sql
-- Check media records
SELECT * FROM medias ORDER BY CreatedAt DESC LIMIT 10;

-- Check course records with cover images
SELECT c.Id, c.Title, c.coverImageId, m.Name, m.Hashname 
FROM courses c 
LEFT JOIN medias m ON c.coverImageId = m.Id 
ORDER BY c.CreatedAt DESC LIMIT 10;
```

### **5. Check File Storage:**
```bash
# Check if files are stored in expected directories
ls -la [backend_storage_path]/[user_slug]/content/coverimage/
```

## 📝 Test Results Documentation

### **Expected Test Output:**
- ✅ File type validation passed
- ✅ File size validation passed  
- ✅ Media service upload completed
- ✅ Uploaded file ID: [number]
- ✅ Uploaded filename: [filename]
- ✅ File URL: [url]
- ✅ MediaFile object created for course
- ✅ Course created successfully in backend
- ✅ Course cover image ID: [number]
- ✅ Course cover image filename: [filename]

### **Database Verification Queries:**
```sql
-- Verify media record
SELECT * FROM medias WHERE Id = [uploaded_file_id];

-- Verify course record
SELECT * FROM courses WHERE Id = [created_course_id];

-- Verify relationship
SELECT c.Title, m.Name as CoverImageName 
FROM courses c 
JOIN medias m ON c.coverImageId = m.Id 
WHERE c.Id = [created_course_id];
```

## 🐛 Common Issues to Watch For

1. **Base64 encoding issues** - Large files may cause memory problems
2. **File path conflicts** - Duplicate filenames need unique handling
3. **Database foreign key constraints** - Media must exist before course creation
4. **CORS issues** - File uploads may trigger CORS restrictions
5. **File size limits** - Backend may have different limits than frontend
6. **Storage permissions** - Backend needs write access to storage directories

## 📈 Performance Considerations

- **File compression** for large images
- **Progress tracking** for user experience
- **Error recovery** for network issues
- **Cleanup procedures** for failed uploads
- **Database indexing** for media lookups
