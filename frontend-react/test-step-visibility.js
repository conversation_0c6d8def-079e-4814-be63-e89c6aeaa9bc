// Test script to verify step visibility
// Run this in browser console on the course creator page

console.log('🧪 Testing Step Visibility...');

// Find all step elements
const stepElements = document.querySelectorAll('[class*="group flex items-center"]');
console.log(`Found ${stepElements.length} step elements`);

stepElements.forEach((step, index) => {
  const computedStyle = window.getComputedStyle(step);
  const backgroundColor = computedStyle.backgroundColor;
  const color = computedStyle.color;
  const isActive = step.classList.contains('bg-red-600') || backgroundColor.includes('220, 53, 69');
  
  console.log(`Step ${index + 1}:`, {
    isActive,
    backgroundColor,
    textColor: color,
    classes: step.className,
    visible: computedStyle.visibility !== 'hidden' && computedStyle.display !== 'none'
  });
  
  if (isActive) {
    console.log('🔴 Active step found:', step);
    
    // Check text elements within active step
    const textElements = step.querySelectorAll('div');
    textElements.forEach((textEl, textIndex) => {
      const textStyle = window.getComputedStyle(textEl);
      console.log(`  Text element ${textIndex}:`, {
        color: textStyle.color,
        content: textEl.textContent?.trim(),
        visible: textStyle.visibility !== 'hidden'
      });
    });
  }
});

// Test contrast ratio
function getContrastRatio(color1, color2) {
  // Simple contrast check - in real implementation would calculate actual ratio
  return color1.includes('255') && color2.includes('220') ? 'Good' : 'Check needed';
}

console.log('✅ Step visibility test completed');
console.log('💡 If active step is not visible, check:');
console.log('   1. Background color is applied (should be red)');
console.log('   2. Text color is white');
console.log('   3. No CSS conflicts override the styling');
console.log('   4. Brand colors are properly defined in CSS');
