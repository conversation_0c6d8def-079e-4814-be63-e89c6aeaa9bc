# Tailwind CSS v3.4 + shadcn/ui Implementation Plan

## 🎯 Overview
This document outlines the systematic implementation of Tailwind CSS v3.4 and shadcn/ui blocks across the BrainMaker Academy application, inspired by Domestika's minimalistic design approach.

## ✅ Completed Setup
- [x] Tailwind CSS v3.4 installed and configured
- [x] shadcn/ui dependencies installed (class-variance-authority, clsx, tailwind-merge, lucide-react)
- [x] CSS variables and design tokens configured
- [x] Path aliases configured (@/ mapping)
- [x] Core UI components created (Button, Card, Badge)
- [x] Modern HeroSection component created

## ✅ Completed Components (Ready to Use!)

### Core UI Components
- [x] **Button** - Multiple variants (default, outline, ghost, link, destructive)
- [x] **Card** - Header, content, footer, title, description components
- [x] **Badge** - Default, secondary, destructive, outline variants

### Form Components
- [x] **Input** - Standard, search, and password input variants
- [x] **Label** - Accessible form labels
- [x] **Textarea** - Multi-line text input
- [x] **Select** - Custom dropdown with search functionality
- [x] **Checkbox** - Styled checkbox with proper states

### Navigation Components
- [x] **NavigationMenu** - Modern navigation with dropdown support
- [x] **ModernHeader** - Complete header with mobile menu, search, cart, notifications
- [x] **Breadcrumb** - Navigation breadcrumbs with separators

### Course Components
- [x] **CourseCard** - Feature-rich course cards with ratings, badges, favorites
- [x] **CourseGrid** - Complete course grid with filtering and loading states
- [x] **InstructorProfile** - Instructor cards with stats, bio, and social links
- [x] **Avatar** - User avatar component with fallbacks

### Dashboard Components
- [x] **StatsCard** - Statistics cards with trends and variants
- [x] **StatsGrid** - Grid layout for multiple stats
- [x] **Progress** - Linear, circular, and stepped progress indicators
- [x] **ActivityTimeline** - User activity feed with icons and metadata

### Interactive Components
- [x] **Tabs** - Horizontal and vertical tab components
- [x] **Accordion** - Collapsible content sections
- [x] **Dialog** - Modal dialogs with alert dialog variant

### Layout Components
- [x] **Container** - Responsive container with size variants
- [x] **Section** - Section wrapper with spacing and background options
- [x] **Grid** - Responsive grid layout component
- [x] **Flex** - Flexible layout component
- [x] **Separator** - Horizontal and vertical separators

### Hero Components
- [x] **HeroSection** - Modern hero slider with image backgrounds

## 🏗️ Implementation Strategy

### Phase 1: Core UI Components (Priority: High)
**Components to implement from shadcnui-blocks.com:**

1. **Navigation Components**
   - [ ] Modern header/navbar (navbar-01 or navbar-02 style)
   - [ ] Mobile-responsive navigation
   - [ ] Breadcrumb navigation

2. **Layout Components**
   - [x] Button variants (default, outline, ghost, link)
   - [x] Card components (header, content, footer)
   - [x] Badge components
   - [ ] Separator component
   - [ ] Container/Section wrappers

### Phase 2: Landing Page Sections (Priority: High)
**Using shadcnui-blocks.com patterns:**

1. **Hero Section**
   - [x] Hero slider with image backgrounds
   - [x] CTA buttons with proper styling
   - [x] Navigation arrows and indicators
   - [ ] Replace current PromotionalBanner with new HeroSection

2. **Features Section**
   - [ ] Implement features-01 or features-02 pattern
   - [ ] Replace current WhyChooseUsSection
   - [ ] Clean grid layout with icons

3. **Logos/Trust Section**
   - [ ] Implement logos-01 or logos-06 (infinite scroll) pattern
   - [ ] Replace current TrustSignalsSection
   - [ ] Company logos with clean presentation

4. **Course Grid Section**
   - [ ] Implement custom course cards with shadcn/ui styling
   - [ ] Replace current SkillsSection course grid
   - [ ] Clean, minimal course card design

### Phase 3: Advanced Components (Priority: Medium)
**From shadcnui-blocks.com:**

1. **Interactive Components**
   - [ ] Tabs component for course categories
   - [ ] Accordion for FAQ sections
   - [ ] Carousel for testimonials
   - [ ] Progress indicators

2. **Form Components**
   - [ ] Input components
   - [ ] Select dropdowns
   - [ ] Checkbox and radio groups
   - [ ] Form validation styling

### Phase 4: Page-Specific Components (Priority: Medium)
**Using shadcnui-blocks.com patterns:**

1. **Course Pages**
   - [ ] Course detail cards
   - [ ] Instructor profiles
   - [ ] Progress tracking components

2. **User Dashboard**
   - [ ] Stats cards (stats-01 pattern)
   - [ ] Progress charts
   - [ ] Activity timeline

3. **Authentication Pages**
   - [ ] Login forms (login-01 pattern)
   - [ ] Signup forms (signup-01 pattern)
   - [ ] Password reset forms

## 🎨 Design System Guidelines

### Color Palette (Based on CSS Variables)
```css
Primary: hsl(355 78% 60%) /* #dc3545 - BrainMaker Red */
Secondary: hsl(210 40% 96%) /* Light gray */
Muted: hsl(215.4 16.3% 46.9%) /* Medium gray */
Background: hsl(0 0% 100%) /* White */
Foreground: hsl(222.2 84% 4.9%) /* Dark text */
```

### Typography Scale
- Headings: font-bold with proper line-height
- Body: font-normal with 1.6 line-height
- Small text: text-sm with muted-foreground color

### Spacing System
- Container max-width: 1200px
- Section padding: py-16 md:py-24
- Component spacing: space-y-6 or gap-6
- Button padding: px-6 py-3 (lg), px-4 py-2 (default)

### Component Patterns
1. **Cards**: rounded-lg border shadow-sm
2. **Buttons**: rounded-md with proper focus states
3. **Sections**: container mx-auto px-4
4. **Grid**: grid-cols-1 md:grid-cols-2 lg:grid-cols-3

## 📋 Implementation Checklist

### Immediate Tasks
- [ ] Replace PromotionalBanner with new HeroSection
- [ ] Create modern CourseCard component using shadcn/ui
- [ ] Implement features section using features-01 pattern
- [ ] Create logos section using logos-01 pattern
- [ ] Update header navigation with modern styling

### Next Steps
- [ ] Implement responsive navigation menu
- [ ] Add dark mode support
- [ ] Create reusable section components
- [ ] Implement form components
- [ ] Add animation and transition utilities

## 🔗 Resources
- [shadcn/ui Blocks](https://www.shadcnui-blocks.com/)
- [Tailwind CSS v3.4 Documentation](https://tailwindcss.com/)
- [Domestika Design Reference](https://www.domestika.org/en)
- [Lucide Icons](https://lucide.dev/)

## 📝 Notes
- All components should be fully responsive
- Maintain accessibility standards (ARIA labels, keyboard navigation)
- Use semantic HTML elements
- Follow Domestika's minimalistic design principles
- Ensure consistent spacing and typography throughout
