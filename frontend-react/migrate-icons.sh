#!/bin/bash

# Script to migrate all @ant-design/icons to Lucide React icons

echo "🎨 Starting Ant Design Icons to Lucide React migration..."

# Find all TypeScript/TSX files that import from '@ant-design/icons'
files=$(find src -name "*.tsx" -o -name "*.ts" | xargs grep -l "@ant-design/icons")

count=0
total=$(echo "$files" | wc -l)

echo "📁 Found $total files to migrate"

# Common icon mappings from Ant Design to Lucide
declare -A icon_map=(
    ["UserOutlined"]="User"
    ["MenuOutlined"]="Menu"
    ["MenuUnfoldOutlined"]="Menu"
    ["MenuFoldOutlined"]="Menu"
    ["SearchOutlined"]="Search"
    ["PlusOutlined"]="Plus"
    ["EditOutlined"]="Edit"
    ["DeleteOutlined"]="Trash2"
    ["EyeOutlined"]="Eye"
    ["DownloadOutlined"]="Download"
    ["UploadOutlined"]="Upload"
    ["SaveOutlined"]="Save"
    ["CloseOutlined"]="X"
    ["CheckOutlined"]="Check"
    ["SettingOutlined"]="Settings"
    ["HomeOutlined"]="Home"
    ["BookOutlined"]="Book"
    ["FileOutlined"]="File"
    ["FolderOutlined"]="Folder"
    ["CalendarOutlined"]="Calendar"
    ["ClockCircleOutlined"]="Clock"
    ["StarOutlined"]="Star"
    ["HeartOutlined"]="Heart"
    ["MessageOutlined"]="MessageCircle"
    ["MailOutlined"]="Mail"
    ["PhoneOutlined"]="Phone"
    ["VideoCameraOutlined"]="Video"
    ["AudioOutlined"]="Mic"
    ["AudioMutedOutlined"]="MicOff"
    ["PlayCircleOutlined"]="Play"
    ["PauseCircleOutlined"]="Pause"
    ["StopOutlined"]="Square"
    ["RecordOutlined"]="Circle"
    ["FullscreenOutlined"]="Maximize"
    ["FullscreenExitOutlined"]="Minimize"
    ["MoreOutlined"]="MoreHorizontal"
    ["FilterOutlined"]="Filter"
    ["SortAscendingOutlined"]="ArrowUp"
    ["SortDescendingOutlined"]="ArrowDown"
    ["LeftOutlined"]="ChevronLeft"
    ["RightOutlined"]="ChevronRight"
    ["UpOutlined"]="ChevronUp"
    ["DownOutlined"]="ChevronDown"
    ["InfoCircleOutlined"]="Info"
    ["ExclamationCircleOutlined"]="AlertCircle"
    ["CheckCircleOutlined"]="CheckCircle"
    ["CloseCircleOutlined"]="XCircle"
    ["QuestionCircleOutlined"]="HelpCircle"
    ["WarningOutlined"]="AlertTriangle"
    ["BellOutlined"]="Bell"
    ["TeamOutlined"]="Users"
    ["UserAddOutlined"]="UserPlus"
    ["UserDeleteOutlined"]="UserMinus"
    ["LockOutlined"]="Lock"
    ["UnlockOutlined"]="Unlock"
    ["KeyOutlined"]="Key"
    ["EyeInvisibleOutlined"]="EyeOff"
    ["CopyOutlined"]="Copy"
    ["ShareAltOutlined"]="Share"
    ["LinkOutlined"]="Link"
    ["GlobalOutlined"]="Globe"
    ["EnvironmentOutlined"]="MapPin"
    ["TagOutlined"]="Tag"
    ["TagsOutlined"]="Tags"
    ["FlagOutlined"]="Flag"
    ["BulbOutlined"]="Lightbulb"
    ["ThunderboltOutlined"]="Zap"
    ["FireOutlined"]="Flame"
    ["TrophyOutlined"]="Trophy"
    ["GiftOutlined"]="Gift"
    ["CrownOutlined"]="Crown"
    ["DiamondOutlined"]="Diamond"
    ["DollarOutlined"]="DollarSign"
    ["ShoppingOutlined"]="ShoppingBag"
    ["ShoppingCartOutlined"]="ShoppingCart"
    ["CreditCardOutlined"]="CreditCard"
    ["BankOutlined"]="Building"
    ["FileTextOutlined"]="FileText"
    ["FilePdfOutlined"]="FileText"
    ["FileImageOutlined"]="Image"
    ["PictureOutlined"]="Image"
    ["FileExcelOutlined"]="FileSpreadsheet"
    ["FileWordOutlined"]="FileText"
    ["FilePptOutlined"]="Presentation"
    ["FileZipOutlined"]="Archive"
    ["FolderAddOutlined"]="FolderPlus"
    ["FolderOpenOutlined"]="FolderOpen"
    ["InboxOutlined"]="Inbox"
    ["SendOutlined"]="Send"
    ["PrinterOutlined"]="Printer"
    ["ScanOutlined"]="Scan"
    ["CameraOutlined"]="Camera"
    ["PictureOutlined"]="Image"
    ["DesktopOutlined"]="Monitor"
    ["LaptopOutlined"]="Laptop"
    ["TabletOutlined"]="Tablet"
    ["MobileOutlined"]="Smartphone"
    ["WifiOutlined"]="Wifi"
    ["BluetoothOutlined"]="Bluetooth"
    ["UsbOutlined"]="Usb"
    ["CloudOutlined"]="Cloud"
    ["CloudDownloadOutlined"]="CloudDownload"
    ["CloudUploadOutlined"]="CloudUpload"
    ["DatabaseOutlined"]="Database"
    ["ServerOutlined"]="Server"
    ["ApiOutlined"]="Code"
    ["CodeOutlined"]="Code"
    ["BugOutlined"]="Bug"
    ["ExperimentOutlined"]="Flask"
    ["RocketOutlined"]="Rocket"
    ["ToolOutlined"]="Wrench"
    ["BuildOutlined"]="Hammer"
    ["SafetyOutlined"]="Shield"
    ["SecurityScanOutlined"]="ShieldCheck"
    ["AuditOutlined"]="FileCheck"
    ["HistoryOutlined"]="History"
    ["ReloadOutlined"]="RefreshCw"
    ["SyncOutlined"]="RotateCcw"
    ["UndoOutlined"]="Undo"
    ["RedoOutlined"]="Redo"
    ["ScissorOutlined"]="Scissors"
    ["HighlightOutlined"]="Highlighter"
    ["FontSizeOutlined"]="Type"
    ["BoldOutlined"]="Bold"
    ["ItalicOutlined"]="Italic"
    ["UnderlineOutlined"]="Underline"
    ["StrikethroughOutlined"]="Strikethrough"
    ["AlignLeftOutlined"]="AlignLeft"
    ["AlignCenterOutlined"]="AlignCenter"
    ["AlignRightOutlined"]="AlignRight"
    ["OrderedListOutlined"]="List"
    ["UnorderedListOutlined"]="List"
    ["TableOutlined"]="Table"
    ["InsertRowAboveOutlined"]="RowsAbove"
    ["InsertRowBelowOutlined"]="RowsBelow"
    ["BorderOutlined"]="Square"
    ["DashboardOutlined"]="BarChart3"
    ["BarChartOutlined"]="BarChart"
    ["LineChartOutlined"]="TrendingUp"
    ["PieChartOutlined"]="PieChart"
    ["DotChartOutlined"]="ScatterChart"
    ["AreaChartOutlined"]="AreaChart"
    ["StockOutlined"]="TrendingUp"
    ["FundOutlined"]="TrendingUp"
    ["SlackOutlined"]="MessageSquare"
    ["TwitterOutlined"]="Twitter"
    ["FacebookOutlined"]="Facebook"
    ["InstagramOutlined"]="Instagram"
    ["LinkedinOutlined"]="Linkedin"
    ["GithubOutlined"]="Github"
    ["GoogleOutlined"]="Chrome"
    ["AppleOutlined"]="Apple"
    ["WindowsOutlined"]="Monitor"
    ["AndroidOutlined"]="Smartphone"
    ["SkypeOutlined"]="Video"
    ["WechatOutlined"]="MessageCircle"
    ["QqOutlined"]="MessageCircle"
    ["DingtalkOutlined"]="MessageCircle"
    ["AliwangwangOutlined"]="MessageCircle"
    ["YahooOutlined"]="Mail"
    ["RedditOutlined"]="MessageSquare"
    ["TaobaoOutlined"]="ShoppingBag"
    ["AlipayOutlined"]="CreditCard"
    ["ZhihuOutlined"]="MessageSquare"
    ["Html5Outlined"]="Code"
    ["CssOutlined"]="Code"
    ["JavascriptOutlined"]="Code"
    ["PythonOutlined"]="Code"
    ["JavaOutlined"]="Code"
    ["COutlined"]="Code"
    ["CplusplusOutlined"]="Code"
    ["RubyOutlined"]="Code"
    ["PhpOutlined"]="Code"
    ["SwiftOutlined"]="Code"
    ["KotlinOutlined"]="Code"
    ["GoOutlined"]="Code"
    ["RustOutlined"]="Code"
    ["DockerOutlined"]="Package"
    ["KubernetesOutlined"]="Package"
    ["GitlabOutlined"]="GitBranch"
    ["BitbucketOutlined"]="GitBranch"
    ["ChromeOutlined"]="Chrome"
    ["FirefoxOutlined"]="Globe"
    ["SafariOutlined"]="Globe"
    ["EdgeOutlined"]="Globe"
    ["IeOutlined"]="Globe"
    ["OperaOutlined"]="Globe"
)

for file in $files; do
    count=$((count + 1))
    echo "[$count/$total] Migrating icons in: $file"
    
    # Replace @ant-design/icons import with lucide-react
    sed -i '' "s|from '@ant-design/icons'|from 'lucide-react'|g" "$file"
    
    # Replace individual icon names
    for ant_icon in "${!icon_map[@]}"; do
        lucide_icon="${icon_map[$ant_icon]}"
        # Replace in import statements
        sed -i '' "s|$ant_icon|$lucide_icon|g" "$file"
    done
done

echo "✅ Icon migration complete!"
echo "🔍 Checking for remaining @ant-design/icons imports..."

remaining=$(find src -name "*.tsx" -o -name "*.ts" | xargs grep -l "@ant-design/icons" | wc -l)
echo "📊 Remaining files with @ant-design/icons: $remaining"

if [ "$remaining" -gt 0 ]; then
    echo "⚠️  Some files still have @ant-design/icons imports. Manual review needed:"
    find src -name "*.tsx" -o -name "*.ts" | xargs grep -l "@ant-design/icons"
fi
