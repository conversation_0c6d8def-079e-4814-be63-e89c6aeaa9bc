#!/bin/bash

# Simple script to replace antd imports with shadcn/ui compatibility layer

echo "🔄 Replacing Ant Design imports with shadcn/ui compatibility layer..."

# Find all files that import from 'antd'
find src -name "*.tsx" -o -name "*.ts" | while read file; do
    if grep -q "from 'antd'" "$file"; then
        echo "Updating: $file"
        
        # Determine correct relative path based on file location
        if [[ "$file" == src/components/* ]]; then
            sed -i '' "s|from 'antd'|from './antd'|g" "$file"
        elif [[ "$file" == src/pages/* ]]; then
            sed -i '' "s|from 'antd'|from '../../components/antd'|g" "$file"
        elif [[ "$file" == src/hooks/* ]]; then
            sed -i '' "s|from 'antd'|from '../components/antd'|g" "$file"
        else
            sed -i '' "s|from 'antd'|from '../components/antd'|g" "$file"
        fi
    fi
done

echo "✅ Import replacement complete!"

# Check remaining antd imports
remaining=$(find src -name "*.tsx" -o -name "*.ts" | xargs grep -l "from 'antd'" | wc -l)
echo "📊 Remaining files with antd imports: $remaining"
