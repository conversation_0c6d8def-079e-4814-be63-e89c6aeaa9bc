const axios = require('axios');

/**
 * Debug the courses page to see exactly what's happening with the course cards
 * This will simulate the exact flow that the CoursesPage component uses
 */

const API_BASE = 'http://localhost:3200/api';
const ENVIRONMENT_PATH = 'http://localhost:3200';

// Simulate the course service's formatCourse method
function formatCourse(course) {
  if (!course) return course;

  const constructMediaUrl = (hashname) => {
    if (!hashname) return '';
    if (hashname.startsWith('http://') || hashname.startsWith('https://')) {
      return hashname;
    }
    const cleanHashname = hashname.startsWith('/') ? hashname.substring(1) : hashname;
    return `${ENVIRONMENT_PATH}/${cleanHashname}`;
  };

  // Format cover image URL
  if (course.CoverImage?.Hashname) {
    course.CoverImage.Hashname = constructMediaUrl(course.CoverImage.Hashname);
  }

  // Format creator photo
  if (course.CreatedBy?.Photo?.Hashname) {
    course.CreatedBy.Photo.Hashname = constructMediaUrl(course.CreatedBy.Photo.Hashname);
  }

  return course;
}

// Simulate the course service's formatCourses method
function formatCourses(courses) {
  if (!Array.isArray(courses)) return [];
  return courses.map(course => formatCourse(course));
}

async function debugCoursesPage() {
  console.log('🔍 Debug: Courses Page Data Flow');
  console.log('=' .repeat(50));

  try {
    // Step 1: Simulate what CoursesPage does - courseService.getAll(take, skip, true)
    console.log('\n1️⃣ SIMULATING CoursesPage.tsx API Call');
    console.log('-' .repeat(40));
    
    const take = 15;
    const skip = 0;
    const justPublished = true;
    
    console.log(`📡 Calling: courseService.getAll(${take}, ${skip}, ${justPublished})`);
    console.log(`   Actual API: GET ${API_BASE}/courses/${take}/${skip}?justPublished=${justPublished}`);
    
    // Raw API call (what the service does internally)
    const rawResponse = await axios.get(`${API_BASE}/courses/${take}/${skip}?justPublished=${justPublished}`);
    const rawCourses = rawResponse.data;
    
    console.log(`✅ Raw API returned ${rawCourses.length} courses`);
    
    // Apply course service formatting (what getAll should do now)
    const formattedCourses = formatCourses(rawCourses);
    console.log(`✅ Course service formatting applied to ${formattedCourses.length} courses`);

    // Step 2: Find our target course
    console.log('\n2️⃣ ANALYZING TARGET COURSE');
    console.log('-' .repeat(40));
    
    const targetCourse = formattedCourses.find(course => 
      course.Title && course.Title.toLowerCase().includes('demo course two')
    );
    
    if (targetCourse) {
      console.log(`✅ Target course found: "${targetCourse.Title}"`);
      console.log(`   - ID: ${targetCourse.Id}`);
      console.log(`   - Slug: ${targetCourse.Slug}`);
      console.log(`   - CoverImage.Hashname: "${targetCourse.CoverImage?.Hashname || 'NONE'}"`);
      console.log(`   - CreatedBy.Firstname: "${targetCourse.CreatedBy?.Firstname || 'NONE'}"`);
      console.log(`   - CreatedBy.Lastname: "${targetCourse.CreatedBy?.Lastname || 'NONE'}"`);
      console.log(`   - CreatedBy.Photo.Hashname: "${targetCourse.CreatedBy?.Photo?.Hashname || 'NONE'}"`);
      console.log(`   - Price: ${targetCourse.Price || 0}`);
      console.log(`   - Free: ${targetCourse.Free}`);
      console.log(`   - Ratings: ${targetCourse.Ratings?.length || 0} ratings`);
    } else {
      console.log('❌ Target course not found');
      return;
    }

    // Step 3: Simulate what CoursesPage passes to CourseCard
    console.log('\n3️⃣ SIMULATING CourseCard Props');
    console.log('-' .repeat(40));
    
    // This is what CoursesPage.tsx does now (after our fix)
    const courseCardProps = {
      ...targetCourse,
      Slug: targetCourse.Slug,
      ReviewsCount: targetCourse.Ratings?.length || 0,
      StudentsCount: 0,
      averageRating: targetCourse.Rating || 0,
      reviewCount: targetCourse.Ratings?.length || 0
    };
    
    console.log(`📦 CourseCard will receive:`);
    console.log(`   - Title: "${courseCardProps.Title}"`);
    console.log(`   - CoverImage.Hashname: "${courseCardProps.CoverImage?.Hashname || 'NONE'}"`);
    console.log(`   - CreatedBy.Firstname: "${courseCardProps.CreatedBy?.Firstname || 'NONE'}"`);
    console.log(`   - CreatedBy.Lastname: "${courseCardProps.CreatedBy?.Lastname || 'NONE'}"`);
    console.log(`   - CreatedBy.Photo.Hashname: "${courseCardProps.CreatedBy?.Photo?.Hashname || 'NONE'}"`);

    // Step 4: Simulate CourseCard's getCourseImage() method
    console.log('\n4️⃣ SIMULATING CourseCard.getCourseImage()');
    console.log('-' .repeat(40));
    
    const getCourseImage = () => {
      if (courseCardProps.CoverImage?.Hashname) {
        return courseCardProps.CoverImage.Hashname;
      }
      return undefined;
    };
    
    const courseImageUrl = getCourseImage();
    console.log(`🖼️  getCourseImage() returns: "${courseImageUrl || 'undefined'}"`);
    
    if (courseImageUrl) {
      try {
        const imageResponse = await axios.head(courseImageUrl, { timeout: 5000 });
        console.log(`✅ Course image URL works: ${imageResponse.status}`);
      } catch (error) {
        console.log(`❌ Course image URL failed: ${error.response?.status || 'Network Error'}`);
      }
    }

    // Step 5: Simulate CourseCard's getInstructorImage() method
    console.log('\n5️⃣ SIMULATING CourseCard.getInstructorImage()');
    console.log('-' .repeat(40));
    
    const getInstructorImage = () => {
      if (courseCardProps.CreatedBy?.Photo?.Hashname) {
        return courseCardProps.CreatedBy.Photo.Hashname;
      }
      return undefined;
    };
    
    const instructorImageUrl = getInstructorImage();
    console.log(`👤 getInstructorImage() returns: "${instructorImageUrl || 'undefined'}"`);
    
    if (instructorImageUrl) {
      try {
        const avatarResponse = await axios.head(instructorImageUrl, { timeout: 5000 });
        console.log(`✅ Instructor image URL works: ${avatarResponse.status}`);
      } catch (error) {
        console.log(`❌ Instructor image URL failed: ${error.response?.status || 'Network Error'}`);
      }
    }

    // Step 6: Check all courses for images
    console.log('\n6️⃣ ANALYZING ALL COURSES FOR IMAGES');
    console.log('-' .repeat(40));
    
    let coursesWithImages = 0;
    let coursesWithInstructorPhotos = 0;
    
    formattedCourses.forEach((course, index) => {
      if (course.CoverImage?.Hashname) {
        coursesWithImages++;
      }
      if (course.CreatedBy?.Photo?.Hashname) {
        coursesWithInstructorPhotos++;
      }
    });
    
    console.log(`📊 Image Statistics:`);
    console.log(`   - Total courses: ${formattedCourses.length}`);
    console.log(`   - Courses with cover images: ${coursesWithImages}`);
    console.log(`   - Courses with instructor photos: ${coursesWithInstructorPhotos}`);
    
    // Step 7: List first few courses with their image status
    console.log('\n7️⃣ FIRST 5 COURSES IMAGE STATUS');
    console.log('-' .repeat(40));
    
    formattedCourses.slice(0, 5).forEach((course, index) => {
      console.log(`${index + 1}. "${course.Title}"`);
      console.log(`   Cover: ${course.CoverImage?.Hashname ? '✅' : '❌'}`);
      console.log(`   Instructor: ${course.CreatedBy?.Photo?.Hashname ? '✅' : '❌'}`);
    });

  } catch (error) {
    console.error('💥 Debug failed:', error.message);
  }

  console.log('\n🎉 Courses page debug completed!');
  console.log('\n💡 If images are still not showing in the browser:');
  console.log('   1. Check browser console for JavaScript errors');
  console.log('   2. Check Network tab for failed image requests');
  console.log('   3. Verify the CourseCard component is rendering img tags with correct src');
  console.log('   4. Try hard refresh (Ctrl+Shift+R or Cmd+Shift+R)');
}

// Run the debug
debugCoursesPage().catch(console.error);
