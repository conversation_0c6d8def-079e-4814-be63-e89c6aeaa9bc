/**
 * Comprehensive Media Upload Test
 * Tests both /api/utils/savefile and /api/medias endpoints
 * Verifies file saving functionality before implementing in React components
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3200/api';

// Create a small test image in base64 format (1x1 pixel PNG)
const TEST_IMAGE_BASE64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';

// Create a small test video in base64 format (minimal MP4 header)
const TEST_VIDEO_BASE64 = 'AAAAIGZ0eXBpc29tAAACAGlzb21pc28yYXZjMW1wNDEAAAAIZnJlZQAAAr1tZGF0';

async function testUtilsSaveFileEndpoint() {
  console.log('\n🧪 Testing /api/utils/savefile endpoint...');
  
  try {
    // Test 1: Cover Image Upload
    console.log('1️⃣ Testing cover image upload...');
    const coverResponse = await axios.post(`${BASE_URL}/utils/savefile`, {
      File: TEST_IMAGE_BASE64,
      Filename: `test-cover-${Date.now()}.png`,
      SubDir: 'courses/covers'
    });
    
    console.log('✅ Cover image upload response:', coverResponse.data);
    
    // Test 2: Presentation Video Upload
    console.log('2️⃣ Testing presentation video upload...');
    const videoResponse = await axios.post(`${BASE_URL}/utils/savefile`, {
      File: TEST_VIDEO_BASE64,
      Filename: `test-video-${Date.now()}.mp4`,
      SubDir: 'courses/videos'
    });
    
    console.log('✅ Video upload response:', videoResponse.data);
    
    // Test 3: Content Video Upload
    console.log('3️⃣ Testing content video upload...');
    const contentVideoResponse = await axios.post(`${BASE_URL}/utils/savefile`, {
      File: TEST_VIDEO_BASE64,
      Filename: `content-video-${Date.now()}.mp4`,
      SubDir: 'instructor/content'
    });
    
    console.log('✅ Content video upload response:', contentVideoResponse.data);
    
    return {
      success: true,
      endpoint: '/api/utils/savefile',
      results: {
        cover: coverResponse.data,
        video: videoResponse.data,
        contentVideo: contentVideoResponse.data
      }
    };
    
  } catch (error) {
    console.error('❌ Utils/savefile endpoint failed:', error.response?.data || error.message);
    return {
      success: false,
      endpoint: '/api/utils/savefile',
      error: error.response?.data || error.message
    };
  }
}

async function testMediasEndpoint() {
  console.log('\n🧪 Testing /api/medias endpoint...');
  
  try {
    // Test 1: Cover Image Upload
    console.log('1️⃣ Testing cover image upload via medias...');
    const coverResponse = await axios.post(`${BASE_URL}/medias`, {
      Username: `test-cover-${Date.now()}.png`,
      Hashname: TEST_IMAGE_BASE64,
      Extension: 'image/png',
      Size: 32,
      SubDir: 'courses/covers'
    });
    
    console.log('✅ Cover image upload response:', {
      Id: coverResponse.data.Id,
      Username: coverResponse.data.Username,
      Hashname: coverResponse.data.Hashname?.substring(0, 50) + '...',
      SubDir: coverResponse.data.SubDir
    });
    
    // Test 2: Presentation Video Upload
    console.log('2️⃣ Testing presentation video upload via medias...');
    const videoResponse = await axios.post(`${BASE_URL}/medias`, {
      Username: `test-video-${Date.now()}.mp4`,
      Hashname: TEST_VIDEO_BASE64,
      Extension: 'video/mp4',
      Size: 64,
      SubDir: 'courses/videos'
    });
    
    console.log('✅ Video upload response:', {
      Id: videoResponse.data.Id,
      Username: videoResponse.data.Username,
      Hashname: videoResponse.data.Hashname?.substring(0, 50) + '...',
      SubDir: videoResponse.data.SubDir
    });
    
    return {
      success: true,
      endpoint: '/api/medias',
      results: {
        cover: coverResponse.data,
        video: videoResponse.data
      }
    };
    
  } catch (error) {
    console.error('❌ Medias endpoint failed:', error.response?.data || error.message);
    return {
      success: false,
      endpoint: '/api/medias',
      error: error.response?.data || error.message
    };
  }
}

async function verifyFilesSaved() {
  console.log('\n🔍 Verifying files were saved to backend/public/upload...');
  
  const uploadDir = path.join(__dirname, 'backend/public/upload');
  
  try {
    // Check courses/covers directory
    const coversDir = path.join(uploadDir, 'courses/covers');
    if (fs.existsSync(coversDir)) {
      const coverFiles = fs.readdirSync(coversDir);
      console.log(`✅ Found ${coverFiles.length} files in courses/covers:`, coverFiles.slice(-3)); // Show last 3 files
    } else {
      console.log('❌ courses/covers directory not found');
    }
    
    // Check courses/videos directory
    const videosDir = path.join(uploadDir, 'courses/videos');
    if (fs.existsSync(videosDir)) {
      const videoFiles = fs.readdirSync(videosDir);
      console.log(`✅ Found ${videoFiles.length} files in courses/videos:`, videoFiles.slice(-3)); // Show last 3 files
    } else {
      console.log('❌ courses/videos directory not found');
    }
    
    // Check instructor/content directory
    const contentDir = path.join(uploadDir, 'instructor/content');
    if (fs.existsSync(contentDir)) {
      const contentFiles = fs.readdirSync(contentDir);
      console.log(`✅ Found ${contentFiles.length} files in instructor/content:`, contentFiles.slice(-3)); // Show last 3 files
    } else {
      console.log('❌ instructor/content directory not found');
    }
    
  } catch (error) {
    console.error('❌ Error verifying files:', error.message);
  }
}

async function testFileUrlConstruction() {
  console.log('\n🔗 Testing file URL construction...');
  
  const baseUrl = 'http://localhost:3200';
  const testFiles = [
    { filename: 'test-cover.png', subDir: 'courses/covers' },
    { filename: 'test-video.mp4', subDir: 'courses/videos' },
    { filename: 'content-video.mp4', subDir: 'instructor/content' }
  ];
  
  testFiles.forEach(file => {
    const url = `${baseUrl}/public/upload/${file.subDir}/${file.filename}`;
    console.log(`📁 ${file.subDir}/${file.filename} → ${url}`);
  });
}

async function runComprehensiveTest() {
  console.log('🚀 Starting Comprehensive Media Upload Test');
  console.log('=' .repeat(60));
  
  const results = {
    utilsSavefile: null,
    medias: null,
    timestamp: new Date().toISOString()
  };
  
  // Test utils/savefile endpoint
  results.utilsSavefile = await testUtilsSaveFileEndpoint();
  
  // Test medias endpoint
  results.medias = await testMediasEndpoint();
  
  // Verify files were saved
  await verifyFilesSaved();
  
  // Test URL construction
  await testFileUrlConstruction();
  
  // Summary
  console.log('\n📊 Test Summary');
  console.log('=' .repeat(60));
  console.log(`✅ Utils/savefile endpoint: ${results.utilsSavefile.success ? 'WORKING' : 'FAILED'}`);
  console.log(`${results.medias.success ? '✅' : '❌'} Medias endpoint: ${results.medias.success ? 'WORKING' : 'FAILED'}`);
  
  if (results.utilsSavefile.success) {
    console.log('\n🎉 RECOMMENDATION: Use /api/utils/savefile endpoint');
    console.log('   - Simple base64 upload');
    console.log('   - Direct file saving to backend/public/upload');
    console.log('   - Supports subdirectories');
    console.log('   - No database storage needed');
  }
  
  if (results.medias.success) {
    console.log('\n📝 Alternative: /api/medias endpoint also works');
    console.log('   - Stores metadata in database');
    console.log('   - Returns media entity with ID');
    console.log('   - More complex but provides tracking');
  }
  
  console.log('\n🔧 Implementation Guide:');
  console.log('1. Convert File to base64');
  console.log('2. POST to /api/utils/savefile with { File, Filename, SubDir }');
  console.log('3. File URL: http://localhost:3200/public/upload/{SubDir}/{Filename}');
  
  return results;
}

// Run the test
runComprehensiveTest().catch(console.error);
