/**
 * Check the course that was just created to see if it's using the correct media
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3200/api';

async function apiRequest(method, endpoint, data = null) {
  const config = {
    method,
    url: `${API_BASE_URL}${endpoint}`,
    headers: {
      'Content-Type': 'application/json'
    }
  };
  
  if (data) {
    config.data = data;
  }
  
  try {
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`API Error (${method} ${endpoint}):`, error.response?.data || error.message);
    throw error;
  }
}

async function checkCreatedCourse() {
  console.log('🔍 Checking the course that was just created (ID: 75)');
  console.log('=' .repeat(50));
  
  try {
    // Get the course by ID
    const course = await apiRequest('GET', '/courses/75');
    
    console.log('📊 Course Information:');
    console.log(`   ID: ${course.Id}`);
    console.log(`   Title: ${course.Title}`);
    console.log(`   Slug: ${course.Slug}`);
    
    if (course.CoverImage) {
      console.log('\n🖼️ Cover Image:');
      console.log(`   ID: ${course.CoverImage.Id}`);
      console.log(`   Hashname: ${course.CoverImage.Hashname}`);
      console.log(`   SubDir: ${course.CoverImage.SubDir}`);
      
      // Check if this matches the original media we uploaded (ID: 142)
      if (course.CoverImage.Id === 142) {
        console.log('   ✅ Using original media (ID matches)');
        if (course.CoverImage.SubDir === 'courses/covers') {
          console.log('   ✅ Correct centralized directory');
          console.log('   🎉 SUCCESS: Backend is using NEW approach correctly!');
        } else {
          console.log('   ❌ Wrong directory:', course.CoverImage.SubDir);
          console.log('   ❌ Expected: courses/covers');
        }
      } else {
        console.log('   ❌ Using different media (duplicate created)');
        console.log('   ❌ Expected ID: 142, Got ID:', course.CoverImage.Id);
        console.log('   ❌ Backend is still creating duplicates');
      }
    } else {
      console.log('\n❌ No cover image found in course');
    }
    
    // Also check if there are any new media files created
    console.log('\n🔍 Checking all media to see if duplicates were created...');
    const allMedia = await apiRequest('GET', '/medias');
    const recentMedia = allMedia.filter(m => m.Id >= 142).sort((a, b) => b.Id - a.Id);
    
    console.log('📊 Recent Media (ID >= 142):');
    recentMedia.forEach(m => {
      console.log(`   ID: ${m.Id}, Hashname: ${m.Hashname}, SubDir: ${m.SubDir}`);
    });
    
    if (recentMedia.length === 1 && recentMedia[0].Id === 142) {
      console.log('✅ No duplicates created - backend is working correctly!');
    } else if (recentMedia.length > 1) {
      console.log('❌ Duplicates were created - backend is still using OLD approach');
    }
    
  } catch (error) {
    console.error('Error checking course:', error.message);
  }
}

checkCreatedCourse();
