const axios = require('axios');

/**
 * Test what the frontend is actually receiving from the API
 * This simulates the exact API calls the frontend makes
 */

async function testFrontendAPI() {
  console.log('🔍 Testing Frontend API Calls');
  console.log('=' .repeat(50));

  const API_BASE = 'http://localhost:3200/api';

  try {
    // Test 1: Simulate the courses page API call
    console.log('\n1️⃣ Testing courses page API call (what frontend receives)...');
    
    // This is the exact call the frontend makes for the courses page
    const response = await axios.get(`${API_BASE}/courses/15/0`);
    const courses = response.data;

    console.log(`✅ Received ${courses.length} courses`);

    // Find our target course
    const targetCourse = courses.find(course => 
      course.Title && course.Title.toLowerCase().includes('demo course two')
    );

    if (targetCourse) {
      console.log('\n📋 Target Course Data (Raw from API):');
      console.log(`   - Title: ${targetCourse.Title}`);
      console.log(`   - Slug: ${targetCourse.Slug}`);
      
      if (targetCourse.CoverImage) {
        console.log('   - CoverImage Object:');
        console.log(`     * Hashname: "${targetCourse.CoverImage.Hashname}"`);
        console.log(`     * Name: "${targetCourse.CoverImage.Name || 'N/A'}"`);
        console.log(`     * Extension: "${targetCourse.CoverImage.Extension || 'N/A'}"`);
        
        // Test what the frontend SHOULD construct
        const frontendURL = constructFrontendURL(targetCourse.CoverImage.Hashname);
        console.log(`   - Frontend Should Construct: "${frontendURL}"`);
        
        // Test if this URL works
        try {
          const imageTest = await axios.head(frontendURL, { timeout: 5000 });
          console.log(`   ✅ Constructed URL works: ${imageTest.status}`);
        } catch (error) {
          console.log(`   ❌ Constructed URL fails: ${error.response?.status || 'Network Error'}`);
        }
      } else {
        console.log('   ❌ No CoverImage object in API response');
      }
    } else {
      console.log('❌ Target course not found in API response');
    }

    // Test 2: Check if there are any courses with working cover images
    console.log('\n2️⃣ Checking all courses for cover images...');
    
    const coursesWithImages = courses.filter(course => course.CoverImage?.Hashname);
    console.log(`✅ Found ${coursesWithImages.length} courses with cover images`);

    if (coursesWithImages.length > 0) {
      console.log('\n📋 Sample courses with cover images:');
      coursesWithImages.slice(0, 3).forEach((course, index) => {
        console.log(`   ${index + 1}. "${course.Title}"`);
        console.log(`      - Hashname: "${course.CoverImage.Hashname}"`);
        console.log(`      - Should construct: "${constructFrontendURL(course.CoverImage.Hashname)}"`);
      });
    }

    // Test 3: Test the exact API endpoint the CourseCard component would use
    console.log('\n3️⃣ Testing CourseCard component scenario...');
    
    if (targetCourse && targetCourse.CoverImage) {
      const hashname = targetCourse.CoverImage.Hashname;
      
      console.log('🔧 CourseCard getCourseImage() simulation:');
      console.log(`   - Input hashname: "${hashname}"`);
      
      // This is what our fixed CourseCard should do
      const courseImageURL = hashname; // The service should have already formatted it
      console.log(`   - CourseCard would use: "${courseImageURL}"`);
      
      // But if the service isn't formatting it, CourseCard gets the raw hashname
      console.log(`   - If service not formatting: "${hashname}"`);
      console.log(`   - Manual construction: "${constructFrontendURL(hashname)}"`);
    }

  } catch (error) {
    console.error('💥 Test failed:', error.message);
  }

  console.log('\n🎉 Frontend API test completed!');
}

/**
 * This simulates the frontend URL construction logic from our fixes
 */
function constructFrontendURL(hashname) {
  if (!hashname) return '';

  // If already a complete URL, return as-is
  if (hashname.startsWith('http://') || hashname.startsWith('https://')) {
    return hashname;
  }

  // Remove leading slash if present to avoid double slashes
  const cleanHashname = hashname.startsWith('/') ? hashname.substring(1) : hashname;

  // Use environment path (development)
  const environmentPath = 'http://localhost:3200';
  return `${environmentPath}/${cleanHashname}`;
}

// Run the test
testFrontendAPI().catch(console.error);
