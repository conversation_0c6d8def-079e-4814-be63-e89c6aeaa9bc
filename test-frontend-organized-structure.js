/**
 * Test the frontend course creation with the new organized directory structure
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3200/api';

async function apiRequest(method, endpoint, data = null) {
  const config = {
    method,
    url: `${API_BASE_URL}${endpoint}`,
    headers: {
      'Content-Type': 'application/json'
    }
  };
  
  if (data) {
    config.data = data;
  }
  
  try {
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`API Error (${method} ${endpoint}):`, error.response?.data || error.message);
    throw error;
  }
}

async function testFrontendOrganizedStructure() {
  console.log('🎨 Testing Frontend Course Creation with Organized Structure');
  console.log('=' .repeat(65));
  
  // Simulate frontend course creation workflow
  const tempCourseSlug = `temp-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
  const userSlug = 'D2174571';
  
  console.log(`📋 Frontend Simulation:`);
  console.log(`   Temp Course Slug: ${tempCourseSlug}`);
  console.log(`   User Slug: ${userSlug}`);
  
  // Step 1: Upload cover image (simulating frontend media service)
  console.log('\n1️⃣ Simulating frontend cover image upload...');
  const coverImageData = {
    Name: 'frontend-cover.png',
    Hashname: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==',
    Extension: 'image/png',
    Size: 1500,
    SubDir: `courses/${tempCourseSlug}/cover` // Frontend media service generates this
  };
  
  console.log(`   SubDir: ${coverImageData.SubDir}`);
  const uploadedCover = await apiRequest('POST', '/medias', coverImageData);
  
  console.log('✅ Cover uploaded:');
  console.log(`   ID: ${uploadedCover.Id}`);
  console.log(`   Hashname: ${uploadedCover.Hashname}`);
  console.log(`   Prefix: ${uploadedCover.Hashname.substring(0, 4)}`);
  
  // Step 2: Upload presentation video (simulating frontend media service)
  console.log('\n2️⃣ Simulating frontend video upload...');
  const videoData = {
    Name: 'frontend-video.mp4',
    Hashname: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==',
    Extension: 'video/mp4',
    Size: 5000,
    SubDir: `courses/${tempCourseSlug}/video` // Frontend media service generates this
  };
  
  console.log(`   SubDir: ${videoData.SubDir}`);
  const uploadedVideo = await apiRequest('POST', '/medias', videoData);
  
  console.log('✅ Video uploaded:');
  console.log(`   ID: ${uploadedVideo.Id}`);
  console.log(`   Hashname: ${uploadedVideo.Hashname}`);
  console.log(`   Prefix: ${uploadedVideo.Hashname.substring(0, 4)}`);
  
  // Step 3: Create course (simulating frontend course creator)
  console.log('\n3️⃣ Simulating frontend course creation...');
  const coursePayload = {
    body: {
      Title: 'Frontend Organized Structure Test',
      Resume: 'Testing frontend course creation with organized directory structure',
      Keywords: ['frontend', 'organized', 'test'],
      Prerequisites: ['Basic knowledge'],
      Goals: ['Learn organized structure'],
      Format: 2,
      Language: 'en',
      Free: true,
      Level: [1],
      Message: 'Welcome message',
      Congratulation: 'Congratulations message',
      Categories: [],
      Published: false,
      Archived: false,
      // Frontend passes media IDs and temp slug
      coverImageId: uploadedCover.Id,
      presentationVideoId: uploadedVideo.Id,
      Slug: tempCourseSlug, // Frontend uses the same temp slug
      CreatedBy: { Id: 1 }
    },
    origin: 'http://localhost:3000'
  };
  
  console.log(`   Using coverImageId: ${coursePayload.body.coverImageId}`);
  console.log(`   Using presentationVideoId: ${coursePayload.body.presentationVideoId}`);
  console.log(`   Using temp slug: ${coursePayload.body.Slug}`);
  
  const createdCourse = await apiRequest('POST', '/courses', coursePayload);
  
  console.log('✅ Course created:');
  console.log(`   ID: ${createdCourse.Id}`);
  console.log(`   Title: ${createdCourse.Title}`);
  console.log(`   Slug: ${createdCourse.Slug}`);
  
  // Step 4: Verify the complete workflow
  console.log('\n4️⃣ Verifying complete frontend workflow...');
  const retrievedCourse = await apiRequest('GET', `/courses/${createdCourse.Id}`);
  
  console.log('📊 Frontend Workflow Analysis:');
  
  // Check slug consistency
  const slugConsistent = createdCourse.Slug === tempCourseSlug;
  console.log(`   Slug Consistency: ${slugConsistent ? '✅ YES' : '❌ NO'}`);
  console.log(`     Expected: ${tempCourseSlug}`);
  console.log(`     Got: ${createdCourse.Slug}`);
  
  // Check media references
  const coverCorrect = retrievedCourse.CoverImage?.Id === uploadedCover.Id;
  const videoCorrect = retrievedCourse.PresentationVideo?.Id === uploadedVideo.Id;
  
  console.log(`   Cover Image Reference: ${coverCorrect ? '✅ CORRECT' : '❌ INCORRECT'}`);
  if (retrievedCourse.CoverImage) {
    console.log(`     ID: ${retrievedCourse.CoverImage.Id} (expected: ${uploadedCover.Id})`);
    console.log(`     SubDir: ${retrievedCourse.CoverImage.SubDir}`);
  }
  
  console.log(`   Video Reference: ${videoCorrect ? '✅ CORRECT' : '❌ INCORRECT'}`);
  if (retrievedCourse.PresentationVideo) {
    console.log(`     ID: ${retrievedCourse.PresentationVideo.Id} (expected: ${uploadedVideo.Id})`);
    console.log(`     SubDir: ${retrievedCourse.PresentationVideo.SubDir}`);
  }
  
  // Check directory structure
  const coverDirCorrect = uploadedCover.SubDir === `courses/${tempCourseSlug}/cover`;
  const videoDirCorrect = uploadedVideo.SubDir === `courses/${tempCourseSlug}/video`;
  const coverPrefixCorrect = uploadedCover.Hashname.startsWith('cvr_');
  const videoPrefixCorrect = uploadedVideo.Hashname.startsWith('vid_');
  
  console.log(`   Directory Structure:`);
  console.log(`     Cover Directory: ${coverDirCorrect ? '✅ CORRECT' : '❌ INCORRECT'}`);
  console.log(`     Video Directory: ${videoDirCorrect ? '✅ CORRECT' : '❌ INCORRECT'}`);
  console.log(`     Cover Prefix: ${coverPrefixCorrect ? '✅ CORRECT' : '❌ INCORRECT'}`);
  console.log(`     Video Prefix: ${videoPrefixCorrect ? '✅ CORRECT' : '❌ INCORRECT'}`);
  
  // Final assessment
  console.log('\n5️⃣ Final Assessment:');
  const allCorrect = slugConsistent && coverCorrect && videoCorrect && 
                    coverDirCorrect && videoDirCorrect && 
                    coverPrefixCorrect && videoPrefixCorrect;
  
  if (allCorrect) {
    console.log('🎉 SUCCESS: Frontend course creation with organized structure is working perfectly!');
    console.log('\n📁 File Organization:');
    console.log(`   Course Directory: courses/${tempCourseSlug}/`);
    console.log(`   Cover Image: ${uploadedCover.Hashname}`);
    console.log(`   Video File: ${uploadedVideo.Hashname}`);
    console.log('\n🔧 Frontend Integration:');
    console.log('   ✅ Temp slug generation working');
    console.log('   ✅ Media upload with course-specific directories');
    console.log('   ✅ Course creation with consistent slug');
    console.log('   ✅ No media duplication');
    console.log('   ✅ Proper UUID prefixes');
  } else {
    console.log('❌ ISSUES FOUND: Some aspects need adjustment');
  }
}

testFrontendOrganizedStructure().catch(console.error);
