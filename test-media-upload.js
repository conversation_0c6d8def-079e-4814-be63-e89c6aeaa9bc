const axios = require('axios');

const BASE_URL = 'http://localhost:3200/api';

async function testMediaUploadAndCourseUpdate() {
  try {
    console.log('🧪 Testing Media Upload and Course Update Functionality\n');

    // Get initial media count
    const initialMediaResponse = await axios.get(`${BASE_URL}/medias`);
    const initialMediaCount = initialMediaResponse.data.length;
    console.log(`📊 Initial media count: ${initialMediaCount}`);

    // Step 1: Upload a test cover image using medias endpoint
    console.log('\n1️⃣ Uploading test cover image...');
    const coverImageResponse = await axios.post(`${BASE_URL}/medias`, {
      Name: `test-cover-${Date.now()}.png`,
      Hashname: "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
      Extension: "image/png",
      Size: 32,
      SubDir: "courses/covers"
    });

    console.log('✅ Cover image upload response:', {
      Id: coverImageResponse.data.Id,
      Name: coverImageResponse.data.Name,
      Hashname: coverImageResponse.data.Hashname
    });

    // Step 2: Upload a test presentation video using medias endpoint
    console.log('\n2️⃣ Uploading test presentation video...');
    const videoResponse = await axios.post(`${BASE_URL}/medias`, {
      Name: `test-video-${Date.now()}.mp4`,
      Hashname: "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
      Extension: "video/mp4",
      Size: 32,
      SubDir: "courses/videos"
    });

    console.log('✅ Video upload response:', {
      Id: videoResponse.data.Id,
      Name: videoResponse.data.Name,
      Hashname: videoResponse.data.Hashname
    });

    // Step 3: Get the latest media files to verify they're in the database
    console.log('\n3️⃣ Verifying media files in database...');
    const mediaResponse = await axios.get(`${BASE_URL}/medias`);
    const allMedia = mediaResponse.data;
    const newMediaCount = allMedia.length;

    console.log(`📊 New media count: ${newMediaCount} (added ${newMediaCount - initialMediaCount} files)`);

    // Find the uploaded files by their IDs
    const uploadedCover = coverImageResponse.data;
    const uploadedVideo = videoResponse.data;

    console.log('✅ Cover image in database:', uploadedCover ? {
      Id: uploadedCover.Id,
      Name: uploadedCover.Name,
      Hashname: uploadedCover.Hashname,
      SubDir: uploadedCover.SubDir
    } : 'NOT FOUND');

    console.log('✅ Video in database:', uploadedVideo ? {
      Id: uploadedVideo.Id,
      Name: uploadedVideo.Name,
      Hashname: uploadedVideo.Hashname,
      SubDir: uploadedVideo.SubDir
    } : 'NOT FOUND');

    // Step 4: Get a test course to update
    console.log('\n4️⃣ Getting test course...');
    const coursesResponse = await axios.get(`${BASE_URL}/courses/10/0`);
    const testCourse = coursesResponse.data[0];

    console.log('✅ Test course found:', {
      Id: testCourse.Id,
      Title: testCourse.Title,
      Slug: testCourse.Slug,
      CurrentCoverImage: testCourse.CoverImage,
      CurrentPresentationVideo: testCourse.PresentationVideo
    });

    // Step 5: Update the course with the uploaded media
    console.log('\n5️⃣ Updating course with media references...');
    const updateData = {
      ...testCourse,
      Title: testCourse.Title + ' (Updated with Media)',
      CoverImage: uploadedCover,
      PresentationVideo: uploadedVideo
    };

    console.log('📤 Update data:', {
      Title: updateData.Title,
      CoverImage: updateData.CoverImage ? { Id: updateData.CoverImage.Id, Hashname: updateData.CoverImage.Hashname } : null,
      PresentationVideo: updateData.PresentationVideo ? { Id: updateData.PresentationVideo.Id, Hashname: updateData.PresentationVideo.Hashname } : null
    });

    const updateResponse = await axios.put(`${BASE_URL}/courses/${testCourse.Slug}`, updateData);
    console.log('✅ Course update response:', updateResponse.status === 200 ? 'SUCCESS' : 'FAILED');

    // Step 6: Verify the course was updated with media references
    console.log('\n6️⃣ Verifying course update...');
    const updatedCourseResponse = await axios.get(`${BASE_URL}/courses/public/${testCourse.Slug}`);
    const updatedCourse = updatedCourseResponse.data;

    console.log('✅ Updated course verification:', {
      Id: updatedCourse.Id,
      Title: updatedCourse.Title,
      CoverImage: updatedCourse.CoverImage ? {
        Id: updatedCourse.CoverImage.Id,
        Hashname: updatedCourse.CoverImage.Hashname
      } : null,
      PresentationVideo: updatedCourse.PresentationVideo ? {
        Id: updatedCourse.PresentationVideo.Id,
        Hashname: updatedCourse.PresentationVideo.Hashname
      } : null
    });

    // Step 7: Test file accessibility
    console.log('\n7️⃣ Testing file accessibility...');
    try {
      const coverFileResponse = await axios.get(`http://localhost:3200/${uploadedCover.Hashname}`);
      console.log('✅ Cover image accessible:', coverFileResponse.status === 200 ? 'YES' : 'NO');
    } catch (error) {
      console.log('❌ Cover image accessible: NO -', error.message);
    }

    try {
      const videoFileResponse = await axios.get(`http://localhost:3200/${uploadedVideo.Hashname}`);
      console.log('✅ Video file accessible:', videoFileResponse.status === 200 ? 'YES' : 'NO');
    } catch (error) {
      console.log('❌ Video file accessible: NO -', error.message);
    }

    console.log('\n🎉 Media upload and course update test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

// Run the test
testMediaUploadAndCourseUpdate();
