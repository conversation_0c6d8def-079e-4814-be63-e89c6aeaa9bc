/**
 * Simple test to check if backend has picked up our updates
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3200/api';

async function apiRequest(method, endpoint, data = null) {
  const config = {
    method,
    url: `${API_BASE_URL}${endpoint}`,
    headers: {
      'Content-Type': 'application/json'
    }
  };
  
  if (data) {
    config.data = data;
  }
  
  try {
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`API Error (${method} ${endpoint}):`, error.response?.data || error.message);
    throw error;
  }
}

async function testBackendUpdates() {
  console.log('🔧 Testing Backend Updates');
  console.log('=' .repeat(40));
  
  // Test 1: Check media service prefix logic
  console.log('\n1️⃣ Testing media service prefix logic...');
  const testMediaData = {
    Name: 'prefix-test.png',
    Hashname: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==',
    Extension: 'image/png',
    Size: 500,
    SubDir: 'courses/test-prefix/cover'
  };
  
  console.log('📋 Uploading with SubDir:', testMediaData.SubDir);
  console.log('📋 Expected prefix: cvr_');
  
  const uploadedMedia = await apiRequest('POST', '/medias', testMediaData);
  
  console.log('✅ Media uploaded:');
  console.log('   ID:', uploadedMedia.Id);
  console.log('   Hashname:', uploadedMedia.Hashname);
  console.log('   Prefix:', uploadedMedia.Hashname.substring(0, 4));
  console.log('   SubDir:', uploadedMedia.SubDir);
  
  if (uploadedMedia.Hashname.startsWith('cvr_')) {
    console.log('✅ PREFIX CORRECT: Backend is using updated prefix logic');
  } else {
    console.log('❌ PREFIX INCORRECT: Backend may not have restarted');
    console.log('   Expected: cvr_');
    console.log('   Got:', uploadedMedia.Hashname.substring(0, 4));
  }
  
  // Test 2: Check course service slug logic
  console.log('\n2️⃣ Testing course service slug logic...');
  const testSlug = `test-slug-${Date.now()}`;
  const coursePayload = {
    body: {
      Title: 'Backend Update Test',
      Resume: 'Testing backend updates',
      Keywords: ['test'],
      Format: 2,
      Language: 'en',
      Free: true,
      Level: [1],
      Message: 'Test message',
      Congratulation: 'Test congratulation',
      Categories: [],
      Published: false,
      Archived: false,
      Slug: testSlug, // Provide custom slug
      CreatedBy: { Id: 1 }
    },
    origin: 'http://localhost:3000'
  };
  
  console.log('📋 Providing custom slug:', testSlug);
  
  const createdCourse = await apiRequest('POST', '/courses', coursePayload);
  
  console.log('✅ Course created:');
  console.log('   ID:', createdCourse.Id);
  console.log('   Title:', createdCourse.Title);
  console.log('   Provided Slug:', testSlug);
  console.log('   Actual Slug:', createdCourse.Slug);
  
  if (createdCourse.Slug === testSlug) {
    console.log('✅ SLUG CORRECT: Backend is using provided slug');
  } else {
    console.log('❌ SLUG INCORRECT: Backend may not have restarted');
    console.log('   Expected:', testSlug);
    console.log('   Got:', createdCourse.Slug);
  }
  
  // Summary
  console.log('\n📊 Summary:');
  const prefixCorrect = uploadedMedia.Hashname.startsWith('cvr_');
  const slugCorrect = createdCourse.Slug === testSlug;
  
  if (prefixCorrect && slugCorrect) {
    console.log('🎉 SUCCESS: Backend has picked up all updates!');
  } else {
    console.log('⚠️  PARTIAL SUCCESS: Some updates may not be active');
    console.log('   Prefix logic:', prefixCorrect ? '✅' : '❌');
    console.log('   Slug logic:', slugCorrect ? '✅' : '❌');
    console.log('\n💡 Suggestion: Restart the backend server to apply all changes');
  }
}

testBackendUpdates().catch(console.error);
