const axios = require('axios');

async function testEnhancedCORSFix() {
    console.log('🧪 Testing Enhanced CORS Fix with Additional Headers...\n');

    const testFiles = [
        'cvr_18af6c7318624bf885f97a4fedf09bdf-database-test-cover.png',
        'prs_4e2a2c913d30409585127e268d9eaa8f-database-test-video.mp4'
    ];

    for (const filename of testFiles) {
        console.log(`📁 Testing: ${filename}`);
        
        const fileUrl = `http://localhost:3200/upload/D2174571/content/${filename.startsWith('cvr_') ? 'coverimage' : 'presentation'}/${filename}`;
        
        try {
            const response = await axios.get(fileUrl, {
                headers: {
                    'Origin': 'http://localhost:5174',
                    'Access-Control-Request-Method': 'GET',
                    'Access-Control-Request-Headers': 'Content-Type'
                },
                timeout: 5000
            });

            console.log(`✅ Status: ${response.status}`);
            console.log(`📊 Content-Type: ${response.headers['content-type']}`);
            console.log(`🔒 CORS Headers:`);
            console.log(`   - Access-Control-Allow-Origin: ${response.headers['access-control-allow-origin'] || 'NOT SET'}`);
            console.log(`   - Access-Control-Allow-Methods: ${response.headers['access-control-allow-methods'] || 'NOT SET'}`);
            console.log(`   - Cross-Origin-Resource-Policy: ${response.headers['cross-origin-resource-policy'] || 'NOT SET'}`);
            console.log(`   - Cross-Origin-Embedder-Policy: ${response.headers['cross-origin-embedder-policy'] || 'NOT SET'}`);
            console.log(`   - Cross-Origin-Opener-Policy: ${response.headers['cross-origin-opener-policy'] || 'NOT SET'}`);
            console.log(`   - Cache-Control: ${response.headers['cache-control'] || 'NOT SET'}`);
            console.log(`📏 Content-Length: ${response.headers['content-length']} bytes`);
            
            const hasAllCORSHeaders = response.headers['access-control-allow-origin'] && 
                                    response.headers['cross-origin-resource-policy'] &&
                                    response.headers['cross-origin-embedder-policy'];
            
            if (hasAllCORSHeaders) {
                console.log(`✅ SUCCESS: File accessible with enhanced CORS headers!`);
            } else {
                console.log(`❌ FAILED: Missing some CORS headers!`);
            }
            
        } catch (error) {
            console.log(`❌ ERROR: ${error.message}`);
            if (error.response) {
                console.log(`   Status: ${error.response.status}`);
                console.log(`   Headers:`, error.response.headers);
            }
        }
        
        console.log(''); // Empty line for separation
    }
    
    console.log('🌐 Browser Test Instructions:');
    console.log('1. Clear browser cache (Ctrl+Shift+R or Cmd+Shift+R)');
    console.log('2. Open browser developer tools');
    console.log('3. Go to Network tab');
    console.log('4. Visit: http://localhost:5174/test-course-data.html');
    console.log('5. Check if media files load without OpaqueResponseBlocking errors');
}

testEnhancedCORSFix().catch(console.error);
