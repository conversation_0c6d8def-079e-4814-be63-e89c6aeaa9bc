/**
 * Debug script to check what network requests are being made
 * Run this in the browser console on the courses page
 */

console.log('🔍 Starting CourseCard Image Debug');

// Function to monitor network requests
function monitorNetworkRequests() {
    console.log('📡 Monitoring network requests...');
    
    // Override fetch to log requests
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
        const url = args[0];
        if (typeof url === 'string') {
            if (url.includes('/api/courses') || url.includes('upload/courses/covers')) {
                console.log('🌐 Network Request:', url);
            }
        }
        return originalFetch.apply(this, args);
    };
    
    // Monitor image loading
    const images = document.querySelectorAll('img');
    images.forEach((img, index) => {
        if (img.src.includes('upload/courses/covers')) {
            console.log(`🖼️ Course Image ${index + 1}:`, img.src);
            
            img.onload = () => {
                console.log(`✅ Image ${index + 1} loaded successfully:`, img.src);
            };
            
            img.onerror = () => {
                console.log(`❌ Image ${index + 1} failed to load:`, img.src);
            };
        }
    });
}

// Function to check CourseCard components
function checkCourseCards() {
    console.log('🎯 Checking CourseCard components...');
    
    // Look for course cards
    const courseCards = document.querySelectorAll('[data-testid="course-card"], .course-card, a[href*="/courses/"]');
    console.log(`📊 Found ${courseCards.length} potential course cards`);
    
    // Check for images in course cards
    const courseImages = document.querySelectorAll('img[alt*="course"], img[src*="upload/courses/covers"]');
    console.log(`🖼️ Found ${courseImages.length} course images`);
    
    courseImages.forEach((img, index) => {
        console.log(`Image ${index + 1}:`, {
            src: img.src,
            alt: img.alt,
            complete: img.complete,
            naturalWidth: img.naturalWidth,
            naturalHeight: img.naturalHeight,
            visible: img.offsetWidth > 0 && img.offsetHeight > 0
        });
    });
    
    // Check for placeholder elements
    const placeholders = document.querySelectorAll('[class*="placeholder"], [class*="fallback"]');
    console.log(`📋 Found ${placeholders.length} placeholder elements`);
}

// Function to test API data
async function testAPIData() {
    console.log('📡 Testing API data...');
    
    try {
        const response = await fetch('http://localhost:3200/api/courses/15/0?justPublished=true');
        const courses = await response.json();
        
        console.log(`✅ API returned ${courses.length} courses`);
        
        const coursesWithImages = courses.filter(course => course.CoverImage?.Hashname);
        console.log(`🖼️ Courses with cover images: ${coursesWithImages.length}`);
        
        if (coursesWithImages.length > 0) {
            const sampleCourse = coursesWithImages[0];
            console.log('📋 Sample course with image:', {
                title: sampleCourse.Title,
                coverImage: sampleCourse.CoverImage?.Hashname,
                instructorPhoto: sampleCourse.CreatedBy?.Photo?.Hashname
            });
            
            // Test if the image URL works
            const imageUrl = `http://localhost:3200/${sampleCourse.CoverImage.Hashname}`;
            console.log('🔗 Testing image URL:', imageUrl);
            
            try {
                const imgResponse = await fetch(imageUrl, { method: 'HEAD' });
                console.log(`${imgResponse.ok ? '✅' : '❌'} Image URL test:`, imgResponse.status, imgResponse.statusText);
            } catch (imgError) {
                console.log('❌ Image URL error:', imgError.message);
            }
        }
        
    } catch (error) {
        console.log('❌ API test failed:', error.message);
    }
}

// Function to simulate CourseCard logic
function simulateCourseCardLogic() {
    console.log('🎭 Simulating CourseCard logic...');
    
    // This simulates what the CourseCard component should do
    const getCourseImage = (course) => {
        if (course.CoverImage?.Hashname) {
            return course.CoverImage.Hashname;
        }
        return undefined;
    };
    
    // Test with sample data
    const sampleCourse = {
        Title: "Demo Course",
        CoverImage: {
            Hashname: "http://localhost:3200/upload/courses/covers/fdce5674de1b42b78431413ddf88c29c-abigail_journal.png"
        }
    };
    
    const imageUrl = getCourseImage(sampleCourse);
    console.log('🖼️ getCourseImage() result:', imageUrl);
    
    if (imageUrl) {
        console.log('✅ CourseCard should display image');
    } else {
        console.log('⚠️ CourseCard will show placeholder');
    }
}

// Main debug function
async function debugCourseCardImages() {
    console.log('🚀 Starting comprehensive CourseCard debug...');
    console.log('=' .repeat(50));
    
    // Wait a bit for page to load
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    checkCourseCards();
    console.log('-' .repeat(30));
    
    await testAPIData();
    console.log('-' .repeat(30));
    
    simulateCourseCardLogic();
    console.log('-' .repeat(30));
    
    monitorNetworkRequests();
    
    console.log('🎉 Debug complete! Check the logs above for issues.');
    console.log('💡 If images are still not showing:');
    console.log('   1. Check if img tags have correct src attributes');
    console.log('   2. Check if there are any CSS rules hiding images');
    console.log('   3. Check if CourseCard components are actually rendered');
    console.log('   4. Try hard refresh (Ctrl+Shift+R or Cmd+Shift+R)');
}

// Auto-run if in browser
if (typeof window !== 'undefined') {
    debugCourseCardImages();
} else {
    console.log('Run this script in the browser console on the courses page');
}

// Export for manual use
if (typeof module !== 'undefined') {
    module.exports = { debugCourseCardImages, checkCourseCards, testAPIData };
}
