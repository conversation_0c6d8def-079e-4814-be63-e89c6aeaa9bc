/**
 * Test to display all available courses from the database
 * This will show all courses with their media information for testing
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3200/api';

/**
 * Helper function to make API requests
 */
async function apiRequest(method, endpoint, data = null) {
  const config = {
    method,
    url: `${API_BASE_URL}${endpoint}`,
    headers: {
      'Content-Type': 'application/json'
    }
  };
  
  if (data) {
    config.data = data;
  }
  
  try {
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`API Error (${method} ${endpoint}):`, error.response?.data || error.message);
    throw error;
  }
}

/**
 * Fetch all courses from the database
 */
async function getAllCourses() {
  console.log('📚 Fetching all courses from database...\n');

  try {
    // Use the correct endpoint with pagination - get first 100 courses
    const take = 100;
    const skip = 0;
    const response = await apiRequest('GET', `/courses/${take}/${skip}`);

    console.log('🔍 API Response structure:', {
      type: typeof response,
      isArray: Array.isArray(response),
      keys: response ? Object.keys(response).slice(0, 10) : [], // Show first 10 keys only
      length: response?.length,
      firstItemKeys: response?.[0] ? Object.keys(response[0]).slice(0, 10) : []
    });

    // The API should return an array of courses directly
    let courses;
    if (Array.isArray(response)) {
      courses = response;
    } else if (response && Array.isArray(response.data)) {
      courses = response.data;
    } else if (response && Array.isArray(response.courses)) {
      courses = response.courses;
    } else {
      console.log('❌ Unexpected response format');
      console.log('Response type:', typeof response);
      console.log('Response preview:', JSON.stringify(response).substring(0, 200) + '...');
      return [];
    }

    if (!courses || courses.length === 0) {
      console.log('❌ No courses found in the database');
      return [];
    }

    console.log(`✅ Found ${courses.length} courses in the database\n`);

    // If we got 100 courses, there might be more - let's get the total count
    if (courses.length === take) {
      try {
        const countResponse = await apiRequest('GET', '/courses/count');
        console.log(`📊 Total courses in database: ${countResponse}`);
        console.log(`📋 Showing first ${courses.length} courses\n`);
      } catch (countError) {
        console.log('ℹ️ Could not get total count, showing first batch\n');
      }
    }

    return courses;

  } catch (error) {
    console.error('❌ Failed to fetch courses:', error.message);
    return [];
  }
}

/**
 * Display detailed course information
 */
function displayCourseDetails(course, index) {
  console.log(`📖 Course ${index + 1}: ${course.Title || 'Untitled'}`);
  console.log(`   ID: ${course.Id}`);
  console.log(`   Slug: ${course.Slug}`);
  console.log(`   Published: ${course.Published ? '✅ Yes' : '❌ No'}`);
  console.log(`   Free: ${course.Free ? '✅ Yes' : '💰 Paid'}`);
  console.log(`   Price: ${course.Price ? `$${course.Price}` : 'N/A'}`);
  console.log(`   Language: ${course.Language || 'Not specified'}`);
  console.log(`   Level: ${course.Level || 'Not specified'}`);
  console.log(`   Format: ${course.Format || 'Not specified'}`);
  
  // Cover Image Information
  if (course.CoverImage) {
    console.log(`   🖼️ Cover Image:`);
    console.log(`      ID: ${course.CoverImage.Id}`);
    console.log(`      Filename: ${course.CoverImage.Hashname}`);
    console.log(`      SubDir: ${course.CoverImage.SubDir}`);
    console.log(`      Size: ${course.CoverImage.Size} bytes`);
    console.log(`      Extension: ${course.CoverImage.Extension}`);
    
    // Construct full URL
    const coverUrl = `http://localhost:3200/${course.CoverImage.Hashname}`;
    console.log(`      URL: ${coverUrl}`);
  } else if (course.coverImageId) {
    console.log(`   🖼️ Cover Image ID: ${course.coverImageId} (Media not loaded)`);
  } else {
    console.log(`   🖼️ Cover Image: ❌ None`);
  }
  
  // Presentation Video Information
  if (course.PresentationVideo) {
    console.log(`   🎥 Presentation Video:`);
    console.log(`      ID: ${course.PresentationVideo.Id}`);
    console.log(`      Filename: ${course.PresentationVideo.Hashname}`);
    console.log(`      SubDir: ${course.PresentationVideo.SubDir}`);
    console.log(`      Size: ${course.PresentationVideo.Size} bytes`);
    console.log(`      Extension: ${course.PresentationVideo.Extension}`);
    
    // Construct full URL
    const videoUrl = `http://localhost:3200/${course.PresentationVideo.Hashname}`;
    console.log(`      URL: ${videoUrl}`);
  } else if (course.presentationVideoId) {
    console.log(`   🎥 Presentation Video ID: ${course.presentationVideoId} (Media not loaded)`);
  } else {
    console.log(`   🎥 Presentation Video: ❌ None`);
  }
  
  // Creator Information
  if (course.CreatedBy) {
    console.log(`   👤 Created By: ${course.CreatedBy.Firstname} ${course.CreatedBy.Lastname} (${course.CreatedBy.Username})`);
  } else if (course.createdById) {
    console.log(`   👤 Created By ID: ${course.createdById} (User not loaded)`);
  } else {
    console.log(`   👤 Created By: ❌ Unknown`);
  }
  
  // Description
  if (course.Resume) {
    const shortResume = course.Resume.length > 100 ? 
      course.Resume.substring(0, 100) + '...' : 
      course.Resume;
    console.log(`   📝 Description: ${shortResume}`);
  }
  
  console.log(`   🔗 Frontend URL: http://localhost:5174/courses/${course.Slug}`);
  console.log(''); // Empty line for separation
}

/**
 * Generate HTML test page with all courses
 */
function generateHTMLTestPage(courses) {
  const coursesWithMedia = courses.filter(course => 
    course.CoverImage || course.PresentationVideo || course.coverImageId || course.presentationVideoId
  );
  
  let html = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All Courses Test - Brainmaker</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .course-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px; }
        .course-card { background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .course-title { color: #2c3e50; margin-bottom: 10px; font-size: 18px; font-weight: bold; }
        .media-section { margin: 15px 0; padding: 15px; background: #f8f9fa; border-radius: 5px; }
        .media-item { margin: 10px 0; }
        .media-item img { max-width: 200px; height: auto; border-radius: 4px; }
        .media-item video { max-width: 200px; height: auto; border-radius: 4px; }
        .status { padding: 3px 8px; border-radius: 3px; font-size: 12px; }
        .status.published { background: #d4edda; color: #155724; }
        .status.draft { background: #f8d7da; color: #721c24; }
        .status.free { background: #d1ecf1; color: #0c5460; }
        .status.paid { background: #fff3cd; color: #856404; }
        .error { color: #dc3545; font-style: italic; }
        .success { color: #28a745; font-weight: bold; }
        .info { color: #17a2b8; }
        .stats { display: flex; gap: 20px; margin-bottom: 20px; }
        .stat-card { background: white; padding: 15px; border-radius: 8px; text-align: center; flex: 1; }
        .stat-number { font-size: 24px; font-weight: bold; color: #2c3e50; }
        .stat-label { color: #6c757d; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 All Courses Test - Brainmaker Database</h1>
            <p>Displaying all courses from the database with their media information</p>
            <p><strong>Backend:</strong> http://localhost:3200 | <strong>Frontend:</strong> http://localhost:5174</p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">${courses.length}</div>
                <div class="stat-label">Total Courses</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${coursesWithMedia.length}</div>
                <div class="stat-label">Courses with Media</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${courses.filter(c => c.Published).length}</div>
                <div class="stat-label">Published Courses</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${courses.filter(c => c.Free).length}</div>
                <div class="stat-label">Free Courses</div>
            </div>
        </div>
        
        <div class="course-grid">`;

  courses.forEach((course, index) => {
    const publishedStatus = course.Published ? 'published' : 'draft';
    const pricingStatus = course.Free ? 'free' : 'paid';
    
    html += `
            <div class="course-card">
                <div class="course-title">${course.Title || 'Untitled Course'}</div>
                <div style="margin-bottom: 15px;">
                    <span class="status ${publishedStatus}">${course.Published ? 'Published' : 'Draft'}</span>
                    <span class="status ${pricingStatus}">${course.Free ? 'Free' : `$${course.Price || 0}`}</span>
                </div>
                
                <div><strong>ID:</strong> ${course.Id}</div>
                <div><strong>Slug:</strong> ${course.Slug}</div>
                <div><strong>Language:</strong> ${course.Language || 'Not specified'}</div>
                
                <div class="media-section">
                    <h4>🖼️ Cover Image</h4>`;
    
    if (course.CoverImage) {
      const coverUrl = `http://localhost:3200/${course.CoverImage.Hashname}`;
      html += `
                    <div class="media-item">
                        <div class="success">✅ Available (ID: ${course.CoverImage.Id})</div>
                        <div><strong>File:</strong> ${course.CoverImage.Hashname}</div>
                        <div><strong>Size:</strong> ${course.CoverImage.Size} bytes</div>
                        <img src="${coverUrl}" alt="Cover Image" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                        <div class="error" style="display:none;">Failed to load image</div>
                    </div>`;
    } else if (course.coverImageId) {
      html += `<div class="info">📋 Cover Image ID: ${course.coverImageId} (Media not loaded in this query)</div>`;
    } else {
      html += `<div class="error">❌ No cover image</div>`;
    }
    
    html += `
                    <h4>🎥 Presentation Video</h4>`;
    
    if (course.PresentationVideo) {
      const videoUrl = `http://localhost:3200/${course.PresentationVideo.Hashname}`;
      html += `
                    <div class="media-item">
                        <div class="success">✅ Available (ID: ${course.PresentationVideo.Id})</div>
                        <div><strong>File:</strong> ${course.PresentationVideo.Hashname}</div>
                        <div><strong>Size:</strong> ${course.PresentationVideo.Size} bytes</div>
                        <video controls width="200" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                            <source src="${videoUrl}" type="video/mp4">
                            Your browser does not support the video tag.
                        </video>
                        <div class="error" style="display:none;">Failed to load video</div>
                    </div>`;
    } else if (course.presentationVideoId) {
      html += `<div class="info">📋 Presentation Video ID: ${course.presentationVideoId} (Media not loaded in this query)</div>`;
    } else {
      html += `<div class="error">❌ No presentation video</div>`;
    }
    
    html += `
                </div>
                
                <div style="margin-top: 15px;">
                    <a href="http://localhost:5174/courses/${course.Slug}" target="_blank" style="color: #007bff; text-decoration: none;">
                        🔗 View in Frontend
                    </a>
                </div>
            </div>`;
  });

  html += `
        </div>
        
        <div style="margin-top: 30px; padding: 20px; background: white; border-radius: 8px;">
            <h3>🔧 CORS Fix Status</h3>
            <div style="padding: 10px; background: #d4edda; border-radius: 4px; margin: 10px 0;">
                <strong>✅ Enhanced CORS Fix Applied:</strong> All media files should load with proper cross-origin headers.
            </div>
            <div style="padding: 10px; background: #fff3cd; border-radius: 4px; margin: 10px 0;">
                <strong>⚠️ If media files don't load:</strong> Clear browser cache (Ctrl+Shift+R) and refresh the page.
            </div>
        </div>
    </div>
</body>
</html>`;

  return html;
}

/**
 * Main test execution
 */
async function runAllCoursesTest() {
  console.log('🧪 All Courses Display Test');
  console.log('Fetching and displaying all courses from the database');
  console.log('=' .repeat(80));
  
  try {
    // Fetch all courses
    const courses = await getAllCourses();
    
    if (courses.length === 0) {
      console.log('❌ No courses found to display');
      return;
    }
    
    // Display course details in console
    console.log('📋 Course Details:\n');
    courses.forEach((course, index) => {
      displayCourseDetails(course, index);
    });
    
    // Generate HTML test page
    const html = generateHTMLTestPage(courses);
    const fs = require('fs');
    const path = require('path');
    
    const htmlPath = path.join(__dirname, 'frontend-react', 'public', 'all-courses-test.html');
    fs.writeFileSync(htmlPath, html);
    
    console.log('=' .repeat(80));
    console.log('🎉 ALL COURSES TEST COMPLETED!');
    console.log(`✅ Found and displayed ${courses.length} courses`);
    console.log(`✅ Generated HTML test page: ${htmlPath}`);
    console.log('');
    console.log('🌐 View the interactive test page at:');
    console.log('   http://localhost:5174/all-courses-test.html');
    console.log('');
    console.log('📊 Summary:');
    console.log(`   Total Courses: ${courses.length}`);
    console.log(`   Published: ${courses.filter(c => c.Published).length}`);
    console.log(`   Free: ${courses.filter(c => c.Free).length}`);
    console.log(`   With Cover Images: ${courses.filter(c => c.CoverImage || c.coverImageId).length}`);
    console.log(`   With Videos: ${courses.filter(c => c.PresentationVideo || c.presentationVideoId).length}`);
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    
    if (error.message.includes('ECONNREFUSED')) {
      console.log('\n💡 Backend server is not running. Please start it with:');
      console.log('   cd backend && npm start');
    }
  }
}

// Run the all courses test
runAllCoursesTest();
