/**
 * Test actual upload directories to see where files are being saved
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3200/api';

async function apiRequest(method, endpoint, data = null) {
  const config = {
    method,
    url: `${API_BASE_URL}${endpoint}`,
    headers: {
      'Content-Type': 'application/json'
    }
  };
  
  if (data) {
    config.data = data;
  }
  
  try {
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`API Error (${method} ${endpoint}):`, error.response?.data || error.message);
    throw error;
  }
}

async function testActualUploadDirectories() {
  console.log('🧪 Testing Actual Upload Directories');
  console.log('=' .repeat(50));
  
  // Test 1: Upload cover image with centralized directory
  console.log('\n1️⃣ Testing cover image upload...');
  const coverImageData = {
    Name: 'test-cover.png',
    Hashname: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==',
    Extension: 'image/png',
    Size: 1000,
    SubDir: 'courses/covers'  // This should create files in upload/courses/covers/
  };
  
  console.log('📋 Sending cover image data:');
  console.log('   Name:', coverImageData.Name);
  console.log('   SubDir:', coverImageData.SubDir);
  console.log('   Extension:', coverImageData.Extension);
  
  const uploadedCover = await apiRequest('POST', '/medias', coverImageData);
  
  console.log('✅ Cover image uploaded:');
  console.log('   ID:', uploadedCover.Id);
  console.log('   Hashname:', uploadedCover.Hashname);
  console.log('   SubDir:', uploadedCover.SubDir);
  console.log('   Expected prefix: cvr_');
  console.log('   Actual prefix:', uploadedCover.Hashname.substring(0, 4));
  
  // Test 2: Upload presentation video with centralized directory
  console.log('\n2️⃣ Testing presentation video upload...');
  const videoData = {
    Name: 'test-video.mp4',
    Hashname: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==',
    Extension: 'video/mp4',
    Size: 2000,
    SubDir: 'courses/videos'  // This should create files in upload/courses/videos/
  };
  
  console.log('📋 Sending video data:');
  console.log('   Name:', videoData.Name);
  console.log('   SubDir:', videoData.SubDir);
  console.log('   Extension:', videoData.Extension);
  
  const uploadedVideo = await apiRequest('POST', '/medias', videoData);
  
  console.log('✅ Video uploaded:');
  console.log('   ID:', uploadedVideo.Id);
  console.log('   Hashname:', uploadedVideo.Hashname);
  console.log('   SubDir:', uploadedVideo.SubDir);
  console.log('   Expected prefix: vid_');
  console.log('   Actual prefix:', uploadedVideo.Hashname.substring(0, 4));
  
  // Test 3: Check if files exist in expected directories
  console.log('\n3️⃣ Analysis:');
  
  if (uploadedCover.SubDir === 'courses/covers') {
    console.log('✅ Cover image: Correct SubDir (courses/covers)');
  } else {
    console.log('❌ Cover image: Wrong SubDir -', uploadedCover.SubDir);
    console.log('   Expected: courses/covers');
  }
  
  if (uploadedVideo.SubDir === 'courses/videos') {
    console.log('✅ Video: Correct SubDir (courses/videos)');
  } else {
    console.log('❌ Video: Wrong SubDir -', uploadedVideo.SubDir);
    console.log('   Expected: courses/videos');
  }
  
  if (uploadedCover.Hashname.startsWith('cvr_')) {
    console.log('✅ Cover image: Correct prefix (cvr_)');
  } else {
    console.log('❌ Cover image: Wrong prefix -', uploadedCover.Hashname.substring(0, 4));
    console.log('   Expected: cvr_');
  }
  
  if (uploadedVideo.Hashname.startsWith('vid_')) {
    console.log('✅ Video: Correct prefix (vid_)');
  } else {
    console.log('❌ Video: Wrong prefix -', uploadedVideo.Hashname.substring(0, 4));
    console.log('   Expected: vid_');
  }
  
  console.log('\n📁 Expected file locations:');
  console.log(`   Cover: backend/public/upload/courses/covers/${uploadedCover.Hashname}`);
  console.log(`   Video: backend/public/upload/courses/videos/${uploadedVideo.Hashname}`);
  
  console.log('\n🔍 If you see files in upload/images or upload/videos instead,');
  console.log('   there might be a different upload mechanism being used.');
}

testActualUploadDirectories().catch(console.error);
