-- MySQL dump 10.13  Distrib 8.0.29, for macos12 (x86_64)
--
-- Host: localhost    Database: brain-maker-db
-- ------------------------------------------------------
-- Server version	9.2.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `forumSubjects`
--

DROP TABLE IF EXISTS `forumSubjects`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `forumSubjects` (
  `Slug` varchar(36) NOT NULL,
  `CreatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `UpdatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `Id` int NOT NULL AUTO_INCREMENT,
  `Body` text NOT NULL,
  `Title` varchar(255) DEFAULT NULL,
  `Status` tinyint DEFAULT '1',
  `userId` int DEFAULT NULL,
  `courseId` int DEFAULT NULL,
  `parentId` int DEFAULT NULL,
  `acceptedAnswerId` int DEFAULT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IDX_49c4706b9c913076b1f1312993` (`Slug`),
  KEY `FK_03fa72f29bda12a732177b906d1` (`userId`),
  KEY `FK_c00779772dee93407d53cc95389` (`courseId`),
  KEY `FK_7c3f30f3d1d837d3e8498f49495` (`parentId`),
  KEY `FK_9f9272fd83b50f6449f2198d050` (`acceptedAnswerId`),
  CONSTRAINT `FK_03fa72f29bda12a732177b906d1` FOREIGN KEY (`userId`) REFERENCES `users` (`Id`) ON DELETE SET NULL,
  CONSTRAINT `FK_7c3f30f3d1d837d3e8498f49495` FOREIGN KEY (`parentId`) REFERENCES `forumSubjects` (`Id`) ON DELETE CASCADE,
  CONSTRAINT `FK_9f9272fd83b50f6449f2198d050` FOREIGN KEY (`acceptedAnswerId`) REFERENCES `forumSubjects` (`Id`),
  CONSTRAINT `FK_c00779772dee93407d53cc95389` FOREIGN KEY (`courseId`) REFERENCES `courses` (`Id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `forumSubjects`
--

LOCK TABLES `forumSubjects` WRITE;
/*!40000 ALTER TABLE `forumSubjects` DISABLE KEYS */;
INSERT INTO `forumSubjects` VALUES ('7D7C76CD','2025-05-09 21:46:40.445713','2025-05-09 21:46:40.445713',1,'<p>hoe &nbsp;can i draw a curve</p>\n','curve problem',1,3,1,NULL,NULL);
/*!40000 ALTER TABLE `forumSubjects` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-16 12:24:54
