-- MySQL dump 10.13  Distrib 8.0.29, for macos12 (x86_64)
--
-- Host: localhost    Database: brain-maker-db
-- ------------------------------------------------------
-- Server version	9.2.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `mettings`
--

DROP TABLE IF EXISTS `mettings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mettings` (
  `Slug` varchar(36) NOT NULL,
  `CreatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `UpdatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `Id` int NOT NULL AUTO_INCREMENT,
  `Topic` varchar(255) DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `HostVideo` tinyint NOT NULL DEFAULT '0',
  `GuestVideo` tinyint NOT NULL DEFAULT '0',
  `Passcode` varchar(255) DEFAULT NULL,
  `MettingId` varchar(255) NOT NULL,
  `Recurring` tinyint NOT NULL DEFAULT '0',
  `Timezone` varchar(255) DEFAULT NULL,
  `StartDate` datetime NOT NULL,
  `RealStartTime` datetime DEFAULT NULL,
  `StartTime` varchar(255) NOT NULL,
  `EndTime` datetime DEFAULT NULL,
  `TimePeriod` varchar(255) NOT NULL,
  `DurationHour` int DEFAULT NULL,
  `DurationMin` int DEFAULT NULL,
  `RequirePasscode` tinyint NOT NULL DEFAULT '0',
  `MeetingIdType` enum('personal','generated') DEFAULT NULL,
  `Status` enum('scheduled','running','finished','stoped') NOT NULL DEFAULT 'scheduled',
  `RecurringPeriod` enum('1','2','3','4') DEFAULT '2',
  `ownerId` int DEFAULT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IDX_eb71ff8df82946c2b9272b77b0` (`Slug`),
  KEY `FK_850e7b39b7415502e91552586d2` (`ownerId`),
  CONSTRAINT `FK_850e7b39b7415502e91552586d2` FOREIGN KEY (`ownerId`) REFERENCES `users` (`Id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mettings`
--

LOCK TABLES `mettings` WRITE;
/*!40000 ALTER TABLE `mettings` DISABLE KEYS */;
/*!40000 ALTER TABLE `mettings` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-16 12:24:54
