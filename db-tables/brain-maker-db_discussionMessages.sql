-- MySQL dump 10.13  Distrib 8.0.29, for macos12 (x86_64)
--
-- Host: localhost    Database: brain-maker-db
-- ------------------------------------------------------
-- Server version	9.2.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `discussionMessages`
--

DROP TABLE IF EXISTS `discussionMessages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `discussionMessages` (
  `Slug` varchar(36) NOT NULL,
  `CreatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `UpdatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `Id` int NOT NULL AUTO_INCREMENT,
  `Message` text NOT NULL,
  `Read` tinyint NOT NULL DEFAULT '0',
  `attachementId` int DEFAULT NULL,
  `sendById` int DEFAULT NULL,
  `sendToId` int DEFAULT NULL,
  `parentId` int DEFAULT NULL,
  `discussionId` int DEFAULT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IDX_645298e37df2ab2c42b41158aa` (`Slug`),
  KEY `FK_647a93b9acd9b8245bef3a5153b` (`attachementId`),
  KEY `FK_8ef2d56f16f435f4f9ab86c3c8b` (`sendById`),
  KEY `FK_14650aa2f49469c3608c9ae8406` (`sendToId`),
  KEY `FK_902f9eae2ff7941d4a8307e396d` (`parentId`),
  KEY `FK_86da8c4493b91f42f01a3e1d5de` (`discussionId`),
  CONSTRAINT `FK_14650aa2f49469c3608c9ae8406` FOREIGN KEY (`sendToId`) REFERENCES `users` (`Id`),
  CONSTRAINT `FK_647a93b9acd9b8245bef3a5153b` FOREIGN KEY (`attachementId`) REFERENCES `medias` (`Id`) ON DELETE CASCADE,
  CONSTRAINT `FK_86da8c4493b91f42f01a3e1d5de` FOREIGN KEY (`discussionId`) REFERENCES `discussions` (`Id`) ON DELETE CASCADE,
  CONSTRAINT `FK_8ef2d56f16f435f4f9ab86c3c8b` FOREIGN KEY (`sendById`) REFERENCES `users` (`Id`),
  CONSTRAINT `FK_902f9eae2ff7941d4a8307e396d` FOREIGN KEY (`parentId`) REFERENCES `discussionMessages` (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `discussionMessages`
--

LOCK TABLES `discussionMessages` WRITE;
/*!40000 ALTER TABLE `discussionMessages` DISABLE KEYS */;
/*!40000 ALTER TABLE `discussionMessages` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-16 12:24:51
