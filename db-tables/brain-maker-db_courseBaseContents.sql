-- MySQL dump 10.13  Distrib 8.0.29, for macos12 (x86_64)
--
-- Host: localhost    Database: brain-maker-db
-- ------------------------------------------------------
-- Server version	9.2.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `courseBaseContents`
--

DROP TABLE IF EXISTS `courseBaseContents`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `courseBaseContents` (
  `Slug` varchar(36) NOT NULL,
  `CreatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `UpdatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `Id` int NOT NULL AUTO_INCREMENT,
  `Title` varchar(255) NOT NULL,
  `Type` enum('1','2','3','4','6','5') NOT NULL,
  `Position` int DEFAULT NULL,
  `Content` text,
  `TotalTime` int NOT NULL DEFAULT '0',
  `TypeOfTest` enum('1','2','3','4') DEFAULT NULL,
  `YoutubeLink` text,
  `VimeoLink` text,
  `VideoType` int DEFAULT NULL,
  `EbookType` int DEFAULT NULL,
  `Discriminator` varchar(255) NOT NULL,
  `sectionId` int DEFAULT NULL,
  `mediaId` int DEFAULT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IDX_54af323279218d3726c0f0d27e` (`Slug`),
  UNIQUE KEY `REL_5562bffa2d88506c5eedf5dd95` (`mediaId`),
  KEY `IDX_3af631e1d196e3891f0177efe0` (`Discriminator`),
  KEY `FK_7d26b1c40a06cc4e7567f3480d3` (`sectionId`),
  CONSTRAINT `FK_5562bffa2d88506c5eedf5dd95e` FOREIGN KEY (`mediaId`) REFERENCES `medias` (`Id`) ON DELETE CASCADE,
  CONSTRAINT `FK_7d26b1c40a06cc4e7567f3480d3` FOREIGN KEY (`sectionId`) REFERENCES `courseSections` (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `courseBaseContents`
--

LOCK TABLES `courseBaseContents` WRITE;
/*!40000 ALTER TABLE `courseBaseContents` DISABLE KEYS */;
/*!40000 ALTER TABLE `courseBaseContents` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-16 12:24:57
