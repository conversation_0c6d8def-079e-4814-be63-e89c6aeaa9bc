-- MySQL dump 10.13  Distrib 8.0.29, for macos12 (x86_64)
--
-- Host: localhost    Database: brain-maker-db
-- ------------------------------------------------------
-- Server version	9.2.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `Slug` varchar(36) NOT NULL,
  `CreatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `UpdatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `Id` int NOT NULL AUTO_INCREMENT,
  `Username` varchar(255) NOT NULL,
  `Email` varchar(255) NOT NULL,
  `Password` varchar(255) NOT NULL,
  `Firstname` varchar(255) NOT NULL,
  `Code` varchar(255) DEFAULT NULL,
  `IsEmailConfirm` tinyint NOT NULL DEFAULT '0',
  `FacebookAddress` varchar(255) DEFAULT NULL,
  `TwitterAddress` varchar(255) DEFAULT NULL,
  `GooleAddress` varchar(255) DEFAULT NULL,
  `Country` varchar(255) DEFAULT NULL,
  `LinkedInAddress` varchar(255) DEFAULT NULL,
  `IsLogin` tinyint DEFAULT NULL,
  `LastLoginDate` datetime DEFAULT NULL,
  `Lastname` varchar(255) NOT NULL,
  `Phone` varchar(255) NOT NULL,
  `Civility` int NOT NULL,
  `Sex` int DEFAULT NULL,
  `Status` enum('3','1','2') DEFAULT '1',
  `Birthdate` datetime DEFAULT NULL,
  `About` text,
  `Profil` text,
  `Role` enum('1','2','3','4') DEFAULT '1',
  `FacturationFirstName` varchar(255) DEFAULT NULL,
  `FacturationLastname` varchar(255) DEFAULT NULL,
  `FacturationAccount` varchar(255) DEFAULT NULL,
  `FacturationAddress` varchar(255) DEFAULT NULL,
  `addressId` int DEFAULT NULL,
  `photoId` int DEFAULT NULL,
  `backgroundId` int DEFAULT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IDX_4d7092c4f622793a547c10175a` (`Slug`),
  UNIQUE KEY `IDX_8542bfce8271ad2e1f2a7d2e45` (`Username`),
  UNIQUE KEY `IDX_f73ebcea50dd1c375f20260dbe` (`Email`),
  UNIQUE KEY `IDX_813028c83f67e80f0cebc9121a` (`Phone`),
  UNIQUE KEY `IDX_9bb71946d874bb6b822a612dc5` (`Email`,`Username`,`Phone`),
  KEY `FK_bafb08f60d7857f4670c172a6ea` (`addressId`),
  KEY `FK_f856a4818b32c69dbc8811f3d2c` (`photoId`),
  KEY `FK_fdf340c33dd52301a850a944f45` (`backgroundId`),
  CONSTRAINT `FK_bafb08f60d7857f4670c172a6ea` FOREIGN KEY (`addressId`) REFERENCES `localizations` (`Id`),
  CONSTRAINT `FK_f856a4818b32c69dbc8811f3d2c` FOREIGN KEY (`photoId`) REFERENCES `medias` (`Id`),
  CONSTRAINT `FK_fdf340c33dd52301a850a944f45` FOREIGN KEY (`backgroundId`) REFERENCES `medias` (`Id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES ('DFF61FB9','2025-05-09 19:43:42.227456','2025-05-09 21:05:09.540301',1,'brainmaker-admin','<EMAIL>','$argon2id$v=19$m=65536,t=3,p=4$kpoNWCt/OYTh73VSEcBoNQ$3HoNaRs/RfC2J2GGYcB7MnV7o463kCcAvP+5FnzJrvU','BrainMaker','$argon2id$v=19$m=65536,t=3,p=4$Is2Tfl5u3UZBYbiDftyvmw$Rpfddkaqo2Nol42zC6j6j8RiDwZYhdNIIaoXVEyfDR4',1,NULL,NULL,NULL,NULL,NULL,1,NULL,'Academy','+1234567890',1,3,'1',NULL,NULL,NULL,'2',NULL,NULL,NULL,NULL,NULL,NULL,NULL),('48686082','2025-05-09 19:51:35.550510','2025-05-09 21:31:14.000000',2,'de--DE16\n      ','<EMAIL>','$argon2id$v=19$m=65536,t=3,p=4$8q3vhdz5hH01qYbynCQo5A$gNAMwXZ1O0SIoVW2yZjdU/Q3I7/ZnKNCQxw5q4fdzXo','mukwaba',NULL,1,NULL,NULL,NULL,'Angola',NULL,0,'2025-05-09 21:31:15','John baptist','+256775604482',1,1,'1',NULL,NULL,NULL,'2',NULL,NULL,NULL,NULL,NULL,NULL,NULL),('5A3E4576','2025-05-09 21:33:19.355685','2025-05-09 21:38:55.000000',3,'de-C5 6\n\n      ','<EMAIL>','$argon2id$v=19$m=65536,t=3,p=4$TBj6Y79+wJO9ec3gKclOHQ$VUMHB1+rQYlZlqGHmMElVdc/hsjGeZwGNaTjblVbM/0','jomba',NULL,1,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'creations','+256756099246',1,1,'1',NULL,NULL,NULL,'3',NULL,NULL,NULL,NULL,NULL,NULL,NULL);
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-16 12:24:51
