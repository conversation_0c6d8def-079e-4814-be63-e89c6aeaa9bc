-- MySQL dump 10.13  Distrib 8.0.29, for macos12 (x86_64)
--
-- Host: localhost    Database: brain-maker-db
-- ------------------------------------------------------
-- Server version	9.2.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `courseRatings`
--

DROP TABLE IF EXISTS `courseRatings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `courseRatings` (
  `Slug` varchar(36) NOT NULL,
  `CreatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `UpdatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `Id` int NOT NULL AUTO_INCREMENT,
  `Rating` int NOT NULL,
  `RatedOn` datetime NOT NULL,
  `courseId` int DEFAULT NULL,
  `ratedById` int DEFAULT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IDX_31e5eed940ffc0dacb302ffdda` (`Slug`),
  KEY `FK_03ab8c3a80ce6e0fcc7c94f5dff` (`courseId`),
  KEY `FK_bd18ea2bbf830c49f72a09e10e6` (`ratedById`),
  CONSTRAINT `FK_03ab8c3a80ce6e0fcc7c94f5dff` FOREIGN KEY (`courseId`) REFERENCES `courses` (`Id`) ON DELETE CASCADE,
  CONSTRAINT `FK_bd18ea2bbf830c49f72a09e10e6` FOREIGN KEY (`ratedById`) REFERENCES `users` (`Id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `courseRatings`
--

LOCK TABLES `courseRatings` WRITE;
/*!40000 ALTER TABLE `courseRatings` DISABLE KEYS */;
INSERT INTO `courseRatings` VALUES ('d98bab95b29b47fbbb35d65c6020aa8b','2025-05-09 21:44:31.530327','2025-05-09 21:44:31.530327',1,4,'2025-05-09 21:44:32',1,3);
/*!40000 ALTER TABLE `courseRatings` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-16 12:24:53
