-- MySQL dump 10.13  Distrib 8.0.29, for macos12 (x86_64)
--
-- Host: localhost    Database: brain-maker-db
-- ------------------------------------------------------
-- Server version	9.2.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `courseComments`
--

DROP TABLE IF EXISTS `courseComments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `courseComments` (
  `Slug` varchar(36) NOT NULL,
  `CreatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `UpdatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `Id` int NOT NULL AUTO_INCREMENT,
  `Comment` text NOT NULL,
  `commentedById` int DEFAULT NULL,
  `contentId` int DEFAULT NULL,
  `parentId` int DEFAULT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IDX_bd07cc746225992b04e4ae738b` (`Slug`),
  KEY `FK_c5ec514866e1c16c72cdd574639` (`commentedById`),
  KEY `FK_0e343f7ef7af9ffe7a5a860acc5` (`contentId`),
  KEY `FK_590974d93a111d040265eee5880` (`parentId`),
  CONSTRAINT `FK_0e343f7ef7af9ffe7a5a860acc5` FOREIGN KEY (`contentId`) REFERENCES `courses` (`Id`) ON DELETE CASCADE,
  CONSTRAINT `FK_590974d93a111d040265eee5880` FOREIGN KEY (`parentId`) REFERENCES `courseComments` (`Id`) ON DELETE CASCADE,
  CONSTRAINT `FK_c5ec514866e1c16c72cdd574639` FOREIGN KEY (`commentedById`) REFERENCES `users` (`Id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `courseComments`
--

LOCK TABLES `courseComments` WRITE;
/*!40000 ALTER TABLE `courseComments` DISABLE KEYS */;
INSERT INTO `courseComments` VALUES ('1dc113c2f9384081a0b5c648748f00e2','2025-05-09 21:44:23.250507','2025-05-09 21:44:23.250507',1,'I really like this course its the future ahead of time',3,1,NULL),('984c47cc954c4aa6a3a6a048c862a079','2025-05-09 21:45:06.105635','2025-05-09 21:45:06.105635',2,'the time I joined I was so green but now I believe in what I see',3,1,NULL);
/*!40000 ALTER TABLE `courseComments` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-16 12:24:56
