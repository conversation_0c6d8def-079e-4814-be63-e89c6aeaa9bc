-- MySQL dump 10.13  Distrib 8.0.29, for macos12 (x86_64)
--
-- Host: localhost    Database: brain-maker-db
-- ------------------------------------------------------
-- Server version	9.2.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `courseViews`
--

DROP TABLE IF EXISTS `courseViews`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `courseViews` (
  `Slug` varchar(36) NOT NULL,
  `CreatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `UpdatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `Id` int NOT NULL AUTO_INCREMENT,
  `Position` varchar(255) DEFAULT NULL,
  `LastPositionUpdateDate` datetime DEFAULT NULL,
  `ProgressRating` int DEFAULT NULL,
  `Paid` tinyint NOT NULL DEFAULT '0',
  `viewedById` int DEFAULT NULL,
  `contentId` int DEFAULT NULL,
  `billingId` int DEFAULT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IDX_8ae805df7d68729f227ba51fc9` (`Slug`),
  KEY `FK_835f1da04a9bd0d34762e63b867` (`viewedById`),
  KEY `FK_a3d3bc5a12c22432dc8f101035e` (`contentId`),
  KEY `FK_aee8b3ff0b62c0293d16b64da4e` (`billingId`),
  CONSTRAINT `FK_835f1da04a9bd0d34762e63b867` FOREIGN KEY (`viewedById`) REFERENCES `users` (`Id`),
  CONSTRAINT `FK_a3d3bc5a12c22432dc8f101035e` FOREIGN KEY (`contentId`) REFERENCES `courseBaseContents` (`Id`),
  CONSTRAINT `FK_aee8b3ff0b62c0293d16b64da4e` FOREIGN KEY (`billingId`) REFERENCES `billings` (`Id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `courseViews`
--

LOCK TABLES `courseViews` WRITE;
/*!40000 ALTER TABLE `courseViews` DISABLE KEYS */;
/*!40000 ALTER TABLE `courseViews` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-16 12:24:58
