-- MySQL dump 10.13  Distrib 8.0.29, for macos12 (x86_64)
--
-- Host: localhost    Database: brain-maker-db
-- ------------------------------------------------------
-- Server version	9.2.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `mettingMessages`
--

DROP TABLE IF EXISTS `mettingMessages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mettingMessages` (
  `Slug` varchar(36) NOT NULL,
  `CreatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `UpdatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `Id` int NOT NULL AUTO_INCREMENT,
  `Content` text NOT NULL,
  `UsernameMeeting` text NOT NULL,
  `Read` tinyint NOT NULL DEFAULT '0',
  `ReadAt` datetime DEFAULT NULL,
  `attachementId` int DEFAULT NULL,
  `conversationId` int DEFAULT NULL,
  `fromId` int DEFAULT NULL,
  `parentId` int DEFAULT NULL,
  `meetingId` int DEFAULT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IDX_c71ffdcc808e808e07edaac7df` (`Slug`),
  KEY `FK_ac00aae675e580e9db0a24d4e6e` (`attachementId`),
  KEY `FK_99a9fc2b19505236785a38307e4` (`conversationId`),
  KEY `FK_1296bb993d6d3f9add8d4ad236c` (`fromId`),
  KEY `FK_007f898e0ee361bfb5ce5e41464` (`parentId`),
  KEY `FK_39f46afaf6706a45c1e0eacbd4d` (`meetingId`),
  CONSTRAINT `FK_007f898e0ee361bfb5ce5e41464` FOREIGN KEY (`parentId`) REFERENCES `mettingMessages` (`Id`) ON DELETE CASCADE,
  CONSTRAINT `FK_1296bb993d6d3f9add8d4ad236c` FOREIGN KEY (`fromId`) REFERENCES `users` (`Id`),
  CONSTRAINT `FK_39f46afaf6706a45c1e0eacbd4d` FOREIGN KEY (`meetingId`) REFERENCES `mettings` (`Id`) ON DELETE CASCADE,
  CONSTRAINT `FK_99a9fc2b19505236785a38307e4` FOREIGN KEY (`conversationId`) REFERENCES `mettingConversations` (`Id`) ON DELETE CASCADE,
  CONSTRAINT `FK_ac00aae675e580e9db0a24d4e6e` FOREIGN KEY (`attachementId`) REFERENCES `medias` (`Id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mettingMessages`
--

LOCK TABLES `mettingMessages` WRITE;
/*!40000 ALTER TABLE `mettingMessages` DISABLE KEYS */;
/*!40000 ALTER TABLE `mettingMessages` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-16 12:24:55
