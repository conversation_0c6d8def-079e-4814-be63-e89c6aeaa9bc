-- MySQL dump 10.13  Distrib 8.0.29, for macos12 (x86_64)
--
-- Host: localhost    Database: brain-maker-db
-- ------------------------------------------------------
-- Server version	9.2.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `courseReports`
--

DROP TABLE IF EXISTS `courseReports`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `courseReports` (
  `Slug` varchar(36) NOT NULL,
  `CreatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `UpdatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `Id` int NOT NULL AUTO_INCREMENT,
  `Description` text,
  `Object` enum('1','2','3','4') NOT NULL,
  `Active` tinyint NOT NULL DEFAULT '1',
  `reportedById` int DEFAULT NULL,
  `courseReportedId` int DEFAULT NULL,
  `reportContentId` int DEFAULT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IDX_16b05af53b1ace3bfa69c6ab45` (`Slug`),
  KEY `FK_e63ddabb76830fd5006396ac878` (`reportedById`),
  KEY `FK_7f4fabf2e60b0d9cc7237732964` (`courseReportedId`),
  KEY `FK_c2ef58ccbaf24375aa5b6b1d2f6` (`reportContentId`),
  CONSTRAINT `FK_7f4fabf2e60b0d9cc7237732964` FOREIGN KEY (`courseReportedId`) REFERENCES `courses` (`Id`),
  CONSTRAINT `FK_c2ef58ccbaf24375aa5b6b1d2f6` FOREIGN KEY (`reportContentId`) REFERENCES `courseBaseContents` (`Id`),
  CONSTRAINT `FK_e63ddabb76830fd5006396ac878` FOREIGN KEY (`reportedById`) REFERENCES `users` (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `courseReports`
--

LOCK TABLES `courseReports` WRITE;
/*!40000 ALTER TABLE `courseReports` DISABLE KEYS */;
/*!40000 ALTER TABLE `courseReports` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-16 12:24:58
