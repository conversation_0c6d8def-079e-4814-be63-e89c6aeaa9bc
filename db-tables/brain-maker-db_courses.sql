-- MySQL dump 10.13  Distrib 8.0.29, for macos12 (x86_64)
--
-- Host: localhost    Database: brain-maker-db
-- ------------------------------------------------------
-- Server version	9.2.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `courses`
--

DROP TABLE IF EXISTS `courses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `courses` (
  `Slug` varchar(36) NOT NULL,
  `CreatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `UpdatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `Id` int NOT NULL AUTO_INCREMENT,
  `Title` varchar(255) NOT NULL,
  `Resume` text NOT NULL,
  `Keywords` text,
  `Price` int DEFAULT '0',
  `NewPrice` int DEFAULT '0',
  `Free` tinyint NOT NULL DEFAULT '0',
  `Language` varchar(255) NOT NULL,
  `Published` tinyint DEFAULT '0',
  `Archived` tinyint DEFAULT '0',
  `LastPublishedDate` datetime DEFAULT NULL,
  `Prerequisites` text,
  `Goals` text,
  `Message` varchar(255) DEFAULT NULL,
  `Format` enum('1','2','3','4','6','5') NOT NULL,
  `Congratulation` varchar(255) DEFAULT NULL,
  `Level` text,
  `Currency` varchar(255) DEFAULT NULL,
  `presentationVideoId` int DEFAULT NULL,
  `coverImageId` int DEFAULT NULL,
  `createdById` int DEFAULT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IDX_c06d7e32971476856ca7da0bfb` (`Slug`),
  UNIQUE KEY `REL_5da0009517ea4ad2d03fe6201a` (`presentationVideoId`),
  UNIQUE KEY `REL_2d96f9107af7bb2147201fa674` (`coverImageId`),
  KEY `FK_3fff66ead8c0964a1805eb194b3` (`createdById`),
  CONSTRAINT `FK_2d96f9107af7bb2147201fa6748` FOREIGN KEY (`coverImageId`) REFERENCES `medias` (`Id`) ON DELETE CASCADE,
  CONSTRAINT `FK_3fff66ead8c0964a1805eb194b3` FOREIGN KEY (`createdById`) REFERENCES `users` (`Id`) ON DELETE CASCADE,
  CONSTRAINT `FK_5da0009517ea4ad2d03fe6201a0` FOREIGN KEY (`presentationVideoId`) REFERENCES `medias` (`Id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `courses`
--

LOCK TABLES `courses` WRITE;
/*!40000 ALTER TABLE `courses` DISABLE KEYS */;
INSERT INTO `courses` VALUES ('a69c2722af5c4de29ec6cb67061695da','2025-05-09 21:38:55.647339','2025-05-09 21:41:30.000000',1,'demo','<p>this is a demo couse</p>\n','professionals',70,60,0,'en',1,0,'2025-05-09 21:41:30','dlmlkvnmklm vaoitjhoisdj vjdlsiogo;dsg','dhlsdj. sad;jidld','welcome to this course','2','songs','2,3,1,4','CA$',NULL,2,3);
/*!40000 ALTER TABLE `courses` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-16 12:24:57
