/**
 * Test the actual course creation flow to verify organized directory structure
 * This simulates exactly what the frontend does when creating a course
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3200/api';

async function apiRequest(method, endpoint, data = null) {
  const config = {
    method,
    url: `${API_BASE_URL}${endpoint}`,
    headers: {
      'Content-Type': 'application/json'
    }
  };
  
  if (data) {
    config.data = data;
  }
  
  try {
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`API Error (${method} ${endpoint}):`, error.response?.data || error.message);
    throw error;
  }
}

async function testActualCourseCreationFlow() {
  console.log('🎓 Testing Actual Course Creation Flow');
  console.log('Simulating exactly what the frontend UnifiedCourseCreator does');
  console.log('=' .repeat(70));
  
  // Step 1: Generate temporary course slug (like frontend does)
  const tempCourseSlug = `temp-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
  const userSlug = 'D2174571'; // Existing user
  
  console.log(`📋 Frontend Simulation Setup:`);
  console.log(`   Temp Course Slug: ${tempCourseSlug}`);
  console.log(`   User Slug: ${userSlug}`);
  
  // Step 2: Upload cover image (simulating frontend media service)
  console.log('\n1️⃣ Simulating frontend cover image upload...');
  console.log(`   Context: 'course-cover'`);
  console.log(`   CourseSlug: ${tempCourseSlug}`);
  console.log(`   Expected SubDir: courses/${tempCourseSlug}/cover`);
  
  const coverImageData = {
    Name: `${Date.now()}-test-cover.png`,
    Hashname: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==',
    Extension: 'image/png',
    Size: 1500,
    SubDir: `courses/${tempCourseSlug}/cover` // This is what frontend media service should generate
  };
  
  const uploadedCover = await apiRequest('POST', '/medias', coverImageData);
  
  console.log('✅ Cover image uploaded:');
  console.log(`   ID: ${uploadedCover.Id}`);
  console.log(`   Hashname: ${uploadedCover.Hashname}`);
  console.log(`   SubDir: ${uploadedCover.SubDir}`);
  console.log(`   Prefix: ${uploadedCover.Hashname.substring(0, 4)} (expected: cvr_)`);
  
  // Step 3: Upload presentation video (simulating frontend media service)
  console.log('\n2️⃣ Simulating frontend video upload...');
  console.log(`   Context: 'course-video'`);
  console.log(`   CourseSlug: ${tempCourseSlug}`);
  console.log(`   Expected SubDir: courses/${tempCourseSlug}/video`);
  
  const videoData = {
    Name: `${Date.now()}-test-video.mp4`,
    Hashname: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==',
    Extension: 'video/mp4',
    Size: 5000,
    SubDir: `courses/${tempCourseSlug}/video` // This is what frontend media service should generate
  };
  
  const uploadedVideo = await apiRequest('POST', '/medias', videoData);
  
  console.log('✅ Video uploaded:');
  console.log(`   ID: ${uploadedVideo.Id}`);
  console.log(`   Hashname: ${uploadedVideo.Hashname}`);
  console.log(`   SubDir: ${uploadedVideo.SubDir}`);
  console.log(`   Prefix: ${uploadedVideo.Hashname.substring(0, 4)} (expected: vid_)`);
  
  // Step 4: Create course (simulating frontend course creator)
  console.log('\n3️⃣ Simulating frontend course creation...');
  console.log(`   Using coverImageId: ${uploadedCover.Id}`);
  console.log(`   Using presentationVideoId: ${uploadedVideo.Id}`);
  console.log(`   Using temp slug: ${tempCourseSlug}`);
  
  const coursePayload = {
    body: {
      Title: 'Actual Course Creation Flow Test',
      Resume: 'Testing the actual course creation flow with organized directory structure',
      Keywords: ['test', 'organized', 'actual'],
      Prerequisites: [],
      Goals: [],
      Format: 2,
      Language: 'en',
      Free: true,
      Level: [1],
      Message: 'Welcome to the test course',
      Congratulation: 'Congratulations on completing the test',
      Categories: [],
      Published: false,
      Archived: false,
      // Frontend passes media IDs and temp slug
      coverImageId: uploadedCover.Id,
      presentationVideoId: uploadedVideo.Id,
      Slug: tempCourseSlug, // Frontend uses the same temp slug
      CreatedBy: { Id: 1 }
    },
    origin: 'http://localhost:3000'
  };
  
  const createdCourse = await apiRequest('POST', '/courses', coursePayload);
  
  console.log('✅ Course created:');
  console.log(`   ID: ${createdCourse.Id}`);
  console.log(`   Title: ${createdCourse.Title}`);
  console.log(`   Slug: ${createdCourse.Slug}`);
  
  // Step 5: Verify the complete organized structure
  console.log('\n4️⃣ Verifying organized directory structure...');
  const retrievedCourse = await apiRequest('GET', `/courses/${createdCourse.Id}`);
  
  console.log('📊 Organized Structure Analysis:');
  
  // Check if course uses the correct slug
  const slugCorrect = createdCourse.Slug === tempCourseSlug;
  console.log(`   Slug Consistency: ${slugCorrect ? '✅ CORRECT' : '❌ INCORRECT'}`);
  console.log(`     Expected: ${tempCourseSlug}`);
  console.log(`     Got: ${createdCourse.Slug}`);
  
  // Check media references and directories
  const coverCorrect = retrievedCourse.CoverImage?.Id === uploadedCover.Id;
  const videoCorrect = retrievedCourse.PresentationVideo?.Id === uploadedVideo.Id;
  
  console.log(`   Cover Image Reference: ${coverCorrect ? '✅ CORRECT' : '❌ INCORRECT'}`);
  if (retrievedCourse.CoverImage) {
    console.log(`     ID: ${retrievedCourse.CoverImage.Id} (expected: ${uploadedCover.Id})`);
    console.log(`     SubDir: ${retrievedCourse.CoverImage.SubDir}`);
    console.log(`     Hashname: ${retrievedCourse.CoverImage.Hashname}`);
  }
  
  console.log(`   Video Reference: ${videoCorrect ? '✅ CORRECT' : '❌ INCORRECT'}`);
  if (retrievedCourse.PresentationVideo) {
    console.log(`     ID: ${retrievedCourse.PresentationVideo.Id} (expected: ${uploadedVideo.Id})`);
    console.log(`     SubDir: ${retrievedCourse.PresentationVideo.SubDir}`);
    console.log(`     Hashname: ${retrievedCourse.PresentationVideo.Hashname}`);
  }
  
  // Check directory structure compliance
  const expectedCoverDir = `courses/${tempCourseSlug}/cover`;
  const expectedVideoDir = `courses/${tempCourseSlug}/video`;
  
  const coverDirCorrect = uploadedCover.SubDir === expectedCoverDir;
  const videoDirCorrect = uploadedVideo.SubDir === expectedVideoDir;
  const coverPrefixCorrect = uploadedCover.Hashname.startsWith('cvr_');
  const videoPrefixCorrect = uploadedVideo.Hashname.startsWith('vid_');
  
  console.log(`   Directory Structure:`);
  console.log(`     Cover Directory: ${coverDirCorrect ? '✅ CORRECT' : '❌ INCORRECT'}`);
  console.log(`       Expected: ${expectedCoverDir}`);
  console.log(`       Got: ${uploadedCover.SubDir}`);
  console.log(`     Video Directory: ${videoDirCorrect ? '✅ CORRECT' : '❌ INCORRECT'}`);
  console.log(`       Expected: ${expectedVideoDir}`);
  console.log(`       Got: ${uploadedVideo.SubDir}`);
  console.log(`     Cover Prefix: ${coverPrefixCorrect ? '✅ CORRECT' : '❌ INCORRECT'} (${uploadedCover.Hashname.substring(0, 4)})`);
  console.log(`     Video Prefix: ${videoPrefixCorrect ? '✅ CORRECT' : '❌ INCORRECT'} (${uploadedVideo.Hashname.substring(0, 4)})`);
  
  // Final assessment
  console.log('\n5️⃣ Final Assessment:');
  const allCorrect = slugCorrect && coverCorrect && videoCorrect && 
                    coverDirCorrect && videoDirCorrect && 
                    coverPrefixCorrect && videoPrefixCorrect;
  
  if (allCorrect) {
    console.log('🎉 SUCCESS: Organized directory structure is working perfectly!');
    console.log('\n📁 Organized File Structure:');
    console.log(`   Course Directory: courses/${tempCourseSlug}/`);
    console.log(`   ├── cover/`);
    console.log(`   │   └── ${uploadedCover.Hashname}`);
    console.log(`   └── video/`);
    console.log(`       └── ${uploadedVideo.Hashname}`);
    console.log('\n🔧 Implementation Status:');
    console.log('   ✅ Temporary slug generation');
    console.log('   ✅ Course-specific directory structure');
    console.log('   ✅ Proper UUID prefixes');
    console.log('   ✅ No media duplication');
    console.log('   ✅ Consistent slug usage');
    console.log('\n🚀 Ready for frontend integration!');
  } else {
    console.log('❌ ISSUES FOUND: Some aspects need adjustment');
    console.log('\n🔧 Issues to fix:');
    if (!slugCorrect) console.log('   - Slug consistency');
    if (!coverCorrect || !videoCorrect) console.log('   - Media references');
    if (!coverDirCorrect || !videoDirCorrect) console.log('   - Directory structure');
    if (!coverPrefixCorrect || !videoPrefixCorrect) console.log('   - UUID prefixes');
  }
  
  return {
    success: allCorrect,
    courseId: createdCourse.Id,
    courseSlug: createdCourse.Slug,
    coverImageId: uploadedCover.Id,
    videoId: uploadedVideo.Id
  };
}

testActualCourseCreationFlow().catch(console.error);
