#!/bin/bash
set -e

# Configuration
DEPLOY_DIR="/var/www/backend"
BACKUP_DIR="/var/www/backups/backend"
TIMESTAMP=$(date +"%Y%m%d%H%M%S")

echo "Starting deployment process..."

# Create backup directory if it doesn't exist
mkdir -p $BACKUP_DIR

# Create backup of current deployment
if [ -d "$DEPLOY_DIR" ]; then
  echo "Creating backup of current deployment..."
  tar -czf "$BACKUP_DIR/backup-$TIMESTAMP.tar.gz" -C "$DEPLOY_DIR" .
  echo "Backup created at $BACKUP_DIR/backup-$TIMESTAMP.tar.gz"
fi

# Create deployment directory if it doesn't exist
mkdir -p $DEPLOY_DIR

# Stop the current application if it's running
if [ -f "$DEPLOY_DIR/stop-prod.sh" ]; then
  echo "Stopping current application..."
  cd $DEPLOY_DIR && bash stop-prod.sh
fi

# Copy files to deployment directory
echo "Copying files to deployment directory..."
cp -r dist package.json package-lock.json .env.prod start-prod.sh stop-prod.sh ormconfig.js $DEPLOY_DIR/

# Make scripts executable
echo "Making scripts executable..."
chmod +x $DEPLOY_DIR/start-prod.sh $DEPLOY_DIR/stop-prod.sh

# Install production dependencies
echo "Installing production dependencies..."
cd $DEPLOY_DIR && npm install --production

# Start the application
echo "Starting application..."
cd $DEPLOY_DIR && bash start-prod.sh

echo "Deployment completed successfully!"
