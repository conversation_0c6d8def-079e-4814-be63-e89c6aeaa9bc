ACCESS_TOKEN_SECRET=brainmaker_secure_token_key_2024
ACCESS_TOKEN_LIFE=3650
ACCESS_TOKEN=Bearer token
PORT=3200
NODE_ENV=production
PUBLIC_DIR=/public
DOMAIN=https://api.brainmaker.academy
CILENT=https://brainmaker.academy
FILE=.env.prod

SOCKET_URL=https://server.brainmaker.academy

PROD_DB_USERNAME=brainmaker
PROD_DB_PASSWORD=elsys20202
PROD_DB_NAME=brainmaker
PROD_DB_HOSTNAME=localhost
PROD_DB_DIALECT=mysql
PROD_DB_PORT=3306
PROD_DB_SYNCHRONIZE=false
PROD_DB_DROPSCHEMA=false
PROD_DB_LOGGING=true

# Mail settings - Update these with your actual mail service credentials
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
mail_username=<EMAIL>
MAIL_NAME=BrainMaker Academy
MAIL_PASSWORD=your-app-password
mail_sender=<EMAIL>
MAIL_SECURE=true

# SMS settings - Update these with your actual SMS service credentials
SMS_HTTP=https://api.yoursmsservice.com
SMS_API=your-sms-api-token

# Additional settings
BASE_URL=https://api.brainmaker.academy
FRONTEND_URL=https://brainmaker.academy
ALLOWED_ORIGINS=https://brainmaker.academy,https://api.brainmaker.academy,https://server.brainmaker.academy
