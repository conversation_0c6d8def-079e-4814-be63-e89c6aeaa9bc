{"analysis": {"courseColumns": [{"COLUMN_NAME": "Slug", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "UNI"}, {"COLUMN_NAME": "CreatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED", "COLUMN_KEY": ""}, {"COLUMN_NAME": "UpdatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP(6)", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Id", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "auto_increment", "COLUMN_KEY": "PRI"}, {"COLUMN_NAME": "Title", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Resume", "DATA_TYPE": "text", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Keywords", "DATA_TYPE": "text", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Price", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": "0", "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "NewPrice", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": "0", "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Free", "DATA_TYPE": "tinyint", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "0", "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Language", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Published", "DATA_TYPE": "tinyint", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": "0", "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Archived", "DATA_TYPE": "tinyint", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": "0", "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "LastPublishedDate", "DATA_TYPE": "datetime", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Prerequisites", "DATA_TYPE": "text", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Goals", "DATA_TYPE": "text", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Message", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Format", "DATA_TYPE": "enum", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Congratulation", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Level", "DATA_TYPE": "text", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "<PERSON><PERSON><PERSON><PERSON>", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "presentationVideoId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "UNI"}, {"COLUMN_NAME": "coverImageId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "UNI"}, {"COLUMN_NAME": "createdById", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}], "relatedTables": {"courses": [{"COLUMN_NAME": "Slug", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "UNI"}, {"COLUMN_NAME": "CreatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED", "COLUMN_KEY": ""}, {"COLUMN_NAME": "UpdatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP(6)", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Id", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "auto_increment", "COLUMN_KEY": "PRI"}, {"COLUMN_NAME": "Title", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Resume", "DATA_TYPE": "text", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Keywords", "DATA_TYPE": "text", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Price", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": "0", "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "NewPrice", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": "0", "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Free", "DATA_TYPE": "tinyint", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "0", "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Language", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Published", "DATA_TYPE": "tinyint", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": "0", "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Archived", "DATA_TYPE": "tinyint", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": "0", "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "LastPublishedDate", "DATA_TYPE": "datetime", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Prerequisites", "DATA_TYPE": "text", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Goals", "DATA_TYPE": "text", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Message", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Format", "DATA_TYPE": "enum", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Congratulation", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Level", "DATA_TYPE": "text", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "<PERSON><PERSON><PERSON><PERSON>", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "presentationVideoId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "UNI"}, {"COLUMN_NAME": "coverImageId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "UNI"}, {"COLUMN_NAME": "createdById", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}], "courseSections": [{"COLUMN_NAME": "Slug", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "UNI"}, {"COLUMN_NAME": "CreatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED", "COLUMN_KEY": ""}, {"COLUMN_NAME": "UpdatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP(6)", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Id", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "auto_increment", "COLUMN_KEY": "PRI"}, {"COLUMN_NAME": "Title", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Description", "DATA_TYPE": "text", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Position", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": "0", "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "courseId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}, {"COLUMN_NAME": "Order", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "0", "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "IsActive", "DATA_TYPE": "tinyint", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "1", "EXTRA": "", "COLUMN_KEY": ""}], "courseBaseContents": [{"COLUMN_NAME": "Slug", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "UNI"}, {"COLUMN_NAME": "CreatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED", "COLUMN_KEY": ""}, {"COLUMN_NAME": "UpdatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP(6)", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Id", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "auto_increment", "COLUMN_KEY": "PRI"}, {"COLUMN_NAME": "Title", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Type", "DATA_TYPE": "enum", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Position", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Content", "DATA_TYPE": "text", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "TotalTime", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "0", "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "TypeOfTest", "DATA_TYPE": "enum", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "YoutubeLink", "DATA_TYPE": "text", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "VimeoLink", "DATA_TYPE": "text", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "VideoType", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "EbookType", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "sectionId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}, {"COLUMN_NAME": "mediaId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}, {"COLUMN_NAME": "Discriminator", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Description", "DATA_TYPE": "text", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Duration", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Order", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "0", "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "IsActive", "DATA_TYPE": "tinyint", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "1", "EXTRA": "", "COLUMN_KEY": ""}], "courseContents": [{"COLUMN_NAME": "Slug", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "UNI"}, {"COLUMN_NAME": "CreatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED", "COLUMN_KEY": ""}, {"COLUMN_NAME": "UpdatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP(6)", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Id", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "auto_increment", "COLUMN_KEY": "PRI"}, {"COLUMN_NAME": "Title", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Description", "DATA_TYPE": "text", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Type", "DATA_TYPE": "enum", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Position", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": "0", "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Duration", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": "0", "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Preview", "DATA_TYPE": "tinyint", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": "0", "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "sectionId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}, {"COLUMN_NAME": "mediaId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}], "courseTranscriptions": [{"COLUMN_NAME": "Slug", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "UNI"}, {"COLUMN_NAME": "CreatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED", "COLUMN_KEY": ""}, {"COLUMN_NAME": "UpdatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP(6)", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Id", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "auto_increment", "COLUMN_KEY": "PRI"}, {"COLUMN_NAME": "Language", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "en", "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "mediaId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}, {"COLUMN_NAME": "contentId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}], "courseRatings": [{"COLUMN_NAME": "Slug", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "UNI"}, {"COLUMN_NAME": "CreatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED", "COLUMN_KEY": ""}, {"COLUMN_NAME": "UpdatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP(6)", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Id", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "auto_increment", "COLUMN_KEY": "PRI"}, {"COLUMN_NAME": "Rating", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Comment", "DATA_TYPE": "text", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "ratedById", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}, {"COLUMN_NAME": "courseId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}, {"COLUMN_NAME": "Review", "DATA_TYPE": "text", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "userId", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}], "courseComments": [{"COLUMN_NAME": "Slug", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "UNI"}, {"COLUMN_NAME": "CreatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED", "COLUMN_KEY": ""}, {"COLUMN_NAME": "UpdatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP(6)", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Id", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "auto_increment", "COLUMN_KEY": "PRI"}, {"COLUMN_NAME": "Comment", "DATA_TYPE": "text", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "userId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}, {"COLUMN_NAME": "courseId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}, {"COLUMN_NAME": "parentId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}, {"COLUMN_NAME": "Content", "DATA_TYPE": "text", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "IsActive", "DATA_TYPE": "tinyint", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "1", "EXTRA": "", "COLUMN_KEY": ""}], "courseOpinions": [{"COLUMN_NAME": "Slug", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "UNI"}, {"COLUMN_NAME": "CreatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED", "COLUMN_KEY": ""}, {"COLUMN_NAME": "UpdatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP(6)", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Id", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "auto_increment", "COLUMN_KEY": "PRI"}, {"COLUMN_NAME": "Type", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "courseId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}, {"COLUMN_NAME": "userId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}], "courseNotes": [{"COLUMN_NAME": "Slug", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "UNI"}, {"COLUMN_NAME": "CreatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED", "COLUMN_KEY": ""}, {"COLUMN_NAME": "UpdatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP(6)", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Id", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "auto_increment", "COLUMN_KEY": "PRI"}, {"COLUMN_NAME": "Content", "DATA_TYPE": "text", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "userId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}, {"COLUMN_NAME": "courseId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}], "coursesSaved": [{"COLUMN_NAME": "Slug", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "UNI"}, {"COLUMN_NAME": "CreatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED", "COLUMN_KEY": ""}, {"COLUMN_NAME": "UpdatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP(6)", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Id", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "auto_increment", "COLUMN_KEY": "PRI"}, {"COLUMN_NAME": "userId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}, {"COLUMN_NAME": "courseId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}], "courseContacts": [{"COLUMN_NAME": "Slug", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "UNI"}, {"COLUMN_NAME": "CreatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED", "COLUMN_KEY": ""}, {"COLUMN_NAME": "UpdatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP(6)", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Id", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "auto_increment", "COLUMN_KEY": "PRI"}, {"COLUMN_NAME": "Message", "DATA_TYPE": "text", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "courseId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}, {"COLUMN_NAME": "sendToId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}], "courseReports": [{"COLUMN_NAME": "Slug", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "UNI"}, {"COLUMN_NAME": "CreatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED", "COLUMN_KEY": ""}, {"COLUMN_NAME": "UpdatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP(6)", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Id", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "auto_increment", "COLUMN_KEY": "PRI"}, {"COLUMN_NAME": "Description", "DATA_TYPE": "text", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Object", "DATA_TYPE": "enum", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Active", "DATA_TYPE": "tinyint", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "1", "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "reportedById", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}, {"COLUMN_NAME": "courseReportedId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}, {"COLUMN_NAME": "reportContentId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}], "courseNotifications": [{"COLUMN_NAME": "Slug", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "UNI"}, {"COLUMN_NAME": "CreatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED", "COLUMN_KEY": ""}, {"COLUMN_NAME": "UpdatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP(6)", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Id", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "auto_increment", "COLUMN_KEY": "PRI"}, {"COLUMN_NAME": "Title", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Content", "DATA_TYPE": "text", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Type", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Read", "DATA_TYPE": "tinyint", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": "0", "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "courseId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}, {"COLUMN_NAME": "userId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}], "courseViews": [{"COLUMN_NAME": "Slug", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "UNI"}, {"COLUMN_NAME": "CreatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED", "COLUMN_KEY": ""}, {"COLUMN_NAME": "UpdatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP(6)", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Id", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "auto_increment", "COLUMN_KEY": "PRI"}, {"COLUMN_NAME": "Position", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "LastPositionUpdateDate", "DATA_TYPE": "datetime", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "ProgressRating", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Paid", "DATA_TYPE": "tinyint", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": "0", "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "viewedById", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}, {"COLUMN_NAME": "contentId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}, {"COLUMN_NAME": "billingId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}], "courseHistories": [{"COLUMN_NAME": "Id", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "auto_increment", "COLUMN_KEY": "PRI"}, {"COLUMN_NAME": "StartDate", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "EndDate", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "contentId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}, {"COLUMN_NAME": "viewedById", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}], "courseAnswers": [{"COLUMN_NAME": "Slug", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "UNI"}, {"COLUMN_NAME": "CreatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED", "COLUMN_KEY": ""}, {"COLUMN_NAME": "UpdatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP(6)", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Id", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "auto_increment", "COLUMN_KEY": "PRI"}, {"COLUMN_NAME": "Rate", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Content", "DATA_TYPE": "text", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "sectionTestId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}, {"COLUMN_NAME": "userId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}], "courseQuestions": [{"COLUMN_NAME": "Slug", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "UNI"}, {"COLUMN_NAME": "CreatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED", "COLUMN_KEY": ""}, {"COLUMN_NAME": "UpdatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP(6)", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Id", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "auto_increment", "COLUMN_KEY": "PRI"}, {"COLUMN_NAME": "Title", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "IsRightAnswer", "DATA_TYPE": "tinyint", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "0", "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "sectionTestId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}], "courseSectionTests": [{"COLUMN_NAME": "Id", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "PRI"}, {"COLUMN_NAME": "TypeOfTest", "DATA_TYPE": "enum", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}], "courseAssignments": [{"COLUMN_NAME": "Id", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "PRI"}, {"COLUMN_NAME": "Description", "DATA_TYPE": "text", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "DueDate", "DATA_TYPE": "datetime", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}], "courseCommentRating": [{"COLUMN_NAME": "Slug", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "UNI"}, {"COLUMN_NAME": "CreatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED", "COLUMN_KEY": ""}, {"COLUMN_NAME": "UpdatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP(6)", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Id", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "auto_increment", "COLUMN_KEY": "PRI"}, {"COLUMN_NAME": "Rating", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "courseCommentId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}, {"COLUMN_NAME": "userId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}], "courseCommentOpinions": [{"COLUMN_NAME": "Slug", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "UNI"}, {"COLUMN_NAME": "CreatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED", "COLUMN_KEY": ""}, {"COLUMN_NAME": "UpdatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP(6)", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Id", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "auto_increment", "COLUMN_KEY": "PRI"}, {"COLUMN_NAME": "Type", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "courseCommentId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}, {"COLUMN_NAME": "userId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}]}, "relationships": {"billings_courses_courses": [{"TABLE_NAME": "billings_courses_courses", "COLUMN_NAME": "billingsId", "CONSTRAINT_NAME": "FK_c7a1c6d7a1f3f1c1a1f1c1a1f25", "REFERENCED_TABLE_NAME": "billings", "REFERENCED_COLUMN_NAME": "Id"}, {"TABLE_NAME": "billings_courses_courses", "COLUMN_NAME": "coursesId", "CONSTRAINT_NAME": "FK_c7a1c6d7a1f3f1c1a1f1c1a1f26", "REFERENCED_TABLE_NAME": "courses", "REFERENCED_COLUMN_NAME": "Id"}], "course_base_contents_medias_medias": [{"TABLE_NAME": "course_base_contents_medias_medias", "COLUMN_NAME": "courseBaseContentsId", "CONSTRAINT_NAME": "FK_course_base_contents_medias_medias_courseBaseContentsId", "REFERENCED_TABLE_NAME": "courseBaseContents", "REFERENCED_COLUMN_NAME": "Id"}, {"TABLE_NAME": "course_base_contents_medias_medias", "COLUMN_NAME": "mediasId", "CONSTRAINT_NAME": "FK_course_base_contents_medias_medias_mediasId", "REFERENCED_TABLE_NAME": "medias", "REFERENCED_COLUMN_NAME": "Id"}], "course_notifications_read_by_users": [{"TABLE_NAME": "course_notifications_read_by_users", "COLUMN_NAME": "courseNotificationsId", "CONSTRAINT_NAME": "FK_c7a1c6d7a1f3f1c1a1f1c1a1f71", "REFERENCED_TABLE_NAME": "courseNotifications", "REFERENCED_COLUMN_NAME": "Id"}, {"TABLE_NAME": "course_notifications_read_by_users", "COLUMN_NAME": "usersId", "CONSTRAINT_NAME": "FK_c7a1c6d7a1f3f1c1a1f1c1a1f72", "REFERENCED_TABLE_NAME": "users", "REFERENCED_COLUMN_NAME": "Id"}], "courseAnswers": [{"TABLE_NAME": "courseAnswers", "COLUMN_NAME": "sectionTestId", "CONSTRAINT_NAME": "FK_courseAnswers_sectionTest", "REFERENCED_TABLE_NAME": "courseBaseContents", "REFERENCED_COLUMN_NAME": "Id"}, {"TABLE_NAME": "courseAnswers", "COLUMN_NAME": "userId", "CONSTRAINT_NAME": "FK_courseAnswers_user", "REFERENCED_TABLE_NAME": "users", "REFERENCED_COLUMN_NAME": "Id"}], "courseAssignments": [{"TABLE_NAME": "courseAssignments", "COLUMN_NAME": "Id", "CONSTRAINT_NAME": "FK_courseAssignments_courseBaseContents", "REFERENCED_TABLE_NAME": "courseBaseContents", "REFERENCED_COLUMN_NAME": "Id"}], "courseBaseContents": [{"TABLE_NAME": "courseBaseContents", "COLUMN_NAME": "mediaId", "CONSTRAINT_NAME": "FK_courseBaseContents_media", "REFERENCED_TABLE_NAME": "medias", "REFERENCED_COLUMN_NAME": "Id"}, {"TABLE_NAME": "courseBaseContents", "COLUMN_NAME": "sectionId", "CONSTRAINT_NAME": "FK_courseBaseContents_section", "REFERENCED_TABLE_NAME": "courseSections", "REFERENCED_COLUMN_NAME": "Id"}], "courseCommentOpinions": [{"TABLE_NAME": "courseCommentOpinions", "COLUMN_NAME": "courseCommentId", "CONSTRAINT_NAME": "FK_8f28a06287c873eb7ae6949ba64", "REFERENCED_TABLE_NAME": "courseComments", "REFERENCED_COLUMN_NAME": "Id"}, {"TABLE_NAME": "courseCommentOpinions", "COLUMN_NAME": "userId", "CONSTRAINT_NAME": "FK_9368c76da7e3a4515f3ac62110b", "REFERENCED_TABLE_NAME": "users", "REFERENCED_COLUMN_NAME": "Id"}], "courseCommentRating": [{"TABLE_NAME": "courseCommentRating", "COLUMN_NAME": "courseCommentId", "CONSTRAINT_NAME": "FK_4111564ea10a15e1399b1c20e14", "REFERENCED_TABLE_NAME": "courseComments", "REFERENCED_COLUMN_NAME": "Id"}, {"TABLE_NAME": "courseCommentRating", "COLUMN_NAME": "userId", "CONSTRAINT_NAME": "FK_421214b8837802a65418ff71b62", "REFERENCED_TABLE_NAME": "users", "REFERENCED_COLUMN_NAME": "Id"}], "courseComments": [{"TABLE_NAME": "courseComments", "COLUMN_NAME": "courseId", "CONSTRAINT_NAME": "FK_c7a1c6d7a1f3f1c1a1f1c1a1f36", "REFERENCED_TABLE_NAME": "courses", "REFERENCED_COLUMN_NAME": "Id"}, {"TABLE_NAME": "courseComments", "COLUMN_NAME": "parentId", "CONSTRAINT_NAME": "FK_c7a1c6d7a1f3f1c1a1f1c1a1f37", "REFERENCED_TABLE_NAME": "courseComments", "REFERENCED_COLUMN_NAME": "Id"}, {"TABLE_NAME": "courseComments", "COLUMN_NAME": "userId", "CONSTRAINT_NAME": "FK_c7a1c6d7a1f3f1c1a1f1c1a1f35", "REFERENCED_TABLE_NAME": "users", "REFERENCED_COLUMN_NAME": "Id"}], "courseContacts": [{"TABLE_NAME": "courseContacts", "COLUMN_NAME": "courseId", "CONSTRAINT_NAME": "FK_f91b2c64eef19611f9af06cb612", "REFERENCED_TABLE_NAME": "courses", "REFERENCED_COLUMN_NAME": "Id"}, {"TABLE_NAME": "courseContacts", "COLUMN_NAME": "sendToId", "CONSTRAINT_NAME": "FK_7a9869b0a33c065e065d8cbeba9", "REFERENCED_TABLE_NAME": "users", "REFERENCED_COLUMN_NAME": "Id"}], "courseContents": [{"TABLE_NAME": "courseContents", "COLUMN_NAME": "mediaId", "CONSTRAINT_NAME": "FK_c7a1c6d7a1f3f1c1a1f1c1a1f19", "REFERENCED_TABLE_NAME": "medias", "REFERENCED_COLUMN_NAME": "Id"}, {"TABLE_NAME": "courseContents", "COLUMN_NAME": "sectionId", "CONSTRAINT_NAME": "FK_c7a1c6d7a1f3f1c1a1f1c1a1f18", "REFERENCED_TABLE_NAME": "courseSections", "REFERENCED_COLUMN_NAME": "Id"}], "courseHistories": [{"TABLE_NAME": "courseHistories", "COLUMN_NAME": "contentId", "CONSTRAINT_NAME": "FK_courseHistories_content", "REFERENCED_TABLE_NAME": "courseBaseContents", "REFERENCED_COLUMN_NAME": "Id"}, {"TABLE_NAME": "courseHistories", "COLUMN_NAME": "viewedById", "CONSTRAINT_NAME": "FK_courseHistories_viewedBy", "REFERENCED_TABLE_NAME": "users", "REFERENCED_COLUMN_NAME": "Id"}], "courseNotes": [{"TABLE_NAME": "courseNotes", "COLUMN_NAME": "courseId", "CONSTRAINT_NAME": "FK_c7a1c6d7a1f3f1c1a1f1c1a1f40", "REFERENCED_TABLE_NAME": "courses", "REFERENCED_COLUMN_NAME": "Id"}, {"TABLE_NAME": "courseNotes", "COLUMN_NAME": "userId", "CONSTRAINT_NAME": "FK_c7a1c6d7a1f3f1c1a1f1c1a1f39", "REFERENCED_TABLE_NAME": "users", "REFERENCED_COLUMN_NAME": "Id"}], "courseNotifications": [{"TABLE_NAME": "courseNotifications", "COLUMN_NAME": "courseId", "CONSTRAINT_NAME": "FK_c7a1c6d7a1f3f1c1a1f1c1a1f68", "REFERENCED_TABLE_NAME": "courses", "REFERENCED_COLUMN_NAME": "Id"}, {"TABLE_NAME": "courseNotifications", "COLUMN_NAME": "userId", "CONSTRAINT_NAME": "FK_c7a1c6d7a1f3f1c1a1f1c1a1f69", "REFERENCED_TABLE_NAME": "users", "REFERENCED_COLUMN_NAME": "Id"}], "courseOpinions": [{"TABLE_NAME": "courseOpinions", "COLUMN_NAME": "courseId", "CONSTRAINT_NAME": "FK_b82af9916cd0aa54368d6f00e6e", "REFERENCED_TABLE_NAME": "courses", "REFERENCED_COLUMN_NAME": "Id"}, {"TABLE_NAME": "courseOpinions", "COLUMN_NAME": "userId", "CONSTRAINT_NAME": "FK_0850b6eac7da43bd757916e25e9", "REFERENCED_TABLE_NAME": "users", "REFERENCED_COLUMN_NAME": "Id"}], "courseQuestions": [{"TABLE_NAME": "courseQuestions", "COLUMN_NAME": "sectionTestId", "CONSTRAINT_NAME": "FK_courseQuestions_sectionTest", "REFERENCED_TABLE_NAME": "courseBaseContents", "REFERENCED_COLUMN_NAME": "Id"}], "courseRatings": [{"TABLE_NAME": "courseRatings", "COLUMN_NAME": "courseId", "CONSTRAINT_NAME": "FK_c7a1c6d7a1f3f1c1a1f1c1a1f33", "REFERENCED_TABLE_NAME": "courses", "REFERENCED_COLUMN_NAME": "Id"}, {"TABLE_NAME": "courseRatings", "COLUMN_NAME": "ratedById", "CONSTRAINT_NAME": "FK_c7a1c6d7a1f3f1c1a1f1c1a1f32", "REFERENCED_TABLE_NAME": "users", "REFERENCED_COLUMN_NAME": "Id"}], "courseReports": [{"TABLE_NAME": "courseReports", "COLUMN_NAME": "courseReportedId", "CONSTRAINT_NAME": "FK_courseReports_courseReported", "REFERENCED_TABLE_NAME": "courses", "REFERENCED_COLUMN_NAME": "Id"}, {"TABLE_NAME": "courseReports", "COLUMN_NAME": "reportContentId", "CONSTRAINT_NAME": "FK_courseReports_reportContent", "REFERENCED_TABLE_NAME": "courseBaseContents", "REFERENCED_COLUMN_NAME": "Id"}, {"TABLE_NAME": "courseReports", "COLUMN_NAME": "reportedById", "CONSTRAINT_NAME": "FK_courseReports_reportedBy", "REFERENCED_TABLE_NAME": "users", "REFERENCED_COLUMN_NAME": "Id"}], "courses": [{"TABLE_NAME": "courses", "COLUMN_NAME": "coverImageId", "CONSTRAINT_NAME": "FK_2d96f9107af7bb2147201fa6748", "REFERENCED_TABLE_NAME": "medias", "REFERENCED_COLUMN_NAME": "Id"}, {"TABLE_NAME": "courses", "COLUMN_NAME": "createdById", "CONSTRAINT_NAME": "FK_3fff66ead8c0964a1805eb194b3", "REFERENCED_TABLE_NAME": "users", "REFERENCED_COLUMN_NAME": "Id"}, {"TABLE_NAME": "courses", "COLUMN_NAME": "presentationVideoId", "CONSTRAINT_NAME": "FK_5da0009517ea4ad2d03fe6201a0", "REFERENCED_TABLE_NAME": "medias", "REFERENCED_COLUMN_NAME": "Id"}], "courses_categories_categories": [{"TABLE_NAME": "courses_categories_categories", "COLUMN_NAME": "categoriesId", "CONSTRAINT_NAME": "FK_c7a1c6d7a1f3f1c1a1f1c1a1f11", "REFERENCED_TABLE_NAME": "categories", "REFERENCED_COLUMN_NAME": "Id"}, {"TABLE_NAME": "courses_categories_categories", "COLUMN_NAME": "coursesId", "CONSTRAINT_NAME": "FK_c7a1c6d7a1f3f1c1a1f1c1a1f10", "REFERENCED_TABLE_NAME": "courses", "REFERENCED_COLUMN_NAME": "Id"}], "courseSections": [{"TABLE_NAME": "courseSections", "COLUMN_NAME": "courseId", "CONSTRAINT_NAME": "FK_c7a1c6d7a1f3f1c1a1f1c1a1f16", "REFERENCED_TABLE_NAME": "courses", "REFERENCED_COLUMN_NAME": "Id"}], "courseSectionTests": [{"TABLE_NAME": "courseSectionTests", "COLUMN_NAME": "Id", "CONSTRAINT_NAME": "FK_courseSectionTests_courseBaseContents", "REFERENCED_TABLE_NAME": "courseBaseContents", "REFERENCED_COLUMN_NAME": "Id"}], "coursesSaved": [{"TABLE_NAME": "coursesSaved", "COLUMN_NAME": "courseId", "CONSTRAINT_NAME": "FK_c7a1c6d7a1f3f1c1a1f1c1a1f43", "REFERENCED_TABLE_NAME": "courses", "REFERENCED_COLUMN_NAME": "Id"}, {"TABLE_NAME": "coursesSaved", "COLUMN_NAME": "userId", "CONSTRAINT_NAME": "FK_c7a1c6d7a1f3f1c1a1f1c1a1f42", "REFERENCED_TABLE_NAME": "users", "REFERENCED_COLUMN_NAME": "Id"}], "courseTranscriptions": [{"TABLE_NAME": "courseTranscriptions", "COLUMN_NAME": "contentId", "CONSTRAINT_NAME": "FK_b71ca7d2463d1012036ca1d8952", "REFERENCED_TABLE_NAME": "courseBaseContents", "REFERENCED_COLUMN_NAME": "Id"}, {"TABLE_NAME": "courseTranscriptions", "COLUMN_NAME": "mediaId", "CONSTRAINT_NAME": "FK_95a2d3ebe1f4fd9c12e592268d4", "REFERENCED_TABLE_NAME": "medias", "REFERENCED_COLUMN_NAME": "Id"}], "courseViews": [{"TABLE_NAME": "courseViews", "COLUMN_NAME": "billingId", "CONSTRAINT_NAME": "FK_c7a1c6d7a1f3f1c1a1f1c1a1f30", "REFERENCED_TABLE_NAME": "billings", "REFERENCED_COLUMN_NAME": "Id"}, {"TABLE_NAME": "courseViews", "COLUMN_NAME": "contentId", "CONSTRAINT_NAME": "FK_c7a1c6d7a1f3f1c1a1f1c1a1f29", "REFERENCED_TABLE_NAME": "courseBaseContents", "REFERENCED_COLUMN_NAME": "Id"}, {"TABLE_NAME": "courseViews", "COLUMN_NAME": "viewedById", "CONSTRAINT_NAME": "FK_c7a1c6d7a1f3f1c1a1f1c1a1f28", "REFERENCED_TABLE_NAME": "users", "REFERENCED_COLUMN_NAME": "Id"}], "forumSubjects": [{"TABLE_NAME": "forumSubjects", "COLUMN_NAME": "courseId", "CONSTRAINT_NAME": "FK_forumSubjects_course", "REFERENCED_TABLE_NAME": "courses", "REFERENCED_COLUMN_NAME": "Id"}], "orders_courses_courses": [{"TABLE_NAME": "orders_courses_courses", "COLUMN_NAME": "coursesId", "CONSTRAINT_NAME": "FK_c7a1c6d7a1f3f1c1a1f1c1a1f53", "REFERENCED_TABLE_NAME": "courses", "REFERENCED_COLUMN_NAME": "Id"}, {"TABLE_NAME": "orders_courses_courses", "COLUMN_NAME": "ordersId", "CONSTRAINT_NAME": "FK_c7a1c6d7a1f3f1c1a1f1c1a1f52", "REFERENCED_TABLE_NAME": "orders", "REFERENCED_COLUMN_NAME": "Id"}]}}, "courseCreationSchema": {"basicInfo": {"title": {"type": "string", "required": true, "maxLength": 255}, "resume": {"type": "text", "required": true}, "keywords": {"type": "array", "required": false}, "language": {"type": "string", "required": true, "default": "en"}, "level": {"type": "array", "required": true}, "format": {"type": "enum", "required": true, "values": ["video", "text", "mixed"]}, "message": {"type": "text", "required": false}, "congratulation": {"type": "text", "required": false}}, "pricing": {"price": {"type": "decimal", "required": true, "min": 0}, "newPrice": {"type": "decimal", "required": false, "min": 0}, "currency": {"type": "string", "required": true, "default": "USD"}, "free": {"type": "boolean", "required": true, "default": false}}, "content": {"prerequisites": {"type": "array", "required": false}, "goals": {"type": "array", "required": true}, "sections": {"type": "array", "required": true, "minItems": 1}}, "media": {"coverImageId": {"type": "number", "required": false}, "presentationVideoId": {"type": "number", "required": false}}, "categorization": {"categoryIds": {"type": "array", "required": true, "minItems": 1}}, "settings": {"published": {"type": "boolean", "required": true, "default": false}, "archived": {"type": "boolean", "required": true, "default": false}}}}