{"name": "brain-maker", "version": "0.0.1", "description": "Api brain-maker", "author": "Datnek", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "start": "node dist/main.js", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "NODE_ENV=production node dist/main.js", "deploy": "npm run build && chmod +x ./start-prod.sh && ./start-prod.sh"}, "dependencies": {"@nestjs/common": "^8.4.7", "@nestjs/config": "^2.3.1", "@nestjs/core": "^8.4.7", "@nestjs/platform-express": "^8.0.0", "@nestjs/platform-socket.io": "^8.4.7", "@nestjs/schedule": "^2.2.1", "@nestjs/serve-static": "^3.0.1", "@nestjs/websockets": "^8.4.7", "adm-zip": "^0.5.10", "axios": "^1.3.5", "bcrypt": "^6.0.0", "emailjs": "^4.0.1", "express-easy-zip": "^1.1.5", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "mysql2": "^3.14.1", "puppeteer": "^24.10.2", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.8.0", "slim-exp": "^0.3.2", "socket.io": "^4.6.1", "socket.io-client": "^4.6.1", "typeorm-linq-repository": "^1.1.1"}, "devDependencies": {"@nestjs/cli": "^8.2.8", "@nestjs/common": "^8.4.7", "@nestjs/schematics": "^8.0.0", "@nestjs/swagger": "^6.3.0", "@nestjs/testing": "^8.0.0", "@nestjs/typeorm": "^7.1.4", "@nestjsx/crud": "^4.6.2", "@nestjsx/crud-typeorm": "^4.6.2", "@types/cron": "^2.0.1", "@types/express": "^4.17.13", "@types/jest": "27.5.0", "@types/node": "^16.0.0", "@types/supertest": "^2.0.11", "@types/uuid": "^8.3.0", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "argon2": "^0.30.3", "atob": "^2.1.2", "class-transformer": "^0.3.1", "class-validator": "^0.12.2", "cross-env": "^7.0.2", "crypto": "^1.0.1", "crypto-js": "^4.0.0", "deep-extend": "^0.6.0", "dotenv": "^8.6.0", "eslint": "^8.0.1", "eslint-config-airbnb-base": "^14.2.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-unused-imports": "^1.1.0", "jest": "28.0.3", "jsonwebtoken": "^8.5.1", "mysql": "^2.18.1", "nodemailer": "^6.7.5", "nodemon": "^2.0.5", "passport-jwt": "^4.0.0", "prettier": "^2.3.2", "source-map-support": "^0.5.20", "start-server-webpack-plugin": "^2.2.5", "supertest": "^6.1.3", "swagger-ui-express": "^4.4.0", "ts-jest": "28.0.1", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.0.0", "typeorm": "^0.2.32", "typescript": "^4.3.5", "uuid": "^8.3.1", "webpack-node-externals": "^3.0.0", "websockets": "^0.2.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}