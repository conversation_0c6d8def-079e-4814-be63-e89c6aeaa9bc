module.exports = {
  apps: [{
    name: 'brainmaker-api',
    script: 'dist/main.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    min_uptime: '10s',
    max_restarts: 10,
    restart_delay: 4000,

    // Environment variables
    env: {
      NODE_ENV: 'production',
      PORT: 3200
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3200,
      NODE_OPTIONS: '--max-old-space-size=2048'
    },

    // Logging
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    error_file: 'logs/error.log',
    out_file: 'logs/output.log',
    log_file: 'logs/combined.log',
    merge_logs: true,

    // Process management
    kill_timeout: 5000,
    listen_timeout: 3000,

    // Monitoring
    pmx: true,
    source_map_support: true,
    instance_var: 'INSTANCE_ID'
  }],

  // Deployment configuration
  deploy: {
    production: {
      user: 'root',
      host: 'your-vps-ip',
      ref: 'origin/main',
      repo: 'your-git-repo',
      path: '/var/www/brainmaker-backend',
      'post-deploy': 'npm ci --production && pm2 reload ecosystem.config.js --env production'
    }
  }
};
