/**
 * Secure Secrets Management for BrainMaker Academy
 * Handles all sensitive configuration data securely
 */

import * as crypto from 'crypto';

export class SecretsConfig {
  private static instance: SecretsConfig;

  constructor() {
    this.validateRequiredSecrets();
  }

  public static getInstance(): SecretsConfig {
    if (!SecretsConfig.instance) {
      SecretsConfig.instance = new SecretsConfig();
    }
    return SecretsConfig.instance;
  }

  /**
   * Validate that all required secrets are present
   */
  private validateRequiredSecrets(): void {
    const requiredSecrets = [
      'ACCESS_TOKEN_SECRET',
      'REFRESH_TOKEN_SECRET',
      'ENCRYPTION_KEY'
    ];

    const missingSecrets = requiredSecrets.filter(secret => !process.env[secret]);
    
    if (missingSecrets.length > 0) {
      console.error('❌ Missing required secrets:', missingSecrets);
      console.log('🔧 Please set the following environment variables:');
      missingSecrets.forEach(secret => {
        console.log(`   ${secret}=${this.generateSecureSecret()}`);
      });
      
      // In development, auto-generate missing secrets
      if (process.env.NODE_ENV === 'development') {
        console.log('🔄 Auto-generating secrets for development...');
        missingSecrets.forEach(secret => {
          process.env[secret] = this.generateSecureSecret();
        });
      } else {
        throw new Error('Missing required secrets in production environment');
      }
    }
  }

  /**
   * Generate a cryptographically secure secret
   */
  public generateSecureSecret(length: number = 64): string {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * Generate a secure token for user sessions
   */
  public generateSecureToken(length: number = 32): string {
    return crypto.randomBytes(length).toString('base64url');
  }

  /**
   * Generate a secure slug
   */
  public generateSecureSlug(): string {
    const timestamp = Date.now().toString(36);
    const randomPart = crypto.randomBytes(8).toString('base64url');
    return `${timestamp}-${randomPart}`;
  }

  /**
   * Hash sensitive data
   */
  public hashSensitiveData(data: string): string {
    const salt = crypto.randomBytes(16).toString('hex');
    const hash = crypto.pbkdf2Sync(data, salt, 10000, 64, 'sha512').toString('hex');
    return `${salt}:${hash}`;
  }

  /**
   * Verify hashed data
   */
  public verifySensitiveData(data: string, hashedData: string): boolean {
    const [salt, hash] = hashedData.split(':');
    const verifyHash = crypto.pbkdf2Sync(data, salt, 10000, 64, 'sha512').toString('hex');
    return hash === verifyHash;
  }

  /**
   * Get JWT configuration
   */
  public getJWTConfig() {
    return {
      accessTokenSecret: process.env.ACCESS_TOKEN_SECRET!,
      refreshTokenSecret: process.env.REFRESH_TOKEN_SECRET!,
      accessTokenExpiry: process.env.ACCESS_TOKEN_EXPIRY || '24h',
      refreshTokenExpiry: process.env.REFRESH_TOKEN_EXPIRY || '7d',
      issuer: process.env.JWT_ISSUER || 'brainmaker.academy',
      audience: process.env.JWT_AUDIENCE || 'brainmaker-users'
    };
  }

  /**
   * Get encryption configuration
   */
  public getEncryptionConfig() {
    return {
      algorithm: 'aes-256-gcm',
      key: process.env.ENCRYPTION_KEY!,
      keyLength: 32,
      ivLength: 16,
      tagLength: 16
    };
  }

  /**
   * Encrypt sensitive data using AES-256-CBC (modern, secure method)
   */
  public encrypt(text: string): string {
    const algorithm = 'aes-256-cbc';
    const key = crypto.scryptSync(process.env.ENCRYPTION_KEY!, 'salt', 32);
    const iv = crypto.randomBytes(16);

    const cipher = crypto.createCipheriv(algorithm, key, iv);
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    return `${iv.toString('hex')}:${encrypted}`;
  }

  /**
   * Decrypt sensitive data using AES-256-CBC (modern, secure method)
   */
  public decrypt(encryptedText: string): string {
    const algorithm = 'aes-256-cbc';
    const key = crypto.scryptSync(process.env.ENCRYPTION_KEY!, 'salt', 32);
    const [ivHex, encrypted] = encryptedText.split(':');
    const iv = Buffer.from(ivHex, 'hex');

    const decipher = crypto.createDecipheriv(algorithm, key, iv);
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  }

  /**
   * Generate API key for external integrations
   */
  public generateAPIKey(): string {
    const prefix = 'bm_';
    const timestamp = Date.now().toString(36);
    const randomPart = crypto.randomBytes(24).toString('base64url');
    return `${prefix}${timestamp}_${randomPart}`;
  }

  /**
   * Generate secure password reset token
   */
  public generatePasswordResetToken(): {
    token: string;
    hashedToken: string;
    expiresAt: Date;
  } {
    const token = this.generateSecureToken(32);
    const hashedToken = this.hashSensitiveData(token);
    const expiresAt = new Date(Date.now() + 3600000); // 1 hour

    return { token, hashedToken, expiresAt };
  }

  /**
   * Generate secure email verification token
   */
  public generateEmailVerificationToken(): {
    token: string;
    hashedToken: string;
    expiresAt: Date;
  } {
    const token = this.generateSecureToken(16);
    const hashedToken = this.hashSensitiveData(token);
    const expiresAt = new Date(Date.now() + 86400000); // 24 hours

    return { token, hashedToken, expiresAt };
  }

  /**
   * Sanitize sensitive data for logging
   */
  public sanitizeForLogging(data: any): any {
    const sensitiveFields = [
      'password', 'token', 'secret', 'key', 'hash',
      'privatecode', 'email', 'phone', 'ssn'
    ];

    if (typeof data === 'string') {
      return '***REDACTED***';
    }

    if (typeof data === 'object' && data !== null) {
      const sanitized = { ...data };
      
      Object.keys(sanitized).forEach(key => {
        const lowerKey = key.toLowerCase();
        if (sensitiveFields.some(field => lowerKey.includes(field))) {
          sanitized[key] = '***REDACTED***';
        } else if (typeof sanitized[key] === 'object') {
          sanitized[key] = this.sanitizeForLogging(sanitized[key]);
        }
      });

      return sanitized;
    }

    return data;
  }

  /**
   * Get rate limiting configuration
   */
  public getRateLimitConfig() {
    return {
      windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
      maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
      skipSuccessfulRequests: process.env.RATE_LIMIT_SKIP_SUCCESS === 'true',
      skipFailedRequests: process.env.RATE_LIMIT_SKIP_FAILED === 'false'
    };
  }
}

// Export singleton instance
export const secretsConfig = SecretsConfig.getInstance();
