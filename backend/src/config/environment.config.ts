/**
 * Dynamic Environment Configuration
 * Automatically switches between development and production settings
 *
 * PORT ALLOCATION:
 * - Frontend-React: 5174 (STRICT - Reserved for React app)
 * - Backend API: 3200 (NestJS application)
 * - Socket.io Server: 9353 (Real-time communication)
 */

export class EnvironmentConfig {
  private static instance: EnvironmentConfig;
  private environment: string;

  constructor() {
    this.environment = process.env.NODE_ENV || 'development';
    this.setDynamicEnvironmentVariables();
  }

  public static getInstance(): EnvironmentConfig {
    if (!EnvironmentConfig.instance) {
      EnvironmentConfig.instance = new EnvironmentConfig();
    }
    return EnvironmentConfig.instance;
  }

  private setDynamicEnvironmentVariables(): void {
    const isDevelopment = this.environment === 'development';
    const isProduction = this.environment === 'production';

    // Set dynamic URLs based on environment
    if (isDevelopment) {
      process.env.DOMAIN = 'http://localhost:3200'; // Backend API port
      process.env.CILENT = 'http://localhost:5174'; // STRICT: Frontend-React port
      process.env.SOCKET_URL = 'http://localhost:9353'; // Socket.io server port
      process.env.BASE_URL = 'http://localhost:3200'; // Backend API port
      process.env.FRONTEND_URL = 'http://localhost:5174'; // STRICT: Frontend-React port
      process.env.ALLOWED_ORIGINS = 'http://localhost:5174,http://localhost:4020,http://localhost:3200';
    } else if (isProduction) {
      process.env.DOMAIN = 'https://api.brainmaker.academy';
      process.env.CILENT = 'https://brainmaker.academy';
      process.env.SOCKET_URL = 'https://server.brainmaker.academy';
      process.env.BASE_URL = 'https://api.brainmaker.academy';
      process.env.FRONTEND_URL = 'https://brainmaker.academy';
      process.env.ALLOWED_ORIGINS = 'https://brainmaker.academy,https://api.brainmaker.academy,https://server.brainmaker.academy';
    }

    console.log(`🌍 Environment: ${this.environment}`);
    console.log(`🔗 API Domain: ${process.env.DOMAIN}`);
    console.log(`🌐 Frontend URL: ${process.env.FRONTEND_URL}`);
    console.log(`⚡ Socket URL: ${process.env.SOCKET_URL}`);
  }

  public getEnvironment(): string {
    return this.environment;
  }

  public isDevelopment(): boolean {
    return this.environment === 'development';
  }

  public isProduction(): boolean {
    return this.environment === 'production';
  }

  public getConfig() {
    return {
      environment: this.environment,
      port: process.env.PORT || 3200,
      domain: process.env.DOMAIN,
      client: process.env.CILENT,
      socketUrl: process.env.SOCKET_URL,
      baseUrl: process.env.BASE_URL,
      frontendUrl: process.env.FRONTEND_URL,
      allowedOrigins: process.env.ALLOWED_ORIGINS?.split(',') || [],
      database: this.getDatabaseConfig(),
      mail: this.getMailConfig(),
    };
  }

  private getDatabaseConfig() {
    const isProduction = this.isProduction();
    const prefix = isProduction ? 'PROD_DB_' : 'DEV_DB_';

    return {
      username: process.env[`${prefix}USERNAME`],
      password: process.env[`${prefix}PASSWORD`],
      database: process.env[`${prefix}NAME`],
      host: process.env[`${prefix}HOSTNAME`],
      port: parseInt(process.env[`${prefix}PORT`] || '3306'),
      type: process.env[`${prefix}DIALECT`] || 'mysql',
      synchronize: process.env[`${prefix}SYNCHRONIZE`] === 'true',
      dropSchema: process.env[`${prefix}DROPSCHEMA`] === 'true',
      logging: process.env[`${prefix}LOGGING`] === 'true',
    };
  }

  private getMailConfig() {
    return {
      host: process.env.MAIL_HOST,
      port: parseInt(process.env.MAIL_PORT || '587'),
      username: process.env.mail_username,
      password: process.env.MAIL_PASSWORD,
      name: process.env.MAIL_NAME,
      sender: process.env.mail_sender,
      secure: process.env.MAIL_SECURE === 'true',
    };
  }
}

// Initialize environment configuration
export const environmentConfig = EnvironmentConfig.getInstance();
