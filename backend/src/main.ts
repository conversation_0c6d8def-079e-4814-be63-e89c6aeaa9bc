import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { environmentConfig } from './config/environment.config';
import * as express from 'express';
import { ValidationPipe } from '@nestjs/common';

async function bootstrap() {
  // Initialize dynamic environment configuration
  const config = environmentConfig.getConfig();
  
  console.log('🚀 Starting BrainMaker Backend...');
  console.log(`🌍 Environment: ${config.environment}`);
  console.log(`🔗 Domain: ${config.domain}`);
  console.log(`🌐 Frontend: ${config.frontendUrl}`);
  console.log(`⚡ Socket.io: ${config.socketUrl}`);
  console.log(`🗄️ Database: ${config.database.host}:${config.database.port}/${config.database.database}`);

  const app = await NestFactory.create(AppModule);

  // Configure body parser limits for file uploads
  app.use(express.json({ limit: '100mb' }));
  app.use(express.urlencoded({ limit: '100mb', extended: true }));

  // Global validation pipe
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true,
    forbidNonWhitelisted: true,
    transform: true,
  }));

  // Enable CORS with dynamic origins
  app.enableCors({
    origin: config.allowedOrigins,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'Accept',
      'X-Client-Version',
      'X-Requested-With',
      'X-Timestamp',
      'X-CSRF-Token',
      'X-Request-ID',
      'Origin'
    ],
  });

  // Set global prefix
  app.setGlobalPrefix('api');

  const port = config.port;
  await app.listen(port);

  console.log(`✅ Backend running on: ${config.domain}`);
  console.log(`📡 API available at: ${config.domain}/api`);
}

bootstrap().catch((error) => {
  console.error('❌ Failed to start backend:', error);
  process.exit(1);
});
