#!/bin/bash
set -e

echo "Stopping backend application..."

# Check if PID file exists
if [ -f .pid ]; then
  PID=$(cat .pid)
  
  # Check if process is running
  if ps -p $PID > /dev/null; then
    echo "Stopping process with PID: $PID"
    kill $PID
    
    # Wait for process to stop
    for i in {1..10}; do
      if ! ps -p $PID > /dev/null; then
        echo "Process stopped successfully."
        rm .pid
        exit 0
      fi
      echo "Waiting for process to stop... ($i/10)"
      sleep 1
    done
    
    # Force kill if process is still running
    echo "Process did not stop gracefully. Force killing..."
    kill -9 $PID
    if ! ps -p $PID > /dev/null; then
      echo "Process force killed successfully."
      rm .pid
      exit 0
    else
      echo "Error: Failed to kill process with PID: $PID"
      exit 1
    fi
  else
    echo "No running process found with PID: $PID"
    rm .pid
  fi
else
  echo "No PID file found. Checking for running Node.js processes..."
  
  # Find Node.js processes running main.js
  NODE_PIDS=$(ps aux | grep "[n]ode.*main.js" | awk '{print $2}')
  
  if [ -n "$NODE_PIDS" ]; then
    echo "Found Node.js processes: $NODE_PIDS"
    for PID in $NODE_PIDS; do
      echo "Stopping process with PID: $PID"
      kill $PID
    done
    echo "All processes stopped."
  else
    echo "No running Node.js processes found."
  fi
fi

echo "Backend application stopped."
