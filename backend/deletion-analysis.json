{"safeToDelete": [], "needsInvestigation": [], "keepTables": [{"name": "billings", "rowCount": 0, "hasReferences": true, "hasEntity": true, "reason": "Has entity file, 99 code references, Referenced by 3 tables"}, {"name": "categories", "rowCount": 0, "hasReferences": true, "hasEntity": true, "reason": "Has entity file, 105 code references, Referenced by 5 tables"}, {"name": "comments", "rowCount": 0, "hasReferences": true, "hasEntity": true, "reason": "Has entity file, 165 code references, Referenced by 2 tables"}, {"name": "courses", "rowCount": 0, "hasReferences": true, "hasEntity": true, "reason": "Has entity file, 435 code references, Referenced by 13 tables"}, {"name": "discussions", "rowCount": 0, "hasReferences": true, "hasEntity": true, "reason": "Has entity file, 45 code references, Referenced by 1 tables"}, {"name": "localizations", "rowCount": 0, "hasReferences": true, "hasEntity": true, "reason": "Has entity file, 42 code references, Referenced by 2 tables"}, {"name": "medias", "rowCount": 0, "hasReferences": true, "hasEntity": true, "reason": "Has entity file, 267 code references, Referenced by 13 tables"}, {"name": "mettings", "rowCount": 0, "hasReferences": true, "hasEntity": true, "reason": "Has entity file, 69 code references, Referenced by 4 tables"}, {"name": "Notifications", "rowCount": 0, "hasReferences": true, "hasEntity": true, "reason": "Has entity file, 60 code references, Referenced by 2 tables, System table"}, {"name": "opinions", "rowCount": 0, "hasReferences": true, "hasEntity": true, "reason": "Has entity file, 123 code references"}, {"name": "orders", "rowCount": 0, "hasReferences": true, "hasEntity": true, "reason": "Has entity file, 93 code references, Referenced by 2 tables"}, {"name": "posts", "rowCount": 0, "hasReferences": true, "hasEntity": true, "reason": "Has entity file, 126 code references, Referenced by 5 tables"}, {"name": "rates", "rowCount": 0, "hasReferences": true, "hasEntity": true, "reason": "Has entity file, 54 code references"}, {"name": "requests", "rowCount": 0, "hasReferences": true, "hasEntity": true, "reason": "Has entity file, 27 code references"}, {"name": "sections", "rowCount": 0, "hasReferences": true, "hasEntity": true, "reason": "Has entity file, 174 code references"}, {"name": "securities", "rowCount": 3, "hasReferences": true, "hasEntity": true, "reason": "Has entity file, 141 code references, System table"}, {"name": "users", "rowCount": 3, "hasReferences": true, "hasEntity": true, "reason": "Has entity file, 678 code references, Referenced by 37 tables, System table"}, {"name": "abonnementForfaits", "rowCount": 3, "hasReferences": true, "hasEntity": false, "reason": "21 code references, Referenced by 2 tables"}, {"name": "abonnements", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "48 code references, Referenced by 1 tables"}, {"name": "billings_courses_courses", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "1 code references"}, {"name": "billingSettings", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "39 code references, Referenced by 1 tables"}, {"name": "course_base_contents_medias_medias", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "1 code references"}, {"name": "course_notifications_read_by_users", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "1 code references"}, {"name": "courseAnswers", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "42 code references"}, {"name": "courseAssignments", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "21 code references"}, {"name": "courseBaseContents", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "33 code references, Referenced by 9 tables"}, {"name": "courseCommentOpinions", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "33 code references"}, {"name": "courseCommentRating", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "51 code references"}, {"name": "courseComments", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "51 code references, Referenced by 3 tables"}, {"name": "courseContacts", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "36 code references"}, {"name": "courseContents", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "21 code references"}, {"name": "courseHistories", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "15 code references"}, {"name": "courseNotes", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "27 code references"}, {"name": "courseNotifications", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "24 code references, Referenced by 1 tables"}, {"name": "courseOpinions", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "33 code references"}, {"name": "courseQuestions", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "30 code references"}, {"name": "courseRatings", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "33 code references"}, {"name": "courseReports", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "78 code references"}, {"name": "courses_categories_categories", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "1 code references"}, {"name": "courseSections", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "60 code references, Referenced by 2 tables"}, {"name": "courseSectionTests", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "21 code references"}, {"name": "coursesSaved", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "15 code references"}, {"name": "courseTranscriptions", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "39 code references"}, {"name": "courseViews", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "78 code references"}, {"name": "discussionMessages", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "21 code references, Referenced by 1 tables"}, {"name": "forumComments", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "30 code references, Referenced by 1 tables"}, {"name": "forumSubjects", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "30 code references, Referenced by 4 tables"}, {"name": "forumVotes", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "21 code references"}, {"name": "metting_conversations_participants_users", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "1 code references"}, {"name": "mettingConversations", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "21 code references, Referenced by 2 tables"}, {"name": "mettingMessages", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "21 code references, Referenced by 1 tables"}, {"name": "mettingParticipants", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "12 code references"}, {"name": "mettingSettings", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "30 code references"}, {"name": "notifications_read_by_users", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "1 code references"}, {"name": "notifications_to_users", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "1 code references"}, {"name": "orders_courses_courses", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "1 code references"}, {"name": "payers", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "6 code references, Referenced by 1 tables"}, {"name": "posts_categories_categories", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "1 code references"}, {"name": "subCategories", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "27 code references"}, {"name": "usePolicies", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "12 code references"}, {"name": "users_categories_categories", "rowCount": 0, "hasReferences": true, "hasEntity": false, "reason": "1 code references"}]}