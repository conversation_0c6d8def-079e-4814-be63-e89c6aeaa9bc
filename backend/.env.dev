ACCESS_TOKEN_SECRET=brainmaker_secure_token_key_2024
ACCESS_TOKEN_LIFE=3650
ACCESS_TOKEN=Bearer token
PORT=3200
NODE_ENV=development
PUBLIC_DIR=/public
DOMAIN=http://localhost:3200
CILENT=http://localhost:5174
FILE=.env.dev

SOCKET_URL=http://localhost:9353

# Development Database Settings
DEV_DB_USERNAME=root
DEV_DB_PASSWORD=elsys20202
DEV_DB_NAME=brainmaker
DEV_DB_HOSTNAME=localhost
DEV_DB_DIALECT=mysql
DEV_DB_PORT=3306
DEV_DB_SYNCHRONIZE=false
DEV_DB_DROPSCHEMA=false
DEV_DB_LOGGING=true

# Mail settings (development)
MAIL_HOST=mail.brainmaker.academy
MAIL_PORT=587
mail_username=<EMAIL>
MAIL_NAME=BrainMaker Academy
MAIL_PASSWORD=Juan@2020202
mail_sender=<EMAIL>
MAIL_SECURE=false

# Email notification settings
SEND_LOGIN_NOTIFICATIONS=true
SEND_REGISTRATION_EMAILS=true
SEND_PASSWORD_RESET_EMAILS=true
SEND_EMAIL_VERIFICATION=true

# SMS settings
SMS_HTTP=https://api.yoursmsservice.com
SMS_API=your-sms-api-token

# Development URLs
BASE_URL=http://localhost:3200
FRONTEND_URL=http://localhost:5174
ALLOWED_ORIGINS=http://localhost:5174,http://localhost:4020,http://localhost:3200
