#!/bin/bash
set -e

# Configuration
DEPLOY_DIR="/var/www/backend"
BACKUP_DIR="/var/www/backups/backend"
TIMESTAMP=$(date +"%Y%m%d%H%M%S")

echo "Starting PM2 deployment process..."

# Check if PM2 is installed
if ! command -v pm2 &> /dev/null; then
  echo "PM2 is not installed. Installing PM2..."
  npm install -g pm2
fi

# Create backup directory if it doesn't exist
mkdir -p $BACKUP_DIR

# Create backup of current deployment
if [ -d "$DEPLOY_DIR" ]; then
  echo "Creating backup of current deployment..."
  tar -czf "$BACKUP_DIR/backup-$TIMESTAMP.tar.gz" -C "$DEPLOY_DIR" .
  echo "Backup created at $BACKUP_DIR/backup-$TIMESTAMP.tar.gz"
fi

# Create deployment directory if it doesn't exist
mkdir -p $DEPLOY_DIR

# Stop the current application if it's running with PM2
pm2 stop brainmaker-api 2>/dev/null || true

# Copy files to deployment directory
echo "Copying files to deployment directory..."
cp -r dist package.json package-lock.json .env.prod ecosystem.config.js ormconfig.js $DEPLOY_DIR/

# Create logs directory if it doesn't exist
mkdir -p $DEPLOY_DIR/logs

# Install production dependencies
echo "Installing production dependencies..."
cd $DEPLOY_DIR && npm install --production

# Start the application with PM2
echo "Starting application with PM2..."
cd $DEPLOY_DIR && pm2 start ecosystem.config.js --env production

# Save PM2 process list
pm2 save

# Setup PM2 to start on system boot
echo "Setting up PM2 to start on system boot..."
pm2 startup

echo "PM2 deployment completed successfully!"
echo "To monitor the application, use: pm2 monit"
echo "To view logs, use: pm2 logs brainmaker-api"
