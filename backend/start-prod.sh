#!/bin/bash
set -e

# Load environment variables from .env.prod
if [ -f .env.prod ]; then
  echo "Loading environment variables from .env.prod"
  export $(grep -v '^#' .env.prod | xargs)
else
  echo "Error: .env.prod file not found!"
  exit 1
fi

export NODE_ENV=production

# Print environment information
echo "Starting backend in production mode"
echo "Current directory: $(pwd)"
echo "NODE_ENV: $NODE_ENV"
echo "PORT: $PORT"
echo "Database: $PROD_DB_HOSTNAME:$PROD_DB_PORT/$PROD_DB_NAME"

# Check if dist directory exists
if [ ! -d "dist" ]; then
  echo "Error: dist directory not found! Run 'npm run build' first."
  exit 1
fi

# Check if main.js exists in dist directory
if [ ! -f "dist/main.js" ]; then
  echo "Error: dist/main.js not found! Run 'npm run build' first."
  exit 1
fi

# Create logs directory if it doesn't exist
mkdir -p logs

# Start the application
echo "Starting application..."
node dist/main.js > logs/app.log 2>&1 &
PID=$!
echo "Application started with PID: $PID"
echo $PID > .pid

# Check if application started successfully
sleep 2
if ps -p $PID > /dev/null; then
  echo "Application is running successfully."
else
  echo "Error: Application failed to start. Check logs/app.log for details."
  exit 1
fi

echo "Deployment completed successfully!"
