const isProd = process.env.NODE_ENV === 'production';

module.exports = {
  type: "mysql",
  host: isProd ? process.env.PROD_DB_HOSTNAME : "localhost",
  port: isProd ? parseInt(process.env.PROD_DB_PORT, 10) : 3306,
  username: isProd ? process.env.PROD_DB_USERNAME : "brainmaker",
  password: isProd ? process.env.PROD_DB_PASSWORD : "elsys20202",
  database: isProd ? process.env.PROD_DB_NAME : "brainmaker",
  synchronize: isProd ? (process.env.PROD_DB_SYNCHRONIZE === 'true') : false,
  logging: isProd ? (process.env.PROD_DB_LOGGING === 'true') : true,
  entities: [
    __dirname + "/**/*.entity{.ts,.js}"
  ],
  migrations: [
    __dirname + "/databases/migrations/**/*{.ts,.js}"
  ],
  subscribers: [
    __dirname + "/databases/subscriber/**/*{.ts,.js}"
  ],
  cli: {
    entitiesDir: __dirname + "/**/",
    migrationsDir: __dirname + "/databases/migrations",
    subscribersDir: __dirname + "/databases/subscriber"
  }
}
