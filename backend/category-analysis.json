{"analysis": {"categoryColumns": [{"COLUMN_NAME": "Slug", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "UNI"}, {"COLUMN_NAME": "CreatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED", "COLUMN_KEY": ""}, {"COLUMN_NAME": "UpdatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP(6)", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Id", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "auto_increment", "COLUMN_KEY": "PRI"}, {"COLUMN_NAME": "Name", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Title", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Description", "DATA_TYPE": "text", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "parentId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}, {"COLUMN_NAME": "photoId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}], "subCategoryColumns": [{"COLUMN_NAME": "Slug", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "UNI"}, {"COLUMN_NAME": "CreatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED", "COLUMN_KEY": ""}, {"COLUMN_NAME": "UpdatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP(6)", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Id", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "auto_increment", "COLUMN_KEY": "PRI"}, {"COLUMN_NAME": "Name", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Title", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Description", "DATA_TYPE": "text", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "categoryId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}, {"COLUMN_NAME": "photoId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}, {"COLUMN_NAME": "IsActive", "DATA_TYPE": "tinyint", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "1", "EXTRA": "", "COLUMN_KEY": ""}], "relatedTables": {"categories": [{"COLUMN_NAME": "Slug", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "UNI"}, {"COLUMN_NAME": "CreatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED", "COLUMN_KEY": ""}, {"COLUMN_NAME": "UpdatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP(6)", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Id", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "auto_increment", "COLUMN_KEY": "PRI"}, {"COLUMN_NAME": "Name", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Title", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Description", "DATA_TYPE": "text", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "parentId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}, {"COLUMN_NAME": "photoId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}], "subCategories": [{"COLUMN_NAME": "Slug", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "UNI"}, {"COLUMN_NAME": "CreatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED", "COLUMN_KEY": ""}, {"COLUMN_NAME": "UpdatedAt", "DATA_TYPE": "datetime", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "CURRENT_TIMESTAMP(6)", "EXTRA": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP(6)", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Id", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "auto_increment", "COLUMN_KEY": "PRI"}, {"COLUMN_NAME": "Name", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Title", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "Description", "DATA_TYPE": "text", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": ""}, {"COLUMN_NAME": "categoryId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}, {"COLUMN_NAME": "photoId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "MUL"}, {"COLUMN_NAME": "IsActive", "DATA_TYPE": "tinyint", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "1", "EXTRA": "", "COLUMN_KEY": ""}], "courses_categories_categories": [{"COLUMN_NAME": "coursesId", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "PRI"}, {"COLUMN_NAME": "categoriesId", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "PRI"}], "posts_categories_categories": [{"COLUMN_NAME": "postsId", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "PRI"}, {"COLUMN_NAME": "categoriesId", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "PRI"}], "users_categories_categories": [{"COLUMN_NAME": "usersId", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "PRI"}, {"COLUMN_NAME": "categoriesId", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "EXTRA": "", "COLUMN_KEY": "PRI"}]}, "relationships": {"categories": [{"TABLE_NAME": "categories", "COLUMN_NAME": "parentId", "CONSTRAINT_NAME": "FK_c7a1c6d7a1f3f1c1a1f1c1a1f6", "REFERENCED_TABLE_NAME": "categories", "REFERENCED_COLUMN_NAME": "Id"}, {"TABLE_NAME": "categories", "COLUMN_NAME": "photoId", "CONSTRAINT_NAME": "FK_categories_photo", "REFERENCED_TABLE_NAME": "medias", "REFERENCED_COLUMN_NAME": "Id"}], "courses_categories_categories": [{"TABLE_NAME": "courses_categories_categories", "COLUMN_NAME": "categoriesId", "CONSTRAINT_NAME": "FK_c7a1c6d7a1f3f1c1a1f1c1a1f11", "REFERENCED_TABLE_NAME": "categories", "REFERENCED_COLUMN_NAME": "Id"}, {"TABLE_NAME": "courses_categories_categories", "COLUMN_NAME": "coursesId", "CONSTRAINT_NAME": "FK_c7a1c6d7a1f3f1c1a1f1c1a1f10", "REFERENCED_TABLE_NAME": "courses", "REFERENCED_COLUMN_NAME": "Id"}], "posts_categories_categories": [{"TABLE_NAME": "posts_categories_categories", "COLUMN_NAME": "categoriesId", "CONSTRAINT_NAME": "FK_posts_categories_categories", "REFERENCED_TABLE_NAME": "categories", "REFERENCED_COLUMN_NAME": "Id"}, {"TABLE_NAME": "posts_categories_categories", "COLUMN_NAME": "postsId", "CONSTRAINT_NAME": "FK_posts_categories_posts", "REFERENCED_TABLE_NAME": "posts", "REFERENCED_COLUMN_NAME": "Id"}], "subCategories": [{"TABLE_NAME": "subCategories", "COLUMN_NAME": "categoryId", "CONSTRAINT_NAME": "FK_c7a1c6d7a1f3f1c1a1f1c1a1f8", "REFERENCED_TABLE_NAME": "categories", "REFERENCED_COLUMN_NAME": "Id"}, {"TABLE_NAME": "subCategories", "COLUMN_NAME": "photoId", "CONSTRAINT_NAME": "FK_subcategories_photo", "REFERENCED_TABLE_NAME": "medias", "REFERENCED_COLUMN_NAME": "Id"}], "users_categories_categories": [{"TABLE_NAME": "users_categories_categories", "COLUMN_NAME": "categoriesId", "CONSTRAINT_NAME": "FK_c7a1c6d7a1f3f1c1a1f1c1a1f14", "REFERENCED_TABLE_NAME": "categories", "REFERENCED_COLUMN_NAME": "Id"}, {"TABLE_NAME": "users_categories_categories", "COLUMN_NAME": "usersId", "CONSTRAINT_NAME": "FK_c7a1c6d7a1f3f1c1a1f1c1a1f13", "REFERENCED_TABLE_NAME": "users", "REFERENCED_COLUMN_NAME": "Id"}]}}, "categoryCreationSchema": {"basicInfo": {"title": {"type": "string", "required": true, "maxLength": 255, "unique": true}, "description": {"type": "text", "required": false}, "slug": {"type": "string", "required": false, "generated": true}}, "media": {"photoId": {"type": "number", "required": false}, "photoFile": {"type": "file", "required": false, "accept": "image/*"}}, "subcategories": {"subcategories": {"type": "array", "required": false, "items": "SubCategory"}}, "settings": {"isActive": {"type": "boolean", "required": true, "default": true}}}, "subCategoryCreationSchema": {"basicInfo": {"title": {"type": "string", "required": true, "maxLength": 255, "unique": true}, "description": {"type": "text", "required": false}, "categoryId": {"type": "number", "required": true}, "slug": {"type": "string", "required": false, "generated": true}}, "media": {"photoId": {"type": "number", "required": false}, "photoFile": {"type": "file", "required": false, "accept": "image/*"}}, "settings": {"isActive": {"type": "boolean", "required": true, "default": true}}}}