/**
 * Test Frontend Course Retrieval Integration
 * This script tests the complete frontend-backend integration for course retrieval
 * with proper media URL formatting and display
 */

const axios = require('axios');

const API_BASE = 'http://localhost:3200/api';
const FRONTEND_BASE = 'http://localhost:5174';

// Test course slug from our previous tests
const TEST_COURSE_SLUG = 'd364109feb9c4332ba97561a5413ecc6';

/**
 * Test 1: Direct Backend API Call
 */
async function testBackendAPI() {
  console.log('🔍 Testing Backend API Direct Call...');
  
  try {
    const response = await axios.get(`${API_BASE}/courses/public/${TEST_COURSE_SLUG}`);
    const course = response.data;
    
    console.log('✅ Backend API Response:', {
      courseId: course.Id,
      title: course.Title,
      hasCoverImage: !!course.CoverImage,
      hasVideo: !!course.PresentationVideo,
      coverImageHashname: course.CoverImage?.Hashname,
      videoHashname: course.PresentationVideo?.Hashname
    });
    
    return course;
  } catch (error) {
    console.error('❌ Backend API Error:', error.message);
    return null;
  }
}

/**
 * Test 2: Verify Media URLs are Accessible
 */
async function testMediaAccessibility(course) {
  console.log('\n📸 Testing Media Accessibility...');
  
  const results = {
    coverImage: false,
    video: false
  };
  
  // Test cover image
  if (course.CoverImage?.Hashname) {
    const imageUrl = `http://localhost:3200/${course.CoverImage.Hashname}`;
    try {
      const response = await axios.head(imageUrl);
      results.coverImage = response.status === 200;
      console.log(`✅ Cover Image Accessible: ${imageUrl}`);
    } catch (error) {
      console.log(`❌ Cover Image Not Accessible: ${imageUrl} (${error.response?.status || error.message})`);
    }
  }
  
  // Test presentation video
  if (course.PresentationVideo?.Hashname) {
    const videoUrl = `http://localhost:3200/${course.PresentationVideo.Hashname}`;
    try {
      const response = await axios.head(videoUrl);
      results.video = response.status === 200;
      console.log(`✅ Presentation Video Accessible: ${videoUrl}`);
    } catch (error) {
      console.log(`❌ Presentation Video Not Accessible: ${videoUrl} (${error.response?.status || error.message})`);
    }
  }
  
  return results;
}

/**
 * Test 3: Verify Frontend is Running
 */
async function testFrontendAvailability() {
  console.log('\n🌐 Testing Frontend Availability...');
  
  try {
    const response = await axios.get(FRONTEND_BASE, { timeout: 5000 });
    console.log('✅ Frontend is accessible at:', FRONTEND_BASE);
    return true;
  } catch (error) {
    console.log('❌ Frontend not accessible:', error.message);
    return false;
  }
}

/**
 * Test 4: Simulate Frontend Course Service URL Construction
 */
function testURLConstruction(course) {
  console.log('\n🔧 Testing Frontend URL Construction Logic...');
  
  const ENVIRONMENT_PATH = 'http://localhost:3200'; // Development environment
  
  // Simulate the course service's constructMediaUrl method
  function constructMediaUrl(hashname) {
    if (!hashname) return '';
    
    // If already a complete URL, return as-is
    if (hashname.startsWith('http://') || hashname.startsWith('https://')) {
      return hashname;
    }
    
    // Remove leading slash if present to avoid double slashes
    const cleanHashname = hashname.startsWith('/') ? hashname.substring(1) : hashname;
    
    // Backend serves static files directly from the root
    return `${ENVIRONMENT_PATH}/${cleanHashname}`;
  }
  
  const results = {};
  
  if (course.CoverImage?.Hashname) {
    const originalHashname = course.CoverImage.Hashname;
    const constructedUrl = constructMediaUrl(originalHashname);
    results.coverImage = {
      original: originalHashname,
      constructed: constructedUrl,
      isValid: constructedUrl.startsWith('http')
    };
    console.log('📸 Cover Image URL Construction:', results.coverImage);
  }
  
  if (course.PresentationVideo?.Hashname) {
    const originalHashname = course.PresentationVideo.Hashname;
    const constructedUrl = constructMediaUrl(originalHashname);
    results.video = {
      original: originalHashname,
      constructed: constructedUrl,
      isValid: constructedUrl.startsWith('http')
    };
    console.log('🎥 Video URL Construction:', results.video);
  }
  
  return results;
}

/**
 * Main Test Function
 */
async function runTests() {
  console.log('🧪 Frontend Course Retrieval Integration Test');
  console.log('='.repeat(50));
  
  // Test 1: Backend API
  const course = await testBackendAPI();
  if (!course) {
    console.log('❌ Cannot proceed without backend data');
    return;
  }
  
  // Test 2: Media Accessibility
  const mediaResults = await testMediaAccessibility(course);
  
  // Test 3: Frontend Availability
  const frontendAvailable = await testFrontendAvailability();
  
  // Test 4: URL Construction
  const urlResults = testURLConstruction(course);
  
  // Summary
  console.log('\n📊 Test Summary:');
  console.log('='.repeat(30));
  console.log(`✅ Backend API: PASSED`);
  console.log(`${mediaResults.coverImage ? '✅' : '❌'} Cover Image Access: ${mediaResults.coverImage ? 'PASSED' : 'FAILED'}`);
  console.log(`${mediaResults.video ? '✅' : '❌'} Video Access: ${mediaResults.video ? 'PASSED' : 'FAILED'}`);
  console.log(`${frontendAvailable ? '✅' : '❌'} Frontend Available: ${frontendAvailable ? 'PASSED' : 'FAILED'}`);
  console.log(`${urlResults.coverImage?.isValid ? '✅' : '❌'} Cover URL Construction: ${urlResults.coverImage?.isValid ? 'PASSED' : 'FAILED'}`);
  console.log(`${urlResults.video?.isValid ? '✅' : '❌'} Video URL Construction: ${urlResults.video?.isValid ? 'PASSED' : 'FAILED'}`);
  
  const allPassed = mediaResults.coverImage && mediaResults.video && frontendAvailable && 
                   urlResults.coverImage?.isValid && urlResults.video?.isValid;
  
  console.log('\n🎯 Overall Result:', allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED');
  
  if (frontendAvailable) {
    console.log('\n🌐 Next Steps:');
    console.log('1. Open your browser to: http://localhost:5174');
    console.log('2. Navigate to a course detail page to test the UI');
    console.log('3. Check browser console for any frontend errors');
    console.log(`4. Test course slug: ${TEST_COURSE_SLUG}`);
  }
}

// Run the tests
runTests().catch(console.error);
