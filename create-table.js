const mysql = require('mysql2/promise');

async function createTable() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'elsys20202',
    database: 'brainmaker'
  });

  const createTableSQL = `
    CREATE TABLE \`instructor_applications\` (
      \`Id\` int NOT NULL AUTO_INCREMENT,
      \`Slug\` varchar(255) NOT NULL,
      \`CreatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
      \`UpdatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
      \`FirstName\` varchar(255) NOT NULL,
      \`LastName\` varchar(255) NOT NULL,
      \`Email\` varchar(255) NOT NULL,
      \`Phone\` varchar(255) DEFAULT NULL,
      \`Country\` varchar(255) DEFAULT NULL,
      \`City\` varchar(255) DEFAULT NULL,
      \`Title\` varchar(255) DEFAULT NULL,
      \`Bio\` text,
      \`Experience\` varchar(255) DEFAULT NULL,
      \`Expertise\` json DEFAULT NULL,
      \`LinkedinUrl\` varchar(255) DEFAULT NULL,
      \`WebsiteUrl\` varchar(255) DEFAULT NULL,
      \`Education\` json DEFAULT NULL,
      \`TeachingExperience\` text,
      \`PreviousPlatforms\` text,
      \`SampleCourseOutline\` text,
      \`Status\` enum('pending','approved','rejected','under_review') NOT NULL DEFAULT 'pending',
      \`AdminNotes\` text,
      \`ReviewedAt\` datetime DEFAULT NULL,
      \`userId\` int DEFAULT NULL,
      \`reviewedById\` int DEFAULT NULL,
      \`resumeId\` int DEFAULT NULL,
      \`portfolioId\` int DEFAULT NULL,
      PRIMARY KEY (\`Id\`),
      UNIQUE KEY \`IDX_instructor_applications_slug\` (\`Slug\`),
      UNIQUE KEY \`IDX_instructor_applications_email\` (\`Email\`),
      KEY \`FK_instructor_applications_user\` (\`userId\`),
      KEY \`FK_instructor_applications_reviewer\` (\`reviewedById\`),
      KEY \`FK_instructor_applications_resume\` (\`resumeId\`),
      KEY \`FK_instructor_applications_portfolio\` (\`portfolioId\`),
      CONSTRAINT \`FK_instructor_applications_user\` FOREIGN KEY (\`userId\`) REFERENCES \`users\` (\`Id\`) ON DELETE SET NULL,
      CONSTRAINT \`FK_instructor_applications_reviewer\` FOREIGN KEY (\`reviewedById\`) REFERENCES \`users\` (\`Id\`) ON DELETE SET NULL,
      CONSTRAINT \`FK_instructor_applications_resume\` FOREIGN KEY (\`resumeId\`) REFERENCES \`medias\` (\`Id\`) ON DELETE SET NULL,
      CONSTRAINT \`FK_instructor_applications_portfolio\` FOREIGN KEY (\`portfolioId\`) REFERENCES \`medias\` (\`Id\`) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
  `;

  try {
    await connection.execute(createTableSQL);
    console.log('✅ instructor_applications table created successfully!');
  } catch (error) {
    if (error.code === 'ER_TABLE_EXISTS_ERROR') {
      console.log('ℹ️  Table already exists');
    } else {
      console.error('❌ Error creating table:', error.message);
    }
  } finally {
    await connection.end();
  }
}

createTable();
