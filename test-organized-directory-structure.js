/**
 * Test the new organized directory structure:
 * - Profiles: profiles/userSlug/
 * - Course content: courses/courseSlug/cover, courses/courseSlug/video
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3200/api';

async function apiRequest(method, endpoint, data = null) {
  const config = {
    method,
    url: `${API_BASE_URL}${endpoint}`,
    headers: {
      'Content-Type': 'application/json'
    }
  };
  
  if (data) {
    config.data = data;
  }
  
  try {
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`API Error (${method} ${endpoint}):`, error.response?.data || error.message);
    throw error;
  }
}

async function testOrganizedDirectoryStructure() {
  console.log('🏗️ Testing New Organized Directory Structure');
  console.log('=' .repeat(60));
  
  const testCourseSlug = `test-course-${Date.now()}`;
  const testUserSlug = 'D2174571'; // Using existing user
  
  console.log(`📋 Test Configuration:`);
  console.log(`   Course Slug: ${testCourseSlug}`);
  console.log(`   User Slug: ${testUserSlug}`);
  
  // Test 1: Upload cover image to organized directory
  console.log('\n1️⃣ Testing cover image upload with organized structure...');
  const coverImageData = {
    Name: 'organized-cover.png',
    Hashname: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==',
    Extension: 'image/png',
    Size: 1000,
    SubDir: `courses/${testCourseSlug}/cover`
  };
  
  console.log(`   Expected SubDir: courses/${testCourseSlug}/cover`);
  const uploadedCover = await apiRequest('POST', '/medias', coverImageData);
  
  console.log('✅ Cover image uploaded:');
  console.log(`   ID: ${uploadedCover.Id}`);
  console.log(`   Hashname: ${uploadedCover.Hashname}`);
  console.log(`   SubDir: ${uploadedCover.SubDir}`);
  console.log(`   Prefix: ${uploadedCover.Hashname.substring(0, 4)} (expected: cvr_)`);
  
  // Test 2: Upload presentation video to organized directory
  console.log('\n2️⃣ Testing presentation video upload with organized structure...');
  const videoData = {
    Name: 'organized-video.mp4',
    Hashname: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==',
    Extension: 'video/mp4',
    Size: 2000,
    SubDir: `courses/${testCourseSlug}/video`
  };
  
  console.log(`   Expected SubDir: courses/${testCourseSlug}/video`);
  const uploadedVideo = await apiRequest('POST', '/medias', videoData);
  
  console.log('✅ Video uploaded:');
  console.log(`   ID: ${uploadedVideo.Id}`);
  console.log(`   Hashname: ${uploadedVideo.Hashname}`);
  console.log(`   SubDir: ${uploadedVideo.SubDir}`);
  console.log(`   Prefix: ${uploadedVideo.Hashname.substring(0, 4)} (expected: vid_)`);
  
  // Test 3: Create course with organized media
  console.log('\n3️⃣ Testing course creation with organized media...');
  const coursePayload = {
    body: {
      Title: 'Organized Directory Test Course',
      Resume: 'Testing the new organized directory structure',
      Keywords: ['test', 'organized'],
      Format: 2,
      Language: 'en',
      Free: true,
      Level: [1],
      Message: 'Test message',
      Congratulation: 'Test congratulation',
      Categories: [],
      Published: false,
      Archived: false,
      coverImageId: uploadedCover.Id,
      presentationVideoId: uploadedVideo.Id,
      Slug: testCourseSlug, // Use the same slug for consistency
      CreatedBy: { Id: 1 }
    },
    origin: 'http://localhost:3000'
  };
  
  console.log(`   Using coverImageId: ${coursePayload.body.coverImageId}`);
  console.log(`   Using presentationVideoId: ${coursePayload.body.presentationVideoId}`);
  console.log(`   Using course slug: ${coursePayload.body.Slug}`);
  
  const createdCourse = await apiRequest('POST', '/courses', coursePayload);
  
  console.log('✅ Course created:');
  console.log(`   ID: ${createdCourse.Id}`);
  console.log(`   Title: ${createdCourse.Title}`);
  console.log(`   Slug: ${createdCourse.Slug}`);
  
  // Test 4: Verify course media references
  console.log('\n4️⃣ Verifying course media references...');
  const retrievedCourse = await apiRequest('GET', `/courses/${createdCourse.Id}`);
  
  console.log('📊 Course Media Analysis:');
  
  if (retrievedCourse.CoverImage) {
    console.log(`   Cover Image ID: ${retrievedCourse.CoverImage.Id} (expected: ${uploadedCover.Id})`);
    console.log(`   Cover SubDir: ${retrievedCourse.CoverImage.SubDir}`);
    console.log(`   Cover Match: ${retrievedCourse.CoverImage.Id === uploadedCover.Id ? '✅ YES' : '❌ NO'}`);
  } else {
    console.log('   ❌ No cover image found in course');
  }
  
  if (retrievedCourse.PresentationVideo) {
    console.log(`   Video ID: ${retrievedCourse.PresentationVideo.Id} (expected: ${uploadedVideo.Id})`);
    console.log(`   Video SubDir: ${retrievedCourse.PresentationVideo.SubDir}`);
    console.log(`   Video Match: ${retrievedCourse.PresentationVideo.Id === uploadedVideo.Id ? '✅ YES' : '❌ NO'}`);
  } else {
    console.log('   ❌ No presentation video found in course');
  }
  
  // Test 5: Final analysis
  console.log('\n5️⃣ Final Analysis:');
  
  const coverCorrect = uploadedCover.SubDir === `courses/${testCourseSlug}/cover` && 
                      uploadedCover.Hashname.startsWith('cvr_');
  const videoCorrect = uploadedVideo.SubDir === `courses/${testCourseSlug}/video` && 
                      uploadedVideo.Hashname.startsWith('vid_');
  const courseCorrect = retrievedCourse.CoverImage?.Id === uploadedCover.Id && 
                       retrievedCourse.PresentationVideo?.Id === uploadedVideo.Id;
  
  console.log(`   Cover Image Structure: ${coverCorrect ? '✅ CORRECT' : '❌ INCORRECT'}`);
  console.log(`   Video Structure: ${videoCorrect ? '✅ CORRECT' : '❌ INCORRECT'}`);
  console.log(`   Course References: ${courseCorrect ? '✅ CORRECT' : '❌ INCORRECT'}`);
  
  if (coverCorrect && videoCorrect && courseCorrect) {
    console.log('\n🎉 SUCCESS: New organized directory structure is working perfectly!');
    console.log(`📁 Expected file locations:`);
    console.log(`   Cover: backend/public/upload/courses/${testCourseSlug}/cover/${uploadedCover.Hashname}`);
    console.log(`   Video: backend/public/upload/courses/${testCourseSlug}/video/${uploadedVideo.Hashname}`);
  } else {
    console.log('\n❌ ISSUES FOUND: Directory structure needs adjustment');
  }
}

testOrganizedDirectoryStructure().catch(console.error);
