import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { ToastrService } from 'ngx-toastr';
import { ConfirmationService } from 'primeng/api';
import { Bill, Billing, BillStatus } from '~/app/models/billings/billing.model';
import { Category } from '~/app/models/category.model';
import {
	CourseNotification,
	courseNotificationType,
	NotificationActionType
} from '~/app/models/course-notification.model';
import {
	ContentType,
	Course,
	CourseSearch,
	CourseSearchResult,
	OrderbyType
} from '~/app/models/course.model';
import { User } from '~/app/models/user.model';
import { BillingService } from '~/app/services/billings/billing.service';
import { CategoryService } from '~/app/services/categories/category.service';
import { CourseService } from '~/app/services/courses/course.service';
import { CourseNotificationsService } from '~/app/services/notifications/course-notifications.service';
import { UserService } from '~/app/services/users/user.service';
import { UtilsService } from '~/app/services/utils.service';
import { environment } from '~/environments/environment';

@Component({
	selector: 'app-admin-billings',
	templateUrl: './admin-billings.component.html',
	styleUrls: ['./admin-billings.component.scss']
})
export class AdminBillingsComponent implements OnInit {
	/**
	 * Notification actions list
	 * this is the actions that user can do after
	 * that he have see the details of the notification
	 * eg: user see the notification that new course has been published
	 * he want to navigate to the page of course to verify to verify the
	 * new course and publish his
	 */
	Notification_Actions = {
		VERIFY_COURSE: {
			label: 'verifier le cours',
			title: 'Nouveau cours publié',
			type: courseNotificationType.COURSE_CREATE
		},
		SEE_REPORTED_COURSE: {
			label: 'Voir le course signalé',
			title: 'Cours Signalé',
			type: courseNotificationType.COURSE_REPORT
		}
	};

	/**
	 * Notificaitons list
	 */
	customers: any = [];

	loadingDeleteNotif = false;
	// deletingNotif: Billing[] = [];

	// statuses: any[];
	loading = false;
	displayBasic2 = false;

	user: User;

	selectedBilling: Billing;
	action: NotificationActionType = this.Notification_Actions.VERIFY_COURSE;
	count: number;

	selectedBills: Bill;

	loadingTransfer: boolean;
	loadingReject: boolean;

	transferSucess: boolean;
	transferFail: boolean;

	rejectSucess: boolean;
	rejectFail: boolean;

	transactionDate;
	transactionNumber;
	transactionAmount;

	constructor(
		private utilService: UtilsService,
		private courseNotificationService: CourseNotificationsService,
		private router: Router,
		private userService: UserService,
		private billingService: BillingService,
		private confirmationService: ConfirmationService,
		private toastrService: ToastrService
	) {
		this.selectedBilling = new Billing(null, null, null, null, null);
		this.loadingTransfer = false;
		this.transferSucess = false;
		this.transferFail = false;
		this.loadingReject = false;
		this.rejectSucess = false;
		this.rejectFail = false;
	}

	async ngOnInit(): Promise<void> {
		if (this.userService.isConnected()) {
			this.user = await this.userService.getUserConnected().toPromise();
		} else {
			this.router.navigateByUrl('/');
		}
		try {
			this.loading = true;
			const res = await this.billingService
				.getAllBillingsToVerify()
				.toPromise();

			console.log('Billings: ', res);
			this.customers = res.Billings;
			this.loading = false;
			// this.courseNotificationService.notificationCount.subscribe(
			// 	(count: number) => {
			// 		this.count = count;
			// 	}
			// );
		} catch (error) {
			console.log(error);
			this.toastrService.error(
				'Impossible de récupérer les factures',
				'La requete a échouée'
			);
			this.loading = false;
		}
	}

	/**
	 * Open modal when user click on row in the table
	 * the modal show user the details of notification that he has click
	 * @param customer, notification model
	 */
	openMessageModal(customer: any): void {
		this.displayBasic2 = true;
		this.selectedBilling = customer;

		console.log('Cliqued : ', customer);
		this.selectedBills = customer.Bills;
	}

	// /**
	//  * Return action from type (number) of notification retrived from
	//  * database CourseNotification entity
	//  * @param type
	//  */
	// getNotificationFromType(type: number): NotificationActionType | string {
	// 	switch (type) {
	// 		case this.Notification_Actions.VERIFY_COURSE.type:
	// 			return this.Notification_Actions.VERIFY_COURSE;
	// 		case this.Notification_Actions.SEE_REPORTED_COURSE.type:
	// 			return this.Notification_Actions.SEE_REPORTED_COURSE;
	// 		default:
	// 			return 'Non spécifié';
	// 	}
	// }

	/**
	 * Truncate string when length is too long
	 * @param value
	 */
	truncateString(value: string): string {
		return this.utilService.truncateString(value, 20);
	}

	/**
	 * Check if user have already read notification
	 */
	alreadyVerify(billing: Billing): boolean {
		return billing.Status !== BillStatus.PENDING;
	}

	formatDate(date: Date): string {
		return this.utilService.FormatDateToDayMonthYear(new Date(date));
	}

	confirmTransfert() {
		if (this.loadingTransfer || this.loadingReject || this.billingHasPayed()) {
			return;
		}
		console.log(
			this.transactionDate,
			this.transactionNumber,
			this.transactionAmount
		);
		if (
			!this.transactionAmount ||
			!this.transactionDate ||
			!this.transactionNumber
		) {
			this.toastrService.error(
				"Veuillez remplir tous les champs s'il vous plait",
				'Saisie incorrect'
			);
			return;
		}

		this.confirmationService.confirm({
			header:
				"Enregistrer le transfer d'argent dans le compte de l'instructeur",
			icon: 'pi pi-exclamation-triangle',

			message:
				"Une notification sera envoyé à l'instructeur. Voulez vous continuer ?",
			accept: async () => {
				this.confirmationService.close();
				this.loadingTransfer = true;
				try {
					console.log(
						this.transactionDate,
						this.transactionNumber,
						this.transactionAmount
					);
					const res = await this.billingService
						.proceedToPaidBilling(this.selectedBilling.Slug, {
							Date: new Date(this.transactionDate),
							Number: this.transactionNumber,
							Amount: this.transactionAmount
						})
						.toPromise();
					this.selectedBilling = res;

					console.log('payment suceffully', res);

					const index = this.customers.indexOf(
						this.customers.find((b: Billing) => b.Id === res.Id)
					);
					this.customers[index] = res;

					this.toastrService.success(
						'Le transfert a été effectué avec succès',
						'Transfert effectué'
					);
					this.loadingTransfer = false;
					this.transferSucess = true;
				} catch (error) {
					this.toastrService.error(
						'Une erreur est survenue lors du transfert',
						'Echec du transfert'
					);
					this.loadingTransfer = false;
					this.transferFail = true;
				}
			}
		});
	}

	getAvatar(): string {
		if (
			this.selectedBilling &&
			this.selectedBilling.User &&
			this.selectedBilling.User.Photo
		) {
			return `${environment.path}/upload/${this.selectedBilling.User.Photo.SubDir}/${this.selectedBilling.User.Photo.Hashname}`;
		}
		return '../../../assets/img/avatar.png';
	}

	async rejectBilling(): Promise<void> {
		if (
			this.loadingReject ||
			this.loadingTransfer ||
			this.billingHasReject() ||
			this.billingHasPayed()
		) {
			return;
		}

		this.confirmationService.confirm({
			header: 'Confirmation du rejet',
			icon: 'pi pi-exclamation-triangle',

			message: 'Voulez vous vraiment rejeter cette facture ?',
			accept: async () => {
				this.confirmationService.close();

				this.loadingReject = true;

				try {
					const res = await this.billingService
						.proceedToRejectBilling(this.selectedBilling.Slug)
						.toPromise();
					this.selectedBilling = res;
					console.log('****', res);

					const index = this.customers.indexOf(
						this.customers.find((b: Billing) => b.Id === res.Id)
					);
					this.customers[index] = res;

					this.toastrService.success('La facture a été rejeté', 'Reussie');
					this.rejectSucess = true;
					this.loadingReject = false;

					// Send notification
					this.sendNotification();
				} catch (error) {
					this.rejectFail = true;
					this.toastrService.error(
						'une erreur est survenue lors du traitement',
						'Impossible de rejeter la facture'
					);
					this.loadingReject = false;
				}
			}
		});
	}

	async sendNotification(): Promise<void> {}

	billingHasReject(billing: Billing = null): boolean {
		if (billing) {
			return billing.Status === BillStatus.REJECTED;
		}
		return this.selectedBilling.Status === BillStatus.REJECTED;
	}

	billingHasPayed(): boolean {
		return this.selectedBilling.Status === BillStatus.PAYED;
	}
}
