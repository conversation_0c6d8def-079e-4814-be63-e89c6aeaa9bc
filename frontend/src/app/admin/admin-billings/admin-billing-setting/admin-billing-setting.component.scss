.skin_notif {
	background: #ebebec;
}

.list-holder {
	padding-right: 3em;
	padding-left: 3em;
	margin-top: 8em;
}

tr.unread {
	span {
		font-weight: bold !important;
	}
}

h1,
h2,
h3 {
	display: block;
}

.small {
	font-size: 1rem;
	display: block;
}

.notif-content {
	max-width: 710px;
	margin: auto;
	flex-direction: column !important;

	.notif-date {
		margin-left: auto;
	}
}

.notif-btn {
	display: flex;
}

.notif-delete-btn {
	display: flex;
	justify-content: flex-start;
	align-items: center;
	color: #dc3545 !important;
	cursor: pointer;
}

.table-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.bitting-setting-control {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin: 1rem 0;
}

// .active-config {
// 	background: url('../../../../assets/');
// 	position: relative;
// }

.p-float-label {
	margin: 1.5rem 0;
}

.card-text-center {
	width: 100%;
	height: 60vh;
	display: flex;
	justify-content: center;
	align-items: center;
}

div.small {
	font-size: 12px;
}

@media screen and (max-width: 550px) {
	.bitting-setting-control {
		flex-direction: column;
		justify-content: flex-start;
	}
}
