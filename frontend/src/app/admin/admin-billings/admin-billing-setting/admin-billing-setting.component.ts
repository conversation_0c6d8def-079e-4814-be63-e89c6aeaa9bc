import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Validate, Validator } from 'class-validator';
import { ToastrService } from 'ngx-toastr';
import { ConfirmationService } from 'primeng/api';
import {
	BillingSetting,
	BillingSettingDevice,
	BillingSettingStatus
} from '~/app/models/billings/billing-setting.model';
import {
	CourseNotification,
	courseNotificationType,
	NotificationActionType
} from '~/app/models/course-notification.model';
import { User } from '~/app/models/user.model';
import { BillingSettingService } from '~/app/services/billings/billing-setting.service';
import { BillingService } from '~/app/services/billings/billing.service';
import { CourseNotificationsService } from '~/app/services/notifications/course-notifications.service';
import { UserService } from '~/app/services/users/user.service';
import { UtilsService } from '~/app/services/utils.service';

@Component({
	selector: 'app-admin-billing-setting',
	templateUrl: './admin-billing-setting.component.html',
	styleUrls: ['./admin-billing-setting.component.scss'],
	encapsulation: ViewEncapsulation.None
})
export class AdminBillingSettingComponent implements OnInit {
	/**
	 * Notification actions list
	 * this is the actions that user can do after
	 * that he have see the details of the notification
	 * eg: user see the notification that new course has been published
	 * he want to navigate to the page of course to verify to verify the
	 * new course and publish his
	 */
	Notification_Actions = {
		VERIFY_COURSE: {
			label: 'verifier le cours',
			title: 'Nouveau cours publié',
			type: courseNotificationType.COURSE_CREATE
		},
		SEE_REPORTED_COURSE: {
			label: 'Voir le course signalé',
			title: 'Cours Signalé',
			type: courseNotificationType.COURSE_REPORT
		}
	};

	/**
	 * Notificaitons list
	 */
	customers: CourseNotification[] = [];

	loadingDeleteNotif = false;
	deletingNotif: CourseNotification[] = [];

	// statuses: any[];
	loading = false;
	displayBasic2 = false;

	user: User;

	selectedNotification: CourseNotification = new CourseNotification(
		null,
		null,
		new Date(),
		null,
		'',
		null,
		false,
		null,
		1
	);
	action: NotificationActionType = this.Notification_Actions.VERIFY_COURSE;
	count: number;

	billingSettings: { id: number; label: string; value: string }[];
	selectedbillingSetting: string;

	loadingPage: boolean;
	loadingSettingActivation: boolean;
	displayCreateSettingDialog: boolean;
	displayDetailsSettingDialog: boolean;
	displaySettingHistoryDialog: boolean;
	loadingSave: boolean;
	loadingEditNewSetting: boolean;
	loadingSetting: boolean;
	selectedBillingSettingCanBeModify: boolean;
	updatingSetting: boolean;
	billingAlreadyActive: boolean;

	allBillingSettingsFetched: BillingSetting[];
	selectedBillingSettingFetched: any;
	activBillingSetting: BillingSetting;

	billingSettingForm: FormGroup;

	pendingSetting: BillingSetting;

	loadingG: boolean;
	loadingInit: boolean;

	constructor(
		private utilService: UtilsService,
		private courseNotificationService: CourseNotificationsService,
		private router: Router,
		private userService: UserService,
		private confirmationService: ConfirmationService,
		private toastrService: ToastrService,
		private billingSettingService: BillingSettingService,
		private formBuilder: FormBuilder,
		private billingService: BillingService
	) {
		this.loadingPage = true;
		this.loadingSettingActivation = false;
		this.displayCreateSettingDialog = false;
		this.displayDetailsSettingDialog = false;
		this.displaySettingHistoryDialog = false;
		this.loadingSave = false;
		this.loadingEditNewSetting = false;
		this.loadingSetting = false;
		this.selectedBillingSettingCanBeModify = false;
		this.updatingSetting = false;
		this.billingAlreadyActive = false;

		this.billingSettingForm = this.formBuilder.group({
			AlreadyUse: [false],
			Device: ['USD', Validators.required],
			Name: ['', Validators.required],
			PourcentBuy: ['', Validators.required],
			Status: ['3', Validators.required],
			ViewingCount: ['', Validators.required],
			ViewingPrice: ['', Validators.required]
		});
		this.loadingG = false;
		this.loadingInit = false;
	}

	async ngOnInit(): Promise<void> {
		this.loadingPage = true;

		if (this.userService.isConnected()) {
			this.user = await this.userService.getUserConnected().toPromise();
		} else {
			this.router.navigateByUrl('/');
		}
		this.loading = true;
		// this.customers = await this.courseNotificationService.getAll().toPromise();
		this.loading = false;
		// this.courseNotificationService.notificationCount.subscribe(
		// 	(count: number) => {
		// 		this.count = count;
		// 	}
		// );

		try {
			this.pendingSetting = await this.billingSettingService
				.getPendingSetting()
				.toPromise();
			console.log('Pending setting: ', this.pendingSetting);
		} catch (error) {
			console.log(error);
		}

		// Billings
		await this.getBillingSettings();
		this.loadingPage = false;
	}

	async confirmDelete(
		notification: CourseNotification = this.selectedNotification
	): Promise<void> {
		if (this.displayBasic2) {
			this.displayBasic2 = false;
		}
		this.deletingNotif.push(notification);
		this.loadingDeleteNotif = true;
		try {
			const res = await this.courseNotificationService
				.delete(notification.Slug)
				.toPromise();

			const index = this.customers.indexOf(this.selectedNotification);
			this.customers.splice(index, 1);
		} catch (error) {
			// console.log(error);
		} finally {
			if (notification.Read === false) {
				this.count--;
				this.courseNotificationService.notificationCount.next(this.count);
			}
			const index = this.deletingNotif.indexOf(notification);
			this.deletingNotif.splice(index, 1);
			this.loadingDeleteNotif = false;
		}
	}

	/**
	 * Open modal when user click on row in the table
	 * the modal show user the details of notification that he has click
	 * @param customer, notification model
	 */
	// openMessageModal(customer: CourseNotification): void {
	// 	this.displayBasic2 = true;
	// 	this.selectedNotification = customer;
	// 	this.selectedNotification = customer;
	// 	this.action = this.getNotificationFromType(
	// 		customer.Type
	// 	) as NotificationActionType;

	// 	try {
	// 		customer.Read = true;
	// 		// console.log(customer);
	// 		customer.ReadBy = [...customer.ReadBy, this.user];
	// 		this.courseNotificationService.edit(customer).toPromise();
	// 		this.count--;
	// 		this.courseNotificationService.notificationCount.next(this.count);
	// 	} catch (error) {
	// 		// console.log(error);
	// 	}
	// }

	// /**
	//  * Return action from type (number) of notification retrived from
	//  * database CourseNotification entity
	//  * @param type
	//  */
	// getNotificationFromType(type: number): NotificationActionType | string {
	// 	switch (type) {
	// 		case this.Notification_Actions.VERIFY_COURSE.type:
	// 			return this.Notification_Actions.VERIFY_COURSE;
	// 		case this.Notification_Actions.SEE_REPORTED_COURSE.type:
	// 			return this.Notification_Actions.SEE_REPORTED_COURSE;
	// 		default:
	// 			return 'Non spécifié';
	// 	}
	// }

	// /**
	//  * Truncate string when length is too long
	//  * @param value
	//  */
	// truncateString(value: string): string {
	// 	return this.utilService.truncateString(value, 20);
	// }

	/**
	 * Navigate to the page of course to verify or the reported course
	 * @param action
	 */
	// resolveNotifAction(action: NotificationActionType): void {
	// 	switch (action.type) {
	// 		case this.Notification_Actions.VERIFY_COURSE.type:
	// 			this.router.navigateByUrl('/admin/privateCours');

	// 			return;
	// 		case this.Notification_Actions.SEE_REPORTED_COURSE.type:
	// 			this.router.navigateByUrl(
	// 				`/admin/course-detail/${this.selectedNotification.Course.Slug}`
	// 			);
	// 			return;
	// 		default:
	// 			return;
	// 	}
	// }

	/**
	 * Check if user have click to delete the notification
	 * @param notif
	 */
	// isDeleting(notif: CourseNotification): boolean {
	// 	return this.deletingNotif.find((n: CourseNotification) => n.Id === notif.Id)
	// 		? true
	// 		: false;
	// }

	/**
	 * Check if user have already read notification
	 */
	// userHaveReadNotif(notif: CourseNotification): boolean {
	// 	return notif.ReadBy.find((user: User) => user.Id === this.user.Id)
	// 		? true
	// 		: false;
	// }

	/**
	 * Config
	 */

	confirm() {
		if (
			this.billingAlreadyActive ||
			this.allBillingSettingsFetched.length === 0
		) {
			return;
		}
		this.confirmationService.confirm({
			header:
				'Attention: Etes vous sûre de vouloir activer cette configuration ?',
			message:
				"L'activation de cette configuration aura des conféquence immédiates et irréversibles sur tout le système de facuration. Voulez vous vraiment activer configuration ?",
			accept: () => {
				//Actual logic to perform a confirmation
				this.activeSetting();
			}
		});
	}

	activeSetting() {
		if (this.allBillingSettingsFetched.length === 0) {
			return;
		}
		this.loadingSettingActivation = true;

		try {
			this.billingSettingService
				.activeBillingSetting(this.selectedbillingSetting)
				.subscribe((pendingSetting: BillingSetting) => {
					this.loadingSettingActivation = false;
					// this.activBillingSetting = activeBilling;
					this.pendingSetting = pendingSetting;

					const index = this.allBillingSettingsFetched.indexOf(
						this.allBillingSettingsFetched.find(
							(s: BillingSetting) => s.Id === pendingSetting.Id
						)
					);
					this.allBillingSettingsFetched[index] = pendingSetting;
					this.getBillingSettings();
					this.toastrService.success(
						'Configuration activé',
						'Activation réussie'
					);
				});
		} catch (error) {
			console.log(error);
			this.loading = false;
			this.toastrService.error(
				"Une erreur est survenue lors de l'activation",
				"L'activation a échouée"
			);
		}

		// setTimeout(() => {
		// 	this.loadingSettingActivation = false;
		// 	this.toastrService.success('Configuration activé', 'Activation réussie');
		// }, 2000);
	}

	showDetailsSettingModal(): void {
		if (
			!this.selectedbillingSetting ||
			this.allBillingSettingsFetched.length === 0
		) {
			return;
		}
		const setting = this.allBillingSettingsFetched.find(
			(s: BillingSetting) => s.Slug === this.selectedbillingSetting
		);
		this.selectedBillingSettingFetched = setting;
		// this.selectedBillingSettingFetched.Device = this.getDevise(
		// 	this.selectedBillingSettingFetched.Device
		// );

		this.selectedBillingSettingCanBeModify = !(
			setting.AlreadyUse || setting.Status === BillingSettingStatus.ENABLED
		);
		console.log('Selected setting: ', setting);
		this.displayDetailsSettingDialog = true;
	}

	showCreateSettingModal() {
		this.billingSettingForm.reset({
			AlreadyUse: false,
			Device: 'USD',
			Name: '',
			PourcentBuy: '',
			Status: 3,
			ViewingCount: '',
			ViewingPrice: ''
		});
		this.loadingSave = false;
		this.displayCreateSettingDialog = true;
	}

	showSettingHistoryModal() {
		if (
			!this.selectedbillingSetting ||
			this.allBillingSettingsFetched.length === 0
		) {
			return;
		}
		this.displaySettingHistoryDialog = true;
	}

	getDevise(deviceNumber): string {
		switch (deviceNumber) {
			case BillingSettingDevice.EURO:
				return 'EURO';
			case BillingSettingDevice.USD:
				return 'USD';
			case BillingSettingDevice.XAF:
				return 'XAF';
			default:
				return '';
		}
	}

	formatDate(date: Date): string {
		return this.utilService.FormatDateToDayMonthYear(new Date(date));
	}

	async saveSetting(): Promise<void> {
		this.loadingSave = true;
		console.log('*** world');

		let setting = this.billingSettingForm.getRawValue();
		console.log('*** fdfello', setting);

		// if (this.updatingSetting) {
		// 	setting.Device = this.getDevise(setting.Device);
		// } else {
		setting.Device = this.getDeviseNumberFromString(setting.Device);
		// }
		console.log('*** ffffffo');

		setting = setting as BillingSetting;
		console.log('*** hello');
		let exist = false;
		this.allBillingSettingsFetched?.map((s: BillingSetting) => {
			if (s.Name.toLocaleLowerCase() === setting.Name.toLocaleLowerCase()) {
				exist = true;
				return;
			}
		});

		if (setting.PourcentBuy > 100) {
			this.toastrService.error(
				'Le pourcentage ne peut exéder 100',
				'Pourcentage incorrecte'
			);
			this.loadingSave = false;
			return;
		}

		if (
			setting.PourcentBuy < 0 ||
			setting.ViewingCount < 0 ||
			setting.ViewingPrice < 0
		) {
			this.toastrService.error(
				'Veuillez saisir des chiffre positif uniquement',
				"Aucune valeur négative n'est autorisé"
			);
			this.loadingSave = false;
			return;
		}

		if (exist && !this.updatingSetting) {
			this.toastrService.error(
				'Une configuration avec ce nom existe déjà',
				'Le nom de la configuration est unique'
			);
			this.loadingSave = false;
			return;
		}

		console.log('New setting', setting);

		if (
			!setting.PourcentBuy ||
			setting.PourcentBuy === '' ||
			!setting.ViewingCount ||
			setting.ViewingCount === '' ||
			!setting.ViewingPrice ||
			setting.ViewingPrice === ''
		) {
			this.toastrService.error(
				'Veuillez remplir tous les champs',
				'Tous les champs sont obligatoires'
			);
			this.loadingSave = false;
			return;
		}

		try {
			console.log('*** sauvegard');
			let saved: BillingSetting;
			if (this.updatingSetting) {
				setting.Slug = this.selectedBillingSettingFetched.Slug;
				setting.Id = this.selectedBillingSettingFetched.Id;
				saved = await this.billingSettingService
					.edit(setting as BillingSetting)
					.toPromise();
			} else {
				saved = await this.billingSettingService
					.add(setting as BillingSetting)
					.toPromise();
			}
			console.log('Saved', saved);
			this.displayCreateSettingDialog = false;
			this.toastrService.success(
				'Enregistrement réussie',
				'La nouvelle configuration a été crée'
			);

			if (this.updatingSetting) {
				if (saved.Status === BillingSettingStatus.ENABLED) {
					this.activBillingSetting = saved;
				}

				const index = this.allBillingSettingsFetched.indexOf(
					this.allBillingSettingsFetched.find(
						(s: BillingSetting) => s.Id === saved.Id
					)
				);
				this.allBillingSettingsFetched[index] = saved;
			} else {
				this.getBillingSettings();
			}

			this.selectedbillingSetting = saved.Slug;

			this.loadingSave = false;
			this.billingSettingForm.reset();
		} catch (error) {
			this.toastrService.error(
				'Une erreur est survenue lors de la sauvegarde',
				'La sauvegarde de la nouvelle configuration a échoué'
			);
			this.loadingSave = false;
		}
	}

	async getBillingSettings(): Promise<void> {
		this.loadingSetting = true;
		this.allBillingSettingsFetched = await this.billingSettingService
			.getAll()
			.toPromise();
		console.log('All settgins: ', this.allBillingSettingsFetched);

		const s: { id: number; label: string; value: string }[] = [];
		this.allBillingSettingsFetched?.map((setting: BillingSetting) => {
			s.push({
				id: setting.Id,
				label: setting.Name,
				value: setting.Slug
			});
			if (setting.Status === BillingSettingStatus.ENABLED) {
				this.selectedbillingSetting = setting.Slug;
				this.activBillingSetting = setting;
				this.billingAlreadyActive = true;
			}
		});
		if (s.length > 0 && !this.selectedbillingSetting) {
			this.selectedbillingSetting = s[0].value;
		}
		this.billingSettings = s;
		this.loadingSetting = false;
	}

	getDeviseNumberFromString(deviseString: string): number {
		switch (deviseString.toLocaleLowerCase()) {
			case 'usd':
				return 3;
			case 'euro':
				return 1;
			case 'xaf':
				return 2;
		}
	}

	updateBillingSetting(): void {
		if (!this.selectedBillingSettingCanBeModify) {
			return;
		}
		// const setting = this.allBillingSettingsFetched.find(
		// 	(s: BillingSetting) => s.Slug === this.selectedbillingSetting
		// ) as any;
		const setting = this.selectedBillingSettingFetched;
		if (!setting) {
			this.toastrService.error(
				'Configuration introuvable',
				'impossible de modifier la configuration'
			);
			return;
		}
		setting.Device = this.getDevise(setting.Device);
		this.displayDetailsSettingDialog = false;
		this.updatingSetting = true;
		console.log('Edit', setting);
		this.billingSettingForm.reset(setting);
		setTimeout(() => (this.displayCreateSettingDialog = true), 300);
	}

	handleChange(event) {
		console.log(event.value);
		const setting = this.allBillingSettingsFetched.find(
			(s: BillingSetting) => s.Slug === event.value
		);
		console.log(setting);
		if (setting.Status === BillingSettingStatus.ENABLED) {
			console.log('disabled');
			this.billingAlreadyActive = true;
		} else {
			console.log('Enable');
			this.billingAlreadyActive = false;
		}
	}

	async init(): Promise<void> {
		try {
			this.loadingInit = true;
			await this.billingService.init().toPromise();
			this.toastrService.success('Système de facturation initialisé');
		} catch (error) {
			this.loadingInit = false;
			this.toastrService.error('Error, Ecech de la génération de la facture');
		}
	}

	async generate(): Promise<void> {
		try {
			this.loadingG = true;
			await this.billingService.generate().toPromise();
			this.loadingG = false;
			this.toastrService.success('Génération des factures terminé');
		} catch (error) {
			this.loadingG = false;
			this.toastrService.error('Error, Ecech de la génération de la facture');
		}
	}
}
