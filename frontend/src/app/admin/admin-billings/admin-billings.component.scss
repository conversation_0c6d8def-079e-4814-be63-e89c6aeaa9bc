.skin_notif {
	background: #ebebec;
}

.list-holder {
	padding-right: 3em;
	padding-left: 3em;
	margin-top: 8em;
}

tr.unread {
	span {
		font-weight: bold !important;
	}
}

h1,
h2,
h3 {
	display: block;
}

.small {
	font-size: 1rem;
	display: block;
}

.notif-content {
	max-width: 710px;
	margin: auto;
	flex-direction: column !important;

	.notif-date {
		margin-left: auto;
	}
}

.notif-btn {
	display: flex;
}

.notif-delete-btn {
	display: flex;
	justify-content: flex-start;
	align-items: center;
	color: #dc3545 !important;
	cursor: pointer;
}

.table-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.ng-tns-c486-1.p-dialog-content {
	height: 90vh !important;
}

.instructor-infos {
	.col-6 {
		margin: auto;
	}

	h2 {
		color: #007bff;
	}

	h4 {
		span {
			font-weight: bold;
		}
	}

	p {
		span {
			font-weight: bold;
			color: #000;
		}
	}

	.instructor-avatar {
		width: 200px;
		height: 200px;
		border-radius: 10px;
		overflow: hidden;

		img {
			width: 100%;
			height: 100%;
			object-fit: contain;
		}
	}
}
