import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { MessageService, ConfirmationService } from 'primeng/api';
import {
	ContentType,
	Course,
	CourseSearch,
	CourseSearchResult,
	OrderbyType
} from '~/app/models/course.model';
import { CourseService } from '~/app/services/courses/course.service';
import { UtilsService } from '~/app/services/utils.service';
import { UserService } from '~/app/services/users/user.service';
import { User } from '~/app/models/user.model';
import { Category } from '~/app/models/category.model';
import { CategoryService } from '~/app/services/categories/category.service';
import { ToastrService } from 'ngx-toastr';

@Component({
	selector: 'app-admin-course-verification',
	templateUrl: './admin-course-verification.component.html',
	styleUrls: ['./admin-course-verification.component.scss'],
	encapsulation: ViewEncapsulation.None
})
export class AdminCourseVerificationComponent implements OnInit {
	date12: Date;
	options = [];
	user: User;
	reasons = [
		{ name: 'Raison 1', code: 'NY' },
		{ name: '<PERSON>son 2', code: 'RM' },
		{ name: 'Raison 3', code: 'LDN' }
	];
	reason: any;
	selectedReason: any;
	showRaisonModal = false;
	courses: Course[];

	take: number = 12;
	paginationOptions: number[] = [2, 5, 10, 12, 15, 20];
	loading: boolean = false;
	count: number;

	status: string;

	search: CourseSearch = {
		Title: '',
		Categories: [],
		Type: [ContentType.PDF, ContentType.VIDEO, ContentType.EBOOK],
		PriceType: [],
		Orderby: OrderbyType.RECENT,
		// Archived: false,
		UnPublished: true,
		// Reported: true,
		// UnReported: true,
		New: true
	};
	loadingSearch = false;
	// searchChange: EventEmitter<CourseSearch> = new EventEmitter<CourseSearch>();

	skip = 0;
	// skipChange: EventEmitter<number> = new EventEmitter<number>();
	// take: number;
	// takeChange: EventEmitter<number> = new EventEmitter<number>();

	// courseSearchResult: CourseSearchResult;
	// courseSearchResultChange: EventEmitter<CourseSearchResult> = new EventEmitter<CourseSearchResult>();

	categories: Category[];
	contentType = ContentType;
	courseSearchResult: Course[];

	error: boolean = false;

	constructor(
		private translateService: TranslateService,
		private courseService: CourseService,
		private categoryService: CategoryService,
		private toastrService: ToastrService
	) {}

	async ngOnInit(): Promise<void> {
		const menu = await this.translateService.get('admin.options').toPromise();
		this.options = [
			{ name: 'Publier', icon: 'publish', id_option: 1 },
			{ name: 'Archiver', icon: 'delete', id_option: 2 }
		];
		this.categories = await this.categoryService.getAll().toPromise();
		this.getCourses();
	}

	async getCourses(): Promise<void> {
		this.loading = true;
		this.error = false;

		try {
			const response = await this.courseService
				.getAllCourseToVerifyLazy(this.take)
				.toPromise();
			this.courses = response.Courses;
			this.count = response.Count;
			this.error = false;
			this.loading = false;
			console.log('***', this.courses);
		} catch (error) {
			this.courses = [];
			this.error = true;
			this.toastrService.success(
				'Une erreurs est survenue lors du traitement',
				'Impossible de charger la page'
			);
			this.courses = [];
		}
	}

	onCoursePublish(course: Course): void {
		const index = this.courses?.map((c) => c.Id).indexOf(course.Id);
		this.courses.splice(index, 1);
	}

	onCourseDelete(course: Course): void {
		const index = this.courses?.map((c) => c.Id).indexOf(course.Id);
		this.courses.splice(index, 1);
		this.getCourses();
	}

	async paginate(event: {
		page: number;
		first: number;
		rows: number;
		pageCount: number;
	}): Promise<void> {
		this.loading = true;
		this.error = false;

		console.log(event);
		this.take = event.rows;
		const skip = event.first;
		// console.log(skip, take);
		this.error = false;

		try {
			let response;

			if (this.status) {
				this.search.New = this.status === 'NEW' ? true : false;
				this.search.UnPublished = this.status === 'UNPUBLISHED' ? true : false;

				response = await this.courseService
					.searchCourseToVerifyLazy({ take: this.take, skip }, this.search)
					.toPromise();
				console.log(response);
				this.count = response.Count;
			} else {
				response = await this.courseService
					.getAllCourseToVerifyLazy(this.take, skip)
					.toPromise();
			}

			this.courses = response.Courses;
		} catch (error) {
			console.log(error);
			this.error = true;
			this.toastrService.success(
				'Une erreurs est survenue lors du traitement',
				'Impossible de charger la page'
			);
			this.courses = [];
		} finally {
			this.loading = false;
		}
	}

	onSearch(e): void {
		e.preventDefault();

		console.log('STatus: ', this.status);
		this.search.New = this.status === 'NEW' ? true : false;
		this.search.UnPublished = this.status === 'UNPUBLISHED' ? true : false;
		console.log(this.search);

		this.loading = true;
		this.loadingSearch = true;
		this.error = false;
		this.courses = [];

		console.log('serach is :', this.search);
		this.take = this.take;
		this.skip = this.skip;
		try {
			this.courseService
				.searchCourseToVerifyLazy(
					{ take: this.take, skip: this.skip },
					this.search
				)
				.subscribe((res) => {
					console.log('the search result on the filter is :', res);
					this.courseSearchResult = res.Courses;
					this.courses = res.Courses;
					this.count = res.Count;
					setTimeout(() => {
						this.loading = false;
						this.loadingSearch = false;
					}, 300);
				});
		} catch (error) {
			this.error = true;
			this.courses = [];
			this.toastrService.success(
				'Une erreurs est survenue lors du traitement',
				'Impossible de charger la page'
			);
			this.courses = [];
		}
	}
}
