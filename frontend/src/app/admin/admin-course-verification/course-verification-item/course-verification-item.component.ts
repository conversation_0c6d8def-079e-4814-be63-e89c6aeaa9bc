import {
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	ViewEncapsulation
} from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ToastrService } from 'ngx-toastr';
import { ConfirmationService, MessageService } from 'primeng/api';
import { CourseBaseContent } from '~/app/models/course-base-content.model';
import { CourseReport } from '~/app/models/course-report.model';
import { CourseSection } from '~/app/models/course-section.model';
import { Course, ContentType } from '~/app/models/course.model';
import { CourseReportService } from '~/app/services/courses/course-report.service';
import { CourseService } from '~/app/services/courses/course.service';
import { UtilsService } from '~/app/services/utils.service';
import { environment } from '~/environments/environment';

@Component({
	selector: 'app-course-verification-item',
	templateUrl: './course-verification-item.component.html',
	styleUrls: ['./course-verification-item.component.scss'],
	encapsulation: ViewEncapsulation.None
})
export class CourseVerificationItemComponent implements OnInit {
	@Input() course: Course;
	@Input() options: any;
	localOptions: any;

	@Output() onPublish: EventEmitter<Course> = new EventEmitter<Course>();
	@Output() onDelete: EventEmitter<Course> = new EventEmitter<Course>();

	reason: any;
	selectedReason: any;
	showRaisonModal = false;

	courseHasBeenReported: boolean = false;
	courseHasUnPublished: boolean = false;
	courseHasArchived: boolean = false;
	courseIsNew: boolean = false;
	courseIsPublished: boolean = false;

	videoFormat = false;
	pdfFormat = false;
	eBookFormat = false;

	error: boolean = false;
	loading: boolean = false;

	constructor(
		private confirmationService: ConfirmationService,
		private messageService: MessageService,
		private utilsService: UtilsService,
		private courseService: CourseService,
		private courseReportService: CourseReportService,
		private translateService: TranslateService,
		private toastrService: ToastrService
	) {}

	async ngOnInit(): Promise<void> {
		const menu = await this.translateService.get('admin.options').toPromise();

		this.courseHasBeenReported = this.utilsService.checkIfCourseHasReport(
			this.course
		);
		// console.log('Reported', this.courseHasBeenReported);
		// this.courseHasUnPublished = !(
		// 	this.course.Published === false && this.course.LastPublishedDate === null
		// );

		this.courseHasUnPublished =
			this.course.Published === false && this.course.LastPublishedDate != null;

		this.courseIsNew =
			this.course.Published === false && this.course.LastPublishedDate === null;

		this.courseIsPublished = this.course.Published === true;

		console.log(this.course.CreatedAt);
		console.log(this.course.UpdatedAt);
		console.log(this.course.LastPublishedDate);
		console.log(
			this.course.Published === false && this.course.LastPublishedDate === null
		);
		// console.log(this.course.Published);

		// console.log(this.course.LastPublishedDate, this.courseHasUnPublished);

		this.courseHasArchived = this.course.Archived;
		this.localOptions = [];
		if (this.courseIsPublished) {
			this.localOptions.push({
				name: 'Dépublier',
				icon: 'upgrade',
				id_option: 4
			});
		} else {
			this.localOptions.push({
				name: 'Publier',
				icon: 'publish',
				id_option: 1
			});
		}

		if (this.course.Archived) {
			this.localOptions.push({
				name: 'Déarchiver',
				icon: 'upgrade',
				id_option: 3
			});
		} else {
			this.localOptions.push({
				name: 'Archiver',
				icon: 'delete',
				id_option: 2
			});
		}

		// if (this.courseIsPublished) {
		// 	console.log('*** IS PUBLISHED', this.course.Published);
		// 	this.localOptions[0] = {
		// 		name: 'Dépublier',
		// 		icon: 'upgrade',
		// 		id_option: 4
		// 	};
		// } else {
		// 	console.log('*** IS not publish PUBLISHED', this.course.Published);
		// 	this.localOptions[0] = {
		// 		name: 'Pubier',
		// 		icon: 'publish',
		// 		id_option: 1
		// 	};
		// }

		// if (this.courseHasArchived) {
		// 	this.localOptions[1] = {
		// 		name: 'Déarchiver',
		// 		icon: 'upgrade',
		// 		id_option: 3
		// 	};
		// }

		this.videoFormat = this.course.Format === ContentType.VIDEO;
		this.pdfFormat = this.course.Format === ContentType.PDF;
		this.eBookFormat = this.course.Format === ContentType.EBOOK;
	}

	selectedOptionFxn(event, option): void {
		switch (option.id_option) {
			case 1:
				this.confirmPublish(event, option);
				break;
			case 2:
				this.confirmArchive(event, option);
				break;
			case 3:
				this.confirmUnArchive(event, option);
				break;
			case 4:
				this.confirmUnPublish(event, option);
				break;
			default:
				break;
		}
	}

	confirmArchive(event: Event, customer: any): void {
		console.log(event);
		this.confirmationService.confirm({
			target: event.target,
			header: "Confirmation de l'archivage",
			message: 'Voulez-vous vraiment archiver ce cours?',
			icon: 'pi pi-exclamation-triangle',
			accept: async () => {
				this.course.Archived = true;
				this.course.Published = false;
				this.loading = true;

				try {
					this.course = await this.courseService
						.edit({ ...this.course, CoverImage: null })
						.toPromise();
					this.onDelete.emit(this.course);
					console.log(this.course);
					this.loading = false;
				} catch (error) {
					this.toastrService.error(
						'Une erreurs est survenue lors du traitement',
						'Impossible de charger la page'
					);
					this.error = true;
					this.loading = false;
				}
			},
			reject: () => {
				// this.messageService.add({
				// 	severity: 'error',
				// 	summary: 'Rejected',
				// 	detail: 'You have rejected'
				// });
			}
		});
	}

	confirmUnArchive(event: Event, customer: any): void {
		console.log(event);
		this.confirmationService.confirm({
			target: event.target,
			header: 'Confirmation du déarchivage',
			message: 'Voulez-vous vraiment déarchiver ce cours ?',
			icon: 'pi pi-exclamation-triangle',
			accept: async () => {
				this.course.Archived = false;
				this.course.Published = false;
				this.loading = true;

				try {
					this.course = await this.courseService
						.edit({ ...this.course, CoverImage: null })
						.toPromise();
					this.onDelete.emit(this.course);
					console.log(this.course);

					this.loading = false;
				} catch (error) {
					this.toastrService.error(
						'Une erreurs est survenue lors du traitement',
						'Impossible de charger la page'
					);
					this.error = true;
					this.loading = false;
				}
			},
			reject: () => {}
		});
	}

	confirmPublish(event: Event, customer: any): void {
		console.log('publish');
		this.confirmationService.confirm({
			target: event.target,
			header: 'Confirmation de la publication',
			message: 'Voulez-vous vraiment publier ce cours ?',
			icon: 'pi pi-exclamation-triangle',
			accept: async () => {
				this.course.Published = true;
				this.course.Archived = false;
				this.loading = true;

				try {
					if (this.courseHasBeenReported) {
						// supprimer tous les reports
						this.course.Sections?.map((section: CourseSection) => {
							section.Contents?.map((content: CourseBaseContent) => {
								content.Reports?.map(async (report: CourseReport) => {
									report.Active = false;
									const res = await this.courseReportService
										.edit(report)
										.toPromise();
								});
							});
						});
					}
					this.course = await this.courseService
						.edit({ ...this.course, CoverImage: null })
						.toPromise();
					this.onPublish.emit(this.course);
					console.log(this.course);
					this.loading = false;
				} catch (error) {
					this.course = await this.courseService
						.edit({ ...this.course, CoverImage: null })
						.toPromise();
					this.onDelete.emit(this.course);
					console.log(this.course);
					this.error = true;
					this.loading = false;
				}
			},
			reject: () => {
				// this.messageService.add({
				// 	severity: 'error',
				// 	summary: 'Rejected',
				// 	detail: 'You have done nothing'
				// });
			}
		});
	}

	confirmUnPublish(event: Event, customer: any): void {
		console.log('unpublish');
		this.confirmationService.confirm({
			target: event.target,
			header: 'Confirmation de la mise hors ligne du cours',
			message: 'Voulez-vous vraiment mettre ce cours hors ligne  ?',
			icon: 'pi pi-exclamation-triangle',
			accept: async () => {
				this.course.Published = false;
				this.course.Archived = false;
				this.loading = true;

				try {
					this.course = await this.courseService
						.edit({ ...this.course, CoverImage: null })
						.toPromise();
					this.onPublish.emit(this.course);
					console.log(this.course);
					this.loading = false;
				} catch (error) {
					console.log(error);
					this.toastrService.error(
						'Une erreurs est survenue lors du traitement',
						'Impossible de charger la page'
					);
					this.error = true;
					this.loading = false;
				}
			},
			reject: () => {
				// this.messageService.add({
				// 	severity: 'error',
				// 	summary: 'Rejected',
				// 	detail: 'You have done nothing'
				// });
			}
		});
	}

	addReasonToSignal(): void {
		// Apres la requete, en cas de success;
		this.messageService.add({
			severity: 'info',
			summary: 'Edit',
			detail: 'Contenu signale'
		});
		this.showRaisonModal = false;

		// En cas d'echec
		// this.messageService.add({ severity: 'error', summary: 'Edit', detail: 'Raison' });
	}

	getCourseCoverImage(): string {
		if (this.course.CoverImage) {
			return `${environment.path}/${this.course.CoverImage.Hashname}`;
		}
		return '/assets/img/700x500.png';
	}

	getUserProfileImage(): string {
		if (this.course.CreatedBy.Photo) {
			return `${environment.path}/${this.course.CreatedBy.Photo.Hashname}`;
		}
		return '/assets/img/avatar.png';
	}
}
