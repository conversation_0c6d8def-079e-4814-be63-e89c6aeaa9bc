<div class="course-verification">
	<p-card>
		<div class="row">
			<div class="col-sm-12 d-flex align-items-center">
				<img
					style="width: 50px; height: 50px; border-radius: 50%"
					[src]="getUserProfileImage()"
					alt="avatar"
				/>
				<small class="ml-2 mr-2">{{
					course.CreatedBy.Firstname + course.CreatedBy.Lastname
				}}</small>
				<small class="mr-2"> <b>.</b></small>
				<small>{{ course.CreatedAt | timeago }}</small>
			</div>
			<div class="col-sm-12 col-md-7 pr-0">
				<p
					class="font-bold"
					[routerLink]="'/admin/privateCours-details/' + course.Slug"
				>
					<a href="javascript:;">{{ course.Title }}</a>
				</p>
			</div>
			<div class="col-sm-12 col-md-5">
				<img
					class="w-100 rounded"
					style="height: 150px"
					[src]="getCourseCoverImage()"
					alt="avatar"
				/>
			</div>
			<div
				class="col-sm-12 mt-2 d-flex align-items-center justify-content-between"
			>
				<div class="left">
					<span *ngIf="videoFormat" class="customer-badge status-qualified"
						>VIDEO</span
					>
					<span *ngIf="pdfFormat" class="customer-badge status-qualified"
						>PDF</span
					>
					<span *ngIf="eBookFormat" class="customer-badge status-qualified"
						>EBOOK</span
					>
					<!-- <span class="customer-badge status-unqualified mr-1">PDF</span> -->
					<!-- <span class="customer-badge status-proposal mr-1">EBOOK</span> -->
					<!-- <span class="customer-badge status-qualified">VIDEO</span> -->
				</div>
				<div class="right">
					<span
						*ngIf="courseHasBeenReported"
						class="customer-badge status-unqualified mr-1"
						>Reported</span
					>
					<span *ngIf="courseIsNew" class="customer-badge status-qualified mr-1"
						>NEW</span
					>

					<span
						*ngIf="courseHasUnPublished"
						class="customer-badge status-proposal mr-1"
						>UBPUBLISHED</span
					>

					<span *ngIf="courseIsPublished" class="customer-badge status-new mr-1"
						>PUBLISHED</span
					>
					<button
						*ngIf="!loading"
						pButton
						pRipple
						type="button"
						icon="pi pi-ellipsis-v"
						class="p-button-rounded p-button-text ml-3"
						[matMenuTriggerFor]="menu"
					></button>
					<i *ngIf="loading" class="fas fa-spin fa-spinner mr-2"></i>

					<mat-menu #menu="matMenu">
						<button
							mat-menu-item
							*ngFor="let option of localOptions"
							(click)="selectedOptionFxn($event, option)"
						>
							<mat-icon>{{ option.icon }}</mat-icon>
							<span>{{ option.name }}</span>
						</button>
					</mat-menu>
				</div>
			</div>
		</div>
	</p-card>
</div>
