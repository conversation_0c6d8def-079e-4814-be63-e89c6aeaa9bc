import { Component, OnInit, ViewEncapsulation } from '@angular/core';

@Component({
  selector: 'app-admin',
  templateUrl: './admin.component.html',
  styleUrls: ['./admin.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class AdminComponent implements OnInit {

  constructor() { }

  ngOnInit(): void {
    // Add the skin class to the body for proper styling
    document.body.classList.add('skin');
  }

  ngOnDestroy(): void {
    // Remove the skin class when component is destroyed
    document.body.classList.remove('skin');
  }

}
