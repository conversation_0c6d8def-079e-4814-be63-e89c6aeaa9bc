.skin_notif {
	background: #ebebec;
}

.list-holder {
	padding-right: 3em;
	padding-left: 3em;
	margin-top: 8em;
}

tr.unread {
	span {
		font-weight: bold !important;
	}
}

h1,
h2,
h3 {
	display: block;
}

.small {
	font-size: 1rem;
	display: block;
}

.notif-content {
	max-width: 710px;
	margin: auto;
	flex-direction: column !important;

	.notif-date {
		margin-left: auto;
	}
}

.notif-btn {
	display: flex;
}

.notif-delete-btn {
	display: flex;
	justify-content: flex-start;
	align-items: center;
	color: #dc3545 !important;
	cursor: pointer;
}
