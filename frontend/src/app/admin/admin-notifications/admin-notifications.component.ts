import { Component, OnInit } from '@angular/core';
import {
	CourseNotification,
	courseNotificationType,
	NotificationActionType
} from '~/app/models/course-notification.model';
import { UtilsService } from '~/app/services/utils.service';
import { CourseNotificationsService } from '~/app/services/notifications/course-notifications.service';
import { Router } from '@angular/router';
import { UserService } from '~/app/services/users/user.service';
import { User } from '~/app/models/user.model';
import { ToastrService } from 'ngx-toastr';

@Component({
	selector: 'app-admin-notifications',
	templateUrl: './admin-notifications.component.html',
	styleUrls: ['./admin-notifications.component.scss']
})
export class AdminNotificationsComponent implements OnInit {
	/**
	 * Notification actions list
	 * this is the actions that user can do after
	 * that he have see the details of the notification
	 * eg: user see the notification that new course has been published
	 * he want to navigate to the page of course to verify to verify the
	 * new course and publish his
	 */
	Notification_Actions = {
		VERIFY_COURSE: {
			label: 'verifier le cours',
			title: 'Nouveau cours publié',
			type: courseNotificationType.COURSE_CREATE
		},
		SEE_REPORTED_COURSE: {
			label: 'Voir le course signalé',
			title: 'Cours Signalé',
			type: courseNotificationType.COURSE_REPORT
		}
	};

	/**
	 * Notificaitons list
	 */
	customers: CourseNotification[] = [];

	loadingDeleteNotif = false;
	deletingNotif: CourseNotification[] = [];

	// statuses: any[];
	loading = false;
	displayBasic2 = false;

	user: User;

	selectedNotification: CourseNotification = new CourseNotification(
		null,
		null,
		new Date(),
		null,
		'',
		null,
		false,
		null,
		1
	);
	action: NotificationActionType = this.Notification_Actions.VERIFY_COURSE;
	count: number;

	constructor(
		private utilService: UtilsService,
		private courseNotificationService: CourseNotificationsService,
		private router: Router,
		private userService: UserService,
		private taostrService: ToastrService
	) {}

	async ngOnInit(): Promise<void> {
		if (this.userService.isConnected()) {
			this.user = await this.userService.getUserConnected().toPromise();
		} else {
			this.router.navigateByUrl('/');
		}
		try {
			console.log('Récupération des notifications');
			this.loading = true;
			this.customers = await this.courseNotificationService
				.getAll()
				.toPromise();
			this.loading = false;
			this.courseNotificationService.notificationCount.subscribe(
				(count: number) => {
					this.count = count;
				}
			);
		} catch (error) {
			console.log(error);
			this.loading = false;
			this.taostrService.error(
				'Une erreur est survenue lors du chargement des notifications. Veuillez ressayer plutard',
				'Impossible de charger les notifications'
			);
		}
	}

	async confirmDelete(
		notification: CourseNotification = this.selectedNotification
	): Promise<void> {
		if (this.displayBasic2) {
			this.displayBasic2 = false;
		}
		this.deletingNotif.push(notification);
		this.loadingDeleteNotif = true;
		try {
			const res = await this.courseNotificationService
				.delete(notification.Slug)
				.toPromise();

			const index = this.customers.indexOf(this.selectedNotification);
			this.customers.splice(index, 1);
		} catch (error) {
			// console.log(error);
		} finally {
			if (notification.Read === false) {
				this.count--;
				this.courseNotificationService.notificationCount.next(this.count);
			}
			const index = this.deletingNotif.indexOf(notification);
			this.deletingNotif.splice(index, 1);
			this.loadingDeleteNotif = false;
		}
	}

	/**
	 * Open modal when user click on row in the table
	 * the modal show user the details of notification that he has click
	 * @param customer, notification model
	 */
	openMessageModal(customer: CourseNotification): void {
		this.displayBasic2 = true;
		this.selectedNotification = customer;
		this.selectedNotification = customer;
		this.action = this.getNotificationFromType(
			customer.Type
		) as NotificationActionType;

		try {
			customer.Read = true;
			// console.log(customer);
			customer.ReadBy = [...customer.ReadBy, this.user];
			this.courseNotificationService.edit(customer).toPromise();
			this.count--;
			this.courseNotificationService.notificationCount.next(this.count);
		} catch (error) {
			// console.log(error);
		}
	}

	/**
	 * Return action from type (number) of notification retrived from
	 * database CourseNotification entity
	 * @param type
	 */
	getNotificationFromType(type: number): NotificationActionType | string {
		switch (type) {
			case this.Notification_Actions.VERIFY_COURSE.type:
				return this.Notification_Actions.VERIFY_COURSE;
			case this.Notification_Actions.SEE_REPORTED_COURSE.type:
				return this.Notification_Actions.SEE_REPORTED_COURSE;
			default:
				return 'Non spécifié';
		}
	}

	/**
	 * Truncate string when length is too long
	 * @param value
	 */
	truncateString(value: string): string {
		return this.utilService.truncateString(value, 20);
	}

	/**
	 * Navigate to the page of course to verify or the reported course
	 * @param action
	 */
	resolveNotifAction(action: NotificationActionType): void {
		switch (action.type) {
			case this.Notification_Actions.VERIFY_COURSE.type:
				this.router.navigateByUrl(
					`/admin/privateCours-details/${this.selectedNotification.Slug}`
				);

				return;
			case this.Notification_Actions.SEE_REPORTED_COURSE.type:
				this.router.navigateByUrl(
					`/admin/course-detail/${this.selectedNotification.Course.Slug}`
				);
				return;
			default:
				return;
		}
	}

	/**
	 * Check if user have click to delete the notification
	 * @param notif
	 */
	isDeleting(notif: CourseNotification): boolean {
		return this.deletingNotif.find((n: CourseNotification) => n.Id === notif.Id)
			? true
			: false;
	}

	/**
	 * Check if user have already read notification
	 */
	userHaveReadNotif(notif: CourseNotification): boolean {
		return notif.ReadBy.find((user: User) => user.Id === this.user.Id)
			? true
			: false;
	}
}
