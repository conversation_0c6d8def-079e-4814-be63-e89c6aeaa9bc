import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AdminInstructorsComponent } from './admin-instructors/admin-instructors.component';
import { AdminUsersComponent } from './admin-users/admin-users.component';
import { AdminMeetingsComponent } from './admin-meetings/admin-meetings.component';
import { AdminSubscriptionsComponent } from './admin-subscriptions/admin-subscriptions.component';
import { AdminCoursesComponent } from './admin-courses/admin-courses.component';
import { AdminUserDetailComponent } from './admin-user-detail/admin-user-detail.component';
import { AdminInstructorDetailComponent } from './admin-instructor-detail/admin-instructor-detail.component';
import { AdminMessageDetailsComponent } from './admin-message-details/admin-message-details.component';
import { AdminMessagesComponent } from './admin-messages/admin-messages.component';
import { AdminCourseDetailsComponent } from './admin-course-details/admin-course-details.component';
import { AdminCategoriesComponent } from '~/app/admin/admin-categories/admin-categories.component';
import { AdminCategoriesFormComponent } from '~/app/admin/admin-categories/admin-categories-form/admin-categories-form.component';
import { AdminSubCategoriesComponent } from '~/app/admin/admin-categories/admin-sub-categories/admin-sub-categories.component';
import { AdminSubCategoriesFormComponent } from '~/app/admin/admin-categories/admin-sub-categories-form/admin-sub-categories-form.component';
import { AdminCourseVerificationComponent } from './admin-course-verification/admin-course-verification.component';
import { AdminCourseVerifDetailsComponent } from './admin-course-verif-details/admin-course-verif-details.component';
import { AdminRequestComponent } from '~/app/admin/admin-request/admin-request.component';
import { AdminNotificationsComponent } from './admin-notifications/admin-notifications.component';
import { AdminCourseReportsComponent } from './admin-course-reports/admin-course-reports.component';
import { AdminArchivedCoursesComponent } from './admin-archived-courses/admin-archived-courses.component';
import { AdminPoliciesComponent } from './admin-policies/admin-policies.component';
import { AdminPoliciesTypeComponent } from './admin-policies/admin-policies-type/admin-policies-type.component';
import { AdminBillingsComponent } from './admin-billings/admin-billings.component';
import { AdminBillingSettingComponent } from './admin-billings/admin-billing-setting/admin-billing-setting.component';
import { BackofficeLayoutComponent } from './layouts/backoffice-layout.component';

const routes: Routes = [
	{
		path: '',
		component: BackofficeLayoutComponent,
		children: [
			{
				path: '',
				component: AdminSubscriptionsComponent
			},
			{
				path: 'instructors',
				component: AdminInstructorsComponent
			},
			{
				path: 'users',
				component: AdminUsersComponent
			},
			{
				path: 'users-details/:slug',
				component: AdminUserDetailComponent
			},
			{
				path: 'instructor-details/:slug',
				component: AdminInstructorDetailComponent
			},
			{
				path: 'meetings',
				component: AdminMeetingsComponent
			},
			{
				path: 'requests',
				component: AdminRequestComponent
			},
			{
				path: 'subscriptions',
				component: AdminSubscriptionsComponent
			},
			{
				path: 'courses',
				component: AdminCoursesComponent
			},
			{
				path: 'course-detail/:slug',
				component: AdminCourseDetailsComponent
			},
			{
				path: 'message/:slug',
				component: AdminMessageDetailsComponent
			},
			{
				path: 'messages',
				component: AdminMessagesComponent
			},
			{
				path: 'categories',
				children: [
					{ path: '', component: AdminCategoriesComponent },
					{ path: 'form', component: AdminCategoriesFormComponent },
					{
						path: 'sub',
						children: [
							{ path: ':id', component: AdminSubCategoriesComponent },
							{ path: 'form', component: AdminSubCategoriesFormComponent }
						]
					}
				]
			},
			{
				path: 'privateCours',
				component: AdminCourseVerificationComponent
			},
			{
				path: 'privateCours-details/:id',
				component: AdminCourseVerifDetailsComponent
			},
			{
				path: 'notifications',
				component: AdminNotificationsComponent
			},
			{
				path: 'reported-courses',
				component: AdminCourseReportsComponent
			},
			{
				path: 'archived-courses',
				component: AdminArchivedCoursesComponent
			},
			{
				path: 'policies',
				children: [
					{
						path: '',
						component: AdminPoliciesComponent
					},
					{
						path: ':page',
						component: AdminPoliciesComponent
					}
				]
			},
			{
				path: 'billings',
				children: [
					{
						path: '',
						component: AdminBillingsComponent
					},
					{
						path: 'settings',
						component: AdminBillingSettingComponent
					}
				]
			}
		]
	}
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
	exports: [RouterModule]
})
export class AdminRoutingModule {}
