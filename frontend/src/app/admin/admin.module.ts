import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AdminInstructorsComponent } from './admin-instructors/admin-instructors.component';
import { AdminInstructorDetailComponent } from './admin-instructor-detail/admin-instructor-detail.component';
import { AdminUsersComponent } from './admin-users/admin-users.component';
import { AdminUserDetailComponent } from './admin-user-detail/admin-user-detail.component';
import { AdminCoursesComponent } from './admin-courses/admin-courses.component';
import { AdminMeetingsComponent } from './admin-meetings/admin-meetings.component';
import { AdminSubscriptionsComponent } from './admin-subscriptions/admin-subscriptions.component';
import { AdminRoutingModule } from './admin-routing.module';
import { MenuModule } from 'primeng/menu';
import { SharedModule } from '../shared/shared.module';
import { FormsModule } from '@angular/forms';
import { AdminSidebarComponent } from './admin-sidebar/admin-sidebar.component';
import { AdminComponent } from './admin.component';
import { ToolbarModule } from 'primeng/toolbar';
import { CardModule } from 'primeng/card';
import { TableModule } from 'primeng/table';
import { MultiSelectModule } from 'primeng/multiselect';
import { DropdownModule } from 'primeng/dropdown';
import { SliderModule } from 'primeng/slider';
import { ProgressBarModule } from 'primeng/progressbar';
import { ButtonModule } from 'primeng/button';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { ToastModule } from 'primeng/toast';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { TabViewModule } from 'primeng/tabview';
import { TagModule } from 'primeng/tag';
import { MatMenuModule } from '@angular/material/menu';
import { SidebarModule } from 'primeng/sidebar';
import { CalendarModule } from 'primeng/calendar';
import { DialogModule } from 'primeng/dialog';
import { ConfirmationService, MessageService } from 'primeng/api';
import { AdminMessagesComponent } from './admin-messages/admin-messages.component';
import { AdminMessageDetailsComponent } from './admin-message-details/admin-message-details.component';
import { AdminCourseDetailsComponent } from './admin-course-details/admin-course-details.component';
import { BadgeModule } from 'primeng/badge';
import { AdminMettingDialogComponent } from './admin-metting-dialog/admin-metting-dialog.component';
import { DynamicDialogModule } from 'primeng/dynamicdialog';
import { ChipModule } from 'primeng/chip';
import { TooltipModule } from 'primeng/tooltip';
import { AdminCategoriesComponent } from './admin-categories/admin-categories.component';
import { AdminCategoriesFormComponent } from './admin-categories/admin-categories-form/admin-categories-form.component';
import { AdminSubCategoriesComponent } from './admin-categories/admin-sub-categories/admin-sub-categories.component';
import { AdminSubCategoriesFormComponent } from './admin-categories/admin-sub-categories-form/admin-sub-categories-form.component';
import { InputTextModule } from 'primeng/inputtext';
import { RippleModule } from 'primeng/ripple';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { AdminCategoriesImgCropperComponent } from './admin-categories/admin-categories-img-cropper/admin-categories-img-cropper.component';
import { LyIconModule } from '@alyle/ui/icon';
import { LySliderModule } from '@alyle/ui/slider';
import { AdminCourseVerificationComponent } from './admin-course-verification/admin-course-verification.component';
import { AdminCourseVerifDetailsComponent } from './admin-course-verif-details/admin-course-verif-details.component';
import { PaginatorModule } from 'primeng/paginator';
import { AdminRequestComponent } from './admin-request/admin-request.component';
import { AdminRequestReplyComponent } from './admin-request/admin-request-reply/admin-request-reply.component';
import { AdminNotificationsComponent } from './admin-notifications/admin-notifications.component';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { CoursesModule } from '~/app/courses/courses.module';
import { AdminCourseReportsComponent } from './admin-course-reports/admin-course-reports.component';
import { AdminArchivedCoursesComponent } from './admin-archived-courses/admin-archived-courses.component';
import { CourseVerificationItemComponent } from './admin-course-verification/course-verification-item/course-verification-item.component';
import { AdminPoliciesComponent } from './admin-policies/admin-policies.component';
import { AdminPoliciesTypeComponent } from './admin-policies/admin-policies-type/admin-policies-type.component';
import { NotificationItemComponent } from './admin-notifications/notification-item/notification-item.component';
import { CheckboxModule } from 'primeng/checkbox';
import { RadioButtonModule } from 'primeng/radiobutton';
import { AdminBillingsComponent } from './admin-billings/admin-billings.component';
import { AdminBillingSettingComponent } from './admin-billings/admin-billing-setting/admin-billing-setting.component';
import { BackofficeLayoutComponent } from './layouts/backoffice-layout.component';

@NgModule({
	declarations: [
		AdminComponent,
		AdminInstructorsComponent,
		AdminInstructorDetailComponent,
		AdminUsersComponent,
		AdminUserDetailComponent,
		AdminCoursesComponent,
		AdminMeetingsComponent,
		AdminSubscriptionsComponent,
		AdminSidebarComponent,
		AdminMessagesComponent,
		AdminMessageDetailsComponent,
		AdminCourseDetailsComponent,
		AdminMettingDialogComponent,
		AdminCategoriesComponent,
		AdminCategoriesFormComponent,
		AdminSubCategoriesComponent,
		AdminSubCategoriesFormComponent,
		AdminCategoriesImgCropperComponent,
		AdminCourseVerificationComponent,
		AdminCourseVerifDetailsComponent,
		AdminRequestComponent,
		AdminRequestReplyComponent,
		AdminNotificationsComponent,
		AdminCourseReportsComponent,
		AdminArchivedCoursesComponent,
		CourseVerificationItemComponent,
		AdminPoliciesComponent,
		AdminPoliciesTypeComponent,
		NotificationItemComponent,
		AdminBillingsComponent,
		AdminBillingSettingComponent,
		BackofficeLayoutComponent
	],
	imports: [
		CommonModule,
		AdminRoutingModule,
		MenuModule,
		SharedModule,
		FormsModule,
		ToolbarModule,
		CardModule,
		TableModule,
		MultiSelectModule,
		DropdownModule,
		SliderModule,
		ProgressBarModule,
		ButtonModule,
		OverlayPanelModule,
		ConfirmPopupModule,
		ToastModule,
		MatSlideToggleModule,
		TabViewModule,
		TagModule,
		ChipModule,
		TooltipModule,
		PaginatorModule,
		BadgeModule,
		MatMenuModule,
		SidebarModule,
		CalendarModule,
		DialogModule,
		InputTextModule,
		RippleModule,
		ConfirmDialogModule,
		LyIconModule,
		LySliderModule,
		MatMenuModule,
		SidebarModule,
		CalendarModule,
		DialogModule,
		DynamicDialogModule,
		ConfirmDialogModule,
		InputTextareaModule,
		CoursesModule,
		CheckboxModule,
		RadioButtonModule
	],
	exports: [AdminSidebarComponent],
	entryComponents: [AdminMettingDialogComponent],
	providers: [ConfirmationService, MessageService],
	bootstrap: [AdminComponent]
})
export class AdminModule {}
