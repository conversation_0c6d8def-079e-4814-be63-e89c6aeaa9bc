<!-- Component content starts here -->
<div class="skin">
	<div id="main-wrapper">
		<app-admin-sidebar></app-admin-sidebar>

		<div class="row">
			<div class="col-md-2"></div>
			<div class="col-md-12 col-lg-10 col-sm-12 list-holder"> -->

	<p-card  [style]="{ 'margin-bottom': '2em' }">
		<p-table
			#dt
			[value]="courses"
			[rows]="10"
			[responsive]="true"
			[paginator]="true"
			[(selection)]="selectedCourse"
			[rowHover]="true"
			[loading]="loading"
			dataKey="id"
			currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
			[showCurrentPageReport]="true"
			[globalFilterFields]="[
				'Title',
				'CreatedBy.Firstname',
				'CreatedBy.Lastname',
				'categoriesFormated'
			]"
		>
			<ng-template pTemplate="caption">
				<div class="table-header">
					<h1>{{ 'admin.misc.courses' | translate }}</h1>

					<div class="d-flex">
						<span
							class="p-input-icon-left mr-4"
							style="width: 50%; height: 40px"
							><i class="pi pi-search"></i
							><input
								(input)="dt.filterGlobal($event.target.value, 'contains')"
								pinputtext=""
								type="text"
								[placeholder]="'admin.misc.search' | translate"
								class="search_input"
						/></span>
						<div class="p-field calendar_holder">
							<p-calendar
								[(ngModel)]="date12"
								view="month"
								dateFormat="mm/yy"
								[yearNavigator]="true"
								yearRange="2000:2030"
								[placeholder]="'admin.misc.chooseMonth' | translate"
								[readonlyInput]="true"
								inputId="monthpicker"
							></p-calendar>
						</div>
					</div>
				</div>
			</ng-template>

			<ng-template pTemplate="header">
				<tr>
					<th>
						{{ 'admin.courses.coverImage' | translate }}
					</th>
					<th pSortableColumn="name">
						{{ 'admin.misc.name' | translate }}
						<p-sortIcon field="name"></p-sortIcon>
					</th>
					<th pSortableColumn="instructor">
						{{ 'admin.misc.instructor' | translate }}
						<p-sortIcon field="instructor"></p-sortIcon>
					</th>
					<th>{{ 'admin.courses.category' | translate }}</th>

					<th pSortableColumn="Price">
						{{ 'admin.misc.price' | translate }}
						<p-sortIcon field="Price"></p-sortIcon>
					</th>

					<th pSortableColumn="NewPrice">
						{{ 'admin.misc.newPrice' | translate }}
						<p-sortIcon field="NewPrice"></p-sortIcon>
					</th>

					<th pSortableColumn="UpdatedAt">
						{{ 'admin.misc.lastUpdate' | translate }}
						<p-sortIcon field="UpdatedAt"></p-sortIcon>
					</th>

					<th pSortableColumn="Published">
						{{ 'admin.misc.status' | translate }}
						<p-sortIcon field="Published"></p-sortIcon>
					</th>
					<th style="width: 8vw"></th>
				</tr>
			</ng-template>

			<ng-template pTemplate="body" let-course>
				<tr>
					<td
						[routerLink]="'/admin/course-detail/' + course.Slug"
						class="pointer"
					>
						<img [src]="course.coverFormated" width="100" />
					</td>
					<td>{{ course.Title }}</td>
					<td>
						{{ course.CreatedBy.Firstname }}
						{{ course.CreatedBy.Lastname }}
					</td>
					<td>{{ course.categoriesFormated }}</td>
					<td class="pl-5">
						{{ course.Price | currency: course.Currency }}
					</td>
					<td class="pl-5">
						{{
							course.NewPrice > 0
								? (course.NewPrice | currency: course.Currency)
								: '---'
						}}
					</td>
					<td class="pl-5">
						{{ formatDate(course.UpdatedAt) }}
					</td>
					<td>
						<span
							[class]="'course-badge status-' + course.statusFormated"
							*ngIf="!course.Archived"
							>{{ 'admin.courses.' + course.statusFormated | translate }}</span
						>
						<span
							[class]="'course-badge status-' + course.statusArchieveFormated"
							*ngIf="course.Archived"
							>{{
								'admin.courses.' + course.statusArchieveFormated | translate
							}}</span
						>
					</td>
					<td class="text-align: center">
						<mat-slide-toggle
							class="example-margin"
							[disabled]="course.Archived"
							(change)="
								updateStatus($event, course, course.Published ? false : true)
							"
							[checked]="course.Published == true"
						>
						</mat-slide-toggle>
						<!-- <i class="pi pi-ellipsis-v pointer" style="font-size: 2reme"></i> -->

						<button
							pButton
							pRipple
							type="button"
							icon="pi pi-ellipsis-v"
							class="p-button-rounded p-button-text ml-3"
							[matMenuTriggerFor]="menu"
						></button>
						<mat-menu #menu="matMenu">
							<button
								mat-menu-item
								*ngFor="let option of course.optionMenu"
								(click)="selectedOptionFxn($event, option, course)"
							>
								<mat-icon>{{ option.icon }}</mat-icon>
								<span>{{ option.name }}</span>
							</button>
						</mat-menu>
					</td>
				</tr>
			</ng-template>
			<ng-template pTemplate="summary">
				<div class="p-d-flex p-ai-center p-jc-between">
					In total there are {{ courses ? courses.length : 0 }} courses.
				</div>
			</ng-template>
		</p-table>
	</p-card>

	<!-- 				
			</div>
		</div>
	</div>
</div> -->

	<p-toast SupplementContent></p-toast>

	<p-confirmPopup SupplementContent></p-confirmPopup>
	<p-dialog
		SupplementContent
		header="Modification du prix de la formation"
		[(visible)]="showEditModal"
		[modal]="true"
		[style]="{ width: '50vw' }"
		[baseZIndex]="10000"
		[draggable]="false"
		[resizable]="false"
	>
		<!--<h1>Modification du prix de la formation </h1>-->
		<div class="row">
			<div class="col-lg-4 col-sm-12 d-flex align-items-center">
				<h2 class="mb-0">
					Ancien prix:
					<b>{{
						selectedCourse
							? (selectedCourse.Price | currency: selectedCourse.Currency)
							: null
					}}</b>
				</h2>
			</div>
			<div class="col-sm-12 col-lg-8">
				<label for="Nouveau">Nouveau prix</label>
				<input
					id="Nouveau"
					type="text"
					class="w-100"
					pInputText
					[(ngModel)]="coursePrice"
					placeholder="Nouveau prix"
				/>
			</div>
		</div>

		<ng-template pTemplate="footer">
			<p-button
				icon="pi pi-check"
				(click)="modifyPrice(selectedCourse)"
				label="Modifier"
				styleClass="p-button-text"
			></p-button>
		</ng-template>
	</p-dialog>

	<p-dialog
		SupplementContent
		header="Modification du nouveau prix de la formation"
		[(visible)]="showEditNewPriceModal"
		[modal]="true"
		[style]="{ width: '50vw' }"
		[baseZIndex]="10000"
		[draggable]="false"
		[resizable]="false"
	>
		<!--<h1>Modification du prix de la formation </h1>-->
		<div class="row">
			<div class="col-lg-4 col-sm-12 d-flex align-items-center">
				<h2 class="mb-0">
					Ancienne valeur du nouveau prix:
					<b>{{
						selectedCourse
							? (selectedCourse.NewPrice | currency: selectedCourse.Currency)
							: null
					}}</b>
				</h2>
			</div>
			<div class="col-sm-12 col-lg-8">
				<label for="Nouveau">Nouveau prix</label>
				<input
					id="Nouveau"
					type="text"
					class="w-100"
					pInputText
					[(ngModel)]="coursePrice"
					placeholder="Nouveau prix"
				/>
			</div>
		</div>

		<ng-template pTemplate="footer">
			<p-button
				icon="pi pi-check"
				(click)="modifyPrice(selectedCourse, 'newPrice')"
				label="Modifier"
				styleClass="p-button-text"
			></p-button>
		</ng-template>
	</p-dialog>

	<p-dialog
		SupplementContent
		header="Limiter l'accès à contenu dans certaines région du monde"
		[(visible)]="showLocationModal"
		[modal]="true"
		[style]="{ width: '50vw' }"
		[baseZIndex]="10000"
		[draggable]="false"
		[resizable]="false"
	>
		<div class="col-sm-12" style="min-height: 20vh">
			<p-dropdown
				[options]="zones"
				[(ngModel)]="restrictZone"
				placeholder="Saisir le nom du lieu"
				editable="true"
				optionLabel="name"
				class="w-100"
			></p-dropdown>
		</div>
		<ng-template pTemplate="footer">
			<p-button
				icon="pi pi-check"
				(click)="addRestriction()"
				label="Restraindre"
				styleClass="p-button-text"
			></p-button>
		</ng-template>
	</p-dialog>

	<p-dialog
		SupplementContent
		[header]="'admin.courses.sendMessageForCourseToIntructor' | translate"
		[(visible)]="showMessageCourseModal"
		(onHide)="(hideCourseContactModal)"
		[modal]="true"
		[style]="{ width: '50vw' }"
		[baseZIndex]="10000"
		[draggable]="false"
		[resizable]="false"
	>
		<div class="col-sm-12" style="min-height: 20vh">
			<angular-editor
				[(ngModel)]="courseContactMessage"
				name="Resume"
				id="Resume"
			>
			</angular-editor>
		</div>
		<ng-template pTemplate="footer">
			<p-button
				icon="pi pi-check"
				(click)="messageToCourse()"
				[label]="'admin.options.sendMessage' | translate"
				styleClass="p-button-text"
			></p-button>
		</ng-template>
	</p-dialog>
<!-- Component content ends here -->
