import {
	ChangeDetectorRef,
	Component,
	OnInit,
	ViewEncapsulation
} from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ToastrService } from 'ngx-toastr';
import { MessageService, ConfirmationService } from 'primeng/api';
import { CourseContact } from '~/app/models/course-contact.model';
import { CourseContent } from '~/app/models/course-content.model';
import { Course } from '~/app/models/course.model';
import { CourseContactService } from '~/app/services/courses/course-contact.service';
import { CourseService } from '~/app/services/courses/course.service';

@Component({
	selector: 'app-admin-courses',
	templateUrl: './admin-courses.component.html',
	styleUrls: ['./admin-courses.component.scss'],
	encapsulation: ViewEncapsulation.None
})
export class AdminCoursesComponent implements OnInit {
	constructor(
		private confirmationService: ConfirmationService,
		private messageService: MessageService,
		private translateService: TranslateService,
		private courseService: CourseService,
		private courseContactService: CourseContactService,
		private detectorRef: ChangeDetectorRef,
		private toastr: ToastrService
	) {}

	loading = false;
	selectedOption = {};
	courses = [];
	totalCourseCount = 0;
	menu: any = {};
	options = [];
	selectedCourse: Course = null;
	date12: Date;
	showEditModal = false;
	showEditNewPriceModal = false;
	showLocationModal = false;
	showMessageCourseModal = false;
	courseContactMessage = null;
	coursePrice = 0;
	restrictZone: any;
	zones = [
		{ name: 'New York', code: 'NY' },
		{ name: 'Cameroun', code: 'RM' },
		{ name: 'Togo', code: 'LDN' }
	];
	async ngOnInit(): Promise<void> {
		this.menu = await this.translateService.get('admin.options').toPromise();
		this.options = [
			/* { name: menu.lockContent, icon: 'lock_outline', id_option: 1 },
      { name: menu.unlockContent, icon: 'lock_open', id_option: 2 },
      { name: menu.assignBadge, icon: 'loyalty', id_option: 3 }, */
			{ name: this.menu.editPrice, icon: 'edit', id_option: 4 },
			/* { name: menu.limitAccesToRegion, icon: 'location_off', id_option: 5 }, */
			{ name: this.menu.deleteCourse, icon: 'delete', id_option: 6 },
			{ name: this.menu.sendMessage, icon: 'chat', id_option: 7 }
		];

		// WHE GET TEN COURSES WHEN THE COMPONENT LOAD AT THE FIRST TIME
		this.getCourses(100, 0);

		/* this.courses = [
      {
        id: '1000',
        code: 'f230fh0g3',
        name: 'Bamboo Watch',
        description: 'Product Description',
        image: 'bamboo-watch.jpg',
        price: 65,
        category: 'Accessories',
        quantity: 24,
        inventoryStatus: 'INSTOCK',
        rating: 5
      },
      {
        id: '1001',
        code: 'nvklal433',
        name: 'Black Watch',
        description: 'Product Description',
        image: 'black-watch.jpg',
        price: 72,
        category: 'Accessories',
        quantity: 61,
        inventoryStatus: 'INSTOCK',
        rating: 4
      },
      {
        id: '1002',
        code: 'zz21cz3c1',
        name: 'Blue Band',
        description: 'Product Description',
        image: 'blue-band.jpg',
        price: 79,
        category: 'Fitness',
        quantity: 2,
        inventoryStatus: 'LOWSTOCK',
        rating: 3
      },
      {
        id: '1003',
        code: '244wgerg2',
        name: 'Blue T-Shirt',
        description: 'Product Description',
        image: 'blue-t-shirt.jpg',
        price: 29,
        category: 'Clothing',
        quantity: 25,
        inventoryStatus: 'INSTOCK',
        rating: 5
      },
      {
        id: '1004',
        code: 'h456wer53',
        name: 'Bracelet',
        description: 'Product Description',
        image: 'bracelet.jpg',
        price: 15,
        category: 'Accessories',
        quantity: 73,
        inventoryStatus: 'INSTOCK',
        rating: 4
      },
      {
        id: '1005',
        code: 'av2231fwg',
        name: 'Brown Purse',
        description: 'Product Description',
        image: 'brown-purse.jpg',
        price: 120,
        category: 'Accessories',
        quantity: 0,
        inventoryStatus: 'OUTOFSTOCK',
        rating: 4
      },
      {
        id: '1006',
        code: 'bib36pfvm',
        name: 'Chakra Bracelet',
        description: 'Product Description',
        image: 'chakra-bracelet.jpg',
        price: 32,
        category: 'Accessories',
        quantity: 5,
        inventoryStatus: 'LOWSTOCK',
        rating: 3
      },
      {
        id: '1007',
        code: 'mbvjkgip5',
        name: 'Galaxy Earrings',
        description: 'Product Description',
        image: 'galaxy-earrings.jpg',
        price: 34,
        category: 'Accessories',
        quantity: 23,
        inventoryStatus: 'INSTOCK',
        rating: 5
      },
      {
        id: '1008',
        code: 'vbb124btr',
        name: 'Game Controller',
        description: 'Product Description',
        image: 'game-controller.jpg',
        price: 99,
        category: 'Electronics',
        quantity: 2,
        inventoryStatus: 'LOWSTOCK',
        rating: 4
      },
      {
        id: '1009',
        code: 'cm230f032',
        name: 'Gaming Set',
        description: 'Product Description',
        image: 'gaming-set.jpg',
        price: 299,
        category: 'Electronics',
        quantity: 63,
        inventoryStatus: 'INSTOCK',
        rating: 3
      },
      {
        id: '1010',
        code: 'plb34234v',
        name: 'Gold Phone Case',
        description: 'Product Description',
        image: 'gold-phone-case.jpg',
        price: 24,
        category: 'Accessories',
        quantity: 0,
        inventoryStatus: 'OUTOFSTOCK',
        rating: 4
      },
      {
        id: '1011',
        code: '4920nnc2d',
        name: 'Green Earbuds',
        description: 'Product Description',
        image: 'green-earbuds.jpg',
        price: 89,
        category: 'Electronics',
        quantity: 23,
        inventoryStatus: 'INSTOCK',
        rating: 4
      },
      {
        id: '1012',
        code: '250vm23cc',
        name: 'Green T-Shirt',
        description: 'Product Description',
        image: 'green-t-shirt.jpg',
        price: 49,
        category: 'Clothing',
        quantity: 74,
        inventoryStatus: 'INSTOCK',
        rating: 5
      },
      {
        id: '1013',
        code: 'fldsmn31b',
        name: 'Grey T-Shirt',
        description: 'Product Description',
        image: 'grey-t-shirt.jpg',
        price: 48,
        category: 'Clothing',
        quantity: 0,
        inventoryStatus: 'OUTOFSTOCK',
        rating: 3
      },
      {
        id: '1014',
        code: 'waas1x2as',
        name: 'Headphones',
        description: 'Product Description',
        image: 'headphones.jpg',
        price: 175,
        category: 'Electronics',
        quantity: 8,
        inventoryStatus: 'LOWSTOCK',
        rating: 5
      },
      {
        id: '1015',
        code: 'vb34btbg5',
        name: 'Light Green T-Shirt',
        description: 'Product Description',
        image: 'light-green-t-shirt.jpg',
        price: 49,
        category: 'Clothing',
        quantity: 34,
        inventoryStatus: 'INSTOCK',
        rating: 4
      },
      {
        id: '1016',
        code: 'k8l6j58jl',
        name: 'Lime Band',
        description: 'Product Description',
        image: 'lime-band.jpg',
        price: 79,
        category: 'Fitness',
        quantity: 12,
        inventoryStatus: 'INSTOCK',
        rating: 3
      },
      {
        id: '1017',
        code: 'v435nn85n',
        name: 'Mini Speakers',
        description: 'Product Description',
        image: 'mini-speakers.jpg',
        price: 85,
        category: 'Clothing',
        quantity: 42,
        inventoryStatus: 'INSTOCK',
        rating: 4
      },
      {
        id: '1018',
        code: '09zx9c0zc',
        name: 'Painted Phone Case',
        description: 'Product Description',
        image: 'painted-phone-case.jpg',
        price: 56,
        category: 'Accessories',
        quantity: 41,
        inventoryStatus: 'INSTOCK',
        rating: 5
      },
      {
        id: '1019',
        code: 'mnb5mb2m5',
        name: 'Pink Band',
        description: 'Product Description',
        image: 'pink-band.jpg',
        price: 79,
        category: 'Fitness',
        quantity: 63,
        inventoryStatus: 'INSTOCK',
        rating: 4
      },
      {
        id: '1020',
        code: 'r23fwf2w3',
        name: 'Pink Purse',
        description: 'Product Description',
        image: 'pink-purse.jpg',
        price: 110,
        category: 'Accessories',
        quantity: 0,
        inventoryStatus: 'OUTOFSTOCK',
        rating: 4
      },
      {
        id: '1021',
        code: 'pxpzczo23',
        name: 'Purple Band',
        description: 'Product Description',
        image: 'purple-band.jpg',
        price: 79,
        category: 'Fitness',
        quantity: 6,
        inventoryStatus: 'LOWSTOCK',
        rating: 3
      },
      {
        id: '1022',
        code: '2c42cb5cb',
        name: 'Purple Gemstone Necklace',
        description: 'Product Description',
        image: 'purple-gemstone-necklace.jpg',
        price: 45,
        category: 'Accessories',
        quantity: 62,
        inventoryStatus: 'INSTOCK',
        rating: 4
      },
      {
        id: '1023',
        code: '5k43kkk23',
        name: 'Purple T-Shirt',
        description: 'Product Description',
        image: 'purple-t-shirt.jpg',
        price: 49,
        category: 'Clothing',
        quantity: 2,
        inventoryStatus: 'LOWSTOCK',
        rating: 5
      },
      {
        id: '1024',
        code: 'lm2tny2k4',
        name: 'Shoes',
        description: 'Product Description',
        image: 'shoes.jpg',
        price: 64,
        category: 'Clothing',
        quantity: 0,
        inventoryStatus: 'INSTOCK',
        rating: 4
      },
      {
        id: '1025',
        code: 'nbm5mv45n',
        name: 'Sneakers',
        description: 'Product Description',
        image: 'sneakers.jpg',
        price: 78,
        category: 'Clothing',
        quantity: 52,
        inventoryStatus: 'INSTOCK',
        rating: 4
      },
      {
        id: '1026',
        code: 'zx23zc42c',
        name: 'Teal T-Shirt',
        description: 'Product Description',
        image: 'teal-t-shirt.jpg',
        price: 49,
        category: 'Clothing',
        quantity: 3,
        inventoryStatus: 'LOWSTOCK',
        rating: 3
      },
      {
        id: '1027',
        code: 'acvx872gc',
        name: 'Yellow Earbuds',
        description: 'Product Description',
        image: 'yellow-earbuds.jpg',
        price: 89,
        category: 'Electronics',
        quantity: 35,
        inventoryStatus: 'INSTOCK',
        rating: 3
      },
      {
        id: '1028',
        code: 'tx125ck42',
        name: 'Yoga Mat',
        description: 'Product Description',
        image: 'yoga-mat.jpg',
        price: 20,
        category: 'Fitness',
        quantity: 15,
        inventoryStatus: 'INSTOCK',
        rating: 5
      },
      {
        id: '1029',
        code: 'gwuby345v',
        name: 'Yoga Set',
        description: 'Product Description',
        image: 'yoga-set.jpg',
        price: 20,
        category: 'Fitness',
        quantity: 25,
        inventoryStatus: 'INSTOCK',
        rating: 8
      }
    ]; */
	}

	formatDate(date) {
		return new Date(date).toDateString();
	}

	toogleChange(event: Event, customer: any): void {}
	selectedOptionFxn(event, option, course: Course): void {
		console.log(option);
		this.selectedCourse = { ...course };

		switch (option.id_option) {
			case 1:
				this.confirmLock(event, {});
				break;
			case 2:
				this.messageService.add({
					severity: 'info',
					summary: 'Unlocked',
					detail: 'Vous avez desactive ce contenu'
				});

				break;
			case 3:
				break;
			case 4:
				this.showEditModal = true;
				break;
			case 5:
				this.showLocationModal = true;
				break;
			case 6:
				this.confirmArchiveOrReactivate(event, this.selectedCourse, true);
				break;

			case 7:
				this.confirmArchiveOrReactivate(event, this.selectedCourse, false);
				break;

			case 8:
				this.showMessageCourseModal = true;
				break;

			case 9:
				this.showEditNewPriceModal = true;
				break;

			default:
				break;
		}
	}
	confirmArchiveOrReactivate(
		event: Event,
		course: Course,
		archiveStatus
	): void {
		// console.log(event);
		this.confirmationService.confirm({
			target: event.target,
			message: 'Are you sure that you want to proceed?',
			icon: 'pi pi-exclamation-triangle',
			accept: () => {
				this.archiveCourse(course, archiveStatus);
			},
			reject: () => {
				this.messageService.add({
					severity: 'error',
					summary: 'Rejected',
					detail: 'You have rejected'
				});
			}
		});
	}
	confirmLock(event: Event, customer: any): void {
		// console.log(event);
		this.confirmationService.confirm({
			target: event.target,
			message: 'Souhaitez vous desactivez ce contenu?',
			icon: 'pi pi-exclamation-triangle',
			accept: () => {
				this.messageService.add({
					severity: 'info',
					summary: 'Confirmed',
					detail: 'You have accepted'
				});
				this.options.pop();
			},
			reject: () => {
				this.messageService.add({
					severity: 'error',
					summary: 'Rejected',
					detail: 'You have rejected'
				});
			}
		});
	}

	hideCourseContactModal() {
		this.selectedCourse = null;
	}

	setOptionsMenu(course: Course) {
		const courseOptions = [
			course && !course.Archived
				? { name: this.menu.editPrice, icon: 'edit', id_option: 4 }
				: null,
			course && !course.Archived
				? { name: this.menu.editNewPrice, icon: 'edit', id_option: 9 }
				: null,
			course && !course.Archived
				? { name: this.menu.deleteCourse, icon: 'delete', id_option: 6 }
				: null,
			course && course.Archived
				? { name: this.menu.reactivateCourse, icon: 'lock_open', id_option: 7 }
				: null,
			course && !course.Archived
				? { name: this.menu.sendMessage, icon: 'chat', id_option: 8 }
				: null
		].filter(Boolean);

		return courseOptions;
	}

	// GET THE FISRT COURSES (N FRIST COURSES) AND THE TOTAL NUMBER OF COURSE
	async getCourses(take: number, skip: number) {
		this.loading = true;

		try {
			const resultCourses = await this.courseService
				.getAll(take, skip)
				.toPromise();
			this.totalCourseCount = await this.courseService.getCount().toPromise();

			this.courses = resultCourses.map((c) => ({
				...c,
				...this.courseService.formatCourseFields(c),
				optionMenu: this.setOptionsMenu(c)
			}));
		} catch (error) {
			console.log(error);
		} finally {
			this.loading = false;
		}
	}

	async updateStatus($event, course: Course, publishedStatus: boolean) {
		course.Published = publishedStatus;

		this.loading = true;

		try {
			const result = await this.courseService
				.edit({ ...course, CoverImage: null })
				.toPromise();

			course['statusFormated'] =
				CourseService.STATUS_PUBLISH_MAP[Number(course.Published)];

			const message = await this.translateService
				.get('admin.misc.editStatusSuccess')
				.toPromise();
			this.toastr.success(message, 'Brain-maker');

			if (!result.Published) {
				this.showMessageCourseModal = true;
				this.selectedCourse = course;
			}
		} catch (error) {
			let message = await this.translateService
				.get('admin.misc.editStatusError')
				.toPromise();
			message = `${message} <br/> ${error.error.errors.Error}`;
			this.toastr.error(message, 'Brain-maker');

			if (
				typeof $event.source !== 'undefined' &&
				typeof $event.source.checked !== 'undefined'
			)
				$event.source.checked = !$event.checked;
		} finally {
			this.loading = false;
		}
	}

	//<EMAIL>
	async messageToCourse() {
		this.loading = true;
		const courseContact = new CourseContact(
			null,
			null,
			null,
			null,
			this.courseContactMessage,
			this.selectedCourse,
			this.selectedCourse.CreatedBy
		);

		//console.log(courseContact);

		try {
			const result = await this.courseContactService
				.add(courseContact)
				.toPromise();
			console.log(result);

			const message = await this.translateService
				.get('admin.misc.messageSendSuccess')
				.toPromise();
			this.toastr.success(message, 'Brain-maker');
			this.showMessageCourseModal = false;
		} catch (error) {
			console.log(error);

			let message = await this.translateService
				.get('admin.misc.messageSendError')
				.toPromise();
			this.toastr.error(message, 'Brain-maker');
		} finally {
			this.loading = false;
		}
	}

	async modifyPrice(course: Course, type = 'price') {
		// type is: "price" or "newPrice"

		if (type == 'newPrice') {
			course.NewPrice = Number(this.coursePrice);
		} else {
			course.Price = Number(this.coursePrice);
		}

		this.loading = true;

		try {
			const result = await this.courseService
				.edit({ ...course, CoverImage: null })
				.toPromise();

			const foundIndexUpdated = this.courses.findIndex(
				(c) => c.Id === course.Id
			);
			this.courses.splice(foundIndexUpdated, 1, {
				...course,
				Price: result.Price,
				NewPrice: result.NewPrice
			});

			const message = await this.translateService
				.get('admin.misc.editPriceSuccess')
				.toPromise();

			this.showEditModal = false;
			this.showEditNewPriceModal = false;
			this.coursePrice = 0;

			this.messageService.add({
				severity: 'info',
				summary: 'Edit',
				detail: message
			});
		} catch (error) {
			let message = await this.translateService
				.get('admin.misc.editPriceError')
				.toPromise();
			message = `${message} <br/> ${error.error.errors.Error}`;
			this.messageService.add({
				severity: 'error',
				summary: 'Edit',
				detail: message
			});
		} finally {
			this.loading = false;
		}
	}

	async archiveCourse(course: Course, archiveStatus) {
		course.Archived = archiveStatus;
		this.loading = true;

		try {
			const result = await this.courseService
				.edit({ ...course, CoverImage: null })
				.toPromise();

			const foundIndexUpdated = this.courses.findIndex(
				(c) => c.Id === course.Id
			);
			this.courses.splice(foundIndexUpdated, 1, {
				...result,
				...this.courseService.formatCourseFields(result),
				optionMenu: this.setOptionsMenu(result)
			});

			let message = null;

			if (archiveStatus) {
				message = await this.translateService
					.get('admin.misc.archiveCourseSuccess')
					.toPromise();
			} else {
				message = await this.translateService
					.get('admin.misc.reactiveCourseSuccess')
					.toPromise();
			}

			this.messageService.add({
				severity: 'info',
				summary: 'Edit',
				detail: message
			});
		} catch (error) {
			let message = null;

			if (archiveStatus) {
				message = await this.translateService
					.get('admin.misc.archiveCourseError')
					.toPromise();
			} else {
				message = await this.translateService
					.get('admin.misc.reactiveCourseError')
					.toPromise();
			}

			message = `${message} <br/> ${error.error.errors.Error}`;
			this.messageService.add({
				severity: 'error',
				summary: 'Edit',
				detail: message
			});
		} finally {
			this.loading = false;
		}
	}

	addRestriction(): void {
		// Apres la requete, en cas de success;
		this.messageService.add({
			severity: 'info',
			summary: 'Edit',
			detail: 'Restriction ajoute'
		});
		this.showLocationModal = false;

		// En cas d'echec
		// this.messageService.add({ severity: 'error', summary: 'Edit', detail: 'Raison' });
	}
}
