import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ToastrService } from 'ngx-toastr';
import { CourseService } from '~/app/services/courses/course.service';
import { UserService } from '~/app/services/users/user.service';

@Component({
  selector: 'app-admin-dashboard',
  templateUrl: './admin-dashboard.component.html',
  styleUrls: ['./admin-dashboard.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class AdminDashboardComponent implements OnInit {
  loading = false;
  userCount = 0;
  instructorCount = 0;
  courseCount = 0;
  verificationCount = 0;
  
  constructor(
    private translateService: TranslateService,
    private userService: UserService,
    private courseService: CourseService,
    private toastr: ToastrService
  ) { }

  async ngOnInit(): Promise<void> {
    this.loading = true;
    
    try {
      // Get user count
      const users = await this.userService.getAllByRole('USER').toPromise();
      this.userCount = users.length;
      
      // Get instructor count
      const instructors = await this.userService.getAllByRole('INSTRUCTOR').toPromise();
      this.instructorCount = instructors.length;
      
      // Get course count
      const courses = await this.courseService.getAll(100, 0).toPromise();
      this.courseCount = courses.length;
      
      // Get verification count
      this.courseService.countAllCoursesToVerify().subscribe((count: number) => {
        this.verificationCount = count;
      });
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      this.toastr.error('Error loading dashboard data');
    } finally {
      this.loading = false;
    }
  }
}
