import {
	Component,
	Input,
	OnChanges,
	OnInit,
	ViewEncapsulation
} from '@angular/core';
import { MenuItem } from 'primeng/api';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { environment } from '~/environments/environment';
import { User } from '~/app/models/user.model';
import { UserService } from '~/app/services/users/user.service';
import { CourseService } from '~/app/services/courses/course.service';
import { CourseNotificationsService } from '~/app/services/notifications/course-notifications.service';
import { CourseNotification } from '~/app/models/course-notification.model';
import { ThisReceiver } from '@angular/compiler';
import { ToastrService } from 'ngx-toastr';
import { BillingService } from '~/app/services/billings/billing.service';
declare const $;

@Component({
	selector: 'app-admin-sidebar',
	templateUrl: './admin-sidebar.component.html',
	styleUrls: ['./admin-sidebar.component.scss'],
	encapsulation: ViewEncapsulation.None
})
export class AdminSidebarComponent implements OnInit {
	@Input() countVerifyCourse: number;
	adminMenu: any[] = [];
	visibleSidebar2 = false;
	user: User;

	countCourseToVerify: number;
	countArchivedCourses: number;

	profile = '/assets/img/avatar.png';

	notifications: CourseNotification[];
	countNotifications: number = 0;
	countCourseReports: number = 0;
	countBillings: number = 0;

	constructor(
		private router: Router,
		private translateService: TranslateService,
		private userService: UserService,
		private courseService: CourseService,
		private courseNotificationService: CourseNotificationsService,
		private toastrService: ToastrService,
		private billingService: BillingService
	) {}

	async ngOnInit(): Promise<void> {
		// console.log('***', this.router.url);
		if (this.userService.isConnected()) {
			this.user = await this.userService.getUserConnected().toPromise();

			if (this.user.Photo) {
				this.profile = this.user.Photo.Hashname = `${environment.path}/${this.user.Photo.Hashname}`;
			}
		} else {
			this.router.navigate(['/']);
		}

		try {
			this.courseService
				.countAllArchivedCourses()
				.subscribe((count: number) => {
					this.countArchivedCourses = count;
				});

			this.courseService
				.countAllCoursesToVerify()
				.subscribe((count: number) => {
					this.countCourseToVerify = count;
				});

			this.courseService
				.countAllReportedCourses()
				.subscribe((count: number) => {
					this.countCourseReports = count;
				});

			this.billingService.CountBillings().subscribe((count: number) => {
				this.countBillings = count;
			});

			const response = await this.courseNotificationService
				.getAllLazy(3)
				.toPromise();
			this.notifications = response.Notifications;
			this.countNotifications = response.Count;
		} catch (error) {
			this.toastrService.error(
				'Une erroreur est survenue lors du chargement du menu',
				'impossible de contruire le menue'
			);
		}

		const menu = await this.translateService.get('admin.menu').toPromise();
		// console.log(menu);
		this.adminMenu = [
			{
				label: 'dashboard',
				icon: 'pi pi-chart-line',
				routerLink: '/admin',
				active: true,
				messages: 0
			},
			{
				label: 'learners',
				icon: 'pi pi-bookmark',
				routerLink: '/admin/users',
				active: false,
				messages: 0
			},
			{
				label: 'instructors',
				icon: 'pi pi-briefcase',
				routerLink: '/admin/instructors',
				active: false,
				messages: 0
			},
			{
				label: 'publicCourses',
				icon: 'pi pi-book',
				routerLink: '/admin/courses',
				active: false,
				messages: 0
			},
			{
				label: 'conferences',
				icon: 'pi pi-comments',
				routerLink: '/admin/meetings',
				active: false,
				messages: 0
			},
			/*{
      label: menu.messages,
      icon: 'pi pi-envelope',
      routerLink: '/admin/messages',
      active: false,
      messages: 3
    }, */
			{
				label: 'request',
				icon: 'pi pi-directions',
				routerLink: '/admin/requests',
				active: false,
				messages: 0
			},
			{
				label: 'categories',
				icon: 'pi pi-circle-off',
				routerLink: '/admin/categories',
				active: false,
				messages: 0
			},
			{
				label: 'Verifications',
				icon: 'pi pi-upload',
				routerLink: '/admin/privateCours',
				active: this.router.url === '/admin/privateCours',
				messages: this.countCourseToVerify
			},
			{
				label: 'Signalements',
				icon: 'pi pi-inbox',
				routerLink: '/admin/reported-courses',
				active: this.router.url === '/admin/reported-courses',
				messages: this.countCourseReports
			},
			{
				label: 'archived courses',
				icon: 'pi pi-inbox',
				routerLink: '/admin/archived-courses',
				active: this.router.url === '/admin/archived-courses',
				messages: this.countArchivedCourses
			},
			{
				label: 'policies',
				icon: 'pi pi-inbox',
				routerLink: '/admin/policies',
				active: false,
				messages: 0
			},
			{
				label: 'Notifications',
				icon: 'pi pi-inbox',
				routerLink: '/admin/notifications',
				active: this.router.url === '/admin/notifications',
				messages: this.countNotifications
			},
			{
				label: 'Facturation',
				icon: 'pi pi-inbox',
				routerLink: '/admin/billings',
				active:
					this.router.url === '/admin/billings' ||
					this.router.url === '/admin/billings/settings',
				messages: this.countBillings
			}
			/*,
    {
      label: 'Notifications',
      icon: 'pi pi-upload',
      routerLink: '/admin/notifications',
      active: false,
      messages: 21
    }*/
		];

		this.adminMenu.forEach((element) => {
			if (element.routerLink === this.router.url) {
				element.active = true;
			} else {
				element.active = false;
			}
		});

		this.courseNotificationService.notificationCount.next(
			this.countNotifications
		);

		this.courseNotificationService.notificationCount.subscribe((count) => {
			this.countNotifications = count > 0 ? count : 0;
		});
	}

	tooglemenu(): void {}

	onCourseToVerifyChange(newValue: number): void {
		this.countCourseToVerify = newValue;
	}

	onArchivedCourseChange(newValue: number): void {
		this.countArchivedCourses = newValue;
	}
}
