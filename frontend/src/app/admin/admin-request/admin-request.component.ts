import { Component, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { UtilsService } from '~/app/services/utils.service';
import { Router } from '@angular/router';
import { Request } from '~/app/models/request.model';
import { AdminRequestReplyComponent } from '~/app/admin/admin-request/admin-request-reply/admin-request-reply.component';
import { RequestService } from '~/app/services/requests/request.service';

@Component({
	selector: 'app-admin-request',
	templateUrl: './admin-request.component.html',
	styleUrls: ['./admin-request.component.scss']
})
export class AdminRequestComponent implements OnInit {
	requests: Request[];
	selectedCategories: Request[];

	skip = 0;
	take = 9;
	countOfItem = 0;
	constructor(
		private translateService: TranslateService,
		private utilService: UtilsService,
		private router: Router,
		private requestService: RequestService
	) {}

	selectedOption = {};
	options = [];
	selectedCourse = {};
	date12: Date;
	async ngOnInit(): Promise<void> {
		const menu = await this.translateService.get('admin.options').toPromise();
		this.options = [
			{ name: 'Reply / show message', icon: 'reply', id_option: 1 },
			{ name: menu.delete, icon: 'clear', id_option: 2 }
		];

		this.requestService
			.getAll()
			.subscribe((requests) => (this.requests = requests));
	}
	toogleChange(e, customer: any): void {}
	selectedOptionFxn(option, request: Request): void {
		console.log(option);
		switch (+option.id_option) {
			case 1: {
				this.onCreateRequest(null, request);
				break;
			}
			case 2: {
				if (confirm('do you really want to delete this')) {
					const i = this.requests.indexOf(request);
					this.requests.splice(i, 1);
					this.requestService.delete(request).subscribe((res) => {
						// do something
					});
				}
				break;
			}
		}
	}

	onLoadMore(e): void {
		e.preventDefault();

		this.skip =
			this.countOfItem < this.skip + this.take
				? this.countOfItem
				: this.skip + this.take;
		this.requestService
			.getAllLazy(this.take, this.skip)
			.subscribe(
				(requests) => (this.requests = [...this.requests, ...requests])
			);
	}

	onCreateRequest(e, request = null): void {
		if (e) {
			e.preventDefault();
		}
		this.utilService.buildModal(
			AdminRequestReplyComponent,
			(res) => {
				if (res) {
					this.requestService
						.getAll()
						.subscribe((requests) => (this.requests = requests));
				}
			},
			700,
			true,
			{
				request
			}
		);
	}
}
