import { ComponentFixture, TestBed } from '@angular/core/testing';

import { AdminRequestReplyComponent } from './admin-request-reply.component';

describe('AdminRequestReplyComponent', () => {
  let component: AdminRequestReplyComponent;
  let fixture: ComponentFixture<AdminRequestReplyComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ AdminRequestReplyComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(AdminRequestReplyComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
