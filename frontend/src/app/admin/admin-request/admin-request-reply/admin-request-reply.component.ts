import { ChangeDetectorRef, Component, Inject, OnInit } from '@angular/core';
import { Request } from '~/app/models/request.model';
import { ToastrService } from 'ngx-toastr';
import { LY_DIALOG_DATA, LyDialog, LyDialogRef } from '@alyle/ui/dialog';
import { UtilsService } from '~/app/services/utils.service';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { validate } from 'class-validator';
import { AngularEditorConfig } from '@kolkov/angular-editor';
import { RequestService } from '~/app/services/requests/request.service';

@Component({
	selector: 'app-admin-request-reply',
	templateUrl: './admin-request-reply.component.html',
	styleUrls: ['./admin-request-reply.component.scss']
})
export class AdminRequestReplyComponent implements OnInit {
	errors: any[];
	errorsHelper: any[];
	isLoading: boolean;
	msgError: string;
	request: Request;
	cover = '/assets/img/learning1.jpg';
	coverName: string;
	content: string;
	contentConfig: AngularEditorConfig;
	subject: string;
	constructor(
		private toastr: ToastrService,
		public dialogRef: LyDialogRef,
		private requestService: RequestService,
		private router: Router,
		private dialog: LyDialog,
		@Inject(LY_DIALOG_DATA)
		private data: {
			request: Request;
		},
		private utilsService: UtilsService,
		private cd: ChangeDetectorRef,
		private translateService: TranslateService
	) {}

	ngOnInit(): void {
		this.contentConfig = this.utilsService.getSimpleconfigAngular();
		if (this.data.request) {
			this.request = this.data.request;
			console.log('request : ', this.request);
			this.subject = 'Re: ' + this.request.Subject;
		}
	}

	close(e = null): void {
		if (e) {
			// e.preventDefault();
		}
		this.dialogRef.close();
	}

	isNotvalid(name: string): boolean {
		return this.utilsService.isNotvalid(name, this.errorsHelper);
	}

	async onSubmit(e): Promise<void> {
		if (e) {
			// e.preventDefault();
		}
		this.request.Replies = JSON.parse(
			JSON.stringify({
				Subject: this.subject,
				Body: this.content,
				To: this.request.Email
			})
		);
		this.errors = await validate(this.request);
		this.errorsHelper = this.errors;

		// if ( !(this.errors) || this.errors.length === 0 ) {
		this.isLoading = true;
		try {
			const request = await this.requestService.edit(this.request).toPromise();
			if (request && request.Id) {
				console.log('request :', request);
				const message = await this.translateService.get('success').toPromise();
				this.toastr.success(message, 'Brain-maker');
				this.dialogRef.close(true);
			} else {
				const message = await this.translateService.get('error').toPromise();
				this.toastr.error(message, 'Brain-maker');
			}
			this.isLoading = false;
		} catch (e) {
			console.log(e);
			const message = await this.translateService.get('error').toPromise();
			this.toastr.error(message, 'Brain-maker');
		} finally {
			this.isLoading = false;
		}
	}

	async onItemChange(): Promise<void> {
		this.errorsHelper = await validate(this.request);
	}
}
