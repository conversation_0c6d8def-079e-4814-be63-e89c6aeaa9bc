<form autocomplete="off" class="mw-100"  (ngSubmit)="onSubmit($event)" *ngIf="request">
  <div ly-dialog-content class="px-0 mx-0">
    <div class="login-pop-form" role="document">
      <div class="" id="sign-up">
        <span class="mod-close" (click)="close($event)" aria-hidden="true"
        ><i class="ti-close"></i
        ></span>
        <div class="modal-body mw-100">
          <h4 class="modal-header-title">
            Reply to {{request.Name}}
          </h4>
          <div class="row">
            <div *ngIf="msgError" class="form-group col-12">
              <p class="text-danger font-bold">{{ msgError }}</p>
            </div>

            <div class="form-group col-12">
              <label for="email">
                To
              </label>
              <input
                disabled
                type="text"
                [(ngModel)]="request.Email"
                name="email"
                (blur)="onItemChange()"
                id="email"
                class="form-control"
                required
              />
              <div class="invalid-feedback">
                {{ 'language.required' | translate }}
              </div>
            </div>
            <div class="form-group col-12">
              <label for="subject">
                Subject
              </label>
              <input
                type="text"
                [(ngModel)]="subject"
                name="subject"
                (blur)="onItemChange()"
                id="subject"
                class="form-control"
                required
              />
              <div class="invalid-feedback">
                {{ 'language.required' | translate }}
              </div>
            </div>


            <div class="form-group col-12">
              <label for="content">
                Body
              </label>

              <angular-editor
                required=""
                [(ngModel)]="content"
                name="content"
                id="content"
                [config]="contentConfig"
              >
              </angular-editor>
              <div class="invalid-feedback">
                {{ 'language.required' | translate }}
              </div>
            </div>

            <div class="form-group col-12">
              <span class="">Original message</span>
              <div class="text-muted" [innerHTML]="request.Message"></div>
            </div>

            <div
              class="form-group col-12 d-flex
              mt-4
              justify-content-between align-items-center">
              <div class="">

                <button pButton pRipple label="Submit"
                        class="p-button-primary p-mr-2" type="submit"   [disabled]="isLoading"></button>
                <div
                  class="mt-2 d-flex align-items-center justify-content-center"
                  *ngIf="isLoading"
                >
                  <i class="fas fa-spin fa-spinner mr-2"></i>
                  {{ 'home.loading' | translate }}
                </div>
              </div>
            </div>
          </div>

          <div class="text-center"></div>
        </div>
      </div>
    </div>
  </div>
</form>
