import { User } from './../../models/user.model';
import { Component, OnInit } from '@angular/core';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { DynamicDialogConfig } from 'primeng/dynamicdialog';
@Component({
	selector: 'app-admin-metting-dialog',
	templateUrl: './admin-metting-dialog.component.html',
	styleUrls: ['./admin-metting-dialog.component.scss']
})
export class AdminMettingDialogComponent implements OnInit {
	participants: User[] = [];
	constructor(
		public ref: DynamicDialogRef,
		public config: DynamicDialogConfig
	) {}

	ngOnInit(): void {
		this.participants = this.config.data.metting.Participants;

		/* this.participants = [
			{
				id: '1000',
				code: 'f230fh0g3',
				name: 'Bamboo Watch',
				description: 'Product Description',
				image: 'bamboo-watch.jpg',
				price: 65,
				category: 'Accessories',
				quantity: 24,
				inventoryStatus: 'INSTOCK',
				rating: 5
			},
			{
				id: '1001',
				code: 'nvklal433',
				name: 'Black Watch',
				description: 'Product Description',
				image: 'black-watch.jpg',
				price: 72,
				category: 'Accessories',
				quantity: 61,
				inventoryStatus: 'INSTOCK',
				rating: 4
			},
			{
				id: '1002',
				code: 'zz21cz3c1',
				name: 'Blue Band',
				description: 'Product Description',
				image: 'blue-band.jpg',
				price: 79,
				category: 'Fitness',
				quantity: 2,
				inventoryStatus: 'LOWSTOCK',
				rating: 3
			},
			{
				id: '1003',
				code: '244wgerg2',
				name: 'Blue T-Shirt',
				description: 'Product Description',
				image: 'blue-t-shirt.jpg',
				price: 29,
				category: 'Clothing',
				quantity: 25,
				inventoryStatus: 'INSTOCK',
				rating: 5
			}
		]; */
	}
	selectProduct(product): void {
		this.ref.close(product);
	}
}
