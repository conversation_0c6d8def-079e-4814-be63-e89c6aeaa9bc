<p-table
	[value]="participants"
	[paginator]="true"
	[rows]="10"
	[responsive]="true"
>
	<ng-template pTemplate="header">
		<tr>
			<th pSortableColumn="name">
				{{ 'admin.misc.name' | translate }}
				<p-sortIcon field="vin"></p-sortIcon>
			</th>
			<th pSortableColumn="price">
				Email
				<p-sortIcon field="price"></p-sortIcon>
			</th>
			<!-- <th pSortableColumn="inventoryStatus">{{'admin.misc.status'|translate}}
                <p-sortIcon field="inventoryStatus"></p-sortIcon>
            </th> -->
			<!-- <th style="width:4em"></th> -->
		</tr>
	</ng-template>
	<ng-template pTemplate="body" let-participant>
		<tr>
			<td>{{ participant.LastName }} {{ participant.Firstname }}</td>
			<td>{{ participant.Email }}</td>
			<!-- <td><span [class]="'product-badge status-'+participant.inventoryStatus.toLowerCase()">{{participant.inventoryStatus}}</span></td> -->
		</tr>
	</ng-template>
</p-table>
