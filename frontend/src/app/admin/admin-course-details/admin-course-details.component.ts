import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { ConfirmationService, MessageService } from 'primeng/api';
import { TranslateService } from '@ngx-translate/core';
import { CourseService } from '~/app/services/courses/course.service';
import { Course } from '~/app/models/course.model';
import { ActivatedRoute } from '@angular/router';
import { CourseReportService } from '~/app/services/course-report.service';
import { CourseReport } from '~/app/models/course-report.model';
import { UserService } from '~/app/services/users/user.service';
import { User } from '~/app/models/user.model';

@Component({
	selector: 'app-admin-course-details',
	templateUrl: './admin-course-details.component.html',
	styleUrls: ['./admin-course-details.component.scss'],
	encapsulation: ViewEncapsulation.None
})
export class AdminCourseDetailsComponent implements OnInit {
	customers: any[];
	course: Course;
	courseReports: CourseReport[];
	learners: any[];

	selectedCustomers: any[];

	representatives: any[];

	statuses: any[];

	loading = false;
	date12: Date;
	activityValues: number[] = [0, 100];

	courses = [];
	selectedCourse = {};
	courseReportSelected: CourseReport;
	displayModal = false;
	options = [];
	showEditModal = false;
	showLocationModal = false;
	coursePrice = 0;
	restrictZone: any;
	zones = [
		{ name: 'New York', code: 'NY' },
		{ name: 'Cameroun', code: 'RM' },
		{ name: 'Togo', code: 'LDN' }
	];
	constructor(
		private confirmationService: ConfirmationService,
		private messageService: MessageService,
		private translateService: TranslateService,
		private courseServive: CourseService,
		private courseReportService: CourseReportService,
		private activatedRoute: ActivatedRoute,
		private userService: UserService
	) {}

	async ngOnInit(): Promise<void> {
		const menu = await this.translateService.get('admin.options').toPromise();

		this.options = [
			{ name: menu.lockContent, icon: 'lock_outline', id_option: 1 },
			{ name: menu.unlockContent, icon: 'lock_open', id_option: 2 },
			{ name: menu.assignBadge, icon: 'loyalty', id_option: 3 },
			{ name: menu.editPrice, icon: 'edit', id_option: 4 },
			{ name: menu.limitAccesToRegion, icon: 'location_off', id_option: 5 },
			{ name: menu.deleteCourse, icon: 'delete', id_option: 6 }
		];

		const slug = this.activatedRoute.snapshot.params['slug'];
		await this.getCourse(slug);
		this.getReportsOfCourse(slug);
		this.getLearnersOfCourse(slug);
	}

	formatDate(date) {
		return new Date(date).toLocaleDateString();
	}

	showModalDialog(courseReport): void {
		this.displayModal = true;
		this.courseReportSelected = courseReport;
	}

	selectedOptionFxn(event, option): void {
		console.log(option);
		switch (option.id_option) {
			case 1:
				this.confirmLock(event, {});
				break;
			case 2:
				this.messageService.add({
					severity: 'info',
					summary: 'Unlocked',
					detail: 'Vous avez desactive ce contenu'
				});

				break;
			case 3:
				break;
			case 4:
				this.showEditModal = true;
				break;
			case 5:
				this.showLocationModal = true;
				break;
			case 6:
				this.confirmDelete(event, {});

				break;

			default:
				break;
		}
	}
	confirmDelete(event: Event, customer: any): void {
		// console.log(event);
		this.confirmationService.confirm({
			target: event.target,
			message: 'Are you sure that you want to proceed?',
			icon: 'pi pi-exclamation-triangle',
			accept: () => {
				this.messageService.add({
					severity: 'info',
					summary: 'Confirmed',
					detail: 'You have accepted'
				});
				this.options.pop();
			},
			reject: () => {
				this.messageService.add({
					severity: 'error',
					summary: 'Rejected',
					detail: 'You have rejected'
				});
			}
		});
	}
	confirmLock(event: Event, customer: any): void {
		// console.log(event);
		this.confirmationService.confirm({
			target: event.target,
			message: 'Souhaitez vous desactivez ce contenu?',
			icon: 'pi pi-exclamation-triangle',
			accept: () => {
				this.messageService.add({
					severity: 'info',
					summary: 'Confirmed',
					detail: 'You have accepted'
				});
				this.options.pop();
			},
			reject: () => {
				this.messageService.add({
					severity: 'error',
					summary: 'Rejected',
					detail: 'You have rejected'
				});
			}
		});
	}

	async getCourse(slug: string) {
		this.loading = true;

		try {
			const result = await this.courseServive.getBySlug(slug).toPromise();
			this.course = {
				...result,
				...this.courseServive.formatCourseFields(result)
			};
			console.log(this.course);
		} catch (error) {
			console.log(error);
		} finally {
			this.loading = false;
		}
	}

	async getReportsOfCourse(slug: string) {
		this.loading = true;

		try {
			const result = await this.courseReportService
				.getReportsForCourse(this.course.Id)
				.toPromise();
			this.courseReports = result.map((courseReport) => ({
				...courseReport,
				...this.courseReportService.formatCourseReportFields(courseReport)
			}));
			//console.log('getReportsOfCourse', this.courseReports);
		} catch (error) {
			console.log(error);
		} finally {
			this.loading = false;
		}
	}

	async getLearnersOfCourse(slug: string) {
		this.loading = true;

		try {
			const result = await this.courseServive
				.getLearnersForCourse(slug)
				.toPromise();
			this.learners = result.map((r) => ({
				...r.user,
				...this.userService.formatUserFields(r.user),
				percentageViewed: r.percentageViewed
			}));
			//console.log('learners', result);
		} catch (error) {
			console.log(error);
		} finally {
			this.loading = false;
		}
	}

	modifyPrice(): void {
		// Apres la requete, en cas de success;
		this.messageService.add({
			severity: 'info',
			summary: 'Edit',
			detail: 'Prix modifie'
		});
		this.showEditModal = false;

		// En cas d'echec
		// this.messageService.add({ severity: 'error', summary: 'Edit', detail: 'Raison' });
	}
	addRestriction(): void {
		// Apres la requete, en cas de success;
		this.messageService.add({
			severity: 'info',
			summary: 'Edit',
			detail: 'Restriction ajoute'
		});
		this.showLocationModal = false;

		// En cas d'echec
		// this.messageService.add({ severity: 'error', summary: 'Edit', detail: 'Raison' });
	}
}
