<!-- Component content starts here -->
	<div class="row">
		<div class="col-sm-12">
			<div class="card border pay_card">
				<div class="card-body">
					<h2>{{ 'admin.misc.financeStats' | translate }}</h2>
					<div
						class="p-field calendar_holder d-flex justify-content-start align-items-center"
					>
						<span>{{ 'admin.misc.statTextTitle' | translate }}</span>
						<p-calendar
							(onSelect)="onSelect()"
							[(ngModel)]="date12"
							view="month"
							dateFormat="mm/yy"
							[yearNavigator]="true"
							yearRange="2000:2030"
							[placeholder]="'admin.misc.chooseMonth' | translate"
							[readonlyInput]="true"
							inputId="monthpicker"
							class="ml-4"
						></p-calendar>
					</div>
				</div>
			</div>
		</div>
		<div class="col-sm-12 col-md-6 col-lg-3">
			<div class="card border pay_card">
				<div class="card-body">
					<h3 class="text-center mg-0">
						<b>{{ 'admin.misc.cashPaidToTrainers' | translate }}</b>
					</h3>
					<div
						class="d-flex align-items-center justify-content-center flex-column"
					>
						<p class="mb-0 title mt-4">{{ totalCashPaid }}</p>
						<h3>Euro(s)</h3>
					</div>
					<!-- <div class="p-field calendar_holder">
                                    <p-calendar [(ngModel)]="date12" view="month" dateFormat="mm/yy" [yearNavigator]="true" yearRange="2000:2030" [placeholder]="'Choissisez un mois'" [readonlyInput]="true" inputId="monthpicker"></p-calendar>
                                </div> -->
				</div>
			</div>
		</div>
		<div class="col-sm-12 col-md-6 col-lg-3">
			<div class="card border pay_card">
				<div class="card-body">
					<h3 class="text-center mg-0">
						<b>{{ 'admin.misc.subscriptionCash' | translate }}</b>
					</h3>

					<div
						class="d-flex align-items-center justify-content-center flex-column"
					>
						<p class="mb-0 title mt-4">{{ totalCashSubscription }}</p>
						<h3>Euro(s)</h3>
					</div>
				</div>
			</div>
		</div>
		<div class="col-sm-12 col-md-6 col-lg-3">
			<div class="card border pay_card">
				<div class="card-body">
					<h3 class="text-center mg-0">
						<b>{{ 'admin.misc.cashFromPurchase' | translate }}</b>
					</h3>

					<div
						class="d-flex align-items-center justify-content-center flex-column"
					>
						<p class="mb-0 title mt-4">{{ totalCashPurchase }}</p>
						<h3>Euro(s)</h3>
					</div>
				</div>
			</div>
		</div>
		<div class="col-sm-12 col-md-6 col-lg-3">
			<div class="card border pay_card">
				<div class="card-body">
					<h3 class="text-center mg-0">
						<b>{{ 'admin.misc.cashRevewals' | translate }}</b>
					</h3>

					<div
						class="d-flex align-items-center justify-content-center flex-column"
					>
						<p class="mb-0 title mt-4">2000</p>
						<h3>Euro(s)</h3>
					</div>
				</div>
			</div>
		</div>
		<div class="col-sm-12">
			<div class="card p-3">
				<h1>Activites</h1>
				<p-tabView>
					<p-tabPanel [header]="'admin.misc.subscriptions' | translate">
						<p-table
							#dt
							[value]="abonnements"
							[rows]="10"
							[paginator]="true"
							[(selection)]="selectedCourse"
							[rowHover]="true"
							dataKey="id"
							[responsive]="true"
							currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
							[showCurrentPageReport]="true"
							[globalFilterFields]="['name']"
						>
							<ng-template pTemplate="caption">
								<div class="d-flex">
									<span
										class="p-input-icon-left mr-4"
										style="width: 50%; height: 40px"
										><i class="pi pi-search"></i
										><input
											(input)="dt.filterGlobal($event.target.value, 'contains')"
											pinputtext=""
											type="text"
											[placeholder]="'admin.misc.search' | translate"
											class="search_input"
									/></span>
								</div>
							</ng-template>
							<ng-template pTemplate="header">
								<tr>
									<th pSortableColumn="name">
										{{ 'admin.misc.name' | translate }}
										<p-sortIcon field="name"></p-sortIcon>
									</th>
									<th>{{ 'admin.misc.transactionID' | translate }}</th>
									<th pSortableColumn="price">
										{{ 'admin.misc.montant' | translate }}
										<p-sortIcon field="price"></p-sortIcon>
									</th>
									<th pSortableColumn="quantity">
										{{ 'admin.misc.transactionDate' | translate }}
									</th>
									<th pSortableColumn="rating">
										{{ 'admin.misc.state' | translate }}
									</th>
								</tr>
							</ng-template>
							<ng-template pTemplate="body" let-abonnement>
								<tr>
									<td>
										{{
											abonnement.User?.Firstname +
												' ' +
												abonnement.User?.Lastname
										}}
									</td>
									<td>{{ abonnement.TransactionId }}</td>
									<td>{{ abonnement.Price | currency: 'USD' }}</td>
									<td>
										{{
											abonnement.CreatedAt
												| date: ('forum.dateFormat' | translate)
										}}
									</td>
									<td>{{ abonnement.PaymentStatut }}</td>
								</tr>
							</ng-template>
							<ng-template pTemplate="summary">
								<div class="p-d-flex p-ai-center p-jc-between">
									In total there are
									{{ abonnements ? abonnements.length : 0 }} abonnements.
								</div>
							</ng-template>
						</p-table>
					</p-tabPanel>

					<p-tabPanel [header]="'admin.misc.paymentToTrainers' | translate">
						<p-table
							#dt
							[value]="instructorPaids"
							[rows]="10"
							[paginator]="true"
							[(selection)]="selectedCourse"
							[rowHover]="true"
							dataKey="id"
							[responsive]="true"
							currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
							[showCurrentPageReport]="true"
							[globalFilterFields]="['name']"
						>
							<ng-template pTemplate="caption">
								<div class="d-flex">
									<span
										class="p-input-icon-left mr-4"
										style="width: 50%; height: 40px"
										><i class="pi pi-search"></i
										><input
											(input)="dt.filterGlobal($event.target.value, 'contains')"
											pinputtext=""
											type="text"
											[placeholder]="'admin.misc.search' | translate"
											class="search_input"
									/></span>
								</div>
							</ng-template>
							<ng-template pTemplate="header">
								<tr>
									<th pSortableColumn="name">
										{{ 'admin.misc.name' | translate }}
										<p-sortIcon field="name"></p-sortIcon>
									</th>
									<th pSortableColumn="price">
										{{ 'admin.misc.montant' | translate }}
										<p-sortIcon field="price"></p-sortIcon>
									</th>
								</tr>
							</ng-template>
							<ng-template pTemplate="body" let-institutor>
								<tr>
									<td>
										{{ institutor.User.Firstname + institutor.User.Lastname }}
									</td>
									<td>{{ institutor.Paid | currency: 'USD' }}</td>
								</tr>
							</ng-template>
							<ng-template pTemplate="summary">
								<div class="p-d-flex p-ai-center p-jc-between">
									In total there are
									{{
										instructorPaids ? instructorPaids.length : 0
									}}
									institutors.
								</div>
							</ng-template>
						</p-table>
					</p-tabPanel>
					<p-tabPanel [header]="'admin.misc.paidCourses' | translate">
						<p-table
							#dt
							[value]="paidCourses"
							[rows]="10"
							[paginator]="true"
							[(selection)]="selectedCourse"
							[rowHover]="true"
							dataKey="id"
							[responsive]="true"
							currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
							[showCurrentPageReport]="true"
							[globalFilterFields]="['name']"
						>
							<ng-template pTemplate="caption">
								<div class="d-flex">
									<span
										class="p-input-icon-left mr-4"
										style="width: 50%; height: 40px"
										><i class="pi pi-search"></i
										><input
											(input)="dt.filterGlobal($event.target.value, 'contains')"
											pinputtext=""
											type="text"
											[placeholder]="'admin.misc.search' | translate"
											class="search_input"
									/></span>
								</div>
							</ng-template>
							<ng-template pTemplate="header">
								<tr>
									<th pSortableColumn="name">
										{{ 'admin.misc.name' | translate }}
										<p-sortIcon field="name"></p-sortIcon>
									</th>
									<th pSortableColumn="price">
										{{ 'admin.misc.montant' | translate }}
										<p-sortIcon field="price"></p-sortIcon>
									</th>
								</tr>
							</ng-template>
							<ng-template pTemplate="body" let-course>
								<tr>
									<td>{{ course.Title }}</td>
									<td>{{ course.Price | currency: 'USD' }}</td>
								</tr>
							</ng-template>
							<ng-template pTemplate="summary">
								<div class="p-d-flex p-ai-center p-jc-between">
									In total there are {{ courses ? courses.length : 0 }} courses.
								</div>
							</ng-template>
						</p-table>
					</p-tabPanel>
				</p-tabView>
			</div>
		</div>
	</div>
	<!-- Component content ends here -->
