import { Component, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Category } from '~/app/models/category.model';
import { CategoryService } from '~/app/services/categories/category.service';
import { UtilsService } from '~/app/services/utils.service';
import { AdminCategoriesFormComponent } from '~/app/admin/admin-categories/admin-categories-form/admin-categories-form.component';
import { Router } from '@angular/router';

@Component({
	selector: 'app-admin-categories',
	templateUrl: './admin-categories.component.html',
	styleUrls: ['./admin-categories.component.scss']
})
export class AdminCategoriesComponent implements OnInit {
	categories: Category[];
	selectedCategories: Category[];

	skip = 0;
	take = 9;
	countOfItem = 0;
	constructor(
		private translateService: TranslateService,
		private utilService: UtilsService,
		private router: Router,
		private categoryService: CategoryService
	) {}

	selectedOption = {};
	options = [];
	selectedCourse = {};
	date12: Date;
	async ngOnInit(): Promise<void> {
		const menu = await this.translateService.get('admin.options').toPromise();
		this.options = [
			{ name: menu.subcategories, icon: 'category', id_option: 1 },
			{ name: menu.update, icon: 'create', id_option: 2 },
			{ name: menu.delete, icon: 'clear', id_option: 3 }
		];

		this.categoryService
			.getAll()
			.subscribe((categories) => (this.categories = categories));
	}
	toogleChange(e, customer: any): void {}
	selectedOptionFxn(option, category: Category): void {
		console.log(option);
		switch (+option.id_option) {
			case 1: {
				this.router.navigate(['/private', 'categories', 'sub', category.Slug]);
				break;
			}
			case 2: {
				this.onCreateCategory(null, category);
				break;
			}
			case 3: {
				if (confirm('do you really want to delete this')) {
					const i = this.categories.indexOf(category);
					this.categories.splice(i, 1);
					this.categoryService.delete(category).subscribe((res) => {
						// do something
					});
				}
				break;
			}
		}
	}

	onLoadMore(e): void {
		e.preventDefault();

		this.skip =
			this.countOfItem < this.skip + this.take
				? this.countOfItem
				: this.skip + this.take;
		this.categoryService
			.getAllLazy(this.take, this.skip)
			.subscribe(
				(categories) => (this.categories = [...this.categories, ...categories])
			);
	}

	onCreateCategory(e, category = null): void {
		if (e) {
			e.preventDefault();
		}
		this.utilService.buildModal(
			AdminCategoriesFormComponent,
			(res) => {
				if (res) {
					this.categoryService
						.getAll()
						.subscribe((categories) => (this.categories = categories));
				}
			},
			700,
			true,
			{
				category
			}
		);
	}
}
