import {AfterViewInit, Component, Inject, OnInit, ViewChild} from '@angular/core';
import {lyl, <PERSON><PERSON><PERSON><PERSON>, ThemeRef, ThemeVariables} from '@alyle/ui';
import {STYLES as SLIDER_STYLES} from '@alyle/ui/slider';
import {
  ImgCropperConfig,
  ImgCropperErrorEvent,
  ImgCropperEvent,
  LyImageCropper,
  STYLES as CROPPER_STYLES
} from '@alyle/ui/image-cropper';
import {LY_DIALOG_DATA, LyDialogRef} from '@alyle/ui/dialog';


// tslint:disable-next-line:variable-name
const STYLES = (_theme: ThemeVariables, ref: ThemeRef) => {
  ref.renderStyleSheet(SLIDER_STYLES);
  ref.renderStyleSheet(CROPPER_STYLES);
  const slider = ref.selectorsOf(SLIDER_STYLES);
  const cropper = ref.selectorsOf(CROPPER_STYLES);

  return {
    root: lyl `{
      ${cropper.root} {
        max-width: 500px
        height: 500px
      }
    }`,
    sliderContainer: lyl `{
      position: relative
      ${slider.root} {
        width: 80%
        position: absolute
        left: 0
        right: 0
        margin: auto
        top: -32px
      }
    }`,
    slider: lyl `{
      padding: 1em
    }`
  };
};
@Component({
  selector: 'app-admin-categories-img-cropper',
  templateUrl: './admin-categories-img-cropper.component.html',
  styleUrls: ['./admin-categories-img-cropper.component.scss']
})
export class AdminCategoriesImgCropperComponent implements OnInit , AfterViewInit {

  readonly classes = this.sRenderer.renderSheet(STYLES, 'root');
  ready: boolean;
  scale: number;
  minScale: number;
  original: ImgCropperEvent;
  @ViewChild(LyImageCropper, { static: true }) cropper: LyImageCropper;
  myConfig: ImgCropperConfig = {
    width: 200,
    height: 200,
    // type: 'image/png',
    keepAspectRatio: true,
    responsiveArea: true,
    output: {
      width: 400,
      height: 400
    },
    resizableArea: true
  } as ImgCropperConfig;

  constructor(
    @Inject(LY_DIALOG_DATA) private event: Event,
    readonly sRenderer: StyleRenderer,
    public dialogRef: LyDialogRef
  ) { }

  ngAfterViewInit(): void {
    // Load image when dialog animation has finished
    this.dialogRef.afterOpened.subscribe(() => {
      this.cropper.selectInputEvent(this.event);
    });
  }

  onCropped(e: ImgCropperEvent): void {
    console.log('cropped img: ', e);
  }

  onLoaded(e: ImgCropperEvent): void {
    console.log('img loaded', e);
    this.original = e;
  }

  onError(e: ImgCropperErrorEvent): void {
    console.warn(`'${e.name}' is not a valid image`, e);
    // Close the dialog if it fails
    this.dialogRef.close();
  }

  onclose(e): void {
    e.preventDefault();
    this.dialogRef.close({
      cropper: this.cropper.crop(),
      original: this.original
    });
  }

  ngOnInit(): void {
  }
}

