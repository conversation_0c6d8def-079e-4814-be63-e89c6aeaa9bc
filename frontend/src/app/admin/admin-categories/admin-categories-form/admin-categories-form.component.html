<form autocomplete="off" (ngSubmit)="onSubmit($event)" *ngIf="category">
  <div ly-dialog-content class="px-0 mx-0">
    <div class="login-pop-form" role="document">
      <div class="" id="sign-up">
        <span class="mod-close" (click)="close($event)" aria-hidden="true"
        ><i class="ti-close"></i
        ></span>
        <div class="modal-body">
          <h4 class="modal-header-title">
            Category
          </h4>
          <div class="row">
            <div *ngIf="msgError" class="form-group col-12">
              <p class="text-danger font-bold">{{ msgError }}</p>
            </div>

            <div class="form-group col-12">
              <label for="category-title">
                Title
              </label>
              <input
                type="text"
                [(ngModel)]="category.Title"
                name="category-title"
                (blur)="onItemChange()"
                [ngClass]="{ 'is-invalid': errors && isNotvalid('Title') }"
                id="category-title"
                class="form-control"
                required
              />
              <div class="invalid-feedback">
                {{ 'language.required' | translate }}
              </div>
            </div>

            <div class="col-12 row m-0 p-0">
              <input #_fileInput type="file" (change)="openCropperDialog($event)" accept="image/*" hidden>
              <div class="col d-flex align-items-center ">
                <div *ngIf="cover"><img class="img mw-100" [src]="cover"></div>
              </div>
              <div class="p-fluid col form-group" (click)="_fileInput.click()">
                <label for="coverIage"> Upload photo.
                  The image you upload must be of good quality
                </label>
                <div class="p-inputgroup">
                  <input type="text" [value]="coverName" disabled id="coverIage"
                         pInputText placeholder="Keyword">
                  <button type="button" pButton pRipple
                          class="p-button-raised p-button-secondary p-button-text"
                          label="Upload file"></button>
                </div>
              </div>
            </div>

            <div
              class="form-group col-12 d-flex
              mt-4
              justify-content-between align-items-center">
              <div class="">

                <button pButton pRipple label="Submit" icon="pi pi-save"
                        class="p-button-primary p-mr-2" type="submit"   [disabled]="isLoading"></button>
                <div
                  class="mt-2 d-flex align-items-center justify-content-center"
                  *ngIf="isLoading"
                >
                  <i class="fas fa-spin fa-spinner mr-2"></i>
                  {{ 'home.loading' | translate }}
                </div>
              </div>
            </div>
          </div>

          <div class="text-center"></div>
        </div>
      </div>
    </div>
  </div>
</form>
