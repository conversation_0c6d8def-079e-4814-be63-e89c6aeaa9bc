import { ChangeDetectorRef, Component, Inject, OnInit } from '@angular/core';
import { Category } from '~/app/models/category.model';
import { ToastrService } from 'ngx-toastr';
import { LY_DIALOG_DATA, LyDialog, LyDialogRef } from '@alyle/ui/dialog';
import { CategoryService } from '~/app/services/categories/category.service';
import { UtilsService } from '~/app/services/utils.service';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { validate } from 'class-validator';
import { ImgCropperEvent } from '@alyle/ui/image-cropper';
import { Media } from '~/app/models/media.model';
import { AdminCategoriesImgCropperComponent } from '~/app/admin/admin-categories/admin-categories-img-cropper/admin-categories-img-cropper.component';

@Component({
	selector: 'app-admin-categories-form',
	templateUrl: './admin-categories-form.component.html',
	styleUrls: ['./admin-categories-form.component.scss']
})
export class AdminCategoriesFormComponent implements OnInit {
	errors: any[];
	errorsHelper: any[];
	isLoading: boolean;
	msgError: string;
	category: Category = new Category(null, '123', null, null, null, null, null);
	cover = '/assets/img/learning1.jpg';
	coverName: string;
	constructor(
		private toastr: ToastrService,
		public dialogRef: LyDialogRef,
		private categoryService: CategoryService,
		private utilsService: UtilsService,
		private router: Router,
		private dialog: LyDialog,
		@Inject(LY_DIALOG_DATA)
		private data: {
			category: Category;
		},
		private cd: ChangeDetectorRef,
		private translateService: TranslateService
	) {}

	ngOnInit(): void {
		if (this.data.category) {
			this.category = this.data.category;
			console.log('category : ', this.category);
			if (this.category.Photo) {
				this.cover = this.category.Photo.Hashname;
				this.coverName = this.category.Photo.Name;
			}
		}
	}

	close(e = null): void {
		if (e) {
			// e.preventDefault();
		}
		this.dialogRef.close();
	}

	isNotvalid(name: string): boolean {
		return this.utilsService.isNotvalid(name, this.errorsHelper);
	}

	async onSubmit(e): Promise<void> {
		if (e) {
			// e.preventDefault();
		}
		this.errors = await validate(this.category);
		this.errorsHelper = this.errors;

		// if ( !(this.errors) || this.errors.length === 0 ) {
		this.isLoading = true;
		try {
			let category: Category;
			if (this.category.Id) {
				category = await this.categoryService.edit(this.category).toPromise();
			} else {
				category = await this.categoryService.add(this.category).toPromise();
			}
			if (category && category.Id) {
				console.log('category :', category);
				const message = await this.translateService.get('success').toPromise();
				this.toastr.success(message, 'Brain-maker');
				this.dialogRef.close(true);
			} else {
				const message = await this.translateService.get('error').toPromise();
				this.toastr.error(message, 'Brain-maker');
			}
			this.isLoading = false;
		} catch (e) {
			console.log(e);
			const message = await this.translateService.get('error').toPromise();
			this.toastr.error(message, 'Brain-maker');
		} finally {
			this.isLoading = false;
		}
	}

	async onItemChange(): Promise<void> {
		this.errorsHelper = await validate(this.category);
	}

	openCropperDialog(event: Event): void {
		// this.cover = null;
		this.dialog
			.open<AdminCategoriesImgCropperComponent, Event>(
				AdminCategoriesImgCropperComponent,
				{
					data: event,
					width: 500,
					disableClose: true
				}
			)
			.afterClosed.subscribe(
				(result?: { cropper: ImgCropperEvent; original: ImgCropperEvent }) => {
					if (result) {
						console.log('result end : ', result);
						this.cover = result.cropper.dataURL;
						this.coverName = result.original.name;
						this.category.Photo = new Media(
							this.coverName,
							result.cropper.dataURL?.substr(
								result.cropper.dataURL?.indexOf(',') + 1
							),
							result.original.type,
							result.original.size,
							'123'
						);
						this.cd.markForCheck();
					}
				}
			);
	}
}
