import { Component, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { UtilsService } from '~/app/services/utils.service';
import { ActivatedRoute, Router } from '@angular/router';
import { SubCategory } from '~/app/models/sub-category.model';
import { AdminSubCategoriesFormComponent } from '~/app/admin/admin-categories/admin-sub-categories-form/admin-sub-categories-form.component';
import { Category } from '~/app/models/category.model';
import { CategoryService } from '~/app/services/categories/category.service';
import { SubCategoryService } from '~/app/services/categories/sub-category.service';

@Component({
	selector: 'app-admin-sub-categories',
	templateUrl: './admin-sub-categories.component.html',
	styleUrls: ['./admin-sub-categories.component.scss']
})
export class AdminSubCategoriesComponent implements OnInit {
	category: Category;
	selectedCategories: SubCategory[];

	skip = 0;
	take = 9;
	countOfItem = 0;
	constructor(
		private translateService: TranslateService,
		private utilService: UtilsService,
		private router: Router,
		private activatedRoute: ActivatedRoute,
		private subCategoryService: SubCategoryService,
		private categoryService: CategoryService
	) {}

	selectedOption = {};
	options = [];
	selectedCourse = {};
	date12: Date;
	async ngOnInit(): Promise<void> {
		const menu = await this.translateService.get('admin.options').toPromise();
		this.options = [
			{ name: menu.update, icon: 'create', id_option: 1 },
			{ name: menu.delete, icon: 'clear', id_option: 2 }
		];

		if (this.activatedRoute.snapshot.params.id) {
			this.category = await this.categoryService
				.getBySlug(this.activatedRoute.snapshot.params.id)
				.toPromise();
		} else {
			this.router.navigate(['/private', 'categories']);
		}
	}
	toogleChange(e, customer: any): void {}
	selectedOptionFxn(option, subCategory: SubCategory): void {
		console.log(option);
		switch (+option.id_option) {
			case 1: {
				this.onCreateSubCategory(null, subCategory);
				break;
			}
			case 2: {
				if (confirm('do you really want to delete this')) {
					const i = this.category.SubCategories.indexOf(subCategory);
					this.category.SubCategories.splice(i, 1);
					this.subCategoryService.delete(subCategory).subscribe((res) => {
						// do something
					});
				}
				break;
			}
		}
	}

	onCreateSubCategory(
		e,
		subCategory = new SubCategory(
			null,
			'123',
			null,
			null,
			null,
			null,
			this.category
		)
	): void {
		if (e) {
			e.preventDefault();
		}

		subCategory.Category = this.category;
		this.utilService.buildModal(
			AdminSubCategoriesFormComponent,
			(res) => {
				if (res) {
					this.categoryService
						.getBySlug(this.category.Slug)
						.subscribe((c) => (this.category = c));
				}
			},
			700,
			true,
			{
				subCategory
			}
		);
	}

	getPhoto(subCategory): string {
		console.log(subCategory);
		return subCategory.Photo
			? subCategory.Photo.Hashname
			: 'https://via.placeholder.com/70x70';
	}
}
