import { ChangeDetectorRef, Component, Inject, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { LY_DIALOG_DATA, LyDialog, LyDialogRef } from '@alyle/ui/dialog';
import { UtilsService } from '~/app/services/utils.service';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { validate } from 'class-validator';
import { ImgCropperEvent } from '@alyle/ui/image-cropper';
import { Media } from '~/app/models/media.model';
import { AdminCategoriesImgCropperComponent } from '~/app/admin/admin-categories/admin-categories-img-cropper/admin-categories-img-cropper.component';
import { SubCategory } from '~/app/models/sub-category.model';
import { Category } from '~/app/models/category.model';
import { CategoryService } from '~/app/services/categories/category.service';
import { SubCategoryService } from '~/app/services/categories/sub-category.service';

@Component({
	selector: 'app-admin-sub-categories-form',
	templateUrl: './admin-sub-categories-form.component.html',
	styleUrls: ['./admin-sub-categories-form.component.scss']
})
export class AdminSubCategoriesFormComponent implements OnInit {
	errors: any[];
	errorsHelper: any[];
	isLoading: boolean;
	msgError: string;
	subCategory: SubCategory;
	cover = '/assets/img/learning1.jpg';
	coverName: string;
	categories: Category[];
	constructor(
		private toastr: ToastrService,
		public dialogRef: LyDialogRef,
		private subCategoryService: SubCategoryService,
		private categoryService: CategoryService,
		private utilsService: UtilsService,
		private router: Router,
		private dialog: LyDialog,
		@Inject(LY_DIALOG_DATA)
		private data: {
			subCategory: SubCategory;
		},
		private cd: ChangeDetectorRef,
		private translateService: TranslateService
	) {}

	ngOnInit(): void {
		/* this.categoryService.getAll().subscribe( res => {
      this.categories = res;
    }); */

		if (this.data.subCategory) {
			this.subCategory = this.data.subCategory;
			console.log('subCategory : ', this.subCategory);
			if (this.subCategory.Photo) {
				this.cover = this.subCategory.Photo.Hashname;
				this.coverName = this.subCategory.Photo.Name;
			}
		}
	}

	close(e = null): void {
		if (e) {
			// e.preventDefault();
		}
		this.dialogRef.close();
	}

	isNotvalid(name: string): boolean {
		return this.utilsService.isNotvalid(name, this.errorsHelper);
	}

	async onSubmit(e): Promise<void> {
		if (e) {
			// e.preventDefault();
		}
		this.errors = await validate(this.subCategory);
		this.errorsHelper = this.errors;

		// if ( !(this.errors) || this.errors.length === 0 ) {
		this.isLoading = true;
		try {
			let subCategory: SubCategory;
			if (this.subCategory.Id) {
				this.subCategory.Category = { Id: this.subCategory.Category.Id } as any;
				subCategory = await this.subCategoryService
					.edit(this.subCategory)
					.toPromise();
			} else {
				subCategory = await this.subCategoryService
					.add(this.subCategory)
					.toPromise();
			}
			if (subCategory && subCategory.Id) {
				console.log('subCategory :', subCategory);
				const message = await this.translateService.get('success').toPromise();
				this.toastr.success(message, 'Brain-maker');
				this.dialogRef.close(true);
			} else {
				const message = await this.translateService.get('error').toPromise();
				this.toastr.error(message, 'Brain-maker');
			}
			this.isLoading = false;
		} catch (e) {
			console.log(e);
			const message = await this.translateService.get('error').toPromise();
			this.toastr.error(message, 'Brain-maker');
		} finally {
			this.isLoading = false;
		}
	}

	async onItemChange(): Promise<void> {
		this.errorsHelper = await validate(this.subCategory);
	}

	openCropperDialog(event: Event): void {
		// this.cover = null;
		this.dialog
			.open<AdminCategoriesImgCropperComponent, Event>(
				AdminCategoriesImgCropperComponent,
				{
					data: event,
					width: 500,
					disableClose: true
				}
			)
			.afterClosed.subscribe(
				(result?: { cropper: ImgCropperEvent; original: ImgCropperEvent }) => {
					if (result) {
						console.log('result end : ', result);
						this.cover = result.cropper.dataURL;
						this.coverName = result.original.name;
						this.subCategory.Photo = new Media(
							this.coverName,
							result.cropper.dataURL?.substr(
								result.cropper.dataURL?.indexOf(',') + 1
							),
							result.original.type,
							result.original.size,
							'123'
						);
						this.cd.markForCheck();
					}
				}
			);
	}
}
