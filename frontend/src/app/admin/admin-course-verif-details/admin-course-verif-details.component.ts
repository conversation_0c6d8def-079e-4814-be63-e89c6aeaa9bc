import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { Course, ContentType } from '../../models/course.model';
import { ActivatedRoute, Router } from '@angular/router';
import { UtilsService } from '../../services/utils.service';
import { TranslateService } from '@ngx-translate/core';
import { ConfirmationService, MessageService } from 'primeng/api';
import { CourseService } from '~/app/services/courses/course.service';
import { User, UserRoleType } from '~/app/models/user.model';
import { UserService } from '~/app/services/users/user.service';
import { CourseSection } from '~/app/models/course-section.model';
import { CourseBaseContent } from '~/app/models/course-base-content.model';
import { CourseReport } from '~/app/models/course-report.model';
import { CourseReportService } from '~/app/services/course-report.service';

@Component({
	selector: 'app-admin-course-verif-details',
	templateUrl: './admin-course-verif-details.component.html',
	styleUrls: ['./admin-course-verif-details.component.scss'],
	encapsulation: ViewEncapsulation.None
})
export class AdminCourseVerifDetailsComponent implements OnInit {
	course: Course;
	currentContent: any;
	contentType = ContentType;

	cover = '/assets/img/1920x650.png';
	languages: { code: string; name: string; nativeName: string }[];
	options = [];
	reasons = [
		{ name: 'Raison 1', code: 'NY' },
		{ name: 'Raison 2', code: 'RM' },
		{ name: 'Raison 3', code: 'LDN' }
	];
	reason: any;
	selectedReason: any;
	showRaisonModal = false;
	slug: string;
	user: User;

	courseHasBeenReported = false;

	constructor(
		private router: Router,
		private courseService: CourseService,
		private utilsService: UtilsService,
		private activedRoute: ActivatedRoute,
		private confirmationService: ConfirmationService,
		private messageService: MessageService,
		private translateService: TranslateService,
		private userService: UserService,
		private courseReportService: CourseReportService
	) {}

	async ngOnInit(): Promise<void> {
		this.user = await this.userService.getUserConnected().toPromise();
		const menu = await this.translateService.get('admin.options').toPromise();

		this.options = [
			{ name: menu.publishCourse, icon: 'publish', id_option: 1 },
			{
				name: menu.unPublishCourse,
				icon: 'remove_circle_outline',
				id_option: 2
			},
			{ name: menu.signalCourse, icon: 'block', id_option: 3 },
			{ name: menu.deleteCourse, icon: 'delete', id_option: 4 }
		];

		this.slug = this.activedRoute.snapshot.params.id;
		// this.languages = await this.utilsService.getLanguages().toPromise();
		this.course = await this.courseService
			.getBySlug(this.activedRoute.snapshot.params.id)
			.toPromise();
		// this.course = this.courseService.BuildCourse(this.course, this.languages);

		if (this.course && this.course.CoverImage) {
			this.cover = `${this.course.CoverImage.Hashname}`;
		}

		this.courseHasBeenReported = this.utilsService.checkIfCourseHasReport(
			this.course
		);
	}
	selectedOptionFxn(event, option): void {
		switch (option.id_option) {
			case 1:
				this.confirmPublish(event, option);
				break;
			// case 2:
			// 	this.confirmUnPublish(event, option);
			// 	break;
			// case 3:
			// 	this.showRaisonModal = true;
			// 	break;
			case 2:
				this.confirmArchive(event, option);
				break;

			default:
				break;
		}
	}
	confirmArchive(event: Event, customer: any): void {
		// console.log(event);
		this.confirmationService.confirm({
			target: event.target,
			header: 'Confirmation de la suppression',
			message: 'Voulez-vous vraiment supprimer ce cours ?',
			icon: 'pi pi-exclamation-triangle',
			accept: () => {
				// this.messageService.add({
				// 	severity: 'info',
				// 	summary: 'Confirmed',
				// 	detail: 'You have accepted'
				// });
				this.course.Published = false;
				this.course.Archived = true;
				this.courseService
					.edit({ ...this.course, CoverImage: null })
					.subscribe((response) => {
						this.router.navigate(['/admin', 'privateCours']);
					});
				this.options.pop();
			},
			reject: () => {
				// this.messageService.add({
				// 	severity: 'error',
				// 	summary: 'Rejected',
				// 	detail: 'You have rejected'
				// });
			}
		});
	}

	confirmPublish(event: Event, customer: any): void {
		// console.log(event);
		this.confirmationService.confirm({
			target: event.target,
			header: 'Confirmation de la publication',
			message: 'Voulez-vous vraiment publier ce cours ?',
			icon: 'pi pi-exclamation-triangle',
			accept: async () => {
				this.course.Published = true;
				this.course.Archived = false;
				if (this.courseHasBeenReported) {
					// supprimer tous les reports
					this.course.Sections?.map((section: CourseSection) => {
						section.Contents?.map((content: CourseBaseContent) => {
							content.Reports?.map(async (report: CourseReport) => {
								report.Active = false;
								const res = await this.courseReportService
									.edit(report)
									.toPromise();
							});
						});
					});
				}
				this.courseService
					.edit({ ...this.course, CoverImage: null })
					.subscribe((response) => {
						this.router.navigate(['/admin', 'privateCours']);
					});
			},
			reject: () => {
				// this.messageService.add({
				// 	severity: 'error',
				// 	summary: 'Rejected',
				// 	detail: 'You have rejected'
				// });
			}
		});
	}
	// confirmUnPublish(event: Event, customer: any): void {
	// 	// console.log(event);
	// 	this.confirmationService.confirm({
	// 		target: event.target,
	// 		message: 'Are you sure that you want to proceed?',
	// 		icon: 'pi pi-exclamation-triangle',
	// 		accept: () => {
	// 			this.messageService.add({
	// 				severity: 'info',
	// 				summary: 'Confirmed',
	// 				detail: 'You have published'
	// 			});
	// 			this.options.pop();
	// 		},
	// 		reject: () => {
	// 			// this.messageService.add({
	// 			// 	severity: 'error',
	// 			// 	summary: 'Rejected',
	// 			// 	detail: 'You have done nothing'
	// 			// });
	// 		}
	// 	});
	// }
	addReasonToSignal(): void {
		// Apres la requete, en cas de success;
		this.messageService.add({
			severity: 'info',
			summary: 'Edit',
			detail: 'Contenu signale'
		});
		this.showRaisonModal = false;

		// En cas d'echec
		// this.messageService.add({ severity: 'error', summary: 'Edit', detail: 'Raison' });
	}
}
