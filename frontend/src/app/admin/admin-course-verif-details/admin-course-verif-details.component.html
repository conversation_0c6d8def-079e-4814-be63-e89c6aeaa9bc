<!-- Component content starts here -->
<div class="skin">
	<div id="main-wrapper">
		<app-admin-sidebar></app-admin-sidebar>

		<div class="row">
			<div class="col-md-2"></div>
			<div class="col-md-12 col-lg-10 col-sm-12 list-holder"> -->

	<div  class="row">
		<div class="col-sm-12">
			<button
				(click)="selectedOptionFxn($event, options[0])"
				pTooltip="Publier le cours"
				tooltipPosition="bottom"
				class="mr-2"
				mat-mini-fab
				color="#666"
				aria-label="Example icon button with a menu icon"
				style="background: #00ff37"
			>
				<mat-icon>publish</mat-icon>
			</button>
			<!--button (click)="selectedOptionFxn($event, options[1])" pTooltip="Depublier de cours" tooltipPosition="bottom" class="mr-2" mat-mini-fab color="primary" aria-label="Example icon button with a plus one icon">
              <mat-icon>remove_circle_outline</mat-icon>
            </button-->
			<!--button(click)="selectedOptionFxn($event, options[2])" pTooltip="Signaler le cours" tooltipPosition="bottom" class="mr-2" mat-mini-fab color="accent" aria-label="Example icon button with a filter list icon">
              <mat-icon>block</mat-icon>
            </button-->
			<button
				*ngIf="course && !course.Archived"
				(click)="selectedOptionFxn($event, options[1])"
				pTooltip="Archiver"
				tooltipPosition="bottom"
				class="mr-2"
				mat-mini-fab
				color="warn"
				aria-label="Example icon button with a home icon"
			>
				<mat-icon>delete</mat-icon>
			</button>
		</div>
		<div class="col-12">
			<app-course-reader [slug]="slug"></app-course-reader>
		</div>
	</div>

	<!-- 
			</div>
		</div>
	</div> -->

	<!--video controls class="w-100">
                    <source src="../../../assets/img/video_test.mp4" type="video/mp4">
                    {{
            'course.details.errorLoadingVideo' | translate
            }}
                  </video-->
	<!-- </div> -->

	<p-toast SupplementContent></p-toast>

	<p-confirmDialog
		SupplementContent
		header="Validation"
		[style]="{ width: '50vw' }"
		[baseZIndex]="10000"
		rejectButtonStyleClass="p-button-text"
	>
	</p-confirmDialog>
	<p-dialog
		SupplementContent
		header="Raison du signalement"
		[(visible)]="showRaisonModal"
		[modal]="true"
		[style]="{ width: '50vw' }"
		[baseZIndex]="10000"
		[draggable]="false"
		[resizable]="false"
	>
		<div class="w-100 signalement-bar" style="min-height: 10vh">
			<p-dropdown
				[options]="reasons"
				[(ngModel)]="selectedReason"
				placeholder="Choisir la raison du signalement"
				editable="true"
				optionLabel="name"
				class="w-100"
			></p-dropdown>
		</div>
		<ng-template pTemplate="footer">
			<p-button
				icon="pi pi-check"
				(click)="addReasonToSignal()"
				label="Signaler"
				styleClass="p-button-text"
			></p-button>
		</ng-template>
	</p-dialog>
<!-- Component content ends here -->
