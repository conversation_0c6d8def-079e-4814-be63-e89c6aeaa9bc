import { ToastrService } from 'ngx-toastr';
import { UtilsService } from './../../services/utils.service';
import { Meetings, MeetingStatus } from './../../models/meetings';
import { Component, OnDestroy, OnInit, ViewEncapsulation } from '@angular/core';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { MessageService, ConfirmationService } from 'primeng/api';
import { AdminMettingDialogComponent } from '../admin-metting-dialog/admin-metting-dialog.component';
import { TranslateService } from '@ngx-translate/core';
import { MeetingsService } from '~/app/services/meetings/meetings.service';
import * as moment from 'moment-timezone';

@Component({
	selector: 'app-admin-meetings',
	templateUrl: './admin-meetings.component.html',
	styleUrls: ['./admin-meetings.component.scss'],
	encapsulation: ViewEncapsulation.None,
	providers: [DialogService]
})
export class AdminMeetingsComponent implements <PERSON><PERSON><PERSON><PERSON>, OnDestroy {
	customers: any[];
	representatives: any[];

	statuses: any[];

	take = 12;
	meetings: Meetings[];
	selectedMeeting: Meetings[];

	loading = false;
	date12: Date;
	options = [];
	menu: any;
	activityValues: number[] = [0, 100];
	ref: DynamicDialogRef;
	meetingStatus = MeetingStatus;

	constructor(
		public dialogService: DialogService,
		private toastr: ToastrService,
		public messageService: MessageService,
		private confirmationService: ConfirmationService,
		private meetingsService: MeetingsService,
		private translateService: TranslateService,
		private utilService: UtilsService
	) {}

	async ngOnInit(): Promise<void> {
		this.menu = await this.translateService.get('admin.options').toPromise();

		this.options = [
			{ name: this.menu.activateMetting, icon: 'lock_outline', id_option: 1 },
			{ name: this.menu.deactivateMeeting, icon: 'lock_open', id_option: 2 },
			{ name: this.menu.deleteMetting, icon: 'delete', id_option: 3 }
		];

		this.getMeetings();

		this.representatives = [
			{ name: 'Amy Elsner', image: 'amyelsner.png' },
			{ name: 'Anna Fali', image: 'annafali.png' },
			{ name: 'Asiya Javayant', image: 'asiyajavayant.png' },
			{ name: 'Bernardo Dominic', image: 'bernardodominic.png' },
			{ name: 'Elwin Sharvill', image: 'elwinsharvill.png' },
			{ name: 'Ioni Bowcher', image: 'ionibowcher.png' },
			{ name: 'Ivan Magalhaes', image: 'ivanmagalhaes.png' },
			{ name: 'Onyama Limba', image: 'onyamalimba.png' },
			{ name: 'Stephen Shaw', image: 'stephenshaw.png' },
			{ name: 'Xuxue Feng', image: 'xuxuefeng.png' }
		];

		this.statuses = [
			{ label: 'Unqualified', value: 'unqualified' },
			{ label: 'Qualified', value: 'qualified' },
			{ label: 'New', value: 'new' },
			{ label: 'Negotiation', value: 'negotiation' },
			{ label: 'Renewal', value: 'renewal' },
			{ label: 'Proposal', value: 'proposal' }
		];
	}

	formatDate(date: Date) {
		return this.utilService.FormatDateToDayMonthYear(new Date(date));
	}

	formatDate2(date: Date) {
		return moment(new Date(date));
	}

	getStatusClass(
		meetingStatus: MeetingStatus
	): { c: string; label: string } | string {
		switch (meetingStatus) {
			case MeetingStatus.FINISHED:
				return {
					c: 'customer-badge status-unqualified mr-1 mt-2',
					label: 'Terminé'
				};

			case MeetingStatus.RUNNING:
				return {
					c: 'customer-badge status-proposal mr-1 mt-2',
					label: 'En cours'
				};

			case MeetingStatus.SCHEDULED:
				return {
					c: 'customer-badge status-qualified mr-1 mt-2',
					label: 'programmé'
				};
			case MeetingStatus.STOPED:
				return {
					c: 'customer-badge status-unqualified mr-1 mt-2',
					label: 'Bloqué'
				};
			default:
				return '';
		}
	}

	async getMeetings() {
		this.loading = true;

		try {
			const result = await this.meetingsService.getAll().toPromise();
			this.meetings = result;
			//console.log('getReportsOfCourse', this.courseReports);
		} catch (error) {
			console.log(error);
		} finally {
			this.loading = false;
		}
	}

	/* getOptions(metting: Meetings) {
		const options = [
			metting.Status === MeetingStatus.STOPED
				? {
						name: this.menu.activateMetting,
						icon: 'lock_outline',
						id_option: 1
				  }
				: null,
			metting.Status === MeetingStatus.RUNNING ||
			metting.Status === MeetingStatus.SCHEDULED
				? { name: this.menu.deactivateMeeting, icon: 'lock_open', id_option: 2 }
				: null,
			{ name: this.menu.deleteMetting, icon: 'delete', id_option: 3 }
		].filter(Boolean);

		return options;
	} */

	selectedOptionFxn(event: Event, option, metting: Meetings): void {
		console.log(option);
		switch (option.id_option) {
			case 1:
				this.updateStatus(metting, MeetingStatus.SCHEDULED);
				break;
			case 2:
				this.updateStatus(metting, MeetingStatus.STOPED);
				break;
			case 3:
				this.confirmDelete(event, {});

				break;

			default:
				break;
		}
	}

	showParticipants(metting: Meetings): void {
		this.ref = this.dialogService.open(AdminMettingDialogComponent, {
			header: 'Les participants',
			width: '70%',
			contentStyle: { 'max-height': '500px', overflow: 'auto' },
			baseZIndex: 10000,
			data: { metting }
		});

		this.ref.onClose.subscribe((product) => {
			if (product) {
				this.messageService.add({
					severity: 'info',
					summary: 'Product Selected',
					detail: product.name
				});
			}
		});
	}

	confirmDelete(event: Event, customer: any): void {
		// console.log(event);
		this.confirmationService.confirm({
			target: event.target,
			message: 'Are you sure that you want to proceed?',
			icon: 'pi pi-exclamation-triangle',
			accept: () => {
				this.messageService.add({
					severity: 'info',
					summary: 'Confirmed',
					detail: 'You have accepted'
				});
			},
			reject: () => {
				this.messageService.add({
					severity: 'error',
					summary: 'Rejected',
					detail: 'You have rejected'
				});
			}
		});
	}

	async updateStatus(metting: Meetings, newStatus: MeetingStatus) {
		this.loading = true;

		try {
			const result = await this.meetingsService
				.update(metting.Slug, { ...metting, Status: newStatus })
				.toPromise();

			const message = await this.translateService
				.get('admin.misc.editStatusSuccess')
				.toPromise();
			this.toastr.success(message, 'Brain-maker');

			metting.Status = newStatus;
		} catch (error) {
			let message = await this.translateService
				.get('admin.misc.editStatusError')
				.toPromise();
			message = `${message} <br/> ${error.error.errors.Error}`;
			this.toastr.error(message, 'Brain-maker');
		} finally {
			this.loading = false;
		}
	}

	ngOnDestroy(): void {
		if (this.ref) {
			this.ref.close();
		}
	}
}
