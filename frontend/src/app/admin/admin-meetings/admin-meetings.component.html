<!-- Component content starts here -->
<div class="skin">
    <div id="main-wrapper">
        <app-admin-sidebar></app-admin-sidebar>

        <div class="row">
            <div class="col-md-2">
            </div>
            <div class="col-md-12 col-lg-10 col-sm-12 list-holder"> -->
	<p-card  [style]="{ 'margin-bottom': '2em' }">
		<p-table
			#dt
			[value]="meetings"
			[(selection)]="selectedMeeting"
			dataKey="id"
			styleClass="p-datatable-customers pb-4"
			[rowHover]="true"
			[rows]="10"
			[showCurrentPageReport]="true"
			[rowsPerPageOptions]="[10, 25, 50]"
			[loading]="loading"
			[paginator]="true"
			currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
			[filterDelay]="0"
			[globalFilterFields]="[
				'name',
				'country.name',
				'representative.name',
				'status'
			]"
		>
			<ng-template pTemplate="caption">
				<div class="table-header">
					<h1>{{ 'admin.menu.conferences' | translate }}</h1>
					<div class="d-flex">
						<span
							class="p-input-icon-left mr-4"
							style="width: 50%; height: 40px"
							><i class="pi pi-search"></i
							><input
								(input)="dt.filterGlobal($event.target.value, 'contains')"
								pinputtext=""
								type="text"
								[placeholder]="'admin.misc.search' | translate"
								class="search_input"
						/></span>
						<div class="p-field calendar_holder">
							<p-calendar
								[(ngModel)]="date12"
								view="month"
								dateFormat="mm/yy"
								[yearNavigator]="true"
								yearRange="2000:2030"
								[placeholder]="'admin.misc.chooseMonth' | translate"
								[readonlyInput]="true"
								inputId="monthpicker"
							></p-calendar>
						</div>
					</div>
				</div>
			</ng-template>
			<ng-template pTemplate="header">
				<tr>
					<th pSortableColumn="name">
						<div class="p-d-flex p-jc-between p-ai-center">
							{{ 'admin.misc.theme' | translate }}
							<p-sortIcon field="name"></p-sortIcon>
						</div>
					</th>

					<th pSortableColumn="country.name">
						<div class="p-d-flex p-jc-between p-ai-center">
							{{ 'admin.misc.startDate' | translate }}
						</div>
					</th>
					<th pSortableColumn="country.name">
						<div class="p-d-flex p-jc-between p-ai-center">
							{{ 'admin.misc.endDate' | translate }}
						</div>
					</th>

					<th pSortableColumn="balance">
						<div class="p-d-flex p-jc-between p-ai-center">Type</div>
					</th>
					<th pSortableColumn="balance">
						<div class="p-d-flex p-jc-between p-ai-center">
							{{ 'admin.misc.duration' | translate }}
						</div>
					</th>

					<th pSortableColumn="balance"></th>
				</tr>
			</ng-template>
			<ng-template pTemplate="body" let-metting>
				<tr class="p-selectable-row">
					<td>
						<span class="p-column-title" (click)="showParticipants(metting)">{{
							metting.Topic
						}}</span>
					</td>
					<td>
						<span class="image-text">{{
							formatDate(metting.StartDate) +
								' à ' +
								metting.StartTime +
								' ' +
								metting.TimePeriod
						}}</span>
					</td>
					<td>
						<span class="image-text">{{ formatDate2(metting.EndTime) }}</span>
					</td>
					<td>
						<!-- <span class="image-text">{{ getStatusClass(metting.Status) }}</span> -->
						<span [class]="getStatusClass(metting.Status).c">{{
							getStatusClass(metting.Status).label
						}}</span>
					</td>
					<td>
						{{ metting.DurationHour + 'H:' + metting.DurationMin + 'min' }}
					</td>
					<td>
						<button
							pButton
							pRipple
							type="button"
							icon="pi pi-ellipsis-v"
							class="p-button-rounded p-button-text ml-3"
							[matMenuTriggerFor]="menu"
						></button>
						<mat-menu #menu="matMenu">
							<button
								mat-menu-item
								*ngFor="let option of options"
								(click)="selectedOptionFxn($event, option, metting)"
								[disabled]="
									(option.id_option === 1 &&
										(metting.Status === meetingStatus.RUNNING ||
											metting.Status === meetingStatus.SCHEDULED)) ||
									(option.id_option === 2 &&
										metting.Status === meetingStatus.STOPED)
								"
							>
								<mat-icon>{{ option.icon }}</mat-icon>
								<span>{{ option.name }}</span>
							</button>
						</mat-menu>
					</td>
				</tr>
			</ng-template>
			<ng-template pTemplate="emptymessage">
				<tr>
					<td colspan="8">No meetings found.</td>
				</tr>
			</ng-template>
		</p-table>
	</p-card>
	<!-- </div>
        </div>
    </div> -->
	<p-toast SupplementContent></p-toast>

	<p-confirmPopup SupplementContent></p-confirmPopup>
<!-- Component content ends here -->
