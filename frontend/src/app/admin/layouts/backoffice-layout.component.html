<div class="skin">
	<div id="main-wrapper">
		<!-- <app-admin-sidebar></app-admin-sidebar> -->
		<div class="menu_wrapper d-flex" *ngIf="user">
			<!-- Sidebar -->
			<div class="left_menu_section">
				<p-card [style]="{ 'margin-bottom': '2em' }">
					<a class="nav-brand static-logo mt-2 pl-4 pr-2" routerLink="/admin">
						<span class="font-weight-bold">
							<span class="text-dark">Brain-</span
							><span class="text-danger">maker</span>
						</span>
					</a>
					<ul class="pt-4 mt-4">
						<li
							*ngFor="let menuItem of adminMenu"
							[routerLink]="menuItem.routerLink"
							class="sidebar_menu_content pb-2 pt-2 pl-4 pr-4 pointer"
							[ngClass]="{ active: menuItem.active === true }"
						>
							<i [class]="menuItem.icon" style="font-size: 1.5rem"></i>
							<div class="sidebar_menu_content_main pl-2 pr-2 pointer">
								<span>{{ menuItem.label }} </span>
							</div>
							<p-badge
								*ngIf="menuItem.messages > 0"
								[value]="menuItem.messages"
								severity="danger"
								class="right"
							>
							</p-badge>
						</li>
					</ul>
				</p-card>
			</div>
			<!-- Sidebar -->

			<!--Nav bar-->
			<p-toolbar>
				<div class="p-toolbar-group-left">
					<i
						class="pi pi-bars pointer menu-icon show-on-medium-and-down"
						(click)="visibleSidebar2 = true"
						style="font-size: 1.5rem"
					></i>
				</div>

				<div class="p-toolbar-group-right" style="position: relative">
					<i
						*ngIf="countNotifications > 0"
						class="pi pi-bell p-mr-4 p-text-secondary pointer head_menu_icon"
						pBadge
						style="font-size: 2rem"
						value="{{ countNotifications }}"
						(click)="op.toggle($event)"
						#iconNotif
						severity="danger"
					></i>
					<i
						*ngIf="countNotifications === 0"
						class="pi pi-bell p-mr-4 p-text-secondary pointer head_menu_icon"
						style="font-size: 2rem"
						value="0"
						(click)="op.toggle($event)"
						#iconNotif
						severity="danger"
					></i>
					<p-overlayPanel
						#op
						[showCloseIcon]="true"
						[style]="{ width: '300px' }"
					>
						<ng-template pTemplate>
							<ul>
								<li>
									<div class="list-group">
										<a
											routerLink="/admin/notifications"
											*ngFor="let notification of notifications"
											href="#"
											class="list-group-item list-group-item-action"
										>
											<app-notification-item
												[notification]="notification"
											></app-notification-item>
										</a>
									</div>
								</li>
							</ul>
							<a routerLink="/admin/notifications">Voir plus</a>
						</ng-template>
					</p-overlayPanel>
					<span class="mr-4">{{ user.Firstname }}</span>
					<img
						[src]="profile"
						alt=""
						srcset=""
						style="height: 40px; width: 40px; border-radius: 50%"
					/>
				</div>
			</p-toolbar>
			<!--Nav bar-->
		</div>
		<!-- <app-admin-sidebar></app-admin-sidebar> -->

		<div class="row">
			<div class="col-md-2"></div>
			<div class="col-md-12 col-lg-10 col-sm-12 list-holder">
				<!--Dynamic content-->
				<div>
					<router-outlet></router-outlet>
				</div>
				<!--Dynamic content-->
			</div>
		</div>
	</div>
	<!-- <p-toast></p-toast> -->

	<!--Dynamic content -->
	<ng-content select="[SupplementContent]"></ng-content>
	<!--Dynamic content -->

	<!-- Mobile sidebar menu -->
	<p-sidebar [(visible)]="visibleSidebar2" position="left" [baseZIndex]="10000">
		<a class="nav-brand static-logo mt-2 pl-4 pr-2" routerLink="/admin">
			<span class="font-weight-bold">
				<span class="text-dark">Brain-</span
				><span class="text-danger">maker</span>
			</span>
		</a>
		<ul class="pt-4 mt-4">
			<li
				*ngFor="let menuItem of adminMenu"
				[routerLink]="menuItem.routerLink"
				class="sidebar_menu_content pb-2 pt-2 pl-4 pr-4 pointer"
				[ngClass]="{ active: menuItem.active === true }"
				(click)="visibleSidebar2 = false"
			>
				<i [class]="menuItem.icon" style="font-size: 1.5rem"></i>
				<div class="sidebar_menu_content_main pl-2 pr-2 pointer">
					<span>{{ menuItem.label }} </span>
				</div>
				<p-badge
					*ngIf="menuItem.messages > 0 && menuItem.active !== true"
					[value]="menuItem.messages"
					severity="danger"
					class="right"
				>
				</p-badge>
			</li>
		</ul>
	</p-sidebar>
</div>
