.menu_wrapper {
	position: relative;
}

.left_menu_section .p-card.p-component {
	width: 15%;
	margin-bottom: 2em;
	z-index: 9;
	transform: translateX(0%);
	transition: all 0.5s;
}

.menu_wrapper .p-menu {
	border-top: none !important;
	border-bottom: none !important;
}

.sidebar_menu_content:hover {
	background: #636c7524;
}

.sidebar_menu_content {
	display: flex;
	align-items: center;
	position: relative;
}

.sidebar_menu_content.active {
	background: #f0c7db !important;
}

.sidebar_menu_content_main {
	display: flex;
	flex-direction: column;
}

.p-card-body {
	padding: 0 !important;
}

.left_menu_section .p-card {
	height: 100vh;
	position: fixed;
}

.menu_wrapper .p-toolbar.p-component {
	width: 85%;
	position: fixed;
	right: 0;
	z-index: 9;
}

.skin {
	background: #ebebec;
	min-height: 100vh;
}

.menu-icon {
	display: none;
}

.p-overlaypanel {
	left: 0 !important;
}

.head_menu_icon {
	margin-right: 3em;
}

.left_menu_section .p-card {
	border-radius: 0 !important;
}

@media only screen and (max-width: 992px) {
	.hide-on-med-and-down {
		display: none !important;
	}
	.left_menu_section .p-card.p-component {
		transform: translateX(-101%);
		display: none;
	}
	.menu_wrapper .p-toolbar.p-component {
		width: 100% !important;
	}
	.head_menu_icon {
		margin-right: 2em;
	}
	.p-overlaypanel {
		width: 250px !important;
	}
}

@media only screen and (min-width: 601px) {
	.show-on-medium-and-up {
		display: block !important;
	}
}

@media only screen and (max-width: 992px) {
	.show-on-medium-and-down {
		display: block !important;
	}
}
