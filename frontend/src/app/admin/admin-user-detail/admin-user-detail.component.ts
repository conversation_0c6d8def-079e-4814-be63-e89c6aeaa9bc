import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { ConfirmationService, MessageService } from 'primeng/api';
import { TranslateService } from '@ngx-translate/core';
import { ActivatedRoute } from '@angular/router';
import { User, UserRoleType, UserStatusType } from '~/app/models/user.model';
import { ToastrService } from 'ngx-toastr';

import { CourseHistoryService } from '~/app/services/courses/course-history.service';
import { UserService } from '~/app/services/users/user.service';
import { CourseService } from '~/app/services/courses/course.service';

@Component({
  selector: 'app-admin-user-detail',
  templateUrl: './admin-user-detail.component.html',
  styleUrls: ['./admin-user-detail.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class AdminUserDetailComponent implements OnInit {
  courses = [];
  coursesHistories = [];
  userConnected: User;
  user: User;
  loading = false;
  menu: any;

  userStatusActive = UserStatusType.ACTIVE;
  userStatusBlocked = UserStatusType.BLOCKED;
  userStatusDeleted = UserStatusType.DELETED;

  userRoleLearner = UserRoleType.USER;
  userRoleInstructor = UserRoleType.INSTRUCTOR;

  selectedCourse = {};
  displayModal = false;
  displayBasic2 = false;
  options = [];
  constructor(
    private confirmationService: ConfirmationService,
    private messageService: MessageService,
    private translateService: TranslateService, private activatedRoute: ActivatedRoute, private userService: UserService, private toastr: ToastrService,
    private courseService: CourseService, private courseHistoryService: CourseHistoryService) { }

  async ngOnInit(): Promise<void> {
    const slug = this.activatedRoute.snapshot.params['slug'];

    try {

      this.userConnected = await this.userService.getUserConnected().toPromise();
    } catch (error) {
      this.toastr.error('Error to get connected user');
    }


    this.getUser(slug);
    this.getCourseOfUser(slug);
    this.getCourseHistoryOfUser(slug);

    this.menu = await this.translateService.get('admin.options').toPromise();
    this.setOptionsMenu(this.user);

    /* this.courses = [
      {
        id: '1000',
        code: 'f230fh0g3',
        name: 'Bamboo Watch',
        description: 'Product Description',
        image: 'bamboo-watch.jpg',
        price: 65,
        category: 'Accessories',
        quantity: 24,
        inventoryStatus: 'INSTOCK',
        rating: 5
      },
      {
        id: '1001',
        code: 'nvklal433',
        name: 'Black Watch',
        description: 'Product Description',
        image: 'black-watch.jpg',
        price: 72,
        category: 'Accessories',
        quantity: 61,
        inventoryStatus: 'INSTOCK',
        rating: 4
      },
      {
        id: '1002',
        code: 'zz21cz3c1',
        name: 'Blue Band',
        description: 'Product Description',
        image: 'blue-band.jpg',
        price: 79,
        category: 'Fitness',
        quantity: 2,
        inventoryStatus: 'LOWSTOCK',
        rating: 3
      },
      {
        id: '1003',
        code: '244wgerg2',
        name: 'Blue T-Shirt',
        description: 'Product Description',
        image: 'blue-t-shirt.jpg',
        price: 29,
        category: 'Clothing',
        quantity: 25,
        inventoryStatus: 'INSTOCK',
        rating: 5
      },
      {
        id: '1004',
        code: 'h456wer53',
        name: 'Bracelet',
        description: 'Product Description',
        image: 'bracelet.jpg',
        price: 15,
        category: 'Accessories',
        quantity: 73,
        inventoryStatus: 'INSTOCK',
        rating: 4
      },
      {
        id: '1005',
        code: 'av2231fwg',
        name: 'Brown Purse',
        description: 'Product Description',
        image: 'brown-purse.jpg',
        price: 120,
        category: 'Accessories',
        quantity: 0,
        inventoryStatus: 'OUTOFSTOCK',
        rating: 4
      },
      {
        id: '1006',
        code: 'bib36pfvm',
        name: 'Chakra Bracelet',
        description: 'Product Description',
        image: 'chakra-bracelet.jpg',
        price: 32,
        category: 'Accessories',
        quantity: 5,
        inventoryStatus: 'LOWSTOCK',
        rating: 3
      },
      {
        id: '1007',
        code: 'mbvjkgip5',
        name: 'Galaxy Earrings',
        description: 'Product Description',
        image: 'galaxy-earrings.jpg',
        price: 34,
        category: 'Accessories',
        quantity: 23,
        inventoryStatus: 'INSTOCK',
        rating: 5
      },
      {
        id: '1008',
        code: 'vbb124btr',
        name: 'Game Controller',
        description: 'Product Description',
        image: 'game-controller.jpg',
        price: 99,
        category: 'Electronics',
        quantity: 2,
        inventoryStatus: 'LOWSTOCK',
        rating: 4
      },
      {
        id: '1009',
        code: 'cm230f032',
        name: 'Gaming Set',
        description: 'Product Description',
        image: 'gaming-set.jpg',
        price: 299,
        category: 'Electronics',
        quantity: 63,
        inventoryStatus: 'INSTOCK',
        rating: 3
      },
      {
        id: '1010',
        code: 'plb34234v',
        name: 'Gold Phone Case',
        description: 'Product Description',
        image: 'gold-phone-case.jpg',
        price: 24,
        category: 'Accessories',
        quantity: 0,
        inventoryStatus: 'OUTOFSTOCK',
        rating: 4
      },
      {
        id: '1011',
        code: '4920nnc2d',
        name: 'Green Earbuds',
        description: 'Product Description',
        image: 'green-earbuds.jpg',
        price: 89,
        category: 'Electronics',
        quantity: 23,
        inventoryStatus: 'INSTOCK',
        rating: 4
      },
      {
        id: '1012',
        code: '250vm23cc',
        name: 'Green T-Shirt',
        description: 'Product Description',
        image: 'green-t-shirt.jpg',
        price: 49,
        category: 'Clothing',
        quantity: 74,
        inventoryStatus: 'INSTOCK',
        rating: 5
      },
      {
        id: '1013',
        code: 'fldsmn31b',
        name: 'Grey T-Shirt',
        description: 'Product Description',
        image: 'grey-t-shirt.jpg',
        price: 48,
        category: 'Clothing',
        quantity: 0,
        inventoryStatus: 'OUTOFSTOCK',
        rating: 3
      },
      {
        id: '1014',
        code: 'waas1x2as',
        name: 'Headphones',
        description: 'Product Description',
        image: 'headphones.jpg',
        price: 175,
        category: 'Electronics',
        quantity: 8,
        inventoryStatus: 'LOWSTOCK',
        rating: 5
      },
      {
        id: '1015',
        code: 'vb34btbg5',
        name: 'Light Green T-Shirt',
        description: 'Product Description',
        image: 'light-green-t-shirt.jpg',
        price: 49,
        category: 'Clothing',
        quantity: 34,
        inventoryStatus: 'INSTOCK',
        rating: 4
      },
      {
        id: '1016',
        code: 'k8l6j58jl',
        name: 'Lime Band',
        description: 'Product Description',
        image: 'lime-band.jpg',
        price: 79,
        category: 'Fitness',
        quantity: 12,
        inventoryStatus: 'INSTOCK',
        rating: 3
      },
      {
        id: '1017',
        code: 'v435nn85n',
        name: 'Mini Speakers',
        description: 'Product Description',
        image: 'mini-speakers.jpg',
        price: 85,
        category: 'Clothing',
        quantity: 42,
        inventoryStatus: 'INSTOCK',
        rating: 4
      },
      {
        id: '1018',
        code: '09zx9c0zc',
        name: 'Painted Phone Case',
        description: 'Product Description',
        image: 'painted-phone-case.jpg',
        price: 56,
        category: 'Accessories',
        quantity: 41,
        inventoryStatus: 'INSTOCK',
        rating: 5
      },
      {
        id: '1019',
        code: 'mnb5mb2m5',
        name: 'Pink Band',
        description: 'Product Description',
        image: 'pink-band.jpg',
        price: 79,
        category: 'Fitness',
        quantity: 63,
        inventoryStatus: 'INSTOCK',
        rating: 4
      },
      {
        id: '1020',
        code: 'r23fwf2w3',
        name: 'Pink Purse',
        description: 'Product Description',
        image: 'pink-purse.jpg',
        price: 110,
        category: 'Accessories',
        quantity: 0,
        inventoryStatus: 'OUTOFSTOCK',
        rating: 4
      },
      {
        id: '1021',
        code: 'pxpzczo23',
        name: 'Purple Band',
        description: 'Product Description',
        image: 'purple-band.jpg',
        price: 79,
        category: 'Fitness',
        quantity: 6,
        inventoryStatus: 'LOWSTOCK',
        rating: 3
      },
      {
        id: '1022',
        code: '2c42cb5cb',
        name: 'Purple Gemstone Necklace',
        description: 'Product Description',
        image: 'purple-gemstone-necklace.jpg',
        price: 45,
        category: 'Accessories',
        quantity: 62,
        inventoryStatus: 'INSTOCK',
        rating: 4
      },
      {
        id: '1023',
        code: '5k43kkk23',
        name: 'Purple T-Shirt',
        description: 'Product Description',
        image: 'purple-t-shirt.jpg',
        price: 49,
        category: 'Clothing',
        quantity: 2,
        inventoryStatus: 'LOWSTOCK',
        rating: 5
      },
      {
        id: '1024',
        code: 'lm2tny2k4',
        name: 'Shoes',
        description: 'Product Description',
        image: 'shoes.jpg',
        price: 64,
        category: 'Clothing',
        quantity: 0,
        inventoryStatus: 'INSTOCK',
        rating: 4
      },
      {
        id: '1025',
        code: 'nbm5mv45n',
        name: 'Sneakers',
        description: 'Product Description',
        image: 'sneakers.jpg',
        price: 78,
        category: 'Clothing',
        quantity: 52,
        inventoryStatus: 'INSTOCK',
        rating: 4
      },
      {
        id: '1026',
        code: 'zx23zc42c',
        name: 'Teal T-Shirt',
        description: 'Product Description',
        image: 'teal-t-shirt.jpg',
        price: 49,
        category: 'Clothing',
        quantity: 3,
        inventoryStatus: 'LOWSTOCK',
        rating: 3
      },
      {
        id: '1027',
        code: 'acvx872gc',
        name: 'Yellow Earbuds',
        description: 'Product Description',
        image: 'yellow-earbuds.jpg',
        price: 89,
        category: 'Electronics',
        quantity: 35,
        inventoryStatus: 'INSTOCK',
        rating: 3
      },
      {
        id: '1028',
        code: 'tx125ck42',
        name: 'Yoga Mat',
        description: 'Product Description',
        image: 'yoga-mat.jpg',
        price: 20,
        category: 'Fitness',
        quantity: 15,
        inventoryStatus: 'INSTOCK',
        rating: 5
      },
      {
        id: '1029',
        code: 'gwuby345v',
        name: 'Yoga Set',
        description: 'Product Description',
        image: 'yoga-set.jpg',
        price: 20,
        category: 'Fitness',
        quantity: 25,
        inventoryStatus: 'INSTOCK',
        rating: 8
      }
    ]; */
  }
  showModalDialog(): void {
    this.displayModal = true;
  }
  confirmDelete(event: Event, user: User): void {
    // console.log(event);
    this.confirmationService.confirm({
      target: event.target,
      message: 'Are you sure that you want to proceed?',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.messageService.add({ severity: 'info', summary: 'Confirmed', detail: 'You have accepted' });
        this.changeStatusOfUser(user, UserStatusType.DELETED);
      },
      reject: () => {
        this.messageService.add({ severity: 'error', summary: 'Rejected', detail: 'You have rejected' });
      }
    });
  }
  selectedOptionFxn(event: Event, option, user: User): void {
    switch (option.id_option) {
      case 1:
        this.changeStatusOfUser(user, UserStatusType.ACTIVE);
        //this.displayBasic2 = true;

        break;
      case 2:
        this.changeStatusOfUser(user, UserStatusType.BLOCKED);
        break;
      case 3:
        this.confirmDelete(event, user);

        break;

      default:
        break;
    }
  }
  generate_code(): void {
    this.displayBasic2 = true;
  }

  formatDate(date) {
    return new Date(date).toLocaleDateString();
  }


  setOptionsMenu(user: User) {

    this.options = [
      /* { name: menu.asignPromo, icon: 'upgrade', id_option: 1 }, */
      user && user.Status == UserStatusType.BLOCKED ? { name: this.menu.unlockUser, icon: 'lock_open', id_option: 1 } : null,
      user && user.Status == UserStatusType.ACTIVE ? { name: this.menu.blockUser, icon: 'lock_outline', id_option: 2 } : null,
      user && user.Status != UserStatusType.DELETED ? { name: this.menu.deleteUser, icon: 'delete', id_option: 3 } : null

    ].filter(Boolean);
  }

  async getUser(slug: string) {

    this.loading = true;

    try {

      const result = await this.userService.getBySlug(slug).toPromise();
      this.user = { ...result, ...this.userService.formatUserFields(result) }
      this.setOptionsMenu(this.user);
      console.log(this.user);

    } catch (error) {

      console.log(error);
    } finally {

      this.loading = false;
    }
  }

  async getCourseOfUser(userSlug: string) {

    this.loading = true;

    try {

      const result = await this.courseService.getCoursesOfLearner(userSlug).toPromise();

      this.courses = result.map(c => ({
        ...c,
        ...this.courseService.formatCourseFields(c)
      }));

    } catch (error) {

      console.log(error);
    } finally {

      this.loading = false;
    }
  }


  async getCourseHistoryOfUser(userSlug: string) {

    this.loading = true;

    try {

      const result = await this.courseHistoryService.getCourseHistoryForUser(userSlug).toPromise();

      this.coursesHistories = result.map(c => ({
        ...c,
        ...this.courseHistoryService.formatCourseHistoryFields(c)
      }));

      console.log('getCourseHistoryOfUser', this.coursesHistories);

    } catch (error) {

      console.log(error);
    } finally {

      this.loading = false;
    }
  }


  async changeStatusOfUser(user: User, newStatus) {

    this.loading = true;

    try {

      const result = await this.userService.editStatus(user.Slug, { status: newStatus }).toPromise();
      this.user = { ...result, ...this.userService.formatUserFields(result) };
      this.setOptionsMenu(this.user);

      const message = await this.translateService.get('admin.misc.editStatusSuccess').toPromise();
      this.toastr.success(message, 'Brain-maker');
    } catch (error) {

      let message = await this.translateService.get('admin.misc.editStatusError').toPromise();
      message = `${message} ${error.error.errors.Error}`;
      this.toastr.error(message, 'Brain-maker');
    } finally {

      this.loading = false;
    }
  }


}
