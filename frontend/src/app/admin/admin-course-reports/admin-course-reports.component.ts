import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ToastrService } from 'ngx-toastr';
import { Category } from '~/app/models/category.model';
import {
	ContentType,
	Course,
	CourseSearch,
	CourseSearchResult,
	OrderbyType
} from '~/app/models/course.model';
import { CategoryService } from '~/app/services/categories/category.service';
import { CourseService } from '~/app/services/courses/course.service';

@Component({
	selector: 'app-admin-course-reports',
	templateUrl: './admin-course-reports.component.html',
	styleUrls: ['./admin-course-reports.component.scss'],
	encapsulation: ViewEncapsulation.None
})
export class AdminCourseReportsComponent implements OnInit {
	date12: Date;
	options = [];
	reasons = [
		{ name: 'Raison 1', code: 'NY' },
		{ name: 'Raison 2', code: 'RM' },
		{ name: 'Raison 3', code: 'LDN' }
	];
	reason: any;
	selectedReason: any;
	showRaisonModal = false;
	courses: Course[];
	count: number;
	searchText: string;
	searchDate: Date;
	loading = false;

	take: number = 12;
	paginationOptions: number[] = [2, 5, 10, 12, 15, 20];

	search: CourseSearch = {
		Title: '',
		Categories: [],
		Type: [ContentType.PDF, ContentType.VIDEO, ContentType.EBOOK],
		PriceType: [],
		Orderby: OrderbyType.RECENT,
		Archived: false,
		UnPublished: false,
		Reported: false,
		UnReported: false,
		Published: false
	};
	loadingSearch = false;
	// searchChange: EventEmitter<CourseSearch> = new EventEmitter<CourseSearch>();

	skip = 0;
	// skipChange: EventEmitter<number> = new EventEmitter<number>();
	// take: number;
	// takeChange: EventEmitter<number> = new EventEmitter<number>();

	// courseSearchResult: CourseSearchResult;
	// courseSearchResultChange: EventEmitter<CourseSearchResult> = new EventEmitter<CourseSearchResult>();
	categories: Category[];
	contentType = ContentType;
	courseSearchResult: Course[];

	status;
	state;

	error: boolean = false;

	constructor(
		private translateService: TranslateService,
		private courseService: CourseService,
		private categoryService: CategoryService,
		private toastrService: ToastrService
	) {}

	async ngOnInit(): Promise<void> {
		// const menu = await this.translateService.get('admin.options').toPromise();

		this.options = [
			{ name: 'Publier', icon: 'publish', id_option: 1 },
			{ name: 'DéPublier', icon: 'upgrade', id_option: 4 },
			{ name: 'Archiver', icon: 'delete', id_option: 2 }
		];

		this.categories = await this.categoryService.getAll().toPromise();
		this.getCourses();
	}

	async getCourses(): Promise<void> {
		this.loading = true;
		this.error = false;

		try {
			const response = await this.courseService
				.getAllReportedCourse(this.take)
				.toPromise();
			this.courses = response.Courses;
			this.count = response.Count;
			this.loading = false;
		} catch (error) {
			this.error = true;
			this.toastrService.success(
				'Une erreurs est survenue lors du traitement',
				'Impossible de charger la page'
			);
			this.courses = [];
			this.error = true;
		}
	}

	onCoursePublish(course: Course): void {
		const index = this.courses?.map((course) => course.Id).indexOf(course.Id);
		this.courses.splice(index, 1);
		this.getCourses();
	}

	onCourseDelete(course: Course): void {
		const index = this.courses?.map((course) => course.Id).indexOf(course.Id);
		this.courses.splice(index, 1);
		this.getCourses();
	}

	async paginate(event: {
		page: number;
		first: number;
		rows: number;
		pageCount: number;
	}): Promise<void> {
		this.loading = true;
		this.error = false;
		this.courses = [];

		console.log(event);
		this.take = event.rows;
		const skip = event.first;
		// console.log(skip, take);

		try {
			if (this.status) {
				this.search.New = this.status === 'NEW' ? true : false;
				this.search.UnPublished = this.status === 'UNPUBLISHED' ? true : false;
				this.search.Published = this.status === 'PUBLISHED' ? true : false;

				this.courseService
					.searchReportedCourseLazy(
						{ take: this.take, skip: this.skip },
						this.search
					)
					.subscribe((res) => {
						console.log('the search result on the filter is :', res);
						this.courseSearchResult = res.Courses;
						this.courses = res.Courses;
						this.count = res.Count;
						setTimeout(() => {
							this.loading = false;
							this.loadingSearch = false;
						}, 300);
					});
			} else {
				const response = await this.courseService
					.getAllReportedCourse(this.take, skip)
					.toPromise();
				this.courses = response.Courses;
				this.loading = false;
			}
		} catch (error) {
			console.log(error);
			this.error = true;
			this.toastrService.success(
				'Une erreurs est survenue lors du traitement',
				'Impossible de charger la page'
			);
			this.courses = [];
			this.loading = false;
		}
	}

	onSearch(e): void {
		e.preventDefault();
		this.courses = [];

		this.search.New = this.status === 'NEW' ? true : false;
		this.search.UnPublished = this.status === 'UNPUBLISHED' ? true : false;
		this.search.Published = this.status === 'PUBLISHED' ? true : false;

		this.loading = true;
		this.loadingSearch = true;
		this.error = false;

		console.log('serach is :', this.search);
		this.take = this.take;
		this.skip = this.skip;
		try {
			this.courseService
				.searchReportedCourseLazy(
					{ take: this.take, skip: this.skip },
					this.search
				)
				.subscribe((res) => {
					console.log('the search result on the filter is :', res);
					this.courseSearchResult = res.Courses;
					this.courses = res.Courses;
					this.count = res.Count;
					setTimeout(() => {
						this.loading = false;
						this.loadingSearch = false;
					}, 300);
				});
		} catch (error) {
			console.log(error);
			this.error = true;
			this.toastrService.success(
				'Une erreurs est survenue lors du traitement',
				'Impossible de charger la page'
			);
			this.courses = [];
		}
	}
}
