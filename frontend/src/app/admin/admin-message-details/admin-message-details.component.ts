import { Component, OnInit } from '@angular/core';
import { AngularEditorConfig } from '@kolkov/angular-editor';
import { UtilsService } from '../../services/utils.service';
import { Location } from '@angular/common';

@Component({
  selector: 'app-admin-message-details',
  templateUrl: './admin-message-details.component.html',
  styleUrls: ['./admin-message-details.component.scss']
})
export class AdminMessageDetailsComponent implements OnInit {
  customers: any[];

  selectedCustomers: any[];

  representatives: any[];

  statuses: any[];

  replyBlock = false;
  transferAddresse = false;
  resumeConfig: AngularEditorConfig;
  model = '';
  // tslint:disable-next-line: variable-name
  constructor(private utilsService: UtilsService, public _location: Location) { }

  ngOnInit(): void {
    this.resumeConfig = this.utilsService.getSimpleconfigAngular();

  }
  show_relpy(): void {
    this.replyBlock = true;
    this.transferAddresse = false;
  }
  show_transfer(): void {
    this.replyBlock = true;
    this.transferAddresse = true;
  }
}
