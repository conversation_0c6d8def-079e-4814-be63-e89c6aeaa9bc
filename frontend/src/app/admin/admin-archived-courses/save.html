<div class="skin">
	<div id="main-wrapper">
		<app-admin-sidebar></app-admin-sidebar>

		<div class="row">
			<div class="col-md-2"></div>
			<div class="col-md-12 col-lg-10 col-sm-12 list-holder">
				<div class="row">
					<div class="col-sm-12 filter-head mb-3">
						<p-card>
							<h1 class="test-title">
								{{ 'admin.menu.archivedCourses' | translate }}
							</h1>
							<div class="d-flex">
								<!-- <span
									class="p-input-icon-left mr-4"
									style="width: 100%; height: 40px"
									><i class="pi pi-search"></i
									><input
										pinputtext=""
										type="text"
										[placeholder]="'admin.misc.search' | translate"
										class="search_input"
								/></span> -->
								<form method="post" class="row" (ngSubmit)="onSearch($event)">
									<div class="form-group col-12 col-md-9">
										<input
											class="form-control"
											type="search"
											name="search"
											id="search"
											[(ngModel)]="search.Title"
											[placeholder]="'course.details.searchCourses' | translate"
										/>
									</div>

									<div class="form-group col-12 col-md-3">
										<button type="submit" class="btn btn-theme full-width">
											<span *ngIf="!loadingSearch">Rechercher</span>
											<app-loading
												*ngIf="loadingSearch"
												[isLoading]="true"
											></app-loading>
										</button>
									</div>

									<div class="form-group col-12 col-md-4">
										<h4 class="side_title">
											{{ 'course.details.courseCategories' | translate }}
										</h4>
										<p-multiSelect
											[options]="categories"
											name="categories"
											optionValue="Id"
											id="categories"
											[(ngModel)]="search.Categories"
											[defaultLabel]="'course.details.selectCategs' | translate"
											optionLabel="Title"
										></p-multiSelect>
									</div>

									<div class="form-group col-6 col-md-4">
										<h4 class="side_title">
											{{ 'course.details.courseCategories' | translate }}
										</h4>
										<div class="p-field-checkbox d-flex align-items-center">
											<p-checkbox
												name="TypeOfCourse"
												[value]="contentType.VIDEO"
												[(ngModel)]="search.Type"
												id="TypeOfCourse2"
											>
											</p-checkbox>
											<label for="TypeOfCourse2" class="mx-2 mt-2">Video</label>
										</div>
										<div class="p-field-checkbox d-flex align-items-center">
											<p-checkbox
												name="TypeOfCourse"
												[value]="contentType.PDF"
												[(ngModel)]="search.Type"
												id="TypeOfCourse3"
											>
											</p-checkbox>
											<label for="TypeOfCourse3" class="mx-2 mt-2">PDF</label>
										</div>

										<div class="p-field-checkbox d-flex align-items-center">
											<p-checkbox
												name="TypeOfCourse"
												[value]="contentType.EBOOK"
												[(ngModel)]="search.Type"
												id="TypeOfCourse4"
											>
											</p-checkbox>
											<label for="TypeOfCourse4" class="mx-2 mt-2">Ebook</label>
										</div>
									</div>

									<div class="form-group col-6 col-md-4">
										<h4 class="side_title">Status</h4>

										<div class="p-field-radiobutton">
											<p-radioButton
												name="status"
												value="NEW"
												[(ngModel)]="status"
												inputId="new"
											></p-radioButton>
											<label for="new">Nouveau</label>
										</div>

										<div class="p-field-radiobutton">
											<p-radioButton
												name="status"
												value="UNPUBLISHED"
												[(ngModel)]="status"
												inputId="unpublished"
											></p-radioButton>
											<label for="unpublished">Dépublié</label>
										</div>
									</div>
								</form>
							</div>
						</p-card>
					</div>

					<div
						class="w-100"
						*ngIf="!loading && courses && courses.length === 0"
					>
						<h1 *ngIf="!error" class="text-center mt-50 mb-10">
							Aucun course archivé trouvé
						</h1>
						<h1 *ngIf="error" class="text-center mt-50 mb-10">
							Un problème est survenue lors du traitement, veuillez réessayer
							plustard.
						</h1>
					</div>

					<div class="w-100" class="admin-loading" *ngIf="loading">
						<app-loading [isLoading]="true"></app-loading>
					</div>

					<div *ngIf="!loading" class="col-12">
						<div class="row">
							<div
								*ngFor="let course of courses"
								class="col-sm-12 col-md-6 col-lg-4 verifs-holder"
							>
								<app-course-verification-item
									[course]="course"
									[options]="options"
									(onPublish)="onCoursePublish($event)"
									(onDelete)="onCourseDelete($event)"
								></app-course-verification-item>
							</div>
						</div>
					</div>

					<div *ngIf="courses && courses.length > 0" class="col-sm-12 mt-4">
						<p-paginator
							[rows]="take"
							[totalRecords]="count"
							[rowsPerPageOptions]="paginationOptions"
							(onPageChange)="paginate($event)"
						></p-paginator>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!-- <p-toast></p-toast> -->

	<p-confirmDialog
		header="Validation"
		[style]="{ width: '50vw' }"
		[baseZIndex]="10000"
		rejectButtonStyleClass="p-button-text"
	>
	</p-confirmDialog>
	<p-dialog
		header="Raison du signalement"
		[(visible)]="showRaisonModal"
		[modal]="true"
		[style]="{ width: '50vw' }"
		[baseZIndex]="10000"
		[draggable]="false"
		[resizable]="false"
	>
		<div class="w-100 signalement-bar" style="min-height: 10vh">
			<p-dropdown
				[options]="reasons"
				[(ngModel)]="selectedReason"
				placeholder="Choisir la raison du signalement"
				editable="true"
				optionLabel="name"
				class="w-100"
			></p-dropdown>
		</div>
		<ng-template pTemplate="footer">
			<p-button
				icon="pi pi-check"
				(click)="addReasonToSignal()"
				label="Signaler"
				styleClass="p-button-text"
			></p-button>
		</ng-template>
	</p-dialog>
</div>
