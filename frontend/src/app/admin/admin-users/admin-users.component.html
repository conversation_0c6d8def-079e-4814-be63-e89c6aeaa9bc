<!-- Component content starts here -->
	<p-card [style]="{ 'margin-bottom': '2em' }">
		<p-table
			#dt
			[value]="users"
			[(selection)]="selectedCustomers"
			dataKey="id"
			styleClass="p-datatable-customers pb-4"
			[responsive]="true"
			[rowHover]="true"
			[rows]="10"
			[showCurrentPageReport]="true"
			[rowsPerPageOptions]="[10, 25, 50]"
			[loading]="loading"
			[paginator]="true"
			currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
			[filterDelay]="0"
			[globalFilterFields]="['Firstname', 'Lastname']"
		>
			<ng-template pTemplate="caption">
				<div class="table-header">
					<h1>{{ 'admin.misc.learners' | translate }}</h1>

					<div class="d-flex">
						<span
							class="p-input-icon-left mr-4"
							style="width: 50%; height: 40px"
							><i class="pi pi-search"></i
							><input
								(input)="dt.filterGlobal($event.target.value, 'contains')"
								pinputtext=""
								type="text"
								[placeholder]="'admin.misc.search' | translate"
								class="search_input"
						/></span>
						<div class="p-field calendar_holder">
							<p-calendar
								[(ngModel)]="date12"
								view="month"
								dateFormat="mm/yy"
								[yearNavigator]="true"
								yearRange="2000:2030"
								[placeholder]="'admin.misc.chooseMonth' | translate"
								[readonlyInput]="true"
								inputId="monthpicker"
							></p-calendar>
						</div>
					</div>
				</div>
			</ng-template>
			<ng-template pTemplate="header">
				<tr>
					<th pSortableColumn="photoUser">
						<div class="p-d-flex p-jc-between p-ai-center">
							{{ 'admin.misc.photoUser' | translate }}
							<p-sortIcon field="photoUser"></p-sortIcon>
						</div>
					</th>
					<th pSortableColumn="name">
						<div class="p-d-flex p-jc-between p-ai-center">
							{{ 'admin.misc.name' | translate }}
							<p-sortIcon field="name"></p-sortIcon>
						</div>
					</th>
					<th pSortableColumn="country.name">
						<div class="p-d-flex p-jc-between p-ai-center">
							{{ 'admin.misc.country' | translate }}
							<p-sortIcon field="country.name"></p-sortIcon>
						</div>
					</th>
					<th pSortableColumn="sex">
						<div class="p-d-flex p-jc-between p-ai-center">
							{{ 'admin.misc.gender' | translate }}
							<p-sortIcon field="sex"></p-sortIcon>
						</div>
					</th>
					<th pSortableColumn="status">
						<div class="p-d-flex p-jc-between p-ai-center">
							{{ 'admin.misc.status' | translate }}
							<p-sortIcon field="status"></p-sortIcon>
						</div>
					</th>
					<th></th>
					<!-- <th style="width: 8rem"></th> -->
				</tr>
			</ng-template>
			<ng-template pTemplate="body" let-user>
				<tr class="p-selectable-row">
					<td
						class="pointer"
						[routerLink]="'/admin/users-details/' + user.Slug"
					>
						<img [src]="user.photoFormated" width="60" height="60" />
					</td>
					<td>
						<!-- <span class="p-column-title">Country</span> -->
						<!-- <img src="assets/showcase/images/demo/flag/flag_placeholder.png" [class]="'flag flag-' + user.country.code" width="30"> -->
						<span class="image-text"
							>{{ user.Firstname }} {{ user.Lastname }}</span
						>
					</td>
					<td>
						<!-- <span class="p-column-title">Representative</span>
                                    <img [alt]="user.representative.name" src="assets/showcase/images/demo/avatar/{{user.representative.image}}" width="32" style="vertical-align: middle" /> -->
						<span class="image-text">{{ user.Country }}</span>
					</td>

					<td>
						<span class="image-text">
							{{ 'admin.misc.' + user.sexFormated | translate }}
						</span>
					</td>

					<td>
						<span [class]="'user-badge status-' + user.statusFormated">{{
							'admin.learners.' + user.statusFormated | translate
						}}</span>
					</td>

					<td style="text-align: center">
						<mat-slide-toggle
							#toggleElement
							class="example-margin"
							[disabled]="userConnected && userConnected.Id == user.Id"
							(change)="
								changeStatusOfUser(
									$event,
									user,
									user.Status == userStatusActive
										? userStatusBlocked
										: userStatusActive
								)
							"
							[checked]="user.Status == userStatusActive"
						>
						</mat-slide-toggle>

						<!-- <button (click)="confirmDelete($event, customer)" pButton pRipple type="button" icon="pi pi-trash" class="p-button-rounded p-button-text p-button-danger"></button> -->
						<button
							pButton
							pRipple
							type="button"
							icon="pi pi-ellipsis-v"
							class="p-button-rounded p-button-text ml-3"
							[matMenuTriggerFor]="menu"
							[disabled]="
								user.optionMenu.length <= 0 ||
								(userConnected && userConnected.Id == user.Id)
							"
						></button>
						<mat-menu #menu="matMenu">
							<button
								mat-menu-item
								*ngFor="let option of user.optionMenu"
								(click)="selectedOptionFxn($event, option, user)"
							>
								<mat-icon>{{ option.icon }}</mat-icon>
								<span>{{ option.name }}</span>
							</button>
						</mat-menu>
					</td>
				</tr>
			</ng-template>
			<ng-template pTemplate="emptymessage">
				<tr>
					<td colspan="8">No users found.</td>
				</tr>
			</ng-template>
		</p-table>
	</p-card>
	<!--
			</div>
		</div>
	</div> -->
	<p-toast SupplementContent></p-toast>

	<p-confirmPopup SupplementContent></p-confirmPopup>
	<p-dialog
		SupplementContent
		header="Code promotion"
		[(visible)]="displayBasic2"
		[modal]="true"
		[style]="{ width: '50vw' }"
		[baseZIndex]="10000"
		[draggable]="false"
		[resizable]="false"
	>
		<p>
			Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
			tempor incididunt ut labore et dolore magna aliqua.
		</p>
		<h1 class="text-center"><b>qqqii-asdasd</b></h1>
		<ng-template pTemplate="footer">
			<p-button
				icon="pi pi-check"
				(click)="displayBasic2 = false"
				label="Envoyer a l'utilisatuer"
				styleClass="p-button-text"
			></p-button>
		</ng-template>
	</p-dialog>
	<!-- Component content ends here -->
