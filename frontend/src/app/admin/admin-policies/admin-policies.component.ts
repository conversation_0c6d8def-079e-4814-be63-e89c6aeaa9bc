import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { AngularEditorConfig } from '@kolkov/angular-editor';
import { PolicyType, UsePolicy } from '~/app/models/use-policy.model';
import { UsePolicyService } from '~/app/services/use-policy.service';
import { UtilsService } from '~/app/services/utils.service';
import { MenuItem } from 'primeng/api';


@Component({
  selector: 'app-admin-policies',
  templateUrl: './admin-policies.component.html',
  styleUrls: ['./admin-policies.component.scss'],
})
export class AdminPoliciesComponent implements OnInit {

  items: MenuItem[] = [
    { label: 'COURSE', icon: '', routerLink: '/admin/policies/COURSE' },
    { label: 'VIDEO', icon: '', routerLink: '/admin/policies/VIDEO' },
    { label: 'AUDIENCE', icon: '', routerLink: '/admin/policies/AUDIENCE' }
  ];

  resumeConfig: AngularEditorConfig;
  course = '';
  showInput: boolean;
  policyType = 1;
  userPolicy: UsePolicy;
  page: string;


  constructor(
    private activatedRoute: ActivatedRoute,
    private utilsService: UtilsService,
    private usePolicyService: UsePolicyService,
  ) { }

  ngOnInit() {

    this.activatedRoute.params.subscribe(params => {
      this.page = params['page'];

      console.log('PAGE', this.page);
      this.initialiseState(); // reset and set based on new parameter this time
    });

    //this.page = this.activatedRoute.snapshot.params.page;
  }

  async initialiseState() {

    if (this.page) {
      if (PolicyType[this.page]) {
        this.policyType = +PolicyType[this.page];
        console.log(this.policyType);
      }
    }
    try {
      this.userPolicy = await this.usePolicyService
        .getByPolicyType(this.policyType)
        .toPromise();
    } catch (e) {
      console.log(e);
    }

    if (!this.userPolicy) {
      this.userPolicy = new UsePolicy(null, null, null, this.policyType);
    }

    this.showInput = !!!this.userPolicy.Title;
    console.log(this.userPolicy);
    this.resumeConfig = this.utilsService.getSimpleconfigAngular();
  }

  onSubmit(e): void {
    e.preventDefault();
    this.usePolicyService
      .update(this.userPolicy.Slug, this.userPolicy)
      .subscribe((data) => {
        console.log(data);
        this.showInput = !this.showInput;
      });
  }

}
