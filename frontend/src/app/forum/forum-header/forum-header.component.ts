import { Component, Input, OnInit } from '@angular/core';
import { User, UserRoleType } from '~/app/models/user.model';
import { HomeLoginComponent } from '~/app/home/<USER>/home-login.component';
import { HomeRegisterComponent } from '~/app/home/<USER>/home-register.component';
import { UtilsService } from '~/app/services/utils.service';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { TranslateService } from '@ngx-translate/core';
import { BRAINMAKERBASKET } from '~/app/models/order.model';
import { environment } from '~/environments/environment';
import { UserService } from '~/app/services/users/user.service';
import { AbonnementsService } from '~/app/services/abonnements/abonnements.service';

@Component({
	selector: 'app-forum-header',
	templateUrl: './forum-header.component.html',
	styleUrls: ['./forum-header.component.scss']
})
export class ForumHeaderComponent implements OnInit {
	user: User;
	@Input() theclass = 'dark-text header-transparent change-logo';
	visibleSidebar1 = false;
	profile = '/assets/img/avatar.png';
	constructor(
		private userService: UserService,
		private utilsService: UtilsService,
		private router: Router,
		private toastr: ToastrService,
		private translateService: TranslateService,
		private abonnementService: AbonnementsService
	) {}

	async ngOnInit(): Promise<void> {
		this.user = await this.userService.getUserConnected()?.toPromise();
		if (this.user) {
			if (this.user.Photo) {
				this.profile = this.user.Photo.Hashname = `${environment.path}/${this.user.Photo.Hashname}`;
			}
		}

		if (this.user) {
			const abonnement = await this.abonnementService
				.getUserActiveAbonnement(this.user.Slug)
				.toPromise();
			if (abonnement?.Forfait?.Name.toLowerCase() === 'essential') {
				if (this.user.Role != UserRoleType.ADMIN) {
					this.router.navigateByUrl('/home/<USER>');
					this.toastr.warning(
						'Brain maker',
						'Veuillez chosoir un forfait adequat pour avoir accès au forum'
					);
				}
			}
		} else {
			this.utilsService.buildModal(HomeLoginComponent, () => {}, 500, true, {});
		}
	}

	openRegisterModal(e): void {
		e.preventDefault();
		const dialogRef = this.utilsService.buildModal(
			HomeRegisterComponent,
			() => {},
			500,
			true,
			{}
		);
	}

	async logout(e): Promise<void> {
		e.preventDefault();
		await this.userService.logout(this.user.Slug).toPromise();
		this.user = null;
		if (this.router.url.startsWith('/forum/ask')) {
			this.router.navigateByUrl('/forum');
		}
	}

	async goToAsk(): Promise<void> {
		if (this.user) this.router.navigateByUrl('/forum/ask');
		else {
			const message = await this.translateService
				.get('forum.read.need_login')
				.toPromise();
			this.toastr.info(message, 'Brain-maker');
			// show login modal
			this.utilsService.buildModal(HomeLoginComponent, () => {});
		}
	}
}
