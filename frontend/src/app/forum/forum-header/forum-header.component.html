<div style="border-bottom: solid .2em red" class="header change-logo" [ngClass]="theclass">
  <div class="container">
    <nav id="navigation" class="navigation navigation-landscape">
      <div class="nav-header">
        <a class="nav-brand static-logo mt-2" routerLink="/">
                    <span class="font-weight-bold">
            <span class="text-dark">Brain-</span><span class="text-danger">maker</span>
                    </span>
        </a>
        <a class="nav-brand fixed-logo mt-2" routerLink="/">
                    <span class="font-weight-bold">
            <span class="text-dark">Brain-</span><span class="text-danger">maker</span>
                    </span>
        </a>
        <div class="nav-toggle"></div>
      </div>
      <div class="nav-menus-wrapper" style="transition-property: none">
        <button pButton pRipple type="button" icon="pi pi-bars" (click)="visibleSidebar1 = true" class="hide-on-large-only  p-button-outlined right mt-3 border-0 text-dark"></button>

        <ul class="nav-menu nav-menu-social hide-on-med-and-down align-to-right">
          <li class="font-weight-bold" *ngIf="user"><a routerLink="/forum">{{ 'forum.header.problem' | translate }}</a></li>
          <li class="font-weight-bold"><a (click)="goToAsk()"> <i class="fas fa-question"></i>  {{ 'forum.header.pose_problem' | translate }}</a></li>
          <li class="mt-3 ml-3 bg-light pointer" *ngIf="user">
            <div class="ps_popover__wrapper">

              <img [src]="profile" class="ps_popover__title p-0" style="height: 40px;width:40px;border-radius: 50%;" alt="" srcset="">
              <div class="ps_popover__content content_user_info" style="top:60px">
                <div class="d-flex justify-content-start mb-1">
                  <img [src]="profile" class="p-0" style="height: 60px;width:60px;border-radius: 50%;" alt="" srcset="">
                  <div class="mb-1 left d-flex ml-3 flex-column justify-content-start align-items-start">
                    <p> <b>{{user.Firstname}}</b> </p>
                    <p><small>{{user.Username}}</small></p>
                  </div>
                </div>
                <div class="d-flex notif_block pointer ">
                  <ngbd-theme-switch [size]="'sm'"></ngbd-theme-switch>
                </div>
                <div class="d-flex notif_block pointer ">
                  <a routerLink="/users">Account</a>
                </div>
                <div class="d-flex notif_block pointer ">
                  <a (click)="logout($event)" href="">Logout</a>
                </div>
                <div class="d-flex notif_block pointer ">
                  <a routerLink="/meetings">Meetings</a>
                </div>
                <div class="d-flex notif_block pointer ">
                  <a routerLink="/forum">Forum</a>
                </div>
                <div class="d-flex notif_block pointer ">
                  <a routerLink="/posts">Blog</a>
                </div>
                <div class="d-flex notif_block pointer ">
                  <a routerLink="/home/<USER>">About</a>
                </div>
                <div class="d-flex notif_block pointer ">
                  <a routerLink="/home/<USER>">Contact</a>
                </div>
                <div class="d-flex notif_block pointer ">
                  <a routerLink="/home/<USER>">pricing</a>
                </div>
              </div>
            </div>
          </li>


          <!-- <li class="login_click bg-red" *ngIf="user">
    <a (click)="logout($event)" href="">Logout</a>
</li> -->

          <li class="login_click bg-red" *ngIf="!user">
            <a href="/" (click)="openLoginModal($event)">{{
              'home.login.title3' | translate
              }}</a>
          </li>
          <li class="login_click" *ngIf="!user">
            <a href="/" (click)="openRegisterModal($event)">{{
              'home.register.title1' | translate
              }}</a>
          </li>
        </ul>
      </div>
    </nav>
  </div>
</div>
<p-sidebar [(visible)]="visibleSidebar1" [baseZIndex]="10000">
  <!-- <a class="nav-brand static-logo mt-2" routerLink="/">
    <span class="font-weight-bold">
  <span class="text-dark">Brain-</span><span class="text-danger">maker</span>
    </span>
</a> -->
  <div class="d-flex justify-content-start mb-1" *ngIf="user">
    <img [src]="profile" class="p-0" style="height: 60px;width:60px;border-radius: 50%;" alt="" srcset="">
    <div class="mb-1 left d-flex ml-3 flex-column justify-content-start align-items-start">
      <p> <b>{{user.Firstname}}</b> </p>
      <p><small>{{user.Username}}</small></p>
    </div>
  </div>

  <!-- <li class="" *ngIf="user"> -->
  <!-- </li> -->

  <ul class="nav-menu">

    <li class="login_click bg-red" *ngIf="!user">
      <a href="/" (click)="openLoginModal($event)">{{
        'home.login.title3' | translate
        }}</a>
    </li>
    <li class="login_click" *ngIf="!user">
      <a href="/" (click)="openRegisterModal($event)">{{
        'home.register.title1' | translate
        }}</a>
    </li>
  </ul>
  <div class="d-flex flex-column">
    <!-- <p routerLink="/instructors" *ngIf="user"><a>Créer une formation</a></p> -->
    <!-- <p class="" routerLink="/courses"><a></a></p> -->
    <div class="d-flex notif_block pointer ">
      <a href="">Public profile</a>
    </div>
    <div class="d-flex notif_block pointer ">
      <a routerLink="/users">Account</a>
    </div>
    <div class="d-flex notif_block pointer ">
      <a (click)="logout($event)" href="">Logout</a>
    </div>
    <div class="d-flex notif_block pointer ">
      <a routerLink="/meetings">Meetings</a>
    </div>
    <div class="d-flex notif_block pointer ">
      <a routerLink="/forum">Forum</a>
    </div>
    <div class="d-flex notif_block pointer ">
      <a routerLink="/posts">Blog</a>
    </div>
    <div class="d-flex notif_block pointer ">
      <a routerLink="/home/<USER>">About</a>
    </div>
    <div class="d-flex notif_block pointer ">
      <a routerLink="/home/<USER>">Contact</a>
    </div>
    <div class="d-flex notif_block pointer ">
      <a routerLink="/home/<USER>">pricing</a>
    </div>
    <div class="d-flex notif_block pointer" *ngIf="user" routerLink="/instructors">
      <a>Créer une formation</a>
    </div>
    <div class="d-flex notif_block pointer" routerLink="/courses">
      <a>Suivre une formation</a>
    </div>
  </div>

</p-sidebar>
<br>
<br>
<br>
<br>
<br>
