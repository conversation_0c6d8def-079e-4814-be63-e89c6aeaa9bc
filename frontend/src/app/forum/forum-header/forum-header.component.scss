.ps_popover__title {
  font-size: 24px;
  line-height: 36px;
  text-decoration: none;
  color: rgb(228, 68, 68);
  text-align: center;
  padding: 15px 0;
}

.ps_popover__wrapper {
  position: relative;
  // margin-top: 1.5rem;
  display: inline-block;
}

.ps_popover__content {
  opacity: 0;
  visibility: hidden;
  position: absolute;
  left: -150px;
  transform: translate(0, 10px);
  background-color: #fff;
  padding: 1.5rem;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.26);
  width: 300px;
  top: 70px
}

// .ps_popover__content:before {
//     position: absolute;
//     z-index: -1;
//     content: "";
//     right: calc(50% - 10px);
//     top: -8px;
//     border-style: solid;
//     border-width: 0 10px 10px 10px;
//     border-color: transparent transparent #bfbfbf transparent;
//     transition-duration: 0.3s;
//     transition-property: transform;
// }
.ps_popover__wrapper:hover .ps_popover__content {
  z-index: 10;
  opacity: 1;
  visibility: visible;
  transform: translate(0, -20px);
  transition: all 0.5s cubic-bezier(0.75, -0.02, 0.2, 0.97);
}

.ps_popover__message {
  text-align: center;
}

.notif_dot {
  height: 10px;
  width: 20px;
  background: red;
  border-radius: 50%;
}

.notif_image {
  height: 50px;
  border-radius: 5px;
}

.notif_block {
  // border-bottom: 1px solid #66666640;
  // margin-bottom: 1em;
  padding: 0.5em;
}

.notif_block:hover a {
  color: #da0b4e;
}

.notif_block:hover {
  background: aliceblue;
}

@media (max-width: 767px) {
  .ps_popover__content {
    width: 70vw;
    left: 0px;
  }
  .content_user_info {
    left: -250px;
  }
}

@media only screen and (max-width: 992px) {
  .hide-on-med-and-down {
    display: none !important;
  }
}

@media only screen and (min-width: 601px) {
  .show-on-medium-and-up {
    display: block !important;
  }
}

@media only screen and (min-width: 993px) {
  .hide-on-large-only {
    display: none !important;
  }
}

@media only screen and (min-width: 600px) and (max-width: 992px) {
  .show-on-medium {
    display: block !important;
  }
  .ps_popover__content {
    width: 40vw;
    left: 0px;
  }
}
