import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ForumSubject } from '~/app/models/forum-subject.model';
import { User, UserRoleType } from '~/app/models/user.model';
import { ForumVote } from '~/app/models/forum-vote.model';
import { ToastrService } from 'ngx-toastr';
import { UserService } from '~/app/services/users/user.service';
import { HomeLoginComponent } from '~/app/home/<USER>/home-login.component';
import { UtilsService } from '~/app/services/utils.service';
import { Course } from '~/app/models/course.model';
import { ConfirmationDialogService } from '~/app/services/confirmation-dialog.service';
import { TranslateService } from '@ngx-translate/core';
import { environment } from '~/environments/environment';
import { ForumSubjectService } from '../services/forum/forum-subject.service';
import { CourseService } from '../services/courses/course.service';
import { AbonnementsService } from '../services/abonnements/abonnements.service';
import { AbonnementsForfaitsService } from '../services/abonnements/forfaits/abonnements-forfaits.service';
import { AbonnementForfait } from '../models/abonnements/forfaits/abonnement-forfait.model';

@Component({
	selector: 'app-forum',
	templateUrl: './forum.component.html',
	styleUrls: ['./forum.component.scss']
})
export class ForumComponent implements OnInit {
	user: User;
	forumSubjects: ForumSubject[] = [];
	isLoading = false;
	search;
	course: Course;
	tab = 'newest';
	totalRecord = 0;

	take: number;
	skip = 0;
	paginateNumberList = [];
	paginateNumberSelected = null;

	constructor(
		private forumSubjectService: ForumSubjectService,
		private router: Router,
		private userService: UserService,
		private toastr: ToastrService,
		private route: ActivatedRoute,
		private courseService: CourseService,
		private confirmationDialogService: ConfirmationDialogService,
		private translateService: TranslateService,
		private utilsService: UtilsService,
		private abonnementService: AbonnementsService
	) {}

	async ngOnInit(): Promise<void> {
		this.take = ForumSubjectService.FORUM_SUBJECT_TAKE;

		this.user = await this.userService.getUserConnected()?.toPromise();

		if (this.user) {
			const abonnement = await this.abonnementService
				.getUserActiveAbonnement(this.user.Slug)
				.toPromise();

			if (abonnement?.Forfait?.Name.toLowerCase() === 'essential') {
				if (this.user.Role != UserRoleType.ADMIN) {
					this.router.navigateByUrl('/home/<USER>');
					this.toastr.warning(
						'Brain maker',
						'Veuillez chosoir un forfait adequat pour avoir accès au forum'
					);
				}
			}
		} else {
			this.utilsService.buildModal(HomeLoginComponent, () => {}, 500, true, {});
		}

		const courseId = this.route.snapshot.params.id;
		if (courseId) {
			this.tab = 'course';
			this.course = await this.courseService.getBySlug(courseId).toPromise();
			await this.getSubjectsByCourse(this.course, this.take, this.skip);
		} else {
			await this.getSubjects(this.take, this.skip);
		}
	}

	getTotalVotes(votes: ForumVote[]): number {
		return votes.length > 0
			? votes.map((item) => item.UpDown).reduce((prev, next) => prev + next)
			: 0;
	}

	async onDelete(subject: ForumSubject): Promise<void> {
		const onDelete = await this.confirmDelete();
		if (!onDelete) {
			return;
		}

		await this.forumSubjectService.delete(subject).toPromise();
		const findIndexDeleted = this.forumSubjects.findIndex(
			(elt) => elt.Id == subject.Id
		);
		this.forumSubjects.splice(findIndexDeleted, 1);

		const message = await this.translateService
			.get('forum.delete_subject')
			.toPromise();
		this.toastr.success(message, 'Brain-maker');
	}

	async closeSubject(subject: ForumSubject): Promise<void> {
		subject.Status = false;
		await this.forumSubjectService.edit(subject).toPromise();

		const findIndexUpdated = this.forumSubjects.findIndex(
			(elt) => elt.Id == subject.Id
		);
		this.forumSubjects.splice(findIndexUpdated, 1, { ...subject });

		const message = await this.translateService
			.get('forum.success_close')
			.toPromise();
		this.toastr.success(message, 'Brain-maker');
	}

	async searchSubjects(
		/* e: KeyboardEvent = null,
		onScroll: boolean = false, */
		take: number,
		skip: number
	): Promise<void> {
		if (!this.search) {
			return;
		}

		/* if (!onScroll && e && e.keyCode === 13) {
			this.forumSubjects = [];
			this.skip = 0;
			this.totals = 1;
			this.tab = 'search';
		} else if (onScroll) {
			this.skip += 3;
			this.tab = 'search';
		} */

		//if (this.tab !== 'search') {
		this.skip = 0;
		skip = 0;
		this.paginateNumberSelected = undefined;
		//}

		this.tab = 'search';
		//if (this.totals > this.skip && ((e && e.keyCode === 13) || onScroll)) {
		this.isLoading = true;
		const response = await this.forumSubjectService
			.searchSubjects(this.search, take, skip)
			.toPromise();

		this.forumSubjects = [...response.Subjects];
		this.totalRecord = response.Count;

		this.isLoading = false;
		//this.totals = response.Count;
		//}
	}

	async getSubjects(
		/* onScroll: boolean = false */ take: number,
		skip: number
	): Promise<void> {
		/* if (!onScroll) {
			this.forumSubjects = [];
			this.skip = 0;
			this.totals = 1;
		} else {
			this.skip += 3;
		} */

		if (this.tab !== 'newest') {
			this.skip = 0;
			skip = 0;
			this.paginateNumberSelected = undefined;
		}

		this.tab = 'newest';

		this.isLoading = true;
		this.forumSubjectService.getSubjects(take, skip).subscribe(
			(value) => {
				this.forumSubjects = [...value.Subjects];

				this.isLoading = false;
				this.totalRecord = value.Count;
			},
			(error) => {
				this.isLoading = false;
			}
		);
	}

	async getSubjectsASC(
		/* onScroll: boolean = false */ take: number,
		skip: number
	): Promise<void> {
		/* if (!onScroll) {
			this.forumSubjects = [];
			this.skip = 0;
			this.totals = 1;
		} else {
			this.skip += 3;
		}
 */
		if (this.tab !== 'old') {
			this.skip = 0;
			skip = 0;
			this.paginateNumberSelected = undefined;
		}

		this.tab = 'old';
		//if (this.totals > this.skip) {
		this.isLoading = true;
		const response = await this.forumSubjectService
			.getSubjectsASC(take, skip)
			.toPromise();
		this.totalRecord = response.Count;

		this.forumSubjects = [...response.Subjects];

		this.isLoading = false;
		//this.totals = response.Count;
		//}
	}

	async getSubjectsByCourse(
		course: Course = null,
		/* onScroll: boolean = false */
		take: number,
		skip: number
	): Promise<void> {
		/* if (!onScroll) {
			this.forumSubjects = [];
			this.skip = 0;
			this.totals = 1;
			this.course = course;
		} else {
			this.skip += 3;
		} */
		if (this.tab !== 'course') {
			this.skip = 0;
			skip = 0;
			this.paginateNumberSelected = undefined;
		}

		this.tab = 'course';
		console.log('course for get subject ========+> ', course);
		this.course = course;

		window.scroll(0, 0);
		this.isLoading = true;
		try {
			const response = await this.forumSubjectService
				.getSubjectsByCourse(this.course.Slug, take, skip)
				.toPromise();

			this.forumSubjects = [...response.Subjects];
			this.totalRecord = response.Count;
		} catch (error) {
			console.log('Error to load form subject by course ====> ', error);
			this.toastr.error('Error to load form subject by course', 'Brain-Maker');
		} finally {
			this.isLoading = false;
		}
	}

	onLoadFromPage(pageNumber: number) {
		if (this.paginateNumberSelected === pageNumber) {
			return;
		}

		window.scroll(0, 0);

		this.skip = pageNumber === 0 ? 0 : pageNumber * this.take;
		this.paginateNumberSelected = pageNumber;

		switch (this.tab) {
			case 'newest':
				this.getSubjects(this.take, this.skip);
				break;

			case 'old':
				this.getSubjectsASC(this.take, this.skip);
				break;

			case 'search':
				this.searchSubjects(this.take, this.skip);
				break;

			case 'course':
				this.getSubjectsByCourse(this.course, this.take, this.skip);
				break;

			default:
				break;
		}
	}

	/* async onScroll(): Promise<void> {
		if (this.tab === 'newest') {
			await this.getSubjects(true);
		} else if (this.tab === 'old') {
			await this.getSubjectsASC(true);
		} else if (this.tab === 'search') {
			await this.searchSubjects(null, true);
		} else if (this.tab === 'course') {
			await this.getSubjectsByCourse(null, true);
		}
	} */

	async confirmDelete(): Promise<boolean> {
		const title = await this.translateService.get('forum.confirm').toPromise();
		const message = await this.translateService
			.get('forum.do_you_want')
			.toPromise();
		const okText = await this.translateService.get('forum.ok').toPromise();
		const cancelText = await this.translateService
			.get('forum.cancel')
			.toPromise();
		return await this.confirmationDialogService
			.confirm(title, message, okText, cancelText)
			.toPromise();
	}

	getImg(forum: ForumSubject): string {
		if (forum.User.Photo) {
			return `${environment.path}/${forum.User.Photo.Hashname}`;
		}
		return '/assets/img/avatar.png';
	}
	async goToAsk(): Promise<void> {
		if (this.user) {
			this.router.navigateByUrl('/forum/ask');
		} else {
			const message = await this.translateService
				.get('forum.read.need_login')
				.toPromise();
			this.toastr.info(message, 'Brain-maker');
			// show login modal
			// this.utilsService.buildModal(HomeLoginComponent, () => { });
			this.utilsService.buildModal(HomeLoginComponent, () => {});
		}
	}
}
