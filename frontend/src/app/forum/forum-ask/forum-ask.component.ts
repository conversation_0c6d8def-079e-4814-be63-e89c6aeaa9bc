import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { Course } from '~/app/models/course.model';
import { Category } from '~/app/models/category.model';
import { CategoryService } from '~/app/services/categories/category.service';
import { AngularEditorConfig } from '@kolkov/angular-editor';
import { ForumSubject } from '~/app/models/forum-subject.model';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute, Router } from '@angular/router';
import { User, UserRoleType } from '~/app/models/user.model';
import { UserService } from '~/app/services/users/user.service';
import { Location } from '@angular/common';
import { ConfirmationDialogService } from '~/app/services/confirmation-dialog.service';
import { TranslateService } from '@ngx-translate/core';
import { CourseService } from '~/app/services/courses/course.service';
import { ForumSubjectService } from '~/app/services/forum/forum-subject.service';
import { HomeLoginComponent } from '~/app/home/<USER>/home-login.component';
import { AbonnementsService } from '~/app/services/abonnements/abonnements.service';
import { UtilsService } from '~/app/services/utils.service';

@Component({
	selector: 'app-forum-ask',
	templateUrl: './forum-ask.component.html',
	styleUrls: ['./forum-ask.component.scss']
})
export class ForumAskComponent implements OnInit {
	categories: Category[] = [];
	selectedCategory: Category = null;

	courses: Course[] = [];

	forumSubject: ForumSubject = new ForumSubject(0, '');
	user: User;

	editorConfig: AngularEditorConfig = {
		editable: true,
		spellcheck: true,
		height: 'auto',
		minHeight: '150px',
		maxHeight: 'auto',
		width: 'auto',
		minWidth: '0',
		translate: 'yes',
		enableToolbar: true,
		showToolbar: true,
		placeholder: 'Enter text here...',
		defaultParagraphSeparator: '',
		defaultFontName: '',
		defaultFontSize: '',
		fonts: [
			{ class: 'arial', name: 'Arial' },
			{ class: 'times-new-roman', name: 'Times New Roman' },
			{ class: 'calibri', name: 'Calibri' },
			{ class: 'comic-sans-ms', name: 'Comic Sans MS' }
		],
		customClasses: [
			{
				name: 'quote',
				class: 'quote'
			},
			{
				name: 'redText',
				class: 'redText'
			},
			{
				name: 'titleText',
				class: 'titleText',
				tag: 'h1'
			}
		],
		uploadUrl: 'v1/image',
		uploadWithCredentials: false,
		sanitize: true,
		toolbarPosition: 'top',
		toolbarHiddenButtons: [['bold', 'italic'], ['fontSize']]
	};
	subject: ForumSubject;
	onEdit = false;
	subjectSlug;

	constructor(
		private courseService: CourseService,
		private categoryService: CategoryService,
		private toastr: ToastrService,
		private forumSubjectService: ForumSubjectService,
		private router: Router,
		private route: ActivatedRoute,
		private userService: UserService,
		private location: Location,
		private translateService: TranslateService,
		private abonnementService: AbonnementsService,
		private utilsService: UtilsService,
		private _cd: ChangeDetectorRef
	) {}

	async ngOnInit(): Promise<void> {
		this.user = await this.userService.getUserConnected().toPromise();
		if (!this.user) {
			this.location.back();
			const message = await this.translateService
				.get('forum.read.need_login')
				.toPromise();
			this.toastr.info(message, 'Brain-maker');
			return;
		}

		if (this.user) {
			const abonnement = await this.abonnementService
				.getUserActiveAbonnement(this.user.Slug)
				.toPromise();
			if (abonnement?.Forfait?.Name.toLowerCase() === 'essential') {
				if (this.user.Role != UserRoleType.ADMIN) {
					this.router.navigateByUrl('/home/<USER>');
					this.toastr.warning(
						'Brain maker',
						'Veuillez chosoir un forfait adequat pour avoir accès au forum'
					);
				}
			}
		} else {
			this.utilsService.buildModal(HomeLoginComponent, () => {}, 500, true, {});
		}

		this.forumSubject.Course = null;
		if (this.router.url !== '/forum/ask') {
			this.subjectSlug = this.route.snapshot.params.subjectSlug;
			this.subject = await this.forumSubjectService
				.getBySlug(this.subjectSlug)
				.toPromise();

			this.forumSubject = this.subject;
		}

		// if is not answer of subject, load this
		if (!this.subject || this.subject?.Title) {
			this.categories = await this.categoryService.getAll().toPromise();
			this.courses = await this.courseService.getAll(1000, 0).toPromise();

			if (this.subject) {
				this.selectedCategory = this.categories.find(
					(elt) => elt.Id === this.subject.Course.Categories[0].Id
				);

				this.forumSubject.Course = this.courses.find(
					(elt) => elt.Id === this.subject.Course.Id
				);
			}
		}
	}

	onSubmit(): void {
		if (!this.subject) {
			// create mode
			this.forumSubject.User = this.user;

			this.forumSubjectService.add(this.forumSubject).subscribe(
				async (value) => {
					console.log(value);
					const message = await this.translateService
						.get('forum.ask.success_add_subject')
						.toPromise();
					this.toastr.success(message, 'Brain-maker');
					this.router.navigateByUrl('/forum');
				},
				async (error) => {
					console.log(error);
					const message = await this.translateService
						.get('forum.an_error_occured')
						.toPromise();
					this.toastr.error(message, 'Brain-maker');
				}
			);
		} else {
			// edit mode
			this.forumSubjectService.edit(this.forumSubject).subscribe(
				async (value) => {
					console.log(value);
					const message = await this.translateService
						.get('forum.success_update')
						.toPromise();
					this.toastr.success(message, 'Brain-maker');
					this.router.navigateByUrl(
						`/forum/read/${
							this.subject.Parent ? this.subject.Parent.Slug : this.subject.Slug
						}`
					);
				},
				async (error) => {
					console.log(error);
					const message = await this.translateService
						.get('forum.an_error_occured')
						.toPromise();
					this.toastr.error(message, 'Brain-maker');
				}
			);
		}
	}

	async onChange(): Promise<void> {
		this.forumSubject.Course = null;
		this.courses = (
			await this.courseService
				.getAllByCategories(this.selectedCategory.Slug, 1000, 0)
				.toPromise()
		).Courses;
	}
}
