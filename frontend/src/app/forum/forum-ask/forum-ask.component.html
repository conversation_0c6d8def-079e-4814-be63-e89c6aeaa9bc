<div class="red-skin">
	<!-- BODY -->

	<div id="main-wrapper">
		<app-home-header></app-home-header>
		<!-- MAIN -->

		<section class="main p-0 mt-3">
			<div class="bg-light text-center w-100 pb-2">
				<p class="bg-light text-black-50 title text-center text-uppercase">
					{{
						subject
							? ('forum.ask.update' | translate)
							: ('forum.ask.create' | translate)
					}}
				</p>
				{{ 'forum.ask.fill_field' | translate }} (
				<span class="requi">*</span> )
			</div>

			<div class="container mt-2">
				<div id="ask">
					<form class="row" #f="ngForm" (ngSubmit)="onSubmit()">
						<div
							*ngIf="!subject || subject?.Title"
							class="form-group col-sm-12"
						>
							<h4>
								{{ 'forum.ask.title' | translate }} <span class="requi">*</span>
							</h4>
							<label for="ask-title">{{
								'forum.ask.title_desc' | translate
							}}</label>
							<input
								required
								value=""
								[(ngModel)]="forumSubject.Title"
								class="form-control"
								type="text"
								name="title"
								id="ask-title"
								autocomplete="current-title"
							/>
						</div>

						<div
							*ngIf="!subject || subject?.Title"
							class="form-group col-lg-6 col-sm-12"
						>
							<h4>{{ 'forum.ask.category' | translate }}</h4>
							<label>{{ 'forum.ask.category_desc' | translate }}</label> <br />
							<select
								(change)="onChange()"
								class="form-control"
								name="ategory"
								id="category"
								[(ngModel)]="selectedCategory"
							>
								<option disabled value="null">
									{{ 'forum.ask.select_category' | translate }}
								</option>
								<option
									*ngFor="let category of categories"
									[ngValue]="category"
								>
									{{ category.Title }}
								</option>
							</select>
						</div>

						<div
							*ngIf="!subject || subject?.Title"
							class="form-group col-lg-6 col-sm-12"
						>
							<h4>
								{{ 'forum.ask.course' | translate }}
								<span class="requi">*</span>
							</h4>
							<label>{{ 'forum.ask.course_desc' | translate }}</label> <br />
							<select
								required
								class="form-control"
								name="course"
								id="course"
								[(ngModel)]="forumSubject.Course"
							>
								<option disabled value="null">
									{{ 'forum.ask.select_course' | translate }}
								</option>
								<option *ngFor="let course of courses" [ngValue]="course">
									{{ course.Title }}
								</option>
							</select>
						</div>

						<div class="form-group col-12">
							<h4>
								{{ 'forum.ask.body' | translate }} <span class="requi">*</span>
							</h4>
							<label for="ask-body">{{
								'forum.ask.body_desc' | translate
							}}</label>
							<ckeditor
								class="form-control ckeditor"
								type="inline"
								name="body"
								id="ask-body"
								required
								[(ngModel)]="forumSubject.Body"
							></ckeditor>
							<!--angular-editor [config]="editorConfig" name="body" id="ask-body" required
                                            [placeholder]="'forum.read.enter_text' | translate "
                                            [(ngModel)]="forumSubject.Body"></angular-editor-->
							<br />
							<div class="text-center">
								<input
									[disabled]="f.invalid"
									class="btn btn-danger"
									id="btn"
									type="submit"
									value="{{
										subject
											? ('forum.ask.update' | translate)
											: ('forum.ask.publish' | translate)
									}}"
								/>
							</div>
						</div>
					</form>
				</div>
			</div>
		</section>
		<!-- END OF MAIN -->
	</div>
	<!-- END OF BODY -->
	<br />
	<br />
	<app-home-footer></app-home-footer>
</div>
