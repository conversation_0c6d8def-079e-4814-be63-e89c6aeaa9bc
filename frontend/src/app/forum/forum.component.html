<app-home-header></app-home-header>

<!-- BODY -->

<div id="main-wrapper">
	<!-- MAIN -->
	<section class="main">
		<div class="container">
			<div class="tabs mt-3 row">
				<div
					class="col-12 mb-5 d-flex align-items-center justify-content-between row"
				>
					<form #f="ngForm" class="form-group col-12 p-0 d-flex">
						<input
							[(ngModel)]="search"
							placeholder="{{ 'forum.search' | translate }}"
							class="form-control input-search mr-2"
							value=""
							type="search"
							name="search"
							id="search"
							(keydown)="searchSubjects(take, skip)"
							autocomplete="current-search"
							required
						/>
						<!--button style="" [disabled]="f.invalid" type="submit" class="text-white bg-primary btn btn-outline-primary"><i class="fas fa-search"></i></button-->
						<p-button
							[label]="'forum.header.pose_problem' | translate"
							(click)="goToAsk()"
							icon="pi pi-comments"
						>
						</p-button>
					</form>
					<div class="nav col nav-tabs">
						<a
							(click)="getSubjects(take, skip)"
							data-toggle="tab"
							class="nav-item nav-link {{ tab == 'newest' ? 'active' : '' }}"
							>{{ 'forum.newest' | translate }}</a
						>
						<a
							(click)="getSubjectsASC(take, skip)"
							data-toggle="tab"
							class="nav-item nav-link {{ tab == 'old' ? 'active' : '' }}"
							>{{ 'forum.older' | translate }}</a
						>
						<a
							*ngIf="tab == 'course'"
							(click)="getSubjectsByCourse(course, take, 0)"
							data-toggle="tab"
							class="nav-item nav-link col {{
								tab == 'course' ? 'active' : ''
							}}"
							>{{ 'forum.course' | translate }} [ {{ course?.Title }} ]</a
						>
					</div>
				</div>

				<!--  begin  subject-list -->
				<!-- infiniteScroll
                    class="tab-pane active"
						[infiniteScrollDistance]="2"
						[infiniteScrollThrottle]="150"
						(scrolled)="onScroll()" -->
				<div class="tab-content col-12">
					<div class="tab-pane active">
						<div class="row" *ngFor="let subject of forumSubjects">
							<div class="col-2 text-center text-muted">
								<div>
									<span>{{ getTotalVotes(subject.Votes) }}</span> <br />
									<span style="font-size: 10px">{{
										'forum.votes' | translate
									}}</span>
								</div>
								<div class="mt-2">
									<span>{{ subject.Answers?.length }}</span> <br />
									<span style="font-size: 10px">{{
										'forum.answers' | translate
									}}</span>
								</div>
							</div>
							<div class="col-10">
								<a
									class="text-dark text-title"
									style="font-size: 22px"
									routerLink="/forum/read/{{ subject.Slug }}"
								>
									<span class="text-success" *ngIf="subject.AcceptedAnswer"
										>[{{ 'forum.solved' | translate }}]
									</span>
									<span style="color: red" *ngIf="subject.Status == false"
										>[{{ 'forum.closed' | translate }}]
									</span>
									{{
										subject.Title?.length < 150
											? subject.Title
											: subject.Title.substr(0, 150) + '...'
									}}
								</a>

								<span
									*ngIf="
										user && user.Id == subject.User.Id && subject.Status == true
									"
									class="float-right"
								>
									<a class="ml-3" style="" (click)="onDelete(subject)"
										><i class="fas fa-trash text-danger"></i
									></a>
									<a
										class="ml-2"
										style=""
										routerLink="/forum/ask/edit/{{ subject.Slug }}"
										><i class="fas fa-edit"> </i
									></a>
									<a
										class="ml-1 btn-sm font-weight-bold text-purple"
										style=""
										(click)="closeSubject(subject)"
										>{{ 'forum.close' | translate }}</a
									>
								</span>
								<br />
								<span class="ml-0" style="font-size: 14px">
									<!-- {{subject.CreatedAt | date: ('forum.dateFormat' | translate)}} {{ 'forum.by' | translate }}
                  <span class='text-primary'> {{subject.User.Username}} </span> . -->
									<a
										class="ml-2 text-info"
										(click)="getSubjectsByCourse(subject.Course, take, skip)"
										>{{ subject.Course?.Title }}</a
									>
								</span>
								<br />
								<p
									[innerHTML]="subject.Body.substr(0, 500) + '...'"
									class="text-wrap text-forum-body"
								></p>
							</div>
							<div class="col-12 d-flex justify-content-end">
								<div>
									<span>{{
										subject.CreatedAt | date: ('forum.dateFormat' | translate)
									}}</span>
									<div class="d-flex mt-2">
										<img
											[src]="getImg(subject)"
											alt=""
											class="circle"
											srcset=""
											style="height: 40px; width: 40px"
										/>
										<p class="ml-2 d-flex align-items-center">
											{{ subject.User.Firstname }}
										</p>
									</div>
								</div>
							</div>
							<div class="col-12">
								<hr />
							</div>
						</div>
					</div>
				</div>
				<!--  end  subject-list -->

				<div *ngIf="isLoading" class="text-center col-12">
					<img src="assets/img/forum_loaging.gif" alt="" />
				</div>

				<div *ngIf="forumSubjects?.length == 0" class="text-center col-12">
					<br /><br /><br /><br /><br />
					<span style="font-size: 20px" class="text-muted">{{
						'forum.no_content' | translate
					}}</span>
				</div>
				<!--        <section class="text-center">-->
				<!--          <a style="" class="btn btn-sm text-primary btn-outline-primary"> 1 </a>-->
				<!--        </section>-->
			</div>

			<div
				class="col-lg-12 col-md-12 col-sm-12 text-center"
				*ngIf="totalRecord"
			>
				<!-- <button
					type="button"
					class="btn btn-pagninate mr-2"
					[ngClass]="{ active: paginateNumberSelected === num }"
					*ngFor="let num of paginateNumberList"
					(click)="onLoadFromPage(num)"
				>
					{{ num }}
				</button> -->
				<p-paginator
					[rows]="take"
					[totalRecords]="totalRecord"
					(onPageChange)="onLoadFromPage($event.page)"
				></p-paginator>
			</div>
		</div>
	</section>
	<!-- END OF MAIN -->
	<br />
	<br />
	<app-home-footer></app-home-footer>
</div>
<!-- END OF BODY -->
