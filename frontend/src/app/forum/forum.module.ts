import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ForumRoutingModule } from './forum-routing.module';
import { ForumComponent } from './forum.component';

import { SharedModule } from '~/app/shared/shared.module';
import { FormsModule } from '@angular/forms';
import { ForumReadComponent } from './forum-read/forum-read.component';
import { ForumAskComponent } from './forum-ask/forum-ask.component';
import { ForumCommentComponent } from './forum-comment/forum-comment.component';
import { ForumHeaderComponent } from './forum-header/forum-header.component';
import { MultiSelectModule } from 'primeng/multiselect';
import { SelectButtonModule } from 'primeng/selectbutton';
import { InfiniteScrollModule } from 'ngx-infinite-scroll';
import { ButtonModule } from 'primeng/button';

@NgModule({
  declarations: [ForumComponent, ForumReadComponent, ForumAskComponent, ForumCommentComponent, ForumHeaderComponent],
  imports: [CommonModule, ForumRoutingModule, SharedModule, FormsModule, MultiSelectModule,
    SelectButtonModule, ButtonModule, InfiniteScrollModule]
})
export class ForumModule { }
