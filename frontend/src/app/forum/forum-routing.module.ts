import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import {ForumReadComponent} from '~/app/forum/forum-read/forum-read.component';
import {ForumCommentComponent} from '~/app/forum/forum-comment/forum-comment.component';
import {ForumAskComponent} from '~/app/forum/forum-ask/forum-ask.component';
import {ForumComponent} from '~/app/forum/forum.component';

const routes: Routes = [
  { path: '', component: ForumComponent },
  { path: 'course/:id', component: ForumComponent },
  { path: 'tag/:tag', component: ForumComponent },
  { path: 'read/:subjectSlug', component: ForumReadComponent },
  { path: 'comment/edit/:subjectSlug/:commentSlug', component: ForumCommentComponent },
  { path: 'ask', component: ForumAskComponent },
  { path: 'ask/edit/:subjectSlug', component: ForumAskComponent }
  ];

@NgModule({
	imports: [RouterModule.forChild(routes)],
	exports: [RouterModule]
})
export class ForumRoutingModule {}
