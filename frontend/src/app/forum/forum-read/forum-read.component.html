<div id="main-wrapper">
	<app-home-header [theclass]="'header-light'"></app-home-header>

	<main class="my-4" style="background-color: #eeeeee61; position: relative">
		<section class="container">
			<div class="tabs-body row">
				<div class="col-1 mt-4 ml-2">
					<span *ngIf="user">
						<span *ngIf="getCurrentUserVote(subject?.Votes)?.UpDown == -1">
							<a (click)="editVote(getCurrentUserVote(subject?.Votes), 1)">
								<i class="far fa-thumbs-up text-black-50"></i>
							</a>
							<br />
							<span>{{ getTotalVotes(subject?.Votes) }}</span> <br />
							<a (click)="deleteVote(getCurrentUserVote(subject?.Votes))">
								<i class="fas fa-thumbs-down text-black-50"></i>
							</a>
						</span>
						<span *ngIf="getCurrentUserVote(subject?.Votes)?.UpDown == 1">
							<a (click)="deleteVote(getCurrentUserVote(subject?.Votes))"
								><i class="fas fa-thumbs-up text-black-50"></i
							></a>
							<br />
							<span>{{ getTotalVotes(subject?.Votes) }}</span> <br />
							<a (click)="editVote(getCurrentUserVote(subject?.Votes), -1)"
								><i class="far fa-thumbs-down text-black-50"></i
							></a>
						</span>
						<span *ngIf="!getCurrentUserVote(subject?.Votes)?.UpDown">
							<a (click)="saveVote(subject, 1)"
								><i class="far fa-thumbs-up text-black-50"></i
							></a>
							<br />
							<span>{{ getTotalVotes(subject?.Votes) }}</span> <br />
							<a (click)="saveVote(subject, -1)"
								><i class="far fa-thumbs-down text-black-50"></i
							></a>
						</span>
					</span>
					<span *ngIf="!user">
						<a (click)="loginToVote()"
							><i class="far fa-thumbs-up text-black-50"></i
						></a>
						<br />
						<span>{{ getTotalVotes(subject?.Votes) }}</span> <br />
						<a (click)="loginToVote()"
							><i class="far fa-thumbs-down text-black-50"></i
						></a>
					</span>
				</div>

				<!--     begin subject-->
				<div class="col-10">
					<a routerLink="/forum" class="btn btn-modern mb-2"
						>{{ 'user.misc.back' | translate
						}}<span><i class="ti-arrow-left"></i></span
					></a>

					<div class="toast-body card">
						<p
							*ngIf="subject?.Status == false"
							style="font-size: 26px"
							class="text-purple"
						>
							{{ 'forum.read.closed_message' | translate }}
						</p>
						<h1>
							<span *ngIf="subject?.Status == false" class="text-purple"
								>[{{ 'forum.closed' | translate }}]</span
							>
							{{ subject?.Title }}
						</h1>
						<div style="font-size: 13px">
							<a
								class="ml-0 text-info"
								routerLink="/forum/course/{{ subject?.Course.Slug }}"
								>{{ subject?.Course.Title }}</a
							>
							<span
								*ngIf="
									user && user.Id == subject?.User.Id && subject?.Status == true
								"
							>
								<a class="ml-3" style="" (click)="onDelete(subject)"
									><i class="fas fa-trash text-danger"></i
								></a>
								<a class="ml-2" routerLink="/forum/ask/edit/{{ subject?.Slug }}"
									><i class="fas fa-edit"> </i
								></a>
								<a
									class="ml-1 btn-sm font-weight-bold text-purple"
									style=""
									(click)="closeSubject()"
									>{{ 'forum.close' | translate }}</a
								>
							</span>
						</div>

						<div style="font-size: 13px"></div>
						<p [innerHTML]="subject?.Body" class="text-forum-body"></p>
						<!-- Asked the {{subject?.CreatedAt | date: ('forum.dateFormat' | translate)}} {{ 'forum.by' | translate }} <span
            class='text-primary'>{{subject?.User.Username}}</span> . -->
						<div class="w-100 d-flex justify-content-end">
							<img
								[src]="getImg(subject)"
								alt=""
								class="circle"
								srcset=""
								style="height: 40px; width: 40px"
							/>
							<div>
								<span>{{
									subject?.CreatedAt | date: ('forum.dateFormat' | translate)
								}}</span>
								<br />
								<span>{{ subject?.User.Firstname }}</span>
							</div>
						</div>
						<!--         list of subject comment-->
						<hr *ngIf="subject?.Comments.length > 0" />
						<div>
							<div
								*ngFor="let subjectComment of subject?.Comments"
								class="mb-4"
							>
								<div style="font-size: 10px" class="text-forum-body-answer">
									<span style="font-size: 13px">
										{{ subjectComment.Body }}
									</span>
									|
									<!--  *ngIf="user && user.Id == subjectComment.User?.Id"-->
									<span *ngIf="subject?.Status == true">
										<a
											*ngIf="
												user &&
												(user.Id == subjectComment.User.Id ||
													user.Id == subject?.User.Id)
											"
											class="ml-2"
											(click)="removeComment(subjectComment)"
											><i class="fas fa-trash text-danger"></i
										></a>
										<a
											*ngIf="user && user.Id == subjectComment.User.Id"
											class="ml-2"
											routerLink="/forum/comment/edit/{{ subject?.Slug }}/{{
												subjectComment.Slug
											}}"
											><i class="fas fa-edit"> </i
										></a>
									</span>
									|
									<a
										*ngIf="subject?.Status == true"
										(click)="showHide('c' + subjectComment.Id)"
										class="text-muted"
									>
										<i class="fa-reply"></i
										>{{ 'forum.read.answer_comment' | translate }}</a
									>
									<div class="w-100 mb-2 d-flex justify-content-end">
										<img
											[src]="getImg(subjectComment)"
											alt=""
											class="circle"
											srcset=""
											style="height: 40px; width: 40px"
										/>
										<div>
											<span>{{
												subjectComment.CreatedAt
													| date: ('forum.dateFormat' | translate)
											}}</span>
											<br />
											<span>{{ subjectComment.User.Firstname }}</span>
										</div>
									</div>
									<div style="font-size: 9px" class="col">
										<!-- subject-comment answer form -->
										<form
											#f="ngForm"
											id="c{{ subjectComment.Id }}"
											(ngSubmit)="onSubmitComment(null, subjectComment)"
											class="d-none comment-form"
										>
											<textarea
												required
												[(ngModel)]="commentBody"
												class="form-control"
												name="body"
											></textarea
											><br />
											<input
												[disabled]="f.invalid"
												class="btn btn-sm btn-danger"
												type="submit"
												value="{{ 'forum.read.comment' | translate }}"
											/>
										</form>
									</div>
								</div>

								<!--         list of subject comment answer -->
								<div
									*ngFor="let commentSubjectAnswer of subjectComment.Answers"
									style="padding-left: 3%; font-size: 8px"
									class="text-forum-body-answer"
								>
									<span style="font-size: 11px">
										{{ commentSubjectAnswer.Body }}
									</span>
									<span>
										<span *ngIf="subject?.Status == true">
											<a
												*ngIf="
													user &&
													(user.Id == commentSubjectAnswer.User.Id ||
														user.Id == subject?.User.Id)
												"
												class="ml-2"
												(click)="removeComment(commentSubjectAnswer)"
												><i class="fas fa-trash text-danger"></i
											></a>
											<a
												*ngIf="user && user.Id == commentSubjectAnswer.User.Id"
												class="ml-2"
												routerLink="/forum/comment/edit/{{ subject?.Slug }}/{{
													commentSubjectAnswer.Slug
												}}"
												><i class="fas fa-edit"> </i
											></a>
										</span>
									</span>
									<div class="w-100 mb-2 d-flex justify-content-end">
										<img
											[src]="getImg(commentSubjectAnswer)"
											alt=""
											class="circle"
											srcset=""
											style="height: 40px; width: 40px"
										/>
										<div>
											<span>{{
												commentSubjectAnswer.CreatedAt
													| date: ('forum.dateFormat' | translate)
											}}</span>
											<br />
											<span>{{ commentSubjectAnswer.User.Firstname }}</span>
										</div>
									</div>
								</div>
								<!--    end  list of subject comment answer -->
							</div>
							<!--  end  list of subject comment-->
							<hr />
							<div style="font-size: 10px">
								<!--  subject-comment form -->
								<a
									*ngIf="subject?.Status == true"
									(click)="showHide('a' + subject?.Id)"
									class="text-muted float-right"
								>
									<i class="fas fa-reply"></i
									>{{ 'forum.read.add_comment' | translate }}</a
								>
								<form
									#g="ngForm"
									id="a{{ subject?.Id }}"
									(ngSubmit)="onSubmitComment(subject)"
									class="d-none comment-form"
								>
									<textarea
										required
										rows="1"
										[(ngModel)]="commentBody"
										class="form-control"
										name="body"
									></textarea
									><br />
									<input
										[disabled]="g.invalid"
										class="btn btn-sm btn-danger float-right"
										type="submit"
										value="{{ 'forum.read.comment' | translate }}"
									/>
								</form>
							</div>
						</div>
					</div>
					<!--  end  subject -->

					<br />
					<strong>
						{{ nbrAnswer }} {{ 'forum.read.answer' | translate }}
					</strong>
					<br />
					<br />
					<br />

					<div class="toast-body card">
						<!--  begin  answers -->
						<div *ngFor="let subjectAnswer of subjectAnswers" class="row">
							<div class="col-12">
								<hr />
							</div>
							<div class="col-1 mt-4">
								<span *ngIf="user">
									<span
										*ngIf="
											getCurrentUserVote(subjectAnswer.Votes)?.UpDown == -1
										"
									>
										<a
											(click)="
												editVote(getCurrentUserVote(subjectAnswer.Votes), 1)
											"
										>
											<i class="far fa-thumbs-up text-black-50"></i>
										</a>
										<br />
										<span>{{ getTotalVotes(subjectAnswer.Votes) }}</span> <br />
										<a
											(click)="
												deleteVote(getCurrentUserVote(subjectAnswer.Votes))
											"
										>
											<i class="fas fa-thumbs-down text-black-50"></i>
										</a>
									</span>
									<span
										*ngIf="getCurrentUserVote(subjectAnswer.Votes)?.UpDown == 1"
									>
										<a
											(click)="
												deleteVote(getCurrentUserVote(subjectAnswer.Votes))
											"
											><i class="fas fa-thumbs-up text-black-50"></i
										></a>
										<br />
										<span>{{ getTotalVotes(subjectAnswer.Votes) }}</span> <br />
										<a
											(click)="
												editVote(getCurrentUserVote(subjectAnswer.Votes), -1)
											"
											><i class="far fa-thumbs-down text-black-50"></i
										></a>
									</span>
									<span
										*ngIf="!getCurrentUserVote(subjectAnswer.Votes)?.UpDown"
									>
										<a (click)="saveVote(subjectAnswer, 1)"
											><i class="far fa-thumbs-up text-black-50"></i
										></a>
										<br />
										<span>{{ getTotalVotes(subjectAnswer.Votes) }}</span> <br />
										<a (click)="saveVote(subjectAnswer, -1)"
											><i class="far fa-thumbs-down text-black-50"></i
										></a>
									</span>
								</span>
								<span *ngIf="!user">
									<a (click)="loginToVote()"
										><i class="far fa-thumbs-up text-black-50"></i
									></a>
									<br />
									<span>{{ getTotalVotes(subject?.Votes) }}</span> <br />
									<a (click)="loginToVote()"
										><i class="far fa-thumbs-down text-black-50"></i
									></a>
								</span>
								<span *ngIf="subjectAnswer.Id == subject?.AcceptedAnswer?.Id">
									<br />
									<i class="fas fa-check text-success"></i>
									<a
										(click)="deleteAcceptedAnswer()"
										*ngIf="
											user &&
											user.Id == subject?.User.Id &&
											subject?.Status == true
										"
									>
										<i class="fas fa-times text-danger"></i>
									</a>
								</span>
							</div>
							<div class="col-10">
								<span [innerHTML]="subjectAnswer.Body"></span>
								<div style="font-size: 11px">
									<span *ngIf="subject?.Status == true">
										<a
											*ngIf="
												user &&
												(user.Id == subjectAnswer.User.Id ||
													user.Id == subject?.User.Id)
											"
											class="ml-3"
											style=""
											(click)="onDelete(subjectAnswer)"
											><i class="fas fa-trash text-danger"></i
										></a>
										<a
											*ngIf="user && user.Id == subjectAnswer.User.Id"
											class="ml-2"
											routerLink="/forum/ask/edit/{{ subjectAnswer.Slug }}"
											><i class="fas fa-edit"> </i
										></a>
										<a
											class="ml-2"
											*ngIf="
												subjectAnswer.Id != subject?.AcceptedAnswer?.Id &&
												user &&
												user.Id == subject?.User.Id
											"
											(click)="setAcceptedAnswer(subjectAnswer)"
										>
											<i class="fas fa-check-square text-success"></i>
										</a>
									</span>
								</div>
								<div class="w-100 d-flex justify-content-end">
									<img
										[src]="getImg(subjectAnswer)"
										alt=""
										class="circle"
										srcset=""
										style="height: 40px; width: 40px"
									/>
									<div>
										<span>{{
											subjectAnswer.CreatedAt
												| date: ('forum.dateFormat' | translate)
										}}</span>
										<br />
										<span>{{ subjectAnswer.User.Firstname }}</span>
									</div>
								</div>
								<hr />

								<!--   list of subject-answers comment-->
								<div class="toast-body">
									<div
										*ngFor="let subjectAnswerComment of subjectAnswer.Comments"
									>
										<div style="font-size: 10px">
											<span style="font-size: 13px">
												{{ subjectAnswerComment.Body }}
											</span>
											|
											<span>
												<span *ngIf="subject?.Status == true">
													<a
														*ngIf="
															user &&
															(user.Id == subjectAnswerComment.User.Id ||
																user.Id == subject?.User.Id)
														"
														class="ml-2"
														(click)="removeComment(subjectAnswerComment)"
														><i class="fas fa-trash text-danger"></i
													></a>
													<a
														*ngIf="
															user && user.Id == subjectAnswerComment.User.Id
														"
														class="ml-2"
														routerLink="/forum/comment/edit/{{
															subject?.Slug
														}}/{{ subjectAnswerComment.Slug }}"
														><i class="fas fa-edit"> </i
													></a>
												</span>
											</span>
											|
											<a
												*ngIf="subject?.Status == true"
												(click)="showHide('c' + subjectAnswerComment.Id)"
												class="text-muted"
												><i class="fas fa-reply"></i
												>{{ 'forum.read.answer_comment' | translate }}</a
											>
											<div class="w-100 mb-2 d-flex justify-content-end">
												<img
													[src]="getImg(subjectAnswerComment)"
													alt=""
													class="circle"
													srcset=""
													style="height: 40px; width: 40px"
												/>
												<div>
													<span>{{
														subjectAnswerComment.CreatedAt
															| date: ('forum.dateFormat' | translate)
													}}</span>
													<br />
													<span>{{ subjectAnswerComment.User.Firstname }}</span>
												</div>
											</div>
											<div style="font-size: 9px">
												<!-- subject-answers comment form -->
												<form
													#f="ngForm"
													id="c{{ subjectAnswerComment.Id }}"
													(ngSubmit)="
														onSubmitComment(null, subjectAnswerComment)
													"
													class="d-none comment-form"
												>
													<textarea
														required
														rows="1"
														[(ngModel)]="commentBody"
														class="form-control"
														name="body"
													></textarea
													><br />
													<input
														[disabled]="f.invalid"
														class="btn btn-sm btn-danger"
														type="submit"
														value="{{ 'forum.read.comment' | translate }}"
													/>
												</form>
											</div>
										</div>

										<!--         list of subject-answer-comment answer -->
										<div
											*ngFor="let commentAnswer of subjectAnswerComment.Answers"
											style="padding-left: 3%; font-size: 8px"
										>
											<span style="font-size: 11px">
												{{ commentAnswer.Body }}
											</span>
											|
											<span>
												<span
													*ngIf="
														user &&
														user.Id == commentAnswer.User.Id &&
														subject?.Status == true
													"
												>
													<a
														*ngIf="
															user &&
															(user.Id == commentAnswer.User.Id ||
																user.Id == subject?.User.Id)
														"
														class="ml-2"
														(click)="removeComment(commentAnswer)"
														><i class="fas fa-trash text-danger"></i
													></a>
													<a
														*ngIf="user && user.Id == commentAnswer.User.Id"
														class="ml-2"
														routerLink="/forum/comment/edit/{{
															subject?.Slug
														}}/{{ commentAnswer.Slug }}"
														><i class="fas fa-edit"> </i
													></a>
												</span>
											</span>
											<div class="w-100 mb-2 d-flex justify-content-end">
												<img
													[src]="getImg(commentAnswer)"
													alt=""
													class="circle"
													srcset=""
													style="height: 40px; width: 40px"
												/>
												<div>
													<span>{{
														commentAnswer.CreatedAt
															| date: ('forum.dateFormat' | translate)
													}}</span>
													<br />
													<span>{{ commentAnswer.User.Firstname }}</span>
												</div>
											</div>
										</div>
										<!--    end  list of subject-answers comment -->
									</div>
									<!--  end  list of subject comment-->
									<hr *ngIf="subjectAnswer?.Comments?.length > 0" />
									<div style="font-size: 10px">
										<!--  subject-answers-comment answers form -->
										<a
											*ngIf="subject?.Status == true"
											(click)="showHide('a' + subjectAnswer.Id)"
											class="text-muted float-right"
											><i class="fas fa-reply"></i
											>{{ 'forum.read.add_comment' | translate }}</a
										>
										<form
											#f="ngForm"
											id="a{{ subjectAnswer.Id }}"
											(ngSubmit)="onSubmitComment(subjectAnswer)"
											class="d-none comment-form"
										>
											<textarea
												required
												rows="1"
												[(ngModel)]="commentBody"
												class="form-control"
												name="body"
											></textarea
											><br />
											<input
												[disabled]="f.invalid"
												class="btn btn-sm btn-danger float-right"
												type="submit"
												value="{{ 'forum.read.comment' | translate }}"
											/>
										</form>
									</div>
								</div>
							</div>
							<br />
							<br />
						</div>

						<!--  post answer form -->
						<div *ngIf="subject?.Status">
							<br />
							<br />
							<form
								#f="ngForm"
								(ngSubmit)="onSubmitAnswer()"
								class="form-group col"
								id="repQuestion"
							>
								<p>{{ 'forum.read.your_answer' | translate }}</p>
								<ckeditor
									class="form-control ckeditor"
									type="inline"
									name="body"
									id="ask-body"
									required
									[(ngModel)]="answerBody"
								></ckeditor>
								<!--angular-editor [config]="editorConfig" name="body" id="ask-body"
                                                required [placeholder]="'forum.read.enter_text' | translate "
                                                [(ngModel)]="answerBody"></angular-editor-->
								<br />
								<div class="text-center">
									<input
										[disabled]="f.invalid"
										class="btn btn-danger"
										id="btn"
										type="submit"
										value="{{ 'forum.read.post' | translate }}"
									/>
								</div>
							</form>
						</div>
					</div>
				</div>
			</div>
		</section>
		<button
			(click)="goToAsk()"
			mat-fab
			color="primary"
			class=""
			aria-label="Example icon button with a delete icon"
			style="position: fixed; bottom: 10vh; right: 15px; z-index: 99"
		>
			<mat-icon>chat_bubble_outline</mat-icon>
		</button>
	</main>
	<br />
	<br />
	<app-home-footer></app-home-footer>
</div>
