import { Component, OnInit } from '@angular/core';
import { User, UserRoleType } from '~/app/models/user.model';
import { ActivatedRoute, Router } from '@angular/router';
import { ForumSubject } from '~/app/models/forum-subject.model';
import { ForumComment } from '~/app/models/forum-comment.model';
import { ToastrService } from 'ngx-toastr';
import { AngularEditorConfig } from '@kolkov/angular-editor';
declare const $: any;
import { HomeLoginComponent } from '~/app/home/<USER>/home-login.component';
import { UtilsService } from '~/app/services/utils.service';
import { UserService } from '~/app/services/users/user.service';
import { ForumVote } from '~/app/models/forum-vote.model';
import { ConfirmationDialogService } from '~/app/services/confirmation-dialog.service';
import { TranslateService } from '@ngx-translate/core';
import { environment } from '~/environments/environment';
import { ForumSubjectService } from '~/app/services/forum/forum-subject.service';
import { ForumCommentService } from '~/app/services/forum/forum-comment.service';
import { ForumVoteService } from '~/app/services/forum/forum-vote.service';
import { AbonnementsService } from '~/app/services/abonnements/abonnements.service';

@Component({
	selector: 'app-forum-read',
	templateUrl: './forum-read.component.html',
	styleUrls: ['./forum-read.component.scss']
})
export class ForumReadComponent implements OnInit {
	subjectSlug: string;
	nbrAnswer = 0;
	userVote = null;
	user: User;
	subjectAnswers: ForumSubject[] = [];
	subject: ForumSubject;
	subjectAnswerTake = 10;
	subjectAnswerSkip = 0;
	answerBody: string;
	editorConfig: AngularEditorConfig = {
		editable: true,
		spellcheck: true,
		height: 'auto',
		minHeight: '150px',
		maxHeight: 'auto',
		width: 'auto',
		minWidth: '0',
		translate: 'yes',
		enableToolbar: true,
		showToolbar: true,
		placeholder: 'Enter text here...',
		defaultParagraphSeparator: '',
		defaultFontName: '',
		defaultFontSize: '',
		fonts: [
			{ class: 'arial', name: 'Arial' },
			{ class: 'times-new-roman', name: 'Times New Roman' },
			{ class: 'calibri', name: 'Calibri' },
			{ class: 'comic-sans-ms', name: 'Comic Sans MS' }
		],
		customClasses: [
			{
				name: 'quote',
				class: 'quote'
			},
			{
				name: 'redText',
				class: 'redText'
			},
			{
				name: 'titleText',
				class: 'titleText',
				tag: 'h1'
			}
		],
		uploadUrl: 'v1/image',
		uploadWithCredentials: false,
		sanitize: true,
		toolbarPosition: 'top',
		toolbarHiddenButtons: [['bold', 'italic'], ['fontSize']]
	};
	commentBody: string;

	constructor(
		private route: ActivatedRoute,
		private router: Router,
		private forumSubjectService: ForumSubjectService,
		private forumCommentService: ForumCommentService,
		private toastr: ToastrService,
		private utilsService: UtilsService,
		private userService: UserService,
		private voteService: ForumVoteService,
		private confirmationDialogService: ConfirmationDialogService,
		private translateService: TranslateService,
		private abonnementService: AbonnementsService
	) {}

	async ngOnInit(): Promise<void> {
		this.user = await this.userService.getUserConnected()?.toPromise();
		this.subjectSlug = this.route.snapshot.params.subjectSlug;
		this.subject = await this.forumSubjectService
			.getBySlug(this.subjectSlug)
			?.toPromise();
		console.log(this.subject);

		this.nbrAnswer = this.subject.Answers.length;
		// load first 10 answers
		await this.onLoadSubjectAnswers(false);

		if (this.user) {
			const abonnement = await this.abonnementService
				.getUserActiveAbonnement(this.user.Slug)
				.toPromise();
			if (abonnement?.Forfait?.Name.toLowerCase() === 'essential') {
				if (this.user.Role != UserRoleType.ADMIN) {
					this.router.navigateByUrl('/home/<USER>');
					this.toastr.warning(
						'Brain maker',
						'Veuillez chosoir un forfait adequat pour avoir accès au forum'
					);
				}
			}
		} else {
			this.utilsService.buildModal(HomeLoginComponent, () => {}, 500, true, {});
		}
	}

	async onLoadSubjectAnswers(onTake = true): Promise<void> {
		if (this.subject.Answers.length > this.subjectAnswerTake || !onTake) {
			if (onTake) {
				this.subjectAnswerTake += this.subjectAnswerTake;
			}
			this.subjectAnswers = await this.forumSubjectService
				.getAnswers(this.subjectSlug, this.subjectAnswerTake, 0)
				?.toPromise();
		}
	}

	async onDelete(subject: ForumSubject): Promise<void> {
		let onDelete = await this.confirmDelete();
		if (!onDelete) return;

		await this.forumSubjectService.delete(subject)?.toPromise();
		if (subject.Parent == null) {
			this.router.navigateByUrl('/forum');
		} else {
			// reload current answers
			this.onLoadSubjectAnswers(false);
		}
	}

	async removeComment(comment: ForumComment): Promise<void> {
		let onDelete = await this.confirmDelete();
		if (!onDelete) return;

		await this.forumCommentService.delete(comment)?.toPromise();
		this.ngOnInit();
	}

	onSubmitComment(subject: ForumSubject, parent: ForumComment = null): void {
		const comment: ForumComment = new ForumComment(0, '');
		comment.User = this.user;
		comment.Body = this.commentBody;
		comment.Subject = subject;
		comment.Parent = parent;

		this.forumCommentService.add(comment).subscribe(
			async (value) => {
				this.onLoadSubjectAnswers(false);
				const message = await this.translateService
					.get('forum.read.success_comment')
					?.toPromise();
				this.toastr.success(message, 'Brain-maker');
				// close comment form
				const form = $('.comment-form');
				form.each(function (value) {
					$(this).addClass('d-none');
				});
				// reload datas
				this.ngOnInit();
			},
			async (error) => {
				console.log(error);
				const message = await this.translateService
					.get('forum.an_error_occured')
					?.toPromise();
				this.toastr.error(message, 'Brain-maker');
			}
		);
	}

	async onSubmitAnswer(): Promise<void> {
		const answer: ForumSubject = new ForumSubject(0, '');
		answer.Body = this.answerBody;
		answer.User = this.user;
		answer.Parent = this.subject;

		if (!this.user) {
			const message = await this.translateService
				.get('forum.read.need_login')
				?.toPromise();
			this.toastr.info(message, 'Brain-maker');
			// show login modal
			this.utilsService.buildModal(HomeLoginComponent, () => {});
		} else {
			this.forumSubjectService.add(answer).subscribe(
				async (value) => {
					this.answerBody = '';
					this.nbrAnswer++;
					//reload current answers
					this.onLoadSubjectAnswers(false);
					const message = await this.translateService
						.get('forum.read.success_answer')
						?.toPromise();
					this.toastr.success(message, 'Brain-maker');
				},
				(error) => {
					console.log(error);
					this.toastr.error('An error occured', 'Brain-maker');
				}
			);
		}
	}

	async setAcceptedAnswer(answer: ForumSubject): Promise<void> {
		this.subject.AcceptedAnswer = answer;
		await this.forumSubjectService.edit(this.subject)?.toPromise();
		this.onLoadSubjectAnswers(false);
	}

	async deleteAcceptedAnswer(): Promise<void> {
		this.subject.AcceptedAnswer = null;
		await this.forumSubjectService.edit(this.subject)?.toPromise();
		this.onLoadSubjectAnswers(false);
	}

	async showHide(id): Promise<void> {
		this.commentBody = '';
		id = $('#' + id);
		const form = $('.comment-form');

		if (id.is(':hidden')) {
			form.each(function (value) {
				$(this).addClass('d-none');
			});
			this.user = await this.userService.getUserConnected()?.toPromise();
			if (!this.user) {
				const message = await this.translateService
					.get('forum.read.need_login')
					?.toPromise();
				this.toastr.info(message, 'Brain-maker');
				// show login modal
				this.utilsService.buildModal(HomeLoginComponent, () => {});
			} else id.removeClass('d-none');
		} else {
			form.each(function (value) {
				$(this).addClass('d-none');
			});
		}
	}

	getCurrentUserVote(votes: ForumVote[]): ForumVote {
		if (this.user) {
			return votes?.find((x) => x.User.Slug == this.user.Slug);
		}
		return null;
	}

	async loginToVote(): Promise<void> {
		const message = await this.translateService
			.get('forum.read.need_login')
			?.toPromise();
		this.toastr.info(message, 'Brain-maker');
		// show login modal
		this.utilsService.buildModal(HomeLoginComponent, () => {});
	}

	async saveVote(subject: ForumSubject, upDown: number): Promise<void> {
		if (!this.subject.Status) return;

		this.user = await this.userService.getUserConnected()?.toPromise();
		if (this.user) {
			const vote: ForumVote = new ForumVote(0, '');
			vote.Subject = subject;
			vote.User = this.user;
			vote.UpDown = upDown;
			await this.voteService.add(vote)?.toPromise();

			this.ngOnInit();
		} else {
			const message = await this.translateService
				.get('forum.read.need_login')
				?.toPromise();
			this.toastr.info(message, 'Brain-maker');
			// show login modal
			this.utilsService.buildModal(HomeLoginComponent, () => {});
		}
	}

	async editVote(vote: ForumVote, upDown: number): Promise<void> {
		if (!this.subject.Status) return;

		this.user = await this.userService.getUserConnected()?.toPromise();
		if (this.user) {
			vote.UpDown = upDown;
			await this.voteService.edit(vote)?.toPromise();

			this.ngOnInit();
		} else {
			const message = await this.translateService
				.get('forum.read.need_login')
				?.toPromise();
			this.toastr.info(message, 'Brain-maker');
			// show login modal
			this.utilsService.buildModal(HomeLoginComponent, () => {});
		}
	}

	getTotalVotes(votes: ForumVote[]): number {
		return votes?.length > 0
			? votes.map((item) => item.UpDown)?.reduce((prev, next) => prev + next)
			: 0;
	}

	async deleteVote(vote: ForumVote): Promise<void> {
		if (!this.subject.Status) return;

		this.user = await this.userService.getUserConnected()?.toPromise();
		if (this.user) {
			await this.voteService.delete(vote).toPromise();

			this.ngOnInit();
		} else {
			const message = await this.translateService
				.get('forum.read.need_login')
				.toPromise();
			this.toastr.info(message, 'Brain-maker');
			// show login modal
			this.utilsService.buildModal(HomeLoginComponent, () => {});
		}
	}

	async closeSubject(): Promise<void> {
		this.subject.Status = false;
		await this.forumSubjectService.edit(this.subject)?.toPromise();
		await this.onLoadSubjectAnswers(false);

		const message = await this.translateService
			.get('forum.success_close')
			?.toPromise();
		this.toastr.success(message, 'Brain-maker');
	}

	async confirmDelete(): Promise<boolean> {
		const title = await this.translateService.get('forum.confirm')?.toPromise();
		const message = await this.translateService
			.get('forum.do_you_want')
			?.toPromise();
		const okText = await this.translateService.get('forum.ok')?.toPromise();
		const cancelText = await this.translateService
			.get('forum.cancel')
			?.toPromise();
		return await this.confirmationDialogService
			.confirm(title, message, okText, cancelText)
			?.toPromise();
	}

	getImg(forum: ForumSubject | ForumComment): string {
		//console.log(forum);
		if (forum?.User.Photo) {
			return `${environment.path}/${forum.User.Photo.Hashname}`;
		}
		return '/assets/img/avatar.png';
	}
	async goToAsk(): Promise<void> {
		if (this.user) {
			this.router.navigateByUrl('/forum/ask');
		} else {
			const message = await this.translateService
				.get('forum.read.need_login')
				.toPromise();
			this.toastr.info(message, 'Brain-maker');
			// show login modal
			// this.utilsService.buildModal(HomeLoginComponent, () => { });
			this.utilsService.buildModal(HomeLoginComponent, () => {});
		}
	}
}
