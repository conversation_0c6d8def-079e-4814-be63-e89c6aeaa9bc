import { Component, OnInit } from '@angular/core';
import { ForumComment } from '~/app/models/forum-comment.model';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { User, UserRoleType } from '~/app/models/user.model';
import { UserService } from '~/app/services/users/user.service';
import { Location } from '@angular/common';
import { TranslateService } from '@ngx-translate/core';
import { UtilsService } from '../../services/utils.service';
import { HomeLoginComponent } from '../../home/<USER>/home-login.component';
import { ForumCommentService } from '~/app/services/forum/forum-comment.service';
import { AbonnementsService } from '~/app/services/abonnements/abonnements.service';

@Component({
	selector: 'app-forum-comment',
	templateUrl: './forum-comment.component.html',
	styleUrls: ['./forum-comment.component.scss']
})
export class ForumCommentComponent implements OnInit {
	forumComment: ForumComment;
	subjectSlug;
	commentSlug;
	user: User;
	constructor(
		private forumCommentService: ForumCommentService,
		private router: Router,
		private route: ActivatedRoute,
		private toastr: ToastrService,
		private userService: UserService,
		private translateService: TranslateService,
		private utilsService: UtilsService,
		private abonnementService: AbonnementsService
	) {}

	async ngOnInit(): Promise<void> {
		this.user = await this.userService.getUserConnected().toPromise();

		this.subjectSlug = this.route.snapshot.params.subjectSlug;
		this.commentSlug = this.route.snapshot.params.commentSlug;

		this.forumComment = await this.forumCommentService
			.getBySlug(this.commentSlug)
			.toPromise();

		if (this.user) {
			const abonnement = await this.abonnementService
				.getUserActiveAbonnement(this.user.Slug)
				.toPromise();
			if (abonnement?.Forfait?.Name.toLowerCase() === 'essential') {
				if (this.user.Role != UserRoleType.ADMIN) {
					this.router.navigateByUrl('/home/<USER>');
					this.toastr.warning(
						'Brain maker',
						'Veuillez chosoir un forfait adequat pour avoir accès au forum'
					);
				}
			}
		} else {
			this.utilsService.buildModal(HomeLoginComponent, () => {}, 500, true, {});
		}
	}

	onSubmit(): void {
		this.forumCommentService.edit(this.forumComment).subscribe(
			async (value) => {
				console.log(value);
				const message = await this.translateService
					.get('forum.success_update')
					.toPromise();
				this.toastr.success(message, 'Brain-maker');
				this.router.navigateByUrl(`/forum/read/${this.subjectSlug}`);
			},
			async (error) => {
				console.log(error);
				const message = await this.translateService
					.get('forum.an_error_occured')
					.toPromise();
				this.toastr.error(message, 'Brain-maker');
			}
		);
	}
	async goToAsk(): Promise<void> {
		if (this.user) {
			this.router.navigateByUrl('/forum/ask');
		} else {
			const message = await this.translateService
				.get('forum.read.need_login')
				.toPromise();
			this.toastr.info(message, 'Brain-maker');
			// show login modal
			// this.utilsService.buildModal(HomeLoginComponent, () => { });
			this.utilsService.buildModal(HomeLoginComponent, () => {});
		}
	}
}
