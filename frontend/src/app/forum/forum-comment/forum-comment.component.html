<div class="red-skin">
	<!-- BODY -->

	<div id="main-wrapper">
		<app-home-header></app-home-header>
		<!-- MAIN -->
		<section class="main">
			<div id="ask">
				<div class="bg-light text-center pb-2">
					<p style="font-size: 35px" class="bg-light text-center">
						{{ 'forum.comment.update' | translate }}
					</p>
					{{ 'forum.comment.fill_body' | translate }}
				</div>
				<br />
				<br />
				<br />

				<div class="container">
					<form
						class="form-group col"
						#f="ngForm"
						(ngSubmit)="onSubmit()"
						method="post"
					>
						<h4>{{ 'forum.comment.edit_comment' | translate }}</h4>
						<label for="comment">{{
							'forum.comment.comment_desc' | translate
						}}</label>
						<textarea
							*ngIf="forumComment"
							[(ngModel)]="forumComment.Body"
							id="comment"
							required
							class="form-control"
							rows="1"
							name="body"
							type="text"
						></textarea
						><br />
						<div class="text-center">
							<input
								[disabled]="f.invalid"
								class="btn btn-sm btn-primary"
								id="btn"
								type="submit"
								value="{{ 'forum.comment.edit_comment' | translate }}"
							/>
						</div>
					</form>
				</div>
			</div>
		</section>
		<!-- END OF MAIN -->
	</div>
	<!-- END OF BODY -->
	<br />
	<br />
	<app-home-footer></app-home-footer>
</div>

<!-- END OF MAIN -->
