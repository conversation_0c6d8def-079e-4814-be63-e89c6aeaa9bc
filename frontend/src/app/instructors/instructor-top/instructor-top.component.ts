import { OwlOptions } from 'ngx-owl-carousel-o';
import { AfterViewInit, Component, OnInit } from '@angular/core';
import { User } from '~/app/models/user.model';
import { CourseService } from '~/app/services/courses/course.service';
declare const $;

@Component({
	selector: 'app-instructor-top',
	templateUrl: './instructor-top.component.html',
	styleUrls: ['./instructor-top.component.scss']
})
export class InstructorTopComponent implements OnInit {
	responsiveOptions;
	instructors: Array<{
		NumberOfCourse: number;
		Instructor: User;
		NumberOfStudent: number;
	}>;

	customTeachersOptions: OwlOptions = {
		loop: true,
		mouseDrag: true,
		touchDrag: true,
		pullDrag: true,
		margin: 30,
		dots: false,
		navSpeed: 700,
		center: false,
		navText: ['', ''],
		responsive: {
			0: {
				items: 2
			},
			400: {
				items: 3
			},
			740: {
				items: 4
			},
			940: {
				items: 4
			}
		},
		nav: false
	};
	constructor(private courseService: CourseService) {}

	ngOnInit(): void {
		this.courseService.getAllInstructor().subscribe((response) => {
			if (response) {
				this.instructors = response;
				//console.log('intructeur', this.instructors);
			}
		});

		this.responsiveOptions = [
			{
				breakpoint: '1024px',
				numVisible: 3,
				numScroll: 3
			},
			{
				breakpoint: '768px',
				numVisible: 2,
				numScroll: 2
			},
			{
				breakpoint: '560px',
				numVisible: 1,
				numScroll: 1
			}
		];
	}
}
