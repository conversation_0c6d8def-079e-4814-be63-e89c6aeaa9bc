<!-- <section class="pt-0">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-5 col-md-6 col-sm-12">
                <div class="sec-heading center">
                    <p>{{'home.page.meetInstruct'|translate}}</p>
                    <h2>
                        <span class="theme-cl" style="text-transform: none;">{{'home.page.topNFamous'|translate}}</span> {{'home.page.insturctInCity'|translate}}
                    </h2>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <p-carousel [value]="instructors" styleClass="custom-carousel" [numVisible]="3" [numScroll]="1" [circular]="false" [autoplayInterval]="3000" [responsiveOptions]="responsiveOptions">
                    <ng-template let-instructor pTemplate="item">
                        <div class="product-item">
                            <app-instructor-single [item]="instructor"></app-instructor-single>
                        </div>
                    </ng-template>
                </p-carousel>
            </div>
        </div>
    </div>
</section> -->
<section class="teachers">
	<div class="container">
		<span class="sup-title">{{ 'home.page.meetInstruct' | translate }}</span>
		<h2>
			<span class="text-primary">{{ 'home.page.topNFamous' | translate }}</span>
			{{ 'home.page.insturctInCity' | translate }}
		</h2>
		<p class="desc">
			{{ 'home.page.insturctInCityDesc' | translate }}
		</p>

		<owl-carousel-o [options]="customTeachersOptions" #owlCar2>
			<ng-container *ngFor="let instructor of instructors">
				<ng-template carouselSlide [id]="instructor.id">
					<app-instructor-single [item]="instructor"></app-instructor-single>
				</ng-template>
			</ng-container>
		</owl-carousel-o>
		<p class="position-relative">
			<a class="nav-icon left text-primary" (click)="owlCar2.prev()">
				<span class="ti-angle-left"></span>
			</a>
			<a class="nav-icon right text-primary" (click)="owlCar2.next()">
				<span class="ti-angle-right"></span>
			</a>
		</p>
	</div>
</section>
