<!-- Single Slide -->
<!-- <div class="singles_items">
	<div class="instructor_wrap mid">
		<div class="instructor_thumb">
			<a [routerLink]="'/instructors/' + item.Instructor.Slug"
				><img [src]="profile" class="img-fluid" alt=""
			/></a>
		</div>
		<div class="instructor_caption">
			<h4>
				<a [routerLink]="'/instructors/' + item.Instructor.Slug">{{
					item.Instructor.Firstname
				}}</a>
			</h4>
			<span class="skills-min">{{ item.Instructor.Profil }}</span>
			<ul class="colored">
				<li *ngIf="item.Instructor.FacebookAddress">
					<a
						[href]="item.Instructor.FacebookAddress"
						target="_blank"
						class="cl-fb"
						><i class="ti-facebook"></i
					></a>
				</li>
				<li *ngIf="item.Instructor.GooleAddress">
					<a
						[href]="item.Instructor.GooleAddress"
						target="_blank"
						class="cl-google"
						><i class="ti-google"></i
					></a>
				</li>
				<li *ngIf="item.Instructor.TwitterAddress">
					<a
						[href]="item.Instructor.TwitterAddress"
						target="_blank"
						class="cl-twitter"
						><i class="ti-twitter"></i
					></a>
				</li>
			</ul>
		</div>

		<div class="cources_info_style3">
			<ul>
				<li>
					<i class="ti-user mr-2"></i>{{ item.NumberOfStudent }}
					{{ 'home.page.students' | translate }}
				</li>
				<li>
					<i class="ti-book mr-2"></i>{{ item.NumberOfCourse }}
					{{ 'home.page.courses' | translate }}
				</li>
			</ul>
		</div>
	</div>
</div> -->
<a [routerLink]="'/instructors/' + item.Instructor.Slug">
	<div class="teacher-item d-flex justify-content-center flex-column">
		<div class="teacher-img">
			<img [src]="profile" alt="" class="img-fluid" />
		</div>
		<div class="teacher-item-info">
			<h5>
				{{ item.Instructor.Lastname }} {{ item.Instructor.Firstname }}
				<span class="certified"><i class="ti-check"></i></span>
			</h5>
			<small>{{ item.Instructor.Profil }}</small>
			<p>
				<span class="mr-2 font-weight-bold text-primary">{{
					item.NumberOfCourse
				}}</span>
				Cours
			</p>
		</div>
	</div>
</a>
