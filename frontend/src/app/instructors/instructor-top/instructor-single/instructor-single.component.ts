import { Component, Input, OnInit } from '@angular/core';
import { User } from '~/app/models/user.model';
import { environment } from '~/environments/environment';

@Component({
	selector: 'app-instructor-single',
	templateUrl: './instructor-single.component.html',
	styleUrls: ['./instructor-single.component.scss']
})
export class InstructorSingleComponent implements OnInit {
	@Input()
	item: { NumberOfCourse: number; Instructor: User; NumberOfStudent: number };
	profile = '/assets/img/avatar.png';
	constructor() {}

	ngOnInit(): void {
		if (this.item.Instructor.Photo) {
			this.profile = `${environment.path}/${this.item.Instructor.Photo.Hashname}`;
		}
	}
}
