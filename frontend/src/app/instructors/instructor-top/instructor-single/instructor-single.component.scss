.instructor_wrap {
	height: 99% !important;
}

.singles_items {
	height: 320px !important;
}

.teacher-item {
	transition: 0.15s ease-out 100ms;
	cursor: pointer;
	&:hover {
		transform: translateY(-7px);
	}
	.teacher-img {
		position: relative;
		img {
		}
	}
	.teacher-item-info {
		padding: 1rem;
		background-color: #fff;
		border-radius: 0 0 5px 5px;
		.certified {
			position: relative;
			margin-left: 8px;
			i {
				color: #fff;
				background-color: green;
				width: 15px;
				height: 15px;
				border-radius: 15px;
				padding: 5px;
				font-weight: 800;
				font-size: 0.5rem;
				margin-left: 5px;
			}
		}
		h5 {
			margin-bottom: 0.2rem !important;
		}
		p {
			margin-bottom: 8px !important;
		}
	}
}
