import { ComponentFixture, TestBed } from '@angular/core/testing';

import { InstructorBestQuestionComponent } from './instructor-best-question.component';

describe('InstructorBestQuestionComponent', () => {
	let component: InstructorBestQuestionComponent;
	let fixture: ComponentFixture<InstructorBestQuestionComponent>;

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			declarations: [InstructorBestQuestionComponent]
		}).compileComponents();
	});

	beforeEach(() => {
		fixture = TestBed.createComponent(InstructorBestQuestionComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});
});
