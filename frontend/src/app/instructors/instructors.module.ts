import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { InstructorsRoutingModule } from './instructors-routing.module';
import { InstructorsComponent } from './instructors.component';
import { InstructorListComponent } from '~/app/instructors/instructor-list/instructor-list.component';
import { SharedModule } from '~/app/shared/shared.module';
import { FormsModule } from '@angular/forms';
import { InstructorResourcesComponent } from './instructor-resources/instructor-resources.component';
import { InstructorSidebarComponent } from './instructor-sidebar/instructor-sidebar.component';
import { InstructorBestQuestionComponent } from './instructor-best-question/instructor-best-question.component';
import { DropdownModule } from 'primeng/dropdown';
import { ProgressBarModule } from 'primeng/progressbar';
import { InputTextModule } from 'primeng/inputtext';
import { MessagesModule } from 'primeng/messages';
import { MessageModule } from 'primeng/message';
import { ChipModule } from 'primeng/chip';
import { InstructorDetailComponent } from './instructor-detail/instructor-detail.component';
import { MultiSelectModule } from 'primeng/multiselect';

@NgModule({
	declarations: [
		InstructorsComponent,
		InstructorListComponent,
		InstructorResourcesComponent,
		InstructorSidebarComponent,
		InstructorBestQuestionComponent,
		InstructorDetailComponent
	],
	imports: [
		CommonModule,
		InstructorsRoutingModule,
		SharedModule,
		FormsModule,
		DropdownModule,
		InputTextModule,
		ProgressBarModule,
		MessagesModule,
		MessageModule,
		ChipModule,
		MultiSelectModule
	]
})
export class InstructorsModule {}
