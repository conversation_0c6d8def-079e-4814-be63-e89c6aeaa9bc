import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewEncapsulation } from '@angular/core';
import { Observable, Subscription } from 'rxjs';
import { Course, CourseSearch, OrderbyType } from '~/app/models/course.model';
import { UserService } from '~/app/services/users/user.service';
import { Route, Router } from '@angular/router';
import { Media } from '~/app/models/media.model';
import { environment } from '~/environments/environment';
import { UtilsService } from '~/app/services/utils.service';
import { Message, MessageService } from 'primeng/api';
import { CourseService } from '../services/courses/course.service';
import { User } from '../models/user.model';
import { CategoryService } from '../services/categories/category.service';
import { Category } from '../models/category.model';
import { ToastrService } from 'ngx-toastr';

@Component({
	selector: 'app-instructors',
	templateUrl: './instructors.component.html',
	styleUrls: ['./instructors.component.scss'],
	encapsulation: ViewEncapsulation.None
})
export class InstructorsComponent implements OnInit, OnDestroy {
	user: User;
	cover = '/assets/img/default.png';
	courses: Array<Course>;
	connectedUserSubscription: Subscription;

	bestQuestions: {
		title: string;
		icon: string;
		description: string;
		link: string;
	}[];
	selectedFilter: string;
	filters: { id: number; name: string }[] = [
		{
			id: 1,
			name: 'Titre'
		},
		{
			id: 2,
			name: 'Description'
		},
		{
			id: 3,
			name: 'Price'
		}
	];

	totalRecord = 0;
	take: number;
	skip = 0;

	selectedCategory;
	categories: Category[] = [];
	search: CourseSearch = {
		Categories: [],
		Orderby: OrderbyType.RECENT,
		PriceType: [],
		Title: null,
		Type: [],
		CreatedBy: null
	};
	loadingSearch = false;
	fireSearch = false;

	progressValue = 34;

	messages: Message[] = [
		{
			severity: 'info',
			summary: '',
			detail:
				'Due to increased volume of new courses being submitted for review, the Quality Review Process may take up to 6 days. In order to avoid any additional delays, be sure that your course is in compliance with Udemy’s Course Materials Guidelines.'
		},
		{
			severity: 'info',
			summary: '',
			detail:
				'Stop Missing Instructor Emails!Update your communications settings to receive instructor emails focused on giving you all the tools you will need to be successful during course creation and beyond. Get started today !'
		},
		{
			severity: 'info',
			summary: '',
			detail:
				'Were updating the free course experience for students and instructors:New free courses (published after March 17, 2020) must have less than 2 hours of video content.Existing free courses (published before March 17, 2020) that contain more than 2 hours of video content will remain published.'
		}
	];
	constructor(
		private courseService: CourseService,
		private userService: UserService,
		private router: Router,
		private utilsService: UtilsService,
		private categoryService: CategoryService,
		private toastrService: ToastrService
	) {}

	async ngOnInit(): Promise<void> {
		this.take = CourseService.COURSE_INSTRUCTOR_TAKE;

		this.bestQuestions = [
			{
				title: 'Tutorials',
				icon: 'fas fa-chalkboard-teacher',
				description:
					'Le tutorial vidéo est un guide qui vous indique comment produire vos contenus de formations, les standard et les formats à respecter.',
				link: ''
			},

			{
				title: 'Guide utilisateurs',
				icon: 'fab fa-leanpub',
				description:
					"Il s'agit du support des formateurs qui vous donne des astuces et des conseils dans la production de vos contenus sur différents formats.",
				link: ''
			},
			{
				title: 'Support',
				icon: 'far fa-comments',
				description:
					"Les supports sont disponibles pendants les heures d'ouverture, pour vous assister et répondre à vos questions.",
				link: ''
			}
		];

		this.connectedUserSubscription = this.userService.connectedUserSubject.subscribe(
			async (connectedUser) => {
				this.user = connectedUser;
				if (!this.user) {
					this.router.navigate(['/']);
				} else {
					//console.log('Mon user sur===>', this.user);
					this.onSearch(this.take, this.skip);

					this.categories = await this.categoryService.getAll().toPromise();
				}
			}
		);
		this.userService.emitGetConnectedUser();
	}

	getResume(resume: string, lenght = 100): string {
		return this.utilsService.getHtml(resume, lenght);
	}

	getCover(cover: Media): string {
		return cover && cover.Hashname
			? `${environment.path}/${cover.Hashname}`
			: this.cover;
	}

	onKeyDown(event: KeyboardEvent): void {
		if (event.key === 'Enter') {
			this.skip = 0;
			this.onSearch(this.take, this.skip);
		}
	}

	onChangeCategories() {
		this.skip = 0;
		this.onSearch(this.take, this.skip);
	}

	async onSearch(take: number, skip: number): Promise<void> {
		this.fireSearch = true;
		this.search.CreatedBy = this.user.Slug;

		if (this.selectedCategory && this.selectedCategory.Id) {
			this.search.Categories[0] = this.selectedCategory.Id;
		} else {
			this.search.Categories = [];
		}

		console.log('search with =====> ', this.search);

		this.loadingSearch = true;

		try {
			// http req
			this.courseService
				.getByFilter({ take, skip }, this.search)
				.subscribe((res) => {
					console.log('the search result :', res);
					this.courses = res.Courses;
					this.totalRecord = res.Count;
					this.loadingSearch = false;
				});
		} catch (error) {
			this.loadingSearch = false;
			// Toast
			this.toastrService.error(
				'Erreur',
				'un problème est survenue lors de la recherche'
			);
		}
	}

	onLoadFromPage(pageNumber: number) {
		this.skip = pageNumber === 0 ? 0 : pageNumber * this.take;
		this.onSearch(this.take, this.skip);
	}

	ngOnDestroy(): void {
		this.connectedUserSubscription.unsubscribe();
	}
}
