import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { InstructorsComponent } from './instructors.component';
import { InstructorResourcesComponent } from './instructor-resources/instructor-resources.component';
import { InstructorDetailComponent } from '~/app/instructors/instructor-detail/instructor-detail.component';

const routes: Routes = [
	{ path: '', component: InstructorsComponent },
	{ path: 'resources/:page', component: InstructorResourcesComponent },
	{ path: ':id', component: InstructorDetailComponent }
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
	exports: [RouterModule]
})
export class InstructorsRoutingModule {}
