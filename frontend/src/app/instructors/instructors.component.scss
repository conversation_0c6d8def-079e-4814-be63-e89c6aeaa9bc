.svg_checkbox {
    height: 75px;
    width: 75px;
}

.svg_ressource {
    height: 175px;
    width: 100%;
}

.h-100 {
    height: 100% !important;
    min-height: 100px;
}

.icon_search {
    background: #A5DAE3;
    height: 40px !important;
    top: 8px !important;
    right: 0 !important;
    align-items: center;
    text-align: center;
    display: flex;
    width: 20%;
    justify-content: center;
    cursor: pointer;
    border-radius: 0 5px 5px 0;
}

.p-input-icon-right {}

.p-dropdown-label {
    display: flex !important;
    align-items: center;
}

.onging_course {
    height: 110px;
    width: 110px
}

.p-chips-token {
    background: #eeeeee !important;
    margin: 0.2em !important;
    font-size: 15px !important;
}

.p-chip .p-chip-text {
    line-height: 1 !important;
    margin-top: auto !important;
    margin-bottom: auto !important;
    font-size: 11px !important;
}

img.onging_course {
    width: 200px;
}

.p-inputtext {
    height: auto;
}

.search_input {
    width: 20vw;
    height: 40px !important;
}

.p-progressbar {
    height: 1rem;
}

.p-progressbar-label {
    line-height: 1rem !important;
    font-size: smaller;
}

@media (max-width: 767px) {
    .search_input {
        width: 100%;
    }
    .icon_search {
        height: 100%;
    }
}

.img_instruction {
    background-image: url("~src/assets/img/bannerformateurinstruction.png");
    background-repeat: no-repeat;
    width: 100%;
    //height: 491px;
    //background: black;
}

.position-text {
    margin-top: 2px;
}

.header1 {
    background: linear-gradient(rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.1)), url(~src/assets/img/bannerformateurinstruction2.png);
    background-size: cover;
    height: 50vh;
}

.title {
    // position: absolute;
    // top: 52%;
    // left: 60%;
    // transform: translate(-50%, -50%);
    color: white;
    text-align: center;
}