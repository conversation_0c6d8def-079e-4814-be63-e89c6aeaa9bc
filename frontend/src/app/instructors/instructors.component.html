<div class="red-skin">
	<div id="main-wrapper">
		<app-home-header></app-home-header>
		<section class="p-0">
			<div class="row">
				<div class="col">
					<div class="header1">
						<div class="row title-height-100">
							<div class="col-sm-12 col-lg-4"></div>
							<div class="col-lg-8 col-sm-12 title-height-100">
								<div
									class="title title-height-100 d-flex flex-column align-items-center justify-content-center"
								>
									<div class="h3 text-dark">
										{{ 'instructor.course.imgTitle' | translate }}
									</div>
									<p class="h4 text-white hide-on-med-and-down">
										{{ 'instructor.course.imgTitleDesc' | translate }}
									</p>
									<a class="btn btn-dark btn-md" routerLink="/courses/create">{{
										'instructor.course.createCourse' | translate
									}}</a>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>
		<div class="row">
			<div class="col-lg-1">
				<!--app-instructor-sidebar></app-instructor-sidebar-->
			</div>
			<div class="col-lg-10">
				<div class="row">
					<!--section class="col-sm-12 pb-4">
                        <p-messages [(value)]="messages" [enableService]="false"></p-messages>
                    </section-->
					<div class="col-12 row p-0 m-0">
						<div class="col-12 row p-0 m-0">
							<section class="col-sm-12 pt-2 pb-4">
								<div class="row">
									<div class="d-flex col-sm-12 col-lg-6">
										<span class="p-input-icon-right mr-3 mt-3 ml-3">
											<i
												(click)="onSearch(take, 0)"
												class="pi pi-search icon_search"
											></i>
											<input
												[(ngModel)]="search.Title"
												class="search_input"
												type="text"
												pInputText
												placeholder="{{
													'instructor.course.phSearch' | translate
												}}"
												(keydown)="onKeyDown($event)"
											/>
										</span>
										<span class="mr-3 mt-3 ml-3">
											<p-dropdown
												[options]="categories"
												[(ngModel)]="selectedCategory"
												placeholder="Categories"
												optionLabel="Title"
												[showClear]="true"
												class="mt-3"
												(onChange)="onChangeCategories()"
											>
											</p-dropdown>
										</span>
										<!-- <p-dropdown
											[options]="filters"
											[(ngModel)]="selectedFilter"
											placeholder="Filtres"
											optionLabel="name"
											[showClear]="true"
											class="mt-3"
										>
										</p-dropdown> -->
									</div>
									<div class="col-sm-12 col-lg-6">
										<button
											class="btn btn-primary mt-3 right btn-md mr-3"
											routerLink="/courses/create"
										>
											{{ 'instructor.course.createCourse' | translate }}
										</button>
									</div>
								</div>
							</section>
							<div class="col-12 mb-2">
								<span class="h6 text-dark ml-4"> </span>
							</div>

							<div *ngIf="loadingSearch" class="text-center w-100">
								<img src="assets/img/forum_loaging.gif" alt="" />
							</div>

							<!-- <div
								style="text-align: center; width: 100%; height: 400px"
								*ngIf="!loadingSearch && courses?.length === 0"
							>
								<h1>{{ 'no-course-found' | translate }}</h1>
							</div> -->

							<div
								class="d-flex listOfCourse justify-content-center align-items-center"
								style="text-align: center; width: 100%; height: 100px"
								*ngIf="!loadingSearch && courses?.length === 0 && fireSearch"
							>
								<div class="h1 font-bold">
									{{ 'course.details.noCourse' | translate }}
								</div>
							</div>

							<div *ngIf="!loadingSearch && courses?.length > 0">
								<div class="col-sm-12 mb-2 mt-0" *ngFor="let course of courses">
									<app-course-single [course]="course"></app-course-single>
									<!--div class="card d-flex flex-row justify-content-between align-items-center">
																	<div class="d-flex align-items-center">
																			<img [src]="getCover(course.CoverImage)" alt="" srcset="" class="onging_course img-fluid">
																			<div class="ml-2">
																					<a routerLink="/courses/manage/{{course.Slug}}" class="btn btn-link">
																							<b class="p-2">{{course.Title}}</b>
																					</a>
																					<div class="p-d-flex p-ai-center mt-2 small">
																							<p-chip label="{{keyword}}" [removable]="true" class="mr-2" *ngFor="let keyword of course.Keywords "></p-chip>
																					</div>
																			</div>
																	</div>
																	<div class="w-50 ">
																			<h5>{{'instructor.course.finishOngoingCourse'|translate}}</h5>
																			<p-progressBar [value]="progressValue"></p-progressBar>
																	</div>
															</div-->
								</div>
							</div>
						</div>

						<div
							class="col-lg-12 col-md-12 col-sm-12 text-center mb-5"
							*ngIf="totalRecord"
						>
							<p-paginator
								[rows]="take"
								[totalRecords]="totalRecord"
								(onPageChange)="onLoadFromPage($event.page)"
							></p-paginator>
						</div>

						<section
							class="col-sm-12"
							*ngIf="!courses || courses?.length == 0"
							style="padding: 10px 0 50px"
						>
							<div
								class="card d-flex flex-row justify-content-between align-items-center p-4"
							>
								<div
									class="d-flex flex-row justify-content-around align-items-center"
								>
									<img
										src="../../assets/img/checklist.svg"
										alt=""
										class="svg_checkbox"
									/>
									<h1 class="m-0 pl-4">
										{{ 'instructor.course.startCourseCreation' | translate }}
									</h1>
								</div>
								<div>
									<button routerLink="/courses/create" class="btn btn-primary">
										{{ 'instructor.course.createCourse' | translate }}
									</button>
								</div>
							</div>
						</section>

						<div class="col-12">
							<h2 class="text-center">
								{{ 'instructor.course.quoteCourse' | translate }}
							</h2>
						</div>

						<div class="col-sm-12">
							<div class="card p-4">
								<div class="row">
									<div class="col-md-5 text-center">
										<img
											src="../../assets/img/course.svg"
											alt=""
											class="svg_ressource"
										/>
									</div>
									<div class="col-md-7 text-left">
										<div
											class="d-flex flex-column justify-content-between h-100 p-4 align-items-center"
										>
											<h3 class="m-0 p-0 w-100 text-left">
												{{
													'instructor.course.createCoursePolicyTitle'
														| translate
												}}
											</h3>
											<p class="text-left">
												{{
													'instructor.course.createCoursePolicyDescription'
														| translate
												}}
											</p>
											<a
												href="/instructors/resources/COURSE"
												class="text-left w-100 text-decoration-none"
												>{{ 'instructor.course.easeStart' | translate }}</a
											>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="col-md-6">
							<div class="card p-4">
								<div class="row">
									<div class="col-md-5 text-center">
										<img
											src="../../assets/img/video.svg"
											alt=""
											class="svg_ressource"
										/>
									</div>
									<div class="col-md-7 text-left">
										<div
											class="d-flex flex-column justify-content-between h-100 p-4 align-items-center"
										>
											<h3 class="m-0 p-0 w-100 text-left">
												{{
													'instructor.course.videoRecordPolicyTitle' | translate
												}}
											</h3>
											<p class="text-left">
												{{
													'instructor.course.videoRecordPolicyDescription'
														| translate
												}}
											</p>
											<a
												href="/instructors/resources/VIDEO"
												class="text-left w-100 text-decoration-none"
												>{{ 'instructor.course.easeStart' | translate }}</a
											>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="col-md-6">
							<div class="card p-4">
								<div class="row">
									<div class="col-md-5 text-center">
										<img
											src="../../assets/img/chatting.svg"
											alt=""
											class="svg_ressource"
										/>
									</div>
									<div class="col-md-7 text-left">
										<div
											class="d-flex flex-column justify-content-between h-100 p-4 align-items-center"
										>
											<h3 class="m-0 p-0 w-100 text-left">
												{{ 'instructor.course.publicDevelopTitle' | translate }}
											</h3>
											<p class="text-left">
												{{
													'instructor.course.publicDevelopDescription'
														| translate
												}}
											</p>
											<a
												href="/instructors/resources/AUDIENCE"
												class="text-left w-100"
												>{{ 'instructor.course.easeStart' | translate }}</a
											>
										</div>
									</div>
								</div>
							</div>
						</div>
						<!-- <div class="col-sm-12">
                        <div class="card p-4">
                            <div class="row">
                                <div class="col-md-5 text-center">
                                    <img src="../../assets/img/course.svg" alt="" class="svg_ressource">

                                </div>
                                <div class="col-md-7 text-left">
                                    <div class="d-flex flex-column justify-content-between h-100 p-4 align-items-center">
                                        <h2 class="m-0 p-0 w-100 text-left">Commencer la création de cours</h2>
                                        <p class="text-left">Lorem ipsum dolor sit amet consectetur adipisicing elit. Sunt atque fugit odit eveniet, modi in quam, aut explicabo, dolore facilis tempore sit maiores at nam aliquid consequuntur minus ad expedita!
                                        </p>
                                        <a href="" class="text-left w-100">{{'instructor.course.easeStart'|translate}}</a>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div> -->
						<h2 class="text-center w-100 p-4">
							{{ 'instructor.course.quoteCourse2' | translate }}
						</h2>
					</div>
					<div class="row pb-4 mb-4 justify-content-center">
						<!-- <div class="col-sm-12 col-md-2 pointer d-flex flex-column align-items-center justify-content-between">
                      <i class="fas fa-ad fa-3x"></i>
                      <b class="pb-3 pt-2">Video test</b>
                      <small class="text-center">Lorem ipsum dolor sit amet consectetur adipisicing elit. Expedita enim dolore
                        quam, maxime odio deleniti.
                      </small>
                    </div> -->

						<app-instructor-best-question
							class="col-sm-12 col-md-2 pointer d-flex flex-column align-items-center justify-content-between"
							*ngFor="let question of bestQuestions"
							[icon]="question.icon"
							[title]="question.title"
							[description]="question.description"
							[link]="question.link"
						>
						</app-instructor-best-question>
					</div>
					<div
						class="d-flex flex-column justify-content-center align-items-center pb-4 mb-4 w-100"
					>
						<h2 class="text-center w-100 p-4">
							{{ 'instructor.course.readyToStart' | translate }}
						</h2>
						<button class="btn btn-primary btn-md" routerLink="/courses/create">
							{{ 'instructor.course.createCourse' | translate }}
						</button>
					</div>
				</div>
			</div>
		</div>
		<app-home-footer></app-home-footer>
	</div>
</div>
