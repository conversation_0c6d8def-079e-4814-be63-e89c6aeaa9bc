<div id="main-wrapper">
	<app-home-header></app-home-header>

	<!-- ============================ Instructor header Start================================== -->

	<div
		class="image-cover ed_detail_head invers"
		*ngIf="courses && instructor"
		style="background: #0b1c38"
		data-overlay="0"
	>
		<div class="container">
			<div class="row align-items-center">
				<div class="col-lg-12 col-md-12">
					<div class="viewer_detail_wraps">
						<div class="viewer_detail_thumb">
							<img [src]="profile" class="img-fluid" alt="" />
						</div>
						<div class="caption">
							<div class="viewer_header">
								<h4>{{ instructor.Firstname + ' ' + instructor.Lastname }}</h4>
								<span class="viewer_location">{{ instructor.Profil }}</span>
								<ul>
									<!-- <li><strong>112</strong> Points</li> -->
									<li>
										<strong>{{ courses.length }}</strong>
										{{ 'course.details.courses' | translate }}
									</li>
									<li>
										<strong>{{ countAllInstructorCourseReads }}</strong>
										{{ 'user.details.reads' | translate }}
									</li>
								</ul>
							</div>
							<div class="viewer_header">
								<ul class="badge_info">
									<li *ngIf="instructor.FacebookAddress">
										<a [href]="instructor.FacebookAddress" target="_blank"
											><i class="ti-facebook"></i
										></a>
									</li>
									<li class="medium" *ngIf="instructor.GooleAddress">
										<a [href]="instructor.GooleAddress" target="_blank"
											><i class="ti-google"></i
										></a>
									</li>
									<li class="platinum" *ngIf="instructor.TwitterAddress">
										<a [href]="instructor.TwitterAddress"
											><i class="ti-twitter"></i
										></a>
									</li>
									<li class="elite unlock" *ngIf="instructor.LinkedInAddress">
										<a [href]="instructor.TwitterAddress"
											><i class="ti-linkedin"></i
										></a>
									</li>
								</ul>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!-- ============================ Instructor header End ================================== -->

	<!-- ============================ Instructor Detail ================================== -->
	<section *ngIf="courses && instructor">
		<div class="container">
			<div class="row">
				<div class="col-lg-12 col-md-12 col-sm-12">
					<div class="custom-tab customize-tab tabs_creative">
						<ul class="nav nav-tabs pb-2 b-0" id="myTab" role="tablist">
							<li class="nav-item">
								<a
									class="nav-link active"
									id="home-tab"
									data-toggle="tab"
									href="#home"
									role="tab"
									aria-controls="home"
									aria-selected="true"
									>{{ 'user.details.myCourses' | translate }}
								</a>
							</li>
							<li class="nav-item">
								<a
									class="nav-link"
									id="profile-tab"
									data-toggle="tab"
									href="#profile"
									role="tab"
									aria-controls="profile"
									aria-selected="false"
									>{{ 'user.details.aboutMe' | translate }}</a
								>
							</li>
						</ul>
						<div class="tab-content" id="myTabContent">
							<!-- Classess -->
							<div
								class="tab-pane fade show active p-2"
								id="home"
								role="tabpanel"
								aria-labelledby="home-tab"
							>
								<div class="row">
									<!-- Single Video -->
									<div class="col-lg-4 col-md-6" *ngFor="let course of courses">
										<div class="edu-watching">
											<a [routerLink]="'/courses/' + course.Slug">
												<div class="property_video sm">
													<div class="thumb">
														<img
															class="pro_img img-fluid w100"
															[src]="getCover(course)"
															alt="instructor"
														/>
													</div>
													<div class="edu_duration">
														{{ formatCourseDuration(course) }}
													</div>
												</div>
												<div class="edu_video detail">
													<div class="edu_video_header">
														<h4>
															{{ course.Title }}
														</h4>
													</div>
													<div class="edu_video_bottom">
														<div class="edu_video_bottom_left">
															<i class="fas fa-clock mr-2"></i>
															<span>{{ course.CreatedAt | dateAgo }}</span>
														</div>
													</div>
												</div>
											</a>
										</div>
									</div>
								</div>
							</div>

							<!-- Education -->
							<div
								class="tab-pane fade"
								id="profile"
								role="tabpanel"
								aria-labelledby="profile-tab"
							>
								<div class="p-2">
									<h2>{{ 'user.details.aboutMe' | translate }}</h2>
									<p class="text-dark">{{ instructor.About }}</p>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>
	<!-- ============================ Instructor Detail ================================== -->

	<!-- ============================ Footer Start ================================== -->
	<app-home-footer></app-home-footer>
	<!-- ============================ Footer End ================================== -->
</div>
