import { TranslationService } from './../../services/translate/translation.service';
import { UtilsService } from './../../services/utils.service';
import { Component, OnInit } from '@angular/core';
import { Course } from '~/app/models/course.model';
import { ActivatedRoute } from '@angular/router';
import { User } from '~/app/models/user.model';
import { environment } from '~/environments/environment';
import { CourseService } from '~/app/services/courses/course.service';

@Component({
	selector: 'app-instructor-detail',
	templateUrl: './instructor-detail.component.html',
	styleUrls: ['./instructor-detail.component.scss']
})
export class InstructorDetailComponent implements OnInit {
	courses: Course[];
	instructor: User;
	profile = '/assets/img/avatar.png';
	cover = '/assets/img/700x500.png';
	countAllInstructorCourseReads = 0;

	constructor(
		private courseService: CourseService,
		private activatedRoute: ActivatedRoute,
		private utilService: UtilsService,
		private translationService: TranslationService
	) {}

	ngOnInit(): void {
		this.courseService
			.getCoursByInstructor(this.activatedRoute.snapshot.params.id)
			.subscribe(async (response) => {
				if (response) {
					this.courses = response;
					this.instructor = this.courses[0].CreatedBy;
					// console.log('Console est avec c majuscule', this.instructor);
					if (this.instructor.Photo) {
						this.profile = `${environment.path}/${this.instructor.Photo.Hashname}`;
					}

					const res = await this.courseService
						.countAllInstructorLeaners(this.instructor.Slug)
						.toPromise();
					this.countAllInstructorCourseReads = res.Learners;
					// console.log('Course reads: ', this.countAllInstructorCourseReads);
				}
			});
	}

	getCover(course: Course): string {
		if (course.CoverImage) {
			return `${environment.path}/${course.CoverImage.Hashname}`;
		}
		return this.cover;
	}

	formatCourseDuration(course) {
		// console.log('Browser lang', this.translateService.getBrowserLang());
		// console.log('Avant', this.course.Duration);
		// this.translateService.getBrowserLang()
		//  this.translateService.getBrowserLang()
		return this.utilService.getRithTime(
			course.Duration,
			this.translationService.getLanguage()
		);
		// console.log('Après', this.courseDuration);
		// console.log('Course', this.course);
	}
}
