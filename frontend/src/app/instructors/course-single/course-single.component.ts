import { Component, Input, OnInit } from '@angular/core';
import { Course, ContentType } from '~/app/models/course.model';
import { environment } from '~/environments/environment';
import { UtilsService } from '~/app/services/utils.service';

@Component({
	selector: 'app-course-single',
	templateUrl: './course-single.component.html',
	styleUrls: ['./course-single.component.scss']
})
export class CourseSingleComponent implements OnInit {
	cover = '/assets/img/default.png';
	@Input() course: Course;
	resume: string;
	data: any;
	displayModal = false;

	colorArray = [
		'#FF6633',
		'#FFB399',
		'#FF33FF',
		'#FFFF99',
		'#00B3E6',
		'#E6B333',
		'#3366E6',
		'#999966',
		'#99FF99',
		'#B34D4D',
		'#80B300',
		'#809900',
		'#E6B3B3',
		'#6680B3',
		'#66991A',
		'#FF99E6',
		'#CCFF1A',
		'#FF1A66',
		'#E6331A',
		'#33FFCC',
		'#66994D',
		'#B366CC',
		'#4D8000',
		'#B33300',
		'#CC80CC',
		'#66664D',
		'#991AFF',
		'#E666FF',
		'#4DB3FF',
		'#1AB399',
		'#E666B3',
		'#33991A',
		'#CC9999',
		'#B3B31A',
		'#00E680',
		'#4D8066',
		'#809980',
		'#E6FF80',
		'#1AFF33',
		'#999933',
		'#FF3380',
		'#CCCC00',
		'#66E64D',
		'#4D80CC',
		'#9900B3',
		'#E64D66',
		'#4DB380',
		'#FF4D4D',
		'#99E6E6',
		'#6666FF'
	];

	constructor(private utilsService: UtilsService) {}

	ngOnInit(): void {
		// tslint:disable-next-line:max-line-length
		//console.log("================>", this.course);
		this.cover =
			this.course.CoverImage && this.course.CoverImage.Hashname
				? `${environment.path}/${this.course.CoverImage.Hashname}`
				: this.cover;
		this.resume = this.utilsService.getHtml(this.course.Resume, 200);

		//const dataSections = this.course.Sections;

		// Let's group quiz and TP
		const quizGroup = { Title: 'Quiz', Contents: [] };
		const tpGroup = { Title: 'TP', Contents: [] };

		const sectionsQuiz = this.course.Sections.filter((section) => {
			for (const content of section.Contents) {
				if (content.Type == ContentType.QUIZ) {
					return true;
				}
			}
		});

		const sectionsTP = this.course.Sections.filter((section) => {
			for (const content of section.Contents) {
				if (content.Type == ContentType.ASSIGNMENT) {
					return true;
				}
			}
		});

		const sectionsWithoutQuizAndTP = this.course.Sections.filter((section) => {
			for (const content of section.Contents) {
				if (
					content.Type != ContentType.QUIZ &&
					content.Type != ContentType.ASSIGNMENT
				) {
					return true;
				}
			}
		});

		quizGroup.Contents = sectionsQuiz.reduce((acc, currentSection) => {
			return acc.concat(currentSection.Contents);
		}, []);

		tpGroup.Contents = sectionsTP.reduce((acc, currentSection) => {
			return acc.concat(currentSection.Contents);
		}, []);

		const dataSectionsFormated = [
			quizGroup,
			tpGroup,
			...sectionsWithoutQuizAndTP
		];

		const dataSectionsWithTime = dataSectionsFormated.map((section) => {
			const totalTimeOfContentsForSection = section.Contents.reduce(
				(acc, courseContent) => {
					return acc + courseContent.TotalTime;
				},
				0
			);
			return { ...section, totalTime: totalTimeOfContentsForSection };
		});

		const globalTime = dataSectionsWithTime.reduce((acc, courseSection) => {
			return acc + courseSection.totalTime;
		}, 0);

		this.data = {
			labels: dataSectionsWithTime.map(
				(section) =>
					`${section.Title} ( ${
						section.totalTime < 60
							? section.totalTime + 'Min'
							: (section.totalTime / 60).toFixed(2) + 'H'
					} ) `
			),
			datasets: [
				{
					//[300, 50, 100],dataSections
					data: dataSectionsWithTime.map((sectionAndTime) =>
						((sectionAndTime.totalTime * 100) / globalTime).toFixed(2)
					),

					backgroundColor: this.colorArray.slice(dataSectionsFormated.length),

					hoverBackgroundColor: this.colorArray.slice(
						dataSectionsFormated.length
					)
				}
			]
		};
	}
}
