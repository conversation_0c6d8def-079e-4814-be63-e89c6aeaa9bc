<div class="card w-100">
	<div class="card-body">
		<div class="row">
			<div class="col-sm-12 col-lg-3 mb-3 mb-md-0">
				<img [src]="cover" class="img-publish" alt="" />
			</div>
			<div class="col-sm-12 col-lg-9 d-flex flex-column justify-content-center">
				<div class="d-flex flex-column">
					<div class="h6 font-weight-bold">{{ course.Title }}</div>
					<div class="text-muted small">mard 11, 2020</div>

					<div class="">{{ resume }}</div>
					<!-- <div class="p-d-flex p-ai-center mt-1">
                    <p-chip label="{{keyword}}" [removable]="true" class="mr-2" *ngFor="let keyword of course.Keywords "></p-chip>
                    </div> <p-chart type="doughnut" [data]="data"></p-chart>-->
				</div>
				<div class="mt-2">
					<button
						class="btn btn-theme-2 btn-sm mr-2"
						[routerLink]="'/courses/manage/' + course.Slug"
					>
						{{ 'user.details.editManage' | translate }}
					</button>
					<button
						class="btn btn-primary btn-sm hide-on-small"
						(click)="displayModal = true"
					>
						{{ 'user.details.seeDist' | translate }}
					</button>
				</div>
			</div>
		</div>
	</div>
</div>
<p-dialog
	header="Repartition des cours sur la duree"
	[(visible)]="displayModal"
	[modal]="true"
	[style]="{ minWidth: '50vw' }"
	[baseZIndex]="10000"
	[draggable]="false"
	[resizable]="false"
>
	<p-chart type="doughnut" [data]="data"></p-chart>
	<!-- <p-chart type="doughnut" [data]="data" [width]="'200px'" [height]="'200px'"></p-chart> -->
	<ng-template pTemplate="footer">
		<p-button
			icon="pi pi-check"
			(click)="displayModal = false"
			label="Ok"
			styleClass="p-button-text"
		></p-button>
	</ng-template>
</p-dialog>
