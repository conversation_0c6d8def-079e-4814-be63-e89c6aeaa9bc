<div class="sidebar-wrapper back-primary d-sm-none d-md-none d-lg-block">
    <!-- <div class="nav flex-column nav-pills nav-stacked sidebar-content" id="v-pills-tab" role="tablist" aria-orientation="vertical" sidebar-content back-primary>
        <a class="nav-link p-2 d-flex align-center active mt-2 mb-0" id="v-pills-home-tab" data-toggle="pill" href="#v-pills-home" role="tab" aria-controls="v-pills-home" aria-selected="true">
            <i class="fas fa-anchor fa-2x"></i>
            <span class="sidebar-text">Cours</span>
        </a>
        <a class="nav-link p-2 d-flex align-center mt-2 mb-0" id="v-pills-profile-tab" data-toggle="pill" href="#v-pills-profile" role="tab" aria-controls="v-pills-profile" aria-selected="false">
            <i class="fas fa-anchor fa-2x"></i>
            <span class="sidebar-text">Communication</span>
        </a>
        <a class="nav-link p-2 d-flex align-center mt-2 mb-0" id="v-pills-messages-tab" data-toggle="pill" href="#v-pills-messages" role="tab" aria-controls="v-pills-messages" aria-selected="false">
            <i class="fas fa-anchor fa-2x"></i>
            <span class="sidebar-text">Performance</span>
        </a>
        <a class="p-2 d-flex align-center mt-2 bottom_arrow text-white" aria-selected="false">
            <i class="fas fa-arrow-right fa-2x"></i>
            <span class="sidebar-text"></span>
        </a>

    </div> -->
    <div class="d-user-avater ">
        <img [src]="profile" class="img-fluid avater" style="width:40%;
    border-radius: 50%" alt="" (click)="_fileInput.click()" />
        <h4 style="line-height: 13px">{{ user.Firstname + ' ' + user.Lastname }}</h4>
        <h5 style="line-height: 12px" class="text-white sidebar-text w-100 p-0
        ">Profil professionel</h5>
        <span style="line-height: 12px">{{ user.Country }}</span>
    </div>
    <input #_fileInput type="file" (change)="openCropperDialog($event)" accept="image/*" hidden>

    <div class="d-navigation">
        <ul id="side-menu">
            <li class="{{ tab == 'account' ? 'active' : '' }}">
                <a routerLink="/users/account"><i class="pi pi-user-edit mr-2 text-white"></i><span
            class="text-white sidebar-text"> Account</span></a>
            </li>
            <li class="{{ tab == 'profile' ? 'active' : '' }}">
                <a routerLink="/users"><i class="pi pi-user mr-2"></i> <span class="sidebar-text">My Profile</span> </a>
            </li>
            <!--li class="{{ tab == 'photo' ? 'active' : '' }}">
      <a routerLink="/users/user-profile"><i class="ti-heart"></i>Photo</a>
    </li-->
            <li class="{{ tab == 'coursesPublush' ? 'active' : '' }}">
                <a routerLink="/users/courses/publish"><i class="pi pi-clock mr-2"></i> <span class="sidebar-text">En
            cours</span> </a>
            </li>
            <li class="{{ tab == 'courseSave' ? 'active' : '' }}">
                <a routerLink="/users/courses/save"><i class="pi pi-bookmark mr-2"></i> <span class="sidebar-text">Saved Courses</span></a>

                <li class="{{tab == 'posts' ? 'active' : ''}}">
                    <a [routerLink]="'/posts/user/list'"><i class="ti-pencil-alt"></i> <span class="sidebar-text">Posts</span> </a>
                </li>


                <li>
                    <a href="saved-courses.html"><i class="ti-heart"></i> <span class="sidebar-text">Saved Courses</span> </a>
                </li>
                <!--li class="dropdown">
      <a href="all-courses.html"
        ><i class="ti-layers"></i>All Courses<span
          class="ti-angle-left"
        ></span
      ></a>
      <ul class="nav nav-second-level">
        <li><a href="all-courses.html">All</a></li>
        <li><a href="javascript:void(0);">Published</a></li>
        <li><a href="javascript:void(0);">Pending</a></li>
        <li><a href="javascript:void(0);">Expired</a></li>
        <li><a href="javascript:void(0);">In Draft</a></li>
      </ul>
    </li-->
                <li class="{{ tab == 'absFacture' ? 'active' : '' }}">
                    <a href="/users/courses/fatur"><i class="pi pi-id-card"></i> <span class="sidebar-text">Abonements & Factures</span> </a>
                </li>
                <li>
                    <a href="settings.html"><i class="ti-settings"></i><span class="sidebar-text">Settings</span></a>
                </li>
                <li>
                    <a href="reviews.html"><i class="ti-comment-alt"></i><span class="sidebar-text">Reviews</span></a>
                </li>
                <li class="{{ tab == 'close-account' ? 'active' : '' }}">
                    <a routerLink="/users/close-account"><i class="ti-trash"></i><span class="sidebar-text">Close account</span></a>
                </li>
        </ul>
    </div>
</div>
