import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { User } from '../../models/user.model';
import { Subject } from 'rxjs';
import { UserService } from '../../services/users/user.service';
import { environment } from '~/environments/environment';
import { LyDialog } from '@alyle/ui/dialog';
import { ToastrService } from 'ngx-toastr';
import { UserCropperComponent } from '../../user/user-cropper/user-cropper.component';
import { Media } from '~/app/models/media.model';

@Component({
	selector: 'app-instructor-sidebar',
	templateUrl: './instructor-sidebar.component.html',
	styleUrls: ['./instructor-sidebar.component.scss']
})
export class InstructorSidebarComponent implements OnInit {
	profile = '/assets/img/avatar.png';
	user: User;
	tab: string;
	emitUser: Subject<User> = new Subject<User>();
	constructor(
		private userService: UserService,
		private dialog: LyDialog,
		private toastr: ToastrService,
		private cd: ChangeDetectorRef
	) {}

	async ngOnInit(): Promise<void> {
		this.user = await this.userService.getUserConnected().toPromise();

		this.emitUser.next(this.user);

		if (this.user.Photo) {
			this.profile = this.user.Photo.Hashname = `${environment.path}/${this.user.Photo.Hashname}`;
		}
	}
	openCropperDialog(event: Event): void {
		// tslint:disable-next-line:no-non-null-assertion
		this.profile = null!;
		this.dialog
			.open<UserCropperComponent, Event>(UserCropperComponent, {
				data: event,
				width: 320,
				disableClose: true
			})
			.afterClosed.subscribe((result?: any) => {
				if (result) {
					this.profile = result.cropper.dataURL;
					this.cd.markForCheck();
					console.log(result);

					this.user.Photo = new Media(
						result.cropper.name,
						result.cropper.dataURL?.substr(
							result.cropper.dataURL?.indexOf(',') + 1
						),
						result.original.type,
						result.original.size,
						'123'
					);

					this.save();
				}
			});
	}
	save(): void {
		this.userService.edit(this.user).subscribe(
			(value) => {
				this.toastr.success('Profile updated !', 'Brain-maker');
			},
			(error) => {
				this.toastr.error('Fail to update profile !', 'Brain-maker');
			}
		);
	}
}
