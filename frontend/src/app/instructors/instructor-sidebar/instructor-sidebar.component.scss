.sidebar-wrapper {
    display: flex;
    position: fixed;
    width: 56px;
    height: 100%;
    z-index: 999;
    -webkit-transition: width .5s ease-in-out;
    -moz-transition: width .5s ease-in-out;
    -ms-transition: width .5s ease-in-out;
    -o-transition: width .5s ease-in-out;
    transition: width .5s ease-in-out;
}

.nav {
    width: 100%;
    overflow: hidden;
}

.sidebar-wrapper-absolute {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    z-index: 1030;
}

// .sidebar-wrapper:hover .sidebar-content {
//     // width: 230px !important;
// }
.sidebar-wrapper:hover {
    width: 230px;
    -webkit-transition: width .5s ease-in-out;
    -moz-transition: width .5s ease-in-out;
    -ms-transition: width .5s ease-in-out;
    -o-transition: width .5s ease-in-out;
    transition: width .5s ease-in-out;
}

.sidebar-text {
    transition: opacity .5s ease-in-out;
    display: inline-block;
    padding-left: 26px;
    opacity: 0;
}

.sidebar-wrapper:hover .sidebar-text {
    display: inline-block;
    padding-left: 26px;
    opacity: 1;
}

.nav-link.active {
    background-color: #da0b4e !important;
    color: white;
    border-left: 7px solid #433d3d;
    border-radius: 0;
}

.nav-link {
    color: white;
    background-color: #c2185b !important;
}

.nav-link:hover {
    background-color: #99415d !important;
    color: white;
    border-left: 7px solid #433d3d;
    border-radius: 0;
}

.bottom_arrow {
    position: absolute;
    bottom: 0;
    width: 100%;
    margin-bottom: 3em;
}

#side-menu li,
#side-menu .sidebar-text,
#side-menu i {
    color: white !important;
}

#side-menu li {
    height: 50px !important;
}

#side-menu li:hover {
    background-color: #da0b4e !important;
    color: white;
    border-left: 7px solid #433d3d;
    border-radius: 0;
}

#side-menu a {
    display: flex;
    align-items: flex-end;
    height: 100%;
}

.d-navigation ul li a {
    // padding-top: 5px;
}

@media (max-width: 767px) {
    .sidebar-wrapper {
        display: none;
    }
}
