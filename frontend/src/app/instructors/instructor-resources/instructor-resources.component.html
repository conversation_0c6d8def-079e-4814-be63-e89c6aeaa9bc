<div class="red-skin">
	<div id="main-wrapper">
		<app-home-header></app-home-header>
		<div class="row" *ngIf="userPolicy">
			<div class="col-lg-1">
				<!-- <app-instructor-sidebar></app-instructor-sidebar> -->
			</div>
			<div class="col-lg-10 mt-4">
				<div class="container">
					<form class="row mb-4">
						<section class="col-sm-12 text-center form-group">
							<div [ngSwitch]="userPolicy.PolicyType">
								<img
									*ngSwitchCase="1"
									src="../../../assets/img/course.svg"
									alt=""
									class="svg_ressource_details mb-4"
								/>
								<img
									*ngSwitchCase="2"
									src="../../../assets/img/video.svg"
									alt=""
									class="svg_ressource_details mb-4"
								/>
								<img
									*ngSwitchCase="3"
									src="../../../assets/img/chatting.svg"
									alt=""
									class="svg_ressource_details mb-4"
								/>
								<img
									*ngSwitchDefault
									src="../../../assets/img/course.svg"
									alt=""
									class="svg_ressource_details mb-4"
								/>
							</div>
							<h1 class="m-0 p-0 w-100 text-center">
								<!-- <a
									href=""
									class="btn btn-link"
									(click)="$event.preventDefault(); showInput = !showInput"
								>
									{{ userPolicy.Title }}
									<i class="fas fa-edit ml-2 pointer"></i>
								</a> -->
								{{ userPolicy.Title }}
							</h1>
							<h1 *ngIf="!userPolicy.Title">NOTHING SPECIFIED</h1>
						</section>

						<div class="col-12 form-group" *ngIf="!showInput">
							<div [innerHTML]="userPolicy.Content"></div>
						</div>

						<!-- <div class="col-12 form-group" *ngIf="showInput">
                            <label for="title"> {{'instructor.course.policyTitle'|translate}}</label>
                            <input type="text" [(ngModel)]="userPolicy.Title" id="title" required="" name="title" class="form-control">
                        </div>
                        <div class="col-sm-12 form-group" *ngIf="showInput">
                            <label for="content">{{'instructor.course.policyContent'|translate}}</label>
                            <angular-editor [(ngModel)]="userPolicy.Content" required="" name="content" id="content" [config]="resumeConfig">
                            </angular-editor>
                        </div>
                        <div class="col-sm-12 form-group text-right" *ngIf="showInput">
                            <button class="btn btn-lg btn-primary mt-2" type="submit">{{'instructor.course.saveBtn'|translate}}</button>
                        </div> -->
					</form>
				</div>
			</div>
		</div>
	</div>
	<app-home-footer></app-home-footer>
</div>
