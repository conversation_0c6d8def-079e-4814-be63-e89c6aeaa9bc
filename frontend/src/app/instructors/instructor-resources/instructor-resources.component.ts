import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { UtilsService } from '../../services/utils.service';
import { AngularEditorConfig } from '@kolkov/angular-editor';
import { PolicyType } from '~/app/models/use-policy.model';
import { UsePolicyService } from '~/app/services/use-policy.service';
import { UsePolicy } from '../../models/use-policy.model';

@Component({
  selector: 'app-instructor-resources',
  templateUrl: './instructor-resources.component.html',
  styleUrls: ['./instructor-resources.component.scss']
})
export class InstructorResourcesComponent implements OnInit {
  resumeConfig: AngularEditorConfig;
  course = '';
  showInput: boolean;
  policyType = 1;
  userPolicy: UsePolicy;
  constructor(
    private activatedRoute: ActivatedRoute,
    private utilsService: UtilsService,
    private usePolicyService: UsePolicyService
  ) { }

  async ngOnInit(): Promise<void> {
    const page = this.activatedRoute.snapshot.params.page;
    console.log(page);
    if (page) {
      if (PolicyType[page]) {
        this.policyType = +PolicyType[page];
        console.log(this.policyType);
      }
    }
    try {
      this.userPolicy = await this.usePolicyService
        .getByPolicyType(this.policyType)
        .toPromise();
    } catch (e) {
      console.log(e);
    }

    if (!this.userPolicy) {
      this.userPolicy = new UsePolicy(null, null, null, this.policyType);
    }

    this.showInput = !!!this.userPolicy.Title;
    console.log(this.userPolicy);
    this.resumeConfig = this.utilsService.getSimpleconfigAngular();
  }
  /* onSubmit(e): void {
    e.preventDefault();
    this.usePolicyService
      .update(this.userPolicy.Slug, this.userPolicy)
      .subscribe((data) => {
        console.log(data);
        this.showInput = !this.showInput;
      });
  } */
}
