// const cinetpay = require("cinetpay-nodejs");
import {
	AfterViewInit,
	Component,
	Input,
	OnDestroy,
	OnInit,
	ViewEncapsulation
} from '@angular/core';
import { HomeRegisterComponent } from '../home-register/home-register.component';
import { HomeLoginComponent } from '../home-login/home-login.component';

import { Router } from '@angular/router';
import { User } from '~/app/models/user.model';
import { UserService } from '~/app/services/users/user.service';
import { UtilsService } from '~/app/services/utils.service';
import { environment } from '~/environments/environment';
import { Course } from '~/app/models/course.model';
import { BRAINMAKERBASKET } from '~/app/models/order.model';
import { Subscription } from 'rxjs';
import { ThemeService } from 'ng-bootstrap-darkmode';
import { CourseService } from '~/app/services/courses/course.service';
import { OrderService } from '~/app/services/orders/order.service';
declare const { $ }: any;

@Component({
	selector: 'app-home-header',
	templateUrl: './home-header.component.html',
	styleUrls: ['./home-header.component.scss'],
	encapsulation: ViewEncapsulation.None
})
export class HomeHeaderComponent implements OnInit, AfterViewInit, OnDestroy {
	@Input() theclass = 'header-light'; //dark-text header-transparent change-logo
	user: User;
	visibleSidebar1 = false;
	courses: Array<Course> = [];
	total = 0;
	totalStr: string;
	onAddBasket = new Subscription();
	profile = '/assets/img/avatar.png';

	connectedUserSubscription: Subscription;
	searchInput = '';

	constructor(
		private router: Router,
		private userService: UserService,
		private courseService: CourseService,
		private orderService: OrderService,
		private utilsService: UtilsService,
		private themeService: ThemeService
	) {}

	async ngOnInit(): Promise<void> {
		this.themeService.theme$.subscribe((theme) =>
			console.log('current theme :', theme)
		);

		this.connectedUserSubscription = this.userService.connectedUserSubject.subscribe(
			async (connectedUser) => {
				this.user = connectedUser;

				if (this.user) {
					this.user = {
						...this.user,
						...this.userService.formatUserFields(this.user)
					};
				}
			}
		);
		this.userService.emitGetConnectedUser();

		// listener basket
		this.onAddBasket = this.orderService.subjectBasket.subscribe(
			async (res) => {
				if (res?.length) {
					this.courses = res;
					let total = 0;
					for (const course of res) {
						total += course.NewPrice ? course.NewPrice : course.Price;
					}
					this.total = total;
					this.totalStr = this.total.toFixed(2);
				}
			}
		);

		this.orderService.emitBasket();
		this.initializeBasket();
	}

	async initializeBasket() {
		const basket = localStorage.getItem(BRAINMAKERBASKET)
			? (JSON.parse(localStorage.getItem(BRAINMAKERBASKET)) as {
					Id: string;
					Type: string;
			  }[])
			: [];

		const tmpCourses: Course[] = [];
		for (const item of basket) {
			const course = await this.courseService.getBySlug(item.Id).toPromise();
			tmpCourses.push(course);
			this.orderService.addManyToBasket(tmpCourses);
		}
	}

	openLoginModal(e): void {
		this.visibleSidebar1 = false;
		e.preventDefault();
		const dialogRef = this.utilsService.buildModal(
			HomeLoginComponent,
			() => {}
		);
	}

	onSeach(e: KeyboardEvent): void {
		if (this.searchInput && e.keyCode === 13) {
			this.router.navigate(['/courses'], {
				queryParams: { q: this.searchInput }
			});
		}
	}

	submitSearch() {
		if (this.searchInput) {
			this.router.navigate(['/courses'], {
				queryParams: { q: this.searchInput }
			});
		}
	}

	openRegisterModal(e): void {
		this.visibleSidebar1 = false;
		e.preventDefault();
		const dialogRef = this.utilsService.buildModal(
			HomeRegisterComponent,
			() => {},
			500,
			true,
			{}
		);
	}

	async logout(e): Promise<void> {
		e.preventDefault();
		await this.userService.logout(this.user.Slug).toPromise();
		this.router.navigate(['/courses']);
	}

	ngAfterViewInit(): void {}

	ngOnDestroy(): void {
		this.onAddBasket && this.onAddBasket.unsubscribe();
		this.connectedUserSubscription &&
			this.connectedUserSubscription.unsubscribe();
	}
}
