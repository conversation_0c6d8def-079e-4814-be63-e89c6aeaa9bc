import { Component, Input, OnInit } from '@angular/core';
import { Category } from '~/app/models/category.model';
import { CategoryService } from '~/app/services/categories/category.service';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
	selector: 'app-home-sub-categories',
	templateUrl: './home-sub-categories.component.html',
	styleUrls: ['./home-sub-categories.component.scss']
})
export class HomeSubCategoriesComponent implements OnInit {
	@Input() category: Category;
	constructor(
		private activatedRoute: ActivatedRoute,
		private router: Router,
		private categoryService: CategoryService
	) {}

	async ngOnInit(): Promise<void> {
		if (this.activatedRoute.snapshot.params.id) {
			this.category = await this.categoryService
				.getBySlug(this.activatedRoute.snapshot.params.id)
				.toPromise();
		}

		if (!this.category) {
			this.router.navigate(['/']);
		}
	}

	onSearch(): void {}
}
