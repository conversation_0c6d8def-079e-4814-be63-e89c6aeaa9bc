<!-- ============================ Page Title Start================================== -->
<section class="page-title">
	<div class="container">
		<div class="row">
			<div class="col-lg-12 col-md-12">
				<div class="breadcrumbs-wrap mt-4">
					<h1 class="breadcrumb-title">
						{{ 'home.page.subCategOf' | translate }} {{ category?.Title }}
					</h1>
				</div>
			</div>
		</div>
	</div>
</section>
<!-- ============================ Page Title End ================================== -->

<!-- ========================== Articles Section =============================== -->
<section class="pt-0 main-content">
	<div class="container">
		<div class="row">
			<div class="post-search col-12">
				<div class="single_widgets widget_search no-card">
					<!-- <h4 class="title">{{ 'blog.showonepost.searchlabel' | translate }}</h4> -->

					<form (ngSubmit)="onSearch()" action="#" class="sidebar-search-form">
						<input
							type="search"
							id="search"
							name="search"
							placeholder="{{ 'blog.show-one-post.search-label' | translate }}"
						/>
						<button type="submit"><i class="ti-search"></i></button>
					</form>
				</div>
			</div>
		</div>
		<div
			class="row"
			*ngIf="
				category &&
				(!category.SubCategories || category.SubCategories.length === 0)
			"
		>
			<div class="col-12" style="height: 50vh">
				{{ 'home.page.noCategories' | translate }}
			</div>
		</div>
		<div
			class="row"
			*ngIf="
				category && category.SubCategories && category.SubCategories.length > 0
			"
		>
			<!-- Single Article -->
			<div
				class="col-lg-4 col-md-4 col-sm-6"
				*ngFor="let subCategory of category.SubCategories"
			>
				<app-home-sub-categories-single
					[subCategory]="subCategory"
				></app-home-sub-categories-single>
			</div>
		</div>
	</div>
</section>
<!-- ========================== Articles Section =============================== -->
