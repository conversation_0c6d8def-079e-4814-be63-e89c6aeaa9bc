import { Component, Input, OnInit } from '@angular/core';
import { SubCategory } from '~/app/models/sub-category.model';

@Component({
	selector: 'app-home-sub-categories-single',
	templateUrl: './home-sub-categories-single.component.html',
	styleUrls: ['./home-sub-categories-single.component.scss']
})
export class HomeSubCategoriesSingleComponent implements OnInit {
	@Input() subCategory: SubCategory;
	color: string;

	constructor() {}

	ngOnInit(): void {
		this.setRandomColor();
	}
	setRandomColor(): void {
		const items = ['cat-1', 'cat-2', 'cat-3', 'cat-4', 'cat-5'];
		this.color = items[Math.floor(Math.random() * items.length)][0];
		//console.log('color : ', this.color);
	}
}
