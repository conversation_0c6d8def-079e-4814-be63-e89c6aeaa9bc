<form autocomplete="off" (ngSubmit)="onSubmit($event)">
	<div class="" id="sign-up">
		<span class="mod-close" (click)="close($event)" aria-hidden="true"
			><i class="ti-close"></i
		></span>
		<div class="modal-body">
			<h4 class="modal-header-title">
				{{ 'home.login.title3' | translate }}
			</h4>
			<div class="row">
				<div *ngIf="msgError" class="form-group col-12">
					<p class="text-danger font-bold">{{ msgError }}</p>
				</div>

				<div class="form-group col-12">
					<label for="home-login-username">
						{{ 'home.login.username' | translate }}
					</label>
					<input
						type="text"
						[(ngModel)]="user.Username"
						name="home-login-username"
						(blur)="onItemChange()"
						[ngClass]="{ 'is-invalid': errors && isNotvalid('Username') }"
						id="home-login-username"
						class="form-control"
						required
					/>
					<div class="invalid-feedback">
						{{ 'language.required' | translate }}
					</div>
				</div>
				<div class="form-group col-12">
					<label for="home-login-password">
						{{ 'home.login.password' | translate }}
					</label>
					<input
						type="password"
						id="home-login-password"
						[(ngModel)]="user.Password"
						(blur)="onItemChange()"
						[ngClass]="{ 'is-invalid': errors && isNotvalid('Password') }"
						name="home-login-password"
						class="form-control"
						required
					/>
					<div class="invalid-feedback">
						{{ 'language.required' | translate }}
					</div>
				</div>

				<div
					class="form-group col-12 d-flex justify-content-between align-items-center"
				>
					<button type="submit" class="btn btn-dark" [disabled]="isLoading">
						<span *ngIf="isLoading" class="mr-2"
							><i class="fas fa-spin fa-spinner"></i
						></span>
						{{ 'home.login.btn.login' | translate }}
					</button>

					<a
						(click)="close($event)"
						routerLink="/home/<USER>"
						class="btn-link font-weight-bold ml-2"
					>
						{{ 'home.login.btn.forgotpassword' | translate }}
					</a>
				</div>
			</div>
			<div class="text-center"></div>
		</div>
	</div>

	<!--div container ly-dialog-actions class="d-flex justify-content-between px-5">
    <button type="submit" class="btn btn-dark">Sign Up</button>
    <p>
      Already Have An Account? <a href="#" class="link">Go For LogIn</a>
    </p>
  </div-->
</form>
