import { ComponentFixture, TestBed } from '@angular/core/testing';

import { HomeForgotPasswordComponent } from './home-forgot-password.component';

describe('HomeForgotPasswordComponent', () => {
	let component: HomeForgotPasswordComponent;
	let fixture: ComponentFixture<HomeForgotPasswordComponent>;

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			declarations: [HomeForgotPasswordComponent]
		}).compileComponents();
	});

	beforeEach(() => {
		fixture = TestBed.createComponent(HomeForgotPasswordComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});
});
