import { Component, Inject, Input, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
	selector: 'app-home-template-email',
	templateUrl: './home-template-email.component.html',
	styleUrls: ['./home-template-email.component.scss']
})
export class HomeTemplateEmailComponent implements OnInit {
	host;
	constructor(private route: ActivatedRoute, private router: Router) {}

	ngOnInit(): void {
		// this.host = this.route.sn
		this.host = window.location.origin;
	}
}
