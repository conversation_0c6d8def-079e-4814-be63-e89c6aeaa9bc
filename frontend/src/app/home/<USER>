<!-- ============================ Hero Banner  Start================================== -->
<!-- <div
	class="image-cover half_banner"
	style="background: #0b2248 url('/assets/img/home_banner.png') no-repeat"
>
	<div class="container">
		<div class="row">
			<div class="col-lg-12 col-md-12 col-sm-12">
				<div class="banner-search-2">
					<h1 class="cl_2 f_2 mb-0">
						{{ 'home.page.startLearning' | translate }}<br /><span
							class="theme-cl"
							>{{ 'home.page.newThings' | translate }}</span
						>
					</h1>
					<p>{{ 'home.page.studyTopic' | translate }}</p>
					<form class="mt-3 form-search">
						<input
							type="text"
							class="form-control input-search"
							required=""
							[(ngModel)]="searchInput"
							(keydown)="onSeach($event)"
							name="searchInput"
							id="searchInput"
							[placeholder]="'home.page.whatYouWantToLearn' | translate"
						/>
					</form>
				</div>
			</div>
		</div>
	</div>
</div> -->
<div class="bm-hero d-flex align-items-center">
	<div class="container">
		<div class="row align-items-center">
			<div class="col-12 col-md-6">
				<div class="hero-infos">
					<!-- bg-white rounded p-5 shadow -->
					<h1 class="hero-title">
						<span class="text-primary">{{
							'home.page.newThings' | translate
						}}</span>
					</h1>
					<h3>{{ 'home.page.startLearning' | translate }}</h3>
					<p class="pr-0 pr-md-4 desc">
						{{ 'home.page.studyTopic' | translate }}
					</p>
					<div class="d-flex">
						<a [routerLink]="'/courses'"
							><button class="btn btn-primary">
								{{ 'home.page.tryIt' | translate }}
							</button></a
						>
					</div>
				</div>
			</div>
			<div class="col-12 col-md-6"></div>
		</div>
	</div>
</div>
<!-- ============================ Hero Banner End ================================== -->

<!-- ============================ Trips Facts Start ================================== -->
<!-- <div class="trips_wrap full colored">
	<div class="container">
		<div class="row m-0">
			<div class="col-lg-4 col-md-4 col-sm-12">
				<div class="trips">
					<div class="trips_icons">
						<i class="ti-video-camera"></i>
					</div>
					<div class="trips_detail">
						<h4>
							{{ nomberOfCourse }}
							{{ 'home.page.onlineCoursesTitle' | translate }}
						</h4>
						<p>{{ 'home.page.onlineCoursesDesc' | translate }}</p>
					</div>
				</div>
			</div>

			<div class="col-lg-4 col-md-4 col-sm-12">
				<div class="trips">
					<div class="trips_icons">
						<i class="ti-medall"></i>
					</div>
					<div class="trips_detail">
						<h4>{{ 'home.page.expertInstructTitle' | translate }}</h4>
						<p>
							{{ 'home.page.expertInstructDesc' | translate }}
						</p>
					</div>
				</div>
			</div>

			<div class="col-lg-4 col-md-4 col-sm-12">
				<div class="trips none">
					<div class="trips_icons">
						<i class="ti-infinite"></i>
					</div>
					<div class="trips_detail">
						<h4>{{ 'home.page.lifetimeAccessTitle' | translate }}</h4>
						<p>{{ 'home.page.lifetimeAccessDesc' | translate }}</p>
					</div>
				</div>
			</div>
		</div>
	</div>
</div> -->
<!-- ============================ Trips Facts Start ================================== -->
<!-- STATS -->
<section class="stats">
	<div class="container">
		<div class="row">
			<div class="col-12 col-md-4 d-flex align-items-center item-stat">
				<div class="d-flex flex-column text-center">
					<i class="ti-video-camera"></i>
					<h4>{{ nomberOfCourse }} {{ 'home.page.stat1Title' | translate }}</h4>
					<p>{{ 'home.page.stat1Desc' | translate }}</p>
				</div>
			</div>
			<div class="col-12 col-md-4 d-flex align-items-center item-stat">
				<div class="d-flex flex-column text-center">
					<i class="ti-medall"></i>
					<h4>{{ 'home.page.stat2Title' | translate }}</h4>
					<p>{{ 'home.page.stat2Desc' | translate }}</p>
				</div>
			</div>
			<div class="col-12 col-md-4 d-flex align-items-center item-stat">
				<div class="d-flex flex-column text-center">
					<i class="ti-infinite"></i>
					<h4>{{ 'home.page.stat3Title' | translate }}</h4>
					<p>{{ 'home.page.stat3Desc' | translate }}</p>
				</div>
			</div>
		</div>
	</div>
</section>
<!-- END STATS -->

<!-- ========================== Featured Category Section =============================== -->
<app-home-categories-top></app-home-categories-top>
<!-- ========================== Featured Category Section =============================== -->

<!-- ============================ Featured Courses Start ================================== -->
<app-learning-top></app-learning-top>
<!-- ============================ Featured Courses End ================================== -->

<!-- ========================== About Facts List Section =============================== -->
<!-- <section>
	<div class="container">
		<div class="row align-items-center">
			<div class="col-lg-6 col-md-6 col-sm-12">
				<div class="about-short">
					<div class="sec-heading mb-3">
						<span class="h2 text-dark">
							{{ 'home.page.questionBecomeTrainer' | translate
							}}<span class="theme-cl font-weight-bold">Brain</span>
							<span class="font-weight-bold text-dark">-maker</span> ?
						</span>
					</div>
					<p class="h6">
						{{ 'home.page.becomeBrainMakerTxt1' | translate }}
					</p>
					<p class="h6">
						{{ 'home.page.becomeBrainMakerTxt2' | translate }}
					</p>
					<div class="cource_facts">
						<ul>
							<li>
								<span class="theme-cl">7m</span>
								{{ 'home.page.activeCoursesCount' | translate }}
							</li>
							<li>
								<span class="theme-cl">77k</span>
								{{ 'home.page.studentsLearningCount' | translate }}
							</li>
							<li>
								<span class="theme-cl">84+</span>
								{{ 'home.page.freeCourseCount' | translate }}
							</li>
						</ul>
					</div>
					<a routerLink="/instructors" class="btn btn-modern"
						>{{ 'home.page.becomeATrainer' | translate
						}}<span><i class="ti-arrow-right"></i></span
					></a>
				</div>
			</div>
			<div class="col-lg-6 col-md-6 col-sm-12">
				<div class="list_facts_wrap_img">
					<img src="assets/img/img-section2.png" class="img-fluid" alt="" />
				</div>
			</div>
		</div>
	</div>
</section> -->
<!-- ========================== About Facts List Section =============================== -->
<!-- INFOS -->
<section class="infos">
	<div class="container">
		<div class="row mt-4 align-items-center flex-column-reverse">
			<div class="col-12 col-md-6">
				<img src="assets/img/teacher.jpg" class="img-fluid" />
			</div>
			<div class="col-12 col-md-6 pl-3 pl-md-5 my-4 my-md-0">
				<h2>{{ 'home.page.whatsIsBrainMaker' | translate }}</h2>
				<p class="">
					{{ 'home.page.whatsIsBrainMakerTxt1' | translate }}
				</p>
				<p class="">
					{{ 'home.page.whatsIsBrainMakerTxt2' | translate }}
				</p>
				<button
					*ngIf="!user"
					class="btn btn-primary mt-3"
					(click)="openRegisterModal($event)"
				>
					Inscrivez-vous
				</button>
			</div>
		</div>
	</div>
</section>
<!-- END INFOS -->

<!-- INFOS -->
<section class="infos color">
	<div class="container">
		<div class="row mt-4 align-items-center">
			<div class="col-12 col-md-6 pr-3 pr-md-5 mb-4 mb-md-0">
				<h2>{{ 'home.page.questionBecomeTrainer' | translate }}</h2>
				<p class="">
					{{ 'home.page.becomeBrainMakerTxt1' | translate }}<br /><br />

					{{ 'home.page.becomeBrainMakerTxt2' | translate }}
				</p>
				<a routerLink="/instructors"
					><button class="btn btn-primary mt-3">
						{{ 'home.page.becomeATrainer' | translate }}
					</button></a
				>
			</div>
			<div class="col-12 col-md-6">
				<img src="assets/img/img-section3-min.png" class="img-fluid" />
			</div>
		</div>
	</div>
</section>
<!-- END INFOS -->

<!-- ============================ Featured Instructor Start ================================== -->
<app-instructor-top></app-instructor-top>
<!-- ============================ Featured Instructor End ================================== -->

<!-- INFOS -->
<section class="infos">
	<div class="container">
		<div class="row mt-4 align-items-center">
			<div class="col-12 col-md-6 mb-4 mb-md-0">
				<img src="assets/img/Diplome2.png" class="img-fluid" />
			</div>
			<div class="col-12 col-md-6 pl-3 pl-md-5 mb-4 mb-md-0">
				<h2>{{ 'home.page.personalizedRecommend' | translate }}</h2>
				<p class="">
					{{ 'home.page.personalizedRecommendTxt1' | translate }}
				</p>
				<p class="">
					{{ 'home.page.personalizedRecommendTxt2' | translate }}
				</p>
				<p class="">
					{{ 'home.page.personalizedRecommendTxt3' | translate }}
				</p>
				<button
					*ngIf="!user"
					class="btn btn-primary mt-3"
					(click)="openRegisterModal($event)"
				>
					Inscrivez-vous
				</button>
			</div>
		</div>
	</div>
</section>
<!-- END INFOS -->

<!-- ========================== Articles Section =============================== -->
<app-article-top></app-article-top>
<!-- ========================== Articles Section =============================== -->

<!-- ============================== Start Newsletter ================================== -->

<!-- <app-home-explain-diploma></app-home-explain-diploma>
 --><!-- ================================= End Newsletter =============================== -->
