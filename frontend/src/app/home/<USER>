import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { HomeRoutingModule } from './home-routing.module';
import { HomeNotFoundComponent } from '~/app/home/<USER>/home-not-found.component';
import { HomeConfirmEmailComponent } from '~/app/home/<USER>/home-confirm-email.component';
import { HomeTemplateEmailComponent } from '~/app/home/<USER>/home-template-email.component';
import { HomeProfileComponent } from '~/app/home/<USER>/home-profile.component';
import { HomeForgotPasswordComponent } from '~/app/home/<USER>/home-forgot-password.component';
import { PartnerComponent } from '~/app/home/<USER>/partner.component';
import { HomeContactComponent } from '~/app/home/<USER>/home-contact.component';
import { HomePricingComponent } from '~/app/home/<USER>/home-pricing.component';
import { SharedModule } from '~/app/shared/shared.module';
import { FormsModule } from '@angular/forms';
import { MultiSelectModule } from 'primeng/multiselect';
import { BadgeModule } from 'primeng/badge';
import { HomeExplainDiplomaComponent } from './home-explain-diploma/home-explain-diploma.component';
import { HomeComponent } from '~/app/home/<USER>';
import { HomeCategoriesComponent } from './home-categories/home-categories.component';
import { HomeCategoriesSingleComponent } from './home-categories/home-categories-single/home-categories-single.component';
import { HomeSubCategoriesComponent } from './home-categories/home-sub-categories/home-sub-categories.component';
import { HomeSubCategoriesSingleComponent } from './home-categories/home-sub-categories/home-sub-categories-single/home-sub-categories-single.component';
import { HomeCategoriesTopComponent } from './home-categories/home-categories-top/home-categories-top.component';
import { LearningTopComponent } from '~/app/courses/learning/learning-top/learning-top.component';
import { LearningTopSingleComponent } from '~/app/courses/learning/learning-top/learning-top-single/learning-top-single.component';
import { HomeAboutComponent } from './home-about/home-about.component';
import { HomeBasketComponent } from './home-basket/home-basket.component';
import { HomeAboutSocietyComponent } from './home-about/home-about-society/home-about-society.component';
import { HomeAboutPressComponent } from './home-about/home-about-press/home-about-press.component';
import { SidebarModule } from 'primeng/sidebar';
import { InstructorTopComponent } from '~/app/instructors/instructor-top/instructor-top.component';
import { InstructorSingleComponent } from '~/app/instructors/instructor-top/instructor-single/instructor-single.component';
import { CarouselModule as CarouselModulePrimeNG } from 'primeng/carousel';
import { CarouselModule } from 'ngx-owl-carousel-o';
import { HomeHowItWorkComponent } from './home-how-it-work/home-how-it-work.component';
import { HomeLayoutComponent } from './home-layout/home-layout.component';
import { HomeResetPasswordComponent } from './home-reset-password/home-reset-password.component';
import { HomePrivacyPolicyComponent } from './home-privacy-policy/home-privacy-policy.component';

@NgModule({
	declarations: [
		HomeComponent,
		HomeNotFoundComponent,
		HomeConfirmEmailComponent,
		HomeTemplateEmailComponent,
		HomeProfileComponent,
		HomeForgotPasswordComponent,
		PartnerComponent,
		HomeContactComponent,
		HomePricingComponent,
		HomeExplainDiplomaComponent,
		HomeCategoriesComponent,
		HomeCategoriesSingleComponent,
		HomeSubCategoriesComponent,
		HomeSubCategoriesSingleComponent,
		HomeCategoriesTopComponent,
		LearningTopComponent,
		LearningTopSingleComponent,
		HomeAboutComponent,
		HomeBasketComponent,
		HomeAboutSocietyComponent,
		HomeAboutPressComponent,

		InstructorTopComponent,
		InstructorSingleComponent,
		HomeHowItWorkComponent,
		HomeLayoutComponent,
		HomeResetPasswordComponent,
		HomePrivacyPolicyComponent
	],
	imports: [
		CommonModule,
		HomeRoutingModule,
		SharedModule,
		FormsModule,
		MultiSelectModule,
		BadgeModule,
		SidebarModule,
		CarouselModulePrimeNG,
		CarouselModule
	]
})
export class HomeModule {}
