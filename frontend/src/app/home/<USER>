@import '~src/_colors';

.image-cover {
	@media screen and (min-width: 992px) {
		/* margin-top: 5em !important; */
	}
}

.bm-hero {
	padding: 7rem 0 11rem;
	background: url(../../assets/img/home_banner1.jpg) center center;
	@media (max-width: 767.98px) {
		background: url(../../assets/img/home_banner_mobile.jpg) center bottom
			no-repeat;
		background-size: cover;
		padding: 4rem 0 24rem;
	}
	background-size: cover;
	position: relative;
	.hero-infos {
		z-index: 2;
		position: relative;
	}
	&::before {
		// content: "";
		position: absolute;
		display: block;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(#da0b4e, 0.31);
		z-index: 1;
	}
}

.stats {
	background-color: $blueGray;
	.container {
		// background-color: $primary;
		padding-top: 2rem;
		position: relative;
		z-index: 3;
		padding: 2rem;
		.item-stat {
			color: $secondary;
			i {
				font-size: 2rem;
				margin-right: 1rem;
				background-color: $primary;
				color: $white;
				width: 50px;
				height: 50px;
				border-radius: 50%;
				display: flex;
				justify-content: center;
				align-items: center;
				margin: 1rem auto;
			}
		}
	}
}

.infos {
	background-color: $white;
	padding: 4rem 0 0 0;
	&.color {
		background-color: $blueGray !important;
	}
}

.desc {
	width: 75%;
	font-size: 1rem;
	@media (max-width: 567.98px) {
		width: 100%;
	}
}

section {
	padding: auto !important;
}
