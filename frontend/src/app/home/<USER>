import { HomePrivacyPolicyComponent } from './home-privacy-policy/home-privacy-policy.component';
import { HomeResetPasswordComponent } from './home-reset-password/home-reset-password.component';
import { HomeLayoutComponent } from './home-layout/home-layout.component';
import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { HomeContactComponent } from '~/app/home/<USER>/home-contact.component';
import { HomePricingComponent } from '~/app/home/<USER>/home-pricing.component';
import { HomeConfirmEmailComponent } from '~/app/home/<USER>/home-confirm-email.component';
import { HomeProfileComponent } from '~/app/home/<USER>/home-profile.component';
import { HomeForgotPasswordComponent } from '~/app/home/<USER>/home-forgot-password.component';
import { HomeTemplateEmailComponent } from '~/app/home/<USER>/home-template-email.component';
import { HomeComponent } from '~/app/home/<USER>';
import { HomeAboutComponent } from '~/app/home/<USER>/home-about.component';
import { HomeAboutSocietyComponent } from './home-about/home-about-society/home-about-society.component';
import { HomeAboutPressComponent } from './home-about/home-about-press/home-about-press.component';
import { HomeBasketComponent } from './home-basket/home-basket.component';
import { HomeCategoriesComponent } from '~/app/home/<USER>/home-categories.component';
import { HomeSubCategoriesComponent } from '~/app/home/<USER>/home-sub-categories/home-sub-categories.component';
import { HomeHowItWorkComponent } from './home-how-it-work/home-how-it-work.component';

const routes: Routes = [
	{
		path: '',
		component: HomeLayoutComponent,
		children: [
			{ path: '', component: HomeComponent },
			{
				path: 'home',
				children: [
					{ path: 'contact', component: HomeContactComponent },
					{ path: 'pricing', component: HomePricingComponent },
					{ path: 'confirm-email/:slug', component: HomeConfirmEmailComponent },
					{
						path: 'confirm-email/:slug/:code',
						component: HomeConfirmEmailComponent
					},
					{ path: 'profile', component: HomeProfileComponent },
					{ path: 'profile/:slug', component: HomeProfileComponent },
					{ path: 'forgot-password', component: HomeForgotPasswordComponent },
					{
						path: 'reset-password/:slug',
						component: HomeResetPasswordComponent
					},
					{ path: 'template', component: HomeTemplateEmailComponent },
					{ path: 'forgot', component: HomeForgotPasswordComponent },
					{ path: 'about', component: HomeAboutComponent },
					{ path: 'panier', component: HomeBasketComponent },
					{ path: 'process', component: HomeHowItWorkComponent },
					{ path: 'about/society', component: HomeAboutSocietyComponent },
					{ path: 'about/presse', component: HomeAboutPressComponent },
					{
						path: 'categories',
						children: [
							{ path: '', component: HomeCategoriesComponent },
							{
								path: 'sub',
								children: [
									{ path: ':id', component: HomeSubCategoriesComponent }
								]
							}
						]
					},
					{ path: 'privacy-policy', component: HomePrivacyPolicyComponent }
				]
			}
		]
	}
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
	exports: [RouterModule]
})
export class HomeRoutingModule {}
