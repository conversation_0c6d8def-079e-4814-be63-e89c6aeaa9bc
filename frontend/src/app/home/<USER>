import { User } from './../models/user.model';
import { UserService } from './../services/users/user.service';
import { Subscription } from 'rxjs';
import { HomeRegisterComponent } from './home-register/home-register.component';
import { UtilsService } from './../services/utils.service';
import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CourseService } from '~/app/services/courses/course.service';

@Component({
	selector: 'app-home',
	templateUrl: './home.component.html',
	styleUrls: ['./home.component.scss']
})
export class HomeComponent implements OnInit {
	searchInput: string;
	nomberOfCourse = 0;
	user: User;

	connectedUserSubscription = new Subscription();
	constructor(
		private router: Router,
		private userService: UserService,
		private courseService: CourseService,
		private utilsService: UtilsService
	) {
		this.connectedUserSubscription = this.userService.connectedUserSubject.subscribe(
			async (connectedUser) => {
				this.user = connectedUser;

				if (this.user) {
					this.user = {
						...this.user,
						...this.userService.formatUserFields(this.user)
					};
				}
			}
		);
	}

	ngOnInit(): void {
		this.courseService.getCount().subscribe((res) => {
			if (res) {
				this.nomberOfCourse = res;
				console.log(res);
			}
		});
	}

	/* onSeach(e: KeyboardEvent): void {
		if (this.searchInput && e.keyCode === 13) {
			this.router.navigate(['/courses'], {
				queryParams: { q: this.searchInput }
			});
		}
	} */

	openRegisterModal(e): void {
		e.preventDefault();
		const dialogRef = this.utilsService.buildModal(
			HomeRegisterComponent,
			() => {},
			500,
			true,
			{}
		);
	}
}
