<div
	*ngIf="!user"
	class="w-50 mx-auto text-center align-content-center my-5"
	style="font-size: 14px"
>
	<br />
	<br />
	<div class="font-weight-bold" style="font-size: 2.5em">
		<span class="text-dark">Brain-</span><span class="text-danger">maker</span>
	</div>
	<br />
	<br />

	<h1 class="text-center font-weight-light" style="font-size: 3em">
		USER NOT-FOUND
	</h1>

	<a routerLink="/courses" class="mt-5 btn btn-dark"> GO TO COURSES </a>
</div>

<div
	*ngIf="user && (!user.Code || user.Code !== tokenCode)"
	class="w-50 mx-auto text-center align-content-center my-5"
	style="font-size: 14px"
>
	<br />
	<br />
	<div class="font-weight-bold" style="font-size: 2.5em">
		<span class="text-dark">Brain-</span><span class="text-danger">maker</span>
	</div>
	<br />
	<br />

	<h1 class="text-center font-weight-light" style="font-size: 3em">
		Link Expired
	</h1>

	<a routerLink="/courses" class="mt-5 btn btn-dark"> GO TO COURSES </a>
</div>

<form
	*ngIf="user && user.Code && tokenCode && user.Code == tokenCode"
	#f="ngForm"
	(ngSubmit)="onSubmit($event)"
	style="vertical-align: central"
	class="w-50 mx-auto align-content-center text-center my-5"
>
	<div class="font-weight-bold text-center" style="font-size: 2.5em">
		<span class="text-dark">Brain-</span><span class="text-danger">maker</span>
	</div>
	<br />
	<br />
	<p style="font-size: 24px">{{ 'user.misc.reinitPass' | translate }}</p>
	<br />

	<div class="row justify-content-center">
		<div class="form-group col-sm-10 col-lg-6 mb-2">
			<label>{{ 'user.info.newPass' | translate }}</label>
			<input
				type="password"
				name="np"
				[(ngModel)]="newpass"
				class="form-control"
			/>
		</div>
	</div>

	<div class="row justify-content-center">
		<div class="form-group col-sm-10 col-lg-6">
			<label>{{ 'user.info.confirmNewPass' | translate }}</label>
			<input
				type="password"
				class="form-control"
				name="cp"
				id="cp"
				[(ngModel)]="conpass"
			/>
		</div>
	</div>
	<br />

	<div>
		<br />
		<button
			[disabled]="f.invalid || isLoading"
			type="submit"
			class="btn btn-danger text-white"
		>
			<span class="mr-2" *ngIf="isLoading"
				><i class="fas fa-spin fa-spinner"></i
			></span>
			{{ 'user.misc.reinitPass' | translate }}
		</button>
		<br />
	</div>
</form>
