import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-home-about-society',
  templateUrl: './home-about-society.component.html',
  styleUrls: ['./home-about-society.component.scss'],
  encapsulation: ViewEncapsulation.None

})
export class HomeAboutSocietyComponent implements OnInit {

  constructor(private router: Router) { }

  ngOnInit(): void {
  }
  navigate(index): void {
    // this.router.navigate([link]);
    // console.log(link)
    switch (index) {
      case 0:
        this.router.navigate(['home/about']);
        break;
      case 1:
        this.router.navigate(['home/about/society']);
        break;
      case 2:
        this.router.navigate(['home/about/presse']);
        break;
      default:
        this.router.navigate(['home/about']);

        break;
    }
  }
}
