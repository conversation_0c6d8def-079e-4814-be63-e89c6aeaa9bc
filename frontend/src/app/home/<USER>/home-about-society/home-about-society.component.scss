.mat-tab-label-content {
	color: white !important;
}

.mat-tab-nav-bar,
.mat-tab-header {
	border-bottom: 0 !important;
}

.mat-ink-bar {
	width: 110px !important;
}

.mat-tab-label {
	width: 110px !important;
	min-width: 40px !important;
}

.menu-tab-about h2 {
	width: 70%;
	height: 40vh;
}

.menu-tab-about {
	color: white;
	width: 340px;
	background: #46494a45;
}

.title-contain {
	height: 100%;
}

.section-head-about {
	position: relative;
	background-size: cover;
	height: 60vh;
	background: url(~src/assets/img/service.png) no-repeat;
	background-position: center;
}

.section-about-precontainer {
	position: absolute;
	top: 0px;
	left: 0px;
	width: 100%;
	height: 100%;
	background-color: #46494a45;
}

@media only screen and (min-width: 601px) {
	.hide-on-med-and-up {
		display: none !important;
	}
}

@media only screen and (max-width: 992px) {
	.hide-on-med-and-down {
		display: none !important;
	}
	.ideal-card {
		height: 100%;
	}
	.menu-tab-about {
		width: 100% !important;
	}
}

@media only screen and (min-width: 993px) {
	.hide-on-large-only {
		display: none !important;
	}
}
.text-align-justify {
	text-align: justify;
	text-justify: inter-word;
	font-style: italic;
}
