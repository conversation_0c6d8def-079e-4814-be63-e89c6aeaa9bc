import { Component, Input, OnInit } from '@angular/core';
import { UtilsService } from '../../services/utils.service';
import { User } from '~/app/models/user.model';
import { UserService } from '~/app/services/users/user.service';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';

@Component({
	selector: 'app-home-confirm-email',
	templateUrl: './home-confirm-email.component.html',
	styleUrls: ['./home-confirm-email.component.scss']
})
export class HomeConfirmEmailComponent implements OnInit {
	slug: string;
	code = '';
	user: User;
	isLoading = false;
	isLoadingSendCode = false;

	constructor(
		private userService: UserService,
		private route: ActivatedRoute,
		private router: Router,
		private toastr: ToastrService
	) {}

	async ngOnInit(): Promise<void> {
		this.slug = this.route.snapshot.params.slug;
		this.code = this.route.snapshot.params.code || '';

		// this.user = await this.userService.getBySlug(this.slug).toPromise();
		if (!this.userService.isConnected()) {
			this.router.navigate(['/']);
		} else {
			this.user = await this.userService.getUserConnected().toPromise();
		}
		if (this.code) {
			if (this.user.Code) {
				this.confirmEmail();
			} else {
				//this.router.navigate(['/courses']);
			}
		}
	}

	confirmEmail(): void {
		this.isLoading = true;
		this.userService.confirmEmail({ ...this.user, Code: this.code }).subscribe(
			(value) => {
				this.isLoading = false;
				localStorage.setItem(this.userService.LOGINSTORAGE, this.user.Slug);
				if (this.user.Id) {
					this.router.navigate(['/home', 'profile', this.user.Slug]);
				} else {
					// this.router.navigate([`/users/account`]);
				}
			},
			(error) => {
				this.isLoading = false;
				console.log(error);
				this.toastr.error('Code incorrect', 'Brain-maker');
			}
		);
	}

	resendCode(): void {
		if (this.isLoadingSendCode || this.isLoading) return;

		this.isLoadingSendCode = true;
		this.userService.getBySlug(this.slug).subscribe(
			(user) => {
				user.Code = window.location.origin;
				this.userService.sendMail(user).subscribe(
					(value) => {
						this.isLoadingSendCode = false;
						this.toastr.success('code envoyé par mail', 'Brain-maker');
					},
					(error) => {
						this.isLoadingSendCode = false;
						console.log(error);
						this.toastr.error(
							'Error to send Code, Check your internet connection',
							'Brain-maker'
						);
					}
				);
			},
			(error) => {
				this.isLoadingSendCode = false;
				console.log(error);
				this.toastr.error(
					'Error to send Code, Check your internet connection',
					'Brain-maker'
				);
			}
		);
	}
}
