import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	Input,
	OnInit
} from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { validate, ValidationError } from 'class-validator';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { UtilsService } from '~/app/services/utils.service';
import { UserService } from '~/app/services/users/user.service';
import { User } from '~/app/models/user.model';
import { LyDialogRef } from '@alyle/ui/dialog';
import { SecurityService } from '~/app/services/security/security.service';
import { AuthService } from '~/app/services/auth/auth.service';
@Component({
	selector: 'app-home-register',
	templateUrl: './home-register.component.html',
	//changeDetection: ChangeDetectionStrategy.OnPush,
	styleUrls: ['./home-register.component.scss']
})
export class HomeRegisterComponent implements OnInit {
	errors: ValidationError[];
	errorsHelper: ValidationError[];
	isLoading: boolean;

	//phoneInput: any;
	inputTypePassword = 'password';

	set phoneInput(value) {
		console.log(value);
		this.user.Phone = value?.e164Number;
	}

	confirmPassword = '';

	user: User = new User(
		null,
		'123',
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		0,
		null,
		null,
		null,
		null,
		null,
		null,
		null
	);

	public value: string[];

	constructor(
		private toastr: ToastrService,
		public dialogRef: LyDialogRef,
		private userService: UserService,
		private utilsService: UtilsService,
		private securityService: SecurityService,
		private authService: AuthService,
		private router: Router,
		private translateService: TranslateService,
		private changeDetectorRef: ChangeDetectorRef
	) {}

	close(e): void {
		if (e) {
			e.preventDefault();
		}
		this.dialogRef.close();
	}

	ngOnInit(): void {}

	public modelChanged(event: string): void {
		console.log('model changed: ' + event);
	}

	changeInputPasswordType(e: InputEvent): void {
		e.preventDefault();
		this.inputTypePassword =
			this.inputTypePassword === 'password' ? 'text' : 'password';
	}

	async onSubmit(e): Promise<void> {
		e.preventDefault();
		if (this.user.Civility) {
			this.user.Civility = +this.user.Civility;
		}
		if (this.user.Lastname && this.user.Firstname) {
			this.user.Username = `de-${this.utilsService.getUniqueId(
				5,
				`
      ${this.user.Firstname.substr(
				0,
				1
			).toUpperCase()}${this.user.Lastname.substr(0, 1).toUpperCase()}`
			)}
      `;
		}

		this.errors = await validate(this.user);
		this.errorsHelper = this.errors;
		console.log('errors', this.errors);
		if (!this.errors || this.errors.length === 0) {
			if (this.user.Password !== this.confirmPassword) {
				return;
			}

			this.isLoading = true;
			try {
				this.user.Sex = this.user.Civility;
				this.user.Code = window.location.origin;
				const security = await this.securityService
					.create_user_and_token(this.user)
					.toPromise();
				if (security && security.Slug) {
					const message = await this.translateService
						.get('home.register.success')
						.toPromise();
					localStorage.setItem(
						this.userService.LOGINSTORAGE,
						security.User.Slug
					);
					this.authService.setToken(security.Token);
					this.router.navigate([`/home/<USER>/${security.User.Slug}`]);
					this.toastr.success(message, 'Brain-maker');
					this.close(null);
				} else {
					const message = await this.translateService
						.get('home.register.error')
						.toPromise();
					this.toastr.error(message, 'Brain-maker');
				}
			} catch (e) {
				console.log(e);
				const message = await this.translateService.get('error').toPromise();
				this.toastr.error(e.error.message, 'Brain-maker');
			} finally {
				this.isLoading = false;
				this.changeDetectorRef.detectChanges();
			}
		} else {
			const message = await this.translateService
				.get('language.message')
				.toPromise();
			this.toastr.error(message, 'Brain-maker');
		}
	}

	async onItemChange(): Promise<void> {
		this.errorsHelper = await validate(this.user);
	}

	isNotvalid(name: string): boolean {
		return this.utilsService.isNotvalid(name, this.errorsHelper);
	}

	getAllError(name: string): string[] {
		return this.utilsService.getAllError(name, this.errorsHelper);
	}
}
