import { Component, OnInit } from '@angular/core';
import { BRAINMAKERBASKET } from '~/app/models/order.model';
import { Course } from '~/app/models/course.model';
import { Router } from '@angular/router';
import { UserService } from '~/app/services/users/user.service';
import { UtilsService } from '~/app/services/utils.service';
import { environment } from '~/environments/environment';
import { CourseService } from '~/app/services/courses/course.service';
import { OrderService } from '~/app/services/orders/order.service';

@Component({
	selector: 'app-home-basket',
	templateUrl: './home-basket.component.html',
	styleUrls: ['./home-basket.component.scss']
})
export class HomeBasketComponent implements OnInit {
	total = 0;
	totalStr: string;
	basket: Array<{ Id: string; Type: string }> = [];
	courses: Array<Course> = [];
	constructor(
		private router: Router,
		private userService: UserService,
		private courseService: CourseService,
		private orderService: OrderService,
		private utilsService: UtilsService
	) {}

	async ngOnInit(): Promise<void> {
		const items = localStorage.getItem(BRAINMAKERBASKET);
		if (items) {
			this.basket = JSON.parse(items);
			for (const item of this.basket) {
				const course = await this.courseService.getBySlug(item.Id).toPromise();
				this.courses.push(course);
				this.total += course.NewPrice ? course.NewPrice : course.Price;
			}

			this.totalStr = this.total.toFixed(2);
		}
	}

	getImage(course: Course): string {
		if (course.CoverImage) {
			return `${environment.path}/${course.CoverImage.Hashname}`;
		}
		return '/assets/img/default.png';
	}
}
