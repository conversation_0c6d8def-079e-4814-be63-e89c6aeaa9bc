<div class="row">
	<div class="col-lg-12 col-sm-12 col-md-12">
		<div class="header1 mt-3">
			<div class="row title-height-100" style="background-color: #0000006b">
				<div class="col-sm-12 col-lg-4"></div>
				<div class="col-lg-8 col-sm-12 title-height-100">
					<div
						class="title p-2 title-height-100 d-flex flex-column align-items-center justify-content-center"
					>
						<div class="h2 text-white">
							{{ 'user.info.contactTitle' | translate }}
						</div>
						<p class="h5 text-white">
							{{ 'user.info.contactDesc' | translate }}
						</p>
						<!--a class="btn btn-dark btn-lg" routerLink="/courses/create">Commencer maintenant</a-->
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<br />
<!--div class="container">
      <div class="row">
        <div class="col-lg-12 col-md-12">
          <div class="d-flex justify-content-center  ">
            <div class="breadcrumb-title h1">Contact</div>
          </div>
          <div class="d-flex justify-content-center  ">
            <p class="w-75 my-2">
              Nous respectons la règlementation générale sur la protection des données.
              Les données que vous entrez dans ce formulaire ne serons en aucun cas cédée à aucun tier.
              Vos données reste votre propriété et vous pouvez les supprimer quand vous le voulez.
              <a href="">En savoir plus</a>
            </p>
          </div>
        </div>
      </div>
    </div-->
<!-- ============================ Page Title End ================================== -->

<!-- ============================ Agency List Start ================================== -->
<section>
	<div class="container">
		<!-- row Start -->
		<form method="post" class="row" (ngSubmit)="onSubmit($event)">
			<div class="col-lg-8 col-md-7">
				<div class="prc_wrap">
					<div class="prc_wrap_header">
						<h4 class="property_block_title">
							{{ 'user.details.fillForm' | translate }}
						</h4>
					</div>

					<div class="prc_wrap-body">
						<div class="row">
							<div class="col-lg-6 col-md-12">
								<div class="form-group">
									<label for="request-name">{{
										'user.details.name' | translate
									}}</label>
									<input
										type="text"
										[(ngModel)]="request.Name"
										name="request-name"
										(blur)="onItemChange()"
										[ngClass]="{
											'is-invalid': errors && isNotvalid('Name')
										}"
										id="request-name"
										class="form-control simple"
									/>
								</div>
							</div>
							<div class="col-lg-6 col-md-12">
								<div class="form-group">
									<label for="request-email">Email</label>
									<input
										type="email"
										[(ngModel)]="request.Email"
										name="request-email"
										(blur)="onItemChange()"
										[ngClass]="{
											'is-invalid': errors && isNotvalid('Email')
										}"
										id="request-email"
										class="form-control simple"
									/>
								</div>
							</div>
						</div>

						<div class="form-group">
							<label for="request-subject">{{
								'admin.misc.subject' | translate
							}}</label>
							<input
								type="text"
								[(ngModel)]="request.Subject"
								name="request-subject"
								(blur)="onItemChange()"
								[ngClass]="{
									'is-invalid': errors && isNotvalid('Subject')
								}"
								id="request-subject"
								class="form-control simple"
							/>
						</div>

						<div class="form-group">
							<label for="request-message">Message</label>
							<textarea
								[(ngModel)]="request.Message"
								name="request-message"
								(blur)="onItemChange()"
								[ngClass]="{
									'is-invalid': errors && isNotvalid('Message')
								}"
								id="request-message"
								class="form-control simple"
							></textarea>
						</div>

						<div class="form-group">
							<button
								class="btn btn-theme p-mr-2"
								type="submit"
								icon="pi pi-save"
								[disabled]="isLoading"
							>
								{{ 'blog.show-one-post.submit-comment' | translate }}
							</button>
							<div
								class="mt-2 d-flex align-items-center justify-content-center"
								*ngIf="isLoading"
							>
								<i class="fas fa-spin fa-spinner mr-2"></i>
								{{ 'home.loading' | translate }}
							</div>
						</div>
					</div>
				</div>
			</div>

			<div class="col-lg-4 col-md-5">
				<div class="prc_wrap">
					<div class="prc_wrap_header">
						<h4 class="property_block_title">
							{{ 'user.misc.ourOffices' | translate }}
						</h4>
					</div>

					<div class="prc_wrap-body">
						<div class="contact-info">
							<h2>{{ 'user.misc.contactUs' | translate }}</h2>
							<p>
								{{ 'user.misc.visitUs' | translate }}
							</p>

							<div class="cn-info-detail">
								<div class="cn-info-icon">
									<i class="ti-home"></i>
								</div>
								<div class="cn-info-content">
									<h4 class="cn-info-title">
										{{ 'user.misc.reachUs' | translate }}
									</h4>
									Te boelaarlei,<br />41 anvers,<br />2140 borgerhout
								</div>
							</div>

							<div class="cn-info-detail">
								<div class="cn-info-icon">
									<i class="ti-email"></i>
								</div>
								<div class="cn-info-content">
									<h4 class="cn-info-title">
										{{ 'user.misc.dropMail' | translate }}
									</h4>
									<EMAIL><br /><EMAIL>
								</div>
							</div>

							<div class="cn-info-detail">
								<div class="cn-info-icon">
									<i class="ti-mobile"></i>
								</div>
								<div class="cn-info-content">
									<h4 class="cn-info-title">
										{{ 'user.misc.appelezNous' | translate }}
									</h4>
									(+32) 465 80 60 45<br />+32 000 00 00 00
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</form>
		<!-- /row -->
	</div>
</section>
<!-- ============================ Agency List End ================================== -->
