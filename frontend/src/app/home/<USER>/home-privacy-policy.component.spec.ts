import { ComponentFixture, TestBed } from '@angular/core/testing';

import { HomePrivacyPolicyComponent } from './home-privacy-policy.component';

describe('HomePrivacyPolicyComponent', () => {
  let component: HomePrivacyPolicyComponent;
  let fixture: ComponentFixture<HomePrivacyPolicyComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ HomePrivacyPolicyComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(HomePrivacyPolicyComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
