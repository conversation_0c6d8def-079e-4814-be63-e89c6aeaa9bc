import { Component, OnInit } from '@angular/core';
import {Router} from '@angular/router';

@Component({
  selector: 'app-home-new-letter',
  templateUrl: './home-new-letter.component.html',
  styleUrls: ['./home-new-letter.component.scss']
})
export class HomeNewLetterComponent implements OnInit {
  searchInput: string;
  constructor(private router: Router) {}

  ngOnInit(): void {}

  onSeach(e: KeyboardEvent): void {
    if (this.searchInput && e.keyCode === 13) {
      this.router.navigate(['/courses'], {
        queryParams: { q: this.searchInput }
      });
    }
  }
}
