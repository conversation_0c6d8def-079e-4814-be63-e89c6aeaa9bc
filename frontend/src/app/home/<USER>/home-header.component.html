<div class="header change-logo" [ngClass]="theclass">
	<div class="container-fluid">
		<nav id="navigation" class="navigation navigation-landscape">
			<div class="nav-header d-flex align-items-center">
				<a class="nav-brand static-logo" routerLink="/">
					<img src="assets/img/brain-maker-logo.png" />
				</a>
				<a class="nav-brand fixed-logo" routerLink="/">
					<img src="assets/img/brain-maker-logo.png" />
				</a>
				<div class="nav-toggle"></div>
				<form class="form-inline d-flex align-items-center bm-search">
					<input
						type="text"
						class="form-control bm-search"
						required=""
						[(ngModel)]="searchInput"
						(keydown)="onSeach($event)"
						name="searchInput"
						id="searchInput"
						[placeholder]="'home.page.whatYouWantToLearn' | translate"
					/>
					<button
						class="btn btn-primary d-flex justify-content-center align-items-center"
						style="height: 100%"
						(click)="submitSearch()"
					>
						<span class="ti-search"></span>
					</button>
				</form>
			</div>
			<div class="nav-menus-wrapper" style="transition-property: none">
				<ul class="nav-menu"></ul>
				<button
					pButton
					pRipple
					type="button"
					icon="pi pi-bars"
					(click)="visibleSidebar1 = true"
					class="hide-on-large-only p-button-outlined right mt-3 border-0 text-dark"
				></button>

				<ul
					class="nav-menu nav-menu-social hide-on-med-and-down align-to-right"
				>
					<li class="font-weight-bold" *ngIf="user">
						<a routerLink="/instructors">{{
							'menu.createCourse' | translate
						}}</a>
					</li>
					<li class="font-weight-bold">
						<a routerLink="/courses">{{ 'menu.takeCourse' | translate }}</a>
					</li>
					<li class="" *ngIf="user">
						<div
							class="ps_popover__wrapper"
							*ngIf="courses && courses.length > 0"
						>
							<i
								class="pi pi-shopping-cart p-mr-4 p-text-secondary mt-4 ml-3 mr-3 pointer"
								pBadge
								style="font-size: 1.7rem"
								[value]="courses.length.toString()"
							></i>

							<div class="ps_popover__content p-0">
								<div class="d-flex pl-3 pt-3 pr-3 pb-1">
									<span
										><b>{{ 'menu.card' | translate }}</b></span
									>
								</div>
								<app-pay-shopping-basket
									[course]="item"
									*ngFor="let item of courses"
								>
								</app-pay-shopping-basket>

								<div class="p-3">
									<h3 class="font-bold mb-1 text-left">
										TOTAL: <span class="text-primary">{{ totalStr }} CA$</span>
									</h3>
									<p-button
										label="Acceder au panier"
										routerLink="/pay"
										styleClass="p-button-danger w-100"
									></p-button>
								</div>
							</div>
						</div>
					</li>

					<li class="mt-3 ml-3 pointer d-flex" *ngIf="user">
						<div
							class="rounded-pill rounded_user_block p-1"
							routerLink="/users/account"
						>
							<img
								[src]="user.photoFormated"
								class="ps_popover__title p-0 img-user-popover"
								alt=""
								srcset=""
							/>
							<span>{{ user.Firstname }}</span>
						</div>
						<div class="ps_popover__wrapper">
							<div class="image-caret-container d-flex align-items-center">
								<button
									pButton
									pRipple
									type="button"
									icon="pi pi-sort-down"
									class="p-button-rounded p-button-secondary ml-3"
								></button>
							</div>

							<div class="ps_popover__content content_user_info">
								<div class="d-flex justify-content-start mb-1">
									<img
										[src]="this.user.photoFormated"
										class="p-0"
										style="height: 60px; width: 60px; border-radius: 50%"
										alt=""
										srcset=""
									/>
									<div
										class="mb-1 left d-flex ml-3 flex-column justify-content-start align-items-start"
									>
										<p>
											<b>{{ user.Firstname }}</b>
										</p>
										<p>
											<small>{{ user.Username }}</small>
										</p>
									</div>
								</div>
								<div class="d-flex notif_block pointer">
									<ngbd-theme-switch [size]="'sm'"></ngbd-theme-switch>
								</div>
								<div
									class="d-flex notif_block pointer"
									routerLink="/users/account"
								>
									<a>{{ 'menu.myAccount' | translate }}</a>
								</div>
								<!-- <div class="d-flex notif_block pointer" (click)="logout($event)">
                                    <a href="">{{'menu.logout'|translate}}</a>
                                </div> -->
								<div class="d-flex notif_block pointer" routerLink="/meetings">
									<a>{{ 'menu.meetings' | translate }}</a>
								</div>
								<div class="d-flex notif_block pointer" routerLink="/forum">
									<a>Forum</a>
								</div>
								<div class="d-flex notif_block pointer" routerLink="/posts">
									<a>Blog</a>
								</div>
								<div
									class="d-flex notif_block pointer"
									routerLink="/home/<USER>"
								>
									<a>{{ 'home.page.about' | translate }}</a>
								</div>
								<div
									class="d-flex notif_block pointer"
									routerLink="/home/<USER>"
								>
									<a>Contact</a>
								</div>
								<div
									class="d-flex notif_block pointer"
									routerLink="/home/<USER>"
								>
									<a>{{ 'menu.pricing' | translate }}</a>
								</div>
								<div
									class="d-flex notif_block pointer"
									routerLink="/home/<USER>"
								>
									<a>{{ 'menu.process' | translate }}</a>
								</div>
								<div
									class="d-flex notif_block pointer"
									(click)="logout($event)"
								>
									<a href="">{{ 'menu.logout' | translate }}</a>
								</div>
							</div>
						</div>
					</li>

					<li class="login_click bg-red" *ngIf="!user">
						<a href="/" (click)="openLoginModal($event)">{{
							'home.login.title3' | translate
						}}</a>
					</li>
					<li class="login_click" *ngIf="!user">
						<a href="/" (click)="openRegisterModal($event)">{{
							'home.register.title1' | translate
						}}</a>
					</li>
				</ul>
			</div>
		</nav>
	</div>
</div>
<p-sidebar
	[(visible)]="visibleSidebar1"
	[baseZIndex]="10000"
	class="pl-0 pr-0 p-sidebar-home-header"
>
	<div class="d-flex justify-content-start mb-1" *ngIf="user">
		<img
			[src]="user.photoFormated"
			class="p-0"
			style="height: 60px; width: 60px; border-radius: 50%"
			alt=""
			srcset=""
		/>
		<div
			class="mb-1 left d-flex ml-3 flex-column justify-content-start align-items-start"
		>
			<p>
				<b>{{ user.Firstname }}</b>
			</p>
			<p>
				<small>{{ user.Username }}</small>
			</p>
		</div>
	</div>

	<!-- <li class="" *ngIf="user"> -->
	<div class="ps_popover__wrapper" *ngIf="courses && courses.length > 0">
		<i
			class="pi pi-shopping-cart p-mr-4 p-text-secondary mt-4 ml-3 mr-3"
			pBadge
			style="font-size: 1.7rem"
			[value]="courses.length.toString()"
		></i>

		<div class="ps_popover__content p-0">
			<div class="d-flex pl-3 pt-3 pr-3 pb-1">
				<span
					><b>{{ 'menu.card' | translate }}</b></span
				>
			</div>
			<app-pay-shopping-basket
				[course]="item"
				*ngFor="let item of courses"
			></app-pay-shopping-basket>

			<div class="p-3">
				<h3 class="font-bold mb-1 text-left">
					TOTAL: <span class="text-primary">{{ totalStr }} EUR</span>
				</h3>
				<p-button
					[label]="'menu.accessCard' | translate"
					routerLink="/pay"
					styleClass="p-button-danger w-100"
				>
				</p-button>
			</div>
		</div>
	</div>

	<div class="d-flex mb-3">
		<a
			href="/"
			*ngIf="!user"
			class="btn btn-primary btn-md mr-3"
			(click)="openLoginModal($event)"
			>{{ 'home.login.title3' | translate }}</a
		>
		<a
			href="/"
			*ngIf="!user"
			class="btn btn-secondary btn-md"
			(click)="openRegisterModal($event)"
			>{{ 'home.register.title1' | translate }}</a
		>
	</div>
	<div class="d-flex flex-column" *ngIf="user">
		<div class="d-flex notif_block pointer">
			<a routerLink="/users/account">{{ 'menu.myAccount' | translate }}</a>
		</div>

		<div class="d-flex notif_block pointer">
			<a routerLink="/meetings">{{ 'menu.meetings' | translate }}</a>
		</div>
		<div class="d-flex notif_block pointer">
			<a routerLink="/forum">Forum</a>
		</div>
		<div class="d-flex notif_block pointer">
			<a routerLink="/posts">Blog</a>
		</div>
		<div class="d-flex notif_block pointer">
			<a routerLink="/home/<USER>">{{ 'home.page.about' | translate }}</a>
		</div>
		<div class="d-flex notif_block pointer">
			<a routerLink="/home/<USER>">Contact</a>
		</div>
		<div class="d-flex notif_block pointer">
			<a routerLink="/home/<USER>">{{ 'menu.pricing' | translate }}</a>
		</div>
		<div class="d-flex notif_block pointer" routerLink="/home/<USER>">
			<a>{{ 'menu.process' | translate }}</a>
		</div>
		<div class="d-flex notif_block pointer" routerLink="/instructors">
			<a>{{ 'menu.createCourse' | translate }}</a>
		</div>
		<div class="d-flex notif_block pointer" routerLink="/courses">
			<a>{{ 'menu.takeCourse' | translate }}</a>
		</div>
		<div class="d-flex notif_block pointer">
			<a (click)="logout($event)" href="">{{ 'menu.logout' | translate }}</a>
		</div>
	</div>
	<div class="d-flex flex-column" *ngIf="!user">
		<div class="d-flex notif_block pointer" routerLink="/courses">
			<a>{{ 'menu.takeCourse' | translate }}</a>
		</div>
	</div>
</p-sidebar>
