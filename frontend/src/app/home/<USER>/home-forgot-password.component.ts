import { UserStatusType } from './../../models/user.model';
import { Component, OnInit } from '@angular/core';
import { UserService } from '~/app/services/users/user.service';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';

@Component({
	selector: 'app-home-forgot-password',
	templateUrl: './home-forgot-password.component.html',
	styleUrls: ['./home-forgot-password.component.scss']
})
export class HomeForgotPasswordComponent implements OnInit {
	email: string;
	msg: string;
	isLoading = false;

	constructor(
		private userService: UserService,
		private route: ActivatedRoute,
		private router: Router,
		private toastr: ToastrService
	) {}

	ngOnInit(): void {}

	onSubmit(): void {
		this.msg =
			"En cour d'envoi du lien de réinitialisation du mot de passe par mail...";
		this.isLoading = true;
		this.userService.getByEmail(this.email).subscribe(
			(user) => {
				if (user.Status !== UserStatusType.ACTIVE) {
					this.isLoading = false;
					this.msg =
						'Ce compte a été fermé. Veuillez contacter un administrateur';
					this.toastr.error('Ce compte a été fermé', 'Brain-maker');
					return;
				}
				user.Code = window.location.origin;
				user.Slug = 'forgot-password';
				this.userService.sendMail(user).subscribe(
					(value) => {
						this.isLoading = false;
						this.msg = `Lien de réinitialisation envoyé a l\'addresse ${user.Email}`;
						this.toastr.success(
							'Lien de réinitialisation envoyé par mail',
							'Brain-maker'
						);
					},
					(error) => {
						this.isLoading = false;
						this.msg = null;
						this.toastr.error(
							'Error to send link for reinitialize password.',
							'Brain-maker'
						);
						console.log(error);
					}
				);
			},
			(error) => {
				console.log(error);
				this.toastr.error('Addresse e-mail introuvable', 'Brain-maker');
				this.msg = null;
				this.isLoading = false;
			}
		);
	}
}
