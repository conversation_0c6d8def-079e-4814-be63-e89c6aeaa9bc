import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { MenuItem } from 'primeng/api';
import { Router } from '@angular/router';

@Component({
  selector: 'app-home-about',
  templateUrl: './home-about.component.html',
  styleUrls: ['./home-about.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class HomeAboutComponent implements OnInit {
  items: MenuItem[];

  activeItem: MenuItem;

  constructor(private router: Router) { }

  ngOnInit(): void {
    this.items = [
      { label: 'Home', icon: 'pi pi-fw pi-home' },
      { label: 'Calendar', icon: 'pi pi-fw pi-calendar' },
      { label: 'Edit', icon: 'pi pi-fw pi-pencil' },
      { label: 'Documentation', icon: 'pi pi-fw pi-file' },
      { label: 'Settings', icon: 'pi pi-fw pi-cog' }
    ];

    this.activeItem = this.items[0];
  }
  navigate(index): void {
    // this.router.navigate([link]);
    // console.log(link)
    switch (index) {
      case 0:
        this.router.navigate(['home/about']);
        break;
      case 1:
        this.router.navigate(['home/about/society']);
        break;
      case 2:
        this.router.navigate(['home/about/presse']);
        break;
      default:
        this.router.navigate(['home/about']);

        break;
    }
  }
}
