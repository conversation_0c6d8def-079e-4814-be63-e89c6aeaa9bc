import { Component, OnInit } from '@angular/core';
import { Category } from '~/app/models/category.model';
import { CategoryService } from '~/app/services/categories/category.service';

@Component({
	selector: 'app-home-categories',
	templateUrl: './home-categories.component.html',
	styleUrls: ['./home-categories.component.scss']
})
export class HomeCategoriesComponent implements OnInit {
	categories: Category[];

	skip = 0;
	take = 9;
	countOfItem = 0;
	loadMore: boolean;
	constructor(private categoryService: CategoryService) {}

	async ngOnInit(): Promise<void> {
		this.countOfItem = await this.categoryService.getCount().toPromise();
		this.loadMore = this.countOfItem > this.take + this.skip;
		this.take = this.countOfItem < this.take ? this.countOfItem : this.take;
		this.categoryService
			.getAllLazy(this.take, this.skip)
			.subscribe((categories) => (this.categories = categories));
	}

	onSearch(): void {}

	onLoadMore(e): void {
		e.preventDefault();

		this.skip =
			this.countOfItem < this.skip + this.take
				? this.countOfItem
				: this.skip + this.take;
		this.loadMore = this.countOfItem > this.take + this.skip;
		this.categoryService
			.getAllLazy(this.take, this.skip)
			.subscribe(
				(categories) => (this.categories = [...this.categories, ...categories])
			);
	}
}
