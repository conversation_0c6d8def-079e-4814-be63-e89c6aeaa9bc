import {
	ChangeDetectionStrategy,
	Component,
	OnInit,
	ChangeDetectorRef
} from '@angular/core';
import { User, UserStatusType } from '../../models/user.model';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { UtilsService } from '../../services/utils.service';
import { UserService } from '../../services/users/user.service';
import { ToastrService } from 'ngx-toastr';
import { validate } from 'class-validator';
import { LyDialogRef } from '@alyle/ui/dialog';

@Component({
	selector: 'app-home-login',
	//changeDetection: ChangeDetectionStrategy.OnPush,
	templateUrl: './home-login.component.html',
	styleUrls: ['./home-login.component.scss']
})
export class HomeLoginComponent implements OnInit {
	errors: any[];
	errorsHelper: any[];
	isLoading = false;
	msgError: string;
	user: User = new User(
		null,
		'123',
		'<EMAIL>',
		null,
		null,
		null,
		'+32 000000000'
	);

	constructor(
		public dialogRef: LyDialogRef,
		private userService: UserService,
		private utilsService: UtilsService,
		private router: Router,
		private translateService: TranslateService,
		private toastr: ToastrService,
		private changeDetectorRef: ChangeDetectorRef
	) {}

	ngOnInit(): void {
		if (localStorage.getItem(this.userService.LOGINSTORAGE)) {
			// this.router.navigate(['/professional']);
		}
	}

	close(e = null): void {
		if (e) {
			e.preventDefault();
		}
		this.dialogRef.close();
	}

	isNotvalid(name: string): boolean {
		return this.utilsService.isNotvalid(name, this.errorsHelper);
	}

	async onSubmit(e) {
		e.preventDefault();
		this.errors = await validate(this.user);
		this.errorsHelper = this.errors;

		// if ( !(this.errors) || this.errors.length === 0 ) {
		this.isLoading = true;
		this.changeDetectorRef.detectChanges();
		try {
			const user = await this.userService.login(this.user).toPromise();
			if (user && user.Slug) {
				if (user.Status == UserStatusType.BLOCKED) {
					this.msgError = 'Votre compte a été désactiver.';
					const message = await this.translateService
						.get('home.login.closedAccount')
						.toPromise();
					this.toastr.error(message, 'Brain-maker');
				} else if (user.Status == UserStatusType.DELETED) {
					this.msgError = 'Votre compte a été supprimer';
					const message = await this.translateService
						.get('home.login.deletedAccount')
						.toPromise();
					this.toastr.error(message, 'Brain-maker');
				} else {
					const message = await this.translateService
						.get('home.login.success')
						.toPromise();
					if (user.IsLogin) {
						if (this.router.url.startsWith('/courses')) {
							window.location.reload();
						} else {
							this.router.navigate(['/courses']);
						}

						this.toastr.success(message, 'Brain-maker');
					} else {
						this.router.navigate([`/home/<USER>/${user.Slug}`]);
					}

					this.close();
				}
			} else {
				const message = await this.translateService
					.get('home.login.error')
					.toPromise();
				this.toastr.error(message, 'Brain-maker');
			}
		} catch (e) {
			console.log('LOGIN ERROR ==========> ', e);
			const message = await this.translateService
				.get('home.login.error')
				.toPromise();
			this.toastr.error(message, 'Brain-maker');
		} finally {
			this.isLoading = false;
			this.changeDetectorRef.detectChanges();
		}
		// }else {
		// const message = await this.translateService.get('language.message').toPromise();
		// this.toastr.error(message, 'LeChatong');
		// }
	}

	onRegister(e: MouseEvent): void {
		e.preventDefault();
		this.router.navigate(['register']);
	}

	async onItemChange(): Promise<void> {
		this.errorsHelper = await validate(this.user);
	}
}
