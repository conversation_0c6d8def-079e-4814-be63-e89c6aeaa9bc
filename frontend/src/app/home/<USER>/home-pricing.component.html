<div class="clearfix"></div>
<!-- ============================================================== -->
<!-- Top header  -->
<!-- ============================================================== -->

<!-- ============================ Page Title Start================================== -->
<div class="page-title mb-5">
	<div class="row">
		<div class="col-lg-12 col-sm-12 col-md-12">
			<div class="header1">
				<div class="row title-height-100">
					<div class="col-lg-8 col-sm-12 title-height-100">
						<div
							class="title title-height-100 d-flex flex-column align-items-center justify-content-center"
						>
							<div class="h2 text-white">
								{{ 'user.misc.pricingTitle' | translate }}
							</div>
							<p class="h5 text-white">
								{{ 'user.misc.pricingDesc' | translate }}
							</p>
							<!--a class="btn btn-dark btn-lg" routerLink="/courses/create">Commencer maintenant</a-->
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<!-- ============================ Page Title End ================================== -->

<!-- ============================ Pricing Start ================================== -->
<section class="pt-0">
	<div class="container">
		<div class="row">
			<!-- Single Package -->
			<div *ngIf="abonnementForfaits.length > 0" class="col-lg-4 col-md-4">
				<div class="packages_wrapping">
					<div class="packages_headers">
						<i class="lni-layers"></i>
						<!-- <h4 class="packages_pr_title">
									{{ 'user.info.basicPackT1' | translate }}
								</h4> -->
						<h4 class="packages_pr_title">
							{{ abonnementForfaits[0].Name }}
						</h4>
						<!-- <span class="packages_price-subtitle">{{
									'user.info.basicPackT2' | translate
								}}</span> -->
						<!-- <span class="packages_price-subtitle">{{
									'user.details.essential' | translate
								}}</span> -->
					</div>
					<div class="packages_price">
						<h4 class="pr-value">
							{{ abonnementForfaits[0].Price }}
						</h4>
					</div>
					<!-- <div class="packages_middlebody">
								<ul [innerHTML]="'user.info.basicPackDesc' | translate"></ul>
							</div> -->
					<span class="packages_price-subtitle">{{
						'user.details.essential' | translate
					}}</span>
					<div class="packages_bottombody">
						<a
							(click)="onSelectForfait(abonnementForfaits[0])"
							class="btn-pricing pointer"
							>{{ 'user.info.choosePlan' | translate }}</a
						>
					</div>
				</div>
			</div>

			<!-- Single Package -->
			<div *ngIf="abonnementForfaits.length > 1" class="col-lg-4 col-md-4">
				<div class="packages_wrapping recommended">
					<div class="packages_headers">
						<i class="lni-diamond"></i>
						<!-- <h4 class="packages_pr_title">
									{{ 'user.info.platinumPackT1' | translate }}
								</h4> -->
						<h4 class="packages_pr_title">
							{{ abonnementForfaits[1].Name }}
						</h4>
						<!-- <span class="packages_price-subtitle">{{
									'user.info.platinumPackT2' | translate
								}}</span> -->
						<!-- <span class="packages_price-subtitle">{{
									'user.details.standard' | translate
								}}</span> -->
					</div>
					<div class="packages_price">
						<h4 class="pr-value">
							{{ abonnementForfaits[1].Price }}
						</h4>
					</div>
					<!-- <div class="packages_middlebody">
								<ul [innerHTML]="'user.info.platinumPackDesc' | translate"></ul>
							</div> -->
					<span class="packages_price-subtitle text-white">{{
						'user.details.standard' | translate
					}}</span>
					<div class="packages_bottombody">
						<a
							(click)="onSelectForfait(abonnementForfaits[1])"
							class="btn-pricing pointer"
							>{{ 'user.info.choosePlan' | translate }}</a
						>
					</div>
				</div>
			</div>

			<!-- Single Package -->
			<div *ngIf="abonnementForfaits.length > 2" class="col-lg-4 col-md-4">
				<div class="packages_wrapping">
					<div class="packages_headers">
						<i class="lni-invention"></i>
						<!-- <h4 class="packages_pr_title">
									{{ 'user.info.standardPackT1' | translate }}
								</h4> -->
						<h4 class="packages_pr_title">
							{{ abonnementForfaits[2].Name | translate }}
						</h4>
						<!-- <span class="packages_price-subtitle">{{
									'user.info.standardPackT2' | translate
								}}</span> -->
						<!-- <span class="packages_price-subtitle">{{
									'user.details.premium' | translate
								}}</span> -->
					</div>
					<div class="packages_price">
						<h4 class="pr-value">
							{{ abonnementForfaits[2].Price }}
						</h4>
					</div>
					<!-- <div class="packages_middlebody">
								<ul [innerHTML]="'user.info.standardPackDesc' | translate"></ul>
							</div> -->
					<span class="packages_price-subtitle">{{
						'user.details.premium' | translate
					}}</span>
					<div class="packages_bottombody">
						<a
							(click)="onSelectForfait(abonnementForfaits[2])"
							class="btn-pricing pointer"
							>{{ 'user.info.choosePlan' | translate }}</a
						>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>
<!-- ============================ Pricing End ================================== -->

<!-- ============================== Start Newsletter ================================== -->
<!-- ================================= End Newsletter =============================== -->
