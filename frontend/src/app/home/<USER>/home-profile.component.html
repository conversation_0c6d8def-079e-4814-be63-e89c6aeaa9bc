<form
	#f="ngForm"
	(ngSubmit)="onSubmit()"
	class="mx-auto align-content-center mb-4"
	*ngIf="user && categories && countryList"
>
	<br />
	<br />
	<div class="font-weight-bold text-center" style="font-size: 2.5em">
		<span class="text-dark">Brain-</span><span class="text-danger">maker</span>
	</div>
	<br />
	<br />
	<p class="text-center" style="font-size: 30px">
		{{ 'home.register.profileTitle' | translate }}
	</p>
	<br />
	<div class="row justify-content-center">
		<div class="col-10 col-sm-6 p-fluid form-group">
			<div class="p-field">
				<label for="domaine">
					{{ 'home.register.profileOrientation' | translate }}
				</label>
				<p-multiSelect
					[options]="categories"
					name="domaine"
					id="domaine"
					[(ngModel)]="user.Categories"
					defaultLabel="Votre profession"
					optionLabel="Title"
				></p-multiSelect>
			</div>
		</div>
	</div>

	<div class="row justify-content-center">
		<div class="col-10 col-sm-6 form-group">
			<label for="country">
				{{ 'home.register.profileCountry' | translate }}
			</label>
			<select
				name="country"
				required
				[(ngModel)]="user.Country"
				id="country"
				class="select2 form-control"
				data-show-subtext="true"
				data-live-search="true"
			>
				<option value="0" selected disabled>
					{{ 'home.register.profileChoose' | translate }}
				</option>
				<option *ngFor="let country of countryList" value="{{ country.name }}">
					{{ country.name }}
				</option>
			</select>
		</div>
	</div>
	<br />

	<div class="text-center">
		<p style="font-size: 20px">
			{{ 'home.register.profileStudent' | translate }}
		</p>
		<br />
		<button
			[disabled]="f.invalid || isLoading"
			type="submit"
			class="btn btn-danger text-white"
		>
			<span class="mr-2" *ngIf="isLoading"
				><i class="fas fa-spin fa-spinner"></i
			></span>
			{{ 'home.register.profileNext' | translate }}
		</button>
	</div>
</form>
