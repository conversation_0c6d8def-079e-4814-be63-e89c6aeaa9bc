import { Component, OnInit } from '@angular/core';
import { NgDatnekCountrySelectService } from 'ng-datnek-select-country';
import { User } from '~/app/models/user.model';
import { UserService } from '~/app/services/users/user.service';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { TranslateService } from '@ngx-translate/core';
import { Category } from '~/app/models/category.model';
import { CategoryService } from '~/app/services/categories/category.service';

@Component({
	selector: 'app-home-profile',
	templateUrl: './home-profile.component.html',
	styleUrls: ['./home-profile.component.scss']
})
export class HomeProfileComponent implements OnInit {
	countryList = [];
	domaineList = [
		'Développement',
		'Business',
		'Finance et comptabilité',
		'Informatique et logiciels',
		'Productivité bureautique',
		'Développement personnel',
		'Design',
		'Marqueting',
		'Mode de vie',
		'Photographie et vidéo',
		'Santé et bien-être',
		'Musique',
		'Formations et diplômes'
	];
	user: User;
	slug: string;
	categories: Category[];
	isLoading = false;

	constructor(
		private countrySelectService: NgDatnekCountrySelectService,
		private userService: UserService,
		private route: ActivatedRoute,
		private router: Router,
		private toastr: ToastrService,
		private categoryService: CategoryService,
		private translateService: TranslateService
	) {}

	async ngOnInit(): Promise<void> {
		if (this.userService.isConnected()) {
			this.slug = this.route.snapshot.params.slug;
			this.user = await this.userService.getUserConnected().toPromise();
			console.log('user connected :', this.user);
			this.categories = await this.categoryService.getAll().toPromise();
			this.countryList = this.countrySelectService.getData();
		} else {
			this.router.navigate(['/']);
		}
	}

	async onSubmit(): Promise<void> {
		this.isLoading = true;
		this.userService.edit(this.user).subscribe(
			async (value) => {
				this.isLoading = false;
				const message = await this.translateService
					.get('home.login.success')
					.toPromise();
				this.router.navigate(['/courses']);
				this.toastr.success(message, 'Brain-maker');
			},
			async (error) => {
				this.isLoading = false;
				console.log(error);
				const message = await this.translateService
					.get('home.login.error')
					.toPromise();
				this.toastr.error(message, 'Brain-maker');
			}
		);
	}
}
