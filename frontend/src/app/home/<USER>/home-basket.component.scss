.details_basket_block {
    flex: 1 1;
    min-width: 1px;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    max-height: 100px;
    padding-left: 1em;
}

@media only screen and (min-width: 601px) {
    .hide-on-med-and-up {
        display: none !important;
    }
}

@media only screen and (max-width: 992px) {
    .hide-on-med-and-down {
        display: none !important;
    }
}

@media only screen and (min-width: 993px) {
    .hide-on-large-only {
        display: none !important;
    }
}
