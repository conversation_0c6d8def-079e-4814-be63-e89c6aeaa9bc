import { Component, OnInit } from '@angular/core';

import { UserService } from '~/app/services/users/user.service';
import { UtilsService } from '~/app/services/utils.service';
import { TranslateService } from '@ngx-translate/core';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute, Router } from '@angular/router';
import { User } from '~/app/models/user.model';

@Component({
	selector: 'app-home-reset-password',
	templateUrl: './home-reset-password.component.html',
	styleUrls: ['./home-reset-password.component.scss']
})
export class HomeResetPasswordComponent implements OnInit {
	user: User;
	newpass: string;
	conpass: string;
	slug = '';
	tokenCode = '';
	isLoading: boolean;

	constructor(
		private userService: UserService,
		private utilsService: UtilsService,
		private translateService: TranslateService,
		private toastr: ToastrService,
		private router: Router,
		private route: ActivatedRoute
	) {
		this.userService.userAccountCurrentTabSubject.next('account');
	}

	async ngOnInit(): Promise<void> {
		this.slug = this.route.snapshot.params.slug;
		this.tokenCode = decodeURIComponent(
			this.route.snapshot.queryParams.tokenCode
		);

		if (
			(this.slug && this.slug.length) > 0 &&
			this.tokenCode &&
			this.tokenCode.length
		) {
			this.user = await this.userService.getBySlug(this.slug).toPromise();
		} else {
			//this.router.navigate(['/']);
		}
	}

	async onSubmit(e): Promise<void> {
		e.preventDefault();

		if (this.slug.length > 0 && this.newpass) {
			if (this.newpass.length < 8) {
				this.toastr.error('Trop court! 8 caracteres minimum', 'Brain-maker');
			}
			if (this.newpass !== this.conpass) {
				this.toastr.error(
					'Le nouveau mot de passe et sa confirmation ne coincide pas.',
					'Brain-maker'
				);
				return;
			}
			this.isLoading = true;
			if (this.slug) {
				this.user.Password = this.newpass;
				this.userService.edit(this.user).subscribe(
					async (value) => {
						const message = await this.translateService
							.get('home.register.success')
							.toPromise();
						this.toastr.success(message, 'Brain-maker');
						this.isLoading = false;
						this.router.navigate(['/']);
					},
					(error) => {
						this.isLoading = false;
						console.log(error);
						this.toastr.error(
							'Echec de mise a jour du mot de passe',
							'Brain-maker'
						);
					}
				);
			}
		}
	}
}
