<form autocomplete="off" (ngSubmit)="onSubmit($event)">
	<div ly-dialog-content class="px-0 mx-0">
		<div class="login-pop-form" role="document">
			<div class="" id="sign-up">
				<span class="mod-close" (click)="close($event)" aria-hidden="true"
					><i class="ti-close"></i
				></span>
				<div class="modal-body">
					<h4 class="modal-header-title">
						{{ 'home.register.title1' | translate }}
					</h4>
					<div class="row">
						<div class="form-group col-sm-12">
							<label for="home-register-Civility">
								{{ 'home.register.civility' | translate }} *
							</label>
							<select
								[(ngModel)]="user.Civility"
								name="home-register-Civility"
								(change)="onItemChange()"
								required
								[ngClass]="{ 'is-invalid': errors && isNotvalid('Civility') }"
								id="home-register-Civility"
								class="select2 form-control"
							>
								<option value="0" selected disabled>Choose one</option>
								<option value="1">Mr.</option>
								<option value="2">Mrs</option>
								<option value="3">Ms</option>
							</select>
							<div class="invalid-feedback">
								<ul *ngIf="errors && isNotvalid('Civility')">
									<li class="" *ngFor="let item of getAllError('Civility')">
										{{ item }}
									</li>
								</ul>
							</div>
						</div>

						<div class="form-group col-sm-12 col-md-6">
							<label for="home-register-firstname"
								>{{ 'home.register.firstname' | translate }} *
							</label>
							<input
								[(ngModel)]="user.Firstname"
								name="home-register-firstname"
								type="text"
								(keyup)="onItemChange()"
								[ngClass]="{ 'is-invalid': errors && isNotvalid('Firstname') }"
								id="home-register-firstname"
								required
								placeholder=""
								class="form-control"
							/>
							<div class="invalid-feedback">
								<ul *ngIf="errors && isNotvalid('Firstname')">
									<li class="" *ngFor="let item of getAllError('Firstname')">
										{{ item }}
									</li>
								</ul>
							</div>
						</div>
						<div class="form-group col-sm-12 col-md-6">
							<label for="home-register-lastname"
								>{{ 'home.register.lastname' | translate }} *</label
							>
							<input
								[(ngModel)]="user.Lastname"
								type="text"
								(keyup)="onItemChange()"
								[ngClass]="{ 'is-invalid': errors && isNotvalid('Lastname') }"
								id="home-register-lastname"
								name="home-register-lastname"
								required
								placeholder=""
								class="form-control"
							/>
							<div class="invalid-feedback">
								<ul *ngIf="errors && isNotvalid('Lastname')">
									<li class="" *ngFor="let item of getAllError('Lastname')">
										{{ item }}
									</li>
								</ul>
							</div>
						</div>
						<div class="form-group col-sm-12 col-md-6">
							<label for="home-register-phone"
								>{{ 'home.register.phone' | translate }} *
							</label>
							<!-- <input
                [(ngModel)]="user.Phone"
                type="tel"
                placeholder="+32 000000000"
                (keyup)="onItemChange()"
                [ngClass]="{ 'is-invalid': errors && isNotvalid('Phone') }"
                id="home-register-phone"
                name="home-register-phone"
                required
                class="form-control"
              /> -->
							<ngx-intl-tel-input
								[cssClass]="'form-control iti'"
								[preferredCountries]="['cm', 'fr']"
								[separateDialCode]="true"
								[enableAutoCountrySelect]="true"
								[enablePlaceholder]="true"
								[searchCountryFlag]="true"
								[selectFirstCountry]="false"
								[maxLength]="15"
								[phoneValidation]="true"
								name="home-register-phone"
								[(ngModel)]="phoneInput"
							></ngx-intl-tel-input>
							<div class="invalid-feedback">
								<ul *ngIf="errors && isNotvalid('Phone')">
									<li class="" *ngFor="let item of getAllError('Phone')">
										{{ item }}
									</li>
								</ul>
							</div>
						</div>

						<div class="form-group col-sm-12 col-md-6">
							<label for="home-register-email"
								>{{ 'home.register.email' | translate }} *
							</label>
							<input
								[(ngModel)]="user.Email"
								(keyup)="onItemChange()"
								[ngClass]="{ 'is-invalid': errors && isNotvalid('Email') }"
								type="email"
								name="home-register-email"
								id="home-register-email"
								placeholder=""
								class="form-control"
								required
							/>
							<div class="invalid-feedback">
								<ul *ngIf="errors && isNotvalid('Email')">
									<li class="" *ngFor="let item of getAllError('Email')">
										{{ item }}
									</li>
								</ul>
							</div>
						</div>

						<div class="form-group position-relative col-sm-12">
							<label for="home-register-password"
								>{{ 'home.register.password' | translate }} *</label
							>
							<input
								[(ngModel)]="user.Password"
								[type]="inputTypePassword"
								(keyup)="onItemChange()"
								[ngClass]="{ 'is-invalid': errors && isNotvalid('Password') }"
								name="home-register-password"
								id="home-register-password"
								class="form-control"
								required
							/>
							<button
								class="btn btn-default"
								class="btn-show-password-eyes"
								(click)="changeInputPasswordType($event)"
							>
								<i class="fas fa-2X fa-eye"></i>
							</button>
							<div class="invalid-feedback">
								<ul *ngIf="errors && isNotvalid('Password')">
									<li class="" *ngFor="let item of getAllError('Password')">
										{{ item }}
									</li>
								</ul>
							</div>
						</div>

						<div class="form-group col-sm-12">
							<label for="home-register-password-confirm"
								>{{ 'home.register.confirm' | translate }} *</label
							>
							<input
								[(ngModel)]="confirmPassword"
								type="password"
								(keyup)="onItemChange()"
								[ngClass]="{ 'is-invalid': user.Password !== confirmPassword }"
								name="home-register-password-confirm"
								id="home-register-password-confirm"
								class="form-control"
								required
							/>
							<div class="invalid-feedback">
								<ul *ngIf="user.Password !== confirmPassword">
									<li class="">Password does not match</li>
								</ul>
							</div>
						</div>

						<div
							class="form-group col-12 d-flex justify-content-between align-items-center"
						>
							<div class="">
								<button
									type="submit"
									class="btn btn-dark"
									[disabled]="isLoading"
								>
									<span *ngIf="isLoading" class="mr-2"
										><i class="fas fa-spin fa-spinner"></i
									></span>
									{{ 'home.register.btn.register' | translate }}
								</button>
							</div>
						</div>
					</div>
					<div class="text-center"></div>
				</div>
			</div>
		</div>
	</div>

	<!--div container ly-dialog-actions class="d-flex justify-content-between px-5">
  <button type="submit" class="btn btn-dark">Sign Up</button>
  <p>
    Already Have An Account? <a href="#" class="link">Go For LogIn</a>
  </p>
</div-->
</form>
