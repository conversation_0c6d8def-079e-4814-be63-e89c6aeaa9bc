import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { Category } from '~/app/models/category.model';
import { CategoryService } from '~/app/services/categories/category.service';

@Component({
	selector: 'app-home-categories-top',
	templateUrl: './home-categories-top.component.html',
	styleUrls: ['./home-categories-top.component.scss']
})
export class HomeCategoriesTopComponent implements OnInit {
	categories: Category[];

	constructor(private categoryService: CategoryService, private changeDetectorRef: ChangeDetectorRef) { }

	ngOnInit() {
		this.categoryService
			.getAllLazy(9, 0)
			.toPromise().then((categories) => {
				this.categories = categories;
				this.changeDetectorRef.detectChanges()
			});


	}
}
