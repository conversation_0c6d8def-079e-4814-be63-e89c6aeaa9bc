<!-- <section>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-5 col-md-6 col-sm-12">
                <div class="sec-heading center">
                    <p>{{'home.page.popularCateg'|translate}}</p>
                    <h2>
                        <span class="theme-cl">{{'home.page.hotNPopular'|translate}} </span>{{'home.page.categToLearn'|translate}}
                    </h2>
                </div>
            </div>
        </div>

        <div class="row" *ngIf="categories && categories.length > 0">
            <div class="col-lg-4 col-md-4 col-sm-6" *ngFor="let category of categories">
                <app-home-categories-single [category]="category"></app-home-categories-single>
            </div>
        </div>
    </div>
</section>
 -->

<section class="course-cat">
	<div class="container">
		<span class="sup-title">{{ 'home.page.popularCateg' | translate }}</span>
		<h2>
			{{ 'home.page.popularCategLitlleTxt1' | translate
			}}<span class="ml-2 text-primary">{{
				'home.page.popularCategLitlleTxt2' | translate
			}}</span>
		</h2>

		<div class="row mt-4">
			<div
				class="col-12 col-md-6 col-lg-4 col-xl-3 mb-3"
				*ngFor="let category of categories"
			>
				<app-home-categories-single
					[category]="category"
				></app-home-categories-single>
			</div>
		</div>
	</div>
</section>
