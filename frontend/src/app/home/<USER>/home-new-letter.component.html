<!-- ============================== Start Newsletter ================================== -->
<section class="newsletter theme-bg inverse-theme">
	<div class="container">
		<div class="row justify-content-center">
			<div class="col-lg-7 col-md-8 col-sm-12">
				<div class="text-center">
					<h2>{{ 'home.page.joinStudents' | translate }}</h2>
					<p>{{ 'home.page.subscribeTxt1' | translate }}</p>
					<p>{{ 'home.page.subscribeTxt2' | translate }}</p>
					<form class="mt-3 form-search">
						<input
							type="text"
							class="form-control input-search bg-white"
							required=""
							[(ngModel)]="searchInput"
							(keydown)="onSeach($event)"
							name="searchInput"
							id="searchInput"
							[placeholder]="'home.page.whatYouWantToLearn' | translate"
						/>
					</form>
					<!--div class="sup-form">
                        <input type="email" class="form-control sigmup-me" [placeholder]="'home.page.yourMail'|translate" required="required" />
                        <input type="submit" class="btn btn-theme" [value]="'home.page.startNow'|translate" />
                    </div-->
				</div>
			</div>
		</div>
	</div>
</section>
<!-- ================================= End Newsletter =============================== -->
