import { Component, Input, OnInit } from '@angular/core';
import { Category } from '~/app/models/category.model';
import { CourseService } from '~/app/services/courses/course.service';

@Component({
	selector: 'app-home-categories-single',
	templateUrl: './home-categories-single.component.html',
	styleUrls: ['./home-categories-single.component.scss']
})
export class HomeCategoriesSingleComponent implements OnInit {
	@Input() category: Category;
	color: string;
	totalCourse = 0;

	constructor(private courseService: CourseService) {}

	async ngOnInit() {
		//this.setRandomColor();
		const result = await this.courseService
			.getAllByCategories(this.category.Slug, 1, 0)
			.toPromise();
		this.totalCourse = result.Count;
	}

	setRandomColor(): void {
		const items = ['cat-1', 'cat-2', 'cat-3', 'cat-4', 'cat-5'];
		this.color = items[Math.floor(Math.random() * items.length)];
		//console.log('color : ', this.color);
	}
}
