<!-- <div [class]="'edu_cat_2 ' + color">
	<img
		[src]="
			category.Photo
				? category.Photo.Hashname
				: 'https://via.placeholder.com/70x70'
		"
		class="img-fluid"
		alt=""
	/>
	<div class="edu_cat_data">
		<h4 class="title">
			<a
				class="btn btn-link"
				[routerLink]="'/courses/category/' + category.Slug"
				>{{ category.Title }}</a
			>
		</h4>
		<ul class="meta">
			<li class="video">
				<a
					href=""
					class="btn-link text-muted"
					*ngIf="category.SubCategories && category.SubCategories.length > 0"
					[routerLink]="'/home/<USER>/sub/' + category.Slug"
				>
					<i class="ti-video-clapper"></i
					>{{ category.SubCategories ? category.SubCategories.length : 0 }}
					{{ 'home.page.categorieClasses' | translate }}
				</a>
				<span
					class="text-muted"
					*ngIf="!category.SubCategories || category.SubCategories.length === 0"
				>
					<i class="ti-video-clapper"></i
					>{{ category.SubCategories ? category.SubCategories.length : 0 }}
					{{ 'home.page.categorieClasses' | translate }}
				</span>
			</li>
		</ul>
	</div>
</div> -->

<a [routerLink]="'/courses/category/' + category.Slug">
	<div class="cat-item d-flex justify-content-center flex-column">
		<img
			[src]="
				category.Photo
					? category.Photo.Hashname
					: 'https://via.placeholder.com/70x70'
			"
			alt=""
		/>
		<div class="cat-item-info">
			<h5>{{ category.Title }}</h5>
			<p>
				<span>{{ totalCourse }}</span> cours
			</p>
		</div>
	</div>
</a>
