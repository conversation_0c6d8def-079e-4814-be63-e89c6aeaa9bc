import { ChangeDetectorRef, Component, Inject, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { UtilsService } from '~/app/services/utils.service';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { validate } from 'class-validator';
import { Request } from '~/app/models/request.model';
import { RequestService } from '~/app/services/requests/request.service';

@Component({
	selector: 'app-home-contact',
	templateUrl: './home-contact.component.html',
	styleUrls: ['./home-contact.component.scss']
})
export class HomeContactComponent implements OnInit {
	errors: any[];
	errorsHelper: any[];
	isLoading: boolean;
	msgError: string;
	request: Request = new Request();
	cover = '/assets/img/learning1.jpg';
	coverName: string;
	constructor(
		private toastr: ToastrService,
		private requestService: RequestService,
		private utilsService: UtilsService,
		private router: Router,
		private cd: ChangeDetectorRef,
		private translateService: TranslateService
	) {}

	ngOnInit(): void {}

	isNotvalid(name: string): boolean {
		return this.utilsService.isNotvalid(name, this.errorsHelper);
	}

	async onSubmit(e): Promise<void> {
		if (e) {
			e.preventDefault();
		}
		this.errors = await validate(this.request);
		this.errorsHelper = this.errors;

		// if ( !(this.errors) || this.errors.length === 0 ) {
		this.isLoading = true;
		try {
			const request = await this.requestService.add(this.request).toPromise();

			if (!this.errors || this.errors.length === 0) {
				if (request && request.Id) {
					console.log('request :', request);
					const message = await this.translateService
						.get('success')
						.toPromise();
					this.toastr.success(message, 'Brain-maker');
					this.request = new Request();
				} else {
					const message = await this.translateService.get('error').toPromise();
					this.toastr.error(message, 'Brain-maker');
				}
			}
			this.isLoading = false;
		} catch (e) {
			console.log(e);
			const message = await this.translateService.get('error').toPromise();
			this.toastr.error(message, 'Brain-maker');
		} finally {
			this.isLoading = false;
		}
	}

	async onItemChange(): Promise<void> {
		this.errorsHelper = await validate(this.request);
	}
}
