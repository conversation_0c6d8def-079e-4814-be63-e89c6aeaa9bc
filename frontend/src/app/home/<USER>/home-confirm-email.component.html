<div
	*ngIf="user && !user.Code"
	class="w-50 mx-auto text-center align-content-center mb-4"
	style="font-size: 14px"
>
	<br />
	<br />
	<div class="font-weight-bold" style="font-size: 2.5em">
		<span class="text-dark">Brain-</span><span class="text-danger">maker</span>
	</div>
	<br />
	<br />

	<h1 class="text-center font-weight-light" style="font-size: 3em">
		Link Expired
	</h1>

	<a routerLink="/courses" class="mt-5 btn btn-dark"> GO TO COURSES </a>
</div>

<div
	*ngIf="user && user.Code"
	class="mx-auto text-center align-content-center mb-4"
	style="font-size: 14px"
>
	<br />
	<br />
	<div class="font-weight-bold" style="font-size: 2.5em">
		<span class="text-dark">Brain-</span><span class="text-danger">maker</span>
	</div>
	<br />
	<br />
	<h3>{{ 'user.details.confirmEmail' | translate }}</h3>
	<br />
	<input
		name="code"
		[(ngModel)]="code"
		type="text"
		class="mx-auto form-control text-center"
		style="font-size: 20px; width: 50%"
	/>
	<br />
	<p>{{ 'user.details.typeCode' | translate }} {{ user.Email }}</p>

	<br />
	<button
		(click)="confirmEmail()"
		class="btn btn-danger text-white"
		[disabled]="isLoading || isLoadingSendCode || !this.code.length"
	>
		<span class="mr-2" *ngIf="isLoading"
			><i class="fas fa-spin fa-spinner"></i
		></span>
		{{ 'user.details.acceptNConfirmEmail' | translate }}
	</button>
	<br />
	<br />
	<div class="">
		<p>
			{{ 'user.details.noCode' | translate }}

			<a (click)="resendCode()" class="underline pointer">{{
				'user.details.sendCodeAgain' | translate
			}}</a>
			.
			<span class="mr-1" *ngIf="isLoadingSendCode"
				><i class="fas fa-spin fa-spinner"></i
			></span>
		</p>
	</div>
</div>
