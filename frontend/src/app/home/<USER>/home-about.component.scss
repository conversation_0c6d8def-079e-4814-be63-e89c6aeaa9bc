.mat-tab-label-content {
    color: white !important;
}

.mat-tab-nav-bar,
.mat-tab-header {
    border-bottom: 0 !important;
}

.mat-ink-bar {
    width: 110px !important;
}

.mat-tab-label {
    width: 110px !important;
    min-width: 40px !important;
}

.menu-tab-about h2 {
    width: 70%;
    height: 40vh
}

.menu-tab-about {
    color: white;
    width: 340px;
    background: #46494a45;
}

.mat-tab-label {
    opacity: 1 !important;
}

.ideal-card {
    height: 62vh;
}

.title-contain {
    height: 100%;
}

.about-dynamic {
    background: linear-gradient(197.61deg, #ec5252, #6e1a52);
    position: relative;
}

@media only screen and (min-width: 601px) {
    .hide-on-med-and-up {
        display: none !important;
    }
}

@media only screen and (max-width: 992px) {
    .hide-on-med-and-down {
        display: none !important;
    }
    .ideal-card {
        height: 100%;
    }
    .menu-tab-about {
        width: 100% !important;
    }
}

@media only screen and (min-width: 993px) {
    .hide-on-large-only {
        display: none !important;
    }
}