import { Component, OnInit } from '@angular/core';
import { TranslationService } from '~/app/services/translate/translation.service';
import { UtilsService } from '../../services/utils.service';

@Component({
	selector: 'app-home-footer',
	templateUrl: './home-footer.component.html',
	styleUrls: ['./home-footer.component.scss']
})
export class HomeFooterComponent implements OnInit {
	todayISOString: string = new Date().toISOString();

	languagesListUtils: { code: string; title: string }[];
	constructor(
		private utilsService: UtilsService,
		private translationService: TranslationService
	) {}

	ngOnInit(): void {
		this.refreshList();
	}

	async useLanguage(language: string, e): Promise<void> {
		e.preventDefault();
		this.translationService.useLanguage(language);
		await this.refreshList();
	}

	refreshList(): void {
		this.utilsService.getLangueUtils().then((res) => {
			this.languagesListUtils = res.filter((f) => f.code).slice(0, 2);
			// console.log('language : ', this.languagesListUtils);
		});
	}

	getLanguage(language: string, e): string {
		e.preventDefault();
		const lang = this.utilsService.getLangue(language, this.languagesListUtils);
		return lang;
	}
}
