import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import {
	Abonnement,
	AbonnementStatus
} from '~/app/models/abonnements/abonnement.model';
import { AbonnementForfait } from '~/app/models/abonnements/forfaits/abonnement-forfait.model';
import { AbonnementPrice } from '~/app/models/order.model';
import { User } from '~/app/models/user.model';
import { AbonnementsService } from '~/app/services/abonnements/abonnements.service';
import { AbonnementsForfaitsService } from '~/app/services/abonnements/forfaits/abonnements-forfaits.service';
import { UserService } from '~/app/services/users/user.service';
import { UtilsService } from '~/app/services/utils.service';
import { HomeLoginComponent } from '~/app/home/<USER>/home-login.component';

@Component({
	selector: 'app-home-pricing',
	templateUrl: './home-pricing.component.html',
	styleUrls: ['./home-pricing.component.scss']
})
export class HomePricingComponent implements OnInit {
	// abonnementPrice = AbonnementPrice;
	// abonnementType = AbonnementType;

	abonnementForfaits: AbonnementForfait[];
	user: User;
	userAbonnement: Abonnement;

	constructor(
		private abonnementForfaitService: AbonnementsForfaitsService,
		private abonnementService: AbonnementsService,
		private userService: UserService,
		private router: Router,
		private toastrService: ToastrService,
		private utilsService: UtilsService
	) {
		this.abonnementForfaits = [];
	}

	async ngOnInit(): Promise<void> {
		if (this.userService.isConnected()) {
			this.user = await this.userService.getUserConnected().toPromise();
			if (this.user) {
				this.userAbonnement = await this.abonnementService
					.getUserActiveAbonnement(this.user.Slug)
					.toPromise();
			}
		}
		//
		const forfaits = await this.abonnementForfaitService.getAll().toPromise();
		this.abonnementForfaits = forfaits.sort(
			(a: AbonnementForfait, b: AbonnementForfait) =>
				parseInt(a.Price) > parseInt(b.Price)
					? 1
					: parseInt(b.Price) > parseInt(a.Price)
					? -1
					: 0
		);
	}

	async onSelectForfait(forfait: AbonnementForfait): Promise<void> {
		if (!this.user) {
			this.toastrService.warning(
				'Authentification requise',
				'Veuillez vous connecter'
			);
			this.utilsService.buildModal(HomeLoginComponent, () => {}, 500, true, {});
			return;
		}

		console.log(forfait);

		if (this.userAbonnement) {
			this.userAbonnement.PendingForfait = forfait;
			this.userAbonnement = await this.abonnementService
				.edit(this.userAbonnement)
				.toPromise();
		} else {
			const newAbonnement = new Abonnement(null, '123', null, null);
			newAbonnement.PendingForfait = forfait;
			newAbonnement.Owner = this.user;
			newAbonnement.Status = AbonnementStatus.PENDING;

			this.userAbonnement = await this.abonnementService
				.add(newAbonnement)
				.toPromise();
		}
		this.router.navigateByUrl(`/pay/${this.userAbonnement.Slug}`);
	}
}
