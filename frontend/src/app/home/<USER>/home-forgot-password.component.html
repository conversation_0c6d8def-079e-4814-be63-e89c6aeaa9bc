<form
	#f="ngForm"
	(ngSubmit)="onSubmit()"
	style="vertical-align: central"
	class="w-50 mx-auto align-content-center text-center my-5"
>
	<div class="font-weight-bold text-center" style="font-size: 2.5em">
		<span class="text-dark">Brain-</span><span class="text-danger">maker</span>
	</div>
	<br />
	<br />
	<p style="font-size: 24px">{{ 'user.misc.forgotPass' | translate }}</p>
	<br />
	<div class="form-group mx-3">
		<input
			[(ngModel)]="email"
			required
			name="email"
			class="form-control"
			placeholder="<EMAIL>"
			type="email"
		/>
	</div>
	<br />

	<div>
		<br />
		<button
			[disabled]="f.invalid || isLoading"
			type="submit"
			class="btn btn-danger text-white"
		>
			<span class="mr-2" *ngIf="isLoading"
				><i class="fas fa-spin fa-spinner"></i
			></span>
			{{ 'user.misc.reinitPass' | translate }}
		</button>
		ou <a class="link" routerLink="/">{{ 'user.misc.login' | translate }}</a>
		<br />
		<div *ngIf="msg" class="form-group col-12">
			<br />
			<p class="text-info font-bold">{{ msg }}</p>
		</div>
	</div>
</form>
