import {
	ChangeDetectorRef,
	Component,
	OnInit,
	ViewChild,
	ViewEncapsulation
} from '@angular/core';
import { Location } from '@angular/common';
import {
	AgoraClient,
	ClientEvent,
	NgxAgoraService,
	Stream,
	StreamEvent
} from 'ngx-agora';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { User, UserRoleType } from '~/app/models/user.model';
import { UserService } from '~/app/services/users/user.service';
import { MeetingsService } from '~/app/services/meetings/meetings.service';
import { Meetings, MeetingStatus } from '~/app/models/meetings';
import { UtilsService } from '~/app/services/utils.service';
import { HomeLoginComponent } from '~/app/home/<USER>/home-login.component';
import { ToastrService } from 'ngx-toastr';
import { filter } from 'rxjs/operators';
import { PrimeNGConfig } from 'primeng/api';
import { MeetingParticipant } from '~/app/models/meeting-participant.model';
import { MeetingSocketService } from '~/app/services/websockets/meetings/meeting-socket.service';
import { TranslateService } from '@ngx-translate/core';
import * as moment from 'moment-timezone';
import { AbonnementsService } from '~/app/services/abonnements/abonnements.service';

@Component({
	selector: 'app-meetings-video',
	templateUrl: './meetings-video.component.html',
	styleUrls: ['./meetings-video.component.scss'],
	encapsulation: ViewEncapsulation.None
})
export class MeetingsVideoComponent implements OnInit {
	@ViewChild('videoElement') videoElement: any;
	meeting: Meetings = new Meetings();
	meetingStatus = MeetingStatus;
	video: any;
	mediaStreams: MediaStream;
	visibleSidebar2 = false;
	visibleSidebarRemoteUser = false;
	micOn = false;
	videoOn = false;
	userJoinCall = false;
	user: User = new User();
	activeSpeaker_remoteId = null;
	activeSpeaker = null;
	remoteUserSelected = null;

	userNotConnectedModal = false;
	meetingErrorModal = false;
	currentTimeZone = '';
	currentStartDateWithCurrentTimeZone = null;
	currentEndDateWithCurrentTimeZone = null;
	endDateMeeting: Date = null;
	restTime = null;
	intervalId = null;

	// AGORA
	uid = 'ZAZA';
	localCallId = 'agora_local';
	private client: AgoraClient;
	localStream: Stream;
	remoteCalls: string[] = [];
	remoteCallsUsers: any[] = [];
	participants: [];
	displayInvitationModal: boolean;
	loadingSaveEmail: boolean;
	mettingsParticipantsEmails: string[];
	username: string;
	passcodeRequired: number = 0;
	selectedValue: string = 'val1';
	showEmailInvitationCard: boolean;
	loadingSaveMeetingType: boolean;

	constructor(
		public _location: Location,
		private agoraService: NgxAgoraService,
		private router: Router,
		private userService: UserService,
		private activatedRoute: ActivatedRoute,
		private meetingService: MeetingsService,
		private utilsService: UtilsService,
		private toastrService: ToastrService,
		private primengConfig: PrimeNGConfig,
		private meetingSocketService: MeetingSocketService,
		private translateService: TranslateService,
		private abonnementService: AbonnementsService
	) {
		this.displayInvitationModal = false;
		this.loadingSaveEmail = false;
		this.mettingsParticipantsEmails = [] = [];
		this.showEmailInvitationCard = false;
		this.loadingSaveMeetingType = false;
	}

	async ngOnInit() {
		this.currentTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

		// Init socket listeners
		this.meetingSocketService
			.listen('welcome')
			.subscribe((data) => console.log(data));

		this.meetingSocketService.ListenMeetingStarting().subscribe(() => {
			alert('La reunion a commencé, Je me connect !');
			// Traitement à effectuer, pour connecter l'utilisateur à la video conférence
			// lorsque celui qui a créer le meeting Démare la scéance
		});

		this.primengConfig.ripple = true;

		const slug = this.activatedRoute.snapshot.params.slug;
		const meetingUserName = this.activatedRoute.snapshot.queryParams[
			'username'
		];
		this.username = meetingUserName;
		// We get User connected
		try {
			const result = await this.userService.getUserConnected().toPromise();
			this.user = {
				...result,
				...this.userService.formatUserFields(result),
				Username: meetingUserName
			};
		} catch (error) {
			console.log('user error', error);
			// We fake fake value for Id and Username for public meeting
			this.user = {
				...new User(),
				Id: new Date().getTime(),
				Username: meetingUserName,
				Slug: null,
				...this.userService.formatUserFields(new User())
			};
			console.log('Connected User: ', this.user);
		}

		if (this.user) {
			/* const abonnement = await this.abonnementService
				.getUserActiveAbonnement(this.user.Slug)
				.toPromise();
			if (abonnement?.Forfait?.Name.toLowerCase() !== 'premium') {
				if (this.user.Role != UserRoleType.ADMIN) {
					this.router.navigateByUrl('/home/<USER>');
					this.toastrService.warning(
						'Brain maker',
						'Veuillez chosoir un forfait adequat pour avoir accès au meeting'
					);
				}
			} */
		} else {
			this.utilsService.buildModal(HomeLoginComponent, () => {}, 500, true, {});
		}

		// We get current meeting
		try {
			this.meeting = await this.meetingService.getBySlug(slug).toPromise();
			this.endDateMeeting = new Date(this.meeting.EndTime);

			this.showEmailInvitationCard = this.meeting.DurationMin != null;

			const startDateTimeMeeting = new Date(this.meeting.StartDate).setHours(
				Number(this.meeting.StartTime.split(':')[0]),
				Number(this.meeting.StartTime.split(':')[1])
			);

			const startDateWithTimeZone = moment.tz(
				startDateTimeMeeting,
				this.meeting.Timezone
			);
			const endDateWithTimeZone = moment.tz(
				this.meeting.EndTime,
				this.meeting.Timezone
			);

			this.currentStartDateWithCurrentTimeZone = startDateWithTimeZone
				.clone()
				.tz(this.currentTimeZone);
			this.currentEndDateWithCurrentTimeZone = endDateWithTimeZone
				.clone()
				.tz(this.currentTimeZone);

			console.log('Running meeting', this.meeting);
			if (this.meeting) {
				const p: string[] = [];
				this.meeting.Participants?.map((pItem: MeetingParticipant) => {
					console.log('****', pItem.Email);
					p.push(pItem.Email);
				});
				this.mettingsParticipantsEmails = p;
				console.log(
					'all participants emails: ',
					p,
					this.mettingsParticipantsEmails
				);
			}
		} catch (error) {
			this.meetingErrorModal = true;
			console.log('metting error', error);
			return;
		}

		// We check if passcode is required
		if (this.meeting.RequirePasscode) {
			// The passcode is required but the user is not connected
			/* if ((this.user.Id != this.meeting.Owner.Id) && !this.user.Slug) {
				this.userNotConnectedModal = true;
				return;
			} */

			const passcode = this.activatedRoute.snapshot.queryParams['passcode'];
			if (
				this.meeting.Passcode !== passcode &&
				this.meeting.Owner.Id !== this.user.Id
			) {
				this.router
					.navigate([`/meetings/join/${this.meeting.Slug}`])
					.then(() => {
						this.toastrService.error(
							'Veuillez entrer le passcode que vous avez reçu pour rejoindre cette conférence',
							'Passcode incorrect'
						);
					});
			}
		}

		this.videoOn =
			this.activatedRoute.snapshot.queryParams['isVideo'] == 'true'
				? true
				: false;
		this.micOn =
			this.activatedRoute.snapshot.queryParams['isAudio'] == 'true'
				? true
				: false;

		if (
			this.meeting.Status != this.meetingStatus.FINISHED &&
			this.meeting.Status != this.meetingStatus.SCHEDULED
		) {
			this.client = this.agoraService.createClient({
				mode: 'rtc',
				codec: 'h264'
			});
			console.log('CREATE CLIENT', this.client);
			this.assignClientHandlers();

			this.initStandardStream();
		}

		this.intervalId = setInterval(() => {
			this.countRestTime();
		}, 1000);
	}

	initStandardStream(shouldUnpublishLocalStream = false) {
		if (shouldUnpublishLocalStream) this.client.unpublish(this.localStream);

		if (
			this.meeting.Status != this.meetingStatus.FINISHED &&
			this.meeting.Status != this.meetingStatus.SCHEDULED
		) {
			// Added in this step to initialize the local A/V stream
			this.localStream = this.agoraService.createStream({
				streamID: String(this.user.Id),
				audio: true,
				video: true,
				screen: false
			});
			this.assignLocalStreamHandlers();

			// Join and publish methods added in this step
			this.initLocalStream(() =>
				this.join(
					async (uid) => {
						this.userJoinCall = true;
						this.publish();
					},
					(error) => console.error('ERROR JOIN CALL', error)
				)
			);
		}
	}

	countRestTime() {
		//console.log(Intl.DateTimeFormat().resolvedOptions().timeZone
		const actualMoment = moment().tz(this.currentTimeZone);

		if (actualMoment.isAfter(this.currentEndDateWithCurrentTimeZone)) {
			//new Date().getTime() > this.endDateMeeting.getTime()
			this.restTime = `Time reached`;
			clearInterval(this.intervalId);
			return;
		}

		const intervalTime =
			this.currentEndDateWithCurrentTimeZone.toDate().getTime() -
			actualMoment.toDate().getTime();
		const intervalDate = new Date(intervalTime);

		const restHours =
			intervalDate.getHours() - 1 > 0 ? `${intervalDate.getHours() - 1}h:` : '';
		const restMinutes =
			intervalDate.getMinutes() > 0 ? `${intervalDate.getMinutes()}min:` : '';

		this.restTime = `${restHours}${restMinutes}${intervalDate.getSeconds()}s`;
	}

	initCamera(config: any): void {
		const self = this;
		navigator.mediaDevices
			.getUserMedia(config)
			.then((mediaStream) => {
				const video = document.getElementById('videoParent') as any;
				if (mediaStream.active) {
					video.srcObject = mediaStream;

					video.onloadedmetadata = () => {
						video.play();
					};
				}
				self.mediaStreams = mediaStream;
				console.log('open');
				// if (self.stopVideo) {
				//   let tracks = mediaStream.getTracks().reverse();
				//   console.log('close');
				//   tracks.forEach(function (track) {
				//     console.log(track);
				//     track.stop();

				//   });
				// }
			})
			.catch((err) => {
				console.log(err.name + ': ' + err.message);
			});
	}

	formatDate(date) {
		return new Date(date).toDateString();
	}

	formaTime(date) {
		return new Date(date).toTimeString();
	}

	shareScreen(): void {
		this.localStream.stop();
		this.client.unpublish(this.localStream);

		this.localStream = this.agoraService.createStream({
			streamID: String(this.user.Id),
			audio: true,
			video: false,
			screen: true
		});
		this.assignLocalStreamHandlers();

		this.localStream.init(
			() => {
				// The user has granted access to the camera and mic.
				this.localStream.play(this.localCallId);
				this.publish();
			},
			(err) => console.error('getUserMedia failed', err)
		);

		this.localStream.on(StreamEvent.ScreenSharingStopped, (evt) => {
			console.log('Media sharing stopped');
			this.cancelShareScreen();
		});
	}

	cancelShareScreen() {
		this.localStream.stop();
		this.client.unpublish(this.localStream);

		this.localStream = this.agoraService.createStream({
			streamID: String(this.user.Id),
			audio: true,
			video: true,
			screen: false
		});
		this.assignLocalStreamHandlers();

		this.localStream.init(
			() => {
				// The user has granted access to the camera and mic.
				this.localStream.play(this.localCallId);
				this.publish();
			},
			(err) => console.error('getUserMedia failed ', err)
		);
	}

	toogleMic(): void {
		if (this.localStream.isAudioOn()) this.localStream.muteAudio();
		else this.localStream.unmuteAudio();

		this.micOn = this.localStream.isAudioOn();
	}

	toogleVid(): void {
		if (this.localStream.isVideoOn()) this.localStream.muteVideo();
		else this.localStream.unmuteVideo();

		this.videoOn = this.localStream.isVideoOn();
	}

	/*********************************************** AGORA ************************************************************** */
	private assignClientHandlers(): void {
		this.client.on(ClientEvent.LocalStreamPublished, (evt) => {
			console.log('Publish local stream successfully');
		});

		this.client.on(ClientEvent.Error, (error) => {
			console.log('Got error msg:', error.reason);
			if (error.reason === 'DYNAMIC_KEY_TIMEOUT') {
				this.client.renewChannelKey(
					'',
					() => console.log('Renewed the channel key successfully.'),
					(renewError) =>
						console.error('Renew channel key failed: ', renewError)
				);
			}
		});

		this.client.on(ClientEvent.RemoteStreamAdded, (evt) => {
			console.log('remote added');
			const stream = evt.stream as Stream;
			this.client.subscribe(stream, { audio: true, video: true }, (err) => {
				console.log('Subscribe stream failed', err);
			});
		});

		this.client.on(ClientEvent.RemoteStreamSubscribed, async (evt) => {
			console.log('RemoteStreamSubscribed');
			const stream = evt.stream as Stream;
			const remoteId = this.getRemoteId(stream); // Exple: agora_remote-Otis-1
			console.log(
				'***********************************************************'
			);
			console.log(
				'RemoteStreamSubscribed WITH ID ==============>>>>>>>>>>>>>>>  ',
				remoteId
			);
			console.log('**********************************************************');

			this.remoteCalls.push(remoteId);

			const remoteIdSplit = remoteId.split('-');
			const remoteMeetingUsername = remoteIdSplit[1];
			const remoteUserId = remoteIdSplit[2];

			let userRemoteGet: User = {
				...new User(),
				Id: Number(remoteUserId),
				Username: remoteMeetingUsername,
				Slug: null
			};

			try {
				const resultGetUser = await this.userService
					.getById(Number(remoteUserId))
					.toPromise();

				userRemoteGet = resultGetUser;
			} catch (error) {
				console.log('ERROR TO GET ROMOTE USER IN DATABASE', error);
			}

			const isUserAlreadyThere =
				this.remoteCallsUsers.findIndex(
					(userRemote) => userRemote.Id == userRemoteGet.Id
				) != -1;

			if (!isUserAlreadyThere) {
				if (this.meeting.Owner.Id == userRemoteGet.Id) {
					this.remoteCallsUsers.unshift({
						...userRemoteGet,
						...this.userService.formatUserFields(userRemoteGet),
						Username: remoteMeetingUsername,
						callId: remoteId,
						stream: stream
					});
				} else {
					this.remoteCallsUsers.push({
						...userRemoteGet,
						...this.userService.formatUserFields(userRemoteGet),
						Username: remoteMeetingUsername,
						callId: remoteId,
						stream: stream
					});
				}

				//stream.muteVideo();

				setTimeout(() => {
					stream.play(String('sound-' + remoteId));
				}, 1000);
			}

			/* if (!this.activeSpeaker) {

				setTimeout(() => stream.play(String(remoteId)), 1000);
			} */
		});

		this.client.on(ClientEvent.RemoteStreamRemoved, (evt) => {
			const stream = evt.stream as Stream;
			if (stream) {
				stream.stop();

				const remoteIdSplit = this.getRemoteId(stream).split('-');
				const remoteUserId = remoteIdSplit[2];

				//this.remoteCalls = this.remoteCalls.filter(call => call !== `${this.getRemoteId(stream)}`);
				//this.remoteCallsUsers = this.remoteCallsUsers.filter(user => user.Id !== Number(remoteUserId));

				console.log(`Remote stream is removed ${stream.getId()}`);
			}
		});

		this.client.on(ClientEvent.PeerLeave, (evt) => {
			const stream = evt.stream as Stream;
			if (stream) {
				stream.stop();

				const remoteIdSplit = this.getRemoteId(stream).split('-');
				const remoteUserId = remoteIdSplit[2];

				this.remoteCalls = this.remoteCalls.filter(
					(call) => call !== `${this.getRemoteId(stream)}`
				);
				this.remoteCallsUsers = this.remoteCallsUsers.filter(
					(user) => user.Id !== Number(remoteUserId)
				);

				this.activeSpeaker = null;

				console.log(`${evt.uid} left from this channel`);
			}
		});

		this.client.on(ClientEvent.ActiveSpeaker, (evt) => {
			console.log(
				'=========================  ACTIVE SPEAKER  ================================'
			);
			console.log(evt);
			console.log(
				'=========================  ACTIVE SPEAKER  ==============================='
			);

			if (this.activeSpeaker) {
				this.activeSpeaker.stream.close();
			}

			if (evt.uid) {
				this.activeSpeaker = this.remoteCallsUsers.find(
					(userR) => userR.callId == `agora_remote-${evt.uid}`
				);

				if (this.activeSpeaker) {
					this.activeSpeaker.callId = 'active-' + this.activeSpeaker.callId;

					//this.activeSpeaker.stream.unmuteVideo();
					setTimeout(
						() =>
							this.activeSpeaker.stream.play(String(this.activeSpeaker.callId)),
						1000
					);

					this.remoteCallsUsers.forEach((userR) => {
						setTimeout(
							() => userR.stream.play(String('sound-' + userR.callId)),
							1000
						);
					});
				}
			} else {
				this.activeSpeaker = null;
			}
		});
	}

	private getRemoteId(stream: Stream): string {
		return `agora_remote-${stream.getId()}`;
	}

	private assignLocalStreamHandlers(): void {
		this.localStream.on(StreamEvent.MediaAccessAllowed, () => {
			console.log('accessAllowed');
		});

		// The user has denied access to the camera and mic.
		this.localStream.on(StreamEvent.MediaAccessDenied, () => {
			console.log('accessDenied');
			this.cancelShareScreen();
		});
	}

	private initLocalStream(onSuccess?: () => any): void {
		this.localStream.init(
			() => {
				// The user has granted access to the camera and mic.
				this.localStream.play(this.localCallId);

				// INITIALIZE VIDEO BY ACTIVATE OR DEACTIVATE
				if (!this.videoOn) {
					this.localStream.muteVideo();
					this.videoOn = this.localStream.isVideoOn();
				} else {
					this.localStream.unmuteVideo();
					this.videoOn = this.localStream.isVideoOn();
				}

				// INITIALIZE AUDIO BY ACTIVATE OR DEACTIVATE
				if (!this.micOn) {
					this.localStream.muteAudio();
					this.micOn = this.localStream.isAudioOn();
				} else {
					this.localStream.unmuteAudio();
					this.micOn = this.localStream.isAudioOn();
				}

				if (onSuccess) {
					onSuccess();
				}
			},
			(err) => console.error('getUserMedia failed', err)
		);
	}

	/**
	 * Attempts to connect to an online chat room where users can host and receive A/V streams.
	 */
	join(
		onSuccess?: (uid: number | string) => void,
		onFailure?: (error: Error) => void
	): void {
		console.log(`JOIN "${this.meeting.Topic}" channel`);
		this.client.join(
			null,
			this.meeting.Topic,
			`${this.user.Username}-${this.user.Id}`,
			onSuccess,
			onFailure
		);
	}

	onJoinSuccess(uid: number | string) {
		console.log('JOIN CALL METTING =========> UID = ', uid);
	}

	onJoinError(error) {
		console.log('ERROR JOIN CALL METTING =========> UID = ', error);
	}

	/**
	 * Attempts to upload the created local A/V stream to a joined chat room.
	 */
	publish(): void {
		this.client.publish(this.localStream, (err) =>
			console.log('Publish local stream error: ' + err)
		);
	}
	/*********************************************** AGORA ************************************************************** */

	endCall(backToPrevious = false) {
		console.log('END CALL');

		if (this.client && this.localStream) {
			//this.client.unpublish(this.localStream);

			this.agoraService.client.leave(
				() => {
					console.log('Leavel channel successfully');
					this.userJoinCall = false;
					this.localStream.close();
					this.localStream.stop();

					this.client = null;
					this.localStream = null;

					this.router
						.navigateByUrl(`/meetings/join/${this.meeting.Slug}`)
						.then((result) => {
							window.location.reload();
						});
				},
				(err) => {
					console.log('Leave channel failed', err);
					this.localStream.close();
					this.localStream.stop();

					this.client = null;
					this.localStream = null;

					this.router
						.navigateByUrl(`/meetings/join/${this.meeting.Slug}`)
						.then((result) => {
							window.location.reload();
						});
				}
			);

			//this.localStream.close();
			//this.localStream.stop();
		} else {
			this.router.navigateByUrl(`/meetings/join/${this.meeting.Slug}`);
		}
	}

	openLoginModal(e): void {
		e.preventDefault();
		this.userNotConnectedModal = false;
		const dialogRef = this.utilsService.buildModal(
			HomeLoginComponent,
			() => {},
			500,
			true,
			{}
		);
	}

	toogleSidebarRemoteUser() {
		this.visibleSidebarRemoteUser = !this.visibleSidebarRemoteUser;
	}

	selectRemoteUserFromSide(userRemote) {
		if (this.activeSpeaker) {
			this.activeSpeaker.stream.stop();
		}

		if (this.remoteUserSelected) {
			this.remoteUserSelected.stream.stop();
		}

		this.remoteUserSelected = userRemote;
		this.remoteUserSelected.stream.unmuteVideo();
		setTimeout(
			() =>
				this.remoteUserSelected.stream.play(
					String(this.remoteUserSelected.callId)
				),
			1000
		);
	}

	cancelRemoteSelection() {
		this.remoteUserSelected.stream.stop();
		this.remoteUserSelected = null;

		if (this.activeSpeaker) {
			setTimeout(
				() => this.activeSpeaker.stream.play(String(this.activeSpeaker.callId)),
				1000
			);
		}

		this.remoteCallsUsers.forEach((userR) => {
			setTimeout(
				() => userR.stream.play(String('sound-' + userR.callId)),
				1000
			);
		});
	}

	toogleFitScreenLocalStream() {
		document.getElementById(this.localCallId).classList.toggle('fitScreen');
	}

	toogleFitScreenRemoteStream(remoteId) {
		document.getElementById(remoteId).classList.toggle('fitScreen');
	}

	getActiveSpeakerFromRemoteCallUsers() {
		return (
			this.remoteCallsUsers.filter(
				(userR) => userR.callId == this.activeSpeaker_remoteId
			) || []
		);
	}

	ngOnDestroy(): void {
		//Called once, before the instance is destroyed.
		//Add 'implements OnDestroy' to the class.
		clearInterval(this.intervalId);

		if (this.client && this.localStream) {
			//this.client.unpublish(this.localStream);

			this.agoraService.client.leave(
				() => {
					console.log('Leavel channel successfully');
					this.userJoinCall = false;
					this.localStream.close();
					this.localStream.stop();
					//this._location.back();

					this.router
						.navigateByUrl(`/meetings/join/${this.meeting.Slug}`)
						.then((result) => {
							window.location.reload();
						});
				},
				(err) => {
					console.log('Leave channel failed', err);
					this.localStream.close();
					this.localStream.stop();

					this.router
						.navigateByUrl(`/meetings/join/${this.meeting.Slug}`)
						.then((result) => {
							window.location.reload();
						});
				}
			);
		}
	}

	showInvitaionModal(): void {
		if (this.meeting.Status === MeetingStatus.FINISHED) {
			this.toastrService.success(
				'Ce meeting est terminé, vous ne pouvez plus inviter de participants',
				'Meeting terminé'
			);
			return;
		}

		if (this.meeting.Status === MeetingStatus.STOPED) {
			this.toastrService.success('Ce meeting a été bloqué!', 'Meeting bloqué');
			return;
		}

		this.displayInvitationModal = true;
	}

	async saveMeetingsParticipantsEmail(): Promise<void> {
		// console.log(this.mettingsParticipantsEmails);

		this.loadingSaveEmail = true;
		if (this.mettingsParticipantsEmails.length < 1) {
			this.loadingSaveEmail = false;
			this.toastrService.error(
				'Appuyer sur la touche entrer pour inserer un email',
				'Saisie incorrecte'
			);
			return;
		}

		const participants: MeetingParticipant[] = [];

		for (let email of this.mettingsParticipantsEmails) {
			const exist = this.meeting.Participants?.find(
				(p: MeetingParticipant) => p.Email === email
			);

			const alreadyInList = participants?.find((p) => {
				if (p.Email === email) {
					const idx = this.mettingsParticipantsEmails.indexOf(
						this.mettingsParticipantsEmails.find((e) => e === p.Email)
					);
					console.log('*** duplicate: ', this.mettingsParticipantsEmails[idx]);
					this.mettingsParticipantsEmails = this.mettingsParticipantsEmails.splice(
						idx,
						1
					);
					return true;
				}
			});

			if (exist) {
				if (!alreadyInList) {
					participants.push(exist);
				}
			} else {
				const m = this.meeting;
				// console.log('m - avant: ', m);
				// delete m.Participants;
				// delete m.Owner;
				// delete m.Settings;

				console.log('m - après: ', m);

				const newP = new MeetingParticipant();
				newP.Email = email;
				newP.Meeting = {
					Id: this.meeting.Id,
					Slug: this.meeting.Slug
				};
				newP.EmailSend = false;
				console.log('New p', newP);
				participants.push(newP);
				console.log('select---<', m);
			}
		}

		console.log('Participants |--->', participants);

		const participantsToDelete: MeetingParticipant[] = [];
		const oldParticipants = this.meeting.Participants;
		oldParticipants?.map((oldP) => {
			const exist = participants.find((p) => oldP.Email === p.Email);
			if (!exist) {
				participantsToDelete.push(oldP);
			}
		});
		console.log('old', oldParticipants);
		console.log('to delete', participantsToDelete);

		await Promise.all(
			participantsToDelete?.map(
				async (p): Promise<void> => {
					const res = await this.meetingService
						.deleteParticipant(p.Slug)
						.toPromise();
					console.log('delete res', res);
				}
			)
		);

		participants?.map((p: MeetingParticipant) => {
			this.meeting.Participants?.map((selectedP: MeetingParticipant) => {
				if (selectedP.Email === p.Email) {
					p.EmailSend = selectedP.EmailSend;
				}
			});
		});

		console.log('exécuté...');
		this.meeting.Participants = participants;
		try {
			const updatedMeeting = await this.meetingService
				.update(this.meeting.Slug, this.meeting)
				.toPromise();

			console.log(updatedMeeting);

			// Update selected meeting in the list
			// const index = this.meeting.indexOf(
			// 	this.mettings.find((m) => (m.Id = this.meeting.Id))
			// );
			// this.mettings[index] = updatedMeeting;
			this.loadingSaveEmail = false;
			this.displayInvitationModal = false;

			this.toastrService.success(
				'Les participants ont été ajouté avec succès',
				'Participants ajouté'
			);
		} catch (error) {
			console.log(error);
		} finally {
			this.loadingSaveEmail = false;
		}
	}

	async selectMeetingType(): Promise<void> {
		console.log('passcode is required: ', this.passcodeRequired);

		if (this.passcodeRequired == 0) {
			console.log('sans passcode');
			this.meeting.RequirePasscode = false;
			this.showEmailInvitationCard = true;
		} else {
			console.log('Avec passcode');
			this.meeting.RequirePasscode = true;
			this.meeting.Passcode = this.utilsService.getUniqueId(8);
		}

		try {
			this.loadingSaveMeetingType = true;
			this.meeting = await this.meetingService
				.update(this.meeting.Slug, this.meeting)
				.toPromise();
			this.loadingSaveMeetingType = true;
			this.showEmailInvitationCard = true;
		} catch (error) {
			this.toastrService.error("Impossible, d'enregistrer le type du meeting");
			this.loadingSaveMeetingType = true;
		}

		console.log(this.meeting);
	}

	getMeetingLink(meeting: Meetings): string {
		return `meetings/join/${meeting.Slug}`;
	}

	async copyMeetingPassCode(): Promise<void> {
		// const message = await this.translateService
		// 	.get('meeting.messages')
		// 	.toPromise();

		if (this.utilsService.copyTextToClipboard(this.meeting.Passcode)) {
			this.toastrService.info('Le passcode a été copé ', 'Message');
		} else {
			this.toastrService.error("Une erreur es'est produit", 'Message');
		}
	}

	async copyMeetingLink(): Promise<void> {
		const message = await this.translateService
			.get('meeting.messages')
			.toPromise();

		if (
			this.utilsService.copyTextToClipboard(
				`${location.protocol}//${location.hostname}:${
					location.port
				}/${this.getMeetingLink(this.meeting)}`
			)
		) {
			this.toastrService.info('Le lien a été copié', 'Message');
		} else {
			this.toastrService.error("Une erreur es'est produit", 'Message');
		}
	}
}
