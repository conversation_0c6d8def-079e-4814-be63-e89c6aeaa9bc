import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { MeetingsJoinModalComponent } from '../meetings-join-modal/meetings-join-modal.component';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { ConfirmationService, MessageService } from 'primeng/api';
import { TranslateService } from '@ngx-translate/core';
import { Meetings, MeetingStatus } from '~/app/models/meetings';
import { MeetingsService } from '~/app/services/meetings/meetings.service';
import { UtilsService } from '~/app/services/utils.service';
import { User, UserRoleType } from '~/app/models/user.model';
import { UserService } from '~/app/services/users/user.service';
import { HomeLoginComponent } from '~/app/home/<USER>/home-login.component';
import { AbonnementsService } from '~/app/services/abonnements/abonnements.service';
// import * as io from 'socket.io-client';
// import { SocketServerService } from '~/app/services/socket-server.service';
// import { MeetingSocketService } from '~/app/services/websockets/meetings/meeting-socket.service';

@Component({
	selector: 'app-meetings-home',
	templateUrl: './meetings-home.component.html',
	styleUrls: ['./meetings-home.component.scss'],
	encapsulation: ViewEncapsulation.None,
	providers: [ConfirmationService, MessageService, DialogService]
})
export class MeetingsHomeComponent implements OnInit {
	options = [];
	ref: DynamicDialogRef;
	mediaStreams: MediaStream;

	userNotConnectedModal = true;
	homeMeeting: Meetings;
	loadingHomeMeeting: boolean = false;
	user: User;
	loadingStartMeetingImmediately: boolean;

	constructor(
		public dialogService: DialogService,
		private toastr: ToastrService,
		private router: Router,
		private translateService: TranslateService,
		private confirmationService: ConfirmationService,
		private messageService: MessageService,
		private meetingService: MeetingsService,
		private utilService: UtilsService,
		private userService: UserService,
		private abonnementService: AbonnementsService // private meetingSocketservice: MeetingSocketService
	) {
		this.loadingStartMeetingImmediately = false;
	}

	async ngOnInit(): Promise<void> {
		// console.log('appel des routes de la socket --> ');
		// this.meetingSocketservice.listen('welcome').subscribe((data) => {
		// 	console.log('Test welcome', data);
		// });
		const menu = await this.translateService.get('meeting.options').toPromise();

		if (this.userService.isConnected()) {
			this.user = await this.userService.getUserConnected().toPromise();
		}

		if (this.user) {
			const abonnement = await this.abonnementService
				.getUserActiveAbonnement(this.user.Slug)
				.toPromise();

			if (abonnement?.Forfait?.Name.toLowerCase() !== 'premium') {
				if (this.user.Role != UserRoleType.ADMIN) {
					this.router.navigateByUrl('/home/<USER>');
					this.toastr.warning(
						'Brain maker',
						'Veuillez chosoir un forfait adequat pour avoir accès au meeting'
					);
				}
			}
		} else {
			this.utilService.buildModal(HomeLoginComponent, () => {}, 500, true, {});
		}

		this.options = [
			{ name: menu.copyMeeting, icon: 'delete', id_option: 1 },
			{ name: menu.modify, icon: 'payment', id_option: 2 },
			{ name: menu.delete, icon: 'payment', id_option: 3 }
		];

		try {
			this.loadingHomeMeeting = true;
			const res = await this.meetingService.getLazy(1, 0).toPromise();
			this.homeMeeting = res[0];
			console.log('show Options', this.showMeetingOptions());
			console.log('Home meeting', this.homeMeeting);
			this.loadingHomeMeeting = false;
		} catch (error) {
			this.homeMeeting = null;
			this.loadingHomeMeeting = false;
			this.toastr.error(
				'Une erreur est survenue lors du chargement',
				'Impossible de charcher le meeting'
			);
		}
	}

	async selectedOptionFxn(option, event: Event): Promise<void> {
		console.log(option);
		const message = await this.translateService
			.get('meeting.messages')
			.toPromise();

		if (option.id_option === 1) {
			if (this.copyTextToClipboard('le lien vers ma reunion')) {
				this.toastr.info(message.linkCopied, 'Message');
			} else {
				this.toastr.error(message.copyError, 'Message');
			}
		}
		if (option.id_option === 2) {
			this.router.navigate(['/meetings/create']);
		}
		if (option.id_option === 3) {
			this.confirmationService.confirm({
				target: event.target,
				message: message.confirmDelete,
				icon: 'pi pi-exclamation-triangle',
				accept: () => {
					this.messageService.add({
						severity: 'info',
						summary: 'Action',
						detail: message.deletOk
					});
				},
				reject: () => {
					this.messageService.add({
						severity: 'error',
						summary: 'Action',
						detail: message.deleteNoteDone
					});
				}
			});
		}
	}

	async openJoinModal(): Promise<void> {
		console.log('Ouverture de la modal');
		const message = await this.translateService.get('meeting.page').toPromise();

		this.ref = this.dialogService.open(MeetingsJoinModalComponent, {
			header: message.joinMeeting,
			width: '50%',
			contentStyle: { 'max-height': '500px', overflow: 'auto' },
			baseZIndex: 10000
		});

		this.ref.onClose.subscribe((product) => {
			if (product) {
			}
		});
	}
	async openShareScreen(): Promise<void> {
		const message = await this.translateService.get('meeting.page').toPromise();

		this.ref = this.dialogService.open(MeetingsJoinModalComponent, {
			header: message.joinMeeting,
			width: '40%',
			contentStyle: { 'max-height': '500px', overflow: 'auto' },
			baseZIndex: 10000
		});

		this.ref.onClose.subscribe((product) => {
			if (product) {
			}
		});
	}
	copyTextToClipboard(text): boolean {
		const txtArea = document.createElement('textarea');
		txtArea.id = 'txt';
		txtArea.style.position = 'fixed';
		txtArea.style.top = '0';
		txtArea.style.left = '0';
		txtArea.style.opacity = '0';
		txtArea.value = text;
		document.body.appendChild(txtArea);
		txtArea.select();

		try {
			const successful = document.execCommand('copy');
			const msg = successful ? 'successful' : 'unsuccessful';
			console.log('Copying text command was ' + msg);
			if (successful) {
				return true;
			}
		} catch (err) {
			console.log('Oops, unable to copy');
		} finally {
			document.body.removeChild(txtArea);
		}
		return false;
	}

	getMeetingLink(meeting: Meetings): string {
		return `/meetings/join/${meeting.Slug}`;
	}

	formatDate(date: Date) {
		return this.utilService.FormatDateToDayMonthYear(new Date(date));
	}

	showMeetingOptions(): boolean {
		if (!this.user) {
			return false;
		}

		if (
			this.user &&
			this.homeMeeting &&
			this.homeMeeting.Owner &&
			this.user.Id === this.homeMeeting.Owner.Id
		) {
			return true;
		}
		return false;
	}

	async startMeetingImmediately(): Promise<void> {
		// Création d'un meeting avec un status RUNNING
		this.loadingStartMeetingImmediately = false;
		const today = new Date();
		const meeting = new Meetings();
		meeting.Owner = this.user;
		meeting.StartDate = today;
		meeting.TimePeriod = false ? 'PM' : 'AM';
		meeting.StartTime =
			meeting.TimePeriod === 'PM'
				? `${today.getHours() - 12}:${today.getMinutes()}`
				: `${today.getHours()}:${today.getMinutes()}`;
		meeting.Status = MeetingStatus.RUNNING;
		meeting.MettingId = this.utilService.getUniqueId(8);
		meeting.Settings = {
			Join: false,
			Mute: false,
			AutoRecord: false,
			ApproveBlock: false
		};
		meeting.Topic = `${meeting.MettingId} - ${
			this.user.Firstname + ' ' + this.user.Lastname
		}`;

		try {
			const saved = await this.meetingService.schedule(meeting).toPromise();
			if (saved) {
				this.router.navigateByUrl(
					`/meetings/meeting/${
						saved.Slug
					}?isVideo=true&isAudio=true&&username=${
						this.user.Firstname + ' ' + this.user.Lastname
					}`
				);
			}
			this.loadingStartMeetingImmediately = false;
		} catch (error) {
			console.log('error');
			this.toastr.error(
				'Une erreur est survenue lors de la création du meeting instantané',
				'Impossible de créer le meeting'
			);
			this.loadingStartMeetingImmediately = false;
		}
	}
}
