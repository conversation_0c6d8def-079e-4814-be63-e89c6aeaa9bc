.grid-container {
	display: grid;
	grid-template-columns: 100px 100px;
	grid-template-rows: 100px 100px;
	gap: 20px 20px;
	grid-template-areas: '. .' '. .';
	justify-items: center;
	align-items: center;
	align-content: center;
	height: 100%;
}

.icon-card {
	height: 10vh;
	width: 100px;
	padding: 0;
	display: flex;
	align-items: center;
	justify-content: center;
}

.main-card-color {
	background-color: #f33066;
}

.alt-card-color {
	background-color: #0e0d0d;
}

.icon-card img {
	height: 30px;
	// width: 60px;
}

.card-cover {
	position: absolute;
	top: 0;
	height: 100%;
	width: 100%;
	background: #0606064a;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	color: white;
}

.p-dialog {
	max-height: 100% !important;
}

// .p-dialog.p-dynamic-dialog{
// }
// .p-dynamic-dialog {
//     width: 40% !important;
// }

.manage-btn {
	margin-left: 4px;
}
