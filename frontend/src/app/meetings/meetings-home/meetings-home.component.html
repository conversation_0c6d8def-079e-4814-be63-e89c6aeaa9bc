<div class="red-skin">
	<div id="main-wrapper">
		<app-home-header></app-home-header>
		<app-mettings-nav-tab [page]="'home'"></app-mettings-nav-tab>
		<section class="container">
			<div class="row" style="margin-top: 100px">
				<div class="col-sm-12 col-lg-6 d-flex justify-content-center">
					<div class="grid-container">
						<div
							class="text-center d-flex flex-column align-items-center justify-content-center"
						>
							<div
								(click)="startMeetingImmediately()"
								class="card icon-card mb-1 pointer main-card-color"
							>
								<img src="/assets/img/vid-camera.svg" alt="" srcset="" />
							</div>
							<small>{{ 'meeting.page.newMeeting' | translate }}</small>
						</div>
						<div
							class="text-center d-flex flex-column align-items-center justify-content-center"
						>
							<div
								(click)="openJoinModal()"
								class="card icon-card mb-1 pointer alt-card-color"
							>
								<img src="/assets/img/plus.svg" alt="" srcset="" />
							</div>
							<small>{{ 'meeting.page.join' | translate }}</small>
						</div>
						<div
							routerLink="/meetings/create"
							class="text-center d-flex flex-column align-items-center justify-content-center"
						>
							<div class="card icon-card mb-1 pointer alt-card-color">
								<img src="/assets/img/calendar.svg" alt="" srcset="" />
							</div>
							<small>{{ 'meeting.page.program' | translate }}</small>
						</div>
						<div
							class="text-center d-flex flex-column align-items-center justify-content-center"
						>
							<div
								(click)="openShareScreen()"
								class="card icon-card mb-1 pointer alt-card-color"
							>
								<img src="/assets/img/share.svg" alt="" srcset="" />
							</div>
							<small>{{ 'meeting.page.shareScreen' | translate }}</small>
						</div>
					</div>
				</div>
				<div class="col-sm-12 col-lg-4 d-flex align-items-center">
					<div class="card" *ngIf="homeMeeting; else noMeeting">
						<div class="card-header pl-0">
							<img
								src="/assets/img/meeting_cover.svg"
								alt=""
								srcset=""
								style="height: 150px; width: 100%"
							/>
							<div class="card-cover">
								<h1 class="text-white">
									{{ homeMeeting ? homeMeeting.StartTime : '' }}
								</h1>
								<p>
									{{ homeMeeting ? formatDate(homeMeeting.StartDate) : '' }}
								</p>
							</div>
						</div>
						<div class="card-body">
							<h2 class="font-bold">
								<span>{{ homeMeeting ? homeMeeting.Topic : '' }}</span>
								<i
									*ngIf="showMeetingOptions()"
									[matMenuTriggerFor]="menu"
									class="pi pi-ellipsis-v pointer right"
								></i>
							</h2>
							<!-- <mat-menu #menu="matMenu">
								<button
									mat-menu-item
									*ngFor="let option of options"
									(click)="selectedOptionFxn(option, $event)"
								>
									<span>{{ option.name }}</span>
								</button>
							</mat-menu> -->
							<p *ngIf="homeMeeting.DurationHour && homeMeeting.DurationMin">
								Duree:
								{{
									homeMeeting
										? homeMeeting.DurationHour +
										  'H ' +
										  homeMeeting.DurationMin +
										  'min'
										: ''
								}}
							</p>
							<p>
								{{ 'meeting.page.idMeeting' | translate }}:
								{{ homeMeeting ? homeMeeting.MettingId : '' }}
							</p>
							<div class="d-flex">
								<button
									[routerLink]="
										homeMeeting ? '/meetings/join/' + homeMeeting.Slug : ''
									"
									class="btn btn-sm btn-primary"
								>
									{{ 'meeting.page.start' | translate }}
								</button>
								<button
									*ngIf="showMeetingOptions()"
									[routerLink]="
										homeMeeting ? '/meetings/list/' + homeMeeting.Slug : ''
									"
									class="ml-4 btn btn-sm btn-primary"
								>
									gérer
								</button>
							</div>
						</div>
						<div class="card-footer">
							<small>{{ 'meeting.page.nextMeeting' | translate }} (1)</small>
						</div>
					</div>

					<ng-template #noMeeting>
						<div class="card mt-4" #noMeeting>
							<img
								src="/assets/img/meeting_cover.svg"
								alt=""
								srcset=""
								style="height: 300px; width: 100%"
							/>
						</div>
					</ng-template>
					<!-- <video preload="none" id="share_creen" style="height: 75vh;width: 100%;"></video> -->
				</div>
			</div>
		</section>
	</div>
</div>

<p-toast></p-toast>

<p-confirmPopup></p-confirmPopup>

<!-- <button (click)="confirm($event)" pButton icon="pi pi-check" label="Confirm"></button> -->
