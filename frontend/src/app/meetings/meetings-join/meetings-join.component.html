<div id="main-wrapper">
	<div class="header">
		<app-home-header></app-home-header>
	</div>

	<div class="page-loading-card" *ngIf="loadingPage">
		<app-loading [isLoading]="true"></app-loading>
	</div>

	<div *ngIf="!loadingPage && !meetingToJoin" class="meeting-not-found">
		<h1 class="text-center mt-4">Impossible de joindre ce meeting</h1>
		<img src="../../../assets/img/course.svg" />
	</div>

	<div *ngIf="!loadingPage && meetingToJoin" class="">
		<h1 class="text-center">
			{{ meetingToJoin ? meetingToJoin.Topic : null }}
		</h1>
		<!--
		<section class="page-title">
			<div class="container">
				<div class="row">
					<div class="col-lg-12 col-md-12">
						<div class="breadcrumbs-wrap">
							<p class="">Hello</p>
						</div>
					</div>
				</div>
			</div>
		</section> -->

		<div class="container loading">
			<div class="join-card row pt-3">
				<!-- <div class="col-sm-12 col-lg-12">
					<p-dropdown
						[options]="meetings"
						[(ngModel)]="meeting"
						placeholder="Saisir l'id de la reunion"
						editable="true"
						optionLabel="name"
						class="w-100"
					></p-dropdown>
				</div> -->
				<div class="col-sm-12 col-lg-12">
					<label for="username">Votre nom pendant le meeting: </label>
					<input
						id="username"
						type="text"
						class="w-100"
						pInputText
						[(ngModel)]="userName"
						placeholder="Votre nom pendant la reunion"
					/>
				</div>

				<div *ngIf="showPasscodeInput()" class="col-sm-12 col-lg-12">
					<label for="passcode">Passcode pour rejoindre le meeting: </label>
					<input
						id="passcode"
						type="text"
						class="w-100"
						pInputText
						[(ngModel)]="passCode"
						placeholder="Passcode qui vous a été envoyé"
					/>
				</div>

				<div class="col-sm-12 col-lg-6 pt-3">
					<div class="p-field-checkbox">
						<p-checkbox
							[(ngModel)]="noSound"
							binary="true"
							inputId="audio-check-join"
						></p-checkbox>
						<label for="audio-check-join" class="pl-2 mb-0">Pas d'audio</label>
					</div>
				</div>
				<div class="col-sm-12 col-lg-6 pt-3">
					<div class="p-field-checkbox">
						<p-checkbox
							[(ngModel)]="noVideo"
							binary="true"
							inputId="video-check-join"
						></p-checkbox>
						<label for="video-check-join" class="pl-2 mb-0">Pas de video</label>
					</div>
				</div>
				<div class="d-flex mt-4 justify-content-end w-100">
					<button
						(click)="joinConference()"
						class="btn btn-sm btn-primary mr-4"
					>
						<app-loading
							*ngIf="loadingJoin"
							[isLoading]="loadingJoin"
						></app-loading>
						<span *ngIf="!loadingJoin"> Rejoindre </span>
					</button>

					<button
						class="btn-start-meeting"
						mat-raised-button
						color="accent"
						(click)="startMeeting()"
						[disabled]="loadingStart || !meetingCanStart()"
						*ngIf="showControlStartButtons()"
					>
						<app-loading
							[disabled]="!meetingCanStart()"
							*ngIf="loadingStart"
							[isLoading]="loadingStart"
						></app-loading>
						<span *ngIf="!loadingStart"> Demarrer </span>
					</button>

					<button
						class="btn btn-sm btn-primary"
						(click)="endMeeting()"
						[disabled]="loadingEnd"
						*ngIf="showControlEndButtons()"
					>
						<app-loading
							*ngIf="loadingEnd"
							[isLoading]="loadingEnd"
						></app-loading>
						<span *ngIf="!loadingEnd"> Terminer </span>
					</button>
					<!-- <button
						class="btn btn-sm btn-outline-primary mr-4 d-flex align-items-center"
					>
						<span>Fermer</span>
					</button> -->
				</div>
				<div
					*ngIf="displayEndCycleButton"
					class="d-flex mt-4 justify-content-end w-100"
				>
					<p-button
						[disabled]="loadingCloseRecurringCycle"
						type="primary"
						(click)="closeRecurringCycle()"
					>
						<app-loading
							[isLoading]="true"
							*ngIf="loadingCloseRecurringCycle"
						></app-loading>
						<span *ngIf="!loadingCloseRecurringCycle"
							>Terminer le cycle</span
						></p-button
					>
				</div>
			</div>
		</div>
	</div>
</div>
