import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import * as moment from 'moment';
import { ToastrService } from 'ngx-toastr';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { HomeLoginComponent } from '~/app/home/<USER>/home-login.component';
import { Meetings, MeetingStatus } from '~/app/models/meetings';
import { User, UserRoleType } from '~/app/models/user.model';
import { AbonnementsService } from '~/app/services/abonnements/abonnements.service';
import { MeetingsService } from '~/app/services/meetings/meetings.service';
import { UserService } from '~/app/services/users/user.service';
import { UtilsService } from '~/app/services/utils.service';
import { MeetingSocketService } from '~/app/services/websockets/meetings/meeting-socket.service';

@Component({
	selector: 'app-meetings-join',
	templateUrl: './meetings-join.component.html',
	styleUrls: ['./meetings-join.component.scss']
	// encapsulation: ViewEncapsulation.None
})
export class MeetingsJoinComponent implements OnInit {
	// meeting: any;
	meetingToJoin: Meetings;
	meetingStatus = MeetingStatus;
	// meetingStatus = MeetingStatus;
	// meetings = [
	// 	{ name: 'Reunion de New York - 9992088392', code: 'NY' },
	// 	{ name: 'Les gamers du dimanche - 334898894', code: 'RM' },
	// 	{ name: 'Reunion du vendredi - 009092188928', code: 'LDN' }
	// ];
	userName: string = '';
	passCode: string = '';

	user: User;

	loadingPage: boolean = false;
	loading: boolean = false;
	loadingJoin: boolean = false;
	loadingStart: boolean = false;
	loadingEnd: boolean = false;

	noSound: boolean = false;
	noVideo: boolean = false;

	loadingCloseRecurringCycle: boolean = false;
	displayEndCycleButton: boolean = false;

	constructor(
		private userService: UserService,
		private meetingService: MeetingsService,
		private activatedRoute: ActivatedRoute,
		private router: Router,
		private translateService: TranslateService,
		private toastr: ToastrService,
		private meetingSocketService: MeetingSocketService,
		private abonnementService: AbonnementsService,
		private utilService: UtilsService
	) {}

	async ngOnInit(): Promise<void> {
		this.loadingPage = true;
		try {
			if (this.userService.isConnected()) {
				this.user = await this.userService.getUserConnected().toPromise();
				this.userName = this.user.Firstname;
			} else {
				// this.router.navigateByUrl('/');
			}

			if (this.user) {
				/* const abonnement = await this.abonnementService
					.getUserActiveAbonnement(this.user.Slug)
					.toPromise();
				if (abonnement?.Forfait?.Name.toLowerCase() !== 'premium') {
					if (this.user.Role != UserRoleType.ADMIN) {
						this.router.navigateByUrl('/home/<USER>');
						this.toastr.warning(
							'Brain maker',
							'Veuillez chosoir un forfait adequat pour avoir accès au meeting'
						);
					}
				} */
			} else {
				this.utilService.buildModal(
					HomeLoginComponent,
					() => {},
					500,
					true,
					{}
				);
			}

			const slug = this.activatedRoute.snapshot.params.slug;
			console.log(slug);
			this.meetingToJoin = await this.meetingService
				.getBySlug(slug)
				.toPromise();
			this.displayEndCycleButton = this.meetingToJoin.Recurring;
			console.log('Meeting to join', this.meetingToJoin);
			// setTimeout(() => {
			this.loadingPage = false;
			// }, 300);
		} catch (error) {
			this.loadingPage = false;
			this.toastr.error(
				'Une erreur est survenue lors du chargement de la page',
				'Erreur de chargement'
			);
			console.log(error);
		}
		// console.log(this.meetingToJoin.RequirePasscode);
		// console.log('show passcode input', this.showPasscodeInput());
	}

	async updateMeetingStatus(newStatus: MeetingStatus): Promise<boolean> {
		const meetingToUpdate = { ...this.meetingToJoin };
		meetingToUpdate.Status = newStatus;

		this.loading = true;
		let result;
		try {
			result = await this.meetingService
				.update(meetingToUpdate.Slug, meetingToUpdate)
				.toPromise();
			this.meetingToJoin = result;

			const message = await this.translateService
				.get('admin.misc.editStatusSuccess')
				.toPromise();
			this.toastr.success(message, 'Brain-maker');
		} catch (error) {
			let message = await this.translateService
				.get('admin.misc.editStatusError')
				.toPromise();
			this.toastr.error(message, 'Brain-maker');
		} finally {
			this.loading = false;
		}
		return result != null;
	}

	closeModal(): void {}

	joinConference(): void {
		this.loadingJoin = true;
		console.log('user passcode', this.passCode);
		if (!this.userName) {
			this.toastr.error(
				'Vous devez obligatoirement saisir votre nom pour la conférence',
				"Nom d'utilisateur requis"
			);
			this.loadingJoin = false;
			return;
		}

		if (this.user) {
		}

		if (
			this.meetingToJoin.RequirePasscode &&
			(!this.user ||
				(this.user && this.meetingToJoin.Owner.Id !== this.user.Id))
		) {
			// console.log('*** passcode requis !: ', this.meetingToJoin.Passcode);
			const correctPasscode = this.passCode === this.meetingToJoin.Passcode;

			if (!correctPasscode || this.passCode === '') {
				this.toastr.error(
					"Le passcode que vous avez entré n'est pas correct",
					'Passcode incorrect'
				);
				this.passCode = '';
				this.loadingJoin = false;
				return;
			}

			this.router.navigateByUrl(
				`/meetings/meeting/${this.meetingToJoin.Slug}?isVideo=${!this
					.noVideo}&isAudio=${!this.noSound}&&username=${
					this.userName
				}&&passcode=${this.passCode}`
			);
		} else {
			this.router.navigateByUrl(
				`/meetings/meeting/${this.meetingToJoin.Slug}?isVideo=${!this
					.noVideo}&isAudio=${!this.noSound}&&username=${this.userName}`
			);
		}
		this.loadingJoin = false;
	}

	async startMeeting(): Promise<void> {
		if (!this.meetingCanStart()) return;

		this.loadingStart = true;
		await this.updateMeetingStatus(MeetingStatus.RUNNING);
		this.loadingStart = false;
		this.meetingSocketService
			.EmitMeetingStarting(this.meetingToJoin.Slug)
			.subscribe(() => {
				//
			});
	}

	meetingCanStart(): boolean {
		// const currentTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

		const startDateTimeMeeting = new Date(
			this.meetingToJoin.StartDate
		).setHours(
			Number(this.meetingToJoin.StartTime.split(':')[0]),
			Number(this.meetingToJoin.StartTime.split(':')[1])
		);

		// const startDateWithTimeZone = moment.tz(
		// 	startDateTimeMeeting,
		// 	this.meetingToJoin.Timezone
		// );
		// const endDateWithTimeZone = moment.tz(
		// 	this.meetingToJoin.EndTime,
		// 	this.meetingToJoin.Timezone
		// );

		// const currentStartDateWithCurrentTimeZone = startDateWithTimeZone
		// 	.clone()
		// 	.tz(currentTimeZone);
		// const currentEndDateWithCurrentTimeZone = endDateWithTimeZone
		// 	.clone()
		// 	.tz(currentTimeZone);

		// const actualMoment = moment().tz(currentTimeZone);

		return (
			// actualMoment.isSameOrAfter(currentStartDateWithCurrentTimeZone) &&
			// actualMoment.isBefore(currentEndDateWithCurrentTimeZone)
			new Date(startDateTimeMeeting).getTime() < new Date().getTime()
		);
	}

	async endMeeting(): Promise<void> {
		this.loadingEnd = true;
		const res = await this.updateMeetingStatus(MeetingStatus.FINISHED);
		alert(res);
		this.loadingEnd = false;
	}

	showPasscodeInput(): boolean {
		// if the meetings not require passcode
		if (!this.meetingToJoin.RequirePasscode) {
			return false;
		}
		// if meeting require passcode and user is not connected, we ask passcode
		if (!this.user) {
			return true;
		}
		// if meeting require passcode and user is meeting owner, we don't ask passcode
		if (this.user.Id === this.meetingToJoin.Owner.Id) {
			return false;
		}

		// if meeting require passcode and user is not owner
		return true;
	}

	showControlStartButtons(): boolean {
		// user &&
		// meetingToJoin &&
		// meetingToJoin.Status == meetingStatus.SCHEDULED &&
		// meetingToJoin.Owner.Slug == user.Slug

		if (!this.user) {
			return false;
		}

		return (
			this.user.Slug &&
			this.meetingToJoin &&
			this.meetingToJoin.Status == this.meetingStatus.SCHEDULED &&
			this.meetingToJoin.Owner.Slug == this.user.Slug
		);
	}

	showControlEndButtons(): boolean {
		if (!this.user) {
			return false;
		}

		return (
			this.user &&
			this.meetingToJoin &&
			this.meetingToJoin.Status == this.meetingStatus.RUNNING &&
			this.meetingToJoin.Owner.Slug == this.user.Slug
		);
	}

	async closeRecurringCycle(): Promise<void> {
		this.meetingToJoin.Recurring = false;
		this.loadingCloseRecurringCycle = true;
		try {
			this.meetingToJoin = await this.meetingService
				.update(this.meetingToJoin.Slug, this.meetingToJoin)
				.toPromise();
			this.loadingCloseRecurringCycle = false;
			this.displayEndCycleButton = false;
		} catch (error) {
			console.log(error);
			this.toastr.error(
				'Une erreur est survenue lors de la fermeture du cycle',
				'impossible de fermer le cycle du meeting recursif'
			);
			this.loadingCloseRecurringCycle = false;
		}
	}
}
