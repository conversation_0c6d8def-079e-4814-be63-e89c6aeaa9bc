import {
	AfterViewInit,
	Component,
	OnInit,
	ViewChild,
	ViewEncapsulation
} from '@angular/core';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { Discussion } from '~/app/models/discussions/discussion';
import {
	DiscussionMessage,
	FileType
} from '~/app/models/discussions/discussion-message';
import { User, UserRoleType } from '~/app/models/user.model';
import { Media } from '~/app/models/media.model';
import { DiscussionMessageService } from '~/app/services/discussions/discussion-message.service';
import { DiscussionService } from '~/app/services/discussions/discussion.service';
import { UserService } from '~/app/services/users/user.service';
import { MeetingChatSocketService } from '~/app/services/websockets/meetings/chat/meeting-chat-socket.service';
import { PlyrComponent } from 'ngx-plyr';
import { UtilsService } from '~/app/services/utils.service';
import { environment } from '~/environments/environment';
import { HomeLoginComponent } from '~/app/home/<USER>/home-login.component';
import { AbonnementsService } from '~/app/services/abonnements/abonnements.service';
declare const $: any;

@Component({
	selector: 'app-meetings-chat',
	templateUrl: './meetings-chat.component.html',
	styleUrls: ['./meetings-chat.component.scss'],
	encapsulation: ViewEncapsulation.None
})
export class MeetingsChatComponent implements OnInit, AfterViewInit {
	@ViewChild('messagesBox') messagesBox: any;

	sideBarToogle = false;
	visibleSidebarRemoteUser;
	user: User;
	inputMessage = '';
	selectedDiscussion: any = {};

	userSearch = '';
	usersSuggestions = [];

	discussions: any[] = [];
	discussionsFormated: any[] = [];
	discussionsMessage: any[] = [];
	discussionMessageQuote: DiscussionMessage = null;
	loadingDiscussion = false;
	loadingDiscussionMessages = false;
	loadingSendAttachment = false;

	usersTyping: { username: string; room: string }[];
	typingInCurrentChat: boolean = false;
	username: string;

	FILE_TYPE = FileType;

	attachmentFileType;
	attachmentFile;
	attachmentFileSize: string;
	attachmentFileToReadName: string;
	attachmentMedia: Media;
	displayMaximizable = false;
	loading = false;
	// get the component instance to have access to plyr instance
	@ViewChild(PlyrComponent)
	plyr: PlyrComponent;

	// or get it from plyrInit event
	player: Plyr;

	// videoSources: Plyr.Source[] = [
	// 	{
	// 		src: 'bTqVqk7FSmY',
	// 		provider: 'youtube'
	// 	}
	// ];

	videoSources: Plyr.Source[] = [];

	constructor(
		private userService: UserService,
		private router: Router,
		private toastr: ToastrService,
		private discussionService: DiscussionService,
		private discussionMessagesService: DiscussionMessageService,
		private meetingChatSocketService: MeetingChatSocketService,
		private utilsService: UtilsService,
		private abonnementService: AbonnementsService
	) {
		this.usersTyping = [];
	}

	async ngOnInit(): Promise<void> {
		if (this.userService.isConnected()) {
			this.user = await this.userService.getUserConnected().toPromise();
			this.username = this.user.Firstname;
		} else {
			this.router.navigateByUrl('/meetings');
			this.toastr.error(
				'Bien vouloir vous inscrire ou vous connecter pour pouvoir y accéder',
				'Authentification requise'
			);
			return;
		}

		if (this.user) {
			const abonnement = await this.abonnementService
				.getUserActiveAbonnement(this.user.Slug)
				.toPromise();
			if (abonnement?.Forfait?.Name.toLowerCase() !== 'premium') {
				if (this.user.Role != UserRoleType.ADMIN) {
					this.router.navigateByUrl('/home/<USER>');
					this.toastr.warning(
						'Brain maker',
						'Veuillez chosoir un forfait adequat pour avoir accès au meeting'
					);
				}
			}
		} else {
			this.utilsService.buildModal(HomeLoginComponent, () => {}, 500, true, {});
		}

		await this.getAllDiscussionForUser();

		if (this.discussions.length > 0) {
			this.selectedDiscussion = this.discussionsFormated[0];
			await this.getMessagesForDiscussion(this.selectedDiscussion);
			console.log('Selected Discussion : ', this.selectedDiscussion);
		}

		this.initSocketListeners();
	}

	ngAfterViewInit(): void {
		this.messagesBox.scrollTop(10000000);
	}

	initSocketListeners(): void {
		this.meetingChatSocketService
			.EmitInit(this.user.Slug)
			.subscribe((): void => {
				//
			});

		// Join chat channel
		console.log('susbcribe to chat: ', this.selectedDiscussion.Slug);
		this.meetingChatSocketService
			.JoinMeetingChatRoom(this.selectedDiscussion.Slug)
			.subscribe(() => {
				//
			});

		this.meetingChatSocketService
			.ListenNewDiscussion()
			.subscribe(
				(payload: {
					DestUser: string;
					Discussion: DiscussionMessage;
				}): void => {
					// alert('New Discussion for you');
					// const d = this.discussions.find(
					// 	(discussion: Discussion) => discussion.Slug === discussionSlug
					// );
					console.log('New dicussion payload', payload.Discussion);
					console.log(
						'userSlug: ',
						payload.DestUser,
						'discussion: ',
						this.user.Slug
					);
					// if (!d) {
					// 	console.log("*** La discussion n'existe pas on la crée");
					if (this.user.Slug === payload.DestUser) {
						console.log('*** pushhhh');
						this.meetingChatSocketService.JoinMeetingChatRoom(
							payload.Discussion.Slug
						);
						this.discussionsFormated = [
							...this.discussions,
							payload.Discussion
						];

						this.discussions = this.discussionsFormated;
						console.log('ddd', this.discussionsFormated);
					}
					// }
				}
			);

		this.meetingChatSocketService
			.ListenNewMessage()
			.subscribe((message: DiscussionMessage) => {
				// on n'ajoute le message

				if (message && message.Discussion) {
					if (message.Discussion.Id === this.selectedDiscussion.Id) {
						message = {
							...message,
							SendBy: {
								...message.SendBy,
								...this.userService.formatUserFields(message.SendBy)
							},
							SendTo: {
								...message.SendTo,
								...this.userService.formatUserFields(message.SendTo)
							}
						};

						this.discussionsMessage.push(message);
						this.messagesBox.scrollTop(10000000);
					} else {
						const d = this.discussions.find(
							(discussion: Discussion) =>
								discussion.Id === message.Discussion.Id
						);
						if (!d) {
							console.log("*** La discussion n'existe pas on la crée");
							this.discussions.push(d);
						}
					}
				}
			});

		this.meetingChatSocketService
			.ListenTyping()
			.subscribe((typingPayload: { username: string; room: string }) => {
				// On ajoute l'utilisateur qui tape
				console.log('Typing: ', this.selectedDiscussion.Slug, typingPayload);
				if (this.username !== typingPayload.username) {
					const exist = this.usersTyping.find(
						(u) =>
							u.username === typingPayload.username &&
							u.room === typingPayload.room
					);
					console.log('hhh!!', this.usersTyping, exist);
					if (!exist) {
						this.usersTyping.push(typingPayload);
						if (typingPayload.room === this.selectedDiscussion.Slug) {
							console.log('*** Dans la discussion courante');
							this.typingInCurrentChat = true;
						} else {
							console.log('*** Hors');
							this.typingInCurrentChat = false;
						}
						console.log('Show: ', this.typingInCurrentChat);
						setTimeout(() => {
							this.userNoTyping(typingPayload);
						}, 5000);
					}
				}
			});

		this.meetingChatSocketService
			.ListenStopTyping()
			.subscribe((typingPayload: { username: string; room: string }) => {
				this.userNoTyping(typingPayload);
			});
	}

	handleKeyDown(event: KeyboardEvent): void {
		console.log('key down');

		if (event.key === 'Enter') {
			// this.userNoTyping(this.username);
			this.meetingChatSocketService
				.EmitUserStopTyping(this.username, this.selectedDiscussion.Slug)
				.subscribe(() => {
					//
				});
		}

		this.meetingChatSocketService
			.EmitTyping(this.username, this.selectedDiscussion?.Slug)
			.subscribe(() => {
				//
			});
	}

	handleBlur(event: Event): void {
		console.log('Blur');
		this.meetingChatSocketService
			.EmitUserStopTyping(this.username, this.selectedDiscussion?.Slug)
			.subscribe(() => {
				//
			});
	}

	private userNoTyping(typingPayload: {
		username: string;
		room: string;
	}): void {
		if (typingPayload.room === this.selectedDiscussion.Slug) {
			this.typingInCurrentChat === false;
		}
		const index = this.usersTyping.indexOf(
			this.usersTyping.find(
				(u) =>
					u.username === typingPayload.username && u.room === typingPayload.room
			)
		);
		console.log(this.usersTyping, index);
		if (index >= 0) {
			this.usersTyping.splice(index, 1);
		}
	}

	async searchUsers($event) {
		try {
			const result = await this.userService
				.getAllByQuerySearchName($event.query)
				.toPromise();
			this.usersSuggestions = result
				.filter((u) => u.Id != this.user.Id)
				.map((u) => ({
					/* Id: u.Id,
				Username: u.Username,
				Firstname: u.Firstname,
				Lastname: u.Lastname,
				Photo: u.Photo, */
					...u,
					...this.userService.formatUserFields(u)
				}));
		} catch (error) {
			this.toastr.error('Error to search user by query name');
		}
	}

	async suggestionSelected(userSuggestionSelected) {
		//this.userSearch = `@${userSuggestionSelected.Username}`.trim(); // - ${userSuggestionSelected.Firstname}
		this.userSearch = '';

		const found = this.discussionsFormated.find(
			(d) => d.User.Id == userSuggestionSelected.Id || d.User.Id == null
		);

		if (!found) {
			this.discussionsFormated.push({
				Id: null,
				Slug: null,
				User: { ...userSuggestionSelected }
			});
			this.selectedDiscussion = {
				Id: null,
				Slug: null,
				User: { ...userSuggestionSelected }
			};
			this.discussionsMessage = [];
		} else {
			if (this.selectedDiscussion.Id != found.Id) {
				this.selectedDiscussion = found;
				await this.getMessagesForDiscussion(this.selectedDiscussion);
			}
		}
	}

	async getAllDiscussionForUser() {
		this.loadingDiscussion = true;

		try {
			this.discussions = await this.discussionService
				.getAllDiscussionsForUser(this.user.Id)
				.toPromise();
			this.discussionsFormated = this.discussions.map((discuss) => {
				if (discuss.User1.Id != this.user.Id) {
					return {
						Id: discuss.Id,
						Slug: discuss.Slug,
						User: {
							...discuss.User1,
							...this.userService.formatUserFields(discuss.User1)
						}
					};
				} else if (discuss.User2.Id != this.user.Id) {
					return {
						Id: discuss.Id,
						Slug: discuss.Slug,
						User: {
							...discuss.User2,
							...this.userService.formatUserFields(discuss.User2)
						}
					};
				}
			});
		} catch (error) {
			console.log('ERROR TO GET DISCUSSION', error);
		} finally {
			this.loadingDiscussion = false;
		}
	}

	async getMessagesForDiscussion(discussion) {
		this.loadingDiscussionMessages = true;

		try {
			const result = await this.discussionMessagesService
				.getMessagesForDiscussion(discussion.Id)
				.toPromise();
			this.discussionsMessage = result.map((discussMessage) => ({
				...discussMessage,
				SendBy: {
					...discussMessage.SendBy,
					...this.userService.formatUserFields(discussMessage.SendBy)
				},
				SendTo: {
					...discussMessage.SendTo,
					...this.userService.formatUserFields(discussMessage.SendTo)
				}
			}));

			this.messagesBox.scrollTop(10000000);
			console.log('SOOOOOO============>', this.messagesBox.scrollTop);
		} catch (error) {
			console.log('ERROR TO GET DISCUSSION MESSAGES', error);
		} finally {
			this.loadingDiscussionMessages = false;
		}
	}

	async openUserChat(discussion) {
		//console.log('OPEN CHAT ======> ', discussion);
		this.selectedDiscussion = discussion;
		this.discussionMessageQuote = null;
		this.typingInCurrentChat = false;
		this.discussionsMessage = [];
		this.inputMessage = '';

		console.log('Current Discussion: ', this.selectedDiscussion, discussion);
		this.meetingChatSocketService
			.JoinMeetingChatRoom(this.selectedDiscussion.Slug)
			.subscribe(() => {
				//
			});

		await this.getMessagesForDiscussion(discussion);
		// $('#chatSidebar').hide();
		// $('#chatBoardContent').show();
	}

	getInputMessageData(event: KeyboardEvent): void {
		console.log(event.key);
		if (event.key === 'Enter') {
			// On 'Shift+Enter' do this...
			this.sendMessage();
		}
	}

	async sendMessage(): Promise<void> {
		// console.log(this.inputMessage);
		if (this.loading) {
			return;
		}
		this.loading = true;

		this.meetingChatSocketService
			.EmitUserStopTyping(this.username, this.selectedDiscussion.Slug)
			.subscribe(() => {
				//
			});
		let discussionFormated;
		if (this.selectedDiscussion.User && this.selectedDiscussion.Id == null) {
			// WE HAVE TO CREATE DISCUSSION BEFORE TO SEND MESSAGE
			const discussion: any = {
				User1: this.user,
				User2: this.selectedDiscussion.User,
				Id: null,
				Slug: ''
			};

			try {
				const result = (await this.discussionService
					.add(discussion)
					.toPromise()) as Discussion;
				console.log('New Discussion', result);
				discussionFormated = {
					Id: result.Id,
					Slug: result.Slug,
					User: {
						...result.User2,
						...this.userService.formatUserFields(result.User2)
					}
				};
				// this.openUserChat(this.discussionsFormated);
				this.meetingChatSocketService
					.EmitNewDiscussion(result.User2.Slug, discussionFormated)
					.subscribe((): void => {
						//
					});
				this.discussions.push(discussionFormated);
				this.meetingChatSocketService.JoinMeetingChatRoom(result.Slug);

				const foundIndexUpdated = this.discussionsFormated.findIndex(
					(m) => m.Id === null
				);
				this.discussionsFormated.splice(
					foundIndexUpdated,
					1,
					discussionFormated
				);
				this.selectedDiscussion = discussionFormated;
			} catch (error) {
				this.toastr.error('Error to create discussion', 'Brain-maker');
				return;
			}
		}

		if (this.inputMessage.length !== 0 && !this.attachmentFile) {
			let message: DiscussionMessage = {
				SendBy: this.user,
				SendTo: this.selectedDiscussion.User,
				Message: this.inputMessage,
				Read: false,
				Attachement: null,
				Discussion: this.discussions.find(
					(d) => d.Id == this.selectedDiscussion.Id
				),
				Id: 0,
				Slug: '',
				Parent: this.discussionMessageQuote
			};

			try {
				const result = await this.discussionMessagesService
					.add(message)
					.toPromise();

				// this.discussionsMessage.push({
				// 	...result,
				// 	SendBy: {
				// 		...result.SendBy,
				// 		...this.userService.formatUserFields(result.SendBy)
				// 	},
				// 	SendTo: {
				// 		...result.SendTo,
				// 		...this.userService.formatUserFields(result.SendTo)
				// 	}
				// });

				// this.messages.push({
				//   message: {
				//     text: this.inputMessage, name: 'Philippe Steve',
				//     date: new Date().toString()
				//   }, replyMessage: { text: '', name: '', date: '' },
				//   isReplyMessage: false, userMessage: true
				// });
				// const element = document.querySelector('#endPage');

				// // scroll to element
				// element.scrollIntoView();
				// tslint:disable-next-line: no-unused-expression
				// $('#x')[0].scrollHeight;
				// $('#message_container').scrollTop = $(
				// 	'#message_container'
				// ).scrollHeight;
				this.inputMessage = '';
				this.discussionMessageQuote = null;
				// this.messagesBox.scrollTop(1000000);
				this.loading = false;
			} catch (error) {
				//let message = await this.translateService.get('admin.misc.editStatusError').toPromise();
				this.toastr.error('Error to send message', 'Brain-maker');
			}
		} else if (this.attachmentFile) {
			console.log(this.attachmentFile);
			this.loadingSendAttachment = true;
			let message: DiscussionMessage = {
				SendBy: this.user,
				SendTo: this.selectedDiscussion.User,
				Message: this.inputMessage,
				Read: false,
				Attachement: this.attachmentMedia,
				Discussion: this.discussions.find(
					(d) => d.Id == this.selectedDiscussion.Id
				),
				Id: 0,
				Slug: '',
				Parent: null
			};

			try {
				console.log(message);
				const result = await this.discussionMessagesService
					.add(message)
					.toPromise();

				console.log('result: ', result);
				this.cancelAttachment();
				this.loadingSendAttachment = false;
				this.messagesBox.scrollTop(1000000);

				// $('#message_container').scrollTop = $(
				// 	'#message_container'
				// ).scrollHeight;
				this.inputMessage = '';
				this.discussionMessageQuote = null;
				this.loading = false;
			} catch (error) {
				//let message = await this.translateService.get('admin.misc.editStatusError').toPromise();
				this.toastr.error('Error to send message', 'Brain-maker');
			}
		}
		// this.openUserChat(this.discussionsFormated);
		// if (discussionFormated) {
		// 	this.selectedDiscussion = discussionFormated;
		// 	await this.getMessagesForDiscussion(discussionFormated);
		// }
	}

	async deleteMessage(discussionMessage: DiscussionMessage) {
		try {
			const result = await this.discussionMessagesService
				.delete(discussionMessage)
				.toPromise();

			const foundIndexDeleted = this.discussionsMessage.findIndex(
				(m) => m.Id === discussionMessage.Id
			);
			this.discussionsMessage.splice(foundIndexDeleted, 1);
		} catch (error) {
			console.log('metting message delete error', error);
			//let message = await this.translateService.get('admin.misc.editStatusError').toPromise();
			this.toastr.error('Error to delete message', 'Brain-maker');
		}
	}

	async updateMessage(discussionMessage: DiscussionMessage) {
		try {
			const result = await this.discussionMessagesService
				.edit(discussionMessage)
				.toPromise();

			const foundIndexUpdated = this.discussionsMessage.findIndex(
				(m) => m.Id === discussionMessage.Id
			);
			this.discussionsMessage.splice(foundIndexUpdated, 1, {
				...result,
				SendBy: {
					...result.SendBy,
					...this.userService.formatUserFields(result.SendBy)
				},
				SendTo: {
					...result.SendTo,
					...this.userService.formatUserFields(result.SendTo)
				}
			});
		} catch (error) {
			console.log('discussion message update error', error);
			//let message = await this.translateService.get('admin.misc.editStatusError').toPromise();
			this.toastr.error('Error to update discussion message', 'Brain-maker');
		}
	}

	quoteMessage(discussionMessage: DiscussionMessage) {
		this.discussionMessageQuote = discussionMessage;
	}

	closeQuoteMessage() {
		this.discussionMessageQuote = null;
	}

	copyTextToClipboard(text): boolean {
		const txtArea = document.createElement('textarea');
		txtArea.id = 'txt';
		txtArea.style.position = 'fixed';
		txtArea.style.top = '0';
		txtArea.style.left = '0';
		txtArea.style.opacity = '0';
		txtArea.value = text;
		document.body.appendChild(txtArea);
		txtArea.select();

		try {
			const successful = document.execCommand('copy');
			const msg = successful ? 'successful' : 'unsuccessful';
			console.log('Copying text command was ' + msg);
			if (successful) {
				return true;
			}
		} catch (err) {
			console.log('Oops, unable to copy');
		} finally {
			document.body.removeChild(txtArea);
		}
		return false;
	}

	copyDiscussionMessage(message: DiscussionMessage) {
		if (this.copyTextToClipboard(message.Message)) {
			this.toastr.info('Copied', 'Message');
		} else {
			this.toastr.error('Error to copy', 'Message');
		}
	}

	async handleFileUpload(event): Promise<void> {
		console.log('upload !!');
		if (!event.target.files) return;

		const file = event.target.files[0];
		this.attachmentFile = file;

		this.attachmentFileSize =
			file.size > 1024 * 1024
				? (file.size / (1024 * 1024)).toFixed(2) + ' mb'
				: (file.size / 1024).toFixed(2) + ' ko';

		console.log('File: ', file);

		switch (file.type.split('/')[0]) {
			case 'image':
				console.log('Type: IMAGE');
				this.attachmentFileType = this.FILE_TYPE.IMAGE;
				break;
			case 'video':
				console.log('Type: VIDEO');
				this.attachmentFileType = this.FILE_TYPE.VIDEO;
				break;
			case 'audio':
				console.log('Type: audio');
				this.attachmentFileType = this.FILE_TYPE.AUDIO;
				break;
			default:
				console.log('Type: autre');
				this.attachmentFileType = this.FILE_TYPE.OTHER;
		}

		const base64File = await this.FileToBase64(file);

		const t = base64File.split(';base64,')[1];
		// console.log('payload: ', t.join());

		const media = new Media(file.name, t, file.type, file.size, 'discussion');
		this.attachmentMedia = media;
	}

	cancelAttachment(): void {
		this.attachmentFile = null;
		this.attachmentFileSize = null;
		this.attachmentFileType = null;
	}

	FileToBase64(file): Promise<any> {
		return new Promise((resolve, reject) => {
			const reader = new FileReader();
			reader.readAsDataURL(file);
			reader.onload = () => resolve(reader.result);
			reader.onerror = (error) => reject(error);
		});
	}

	played(event: Plyr.PlyrEvent) {
		console.log('played', event);
		// this.hlsDriver.load(this.sources[0].src);
	}

	play(): void {
		this.player.play(); // or this.plyr.player.play()
	}

	async displayAttachmentFile(event: {
		Media: Media;
		Type: number;
	}): Promise<void> {
		this.attachmentFileToReadName = event.Media.Name;
		this.attachmentFileType = event.Type;
		const link = `${environment.path}/${event.Media.Hashname}`;
		this.videoSources = [
			{
				src: link
			}
		];
		console.log('Source****:', this.videoSources[0].src);
		this.displayMaximizable = true;
		console.log(link);

		// console.log('link', event.Link);
		// const file = await this.utilsService.getFileFromApi(event.Link).toPromise();
		// console.log('File: ', file);
	}

	// typingInDiscussion(slug: string): boolean {
	// 	const exist = this.usersTyping.find(
	// 		(t: { username: string; roomName: string }) => t.roomName === slug
	// 	);

	// 	if (exist) {
	// 		return true;
	// 	} else {
	// 		return false;
	// 	}
	// }
}
