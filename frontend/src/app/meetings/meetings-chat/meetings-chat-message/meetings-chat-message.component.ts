import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { MettingMessage } from '~/app/models/metting-message.model';
import { User } from '~/app/models/user.model';
import { MeetingMessageService } from '~/app/services/meetings/meeting-message.service';
import { UserService } from '~/app/services/users/user.service';

@Component({
	selector: 'app-meetings-chat-message',
	templateUrl: './meetings-chat-message.component.html',
	styleUrls: ['./meetings-chat-message.component.scss']
})
export class MeetingsChatMessageComponent implements OnInit {
	@Output('onDeleteMessage') deleteMessageEvent = new EventEmitter<MettingMessage>();
	@Output('onUpdateMessage') updateMessageEvent = new EventEmitter<MettingMessage>();
	@Output('onQuoteMessage') quoteMessageEvent = new EventEmitter<MettingMessage>();
	@Output('onCopyMettingMessage') copyMettingMessage = new EventEmitter<MettingMessage>();


	// tslint:disable-next-line: no-input-rename
	@Input('parent') parent = null;
	// tslint:disable-next-line: no-input-rename
	@Input('replyMessage') replyMessage = new MettingMessage(0, '');
	// tslint:disable-next-line: no-input-rename
	@Input('message') message = new MettingMessage(0, '');

	@Input('currentUser') currentUser;
	@Input('usernameMeeting') usernameMeeting;

	showUpdate = false;

	user: User;
	showResponse = false;
	options = [];

	constructor(private userService: UserService) { }

	async ngOnInit(): Promise<void> {
		//this.user = await this.userService.getUserConnected().toPromise();
		this.options = [
			{ name: 'Copier', icon: 'delete', id_option: 1 },
			/* this.isCurrentUserOrRemote()
				? { name: 'Modifer', icon: 'payment', id_option: 2 }
				: null,  */
			{ name: 'Citer', icon: 'payment', id_option: 3 },
			this.isCurrentUserOrRemote()
				? { name: 'Supprimer', icon: 'payment', id_option: 4 }
				: null
		].filter(Boolean);
	}

	isCurrentUserOrRemote() {
		return (
			(this.message.From && this.currentUser.Id == this.message.From.Id) ||
			this.message.UsernameMeeting == this.usernameMeeting
		);
	}

	formatDate(date) {
		return new Date(date).toDateString();
	}

	formaTime(date) {
		return new Date(date).toLocaleTimeString();
	}

	toogleShowResponse(): void {
		this.showResponse = !this.showResponse;
	}
	selectedOptionFxn(option): void {
		switch (option.id_option) {
			case 1:
				this.copyMettingMessage.emit(this.message);
				break;
			case 2:
				this.showUpdate = true;
				break;
			case 3:
				/* if (!this.showResponse) {
					this.toogleShowResponse();
				} */
				this.quoteMessageEvent.emit(this.message);
				break;
			case 4:
				this.deleteMessageEvent.emit(this.message);
				break;

			default:
				break;
		}
	}

	updateMessage(value) {
		this.updateMessageEvent.emit({ ...this.message, Content: value });
		this.showUpdate = false;
	}
}
