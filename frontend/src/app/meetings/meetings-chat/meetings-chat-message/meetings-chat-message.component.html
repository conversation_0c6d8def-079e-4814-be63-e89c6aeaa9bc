<div class="row">
	<div
		class="w-100 pl-4 pr-4 col-sm-12 col-lg-12 mb-3"
		style="position: relative"
	>
		<div class="d-flex mb-0 left w-100">
			<img
				[src]="
					message.From
						? message.From.photoFormated
						: '/assets/img/default_user.png'
				"
				[alt]="message.UsernameMeeting"
				class="mr-3"
				srcset=""
				style="height: 30px; width: 30px; border-radius: 50%"
			/>
			<div class="d-flex flex-column" style="position: relative">
				<small
					>{{ message.UsernameMeeting }} , {{ formatDate(message.CreatedAt) }},
					{{ formaTime(message.CreatedAt) }}</small
				>
				<div
					class="rounded"
					[class]="isCurrentUserOrRemote() ? 'current-user' : 'remote-user'"
				>
					<div class="d-flex flex-column p-2" *ngIf="parent">
						<div>
							<p class="reply">{{ replyMessage.Content }}</p>
							<small
								>{{ replyMessage.From?.Username }} ,
								{{ replyMessage.CreatedAt }}</small
							>
							<hr class="mt-1 mb-0" />
						</div>
					</div>
					<div class="p-2 pt-1" *ngIf="!showUpdate">
						<span class="p-2 rounded">{{ message.Content }}</span>
					</div>

					<form *ngIf="showUpdate">
						<input
							#inputUpdate
							[value]="message.Content"
							class="p-2 pt-1"
							(keyup.enter)="updateMessage(inputUpdate.value)"
						/>
					</form>
				</div>
			</div>
			<div class="pl-2">
				<i class="pi pi-ellipsis-h pointer" [matMenuTriggerFor]="menu"></i>
				<mat-menu #menu="matMenu">
					<button
						mat-menu-item
						*ngFor="let option of options"
						(click)="selectedOptionFxn(option)"
					>
						<span>{{ option.name }}</span>
					</button>
				</mat-menu>
			</div>
		</div>
	</div>
</div>

<!-- <div class="row" *ngIf="message.From == user">
	<div class="col-sm-12 col-lg-3"></div>
	<div
		class="w-100 pl-4 pr-4 col-sm-12 col-lg-9 mb-3"
		style="position: relative"
	>
		<div class="d-flex right">
			<div class="d-flex mb-0 flex-column">
				<div class="mb-1 d-flex pl-2" style="justify-content: flex-end">
					<small>{{ message.From.Username }} , {{ message.CreatedAt }}</small>
				</div>
				<div
					[class]="
						message.From && currentUser.Id == message.From.Id
							? 'current-user'
							: 'remote-user'
					"
					style="position: relative; justify-content: flex-end"
				>
					<div class="d-flex flex-column p-2" *ngIf="parent">
						<div>
							<p class="reply">{{ replyMessage.Content }}</p>
							<small
								>{{ replyMessage.From.Username }} ,
								{{ replyMessage.CreatedAt }}</small
							>
							<hr class="mt-1 mb-0" />
						</div>
					</div>
					<div class="p-2 pt-1">
						<span class="p-2 rounded">{{ message.Content }}</span>
					</div>
				</div>
			</div>
			<div class="pl-2">
				<i class="pi pi-ellipsis-h pointer" [matMenuTriggerFor]="menu"></i>
				<mat-menu #menu="matMenu">
					<button
						mat-menu-item
						*ngFor="let option of options"
						(click)="selectedOptionFxn(option)"
					>
						<span>{{ option.name }}</span>
					</button>
				</mat-menu>
			</div>
		</div>
	</div>
</div>
 -->
