.cdk-overlay-container {
	z-index: 12000 !important;
}

/* .chat-textarea {
	height: 50px !important;
	padding-top: 12px;
} */

.chat-textarea {
	height: 50px !important;
	padding-top: 12px;
	overflow-y: auto !important;

	&::-webkit-scrollbar {
		display: none;
	}
}

.chat-textarea:focus {
	border-radius: 0px;
	height: auto !important;
	max-height: 80px !important;

	&::-webkit-scrollbar {
		display: block;
	}
}

#chatBoardContent {
	height: 90vh;
	z-index: 1;
}

.writting-section {
	background-color: #eee;
	position: absolute;
	bottom: 5%;
	z-index: 2;
}

.chat-content {
	height: 100% !important;
	padding-bottom: 150px !important;
	position: relative;

	&.quote-message-active {
		padding-bottom: 250px !important;
	}
}

.chat-user-typing {
	ul {
		display: flex;
		margin-left: 2px;
		li {
			display: inline;
			margin: 0 2px;
		}
	}
}

.quote-message-contain {
	background: #cecece;
	border-radius: 5px 5px 30px 30px;
	.quote-message {
		position: relative;
		width: 100%;
		word-break: break-word;
		padding: 10px;
		max-height: 100px;
		overflow-y: auto;

		.close-quote-message-icon {
			position: absolute;
			right: 5px;
			top: 5px;
			color: white;
			font-weight: bold;
			cursor: pointer;
		}
	}
}

@media only screen and (max-width: 992px) {
	#chatBoardContent {
	}
	.writting-section {
		bottom: 5%;
	}
}

@media (max-width: 400px) {
	.writting-section {
		bottom: 10% !important;
	}
}
