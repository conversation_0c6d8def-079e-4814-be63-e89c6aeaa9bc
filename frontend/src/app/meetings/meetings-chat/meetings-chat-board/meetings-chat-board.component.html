<div id="chatBoardContent">
	<div
		class="d-flex align-items-center justify-content-between pl-4 pr-4 border-bottom pb-3 mb-3"
	>
		<div class="d-flex">
			<!-- <i class="pi pi-comments pointer mr-3 show-on-medium-and-up" (click)="showChatList()"></i> -->
			<i class="pi pi-video pointer"></i>
		</div>

		<div class="d-flex justify-content-center align-items-center">
			<b> {{ userMeeting.Username }} </b>
		</div>
		<i class="pi pi-info-circle pointer text-primary"></i>
	</div>
	<div
		class="w-100 mt-4 pr-0 pl-0 chat-content p-3"
		id="mettings-message-container"
		[ngClass]="{ 'quote-message-active': meetingMessageQuote }"
	>
		<p-scrollPanel
			#messagesBox
			[style]="{ width: '100%', height: '100%', paddingBottom: '10px' }"
		>
			<app-meetings-chat-message
				*ngFor="let message of messages"
				[parent]="message.Parent"
				[message]="message"
				[replyMessage]="message.Parent"
				[currentUser]="user"
				[usernameMeeting]="userMeeting.Username"
				(onDeleteMessage)="deleteMessage($event)"
				(onUpdateMessage)="updateMessage($event)"
				(onQuoteMessage)="quoteMessage($event)"
				(onCopyMettingMessage)="copyMettingMessage($event)"
			>
			</app-meetings-chat-message>
			<span id="endPage"></span>
			<!-- <app-meetings-chat-message [userMessage]="true"></app-meetings-chat-message>
            <app-meetings-chat-message [userMessage]="true" ></app-meetings-chat-message>
            <app-meetings-chat-message [userMessage]="false" [isReplyMessage]="true"></app-meetings-chat-message> -->
		</p-scrollPanel>
		<div
			class="row p-3 w-100 writting-section"
			*ngIf="meeting.Status == meetingStatus.RUNNING"
		>
			<div class="col-sm-9 col-lg-10 pr-0">
				<div *ngIf="usersTyping.length > 0" class="chat-user-typing">
					<ul>
						<li *ngFor="let user of usersTyping; let i = index">
							{{
								i === 0
									? user.username
									: i === usersTyping.length - 1
									? ' and ' + user.username
									: i > 0
									? ', ' + user.username
									: user.username
							}}
						</li>
						<li>Typing....</li>
					</ul>
				</div>

				<div [ngClass]="{ 'quote-message-contain': meetingMessageQuote }">
					<div class="quote-message" *ngIf="meetingMessageQuote">
						<span class="close-quote-message-icon" (click)="closeQuoteMessage()"
							>X</span
						>
						{{ meetingMessageQuote.Content }}
					</div>

					<textarea
						[placeholder]="'meeting.page.sendTO' | translate"
						style="width: 100%; height: 100%; border-radius: 50px"
						[cols]="28"
						pInputTextarea
						autoResize="false"
						class="pl-3 pt-3 pb-4 chat-textarea"
						name="inputMessage"
						[(ngModel)]="inputMessage"
						(keyup)="getInputMessageData($event)"
						(keydown)="handleKeyDown($event)"
						(blur)="handleBlur($event)"
					></textarea>
				</div>
			</div>
			<div class="col-sm-3 col-lg-2 chat-actions">
				<div
					class="d-flex w-100 p-0 pl-3 align-items-center"
					style="height: 100%"
				>
					<button
						[pTooltip]="'meeting.page.attachments' | translate"
						tooltipPosition="top"
						pButton
						pRipple
						type="button"
						icon="pi pi-paperclip"
						class="p-button-rounded p-button-danger btn-upload"
					>
						<input
							type="file"
							name=""
							(change)="uploadImage($event)"
							style="opacity: 0; width: 100%; position: absolute"
						/>
					</button>
					<button
						(click)="sendMessage()"
						[pTooltip]="'meeting.page.send' | translate"
						tooltipPosition="top"
						pButton
						pRipple
						type="button"
						icon="pi pi-send"
						class="p-button-rounded ml-3 btn-send"
					></button>
				</div>
			</div>
		</div>
	</div>
</div>
