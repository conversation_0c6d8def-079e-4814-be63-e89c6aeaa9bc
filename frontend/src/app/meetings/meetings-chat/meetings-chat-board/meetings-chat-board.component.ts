import {
	Component,
	Input,
	OnInit,
	ViewChild,
	ViewEncapsulation
} from '@angular/core';
import { MeetingMessageService } from '~/app/services/meetings/meeting-message.service';
import { MettingMessage } from '~/app/models/metting-message.model';
import { UserService } from '~/app/services/users/user.service';
import { User } from '~/app/models/user.model';
import { ToastrService } from 'ngx-toastr';
import { Meetings, MeetingStatus } from '~/app/models/meetings';
import { MeetingSocketService } from '~/app/services/websockets/meetings/meeting-socket.service';
import { ActivatedRoute, Router } from '@angular/router';
import { MeetingsService } from '~/app/services/meetings/meetings.service';
import { HomeLoginComponent } from '~/app/home/<USER>/home-login.component';
import { UtilsService } from '~/app/services/utils.service';
import { AbonnementsService } from '~/app/services/abonnements/abonnements.service';
declare const $: any;

@Component({
	selector: 'app-meetings-chat-board',
	templateUrl: './meetings-chat-board.component.html',
	styleUrls: ['./meetings-chat-board.component.scss'],
	encapsulation: ViewEncapsulation.None
})
export class MeetingsChatBoardComponent implements OnInit {
	@ViewChild('messagesBox') messagesBox: any;
	@Input() userMeeting: any;
	@Input() meeting: Meetings;
	@Input() username: string;

	m: Meetings;
	meetingSlug: string;

	options = [];
	meetingStatus = MeetingStatus;

	messages: any[];
	//   [
	//   { message: { text: 'orem ipsum dolor sit amet consectetur, adipisicing elit. Saepe eligendi id, placeat aperiam blanditiis tempora voluptate consequat', name: 'Arthur Ngoben', date: '20 Fevrier 2021 , 11:30' }, replyMessage: { text: '', name: '', date: '' }, isReplyMessage: false, userMessage: false },
	//   { message: { text: 'orem ipsum dolor sit amet consectetur, adipisicing elit. Saepe eligendi id, placeat aperiam blanditiis tempora voluptate consequat', name: 'Philippe Steve', date: '20 Fevrier 2021 , 11:32' }, replyMessage: { text: '', name: '', date: '' }, isReplyMessage: false, userMessage: true },
	//   { message: { text: 'orem ipsum dolor sit amet consectetur, adipisicing elit. Saepe eligendi id, placeat aperiam blanditiis tempora voluptate consequat', name: 'Philippe Steve', date: '20 Fevrier 2021 , 11:35' }, replyMessage: { text: 'orem ipsum dolor sit amet consectetur, adipisicing elit. Saepe eligendi id, placeat aperiam blanditiis tempora voluptate consequat', name: 'Arthur Ngoben', date: '20 Fevrier 2021 , 11:30' }, isReplyMessage: true, userMessage: true },
	//   { message: { text: 'orem ipsum dolor sit amet consectetur, adipisicing elit. Saepe eligendi id, placeat aperiam blanditiis tempora voluptate consequat', name: 'Arthur Ngoben', date: '20 Fevrier 2021 , 11:40' }, replyMessage: { text: 'orem ipsum dolor sit amet consectetur, adipisicing elit. Saepe eligendi id, placeat aperiam blanditiis tempora voluptate consequat', name: 'Philippe Steve', date: '20 Fevrier 2021 , 11:35' }, isReplyMessage: true, userMessage: false }
	// ];
	inputMessage = '';
	meetingMessageQuote: MettingMessage = null;
	sideBarToogle = false;
	user: User = new User();
	typing: boolean;
	usersTyping: any[];

	constructor(
		private meetingMessageService: MeetingMessageService,
		private userService: UserService,
		private toastr: ToastrService,
		private meetingSocketService: MeetingSocketService,
		private activatedRoute: ActivatedRoute,
		private meetingService: MeetingsService,
		private utilsService: UtilsService,
		private abonnementService: AbonnementsService,
		private router: Router
	) {
		this.typing = true;
		this.usersTyping = [];
	}

	async ngOnInit(): Promise<void> {
		console.log('Meeting::::>', this.meeting);
		this.meetingSlug = this.activatedRoute.snapshot.params.slug;
		this.m = await this.meetingService.getBySlug(this.meetingSlug).toPromise();
		console.log('Meeting:::///>', this.m);

		this.initSocketListeners();

		//this.user = await this.userService.getUserConnected().toPromise();
		this.getMessages();

		this.options = [
			{ name: 'Copier', icon: 'delete', id_option: 1 },
			{ name: 'Modifer', icon: 'payment', id_option: 2 },
			{ name: 'Citer', icon: 'payment', id_option: 2 },
			{ name: 'Supprimer', icon: 'payment', id_option: 2 }
		];

		// if (this.user) {
		// 	const abonnement = await this.abonnementService
		// 		.getUserActiveAbonnement(this.user.Slug)
		// 		.toPromise();
		// 	if (!abonnement || abonnement.Forfait.Name.toLowerCase() !== 'premium') {
		// 		this.router.navigateByUrl('/home/<USER>');
		// 		this.toastr.warning(
		// 			'Brain maker',
		// 			'Veuillez chosoir un forfait adequat pour avoir accès au meeting'
		// 		);
		// 	}
		// } else {
		// 	this.utilsService.buildModal(HomeLoginComponent, () => {}, 500, true, {});
		// }
	}

	initSocketListeners(): void {
		// Join meeting channel
		this.meetingSocketService.JoinMeetingChatRoom(this.m.Slug).subscribe(() => {
			//
		});

		// Listen new message
		this.meetingSocketService
			.ListenNewMessage()
			.subscribe((message: MettingMessage) => {
				this.messages.push(message);

				setTimeout(() => {
					this.messagesBox.scrollTop(1000000);
				}, 300);
			});

		// Listen user typing
		this.meetingSocketService.ListenTyping().subscribe((username: string) => {
			console.log('Typing: ', username);
			if (this.username !== username) {
				const exist = this.usersTyping.find((u) => u.username === username);
				if (!exist) {
					this.usersTyping.push({ username });

					setTimeout(() => {
						this.userNoTyping(username);
					}, 5000);
				}
			}
		});

		// Listen user stop typing
		this.meetingSocketService
			.ListenStopTyping()
			.subscribe((username: string) => {
				this.userNoTyping(username);
			});
	}

	selectedOptionFxn(opt): void {}

	showChatList(): void {}

	uploadImage(event) {}

	getInputMessageData(event: KeyboardEvent): void {
		console.log(event.key);
		if (event.key === 'Enter') {
			// On 'Shift+Enter' do this...
			this.sendMessage();
		}
	}
	async sendMessage(): Promise<void> {
		this.userNoTyping(this.username);
		this.meetingSocketService
			.EmitUserStopTyping(this.username, this.m.Slug)
			.subscribe(() => {
				//
			});
		// console.log(this.inputMessage);
		if (this.inputMessage.length !== 0) {
			let message: MettingMessage = {
				From: this.userMeeting.Slug ? this.userMeeting : null,
				UsernameMeeting: this.userMeeting.Username,
				Content: this.inputMessage,
				Id: 0,
				Slug: '',
				Meeting: this.m,
				Parent: this.meetingMessageQuote
			};

			try {
				const result = await this.meetingMessageService
					.add(message)
					.toPromise();
				//this.messages.push(result);
				this.messagesBox.scrollTop(1000000);

				// this.messages.push({
				//   message: {
				//     text: this.inputMessage, name: 'Philippe Steve',
				//     date: new Date().toString()
				//   }, replyMessage: { text: '', name: '', date: '' },
				//   isReplyMessage: false, userMessage: true
				// });
				// const element = document.querySelector('#endPage');

				// // scroll to element
				// element.scrollIntoView();
				// tslint:disable-next-line: no-unused-expression
				// $('#x')[0].scrollHeight;
				$('#message_container').scrollTop = $(
					'#message_container'
				).scrollHeight;
				this.inputMessage = '';
				this.meetingMessageQuote = null;
			} catch (error) {
				//let message = await this.translateService.get('admin.misc.editStatusError').toPromise();
				this.toastr.error('Error to send message', 'Brain-maker');
			}
		}
	}

	async getMessages() {
		try {
			const result = await this.meetingMessageService
				.getAllForMeeting(this.m.Slug)
				.toPromise();
			this.messages = result.map((m) => {
				if (m.From)
					return {
						...m,
						From: { ...m.From, ...this.userService.formatUserFields(m.From) }
					};
				else return { ...m };
			});

			console.log('MESSAGES ===> ', this.messages);
		} catch (error) {
			console.log('metting error', error);
		}
	}

	async deleteMessage(meetingMessage: MettingMessage) {
		try {
			const result = await this.meetingMessageService
				.delete(meetingMessage)
				.toPromise();

			const foundIndexDeleted = this.messages.findIndex(
				(m) => m.Id === meetingMessage.Id
			);
			this.messages.splice(foundIndexDeleted, 1);
		} catch (error) {
			console.log('metting message delete error', error);
			//let message = await this.translateService.get('admin.misc.editStatusError').toPromise();
			this.toastr.error('Error to delete message', 'Brain-maker');
		}
	}

	async updateMessage(meetingMessage: MettingMessage) {
		try {
			const result = await this.meetingMessageService
				.edit(meetingMessage)
				.toPromise();

			const foundIndexUpdated = this.messages.findIndex(
				(m) => m.Id === meetingMessage.Id
			);
			this.messages.splice(foundIndexUpdated, 1, result);
		} catch (error) {
			console.log('metting message update error', error);
			//let message = await this.translateService.get('admin.misc.editStatusError').toPromise();
			this.toastr.error('Error to update message', 'Brain-maker');
		}
	}

	closeQuoteMessage() {
		this.meetingMessageQuote = null;
	}

	quoteMessage(meetingMessage: MettingMessage) {
		this.meetingMessageQuote = meetingMessage;
	}

	handleKeyDown(event: KeyboardEvent): void {
		console.log('key down');

		if (event.key === 'Enter') {
			// this.userNoTyping(this.username);
			this.meetingSocketService
				.EmitUserStopTyping(this.username, this.m.Slug)
				.subscribe(() => {
					//
				});
		}

		this.meetingSocketService
			.EmitTyping(this.username, this.m.Slug)
			.subscribe(() => {
				//
			});
	}

	handleBlur(event: Event): void {
		console.log('Blur');
		this.meetingSocketService
			.EmitUserStopTyping(this.username, this.m.Slug)
			.subscribe(() => {
				//
			});
	}

	private userNoTyping(username: string): void {
		const index = this.usersTyping.indexOf(
			this.usersTyping.find((u) => u.username === username)
		);
		if (index >= 0) {
			this.usersTyping.splice(index, 1);
		}
	}

	copyTextToClipboard(text): boolean {
		const txtArea = document.createElement('textarea');
		txtArea.id = 'txt';
		txtArea.style.position = 'fixed';
		txtArea.style.top = '0';
		txtArea.style.left = '0';
		txtArea.style.opacity = '0';
		txtArea.value = text;
		document.body.appendChild(txtArea);
		txtArea.select();

		try {
			const successful = document.execCommand('copy');
			const msg = successful ? 'successful' : 'unsuccessful';
			console.log('Copying text command was ' + msg);
			if (successful) {
				return true;
			}
		} catch (err) {
			console.log('Oops, unable to copy');
		} finally {
			document.body.removeChild(txtArea);
		}
		return false;
	}

	copyMettingMessage(message) {
		if (this.copyTextToClipboard(message.Content)) {
			this.toastr.info('Copied', 'Message');
		} else {
			this.toastr.error('Error to copy', 'Message');
		}
	}
}
