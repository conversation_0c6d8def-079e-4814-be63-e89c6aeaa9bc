import {
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	ViewChild
} from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import {
	DiscussionMessage,
	FileType
} from '~/app/models/discussions/discussion-message';
import { DiscussionMessageService } from '../../../services/discussions/discussion-message.service';
import { environment } from '~/environments/environment';
import { UtilsService } from '~/app/services/utils.service';
import { Media } from '~/app/models/media.model';
import { PlyrComponent } from 'ngx-plyr';
import { HomeLoginComponent } from '~/app/home/<USER>/home-login.component';
import { AbonnementsService } from '~/app/services/abonnements/abonnements.service';
import { UserService } from '~/app/services/users/user.service';
import { User } from '~/app/models/user.model';
import { Router } from '@angular/router';

@Component({
	selector: 'app-discussion-message',
	templateUrl: './discussion-message.component.html',
	styleUrls: ['./discussion-message.component.scss']
})
export class DiscussionMessageComponent implements OnInit {
	@Output('onDeleteMessage')
	deleteMessageEvent = new EventEmitter<DiscussionMessage>();
	@Output('onUpdateMessage')
	updateMessageEvent = new EventEmitter<DiscussionMessage>();
	@Output('onQuoteMessage')
	quoteMessageEvent = new EventEmitter<DiscussionMessage>();
	@Output('onCopyDicussionMessage')
	copyDicussionMessage = new EventEmitter<DiscussionMessage>();

	@Output('onDisplayAttachmentFile')
	onDisplayAttachmentFile = new EventEmitter<{ Media: Media; Type: number }>();

	@Input('message') message: DiscussionMessage = new DiscussionMessage(
		null,
		null,
		'',
		false
	);
	@Input('replyMessage') replyMessage = new DiscussionMessage(
		null,
		null,
		'',
		false
	);

	@Input('currentUser') currentUser;

	showUpdate = false;

	showResponse = false;
	options = [];

	FILE_TYPE = FileType;

	attachmentFileType: number;
	attachmentFileName: string;
	attachmentFileBlob: Blob;
	attachmentFileLink: string;
	loadingDownload: boolean;

	@ViewChild(PlyrComponent)
	plyr: PlyrComponent;

	// or get it from plyrInit event
	player: Plyr;

	audioSources: Plyr.Source[] = [];

	user: User;

	constructor(
		private toastr: ToastrService,
		private discussionMessageService: DiscussionMessageService,
		private utilsService: UtilsService,
		private abonnementService: AbonnementsService,
		private userService: UserService,
		private router: Router
	) {
		this.loadingDownload = false;
	}

	async ngOnInit(): Promise<void> {
		// console.log('Message', this.message.Attachement ? this.message : '');

		// console.log('****');

		if (this.userService.isConnected()) {
			this.user = await this.userService.getUserConnected().toPromise();
		}
		//console.log('Message: ', this.message);
		if (this.message.Attachement) {
			this.attachmentFileName = this.message.Attachement.Name;
			this;
			const blobFile = await this.utilsService
				.getFileFromApi(
					`${environment.path}/${this.message.Attachement.Hashname}`
				)
				.toPromise();
			this.attachmentFileBlob = blobFile;
			// this.attachmentFileLink = window.URL.createObjectURL(blobFile);
			// console.log('blob link ----> ', this.attachmentFileLink);
			// console.log(this.message.Attachement, blobFile.type);
			this.attachmentFileLink = `${environment.path}/${this.message.Attachement.Hashname}`;
			console.log('File link', this.attachmentFileLink);

			this.audioSources = [
				{
					src: this.attachmentFileLink,
					type: blobFile.type
				}
			];

			switch (blobFile.type.split('/')[0]) {
				case 'image':
					console.log('Type: IMAGE');
					this.attachmentFileType = this.FILE_TYPE.IMAGE;
					break;
				case 'video':
					console.log('Type: VIDEO');
					this.attachmentFileType = this.FILE_TYPE.VIDEO;
					break;
				case 'audio':
					console.log('Type: audio');
					this.attachmentFileType = this.FILE_TYPE.AUDIO;
					break;
				default:
					console.log('Type: autre');
					this.attachmentFileType = this.FILE_TYPE.OTHER;
			}

			console.log(
				'ATTACHEMENT TYPE: ',
				this.attachmentFileType,
				this.message.Attachement ? this.message.Attachement.Name : ''
			);

			if (!this.message.Message) {
				this.options = [
					{ name: 'Telecharger', icon: 'delete', id_option: 5 },
					this.isCurrentHadSent()
						? { name: 'Supprimer', icon: 'payment', id_option: 4 }
						: null
				].filter(Boolean);
			} else {
				this.options = [
					{ name: 'Copier', icon: 'delete', id_option: 1 },
					this.isCurrentHadSent()
						? { name: 'Modifer', icon: 'payment', id_option: 2 }
						: null,
					{ name: 'Citer', icon: 'payment', id_option: 3 },
					this.isCurrentHadSent()
						? { name: 'Supprimer', icon: 'payment', id_option: 4 }
						: null
				].filter(Boolean);
			}
		} else {
			this.options = [
				{ name: 'Copier', icon: 'delete', id_option: 1 },
				this.isCurrentHadSent()
					? { name: 'Modifer', icon: 'payment', id_option: 2 }
					: null,
				{ name: 'Citer', icon: 'payment', id_option: 3 },
				this.isCurrentHadSent()
					? { name: 'Supprimer', icon: 'payment', id_option: 4 }
					: null
			].filter(Boolean);
		}
	}

	isCurrentHadSent() {
		return this.currentUser.Id == this.message.SendBy.Id;
	}

	formatDate(date) {
		return new Date(date).toDateString();
	}

	formaTime(date) {
		return new Date(date).toLocaleTimeString();
	}

	toogleShowResponse(): void {
		this.showResponse = !this.showResponse;
	}
	selectedOptionFxn(option): void {
		switch (option.id_option) {
			case 1:
				this.copyDicussionMessage.emit(this.message);
				break;
			case 2:
				this.showUpdate = true;
				break;
			case 3:
				/* if (!this.showResponse) {
					this.toogleShowResponse();
				} */
				this.quoteMessageEvent.emit(this.message);
				break;
			case 4:
				this.deleteMessageEvent.emit(this.message);
				break;

			case 5:
				this.downloadFile();
				break;

			default:
				break;
		}
	}

	updateMessage(value) {
		this.updateMessageEvent.emit({ ...this.message, Message: value });
		this.showUpdate = false;
	}

	async downloadFile(): Promise<void> {
		try {
			this.loadingDownload = true;
			console.log('telechargemnt en cours');
			const base64File = await this.utilsService.blobToBase64(
				this.attachmentFileBlob
			);

			// console.log(base64File);
			const downloadLink = document.createElement('a');

			downloadLink.href = base64File;
			downloadLink.download = this.attachmentFileName;
			downloadLink.click();
			console.log('telechargemnt terminé');

			this.loadingDownload = false;
		} catch (error) {
			this.toastr.error(
				'Erreur',
				'Impossible de télécharger le fichier, veuillez réssayer plustard'
			);
			this.loadingDownload = false;
		}
	}

	readFile(): void {
		if (this.attachmentFileType === this.FILE_TYPE.AUDIO) {
			return;
		}
		if (this.attachmentFileType === this.FILE_TYPE.OTHER) {
			this.downloadFile();
			return;
		}
		console.log('Read file');
		// const link = `${environment.path}/${this.message.Attachement.Hashname}`;
		this.onDisplayAttachmentFile.emit({
			Media: this.message.Attachement,
			Type: this.attachmentFileType
		});
	}
	played(event: Plyr.PlyrEvent) {
		console.log('played', event);
	}

	play(): void {
		this.player.play(); // or this.plyr.player.play()
	}

	pause(): void {
		this.player.pause(); // or this.plyr.player.play()
	}

	stop(): void {
		this.player.stop(); // or this.plyr.player.stop()
	}
}
