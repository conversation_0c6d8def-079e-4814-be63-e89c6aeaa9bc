.current-user {
	background-color: #49a3f1;
	color: white;

	.reply-container {
		background: #f2f2f2;
		color: black;
		/* padding: 5px; */
		border: 5px solid #49a3f1;
		border-radius: 0em 2em 0em 0em !important;

		.reply {
			font-style: italic;
			line-height: 20px;
		}
	}
}

.remote-user {
	background-color: #f2f2f2;

	.reply-container {
		background: #49a3f1;
		color: white;
		/* padding: 5px; */
		border: 5px solid #eeebeb;
		border-radius: 0em 2em 0em 0em !important;

		.reply {
			font-style: italic;
			line-height: 20px;
		}
	}
}

.attachment-image {
	overflow: hidden;
	img {
		max-width: 100%;
	}
}

.discussion-message {
	.current-user,
	.remote-user {
		max-width: 500px;
		word-break: break-word;
		border-radius: 0em 2em 0em 2em !important;
	}
}

@media screen and (max-width: 580px) {
	.discussion-message {
		.current-user,
		.remote-user {
			max-width: 300px;
			word-break: break-word;
		}
	}
}
