<div class="row discussion-message">
	<div
		class="d-flex w-100 pl-4 pr-4 col-sm-12 col-lg-12 mb-3"
		[ngClass]="{
			'justify-content-end': isCurrentHadSent(),
			'justify-content-start': !isCurrentHadSent()
		}"
		style="position: relative"
	>
		<div class="d-flex mb-0 left">
			<img
				[src]="
					message.SendBy
						? message.SendBy.photoFormated
						: '/assets/img/default_user.png'
				"
				[alt]="message.SendBy.Firstname"
				class="mr-3"
				srcset=""
				style="height: 30px; width: 30px; border-radius: 50%"
			/>
			<div class="d-flex flex-column" style="position: relative">
				<small
					>{{ message.SendBy.Firstname }} ,
					{{ formatDate(message.CreatedAt) | dateAgo }}</small
				>
				<div
					class="rounded"
					[class]="isCurrentHadSent() ? 'current-user' : 'remote-user'"
				>
					<div
						class="d-flex flex-column p-2 reply-container"
						*ngIf="replyMessage"
					>
						<div>
							<p class="reply">{{ replyMessage.Message }}</p>
							<small
								>{{ replyMessage.SendBy.Firstname }} ,
								{{ formatDate(replyMessage.CreatedAt) }}</small
							>
							<hr class="mt-1 mb-0" />
						</div>
					</div>
					<div
						*ngIf="message && message.Attachement"
						class="message-attachment"
					>
						<div (click)="readFile()">
							<div
								class="attachment-image"
								*ngIf="attachmentFileType === FILE_TYPE.IMAGE"
							>
								<img [src]="attachmentFileLink" alt="message attachment" />
							</div>

							<div
								class="attachment-video"
								*ngIf="attachmentFileType === FILE_TYPE.VIDEO"
							>
								<span><mat-icon>play_arrow</mat-icon></span>
								<video>
									<source [src]="attachmentFileLink" />

									Your browser does not support the video tag.
								</video>
							</div>

							<div class="attachment-label">
								<mat-icon *ngIf="attachmentFileType === FILE_TYPE.OTHER">
									insert_drive_file</mat-icon
								>&nbsp;
								<mat-icon *ngIf="attachmentFileType === FILE_TYPE.VIDEO">
									movie</mat-icon
								>&nbsp;
								<mat-icon *ngIf="attachmentFileType === FILE_TYPE.AUDIO">
									audiotrack</mat-icon
								>&nbsp;
								<mat-icon *ngIf="attachmentFileType === FILE_TYPE.IMAGE">
									image</mat-icon
								>&nbsp;

								<span>{{
									message.Attachement ? message.Attachement.Name : ''
								}}</span>
							</div>
						</div>
						<div *ngIf="attachmentFileType === FILE_TYPE.AUDIO">
							<!-- <audio controls autostart="0" autostart="false" preload="none">
								<source src="{{ attachmentFileLink }}" />
								Your browser does not support the audio element.
							</audio> -->
							<!-- <plyr
								[plyrCrossOrigin]="true"
								[plyrPlaysInline]="true"
								[plyrSources]="videoSources"
								(plyrInit)="player = $event"
								(plyrPlay)="played($event)"
							></plyr> -->

							<div
								mat-card-image
								plyr
								plyrTitle="Audio"
								plyrType="audio"
								[plyrPlaysInline]="true"
								[plyrCrossOrigin]="true"
								[plyrSources]="audioSources"
							></div>
						</div>
					</div>
					<div
						class="p-2 pt-1"
						*ngIf="!showUpdate && message && message.Message"
					>
						<span class="p-2 rounded">{{ message.Message }}</span>
					</div>

					<form *ngIf="showUpdate">
						<input
							#inputUpdate
							[value]="message.Message"
							class="p-2 pt-1"
							(keyup.enter)="updateMessage(inputUpdate.value)"
						/>
					</form>
				</div>
			</div>
			<div
				style="display: flex; justify-content: center; align-items: center"
				class="pl-2"
			>
				<span *ngIf="loadingDownload">
					<i class="fas fa-spin fa-spinner mr-2"></i>
				</span>
				<i
					*ngIf="!loadingDownload"
					class="pi pi-ellipsis-h pointer"
					[matMenuTriggerFor]="menu"
				></i>
				<mat-menu #menu="matMenu">
					<button
						mat-menu-item
						*ngFor="let option of options"
						(click)="selectedOptionFxn(option)"
					>
						<span>{{ option.name }}</span>
					</button>
				</mat-menu>
			</div>
		</div>
	</div>
</div>
