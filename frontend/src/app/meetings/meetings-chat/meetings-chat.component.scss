.chat-container {
	/* background: blanche<PERSON>mond; */
	overflow: hidden;
	height: 100vh;
}

.section-contain {
	height: 100vh;
	padding-bottom: 0;
	overflow: hidden;
}

.chat-content {
	height: 100%;
}

.toogle-side-icon {
	visibility: hidden;
}

.cdk-overlay-container {
	z-index: 12000 !important;
}

.discussion-chat-textarea {
	width: 100%;
	border-radius: 50px;
	height: 50px !important;
	padding-top: 12px;
	overflow-y: auto !important;

	&::-webkit-scrollbar {
		display: none;
	}
}

.discussion-chat-textarea:focus {
	border-radius: 0px;
	height: auto !important;
	max-height: 100px !important;

	&::-webkit-scrollbar {
		display: block;
	}
}

#chatBoardContent {
	/* height: 60vh; */
	z-index: 1;
}

.discussion-writting-section {
	background-color: #eee;
	position: absolute;
	bottom: -35px;
	z-index: 2;
}

.chat-content {
	height: 69vh !important;
	position: relative;
}

.chat-menu-item {
	border-radius: 10px;
	min-height: 40px;
}

.chat-menu-list .active {
	// background: #ff4081;
	background: #729fd29c;
	color: white !important;
}

.chat-menu-item .active h3 {
	color: white !important;
}

.message-container.quote-message-active {
	.p-scrollpanel-wrapper {
		padding-bottom: 100px !important;
	}
}

.discussion-message-container {
	height: 100%;
	padding-bottom: 10px;
}

.quote-message-contain {
	background: #cecece;
	border-radius: 5px 5px 30px 30px;
	.quote-message {
		position: relative;
		width: 100%;
		word-break: break-word;
		padding: 10px;
		max-height: 100px;
		overflow-y: auto;

		.close-quote-message-icon {
			position: absolute;
			right: 5px;
			top: 5px;
			color: white;
			font-weight: bold;
			cursor: pointer;
		}
	}
}

.chat-sidebar {
	background-color: #eceff1;
	z-index: 9;
}

.message-container .p-scrollpanel-wrapper {
	padding-bottom: 60px !important;
}

// .chat-menu-item .active {
//     background: #afaaac;
// }
.chat-menu-item:hover {
	background: #afaaac;
	// color: white !important;
}

.search_input {
	padding-left: 35px;
	height: 40px;
	background: #635e5e38;
	border: none;
	margin-bottom: 0px !important;
	width: 100%;
	border-radius: 50px !important;
}

p-autoComplete .p-inputtext {
	padding-left: 15px;
	height: 40px;
	/* background: #635e5e38; */
	border: none;
	margin-bottom: 0px !important;
	width: 100%;
	border-radius: 50px !important;
}

.chat-user-typing {
	ul {
		display: flex;
		margin-left: 2px;
		li {
			display: inline;
			margin: 0 2px;
		}
	}
}

.display-message-attachment {
	position: relative;
	width: 100%;
	height: 400px !important;
	// overflow: hidden;
	z-index: 1000000;

	img {
		width: 100%;
		max-height: 100%;
		object-fit: contain;
	}

	.video-player {
		width: 100%;
		height: 100%;
	}
}

// .plyr__poster {
// 	height: 100px !important;
// }

.plyr.plyr--full-ui.plyr--video.plyr--html5 {
	// max-height: 100% !important;

	// img {
	// 	width: 100px;
	// 	max-height: 100px;
	// 	object-fit: contain;
	// }

	// .plyr__controls {
	// 	.plyr__video-wrapper {
	// 		video {
	// 			height: 100% !important;
	// 		}
	// 	}
	// }
}

@media (max-width: 500px) {
	.vvv html {
		overflow-y: hidden;
	}

	.discussion-writting-section {
		bottom: -20px !important;
	}

	.chat-actions {
		/* & .btn-upload {
			display: none;
		} */

		& .btn-send {
			position: absolute;
			right: 5px;
			top: -50px;
		}

		& .btn-upload {
			position: absolute;
			right: 45px;
			top: -50px;
		}
	}
}

@media only screen and (max-width: 992px) {
	.chat-content {
		height: 70vh !important;
	}

	#chatBoardContent {
		/* height: 40vh; */
		z-index: 1;
	}
	.discussion-writting-section {
		background-color: #eee;
		bottom: -28px;
		z-index: 2;
	}

	.message-container .p-scrollpanel-wrapper {
		padding-bottom: 70px !important;
	}

	.toogle-side-icon {
		visibility: visible;
	}
}

@media only screen and (max-width: 992px) {
	.hide-on-med-and-down {
		display: none !important;
	}
	.camera-menu {
		height: 20vh;
	}
	.section-contain {
		height: 92vh;
		padding-top: 0;
	}
}
