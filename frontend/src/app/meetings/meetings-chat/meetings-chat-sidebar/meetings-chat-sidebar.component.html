<div class="card border-0 h-100 p-3 hide-on-med-and-down chat-sidebar">
    <p class="p-text-normal">Options</p>
    <div class="chat-menu-list">
        <div pRipple class="chat-menu-item d-flex align-items-center pl-2 pr-2 pointer">
            <i class="pi pi-file mr-3"></i>
            <span>{{'meeting.page.allFiles'|translate}}</span>
        </div>
    </div>
    <p class="p-text-normal">Discussions</p>
    <div class="w-100 mb-3">
        <span class="p-input-icon-left mr-4" style="width: 100%;height:40px"><i class="pi pi-search"></i><input
        pinputtext="" type="text" [placeholder]="'admin.misc.search'|translate" class="search_input text-white"></span>
    </div>
    <div class="chat-menu-list">
        <!-- <div pRipple class="chat-menu-item d-flex align-items-center pl-2 pr-2 pointer" (click)="openUserChat()">
            <img src="/assets/img/default_user.png" alt="" class="mr-1" srcset="" style="height:30px;width:30px;border-radius:50%">
            <div class="p-2">
                <span>Philippe steve (vous)</span>

            </div>
        </div> -->
        <div pRipple class="active chat-menu-item d-flex align-items-center pl-2 pr-2 pointer mt-2 justify-content-between" (click)="openUserChat()">
            <div class="d-flex align-items-center">
                <img src="/assets/img/default_user.png" alt="" class="mr-1" srcset="" style="height:30px;width:30px;border-radius:50%">
                <div class="d-flex flex-column p-2">
                    <span>Arthur Ngoben</span>
                    <small>Lorem ipsum dolor sit amet, c.... </small>
                </div>
            </div>

            <p-badge [value]="3" class="right" severity="danger"></p-badge>

        </div>
        <div pRipple class="chat-menu-item d-flex align-items-center pl-2 pr-2 pointer mt-2 justify-content-between" (click)="openUserChat()">
            <div class="d-flex align-items-center">
                <img src="/assets/img/default_user.png" alt="" class="mr-1" srcset="" style="height:30px;width:30px;border-radius:50%">
                <div class="d-flex flex-column p-2">
                    <span>Danick</span>
                    <small>Lorem ipsum dolor sit amet, c.... </small>
                </div>
            </div>

            <p-badge [value]="200" class="right" severity="danger"></p-badge>

        </div>
        <div pRipple class="chat-menu-item d-flex align-items-center pl-2 pr-2 pointer mt-2 justify-content-between" (click)="openUserChat()">
            <div class="d-flex align-items-center">
                <img src="/assets/img/default_user.png" alt="" class="mr-1" srcset="" style="height:30px;width:30px;border-radius:50%">
                <div class="d-flex flex-column p-2">
                    <span>Arno</span>
                    <small>Lorem ipsum dolor sit amet, c.... </small>
                </div>
            </div>

            <p-badge [value]="32" class="right" severity="danger"></p-badge>

        </div>
    </div>
</div>
<p-sidebar [(visible)]="sideBarToogle" [baseZIndex]="10000" [showCloseIcon]="true">
    <div class="card border-0 h-100 p-3 chat-sidebar">
        <p class="p-text-normal">Options</p>
        <div class="chat-menu-list">
            <div pRipple class="chat-menu-item d-flex align-items-center pl-2 pr-2 pointer">
                <i class="pi pi-file mr-3"></i>
                <span>{{'meeting.page.allFiles'|translate}}</span>
            </div>
        </div>
        <p class="p-text-normal">Discussions</p>
        <div class="w-100 mb-3">
            <span class="p-input-icon-left mr-4" style="width: 100%;height:40px"><i class="pi pi-search"></i><input
          pinputtext="" type="text" [placeholder]="'admin.misc.search'|translate"
          class="search_input text-white"></span>
        </div>
        <div class="chat-menu-list">
            <!-- <div pRipple class="chat-menu-item d-flex align-items-center pl-2 pr-2 pointer" (click)="openUserChat()">
              <img src="/assets/img/default_user.png" alt="" class="mr-1" srcset="" style="height:30px;width:30px;border-radius:50%">
              <div class="p-2">
                  <span>Philippe steve (vous)</span>

              </div>
          </div> -->
            <div pRipple class="active chat-menu-item d-flex align-items-center pl-2 pr-2 pointer mt-2 justify-content-between" (click)="openUserChat()">
                <div class="d-flex align-items-center">
                    <img src="/assets/img/default_user.png" alt="" class="mr-1" srcset="" style="height:30px;width:30px;border-radius:50%">
                    <div class="d-flex flex-column p-2">
                        <span>Arthur Ngoben</span>
                        <small>Lorem ipsum dolor sit amet, c.... </small>
                    </div>
                </div>

                <p-badge [value]="3" class="right" severity="danger"></p-badge>

            </div>
            <div pRipple class="chat-menu-item d-flex align-items-center pl-2 pr-2 pointer mt-2 justify-content-between" (click)="openUserChat()">
                <div class="d-flex align-items-center">
                    <img src="/assets/img/default_user.png" alt="" class="mr-1" srcset="" style="height:30px;width:30px;border-radius:50%">
                    <div class="d-flex flex-column p-2">
                        <span>Danick</span>
                        <small>Lorem ipsum dolor sit amet, c.... </small>
                    </div>
                </div>

                <p-badge [value]="200" class="right" severity="danger"></p-badge>

            </div>
            <div pRipple class="chat-menu-item d-flex align-items-center pl-2 pr-2 pointer mt-2 justify-content-between" (click)="openUserChat()">
                <div class="d-flex align-items-center">
                    <img src="/assets/img/default_user.png" alt="" class="mr-1" srcset="" style="height:30px;width:30px;border-radius:50%">
                    <div class="d-flex flex-column p-2">
                        <span>Arno</span>
                        <small>Lorem ipsum dolor sit amet, c.... </small>
                    </div>
                </div>

                <p-badge [value]="32" class="right" severity="danger"></p-badge>

            </div>
        </div>
    </div>
</p-sidebar>
<button (click)="sideBarToogle = true" mat-fab color="primary" class="hide-on-large-only" aria-label="Example icon button with a delete icon" style="position: fixed; bottom: 16vh; right: 15px; z-index: 99">
  <mat-icon>contacts</mat-icon>
</button>
