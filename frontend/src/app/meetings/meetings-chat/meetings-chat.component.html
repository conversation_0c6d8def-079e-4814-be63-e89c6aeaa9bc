<div class="red-skin chat-container">
	<div id="main-wrapper">
		<app-home-header></app-home-header>
		<app-mettings-nav-tab [page]="'discuss'"></app-mettings-nav-tab>
		<section class="section-contain pb-0">
			<div class="row" style="height: 90vh">
				<div class="col-sm-12 col-lg-3 pr-0">
					<!-- <app-meetings-chat-sidebar></app-meetings-chat-sidebar> -->

					<!-- Chat sidebar -->
					<div
						class="card border-0 h-100 p-3 hide-on-med-and-down chat-sidebar"
					>
						<p class="p-text-normal">Options</p>
						<div class="chat-menu-list">
							<div
								pRipple
								class="chat-menu-item d-flex align-items-center pl-2 pr-2 pointer"
							>
								<i class="pi pi-file mr-3"></i>
								<span>{{ 'meeting.page.allFiles' | translate }}</span>
							</div>
						</div>
						<p class="p-text-normal">Discussions</p>
						<div class="w-100 mb-3">
							<!-- <span
								class="p-input-icon-left mr-4"
								style="width: 100%; height: 40px"
								><i class="pi pi-search"></i
								><input
									pinputtext=""
									type="text"
									[placeholder]="'admin.misc.search' | translate"
									class="search_input text-white"
							/></span> -->
							<p-autoComplete
								class="p-input-icon-left mr-4"
								[(ngModel)]="userSearch"
								[suggestions]="usersSuggestions"
								(completeMethod)="searchUsers($event)"
								(onSelect)="suggestionSelected($event)"
								name="userSearch"
							>
								<ng-template let-user pTemplate="item">
									<img
										[src]="user.photoFormated"
										style="
											width: 32px;
											display: inline-block;
											margin: 5px 0 2px 5px;
										"
									/>
									<div
										style="font-size: 18px; float: right; margin: 10px 10px 0 0"
									>
										{{ user.Lastname }} {{ user.Firstname }}
									</div>
								</ng-template>
							</p-autoComplete>
						</div>
						<div class="chat-menu-list">
							<!-- <div pRipple class="chat-menu-item d-flex align-items-center pl-2 pr-2 pointer" (click)="openUserChat()">
                                <img src="/assets/img/default_user.png" alt="" class="mr-1" srcset="" style="height:30px;width:30px;border-radius:50%">
                                <div class="p-2">
                                    <span>Philippe steve (vous)</span>
                    
                                </div>
                            </div> -->
							<div
								*ngFor="let discussion of discussionsFormated"
								pRipple
								class="chat-menu-item d-flex align-items-center pl-2 pr-2 pointer mt-2 justify-content-between"
								[ngClass]="{ active: discussion.Id == selectedDiscussion.Id }"
								(click)="openUserChat(discussion)"
							>
								<div class="d-flex align-items-center">
									<img
										[src]="discussion.User.photoFormated"
										alt=""
										class="mr-1"
										srcset=""
										style="height: 30px; width: 30px; border-radius: 50%"
									/>
									<div class="d-flex flex-column p-2">
										<span
											>{{ discussion.User.Firstname }}
											{{ discussion.User.Lastname }}</span
										>
										<!-- <span *ngif="typingInDiscussion(discussion.Slug)">&nbsp;Typing...</span> -->
										<!-- <small>Lorem ipsum dolor sit amet, c.... </small> -->
									</div>
								</div>

								<!-- <p-badge [value]="3" class="right" severity="danger"></p-badge> -->
							</div>
							<!-- <div
								pRipple
								class="chat-menu-item d-flex align-items-center pl-2 pr-2 pointer mt-2 justify-content-between"
								(click)="openUserChat()"
							>
								<div class="d-flex align-items-center">
									<img
										src="/assets/img/default_user.png"
										alt=""
										class="mr-1"
										srcset=""
										style="height: 30px; width: 30px; border-radius: 50%"
									/>
									<div class="d-flex flex-column p-2">
										<span>Danick</span>
										<small>Lorem ipsum dolor sit amet, c.... </small>
									</div>
								</div>

								<p-badge
									[value]="200"
									class="right"
									severity="danger"
								></p-badge>
							</div>
							<div
								pRipple
								class="chat-menu-item d-flex align-items-center pl-2 pr-2 pointer mt-2 justify-content-between"
								(click)="openUserChat()"
							>
								<div class="d-flex align-items-center">
									<img
										src="/assets/img/default_user.png"
										alt=""
										class="mr-1"
										srcset=""
										style="height: 30px; width: 30px; border-radius: 50%"
									/>
									<div class="d-flex flex-column p-2">
										<span>Arno</span>
										<small>Lorem ipsum dolor sit amet, c.... </small>
									</div>
								</div>

								<p-badge [value]="32" class="right" severity="danger"></p-badge>
							</div> -->
						</div>
					</div>

					<p-sidebar
						[(visible)]="sideBarToogle"
						position="left"
						[baseZIndex]="10000"
					>
						<div class="card border-0 h-100 p-3 chat-sidebar">
							<p class="p-text-normal">Options</p>
							<div class="chat-menu-list">
								<div
									pRipple
									class="chat-menu-item d-flex align-items-center pl-2 pr-2 pointer"
								>
									<i class="pi pi-file mr-3"></i>
									<span>{{ 'meeting.page.allFiles' | translate }}</span>
								</div>
							</div>
							<p class="p-text-normal">Discussions</p>
							<div class="w-100 mb-3">
								<p-autoComplete
									class="p-input-icon-left mr-4"
									[(ngModel)]="userSearch"
									[suggestions]="usersSuggestions"
									(completeMethod)="searchUsers($event)"
									(onSelect)="suggestionSelected($event)"
									name="userSearch"
								>
									<ng-template let-user pTemplate="item">
										<img
											[src]="user.photoFormated"
											style="
												width: 32px;
												display: inline-block;
												margin: 5px 0 2px 5px;
											"
										/>
										<div
											style="
												font-size: 18px;
												float: right;
												margin: 10px 10px 0 0;
											"
										>
											@{{ user.Username }}
										</div>
									</ng-template>
								</p-autoComplete>
							</div>
							<div class="chat-menu-list">
								<div
									*ngFor="let discussion of discussionsFormated"
									pRipple
									class="chat-menu-item d-flex align-items-center pl-2 pr-2 pointer mt-2 justify-content-between"
									[ngClass]="{ active: discussion.Id == selectedDiscussion.Id }"
									(click)="openUserChat(discussion)"
								>
									<div class="d-flex align-items-center">
										<img
											[src]="discussion.User.photoFormated"
											alt=""
											class="mr-1"
											srcset=""
											style="height: 30px; width: 30px; border-radius: 50%"
										/>
										<div class="d-flex flex-column p-2">
											<span
												>{{ discussion.User.Firstname }}
												{{ discussion.User.Lastname }}</span
											>
										</div>
									</div>
								</div>
							</div>
						</div>
					</p-sidebar>
					<!-- -END- Chat siderbar-->
				</div>
				<div class="col-sm-12 col-lg-9 pl-0">
					<!-- <app-meetings-chat-board></app-meetings-chat-board> -->

					<!--  Chat board-->
					<div id="chatBoardContent">
						<div
							class="d-flex align-items-center justify-content-between pl-4 pr-4 pt-3 border-bottom pb-3 mb-3"
						>
							<div class="d-flex">
								<!-- <i class="pi pi-comments pointer mr-3 show-on-medium-and-up" (click)="showChatList()"></i> -->
								<i
									class="pi pi-comment pointer toogle-side-icon"
									(click)="sideBarToogle = !sideBarToogle"
								></i>
							</div>

							<div class="d-flex justify-content-center align-items-center">
								<b>{{ user ? user.Lastname + ' ' + user.Firstname : '' }}</b>
							</div>
							<i class="pi pi-info-circle pointer text-primary"></i>
						</div>
						<div
							class="message-container w-100 mt-4 pr-0 pl-0 chat-content p-3"
							[ngClass]="{ 'quote-message-active': discussionMessageQuote }"
						>
							<div *ngIf="loadingDiscussionMessages">
								<i class="fas fa-spin fa-spinner mr-2"></i>&nbsp;Chargement....
							</div>

							<p-scrollPanel
								#messagesBox
								[style]="{
									width: '100%',
									height: '100%'
								}"
							>
								<div
									class="discussion-message-container"
									*ngIf="!loadingDiscussionMessages"
								>
									<app-discussion-message
										*ngFor="let message of discussionsMessage"
										[message]="message"
										[currentUser]="user"
										[replyMessage]="message.Parent"
										(onDeleteMessage)="deleteMessage($event)"
										(onUpdateMessage)="updateMessage($event)"
										(onQuoteMessage)="quoteMessage($event)"
										(onCopyDicussionMessage)="copyDiscussionMessage($event)"
										(onDisplayAttachmentFile)="displayAttachmentFile($event)"
									>
									</app-discussion-message>
									<span id="endPage"></span>
								</div>
							</p-scrollPanel>
							<div
								class="row p-3 w-100 discussion-section discussion-writting-section"
							>
								<div class="col-sm-9 col-lg-10 pr-0">
									<div
										*ngIf="typingInCurrentChat && usersTyping.length > 0"
										class="chat-user-typing"
									>
										<ul>
											<li *ngFor="let user of usersTyping; let i = index">
												{{
													typingInCurrentChat
														? user.username
														: i !== 0
														? ' and ' + user.username
														: ''
												}}
											</li>
											<li>Typing ....</li>
										</ul>
									</div>
									<!-- <div>Louis is typing ...</div> -->
									<div
										[ngClass]="{
											'quote-message-contain':
												discussionMessageQuote || attachmentFile
										}"
									>
										<!-- <div class="quote-message-contain"> -->
										<div class="quote-message" *ngIf="discussionMessageQuote">
											<span
												class="close-quote-message-icon"
												(click)="closeQuoteMessage()"
											>
												<mat-icon>close</mat-icon>
											</span>
											{{ discussionMessageQuote.Message }}
										</div>
										<div class="quote-message" *ngIf="attachmentFile">
											<span
												class="close-quote-message-icon"
												(click)="cancelAttachment()"
												><mat-icon>close</mat-icon>
											</span>
											<mat-icon
												*ngIf="
													attachmentFile &&
													attachmentFileType === FILE_TYPE.OTHER
												"
											>
												insert_drive_file</mat-icon
											>&nbsp;
											<mat-icon
												*ngIf="
													attachmentFile &&
													attachmentFileType === FILE_TYPE.VIDEO
												"
											>
												movie</mat-icon
											>&nbsp;
											<mat-icon
												*ngIf="
													attachmentFile &&
													attachmentFileType === FILE_TYPE.AUDIO
												"
											>
												audiotrack</mat-icon
											>&nbsp;
											<mat-icon
												*ngIf="
													attachmentFile &&
													attachmentFileType === FILE_TYPE.IMAGE
												"
											>
												image</mat-icon
											>&nbsp;

											{{ attachmentFile ? attachmentFile.name : '' }}
											<strong *ngIf="attachmentFile"
												>&nbsp;&nbsp;&nbsp;(&nbsp;{{
													attachmentFileSize
												}}&nbsp;)</strong
											>
											<span *ngIf="loadingSendAttachment">
												<i class="fas fa-spin fa-spinner mr-2"></i>
											</span>
										</div>
										<textarea
											[placeholder]="'meeting.page.sendTO' | translate"
											[cols]="28"
											pInputTextarea
											autoResize="false"
											class="pl-3 pt-3 pb-4 discussion-chat-textarea"
											name="inputMessage"
											[(ngModel)]="inputMessage"
											(keyup)="getInputMessageData($event)"
											[disabled]="!selectedDiscussion.User"
											(keydown)="handleKeyDown($event)"
											(blur)="handleBlur($event)"
										></textarea>
									</div>
								</div>
								<div class="col-sm-3 col-lg-2 chat-actions">
									<div
										class="d-flex w-100 p-0 pl-3 align-items-center"
										style="height: 100%"
									>
										<button
											[pTooltip]="'meeting.page.attachments' | translate"
											tooltipPosition="top"
											pButton
											pRipple
											type="button"
											icon="pi pi-paperclip"
											class="p-button-rounded p-button-danger btn-upload"
										>
											<input
												type="file"
												name=""
												(change)="handleFileUpload($event)"
												style="opacity: 0; width: 100%; position: absolute"
											/>
										</button>
										<button
											(click)="sendMessage()"
											[pTooltip]="'meeting.page.send' | translate"
											tooltipPosition="top"
											pButton
											pRipple
											type="button"
											icon="pi pi-send"
											class="p-button-rounded ml-3 btn-send"
										></button>
									</div>
								</div>
							</div>
						</div>
					</div>

					<!-- -END- Chat board-->
				</div>
			</div>
		</section>
	</div>
</div>
<p-dialog
	[header]="attachmentFileToReadName"
	[(visible)]="displayMaximizable"
	[modal]="true"
	[style]="{ width: '50vw', minWidth: '680px' }"
	[maximizable]="true"
	[baseZIndex]="10000"
	[draggable]="false"
	[resizable]="false"
>
	<div
		class="display-message-attachment"
		*ngIf="attachmentFileType === FILE_TYPE.VIDEO"
	>
		<div
			class="video-player"
			plyr
			[plyrPlaysInline]="true"
			[plyrSources]="videoSources"
			(plyrInit)="player = $event"
			(plyrPlay)="played($event)"
		></div>
	</div>
	<div
		class="display-message-attachment"
		*ngIf="attachmentFileType === FILE_TYPE.IMAGE"
	>
		<img [src]="videoSources[0].src" alt="message image attachement" />
	</div>

	<!-- <video controls>
		<source
			src="https://www.youtube.com/watch?v=_O8psLIwTLw&ab_channel=JonathanC.Gambela"
		/>
		Your browser does not support the audio element.
	</video> -->

	<!-- <ng-template pTemplate="footer"> Chargement .... </ng-template> -->
</p-dialog>
