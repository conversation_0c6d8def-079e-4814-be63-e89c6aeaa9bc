<div class="red-skin">
	<div id="main-wrapper">
		<app-home-header></app-home-header>

		<section class="page-title metting-title">
			<div class="container">
				<div class="row">
					<div class="col-lg-12 col-md-12">
						<div *ngIf="!loading" class="breadcrumbs-wrap">
							<h1
								class="breadcrumb-title"
								*ngIf="meetingForm.controls['Id'].value"
							>
								{{ 'meeting.create_edit.edit_title' | translate }}
							</h1>
							<h1
								class="breadcrumb-title"
								*ngIf="!meetingForm.controls['Id'].value"
							>
								{{ 'meeting.create_edit.create_title' | translate }}
							</h1>
						</div>
					</div>
				</div>
			</div>
		</section>

		<div class="container loading">
			<app-loading *ngIf="loading" [isLoading]="loading"></app-loading>
		</div>

		<div *ngIf="!loading" class="meeting-form-card">
			<div>
				<form
					class="main"
					fxLayout="column"
					fxLayoutAlign="start stretch"
					[formGroup]="meetingForm"
					(ngSubmit)="schedule()"
				>
					<div fxFlex fxLayout="row" fxLayoutAlign="start center">
						<div fxFlex="20" class="form-label mat-body-strong">
							{{ 'meeting.create_edit.form.topic' | translate }}*
						</div>
						<mat-form-field appearance="outline" fxFlex>
							<input
								matInput
								type="text"
								placeholder="Topic"
								formControlName="Topic"
								required
							/>
							<mat-error
								*ngIf="
									meetingForm.controls['Topic'].touched &&
									meetingForm.controls['Topic'].errors?.required
								"
							>
								{{ 'meeting.create_edit.form.error.this_field' | translate }}
								<strong>{{
									'meeting.create_edit.form.error.required' | translate
								}}</strong>
							</mat-error>
						</mat-form-field>
					</div>

					<div fxFlex fxLayout="row" fxLayoutAlign="start center">
						<div fxFlex="20" class="form-label mat-body-strong">
							{{ 'meeting.create_edit.form.descrption' | translate }}
						</div>
						<mat-form-field appearance="outline" fxFlex>
							<textarea
								matInput
								placeholder="Description"
								formControlName="Description"
							></textarea>
						</mat-form-field>
					</div>

					<div fxFlex fxLayout="row" fxLayoutAlign="start center">
						<div fxFlex="20" class="form-label mat-body-strong">
							{{ 'meeting.create_edit.form.when' | translate }}*
						</div>

						<mat-form-field
							fxFlex="50"
							class="example-full-width"
							appearance="outline"
						>
							<input
								matInput
								[matDatepicker]="picker"
								formControlName="StartDate"
							/>
							<mat-datepicker-toggle
								matSuffix
								[for]="picker"
							></mat-datepicker-toggle>
							<mat-datepicker #picker></mat-datepicker>
							<mat-error
								*ngIf="
									meetingForm.controls['StartDate'].touched &&
									meetingForm.controls['StartDate'].errors?.required
								"
							>
								{{ 'meeting.create_edit.form.error.this_field' | translate }}
								<strong>{{
									'meeting.create_edit.form.error.required' | translate
								}}</strong>
							</mat-error>
						</mat-form-field>

						<mat-form-field fxFlex="20" fxFlexOffset="1" appearance="outline">
							<!-- <mat-select formControlName="StartTime">
								<mat-option *ngFor="let time of times" [value]="time.value">
									{{ time.viewValue }}
								</mat-option>
							</mat-select> -->

							<input matInput type="time" formControlName="StartTime" />

							<!-- <mat-error
								*ngIf="
									meetingForm.controls['StartTime'].touched &&
									meetingForm.controls['StartTime'].errors?.required
								"
							> -->
							<!-- {{ 'meeting.create_edit.form.error.this_field' | translate }}
								<strong>{{
									'meeting.create_edit.form.error.required' | translate
								}}</strong>
							</mat-error> -->
						</mat-form-field>

						<mat-form-field fxFlex="10" fxFlexOffset="1" appearance="outline">
							<mat-select formControlName="TimePeriod">
								<mat-option [value]="'AM'">AM</mat-option>
								<mat-option [value]="'PM'">PM</mat-option>
							</mat-select>
							<mat-error
								*ngIf="
									meetingForm.controls['TimePeriod'].touched &&
									meetingForm.controls['TimePeriod'].errors?.required
								"
							>
								{{ 'meeting.create_edit.form.error.this_field' | translate }}
								<strong>{{
									'meeting.create_edit.form.error.required' | translate
								}}</strong>
							</mat-error>
						</mat-form-field>
					</div>

					<div fxFlex fxLayout="row" fxLayoutAlign="start center">
						<div fxFlex="20" class="form-label mat-body-strong">
							{{ 'meeting.create_edit.form.duration' | translate }}*
						</div>

						<mat-form-field fxFlex="20" appearance="outline">
							<mat-label>hr</mat-label>
							<mat-select formControlName="DurationHour">
								<mat-option *ngFor="let hour of hours" [value]="hour">
									{{ hour }}
								</mat-option>
							</mat-select>
							<mat-error
								*ngIf="
									meetingForm.controls['DurationHour'].touched &&
									meetingForm.controls['DurationHour'].errors?.required
								"
							>
								{{ 'meeting.create_edit.form.error.this_field' | translate }}
								<strong>{{
									'meeting.create_edit.form.error.required' | translate
								}}</strong>
							</mat-error>
						</mat-form-field>

						<mat-form-field fxFlex="20" fxFlexOffset="1" appearance="outline">
							<mat-label>min</mat-label>
							<mat-select formControlName="DurationMin">
								<mat-option *ngFor="let minute of minutes" [value]="minute">
									{{ minute }}
								</mat-option>
							</mat-select>
							<mat-error
								*ngIf="
									meetingForm.controls['DurationMin'].touched &&
									meetingForm.controls['DurationMin'].errors?.required
								"
							>
								{{ 'meeting.create_edit.form.error.this_field' | translate }}
								<strong>{{
									'meeting.create_edit.form.error.required' | translate
								}}</strong>
							</mat-error>
						</mat-form-field>
					</div>

					<div fxFlex fxLayout="row" fxLayoutAlign="start center">
						<div fxFlex="20" class="form-label mat-body-strong">
							{{ 'meeting.create_edit.form.timezone' | translate }}*
						</div>
						<mat-form-field fxFlex appearance="outline">
							<mat-select formControlName="Timezone">
								<mat-option
									*ngFor="let timezone of timezones"
									[value]="timezone.tzCode"
								>
									{{ timezone.label }}
								</mat-option>
							</mat-select>
							<mat-error
								*ngIf="
									meetingForm.controls['Timezone'].touched &&
									meetingForm.controls['Timezone'].errors?.required
								"
							>
								{{ 'meeting.create_edit.form.error.this_field' | translate }}
								<strong>
									{{ 'meeting.create_edit.form.error.required' | translate }}
								</strong>
							</mat-error>
						</mat-form-field>
					</div>

					<div
						fxFlex
						fxLayout="row"
						fxLayoutAlign="start center"
						fxFlexOffset="2"
						class="example-section"
					>
						<mat-checkbox
							fxFlex
							fxFlexOffset="20"
							class="example-margin"
							formControlName="Recurring"
						>
							<span class="form-label mat-body-strong">
								{{ 'meeting.create_edit.form.recuring_meeting' | translate }}
							</span>
						</mat-checkbox>

						<!-- <mat-checkbox
							fxFlex
							fxFlexOffset="5"
							class="example-margin"
							formControlName="Waiting"
						>
							<span class="form-label mat-body-strong">
								{{ 'meeting.create_edit.form.waiting_room' | translate }}
							</span>
						</mat-checkbox> -->
					</div>

					<div
						fxFlex
						fxLayout="row"
						fxLayoutAlign="start center"
						fxFlexOffset="2"
					>
						<!-- -------SELECT PEDIOD OF RECURSIVE MEETING ------------->
						<div
							style="background-color: #eee; padding: 2rem"
							fxFlex
							fxLayout="row"
							fxLayoutAlign="start center"
						>
							<div fxFlex="20" class="form-label mat-body-strong">
								Recuring Periode*
							</div>
							<mat-form-field fxFlex appearance="outline">
								<mat-select formControlName="RecurringPeriod">
									<mat-option
										*ngFor="let p of recurringPeriod"
										[value]="p.value"
									>
										{{ p.label }}
									</mat-option>
								</mat-select>
								<!-- <mat-error
									*ngIf="
										meetingForm.controls['Timezone'].touched &&
										meetingForm.controls['Timezone'].errors?.required
									"
								>
									{{ 'meeting.create_edit.form.error.this_field' | translate }}
									<strong>
										{{ 'meeting.create_edit.form.error.required' | translate }}
									</strong>
								</mat-error> -->
							</mat-form-field>
						</div>

						<!-- ---X---SELECT PEDIOD OF RECURSIVE MEETING --------X---->
					</div>

					<div
						fxFlex
						fxLayout="row"
						fxLayoutAlign="start center"
						fxFlexOffset="2"
					>
						<div fxFlex="20" class="form-label mat-body-strong">
							{{ 'meeting.create_edit.form.meeting_id' | translate }}*
						</div>
						<mat-radio-group
							fxFlex
							fxLayout="row"
							fxLayoutGap="30px"
							formControlName="MeetingIdType"
						>
							<mat-radio-button class="example-margin" value="generated">
								{{
									'meeting.create_edit.form.generated' | translate
								}}</mat-radio-button
							>
							<mat-radio-button class="example-margin" value="personal"
								>{{ 'meeting.create_edit.form.personal' | translate }} (
								{{ mettingId }} )</mat-radio-button
							>
						</mat-radio-group>
					</div>

					<div
						fxFlex
						fxLayout="row"
						fxLayoutAlign="start center"
						fxFlexOffset="2"
					>
						<div fxFlex="20" class="form-label mat-body-strong">
							{{ 'meeting.create_edit.form.personal' | translate }}
						</div>
						<mat-checkbox
							fxFlex="20"
							class="example-margin"
							formControlName="RequirePasscode"
						>
							<span class="form-label mat-body-strong">
								{{ 'meeting.create_edit.form.require_passcode' | translate }}
							</span>
						</mat-checkbox>
						<mat-form-field fxFlex fxFlexOffset="5" appearance="outline" fxFlex>
							<input
								matInput
								type="text"
								placeholder="Passcode"
								formControlName="Passcode"
								[required]="meetingForm.controls['RequirePasscode'].value"
								[disabled]="!meetingForm.controls['RequirePasscode'].value"
							/>
							<mat-error
								*ngIf="meetingForm.controls['Passcode'].errors?.required"
							>
								{{ 'meeting.create_edit.form.error.this_field' | translate }}
								<strong>{{
									'meeting.create_edit.form.error.required' | translate
								}}</strong>
							</mat-error>
						</mat-form-field>
					</div>

					<div
						fxFlex
						fxLayout="row"
						fxLayoutAlign="end center"
						fxFlexOffset="2"
						fxLayoutGap="5px"
					>
						<button
							mat-raised-button
							type="submit"
							[disabled]="!meetingForm.valid"
							color="accent"
							*ngIf="!meetingForm.controls['Id'].value"
						>
							<app-loading
								*ngIf="loadingSubmitForm"
								[isLoading]="loadingSubmitForm"
							></app-loading>

							<span *ngIf="!loadingSubmitForm">{{
								'meeting.create_edit.form.schedule' | translate
							}}</span>
						</button>
						<button
							mat-raised-button
							type="submit"
							[disabled]="!meetingForm.valid"
							color="accent"
							*ngIf="meetingForm.controls['Id'].value"
						>
							<app-loading
								*ngIf="loadingSubmitForm"
								[isLoading]="loadingSubmitForm"
							></app-loading>
							<span *ngIf="!loadingSubmitForm">{{
								'meeting.create_edit.form.update' | translate
							}}</span>
						</button>
						<button
							mat-raised-button
							type="button"
							(click)="cancelMeeting()"
							color="warn"
						>
							{{ 'meeting.create_edit.form.cancel' | translate }}
						</button>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>
