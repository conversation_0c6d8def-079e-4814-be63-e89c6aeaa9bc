import { Component, OnInit } from '@angular/core';
import {
	FormBuilder,
	FormControl,
	FormGroup,
	Validators
} from '@angular/forms';
import compactTimezones from 'compact-timezone-list';
import { ActivatedRoute, Router } from '@angular/router';
import { UtilsService } from '~/app/services/utils.service';
import { MeetingsService } from '~/app/services/meetings/meetings.service';
import { BrainMakerToastrService } from '~/app/services/toast/brain-maker-toastr.service';
import {
	MeetingIdType,
	Meetings,
	RecurringPeriod
} from '~/app/models/meetings';
import { UserService } from '~/app/services/users/user.service';
import { ToastrService } from 'ngx-toastr';
import { User, UserRoleType } from '~/app/models/user.model';
import { HomeLoginComponent } from '~/app/home/<USER>/home-login.component';
import { AbonnementsService } from '~/app/services/abonnements/abonnements.service';

@Component({
	selector: 'app-meetings-create',
	templateUrl: './meetings-create.component.html',
	styleUrls: ['./meetings-create.component.scss']
})
export class MeetingsCreateComponent implements OnInit {
	times = [
		{ value: '01:00', viewValue: '01:00' },
		{ value: '01:30', viewValue: '01:30' },
		//
		{ value: '02:00', viewValue: '02:00' },
		{ value: '02:06', viewValue: '02:06' },
		{ value: '02:07', viewValue: '02:07' },
		{ value: '02:08', viewValue: '02:08' },
		{ value: '02:09', viewValue: '02:09' },
		{ value: '02:51', viewValue: '02:51' },
		{ value: '02:53', viewValue: '02:53' },
		{ value: '02:57', viewValue: '02:57' },
		{ value: '02:59', viewValue: '02:59' },

		//{ value: '02:30', viewValue: '02:30' },
		{ value: '03:00', viewValue: '03:00' },
		{ value: '03:01', viewValue: '03:01' },
		{ value: '03:02', viewValue: '03:02' },
		{ value: '03:03', viewValue: '03:03' },
		{ value: '03:04', viewValue: '03:04' },
		{ value: '03:05', viewValue: '03:05' },
		{ value: '03:06', viewValue: '03:06' },
		{ value: '03:07', viewValue: '03:07' },
		{ value: '03:08', viewValue: '03:08' },
		{ value: '03:08', viewValue: '03:08' },
		{ value: '03:08', viewValue: '03:08' },
		{ value: '03:08', viewValue: '03:08' },
		{ value: '03:015', viewValue: '03:15' },
		{ value: '03:017', viewValue: '03:17' },
		{ value: '03:020', viewValue: '03:20' },
		{ value: '03:023', viewValue: '03:23' },
		{ value: '03:026', viewValue: '03:26' },
		{ value: '03:029', viewValue: '03:29' },
		{ value: '03:032', viewValue: '03:32' },
		{ value: '03:035', viewValue: '03:35' },
		{ value: '03:40', viewValue: '03:40' },
		{ value: '03:45', viewValue: '03:45' },
		//
		{ value: '03:30', viewValue: '03:30' },
		{ value: '04:00', viewValue: '04:00' },
		{ value: '04:30', viewValue: '04:30' },

		{ value: '05:00', viewValue: '05:00' },
		{ value: '05:20', viewValue: '05:20' },
		{ value: '05:25', viewValue: '05:25' },
		{ value: '05:35', viewValue: '05:35' },
		{ value: '05:40', viewValue: '05:40' },
		{ value: '05:45', viewValue: '05:45' },
		{ value: '05:50', viewValue: '05:50' },

		{ value: '05:30', viewValue: '05:30' },
		{ value: '06:00', viewValue: '06:00' },
		{ value: '06:30', viewValue: '06:30' },
		{ value: '07:00', viewValue: '07:00' },
		{ value: '08:30', viewValue: '08:30' },
		{ value: '09:00', viewValue: '09:00' },
		{ value: '09:30', viewValue: '09:30' },
		{ value: '10:00', viewValue: '10:00' },
		{ value: '10:30', viewValue: '10:30' },
		{ value: '11:00', viewValue: '11:00' },
		{ value: '11:30', viewValue: '11:30' },
		{ value: '12:00', viewValue: '12:00' },
		{ value: '12:30', viewValue: '12:30' }
	];

	recurringPeriod = [
		{ value: RecurringPeriod.DAY, label: 'Jour' },
		{ value: RecurringPeriod.WEEK, label: 'semaine' },
		{ value: RecurringPeriod.MONTH, label: 'Mois' },
		{ value: RecurringPeriod.TEST, label: 'test' }
	];

	hours = Array(24)
		.fill(undefined)
		.map((x, i) => i);

	minutes = Array(60)
		.fill(undefined)
		.map((x, i) => i);

	timezones = compactTimezones;

	mettingId = this.utilService.getUniqueId(8);
	passcode = this.utilService.getUniqueId(8);

	meetingForm: FormGroup = this.formBuilder.group({
		Id: [],
		Topic: ['', Validators.required],
		Description: [''],
		StartDate: ['', Validators.required],
		StartTime: ['', Validators.required],
		TimePeriod: ['', Validators.required],
		DurationHour: ['', Validators.required],
		DurationMin: ['', Validators.required],
		Timezone: ['', Validators.required],
		Recurring: [false],
		RecurringPeriod: [null],
		MeetingIdType: ['personal', Validators.required],
		RequirePasscode: [false],
		Passcode: [this.passcode, Validators.required],
		HostVideo: [false],
		GuestVideo: [false],
		Waiting: [false],
		MettingId: [''],
		Slug: [],
		Settings: new FormGroup({
			Join: new FormControl(false),
			Mute: new FormControl(false),
			AutoRecord: new FormControl(false),
			ApproveBlock: new FormControl(false)
		})
	});

	loading = false;
	loadingSubmitForm = false;
	user: User;

	constructor(
		private formBuilder: FormBuilder,
		private meetingService: MeetingsService,
		private brainToastr: BrainMakerToastrService,
		private utilService: UtilsService,
		private route: ActivatedRoute,
		private router: Router,
		private userService: UserService,
		private toastr: ToastrService,
		private abonnementService: AbonnementsService,
		private utilsService: UtilsService
	) {
		const slug = this.route.snapshot.paramMap.get('slug');
		this.loading = true;

		if (slug) {
			this.meetingService.getBySlug(slug).subscribe((res) => {
				this.meetingForm.reset(res);
				this.mettingId = res.MettingId;
				this.loading = false;
			});
		} else {
			this.loading = false;
		}
	}

	async ngOnInit(): Promise<void> {
		if (this.userService.isConnected()) {
			this.user = await this.userService.getUserConnected().toPromise();
		} else {
			this.router.navigateByUrl('/meetings');
			this.toastr.error(
				'Bien vouloir vous inscrire ou vous connecter pour pouvoir y accéder',
				'Authentification requise'
			);
			return;
		}

		if (this.user) {
			const abonnement = await this.abonnementService
				.getUserActiveAbonnement(this.user.Slug)
				.toPromise();
			if (abonnement?.Forfait?.Name.toLowerCase() !== 'premium') {
				if (this.user.Role != UserRoleType.ADMIN) {
					this.router.navigateByUrl('/home/<USER>');
					this.toastr.warning(
						'Brain maker',
						'Veuillez chosoir un forfait adequat pour avoir accès au meeting'
					);
				}
			}
		} else {
			this.utilsService.buildModal(HomeLoginComponent, () => {}, 500, true, {});
		}
	}

	schedule(): void {
		this.loadingSubmitForm = true;
		const meeting = this.meetingForm.getRawValue() as Meetings;

		if (meeting.MeetingIdType === MeetingIdType.GENERATED) {
			meeting.MettingId = this.utilService.getUniqueId(8);
		} else {
			meeting.MettingId = this.mettingId;
		}

		const startTimeSplit = this.meetingForm.value['StartTime'].split(':');

		let realStartTime = new Date(this.meetingForm.value['StartDate']);
		realStartTime = new Date(
			realStartTime.setHours(
				realStartTime.getHours() + Number(startTimeSplit[0])
			)
		);
		realStartTime = new Date(
			realStartTime.setMinutes(
				realStartTime.getMinutes() + Number(startTimeSplit[1])
			)
		);

		let mettingEndTime = new Date(
			realStartTime.setHours(
				realStartTime.getHours() + this.meetingForm.value['DurationHour']
			)
		);
		mettingEndTime = new Date(
			realStartTime.setMinutes(
				realStartTime.getMinutes() + this.meetingForm.value['DurationMin']
			)
		);
		meeting.EndTime = mettingEndTime;

		//console.log(meeting);

		if (meeting.Id) {
			this.meetingService.update(meeting.Slug, meeting).subscribe((res) => {
				this.brainToastr.success('Meeting updated successfully');
				this.router.navigateByUrl('/meetings/list');
				this.loadingSubmitForm = false;
			});
		} else {
			delete meeting.Slug;

			this.meetingService.schedule(meeting).subscribe((res) => {
				this.brainToastr.success('Meeting scheduled successfully');
				this.router.navigateByUrl('/meetings/list');
				this.loadingSubmitForm = false;
				this.reset();
			});
		}
	}

	reset(): void {
		this.passcode = this.utilService.getUniqueId(8);
		this.mettingId = this.utilService.getUniqueId(8);

		this.meetingForm.reset({
			HostVideo: false,
			GuestVideo: false,
			MeetingIdType: 'personal',
			Passcode: this.passcode
		});
	}

	cancelMeeting(): void {
		if (this.mettingId) {
			this.router.navigateByUrl('/meetings/list');
		} else {
			this.reset();
		}
	}
}
