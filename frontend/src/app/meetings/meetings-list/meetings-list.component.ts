import { Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { MeetingsService } from '~/app/services/meetings/meetings.service';
import { BrainMakerToastrService } from '~/app/services/toast/brain-maker-toastr.service';
import { ToastrService } from 'ngx-toastr';
import { TranslateService } from '@ngx-translate/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ConfirmationService, MessageService } from 'primeng/api';
import { Meetings, MeetingStatus } from '~/app/models/meetings';
import { UtilsService } from '~/app/services/utils.service';
import { UserService } from '~/app/services/users/user.service';
import { User, UserRoleType } from '~/app/models/user.model';
import { environment } from '~/environments/environment';
import { MeetingParticipant } from '~/app/models/meeting-participant.model';
import { HomeLoginComponent } from '~/app/home/<USER>/home-login.component';
import { AbonnementsService } from '~/app/services/abonnements/abonnements.service';

@Component({
	selector: 'app-meetings-list',
	templateUrl: './meetings-list.component.html',
	styleUrls: ['./meetings-list.component.scss'],
	providers: [ConfirmationService, MessageService],
	encapsulation: ViewEncapsulation.None
})
export class MeetingsListComponent implements OnInit {
	mettings: Array<Meetings> = [];
	selectedMeeting: Meetings;

	displayedColumns: string[] = [
		'meetingid',
		'passcode',
		'date',
		'hour',
		'duration',
		'timezone',
		'actions'
	];
	dataSource = new MatTableDataSource<Meetings>();
	sideBarToogle = false;
	@ViewChild(MatPaginator) paginator: MatPaginator;

	loading = false;
	user: User;
	displayInvitationModal: boolean = false;

	mettingsParticipantsEmails: string[] = [];
	loadingSaveEmail: boolean = false;
	meetingStatus = MeetingStatus;

	constructor(
		private meetingService: MeetingsService,
		private brainToastr: BrainMakerToastrService,
		private toastr: ToastrService,
		private translateService: TranslateService,
		private router: Router,
		private confirmationService: ConfirmationService,
		private messageService: MessageService,
		private utilService: UtilsService,
		private userService: UserService,
		private activatedRouter: ActivatedRoute,
		private abonnementService: AbonnementsService
	) {}

	async ngOnInit(): Promise<void> {
		if (this.userService.isConnected()) {
			this.user = await this.userService.getUserConnected().toPromise();
		} else {
			this.router.navigateByUrl('/meetings');
			this.toastr.error(
				'Bien vouloir vous inscrire ou vous connecter pour pouvoir y accéder',
				'Authentification requise'
			);
			return;
		}

		if (this.user) {
			const abonnement = await this.abonnementService
				.getUserActiveAbonnement(this.user.Slug)
				.toPromise();
			console.log(
				'abonneme, => ',
				abonnement,
				this.user.Role != UserRoleType.ADMIN
			);
			if (abonnement?.Forfait?.Name.toLowerCase() !== 'premium') {
				if (this.user.Role != UserRoleType.ADMIN) {
					this.router.navigateByUrl('/home/<USER>');
					this.toastr.warning(
						'Brain maker',
						'Veuillez chosoir un forfait adequat pour avoir accès au meeting'
					);
				}
			}
		} else {
			this.utilService.buildModal(HomeLoginComponent, () => {}, 500, true, {});
		}

		this.loading = true;
		try {
			this.mettings = await this.meetingService
				.getForCurrentUser(this.user.Slug)
				.toPromise();

			const selectedMeetingSlug = this.activatedRouter.snapshot.params.slug;
			if (selectedMeetingSlug) {
				this.selectedMeeting = this.mettings.find(
					(m) => (m.Slug = selectedMeetingSlug)
				);
			} else {
				this.selectedMeeting =
					this.mettings.length > 0 ? this.mettings[0] : null;
			}

			if (this.selectedMeeting) {
				const p: string[] = [];
				this.selectedMeeting.Participants?.map((pItem: MeetingParticipant) => {
					console.log('****', pItem.Email);
					p.push(pItem.Email);
				});
				this.mettingsParticipantsEmails = p;
			}
			this.loading = false;
		} catch (error) {
			this.brainToastr.error('Une erreur est survenue lors du traitement');
			this.loading = false;
		}

		console.log(this.selectedMeeting);
	}
	async copyMeetingLink(): Promise<void> {
		const message = await this.translateService
			.get('meeting.messages')
			.toPromise();

		if (
			this.copyTextToClipboard(
				`${location.protocol}//${location.hostname}:${
					location.port
				}/${this.getMeetingLink(this.selectedMeeting)}`
			)
		) {
			this.toastr.info('Le lien a été copié', 'Message');
		} else {
			this.toastr.error("Une erreur es'est produit", 'Message');
		}
	}

	async copyMeetingPassCode(): Promise<void> {
		const message = await this.translateService
			.get('meeting.messages')
			.toPromise();

		if (this.copyTextToClipboard(this.selectedMeeting.Passcode)) {
			this.toastr.info('Le passcode a été copé ', 'Message');
		} else {
			this.toastr.error("Une erreur es'est produit", 'Message');
		}
	}

	editMetting(): void {
		if (this.selectedMeeting.Status === MeetingStatus.FINISHED) {
			this.toastr.success(
				'Ce meeting est terminé, vous ne pouvez plus le modifier',
				'Meeting terminé'
			);
			return;
		}
		this.router.navigate(['/meetings/edit/' + this.selectedMeeting.Slug]);
	}

	async deleteMeeting(meeting, event: Event): Promise<void> {
		const message = await this.translateService
			.get('meeting.messages')
			.toPromise();

		this.confirmationService.confirm({
			target: event.target,
			header: 'Confirmation de la suppression',
			message: 'Voulez vous vraiment supprimer ce meetings ?',
			icon: 'pi pi-exclamation-triangle',
			accept: async () => {
				try {
					this.loading = true;
					const res = await this.meetingService
						.delete(this.selectedMeeting.Slug)
						.toPromise();

					const index = this.mettings.indexOf(this.selectedMeeting);
					this.mettings.splice(index, 1);
					if (index > 0) {
						this.selectedMeeting = this.mettings[index - 1];
					} else {
						if (this.mettings.length > 0) {
							this.selectedMeeting = this.mettings[0];
						} else {
							this.selectedMeeting = undefined;
						}
					}

					this.loading = false;
					this.messageService.add({
						severity: 'info',
						summary: 'Action',
						detail: message.deletOk
					});
				} catch (error) {
					console.log(error);
				}
			},
			reject: () => {
				// this.messageService.add({
				// 	severity: 'error',
				// 	summary: 'Action',
				// 	detail: message.deleteNoteDone
				// });
			}
		});
	}

	copyTextToClipboard(text): boolean {
		const txtArea = document.createElement('textarea');
		txtArea.id = 'txt';
		txtArea.style.position = 'fixed';
		txtArea.style.top = '0';
		txtArea.style.left = '0';
		txtArea.style.opacity = '0';
		txtArea.value = text;
		document.body.appendChild(txtArea);
		txtArea.select();

		try {
			const successful = document.execCommand('copy');
			const msg = successful ? 'successful' : 'unsuccessful';
			console.log('Copying text command was ' + msg);
			if (successful) {
				return true;
			}
		} catch (err) {
			console.log('Oops, unable to copy');
		} finally {
			document.body.removeChild(txtArea);
		}
		return false;
	}

	selectScheduleMeeting(meeting: Meetings): void {
		this.sideBarToogle = false;
		if (this.selectedMeeting && this.selectedMeeting.Id != meeting.Id) {
			this.loading = true;
			this.selectedMeeting = meeting;

			const p: string[] = [];
			this.selectedMeeting.Participants?.map((pItem: MeetingParticipant) => {
				console.log('****', pItem.Email);
				p.push(pItem.Email);
			});
			this.mettingsParticipantsEmails = p;

			setTimeout(() => {
				this.loading = false;
			}, 300);
		}
	}

	formatDate(date: Date) {
		return this.utilService.FormatDateToDayMonthYear(new Date(date));
	}

	newMeeting() {
		this.router.navigateByUrl('/meetings/create');
	}

	beginMeeting(): void {
		this.router.navigateByUrl(this.getMeetingLink(this.selectedMeeting));
	}

	getMeetingLink(meeting: Meetings): string {
		return `meetings/join/${meeting.Slug}`;
	}

	showInvitationDialog() {
		if (this.selectedMeeting.Status === MeetingStatus.FINISHED) {
			this.toastr.success(
				'Ce meeting est terminé, vous ne pouvez plus inviter de participants',
				'Meeting terminé'
			);
			return;
		}
		this.displayInvitationModal = true;
	}

	async saveMeetingsParticipantsEmail(): Promise<void> {
		// console.log(this.mettingsParticipantsEmails);

		this.loadingSaveEmail = true;
		if (this.mettingsParticipantsEmails.length < 1) {
			this.loadingSaveEmail = false;
			this.toastr.error(
				'Appuyer sur la touche entrer pour inserer un email',
				'Saisie incorrecte'
			);
			return;
		}

		const participants: MeetingParticipant[] = [];

		for (let email of this.mettingsParticipantsEmails) {
			const exist = this.selectedMeeting.Participants?.find(
				(p: MeetingParticipant) => p.Email === email
			);

			const alreadyInList = participants?.find((p) => {
				if (p.Email === email) {
					const idx = this.mettingsParticipantsEmails.indexOf(
						this.mettingsParticipantsEmails.find((e) => e === p.Email)
					);
					console.log('*** duplicate: ', this.mettingsParticipantsEmails[idx]);
					this.mettingsParticipantsEmails = this.mettingsParticipantsEmails.splice(
						idx,
						1
					);
					return true;
				}
			});

			if (exist) {
				if (!alreadyInList) {
					participants.push(exist);
				}
			} else {
				const m = this.selectedMeeting;
				// console.log('m - avant: ', m);
				// delete m.Participants;
				// delete m.Owner;
				// delete m.Settings;

				console.log('m - après: ', m);

				const newP = new MeetingParticipant();
				newP.Email = email;
				newP.Meeting = {
					Id: this.selectedMeeting.Id,
					Slug: this.selectedMeeting.Slug
				};
				newP.EmailSend = false;
				console.log('New p', newP);
				participants.push(newP);
				console.log('select---<', m);
			}
		}

		console.log('Participants |--->', participants);

		const participantsToDelete: MeetingParticipant[] = [];
		const oldParticipants = this.selectedMeeting.Participants;
		oldParticipants?.map((oldP) => {
			const exist = participants.find((p) => oldP.Email === p.Email);
			if (!exist) {
				participantsToDelete.push(oldP);
			}
		});
		console.log('old', oldParticipants);
		console.log('to delete', participantsToDelete);

		await Promise.all(
			participantsToDelete?.map(
				async (p): Promise<void> => {
					const res = await this.meetingService
						.deleteParticipant(p.Slug)
						.toPromise();
					console.log('delete res', res);
				}
			)
		);

		participants?.map((p: MeetingParticipant) => {
			this.selectedMeeting.Participants?.map(
				(selectedP: MeetingParticipant) => {
					if (selectedP.Email === p.Email) {
						p.EmailSend = selectedP.EmailSend;
					}
				}
			);
		});

		console.log('exécuté...');
		this.selectedMeeting.Participants = participants;
		try {
			const updatedMeeting = await this.meetingService
				.update(this.selectedMeeting.Slug, this.selectedMeeting)
				.toPromise();

			console.log(updatedMeeting);

			// Update selected meeting in the list
			const index = this.mettings.indexOf(
				this.mettings.find((m) => (m.Id = this.selectedMeeting.Id))
			);
			this.mettings[index] = updatedMeeting;
			this.loadingSaveEmail = false;
			this.displayInvitationModal = false;

			this.toastr.success(
				'Les participants ont été ajouté avec succès',
				'Participants ajouté'
			);
		} catch (error) {
			console.log(error);
		} finally {
			this.loadingSaveEmail = false;
		}
	}

	getStatusClass(): { c: string; label: string } | string {
		switch (this.selectedMeeting.Status) {
			case MeetingStatus.FINISHED:
				return {
					c: 'customer-badge status-unqualified mr-1 mt-2',
					label: 'Terminé'
				};

			case MeetingStatus.RUNNING:
				return {
					c: 'customer-badge status-proposal mr-1 mt-2',
					label: 'En cours'
				};

			case MeetingStatus.SCHEDULED:
				return {
					c: 'customer-badge status-qualified mr-1 mt-2',
					label: 'programmé'
				};
			case MeetingStatus.STOPED:
				return {
					c: 'customer-badge status-unqualified mr-1 mt-2',
					label: 'Bloqué'
				};
			default:
				return '';
		}
	}
}
