<div class="red-skin">
	<div id="main-wrapper">
		<app-home-header></app-home-header>

		<app-mettings-nav-tab [page]="'meetings'"></app-mettings-nav-tab>

		<section class="section-contain-meetings">
			<div class="row" style="height: 85vh; overflow: auto; padding: 0 2rem">
				<div class="col-sm-12 col-lg-3 pr-0 hide-on-med-and-down">
					<div
						class="card border-0 h-100"
						style="background-color: #eceff1; overflow-y: auto"
					>
						<div class="d-flex align-items-center justify-content-between p-3">
							<i class="pi pi-refresh pointer"></i>
							<button (click)="newMeeting()" class="btn btn-sm btn-primary">
								Nouvelle reunion
							</button>
							<!-- <button pButton pRipple type="button" icon="pi pi-ellipsis-v" class="p-button-rounded p-button-danger p-button-text"></button> -->
							<span></span>
						</div>
						<h2 class="font-bold text-center text-black">501 164 3622</h2>
						<p class="text-center border-bottom pb-3">
							Mon ID de reunion personnelle
						</p>
						<div *ngIf="mettings.length > 0" class="w-100 pl-3 pr-3">
							<span
								class="p-input-icon-left mr-4"
								style="width: 100%; height: 40px"
								><i class="pi pi-search"></i
								><input
									pinputtext=""
									type="text"
									[placeholder]="'admin.misc.search' | translate"
									class="search_input"
							/></span>
						</div>

						<!-- <div class="ml-20">Aucun meeting programmé</div> -->

						<p-scrollPanel
							*ngIf="mettings.length > 0"
							[style]="{ width: '100%', height: '50vh' }"
						>
							<div class="d-flex flex-column p-3 meetings-list">
								<div
									(click)="selectScheduleMeeting(meeting)"
									*ngFor="let meeting of mettings"
									class="d-flex flex-column mb-3 p-2 pointer meeting-item"
									[class]="
										selectedMeeting.Id === meeting.Id
											? 'd-flex flex-column mb-3 p-2 pointer meeting-item active'
											: 'd-flex flex-column mb-3 p-2 pointer meeting-item'
									"
								>
									<h3 class="mb-0">
										{{ meeting.Topic }}
									</h3>
									<p class="mb-0">
										{{
											formatDate(meeting.StartDate) +
												' à ' +
												meeting.StartTime +
												' ' +
												meeting.TimePeriod
										}}
									</p>

									<small>ID de réunion : {{ meeting.MettingId }}</small>
								</div>
							</div>
						</p-scrollPanel>
					</div>
				</div>

				<!--
					MEETING DETAILS
				-->
				<div *ngIf="selectedMeeting" class="col-sm-12 col-lg-8">
					<p-confirmDialog></p-confirmDialog>

					<div *ngIf="loading" class="container-loader">
						<app-loading [isLoading]="loading"></app-loading>
					</div>
					<div *ngIf="!loading" class="d-flex flex-column ml-2 pb-3">
						<h1>
							{{ selectedMeeting.Topic }}
						</h1>

						<p class="mb-0">
							{{
								formatDate(selectedMeeting.StartDate) +
									' à ' +
									selectedMeeting.StartTime +
									' ' +
									selectedMeeting.TimePeriod
							}}
						</p>

						<!-- <span
							>Période: {{ selectedMeeting.StartTime | dateAgo }} -
							{{ selectedMeeting.EndTime | dateAgo }}</span
						> -->
						<small>ID de réunion : {{ selectedMeeting.MettingId }}</small>

						<p>{{ selectedMeeting.Description }}</p>

						<div>
							Durée:
							<!-- {{
								selectedMeeting.DurationMin && selectedMeeting.DurationHour
									? selectedMeeting.DurationHour + 'H'
									: 'Non definie'
							}}
							{{
								selectedMeeting.DurationMin && selectedMeeting.DurationHour
									? selectedMeeting.DurationMin + 'Min'
									: ''
							}} -->

							{{
								selectedMeeting.DurationHour +
									'H:' +
									selectedMeeting.DurationMin +
									'min'
							}}
						</div>

						<div class="mt-3">
							<span [class]="getStatusClass().c">{{
								getStatusClass().label
							}}</span>
						</div>
					</div>

					<!---------------CONTROL BUTTONS RESPONSIVE -------------------->
					<div
						*ngIf="!loading && selectedMeeting?.Status !== meetingStatus.STOPED"
						class="d-flex mt-4 hide-on-med-and-down ml-4"
					>
						<div class="row">
							<div class="col-12">
								<div class="row">
									<button
										class="col-6 btn btn-sm btn-primary mr-4"
										(click)="beginMeeting()"
									>
										Commencer
									</button>
									<div>
										<button
											class="col-6"
											(click)="showInvitationDialog()"
											class="btn btn-sm btn-outline-primary mr-4 d-flex align-items-center"
										>
											<i class="pi pi-copy mr-2"></i>
											<span>Inviter avec une adresse email</span>
										</button>
									</div>
								</div>
							</div>

							<div class="col-12">
								<div class="row">
									<button
										class="col-6"
										(click)="copyMeetingLink()"
										class="btn btn-sm btn-outline-primary mr-4 d-flex align-items-center"
									>
										<i class="pi pi-copy mr-2"></i>
										<span>Copier le lien d'invitation</span>
									</button>
									<button
										*ngIf="selectedMeeting.RequirePasscode"
										class="col-6"
										(click)="copyMeetingPassCode()"
										class="btn btn-sm btn-outline-primary mr-4 d-flex align-items-center"
									>
										<i class="pi pi-copy mr-2"></i>
										<span>Copier le passcode</span>
									</button>
								</div>
							</div>

							<div class="col-12">
								<div class="row">
									<button
										class="col-6"
										(click)="editMetting()"
										class="btn btn-sm btn-outline-primary mr-4 d-flex align-items-center"
									>
										<i class="pi pi-pencil mr-2"></i>
										<span>Modifier</span>
									</button>
									<button
										class="col-6"
										(click)="deleteMeeting({}, $event)"
										class="btn btn-sm btn-outline-primary mr-4 d-flex align-items-center"
									>
										<i class="pi pi-times mr-2"></i>
										<span>Supprimer</span>
									</button>
								</div>
							</div>
						</div>
					</div>

					<!---------------CONTROL BUTTONS RESPONSIVE -------------------->
					<div
						*ngIf="!loading && selectedMeeting?.Status !== meetingStatus.STOPED"
						class="hide-on-large-only"
					>
						<div class="row">
							<div class="col-12">
								<div class="row">
									<button
										class="col-6 btn btn-sm btn-primary mr-4 mb-2"
										(click)="beginMeeting()"
									>
										Commencer
									</button>
									<div>
										<button
											class="col-6"
											(click)="showInvitationDialog()"
											class="btn btn-sm btn-outline-primary mr-4 d-flex align-items-center"
										>
											<i class="pi pi-copy mr-2"></i>
											<span>Inviter avec une adresse email</span>
										</button>
									</div>
								</div>
							</div>

							<div class="col-12">
								<div class="row">
									<button
										class="col-6"
										(click)="copyMeetingLink()"
										class="btn btn-sm btn-outline-primary mr-4 d-flex align-items-center mb-2"
									>
										<i class="pi pi-copy mr-2"></i>
										<span>Copier le lien d'invitation</span>
									</button>
									<button
										class="col-6"
										(click)="copyMeetingPassCode()"
										class="btn btn-sm btn-outline-primary mr-4 d-flex align-items-center"
									>
										<i class="pi pi-copy mr-2"></i>
										<span>Copier le passcode</span>
									</button>
								</div>
							</div>

							<div class="col-12">
								<div class="row">
									<button
										class="col-6"
										(click)="editMetting({})"
										class="btn btn-sm btn-outline-primary mr-4 d-flex align-items-center"
									>
										<i class="pi pi-pencil mr-2"></i>
										<span>Modifier</span>
									</button>
									<button
										class="col-6"
										(click)="deleteMeeting({}, $event)"
										class="btn btn-sm btn-outline-primary mr-4 d-flex align-items-center"
									>
										<i class="pi pi-times mr-2"></i>
										<span>Supprimer</span>
									</button>
								</div>
							</div>
						</div>
					</div>

					<div *ngIf="selectedMeeting?.Status === meetingStatus.STOPED">
						<button
							class="col-6"
							(click)="deleteMeeting({}, $event)"
							class="btn btn-sm btn-outline-primary mr-4 d-flex align-items-center"
						>
							<i class="pi pi-times mr-2"></i>
							<span>Supprimer</span>
						</button>
					</div>
				</div>

				<div class="container-loader" *ngIf="!loading && !selectedMeeting">
					<div>
						<img
							class="no-meeting"
							src="../../../assets/img/course.svg"
							alt=""
						/>
						<h3>Vous n'avez aucun meeting programmé</h3>
					</div>
				</div>
				<!--
						X-- MEETING DETAILS  --X
				-->
			</div>
		</section>
	</div>
</div>
<p-sidebar
	[(visible)]="sideBarToogle"
	[baseZIndex]="10000"
	[showCloseIcon]="true"
>
	<div class="card border-0 h-100" style="background-color: #eceff1">
		<div class="d-flex align-items-center justify-content-between p-3">
			<i class="pi pi-refresh pointer"></i>
			<button (click)="newMeeting()" class="btn btn-sm btn-primary">
				Nouvelle reunion
			</button>
			<span></span>
		</div>
		<h2 class="font-bold text-center text-black">501 164 3622</h2>
		<p class="text-center border-bottom pb-3">Mon ID de reunion personnelle</p>
		<div class="w-100 pl-3 pr-3">
			<span class="p-input-icon-left mr-4" style="width: 100%; height: 40px"
				><i class="pi pi-search"></i
				><input
					pinputtext=""
					type="text"
					[placeholder]="'admin.misc.search' | translate"
					class="search_input"
			/></span>
		</div>

		<p-scrollPanel
			*ngIf="mettings.length > 0"
			[style]="{ width: '100%', height: '50vh' }"
		>
			<div class="d-flex flex-column p-3 meetings-list">
				<div
					(click)="selectScheduleMeeting(meeting)"
					*ngFor="let meeting of mettings"
					class="d-flex flex-column mb-3 p-2 pointer meeting-item"
					[class]="
						selectedMeeting.Id === meeting.Id
							? 'd-flex flex-column mb-3 p-2 pointer meeting-item active'
							: 'd-flex flex-column mb-3 p-2 pointer meeting-item'
					"
				>
					<h3 class="mb-0">
						{{ meeting.Topic }}
					</h3>
					<p class="mb-0">
						{{
							formatDate(meeting.StartDate) +
								' à ' +
								meeting.StartTime +
								' ' +
								meeting.TimePeriod
						}}
					</p>

					<small>ID de réunion : {{ meeting.MettingId }}</small>
				</div>
			</div>
		</p-scrollPanel>
	</div>
</p-sidebar>
<button
	(click)="sideBarToogle = true"
	mat-fab
	color="primary"
	class="hide-on-large-only"
	aria-label="Example icon button with a delete icon"
	style="position: fixed; bottom: 10vh; right: 15px; z-index: 99"
>
	<mat-icon>menu</mat-icon>
</button>

<p-dialog
	header="Invitation avec un email"
	[style]="{ width: '50vw' }"
	[(visible)]="displayInvitationModal"
	[baseZIndex]="10000"
	[draggable]="false"
	[resizable]="false"
>
	<label>Adress E-mail: </label>
	<div>
		<div class="p-field form-group col-12">
			<p-chips
				[style]="{ width: '100%' }"
				id="prerequisites"
				placeholder="Entrez email des pariticipants"
				name="emails"
				aria-describedby="prerequisites-help"
				[(ngModel)]="mettingsParticipantsEmails"
				[allowDuplicate]="false"
				[addOnBlur]="true"
			></p-chips>
		</div>
		<div class="p-field form-group col-12">
			<div style="width: fit-content; margin-left: auto">
				<button
					(click)="saveMeetingsParticipantsEmail()"
					pButton
					type="button"
					[label]="loadingSaveEmail ? '.' : 'Inviter'"
					class="p-button-danger"
				>
					<app-loading
						*ngIf="loadingSaveEmail"
						[isLoading]="loadingSaveEmail"
					></app-loading>
				</button>
			</div>
		</div>
	</div>
</p-dialog>
