.example-card {
	max-width: 400px;
}

table {
	width: 100%;
	margin-top: 10px;
}

button.action {
	margin-right: 5px;
}

.section-contain-meetings {
	height: 100vh;
	padding-top: 10vh;
	padding-bottom: 0;
	overflow: hidden;
}

.meeting-item {
	border-radius: 10px;
}

.meetings-list .active {
	background: #afaaac;
	color: white !important;
}

.meeting-item h3 {
	color: rgb(54, 53, 53) !important;
	line-height: 20px;
}

.meeting-item .active h3 {
	color: white !important;
}

.meeting-item:hover {
	background: #afaaac;
	// color: white !important;
}

.chat-menu-item .active h3 {
	color: white !important;
}

.chat-sidebar {
	background-color: #eceff1;
	z-index: 9;
}

// .chat-menu-item .active {
//     background: #afaaac;
// }
.chat-menu-item:hover {
	background: #afaaac;
	// color: white !important;
}

.search_input {
	padding-left: 35px;
	height: 40px;
	background: #2524243b;
	border: none;
	margin-bottom: 0px !important;
	width: 100%;
	border-radius: 50px !important;
}

.btn-outline-primary:not(:disabled):not(.disabled).active,
.btn-outline-primary:not(:disabled):not(.disabled):active,
.show > .btn-outline-primary.dropdown-toggle {
	color: #fff;
	background-color: #da0b4e !important;
	border-color: #da0b4e !important;
}

.btn-outline-primary.active,
.btn-outline-primary:active,
.btn-outline-primary:focus {
	background: #da0b4e !important;
}

.container-loader {
	width: 100%;
	max-width: 700px;
	height: 300px;
	display: flex;
	justify-content: center;
	align-items: center;

	div {
		img.no-meeting {
			width: 400px;
			max-width: 80%;
			height: auto;
		}
	}
}

.col-12 {
	margin: 5px auto;
}

.p-chips.p-component {
	ul {
		width: 100% !important;
	}
}

.p-inputtext.p-chips-multiple-container {
	max-width: 100% !important;
	height: fit-content !important;
}

.email-modal {
	width: 50vw !important;
}

@media screen and (max-width: 550px) {
	.email-modal {
		width: 100%;
	}
}

@media only screen and (max-width: 992px) {
	.section-contain-meetings {
		padding: unset;
		margin-top: 100px;
		display: flex;
		flex-direction: column;
		align-items: center;
	}
}
