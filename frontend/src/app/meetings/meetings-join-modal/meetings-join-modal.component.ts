import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { NgxAgoraService } from 'ngx-agora';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { Meetings } from '~/app/models/meetings';
import { MeetingsService } from '~/app/services/meetings/meetings.service';

@Component({
	selector: 'app-meetings-join-modal',
	templateUrl: './meetings-join-modal.component.html',
	styleUrls: ['./meetings-join-modal.component.scss'],
	encapsulation: ViewEncapsulation.None
})
export class MeetingsJoinModalComponent implements OnInit {
	meetingId: { name: string; id: number };
	meetingsIds: { name: string; id: number }[] = [];
	userName = '';
	meetings: Meetings[] = [];

	noSound: boolean = false;
	noVideo: boolean = false;

	loading: boolean = false;

	constructor(
		public ref: DynamicDialogRef,
		public config: DynamicDialogConfig,
		private meetingService: MeetingsService,
		private router: Router,
		private toastr: ToastrService
	) {
		this.meetings = [];
	}

	async ngOnInit(): Promise<void> {
		this.meetings = await this.meetingService.getAll().toPromise();
		this.meetings?.map((meeting) =>
			this.meetingsIds.push({
				name: meeting.MettingId + ' - ' + meeting.Topic.substring(0, 100),
				id: meeting.Id
			})
		);
	}
	joinMeeting(): void {
		// if (!this.userName) {
		// 	this.toastr.error(
		// 		'Vous devez vous attribuer un nom pendant le meeting',
		// 		'Votre nom est obligatoire'
		// 	);
		// 	return;
		// }
		// this.loading = true;
		const meetingExist = this.meetings?.find(
			(meeting) => meeting.Id === this.meetingId.id
		);
		console.log(this.meetingId);
		console.log(this.meetings);
		console.log(meetingExist);
		if (meetingExist) {
			this.closeModal();
			this.router.navigateByUrl(this.getMeetingLink(meetingExist));
			return;
		}
		this.toastr.error(
			"L'ID du meeting que vous avez entré n'est pas correct",
			'Impossible de joindre le meeting'
		);
		this.closeModal();
		this.loading = false;
	}
	closeModal(): void {
		this.ref.close();
	}

	getMeetingLink(meeting: Meetings): string {
		return `/meetings/join/${meeting.Slug}`;
	}
	/* meeting: any;
  meetings = [
    { name: 'Reunion de New York - 9992088392', code: 'NY' },
    { name: 'Les gamers du dimanche - 334898894', code: 'RM' },
    { name: 'Reunion du vendredi - 009092188928', code: 'LDN' }
  ];
  userName = '';
  constructor(public ref: DynamicDialogRef, public config: DynamicDialogConfig, private agoraService: NgxAgoraService,
    private router: Router) { }

  ngOnInit(): void {
  }
  joinMeeting(): void {
    //this.closeModal();
    this.agoraService.client.join(null, '1000', null, (uid) => { });
    this.router.navigate(['/meetings/meeting/123']);

  }
  closeModal(): void {
    this.ref.close();
  } */
}
