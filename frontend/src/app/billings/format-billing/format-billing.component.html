<table class="table table-bordered">
	<thead>
		<tr>
			<th scope="col">#</th>
			<th scope="col">Cours</th>
			<th scope="col">Prix</th>
			<th scope="col">Visionages</th>
			<th scope="col">Achats</th>
			<th scope="col">Gains</th>
			<th scope="col">Forfait</th>
		</tr>
	</thead>
	<tbody>
		<tr *ngFor="let bill of bills; let i = index">
			<th scope="row">{{ i + 1 }}</th>
			<td>
				{{ bill.Course.Title }}
			</td>
			<td>{{ bill.Course.Price }}</td>
			<td>{{ bill.Viewings }}</td>
			<td>{{ bill.Buys }}</td>
			<td class="split-cel">
				<table class="inner-table">
					<tr>
						<th>Achats</th>
						<th>Visionages</th>
					</tr>
					<tr>
						<td>{{ bill.Gain.Buy }} CA$</td>
						<td>{{ bill.Gain.Viewing }} CA$</td>
					</tr>
				</table>
			</td>
			<td class="split-cel">
				<table class="inner-table">
					<td>
						{{ bill.BillSetting.ViewingCount }} Visionages =
						{{ bill.BillSetting.ViewingPrice }} CA$
					</td>
					<td>{{ bill.BillSetting.PourcentBuy }}% des Achats</td>
				</table>
			</td>
		</tr>

		<tr>
			<th>Totaux:</th>
			<td class="total">{{ bills.length }} Cours</td>
			<td class="total">{{ getTotalPrice() }}</td>
			<td class="total">{{ getTotalViewingsCount() }}</td>
			<td class="total">{{ getTotalBuyCount() }}</td>
			<td class="split-cel">
				<table class="inner-table">
					<!-- <tr>
						<th>Achats</th>
						<th>Visionages</th>
					</tr> -->

					<td class="total">{{ getTotalBuyPrice() }}CA$</td>
					<td class="total">{{ getTotalViewingsPrice() }}CA$</td>
				</table>
			</td>
			<td>
				Montant net:
				<strong style="color: red">{{ getBillsTotalPrice() }}CA$</strong>
			</td>
		</tr>
	</tbody>
</table>
