import { Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Bill } from '~/app/models/billings/billing.model';
import { Course } from '~/app/models/course.model';

@Component({
	selector: 'app-format-billing',
	templateUrl: './format-billing.component.html',
	styleUrls: ['./format-billing.component.scss']
})
export class FormatBillingComponent implements OnInit {
	@Input() bills: Bill[];

	constructor(private router: Router) {}

	ngOnInit(): void {
		console.log('exécuté****************');
	}

	getBillsTotalPrice(): number {
		let amount = 0;
		this.bills?.map((bill: Bill) => {
			amount += bill.Gain.Buy + bill.Gain.Viewing;
		});
		return amount;
	}

	getTotalViewingsPrice(): number {
		let amount = 0;
		this.bills?.map((bill: Bill) => {
			amount += bill.Gain.Viewing;
		});
		return amount;
	}

	getTotalBuyPrice(): number {
		let amount = 0;
		this.bills?.map((bill: Bill) => {
			amount += bill.Gain.Buy;
		});
		return amount;
	}

	getTotalViewingsCount(): number {
		let count = 0;
		this.bills?.map((bill: Bill) => {
			count += bill.Viewings;
		});
		return count;
	}

	getTotalBuyCount(): number {
		let count = 0;
		this.bills?.map((bill: Bill) => {
			count += bill.Buys;
		});
		return count;
	}

	getTotalPrice(): number {
		let amount = 0;
		this.bills?.map((bill: Bill) => {
			amount += bill.Course.Price;
		});
		return amount;
	}

	// navigateToCourse(course: Course): void {
	// 	console.log(this.router.url);
	// 	if (this.router.url.split('/')[1] === 'private') {
	// 		window.open(`/admin/privateCours-details/${course.Slug}`);
	// 	} else {
	// 		window.open(`/courses/${course.Slug}`);
	// 	}
	// }
}
