.cources_facts {
	position: relative !important;
	width: 100%;
	white-space: nowrap;
	overflow: hidden !important;
	//padding: 0 20px;
}

.cources_facts_list {
	max-width: 100% !important;
	white-space: nowrap !important;
	overflow-x: auto !important;
	margin-bottom: -10px;
	height: 40px;
}
.chevronskill {
	font-size: 12px;
}

.education_block_grid .bl-title {
	height: 25px !important;

	& a {
		color: black;
		&:hover {
			color: #da0b4e;
		}
	}
}

.education_block_body {
	height: 140px;
	padding: 15px 15px 10px 15px !important;
}

.strike {
	text-decoration: line-through;
}
.mt-items-content {
	margin-top: -18px;
}
.mt-items-content2 {
	margin-top: -10px;
}

/* .title-block {
	height: 68px !important;
}
 */
