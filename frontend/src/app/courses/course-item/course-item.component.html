<!--Popover-->
<ng-template #popContent>
	<div class="d-flex flex-column py-2">
		<div class="h6 font-weight-bold">{{ course.Title }}</div>
		<small class="text-success">{{ course.UpdatedAt | dateAgo }}</small>
		<p class="" [innerHTML]="resume"></p>
		<ol class="">
			<li *ngFor="let goal of course.Goals; let i = index">
				{{ goal }}
			</li>
		</ol>
	</div>
</ng-template>
<!--Popover end-->
<!-- <ng-template *ngIf="course"> -->
<div
	class="education_block_grid style_2"
	[ngbPopover]="popContent"
	triggers="mouseenter:mouseleave"
>
	<div class="education_block_thumb n-shadow">
		<a routerLink="/courses/{{ course.Slug }}"
			><img [src]="cover" class="img-fluid" alt=""
		/></a>
	</div>

	<div class="education_block_body title-block">
		<span class="bl-title h4"
			><a routerLink="/courses/{{ course.Slug }}">
				{{ title }}
			</a></span
		>

		<div class="mt-1 d-flex justify-content-between">
			<div class="">
				<i
					class="far fa-file-pdf text-primary font-weight-bold"
					*ngIf="contentType.PDF === course.Format"
				></i>
				<i
					class="fas fa-book text-primary font-weight-bold"
					*ngIf="contentType.EBOOK === course.Format"
				></i>
				<i
					class="far fa-play-circle text-primary font-weight-bold"
					*ngIf="contentType.VIDEO === course.Format"
				></i>
			</div>
			<div class="ed_view_price px-4">
				<h6 class="theme-cl" *ngIf="course.Free">
					<span class="badge badge-success">{{
						'course.free' | translate
					}}</span>
				</h6>
				<h4 class="theme-cl" *ngIf="!course.Free && course.NewPrice">
					<span class="text-success">
						{{ course.Currency }} {{ course.NewPrice }}
					</span>
					<sup class="small text-danger">
						<strike> {{ course.Currency }} {{ course.Price }} </strike>
					</sup>
				</h4>
				<h4
					class="theme-cl text-success"
					*ngIf="!course.Free && !course.NewPrice"
					style="margin-bottom: 0px !important"
				>
					{{ course.Currency }} {{ course.Price }}
				</h4>
			</div>
		</div>

		<div class="cources_facts">
			<!--div class="d-flex justify-content-end">
      <button  class=" btn p-0 m-0 mr-2 btn-link" (click)="scroolItemActivity($event, true)">
        <i class=" fas fa-chevron-left mr-1 chevronskill " ></i>
      </button>
      <button class=" btn p-0 m-0 btn-link"  (click)="scroolItemActivity($event, false)">
         <i class="fas fa-chevron-right ml-1 chevronskill" ></i>
      </button>
    </div-->

			<div class="cources_facts_list" id="cources_facts_list">
				<div
					class="badge badge-secondary mx-1"
					*ngFor="let keyword of course.Keywords"
				>
					{{ keyword }}
				</div>
			</div>
		</div>
	</div>

	<div class="education_block_footer mt-items-content2">
		<div class="education_block_author">
			<div class="path-img">
				<a [routerLink]="'/instructors/' + course.CreatedBy.Slug"
					><img [src]="profile" class="img-fluid" alt=""
				/></a>
			</div>
			<div class="d-flex flex-column">
				<a [routerLink]="'/instructors/' + course.CreatedBy.Slug" class="">{{
					course.CreatedBy.Firstname
				}}</a>
				<span class="d-flex small text-muted">{{
					course.CreatedBy.Profil
				}}</span>
			</div>
		</div>
		<span class="education_block_time"
			><i class="ti-calendar mr-1"></i>{{ course.CreatedAt | dateAgo }}</span
		>
		<!-- <span class="education_block_time"><i class="ti-calendar mr-1"></i>{{course.CreatedAt |timeago}}</span> -->
	</div>
</div>

<!-- </ng-template> -->
