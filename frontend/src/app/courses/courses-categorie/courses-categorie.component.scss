.page-title {
    margin-top: 50px !important;
}

.cources_info {
    padding-top: 12px !important;
}

.load-more {
    margin: auto;
    display: flex;
    width: 200px;
    text-align: center;
    flex-direction: column;
}

.listOfCourse {
    min-height: 50vh !important;
}

.header1 {
    background: linear-gradient(rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.1)), url(~src/assets/img/bm-course-img.png) no-repeat;
    // background-attachment: fixed;
    background-position: center;
    background-size: 100% 75%;
    /* background-repeat: no-repeat; */
    height: 50vh;
    background-position-x: 0;
    background-position-y: 0;
    background-size: cover;
}

.title {
    color: white;
    text-align: center;
}

.title-height-100 {
    height: 100% !important;
}

.bm-text-decoration {
    text-decoration: none;
}
