import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import {
	Course,
	CourseSearch,
	CourseSearchResult,
	OrderbyType
} from '../../models/course.model';
import { CategoryService } from '../../services/categories/category.service';
import { ActivatedRoute, Router } from '@angular/router';
import { CourseService } from '../../services/courses/course.service';
import { Category } from '~/app/models/category.model';
import { UtilsService } from '../../services/utils.service';
import { User } from '~/app/models/user.model';

@Component({
	selector: 'app-courses-categorie',
	templateUrl: './courses-categorie.component.html',
	styleUrls: ['./courses-categorie.component.scss']
})
export class CoursesCategorieComponent implements OnInit {
	skip = 0;
	take: number;
	// countOfItem = 0;
	courseSearchResult: CourseSearchResult;
	coursesModeList = true;
	categorySlug: string;
	category: Category;
	loadMore: boolean;
	search: CourseSearch;
	user: User;
	showFilterMenu = false;

	loadingSearch: boolean = false;
	paginateNumberList = [];
	paginateNumberSelected = 0;
	totalRecord = 0;

	constructor(
		private courseService: CourseService,
		private categoryService: CategoryService,
		private activedRoute: ActivatedRoute,
		private router: Router,
		private utilsService: UtilsService
	) {}

	async ngOnInit(): Promise<void> {
		this.take = CourseService.COURSETAKE;

		this.search = {
			Categories: [],
			Orderby: OrderbyType.RECENT,
			PriceType: [],
			Title: '',
			Type: []
		};

		this.categorySlug = this.activedRoute.snapshot.params.id;

		if (this.activedRoute.snapshot.params.id) {
			this.categorySlug = this.activedRoute.snapshot.params.id;
		} else {
			this.router.navigate(['/']);
		}
		/*
    this.courseService.getAllByCategories(this.categorySlug, this.take, this.skip).subscribe((courses: any) => {
      this.courseSearchResult = courses;
      this.courseSearchResult.Courses = courses.Courses.map((f) => {
        f.Duration = this.utilsService.getCourseTime(f);
        return f;
      });
    }); */

		if (this.categorySlug) {
			this.category = await this.categoryService
				.getBySlug(this.categorySlug)
				.toPromise();
			if (
				this.category &&
				!this.search.Categories.find((f) => this.category.Id === f)
			) {
				this.search.Categories.push(this.category.Id);
			}
		}

		/* this.courseService
      .getByFilter({ take: this.take, skip: this.skip }, this.search)
      .subscribe((res) => {
        console.log('the search result :', res);
        this.courseSearchResult = res;

        const paginateNumber = Math.ceil(this.courseSearchResult.Count / CourseService.COURSETAKE);
        this.paginateNumberList = new Array(paginateNumber).fill(5, 0, paginateNumber).map((x, i) => i + 1);

        this.loadingSearch = false;
      }); */
		this.onSearch();
	}
	openFilter(): void {
		this.showFilterMenu = true;
	}

	onSearch(searchMode = null, reinitialSkip = false) {
		this.loadingSearch = true;

		if (reinitialSkip) {
			this.skip = 0;
		}

		this.courseService
			.getByFilter({ take: this.take, skip: this.skip }, this.search)
			.subscribe((res) => {
				console.log('the search result :', res);
				if (this.courseSearchResult && searchMode !== 'FROM_PAGINATE') {
					const lastCours = this.courseSearchResult.Courses;
					this.courseSearchResult = res;
					this.courseSearchResult.Courses = [
						...lastCours,
						...this.courseSearchResult.Courses
					];
				} else {
					this.courseSearchResult = res;
				}

				this.totalRecord = res.Count;

				//console.log("paginateNumberpaginateNumberpaginateNumber", paginateNumber, this.paginateNumberList);

				this.loadMore = this.courseSearchResult.Count > this.take + this.skip;

				this.loadingSearch = false;
			});
	}

	onCourseMore(slug: string): void {
		this.skip =
			this.courseSearchResult.Count < this.skip + this.take
				? this.courseSearchResult.Count
				: this.skip + this.take;
		this.loadMore = this.courseSearchResult.Count > this.take + this.skip;
		this.onSearch();
	}

	onLoadFromPage(pageNumber: number): void {
		if (pageNumber === this.paginateNumberSelected) return;
		window.scroll(0, 400);
		this.skip = pageNumber === 0 ? 0 : pageNumber * this.take;
		this.paginateNumberSelected = pageNumber;
		this.onSearch('FROM_PAGINATE');
	}

	onCourseLoadingListener(isLoading) {
		this.loadingSearch = isLoading;
	}

	onSearchCourse(s) {
		this.onSearch('FROM_PAGINATE', true);
		this.paginateNumberSelected = 0;
	}
}
