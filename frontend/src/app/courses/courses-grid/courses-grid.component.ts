import {
	Course,
	CourseSearch,
	CourseSearchResult,
	OrderbyType
} from '~/app/models/course.model';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { environment } from '~/environments/environment';
import { Media } from '~/app/models/media.model';
import { UtilsService } from '~/app/services/utils.service';
import { CourseService } from '~/app/services/courses/course.service';

@Component({
	selector: 'app-courses-grid',
	templateUrl: './courses-grid.component.html',
	styleUrls: ['./courses-grid.component.scss']
})
export class CoursesGridComponent implements OnInit {
	@Input()
	search: CourseSearch;
	@Output()
	searchChange: EventEmitter<CourseSearch> = new EventEmitter<CourseSearch>();

	@Input()
	onSearch: Function;

	@Input()
	queryPage: number;

	@Input()
	loadingSearch: boolean;

	orderBy = OrderbyType;

	@Input() paginateNumberList = [];
	@Input() totalRecord = 0;

	@Input() courseSearchResult: CourseSearchResult;
	@Output()
	courseSearchResultChange: EventEmitter<CourseSearchResult> = new EventEmitter<CourseSearchResult>();
	@Input() loadMore: boolean;
	@Output()
	courseMoreEmitter: EventEmitter<string> = new EventEmitter<string>();

	@Output()
	courseLoadFromPaginateEmitter: EventEmitter<number> = new EventEmitter<number>();

	@Input()
	skip = 0;
	@Output()
	skipChange: EventEmitter<number> = new EventEmitter<number>();
	@Input()
	take: number;
	@Output()
	takeChange: EventEmitter<number> = new EventEmitter<number>();
	showFilterMenu = false;

	isloadingSearchCourse = false;
	paginateNumberSelected = 1;

	constructor(
		private utilsService: UtilsService,
		private courseService: CourseService
	) {}

	ngOnInit(): void {}

	onLoadMore(e): void {
		e.preventDefault();
		this.courseMoreEmitter.emit(this.utilsService.getUniqueId());
	}

	onLoadFromPage(page: number): void {
		window.scroll(0, 400);
		this.courseLoadFromPaginateEmitter.emit(page + 1);
		this.paginateNumberSelected = page;
	}

	onOrder(e, order: OrderbyType): void {
		e.preventDefault();
		this.search.Orderby = order;
		this.searchChange.emit(this.search);
		e.preventDefault();

		this.courseService
			.getByFilter({ take: this.take, skip: this.skip }, this.search)
			.subscribe((res) => {
				console.log('the search result :', res);
				this.courseSearchResult = res;
				this.courseSearchResultChange.emit(this.courseSearchResult);
			});
	}
	openMenu(): void {
		this.showFilterMenu = true;
		console.log('here');
	}

	onCourseLoadingListener(isLoading) {
		this.isloadingSearchCourse = isLoading;
	}

	onSearchCourse(s) {
		window.scroll(0, 400);
		this.onSearch('FROM_PAGINATE', true);
		this.paginateNumberSelected = 1;
	}
}
