<!-- ============================ Find Courses with Sidebar ================================== -->
<section class="pt-0 filter_section">
	<div class="container">
		<!-- Row -->
		<div class="row">
			<div class="col-lg-4 col-md-12 col-sm-12 order-2 order-lg-1 order-md-2">
				<div class="page_sidebar hide-23">
					<app-courses-search
						[(courseSearchResult)]="courseSearchResult"
						[(skip)]="skip"
						[(take)]="take"
						[(search)]="search"
						(courseSearchLoadingEmitter)="onCourseLoadingListener($event)"
						(searchCourse)="onSearchCourse($event)"
						[isLoading]="loadingSearch"
					></app-courses-search>
				</div>
			</div>

			<div class="col-lg-8 col-md-12 col-sm-12 order-1 order-lg-2 order-md-1">
				<!-- Row -->
				<div class="row align-items-center mb-3">
					<div class="col-lg-6 col-md-6 col-sm-12">
						{{ 'course.details.weFound' | translate }}
						<strong>{{
							courseSearchResult ? courseSearchResult.Count : 0
						}}</strong>
						{{ 'course.details.coursesForYou' | translate }}
					</div>
					<div class="col-lg-6 col-md-6 col-sm-12 ordering">
						<div class="filter_wraps">
							<div class="dn db-991 mt30 mb0 show-23">
								<div id="main2">
									<button
										class="btn btn-theme arrow-btn filter_open"
										(click)="openMenu()"
									>
										{{ 'course.details.filter' | translate
										}}<span><i class="fas fa-arrow-alt-circle-right"></i></span>
									</button>
								</div>
							</div>
							<div class="dropdown show">
								<a
									class="btn btn-custom dropdown-toggle"
									href="#"
									role="button"
									data-toggle="dropdown"
									aria-haspopup="true"
									aria-expanded="false"
								>
									{{ 'instructor.course.filter.lblRecent' | translate }}
								</a>
								<div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
									<a
										class="dropdown-item"
										href=""
										(click)="onOrder($event, orderBy.RECENT)"
										>{{ 'instructor.course.filter.lblRecent' | translate }}</a
									>
									<a
										class="dropdown-item"
										href=""
										(click)="onOrder($event, orderBy.LAST)"
										>{{ 'instructor.course.filter.lblOld' | translate }}</a
									>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!-- /Row -->

				<div
					class="row"
					*ngIf="
						!loadingSearch &&
						courseSearchResult &&
						courseSearchResult.Courses?.length > 0
					"
				>
					<!-- Cource Grid 1 -->
					<div
						class="col-lg-6 col-md-6 col-sm-6"
						*ngFor="let course of courseSearchResult.Courses; let i = index"
					>
						<app-course-item
							[position]="i + 1"
							[course]="course"
						></app-course-item>
					</div>
				</div>

				<div *ngIf="loadingSearch" class="text-center">
					<img src="assets/img/forum_loaging.gif" alt="" />
				</div>

				<div
					class="d-flex listOfCourse justify-content-center align-items-center"
					*ngIf="!loadingSearch && courseSearchResult.Courses?.length === 0"
				>
					<div class="h2 font-bold">
						{{ 'course.details.noCourse' | translate }}
					</div>
				</div>

				<!-- Row -->
				<div
					class="row"
					*ngIf="
						!loadingSearch &&
						courseSearchResult &&
						courseSearchResult.Courses?.length > 0
					"
				>
					<!-- Pagination -->
					<div
						class="col-lg-12 col-md-12 col-sm-12 text-center"
						*ngIf="loadMore"
						[disabled]="loadingSearch"
					>
						<!-- <button
									type="button"
									class="btn btn-loader"
									(click)="onLoadMore($event)"
								>
									{{ 'home.page.loadMore' | translate
									}}<i class="ti-reload ml-3"></i>
								</button> -->
					</div>

					<div class="col-lg-12 col-md-12 col-sm-12 text-center">
						<!-- <button
							[disabled]="loadingSearch || paginateNumberSelected === num"
							type="button"
							class="btn btn-pagninate mr-2"
							[ngClass]="{ active: paginateNumberSelected === num }"
							*ngFor="let num of paginateNumberList"
							(click)="onLoadFromPage(num)"
						>
							{{ num }}
						</button> -->
						<p-paginator
							[rows]="take"
							[totalRecords]="totalRecord"
							(onPageChange)="onLoadFromPage($event.page)"
						></p-paginator>
					</div>
				</div>
				<!-- /Row -->
			</div>
		</div>
		<!-- Row -->
	</div>
	<p-sidebar [(visible)]="showFilterMenu" [baseZIndex]="10000">
		<h4>{{ 'course.details.filter' | translate }}</h4>
		<div id="" class="mt-3">
			<div class="filt-head">
				<h4 class="filt-first">{{ 'course.advancedOptions' | translate }}</h4>
				<!-- <a href="javascript:void(0)" class="closebtn" (click)="openMenu($event)">Close <i class="ti-close"></i></a> -->
			</div>
			<div class="show-hide-sidebar">
				<!-- Find New Property -->
				<div class="sidebar-widgets">
					<app-courses-search
						[(courseSearchResult)]="courseSearchResult"
						[(skip)]="skip"
						[(take)]="take"
						[(search)]="search"
						(courseSearchLoadingEmitter)="onCourseLoadingListener($event)"
						(searchCourse)="onSearchCourse($event)"
						[isLoading]="loadingSearch"
					></app-courses-search>
				</div>
			</div>
		</div>
	</p-sidebar>
</section>

<!-- ============================ Find Courses with Sidebar End ================================== -->
