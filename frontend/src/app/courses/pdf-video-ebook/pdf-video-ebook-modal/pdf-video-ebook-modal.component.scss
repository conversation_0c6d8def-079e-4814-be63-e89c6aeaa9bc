pdf-viewer {
  display: block;
  //height: 600px !important;
  max-width: 100% !important;
}

::ng-deep  pdf-viewer {
  display: block;
  //height: 600px !important;
  max-width: 100% !important;
}

.peview-render {
  position: relative !important;
  // background-color: #A4C7E5 !important;
  .bg-navigation {
    position: fixed !important;
    bottom: 4em !important;
    right: 4em !important;
    z-index: 1000;
    background-color: #8699A9 !important;
  }
}

.modal-header{
  z-index: 1000;
}


ly-dialog-container{
  border-radius: 0 !important;
}


::ng-deep ly-dialog-container{
  border-radius: 0 !important;
}


.preloading{
  height: 70vh !important;
}
