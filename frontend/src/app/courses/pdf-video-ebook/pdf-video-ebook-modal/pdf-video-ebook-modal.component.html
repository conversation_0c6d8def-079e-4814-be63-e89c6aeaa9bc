<div class="modal-header d-flex align-items-center px-4 pb-1 mb-1 w-100 mw-100 border-0 position-fixed">
    <h4 class="modal-title" id="modal-basic-title">
        {{'blog.create-post.preview'|translate}}
    </h4>
    <button type="button" class="close ml-auto" aria-label="Close" (click)="close($event)">
    <span aria-hidden="true">&times;</span>
  </button>
</div>



<div class="modal-body mw-100">

    <form class="bs-component" autocomplete="off">

        <div class="" *ngIf="htmlText" [innerHTML]="htmlText | hmColor: '#18BE63'"></div>

        <div class="d-flex flex-column align-items-center justify-content-center w-100 preloading" *ngIf="loadingCurrent < 1">
            <p-progressSpinner></p-progressSpinner>
        </div>
        <div class="peview-render " *ngIf="pdfLink">
            <pdf-viewer [src]="pdfLink" [autoresize]="true" [render-text]="true" [show-all]="showAll" (on-progress)="onProgress($event)" [stick-to-page]="stickToPage" (after-load-complete)="afterLoadComplete($event)" (pageChange)="page = $event" [(page)]="page" [zoom]="zoom">
            </pdf-viewer>


            <div class="d-flex bg-navigation d-flex flex-column justify-content-between px-2">
                <!--button class="btn btn-link" [disabled]="page < 2" (click)="page = page - 1">
          <i class="fas fa-chevron-up text-white"></i>
        </button>
        <button class="btn btn-link">
          <span class="text-white"> {{page}} / {{totalPages}}</span>
        </button>
        <button class="btn btn-link" (click)="page = page + 1">
          <i class="fas fa-chevron-down text-white"></i>
        </button-->
                <button class="btn btn-link" [disabled]="zoom < .3" (click)="zoom = zoom - 0.1">
          <i class="fas fa-search-minus text-white"></i>
        </button>
                <button class="btn btn-link" (click)="zoom = zoom + 0.1">
          <i class="fas fa-search-plus text-white"></i>
        </button>
            </div>
        </div>
    </form>
</div>