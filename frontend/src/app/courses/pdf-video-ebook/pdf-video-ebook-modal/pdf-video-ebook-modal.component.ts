import {UtilsService} from '~/app/services/utils.service';

import {Component, Inject, OnInit} from '@angular/core';
import {LY_DIALOG_DATA, LyDialogRef} from '@alyle/ui/dialog';
import {TranslateService} from '@ngx-translate/core';
import {ToastrService} from 'ngx-toastr';
// import {PDFProgressData} from 'pdfjs-dist';

@Component({
  selector: 'app-pdf-video-ebook-modal',
  templateUrl: './pdf-video-ebook-modal.component.html',
  styleUrls: ['./pdf-video-ebook-modal.component.scss']
})
export class PdfVideoEbookModalComponent implements OnInit {

  showAll = true;
  stickToPage = false;
  page = 1;
  scrollbar = true;
  zoom = 1.5;
  totalPages: 1;

  pdfLink: string;
  htmlText: string;

  loadingCurrent = 0;
  loadingTotal = 0;

  constructor(private dialogRef: LyDialogRef,
              private utilsService: UtilsService,
              private translateService: TranslateService,
              private toastr: ToastrService,
              @Inject(LY_DIALOG_DATA) private data: {
                pdfLink: string,
                htmlText: string
              }) { }

  ngOnInit(): void {
    this.pdfLink = this.data.pdfLink;
    this.htmlText = this.data.htmlText;
  }

  close(e): void {
    if (e) {e.preventDefault(); }
    this.dialogRef.close();
    // this.activeModal.close();
  }

  afterLoadComplete(pdf): void {
    this.totalPages = pdf.numPages;
  }

  pagechanging(e): void{
    // this.page = +e; // the page variable
    // console.log('page : ' + this.page);
    console.log('item : ', e);
  }

  onProgress(progressData: any): void{
    // do anything with progress data. For example progress indicator
    this.loadingCurrent = progressData.loaded;
    this.loadingTotal = progressData.total;
  }
}
