<!--div class="row">
  <mat-card class="col-12 col-md-6">
    <mat-card-title>Video</mat-card-title>
    <mat-card-subtitle>Plyr with custom video as a source</mat-card-subtitle>

    <div mat-card-image plyr plyrTitle="Custom video" [plyrPlaysInline]="true" [plyrCrossOrigin]="true"
         [plyrPoster]="poster" [plyrSources]="videoSources" [plyrTracks]="tracks" (plyrInit)="player = $event"
         (plyrPlay)="played($event)"></div>
    <mat-card-actions>
      <button mat-raised-button color="accent" (click)="play()">
        <mat-icon>play_arrow</mat-icon>
        Play
      </button>

      <button mat-raised-button color="accent" (click)="pause()">
        <mat-icon>pause</mat-icon>
        Pause
      </button>

      <button mat-raised-button color="accent" (click)="stop()">
        <mat-icon>stop</mat-icon>
        Stop
      </button>
    </mat-card-actions>
  </mat-card>

  <mat-card class="col-12 col-md-6">
    <mat-card-title>Audio</mat-card-title>
    <mat-card-subtitle>Plyr with audio as a source</mat-card-subtitle>

    <div mat-card-image plyr plyrTitle="Audio" plyrType="audio" [plyrPlaysInline]="true" [plyrCrossOrigin]="true"
         [plyrSources]="audioSources"></div>
  </mat-card>
</div>

<mat-divider></mat-divider>


<div class="row">
  <mat-card class="col-12 col-md-6">
    <mat-card-title>Youtube</mat-card-title>
    <mat-card-subtitle>Plyr with Yotube video as a source</mat-card-subtitle>

    <div mat-card-image plyr plyrTitle="Video from Youtube" [plyrSources]="youtubeSources">
    </div>

  </mat-card>

  <mat-card class="col-12 col-md-6">
    <mat-card-title>Vimeo</mat-card-title>
    <mat-card-subtitle>Plyr with Vimeo video as a source</mat-card-subtitle>

    <div mat-card-image plyr plyrTitle="Video from Vimeo" [plyrSources]="vimeoSources"></div>
  </mat-card>
</div-->
<div *ngIf="poster && sources">
	<div
		class="mb-4"
		plyr
		plyrTitle="Custom video"
		[plyrPlaysInline]="true"
		[plyrCrossOrigin]="true"
		[plyrPoster]="poster"
		[plyrSources]="sources"
		(plyrProgress)="onProgress($event)"
		(plyrPause)="onPause($event)"
		(plyrEnded)="onEnded($event)"
		[plyrTracks]="tracks"
		(plyrInit)="player = $event"
		(plyrPlay)="played($event)"
	></div>
</div>

<div class="mb-4" *ngIf="htmlText" [innerHTML]="htmlText"></div>

<div class="peview-render mb-4" *ngIf="pdfLink">
	<div class="d-flex justify-content-center text-center h4 mt-3">
		{{ title }}
	</div>
	<pdf-viewer
		[src]="pdfLink"
		[autoresize]="true"
		[show-borders]="true"
		[show-all]="showAll"
		[stick-to-page]="stickToPage"
		(after-load-complete)="afterLoadComplete($event)"
		[(page)]="page"
		[zoom]="zoom"
	>
	</pdf-viewer>

	<div
		class="bg-navigation w-100 d-flex flex-row justify-content-between px-4 bg-dark py-1"
	>
		<div class="">
			<button
				class="btn btn-link"
				[disabled]="zoom < 0.3"
				(click)="zoom = zoom - 0.1"
			>
				<i class="fas fa-minus text-white"></i>
			</button>
			<button class="btn btn-link" (click)="zoom = zoom + 0.1">
				<i class="fas fa-plus text-white"></i>
			</button>
			<button class="btn btn-link" type="button" (click)="openLargePDF($event)">
				<i class="pi pi-th-large text-white"></i>
			</button>
		</div>
		<div class="">
			<button
				class="btn btn-link"
				[disabled]="page < 2"
				(click)="page = page - 1"
			>
				<i class="fas fa-chevron-left text-white"></i>
			</button>
			<button class="btn btn-link px-1">
				<span class="text-white"> {{ page }} / {{ totalPages }}</span>
			</button>
			<button class="btn btn-link" (click)="page = page + 1">
				<i class="fas fa-chevron-right text-white"></i>
			</button>
		</div>
	</div>
</div>
