import {UtilsService} from '~/app/services/utils.service';
import {Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild} from '@angular/core';
import {PlyrComponent} from 'ngx-plyr';
import {PdfVideoEbookModalComponent} from '~/app/courses/pdf-video-ebook/pdf-video-ebook-modal/pdf-video-ebook-modal.component';

@Component({
  selector: 'app-pdf-video-ebook',
  templateUrl: './pdf-video-ebook.component.html',
  styleUrls: ['./pdf-video-ebook.component.scss']
})
export class PdfVideoEbookComponent implements OnInit, OnChanges  {

  // get the component instance to have access to plyr instance
  @ViewChild(PlyrComponent, { static: true })
  plyr: PlyrComponent;

  @Input() autoPlay = false;

  // or get it from plyrInit event
  player: Plyr;

  @Input() sources: Plyr.Source[];
  tracks: Plyr.Track[];
  @Input() poster: string;
  @Input() transcriptions: any[];
  @Input() pdfLink: string;
  @Input() htmlText: string;
  @Input() title: string;

  @Output() onProgressChange = new EventEmitter<Plyr.PlyrEvent>();
  @Output() onPauseChange = new EventEmitter<Plyr.PlyrEvent>();
  @Output() onEndedChange = new EventEmitter<Plyr.PlyrEvent>();


  showAll = false;
  stickToPage = false;
  page = 1;
  scrollbar = true;
  zoom = 0.45;
  totalPages: 1;






  /*
    audioSources = [
      {
        src: 'https://cdn.plyr.io/static/demo/Kishi_Bashi_-_It_All_Began_With_a_Burst.mp3',
        type: 'audio/mp3',
      },
      {
        src: 'https://cdn.plyr.io/static/demo/Kishi_Bashi_-_It_All_Began_With_a_Burst.ogg',
        type: 'audio/ogg',
      },
    ];

    youtubeSources = [
      {
        src: 'https://youtube.com/watch?v=bTqVqk7FSmY',
        provider: 'youtube',
      },
    ];

    vimeoSources = [
      {
        src: 'https://vimeo.com/76979871',
        provider: 'vimeo',
      },
    ];

    videoSources: Plyr.Source[] = [
      {
        src: 'https://cdn.plyr.io/static/demo/View_From_A_Blue_Moon_Trailer-576p.mp4',
        type: 'video/mp4',
        size: 576,
      },
      {
        src: 'https://cdn.plyr.io/static/demo/View_From_A_Blue_Moon_Trailer-720p.mp4',
        type: 'video/mp4',
        size: 720,
      },
      {
        src: 'https://cdn.plyr.io/static/demo/View_From_A_Blue_Moon_Trailer-1080p.mp4',
        type: 'video/mp4',
        size: 1080,
      },
      {
        src: 'https://cdn.plyr.io/static/demo/View_From_A_Blue_Moon_Trailer-1440p.mp4',
        type: 'video/mp4',
        size: 1440,
      },
    ];


    tracks = [
      {
        kind: 'captions',
        label: 'French',
        srclang: 'fr',
        src: 'https://cdn.plyr.io/static/demo/View_From_A_Blue_Moon_Trailer-HD.fr.vtt',
        default: true
      },
      {
        kind: 'captions',
        label: 'English',
        srclang: 'en',
        src: 'https://cdn.plyr.io/static/demo/View_From_A_Blue_Moon_Trailer-HD.en.vtt',
        default: false,
      }
    ];

    played(event: Plyr.PlyrEvent): void {
      console.log('played', event);
    }

    play(): void {
      this.player.play(); // or this.plyr.player.play()
    }

    pause(): void {
      this.player.pause(); // or this.plyr.player.play()
    }

    stop(): void {
      this.player.stop(); // or this.plyr.player.stop()
    }

    */

  constructor(private utilsService: UtilsService){

  }

  ngOnInit(): void {
    /*
    this.sources = [
      {
        src: 'https://cdn.plyr.io/static/demo/View_From_A_Blue_Moon_Trailer-720p.mp4'
      }
    ];

    this.tracks = [
      {
        kind: 'captions',
        label: 'French',
        srcLang: 'fr',
        src: 'https://cdn.plyr.io/static/demo/View_From_A_Blue_Moon_Trailer-HD.fr.vtt',
        default: true
      },
      {
        kind: 'captions',
        label: 'English',
        srcLang: 'en',
        src: 'https://cdn.plyr.io/static/demo/View_From_A_Blue_Moon_Trailer-HD.en.vtt',
        default: false,
      }
    ];

     */

    // this.poster = 'https://cdn.plyr.io/static/demo/View_From_A_Blue_Moon_Trailer-HD.jpg';
    // this.pdfLink = '/assets/data/devops.pdf';

    console.log('sources :', this.sources);
    console.log('transcriptions :', this.transcriptions);
    this.tracks = this.transcriptions && this.transcriptions.length > 0 ?
      this.transcriptions.map(t => t.Track) : null;
    console.log('tracks :', this.tracks);
    console.log('poster :', this.poster);
    console.log('pdfLink :', this.pdfLink);
    console.log('htmlText :', this.htmlText);

    if (this.autoPlay){
      this.play();
    }
  }


  played(event: Plyr.PlyrEvent): void {
    console.log('played', event);
  }

  afterLoadComplete(pdf): void {
    this.totalPages = pdf.numPages;
  }


  openLargePDF(e): void {
    e.preventDefault();
    this.utilsService.buildFullModal(PdfVideoEbookModalComponent, () => {}, true, {
      pdfLink: this.pdfLink,
      htmlText: this.htmlText
    } );
  }

  onProgress(ev: Plyr.PlyrEvent): void {
    this.onProgressChange.emit(ev);
  }

  onPause(ev: Plyr.PlyrEvent): void {
    this.onPauseChange.emit(ev);
    // console.log('on pause :', ev);
  }

  onEnded(ev: Plyr.PlyrEvent): void {
    this.onEndedChange.emit(ev);
  }


  play(): void {
   // this.player.play(); // or this.plyr.player.play()
    this.plyr.player.play();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (this.autoPlay){
      this.play();
    }
  }
}
