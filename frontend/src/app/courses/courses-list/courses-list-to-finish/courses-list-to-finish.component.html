<div class="" *ngIf="courseViews && courseViews.length > 0">
	<div class="row">
		<div class="col my-2">
			<span class="text-dark h5">
				{{ 'course.lastcourse' | translate }}
			</span>
			<!---<PERSON><PERSON> est la date à la quelle l'utilisateur à consultée pour la dernière fois le contenus-->
			<span class="small text-dark"
				>{{ courseViews[0].UpdatedAt | dateAgo }}
			</span>
		</div>
	</div>

	<hr />

	<div class="row">
		<div
			class="col-sm-12 col-lg-4 col-md-4"
			*ngFor="let courseView of courseViews"
		>
			<div class="d-flex justify-content-start align-items-center">
				<div class="img-tail">
					<img
						[src]="
							courseView.Content.Section.Course.CoverImage
								? courseView.Content.Section.Course.CoverImage.Hashname
								: cover
						"
						alt=""
						class="img"
					/>
				</div>
				<ul class="ml-2">
					<li class="text-dark course-to-finish-title">
						<a
							[routerLink]="
								'/courses/' + courseView.Content.Section.Course.Slug
							"
							class="text-decoration-none"
						>
							{{ courseView.Content.Section.Course.Title }}
						</a>
					</li>
					<li class="">
						{{
							'course.lastcourse' | translate: { position: courseView.Position }
						}}
					</li>
					<li class="text-muted small">{{ courseView.UpdatedAt | dateAgo }}</li>
				</ul>
			</div>
		</div>
		<div class="col-sm-12 col-lg-4 col-md-4">
			<a
				routerLink="/users/courses/bibiotheque"
				class="text-decoration-none text-dark"
			>
				{{ 'course.countshow' | translate: { count: countItem } }}
			</a>
		</div>
	</div>

	<hr />
</div>
