import { Component, Input, OnInit } from '@angular/core';
import { CourseViewService } from '~/app/services/courses/course-view.service';
import { CourseView } from '~/app/models/course-view.model';
import { User } from '~/app/models/user.model';
import { environment } from '~/environments/environment';
import { UtilsService } from '~/app/services/utils.service';
import { CourseService } from '~/app/services/courses/course.service';

@Component({
	selector: 'app-courses-list-to-finish',
	templateUrl: './courses-list-to-finish.component.html',
	styleUrls: ['./courses-list-to-finish.component.scss']
})
export class CoursesListToFinishComponent implements OnInit {
	countItem: number;
	courseViews: CourseView[];
	@Input()
	user: User;
	cover = '/assets/img/700x500.png';
	constructor(
		private courseViewService: CourseViewService,
		private utilsService: UtilsService,
		private courseService: CourseService
	) {}

	async ngOnInit(): Promise<void> {
		this.courseViewService
			.getAllViewCourseByUser(this.user?.Slug)
			.subscribe(async (res) => {
				if (res) {
					const sort = res?.sort((a, b) =>
						a.UpdatedAt > b.UpdatedAt ? -1 : 1
					);
					const unique: CourseView[] = [];

					for (const c of sort) {
						if (
							!(
								unique &&
								unique.find(
									(f) =>
										f.Content.Section.Course.Id === c.Content.Section.Course.Id
								)
							)
						) {
							unique.push(c);
						}
					}

					this.countItem = unique.length;

					let items = unique?.slice(0, 2);
					const x: CourseView[] = [];
					for (const a of items) {
						a.Content.Section.Course = await this.courseService
							.getBySlug(a.Content.Section.Course.Slug)
							.toPromise();
						x.push(a);
					}
					items = x?.map((f) => {
						if (f.Content.Section.Course.CoverImage) {
							f.Content.Section.Course.CoverImage.Hashname = `${environment.path}/${f.Content.Section.Course.CoverImage.Hashname}`;
						} else {
							//f.Content.Section.Course.CoverImage.Hashname = this.cover;
						}
						const progress = this.courseViewService.getCourseLearningPercent(
							res,
							f.Content.Section.Course
						);
						if (progress && progress.Progress > 0) {
							const minutes = progress.Total - progress.Progress;
							f.Position = this.utilsService.getRithTime(minutes);
						}
						if (f.Content.Section.Course.Title) {
							f.Content.Section.Course.Title = this.utilsService.getHtml(
								f.Content.Section.Course.Title,
								100
							);
						}
						return f;
					});
					this.courseViews = items;
				}
			});
	}
}
