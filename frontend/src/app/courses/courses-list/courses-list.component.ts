import { ActivatedRoute } from '@angular/router';
import {
	Component,
	EventEmitter,
	Input,
	OnDestroy,
	OnInit,
	Output
} from '@angular/core';
import {
	Course,
	CourseSearch,
	CourseSearchResult,
	OrderbyType
} from '~/app/models/course.model';
import { UtilsService } from '~/app/services/utils.service';
import { User } from '~/app/models/user.model';
import { UserService } from '~/app/services/users/user.service';
import { CourseService } from '~/app/services/courses/course.service';
import { Subscription } from 'rxjs';

@Component({
	selector: 'app-courses-list',
	templateUrl: './courses-list.component.html',
	styleUrls: ['./courses-list.component.scss']
})
export class CoursesListComponent implements OnInit, OnDestroy {
	orderBy = OrderbyType;

	courseSearchResultSubcription = new Subscription();

	@Input()
	onSearch: Function;

	@Input()
	loadingSearch: boolean;

	@Input()
	queryPage: number;

	@Input()
	search: CourseSearch;
	@Output()
	searchChange: EventEmitter<CourseSearch> = new EventEmitter<CourseSearch>();

	@Output()
	courseMoreEmitter: EventEmitter<string> = new EventEmitter<string>();

	@Output()
	courseLoadFromPaginateEmitter: EventEmitter<number> = new EventEmitter<number>();

	@Input() courseSearchResult: CourseSearchResult;
	@Output()
	courseSearchResultChange: EventEmitter<CourseSearchResult> = new EventEmitter<CourseSearchResult>();
	@Input() loadMore: boolean;
	showMenu = false;

	@Input()
	skip = 0;
	@Output()
	skipChange: EventEmitter<number> = new EventEmitter<number>();
	@Input()
	take: number;
	@Output()
	takeChange: EventEmitter<number> = new EventEmitter<number>();
	@Input()
	user: User;

	showFilterMenu = false;
	@Input() paginateNumberList = [];
	@Input() totalRecord = 0;

	paginateNumberSelected = 1;

	isloadingSearchCourse = false;

	constructor(
		private utilsService: UtilsService,
		private courseService: CourseService
	) {}

	async ngOnInit(): Promise<void> {
		this.courseSearchResultSubcription = this.courseService.courseSearchResultSubject.subscribe(
			(res) => {
				this.courseSearchResult = res;
			}
		);
	}

	openMenu(e): void {
		// e.preventDefault();
		// this.showMenu = !this.showMenu;
		// if (this.showMenu) {
		// 	document.getElementById('filter-sidebar').style.width = '0';
		// } else {
		// 	document.getElementById('filter-sidebar').style.width = '320px';
		// }
		this.showFilterMenu = true;
	}

	onLoadMore(e): void {
		e.preventDefault();
		this.courseMoreEmitter.emit(this.utilsService.getUniqueId());
	}

	onLoadFromPage(page: number): void {
		window.scroll(0, 700);
		this.courseLoadFromPaginateEmitter.emit(page + 1);
		this.paginateNumberSelected = page;
	}

	onOrder(e, order: OrderbyType): void {
		e.preventDefault();
		this.search.Orderby = order;
		this.searchChange.emit(this.search);
		e.preventDefault();

		this.courseService
			.getByFilter({ take: this.take, skip: this.skip }, this.search)
			.subscribe((res) => {
				// console.log('the search result :', res);
				this.courseSearchResult = res;
				this.courseSearchResultChange.emit(this.courseSearchResult);
			});
	}

	onCourseLoadingListener(isLoading) {
		this.isloadingSearchCourse = isLoading;
	}

	onSearchCourse(s) {
		window.scroll(0, 700);
		this.onSearch('FROM_PAGINATE', true);
		this.paginateNumberSelected = 1;
	}

	ngOnDestroy(): void {
		this.courseSearchResultSubcription.unsubscribe();
	}
}
