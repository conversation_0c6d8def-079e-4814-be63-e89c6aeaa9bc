@media screen and (max-width: 767px) {
	.bm-y2 {
		margin-top: 10px !important;
	}
}
@media screen and (min-width: 767px) and (max-width: 1200px) {
	.noShow-mb {
		display: none;
	}
}

/*
Scroll content
 */

:root {
	--gutter: 20px;
}

.app {
	padding: var(--gutter) 0;
	display: grid;
	grid-gap: var(--gutter) 0;
	grid-template-columns: var(--gutter) 1fr var(--gutter);
	align-content: start;
}

.app > * {
	grid-column: 2 / -2;
}

.app > .full {
	grid-column: 1 / -1;
}

.hs {
	display: grid;
	//grid-gap: calc(var(--gutter) / 2);
	//grid-template-columns: 10px;
	//grid-template-rows: minmax(150px, 1fr);
	grid-auto-flow: column;
	//grid-auto-columns: calc(50% - var(--gutter) * 2);

	overflow-x: scroll;
	scroll-snap-type: x proximity;
	//padding-bottom: calc(.75 * var(--gutter));
	//margin-bottom: calc(-.25 * var(--gutter));
}

.hs:before,
.hs:after {
	content: '';
	//width: 10px;
}

/* Demo styles */

ul {
	list-style: none;
	padding: 0;
}

h1,
h2,
h3 {
	margin: 0;
}

.app {
	width: 100%;
	//height: 667px;
	//background: #DBD0BC;
	overflow-y: scroll;
}

.hs > li,
.item {
	scroll-snap-align: center;
	//padding: calc(var(--gutter) / 2 * 1.5);
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	width: 343px;
	margin: 12px;
}

.no-scrollbar {
	//scrollbar-width: none;
	margin-bottom: 0;
	padding-bottom: 0;
}
.no-scrollbar::-webkit-scrollbar {
	display: none;
}
