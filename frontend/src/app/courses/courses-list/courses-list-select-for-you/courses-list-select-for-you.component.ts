import { Component, Input, OnInit } from '@angular/core';
import { CourseService } from '~/app/services/courses/course.service';
import { Course } from '~/app/models/course.model';
import { User } from '~/app/models/user.model';
import { environment } from '~/environments/environment';
import { UtilsService } from '~/app/services/utils.service';

@Component({
	selector: 'app-courses-list-select-for-you',
	templateUrl: './courses-list-select-for-you.component.html',
	styleUrls: ['./courses-list-select-for-you.component.scss']
})
export class CoursesListSelectForYouComponent implements OnInit {
	TAKE = environment.paginationTake.COURSES_FOR_YOU_TAKE;

	courses: Course[];
	@Input()
	user: User;
	cover = '/assets/img/700x500.png';
	constructor(
		private courseService: CourseService,
		private utilsService: UtilsService
	) {}

	async ngOnInit(): Promise<void> {
		/* console.log(
			'CoursesListSelectForYouComponent ====> ',
			this.user,
			this.user.Categories,
			this.user.Categories.length
		); */
		if (this.user && this.user.Categories && this.user.Categories.length > 0) {
			this.courseService
				.getAllRecommandedCourseToUser(this.user.Slug, this.TAKE)
				.subscribe((res) => {
					if (res) {
						this.courses = res.map((f) => {
							if (f.CoverImage) {
								f.CoverImage.Hashname = `${environment.path}/${f.CoverImage.Hashname}`;
							} else {
								//f.CoverImage.Hashname = this.cover;
							}

							if (f.Title) {
								f.Title = this.utilsService.getHtml(f.Title, 100);
							}

							f.Duration = this.utilsService.getCourseTime(f);
							return f;
						});
					}
				});
		}
	}
}
