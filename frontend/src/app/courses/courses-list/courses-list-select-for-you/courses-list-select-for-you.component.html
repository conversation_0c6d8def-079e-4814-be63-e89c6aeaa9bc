<div class="row" *ngIf="user && courses">
	<div class="col my-2">
		<span class="text-dark h5">
			{{ 'course.selectforuser' | translate: { name: user.Firstname } }}
		</span>
		<div class="app mt-3">
			<div class="hs full no-scrollbar">
				<span class="item ml-2 mx-2 text-dark" *ngFor="let course of courses">
					<div class="card mx-2">
						<a [routerLink]="'/courses/' + course.Slug" class="">
							<img
								class="card-img-top"
								[src]="course.CoverImage ? course.CoverImage.Hashname : cover"
								alt="Card image cap"
							/>
						</a>
						<div class="card-body">
							<span class="card-title h6">
								<a [routerLink]="'/courses/' + course.Slug" class="">
									{{ course.Title }}
								</a>
							</span>
							<div
								class="d-flex justify-content-between align-items-center my-2"
							>
								<i class="far fa-play-circle text-primary"></i>
								<span class="badge badge-success">Success</span>
							</div>
							<div class="">
								<span class=""
									>{{ 'admin.courses.by' | translate }}
									{{ course.CreatedBy.Firstname }}</span
								>
							</div>

							<div class="d-flex justify-content-between align-items-center">
								<span class="small">{{ course.UpdatedAt | dateAgo }}</span>
								<span class="badge badge-dark">{{ course.Duration }}</span>
							</div>
						</div>
					</div>
				</span>
			</div>
		</div>
	</div>
</div>
