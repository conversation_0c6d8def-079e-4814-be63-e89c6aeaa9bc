.app {
	//width: 100%;
	//height: 667px;
	// background: #ffffff;
	//overflow-y: scroll;
	// margin-right: 5px;
}

.hs {
	display: grid;
	grid-gap: calc(var(--gutter) / 2);
	grid-auto-flow: column;
	grid-auto-columns: calc(50% - var(--gutter) * 2);
	overflow-x: scroll;
	scroll-snap-type: x proximity;
	/* display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	overflow-x: scroll;
	scroll-snap-type: x proximity; */
}

.hs > li,
.item {
	scroll-snap-align: center;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	width: 23em;

	& .card-title {
		& a {
			color: black;
			&:hover {
				color: #da0b4e;
			}
		}
	}
}

.no-scrollbar {
	scrollbar-width: none;
	margin-bottom: 0;
	padding-bottom: 0;
}
.no-scrollbar::-webkit-scrollbar {
	display: none;
}

.card-img-top {
	max-width: 100% !important;
}
