<!-- ============================ Full Width Courses  ================================== -->

<section class="pt-0 filter_section">
	<div class="container">
		<!-- Onclick Sidebar -->

		<!-- Row -->
		<div class="row">
			<div class="col-lg-12 col-md-12 col-sm-12">
				<div class="row my-3">
					<div class="col-sm-12 col-md-3 col-lg-3 text-dark">
						<!--span>
              We found <strong>{{courseSearchResult.Courses? courseSearchResult.Count : 0}}</strong> courses for you
            </span-->
					</div>

					<div class="col-sm-12 col-md-3 col-lg-3 offset-md-4 bm-y2">
						<div class="dropdown">
							<a
								class="btn btn-custom dropdown-toggle"
								href="#"
								role="button"
								data-toggle="dropdown"
								aria-haspopup="true"
								aria-expanded="false"
							>
								{{ 'course.recentCourses' | translate }}
							</a>
							<div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
								<a
									class="dropdown-item"
									href=""
									(click)="onOrder($event, orderBy.RECENT)"
									>{{ 'course.recentCourses' | translate }}</a
								>
								<a
									class="dropdown-item"
									href=""
									(click)="onOrder($event, orderBy.LAST)"
									>{{ 'course.oldCourses' | translate }}</a
								>
							</div>
						</div>
					</div>
					<div class="col-sm-12 col-md-2 col-lg-2 bm-y2">
						<button
							type="button"
							class="btn btn-primary btn-lg btn-block"
							(click)="openMenu($event)"
							id="open2"
						>
							<!-- <span class="noShow-mb">Show</span> Filter -->
							{{ 'course.details.filter' | translate }}
						</button>
					</div>
				</div>

				<!-- Dernière consultation -->
				<div class="" *ngIf="user">
					<app-courses-list-to-finish
						[user]="user"
					></app-courses-list-to-finish>
				</div>

				<!--  end Dernière consultation -->
				<div class="" *ngIf="user">
					<app-courses-list-select-for-you
						[user]="user"
					></app-courses-list-select-for-you>
				</div>

				<div class="row">
					<div class="col my-2">
						<span class="text-dark h5" *ngIf="!search.Orderby">
							{{ 'course.allCourses' | translate }}
						</span>
						<span class="text-dark h5" *ngIf="search.Orderby">
							{{ 'course.searchOrderby' + search.Orderby | translate }}
						</span>
					</div>
				</div>

				<div
					class="row"
					*ngIf="
						!loadingSearch &&
						courseSearchResult &&
						courseSearchResult.Courses?.length > 0
					"
				>
					<!-- Cource Grid 1 -->
					<div
						class="col-lg-4 col-md-6"
						*ngFor="let course of courseSearchResult.Courses; let i = index"
					>
						<app-course-item
							[course]="course"
							[position]="i + 1"
						></app-course-item>
					</div>
				</div>

				<div *ngIf="loadingSearch" class="text-center">
					<img src="assets/img/forum_loaging.gif" alt="" />
				</div>

				<div
					class="d-flex listOfCourse justify-content-center align-items-center"
					*ngIf="
						!loadingSearch &&
						courseSearchResult &&
						courseSearchResult.Courses?.length === 0
					"
				>
					<div class="h2 font-bold">
						{{ 'course.details.noCourse' | translate }}
					</div>
				</div>

				<!-- Row -->
				<div
					class="row"
					*ngIf="courseSearchResult && courseSearchResult.Courses?.length > 0"
				>
					<!-- Pagination -->
					<div
						class="col-lg-12 col-md-12 col-sm-12 text-center"
						*ngIf="loadMore"
						[disabled]="loadingSearch"
					>
						<!-- <button
									type="button"
									class="btn btn-loader"
									(click)="onLoadMore($event)"
								>
									{{ 'home.page.loadMore' | translate
									}}<i class="ti-reload ml-3"></i>
								</button> -->
					</div>

					<div class="col-lg-12 col-md-12 col-sm-12 text-center">
						<p-paginator
							[rows]="take"
							[totalRecords]="totalRecord"
							(onPageChange)="onLoadFromPage($event.page)"
						></p-paginator>
					</div>
				</div>
				<!-- /Row -->
			</div>
		</div>
		<!-- Row -->
	</div>
	<p-sidebar [(visible)]="showFilterMenu" [baseZIndex]="10000">
		<h4>{{ 'course.details.filter' | translate }}</h4>
		<div id="" class="mt-3">
			<div class="filt-head">
				<h4 class="filt-first">{{ 'course.advancedOptions' | translate }}</h4>
				<!-- <a href="javascript:void(0)" class="closebtn" (click)="openMenu($event)">Close <i class="ti-close"></i></a> -->
			</div>
			<div class="show-hide-sidebar">
				<!-- Find New Property -->
				<div class="sidebar-widgets">
					<app-courses-search
						[(courseSearchResult)]="courseSearchResult"
						[(skip)]="skip"
						[(take)]="take"
						[(search)]="search"
						(courseSearchLoadingEmitter)="onCourseLoadingListener($event)"
						(searchCourse)="onSearchCourse($event)"
						[isLoading]="loadingSearch"
					></app-courses-search>
				</div>
			</div>
		</div>
	</p-sidebar>
</section>

<!-- ============================ Full Width Courses End ================================== -->
