<div id="main-wrapper" #mainWrapper>
	<app-home-header></app-home-header>

	<section class="p-0">
		<!-- ============================ Page Title Start================================== -->
		<div class="row">
			<div class="col-lg-12 col-sm-12 col-md-12">
				<div class="header1">
					<div
						class="title p-2 title-height-100 d-flex flex-column align-items-center justify-content-center"
					>
						<!-- <img src="../../assets/img/bm-course-img.png" alt="" srcset="" style="position: fixed;height:100%;width:100%;top:0"> -->
						<div class="h2 text-white">
							{{ 'course.details.coursesTitle' | translate }}
						</div>
						<p class="h5 text-white">
							{{ 'course.details.coursesDesc' | translate }}
						</p>
						<!--a class="btn btn-dark btn-lg" routerLink="/courses/create">Commencer maintenant</a-->
					</div>
				</div>
			</div>
		</div>
		<!-- ============================ Page Title End ================================== -->

		<div class="container">
			<div class="d-flex align-items-center">
				<a class="btn btn-link ml-0 pl-0 mr-1" style="text-decoration: none">
					<span class="text-dark"
						>{{ 'course.details.viewMode' | translate }} :</span
					>
				</a>

				<button
					type="button"
					class="btn btn-text mr-1"
					(click)="coursesModeList = true"
					*ngIf="!coursesModeList"
				>
					<span class="text-dark">
						<i class="fa fa-list"></i>
						{{ 'course.details.list' | translate }}
					</span>
				</button>
				<button
					type="button"
					class="btn btn-text mr-1"
					href=""
					(click)="coursesModeList = false"
					*ngIf="coursesModeList"
				>
					<span class="text-dark">
						<i class="fa fa-th"></i>
						{{ 'course.details.grid' | translate }}
					</span>
				</button>
			</div>
		</div>
		<div class="">
			<!-- <div
				class="d-flex listOfCourse justify-content-center align-items-center"
				*ngIf="
					(!courseSearchResult || courseSearchResult.Courses?.length === 0) &&
					!isLoading
				"
			>
				<div class="h2 font-bold">
					{{ 'course.details.noCourse' | translate }}
				</div>
			</div> -->

			<!-- <div
				class="d-flex listOfCourse justify-content-center align-items-center"
				*ngIf="isLoading"
			>
				<div class="h2 font-bold">LOADING...</div>
			</div> -->
			<app-courses-list
				[user]="user"
				[paginateNumberList]="paginateNumberList"
				[totalRecord]="totalRecord"
				[loadMore]="loadMore"
				[(courseSearchResult)]="courseSearchResult"
				[(skip)]="skip"
				[(take)]="take"
				[(search)]="search"
				[onSearch]="onSearch"
				(courseMoreEmitter)="onCourseMoreListener($event)"
				(courseLoadFromPaginateEmitter)="
					onCourseLoadFromPaginateListener($event)
				"
				[loadingSearch]="isLoadingFromPaginate || isLoading"
				[queryPage]="page"
				*ngIf="coursesModeList"
			></app-courses-list>
			<app-courses-grid
				[loadMore]="loadMore"
				[paginateNumberList]="paginateNumberList"
				[totalRecord]="totalRecord"
				[(courseSearchResult)]="courseSearchResult"
				[(skip)]="skip"
				[(take)]="take"
				[(search)]="search"
				[onSearch]="onSearch"
				(courseMoreEmitter)="onCourseMoreListener($event)"
				(courseLoadFromPaginateEmitter)="
					onCourseLoadFromPaginateListener($event)
				"
				[loadingSearch]="isLoadingFromPaginate || isLoading"
				[queryPage]="page"
				*ngIf="!coursesModeList"
			></app-courses-grid>
		</div>
	</section>
	<!-- ============================ Footer Start ================================== -->
	<app-home-footer></app-home-footer>
	<!-- ============================ Footer End ================================== -->
</div>
