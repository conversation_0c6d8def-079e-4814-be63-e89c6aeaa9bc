import { Component, Input, OnInit } from '@angular/core';
import { Course } from '~/app/models/course.model';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from '~/environments/environment';
import {
	CourseOpinion,
	VIEW_TYPE,
	LIKE_TYPE,
	DISLIKE_TYPE,
	LOVE_TYPE
} from '~/app/models/course-opinion.model';
import { User, UserRoleType } from '~/app/models/user.model';
import { UserService } from '~/app/services/users/user.service';
import { CourseRating } from '~/app/models/course-rating.model';
import { ContentType } from '~/app/models/course.model';
import { BRAINMAKERBASKET } from '~/app/models/order.model';
import { CourseView } from '~/app/models/course-view.model';
import { TranslateService } from '@ngx-translate/core';
import { UtilsService } from '~/app/services/utils.service';
import { CourseService } from '~/app/services/courses/course.service';
import { OrderService } from '~/app/services/orders/order.service';
import { CourseOpinionService } from '~/app/services/courses/course-opinion.service';
import { CourseRatingService } from '~/app/services/courses/course-rating.service';
import { CourseViewService } from '~/app/services/courses/course-view.service';
import { HomeLoginComponent } from '~/app/home/<USER>/home-login.component';
import { TranslationService } from '~/app/services/translate/translation.service';
import { AbonnementsService } from '~/app/services/abonnements/abonnements.service';
import {
	Abonnement,
	AbonnementStatus
} from '~/app/models/abonnements/abonnement.model';

@Component({
	selector: 'app-courses-detail',
	templateUrl: './courses-detail.component.html',
	styleUrls: ['./courses-detail.component.scss']
})
export class CoursesDetailComponent implements OnInit {
	@Input() userIsNotConnected: boolean;
	user: User;
	cover = '/assets/img/1920x650.png';
	course: Course;
	contentType = ContentType;

	isLiked: boolean;
	isDisLiked: boolean;
	isLoved: boolean;

	viewCount = 0;
	likeCount = 0;
	disLikeCount = 0;
	loveCount = 0;

	globalRating = 0;
	userRating = 0;

	pourcentVote1 = 0;
	courseViewsCount = 0;
	pourcentVote3 = 0;
	pourcentVote4 = 0;
	pourcentVote2 = 0;
	basket: Array<{ Id: string; Type: string }> = [];
	userOpinion: CourseOpinion;
	isPay: boolean;
	buyNow: boolean;

	courseDuration: string;
	courseFollowersUsers: User[] = [];
	instructorAllCourses: Course[] = [];

	userAbonnement: Abonnement;
	userAbonnementExpired = false;
	userAbonnementActive = false;

	constructor(
		private courseService: CourseService,
		private orderService: OrderService,
		private activedRoute: ActivatedRoute,
		private opinionService: CourseOpinionService,
		private userService: UserService,
		private courseRatingService: CourseRatingService,
		private courseViewService: CourseViewService,
		private translateService: TranslateService,
		private utilService: UtilsService,
		private translationService: TranslationService,
		private router: Router,
		private abonnementService: AbonnementsService
	) {}

	async ngOnInit(): Promise<void> {
		// this.courseRatingService.ratingSubject.subscribe((rating: number) => {
		//   // console.log('Rating value: ', rating);
		//   this.userRating = rating;
		// });

		// this.courseRatingService.globalRatingSubject.subscribe(
		//   (globalRating: number) => {
		//     // console.log('Global Rating value: ', globalRating);
		//     this.globalRating = globalRating;
		//     this.generateCourseRatingHistory();
		//     this.ratingLoaded = true;
		//   }
		// );

		this.course = await this.courseService
			.getBySlug(this.activedRoute.snapshot.params.id)
			.toPromise();
		// console.log(this.course);
		if (this.course.CoverImage) {
			this.cover = `${environment.path}/${this.course.CoverImage.Hashname}`;
		}

		const items = localStorage.getItem(BRAINMAKERBASKET);
		if (items) {
			this.basket = JSON.parse(items);
			if (this.basket.find((b) => b.Id === this.course.Slug)) {
				this.buyNow = true;
			}
		}

		this.formatCourseDuration();

		// console.log('course', this.course);

		// console.log(this.comment);
		/*
		 * Create new opinion with type VIEW_TYPE and store the opinion,
		 * to tell that user have see post
		 */
		if (this.userService.isConnected()) {
			this.user = await this.userService.getUserConnected().toPromise();
			// console.log('User', this.user);

			const order = await this.orderService
				.getByCoursesSlugAndUserSug(this.course.Slug, this.user.Slug)
				.toPromise();
			this.isPay =
				(this.user && this.user.Role === UserRoleType.ADMIN) ||
				// !!(order && order.Id) ||
				(order != null &&
					order.Courses.length > 0 &&
					!!order.Courses.find(
						(c: Course): boolean => c.Slug === this.course.Slug
					)) ||
				+this.course.CreatedBy.Id === +this.user.Id;

			console.log(
				'order',
				order,
				'isPay',
				this.isPay,
				'origion',
				order != null &&
					order.Courses.length > 0 &&
					!!order.Courses.find(
						(c: Course): boolean => c.Slug === this.course.Slug
					)
			);

			if (this.isPay) {
				this.buyNow = false;
			}

			// if (!this.isPay) {
			// 	const orders = await this.orderService
			// 		.getByUserHaveAbonnement(this.user.Slug)
			// 		.toPromise();

			// 	console.log('User Order', orders);
			// 	this.isPay = !!orders.find((f) => +f.User.Id === +this.user.Id);
			// 	// console.log(orders, this.isPay, this.buyNow);
			// 	if (this.isPay) {
			// 		this.buyNow = false;
			// 		// this.router.navigate(['/courses', 'reader', this.course.Slug]);
			// 	}
			// } else {
			// 	this.buyNow = false;
			// 	// this.router.navigate(['/courses', 'reader', this.course.Slug]);
			// }

			const currentUserHasOpinion = (await this.opinionService
				.getCurrentUserCourseOpinion(this.user, this.course)
				.toPromise()) as CourseOpinion;

			if (currentUserHasOpinion) {
				this.userOpinion = currentUserHasOpinion;
			} else {
				const newOpinion = new CourseOpinion();
				newOpinion.User = this.user;
				newOpinion.Course = this.course;
				newOpinion.Type = VIEW_TYPE;

				this.userOpinion = (await this.opinionService
					.add(newOpinion)
					.toPromise()) as CourseOpinion;
			}
		}

		if (!this.user) {
			this.userIsNotConnected = true;
		} else {
			this.instructorAllCourses = await this.courseService
				.getByUser(this.user.Slug)
				.toPromise();
			// console.log('InstructorCourse', this.instructorAllCourses);
		}
		/**
		 * Check if user like current comment
		 */
		// console.log(this.userOpinion);
		if (this.userOpinion && this.userOpinion.Type === LIKE_TYPE) {
			this.like();
		} else if (this.userOpinion && this.userOpinion.Type === DISLIKE_TYPE) {
			this.disLike();
		} else if (this.userOpinion && this.userOpinion.Type === LOVE_TYPE) {
			this.love();
		}

		this.courseViewsCount = await this.courseService
			.countCourseViewsByCourse(this.course.Slug)
			.toPromise();

		// console.log('****', this.courseViews);
		this.courseFollowersUsers = this.utilService.getCourseCountViews(
			this.course
		);
		// console.log('Count Followrs', this.courseFollowersUsers);

		// console.log('Course views', this.courseViews);

		this.countCourseOpinions();
		this.generateCourseRatingHistory();
		// console.log('views: ', this.viewCount);
		// console.log(this.course);

		if (this.user) {
			if (!this.isPay) {
				this.userAbonnement = await this.abonnementService
					.getUserActiveAbonnement(this.user.Slug)
					.toPromise();
				this.userAbonnementExpired =
					this.userAbonnement?.Status === AbonnementStatus.EXPIRED ||
					new Date(this.userAbonnement?.Expire).setHours(0, 0, 0, 0) <=
						new Date().setHours(0, 0, 0, 0);

				if (
					new Date(this.userAbonnement?.Expire).setHours(0, 0, 0, 0) <=
					new Date().setHours(0, 0, 0, 0)
				) {
					this.userAbonnement.Status = AbonnementStatus.EXPIRED;
				}

				this.userAbonnementActive =
					this.userAbonnement?.Status === AbonnementStatus.ACTIVE &&
					new Date().setHours(0, 0, 0, 0) <=
						new Date(this.userAbonnement?.Expire).setHours(0, 0, 0, 0);

				console.log(
					'active: ',
					this.userAbonnementActive,
					'Expired: ',
					this.userAbonnementExpired
				);
			} else {
				this.buyNow = false;
			}
		}
	}

	openInNewTab(url: string): void {
		window.open(url, '_blank');
	}

	handleCourseRating(rating): void {
		// console.log('Rating', rating);
		this.userRating = rating;
		this.generateCourseRatingHistory();
	}

	handleCourseGlobalRating(rating): void {
		// console.log('Global rating', rating);
		this.globalRating = rating;
	}

	getInstructorProfileImage(): string {
		if (this.course.CreatedBy && this.course.CreatedBy.Photo) {
			return `${environment.path}/${this.course.CreatedBy.Photo.Hashname}`;
		}
		return '/assets/img/avatar.png';
	}

	async formatCourseDuration(): Promise<void> {
		// console.log('Browser lang', this.translateService.getBrowserLang());
		// console.log('Avant', this.course.Duration);
		// this.translateService.getBrowserLang()
		//  this.translateService.getBrowserLang()
		this.courseDuration = this.utilService.getRithTime(
			this.course.Duration,
			this.translationService.getLanguage()
		);
		// console.log('Après', this.courseDuration);
		// console.log('Course', this.course);
	}

	async generateCourseRatingHistory(): Promise<void> {
		const ratings = (await this.courseRatingService
			.getCourseRate(this.course)
			.toPromise()) as CourseRating[];
		// tslint:disable-next-line:variable-name
		let vote_1 = 0;
		// tslint:disable-next-line:variable-name
		let vote_3 = 0;
		// tslint:disable-next-line:variable-name
		let vote_4 = 0;
		// tslint:disable-next-line:variable-name
		let vote_2 = 0;

		ratings.map((courseRating: CourseRating) => {
			if (courseRating.Rating === 1) {
				++vote_1;
			}
			if (courseRating.Rating === 3) {
				++vote_3;
			}
			if (courseRating.Rating === 4) {
				++vote_4;
			}
			if (courseRating.Rating === 2) {
				++vote_2;
			}
		});

		this.pourcentVote1 = Math.round((vote_1 / ratings.length) * 100);
		this.pourcentVote3 = Math.round((vote_3 / ratings.length) * 100);
		this.pourcentVote4 = Math.round((vote_4 / ratings.length) * 100);
		this.pourcentVote2 = Math.round((vote_2 / ratings.length) * 100);
	}

	// Get all instructor content count
	getCountContent(): number {
		return !this.course ||
			!this.course.Sections ||
			(this.course.Sections && this.course.Sections.length === 0)
			? 0
			: this.course.Sections.map((s) =>
					s.Contents ? s.Contents.length : 0
			  ).reduce((sum, current) => sum + current, 0);
	}

	/**
	 * Event onClick on like button
	 * if user liked update opinion and set VIEW_TYPE
	 * else set LIKE_TYPE
	 */
	async likeComment(): Promise<void> {
		// alert('like comment');
		if (this.userIsNotConnected) {
			return;
		}
		if (!this.userOpinion) {
			const opinion = new CourseOpinion();
			opinion.Type = LIKE_TYPE;
			opinion.Course = this.course;
			// opinion.Post = null;
			opinion.User = this.user;
			// opinion.Type = LIKE_TYPE;
			this.userOpinion = (await this.opinionService
				.add(opinion)
				.toPromise()) as CourseOpinion;
			this.like();
			this.countCourseOpinions();
			return;
		}
		if (this.userOpinion && this.isLiked) {
			await this.opinionService
				.update(this.userOpinion.Slug, VIEW_TYPE)
				.toPromise();
			this.clearLike();
		} else if (this.userOpinion) {
			await this.opinionService
				.update(this.userOpinion.Slug, LIKE_TYPE)
				.toPromise();
			this.like();
		}

		// console.log('user opinion', this.userOpinion);
		await this.countCourseOpinions();
	}

	/**
	 * Event onClick on dislike button
	 * if user disliked, update opinion and set VIEW_TYPE
	 * else set DISLIKE_TYPE
	 */
	async disLikeComment(): Promise<void> {
		if (this.userIsNotConnected) {
			return;
		}

		if (!this.userOpinion) {
			const opinion = new CourseOpinion();
			opinion.Course = this.course;
			// opinion.Post = null;
			opinion.User = this.user;
			opinion.Type = DISLIKE_TYPE;
			this.userOpinion = (await this.opinionService
				.add(opinion)
				.toPromise()) as CourseOpinion;
			this.disLike();
			this.countCourseOpinions();
			return;
		}

		if (this.userOpinion && this.isDisLiked) {
			await this.opinionService
				.update(this.userOpinion.Slug, VIEW_TYPE)
				.toPromise();
			this.clearDisLike();
		} else if (this.userOpinion) {
			await this.opinionService
				.update(this.userOpinion.Slug, DISLIKE_TYPE)
				.toPromise();
			this.disLike();
		}
		await this.countCourseOpinions();
	}

	/**
	 * Event onClick on love button
	 * if user love, update opinion and set VIEW_TYPE
	 * else set LOVE_TYPE
	 */
	async loveComment(): Promise<void> {
		if (this.userIsNotConnected) {
			return;
		}

		if (!this.userOpinion) {
			const opinion = new CourseOpinion();
			opinion.Course = this.course;
			// opinion.Post = null;
			opinion.User = this.user;
			opinion.Type = LOVE_TYPE;
			this.userOpinion = (await this.opinionService
				.add(opinion)
				.toPromise()) as CourseOpinion;
			this.love();
			this.countCourseOpinions();
			return;
		}

		if (this.userOpinion && this.isLoved) {
			await this.opinionService
				.update(this.userOpinion.Slug, VIEW_TYPE)
				.toPromise();
			this.clearLove();
		} else if (this.userOpinion) {
			await this.opinionService
				.update(this.userOpinion.Slug, LOVE_TYPE)
				.toPromise();
			this.love();
		}
		await this.countCourseOpinions();
	}

	/**
	 * Change likes icons in template
	 * active like icon and disabled dislike icon
	 */
	like(): void {
		this.isLiked = true;
		this.isDisLiked = false;
		this.isLoved = false;
	}

	/**
	 * Change likes icons,
	 * active dislike icon and disabled like icon
	 */
	disLike(): void {
		this.isLiked = false;
		this.isDisLiked = true;
		this.isLoved = false;
	}

	love(): void {
		this.isLoved = true;
		this.isLiked = false;
		this.isDisLiked = false;
	}

	/**
	 * Disabled like icon when user cancel like opinion
	 */
	clearLike(): void {
		this.isLiked = false;
	}

	/**
	 * Disabled dislike icon when user cancel dislike opinion
	 */
	clearDisLike(): void {
		this.isDisLiked = false;
	}

	clearLove(): void {
		this.isLoved = false;
	}

	/*
	 * Get all course opinions and count view, likes and dislikes
	 */
	async countCourseOpinions(): Promise<void> {
		let viewCount = 0;
		let likeCount = 0;
		let disLikeCount = 0;
		let loveCount = 0;
		const commentOpinions = (await this.opinionService
			.getAllCourseOpinions(this.course)
			.toPromise()) as CourseOpinion[];
		commentOpinions.map((opinion) => {
			if (opinion.Type === VIEW_TYPE) {
				++viewCount;
			}
			if (opinion.Type === LIKE_TYPE) {
				++likeCount;
			}
			if (opinion.Type === DISLIKE_TYPE) {
				++disLikeCount;
			}
			if (opinion.Type === LOVE_TYPE) {
				++loveCount;
			}
		});

		this.likeCount = likeCount;
		this.disLikeCount = disLikeCount;
		this.loveCount = loveCount;
		this.viewCount = viewCount + likeCount + disLikeCount + loveCount;
	}

	addToCard(e): void {
		e.preventDefault();
		if (this.basket.find((b) => b.Id === this.course.Slug)) {
			this.buyNow = true;
		} else {
			const elt = { Id: this.course.Slug, Type: 'Course' };
			this.basket.push(elt);
			localStorage.setItem(BRAINMAKERBASKET, JSON.stringify(this.basket));
			this.buyNow = true;

			this.orderService.addToBasket(this.course);
		}
	}

	onBuy($event: MouseEvent): void {
		if (this.userService.isConnected()) {
			this.router.navigate(['/pay']);
		} else {
			this.openLoginModal();
			return;
		}
	}

	openLoginModal(): void {
		const dialogRef = this.utilService.buildModal(HomeLoginComponent, () => {
			this.router.navigate(['/pay']);
		});
	}

	backToTop2() {
		document.body.scrollTop = 0;
		document.documentElement.scrollTop = 0;
	}
}
