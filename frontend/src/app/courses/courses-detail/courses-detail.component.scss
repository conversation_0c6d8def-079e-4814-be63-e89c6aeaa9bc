@import '../../../assets/scss/variables';

.reviews-comments-wrap {
	width: 100%;
}

.list-single-main-item-title.fl-wrap {
	h3 {
		font-size: 20px;
	}
}

.course-opinions {
	display: flex;
	justify-content: flex-start;
	align-items: center;
	padding-top: 0.5rem;
	margin-right: 1rem;
	span {
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 1.2rem;
		margin: 0 0.1rem;
		cursor: pointer;
		color: #ccc;
		strong {
			color: #da0b4e;
			margin: 0 0.2rem;
			font-size: 0.9rem;
		}
	}
}

.ed_detail_head {
	min-height: 40vh;
	display: flex;
	align-items: center;
}

.course-opinions.static-op {
	span {
		color: #fff;
		strong {
			color: #fff;
		}
	}
}

.material-icons.disabled {
	color: #da0b4e !important;
}

.material-icons.active {
	color: #da0b4e !important;
}

.star_info {
	svg.filled {
		color: #ffc107;
	}
}

.fas.fa-star.filled {
	color: #ffc107;
}

.single_instructor_caption {
	h4 {
		a {
			color: $color-dark-blue !important;
			font-weight: bold;
			font-size: 1.2rem;
			text-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
		}
	}
}

.single_instructor_thumb {
	margin: auto;
}

.ng-star-inserted {
	margin-bottom: 0 !important;
}
.edu_list {
	display: initial !important;
}
.lists-custom.ng-star-inserted {
	list-style-type: initial !important;
	padding-left: 20px !important;
	li {
		width: 100% !important;
		margin-bottom: 10px !important;
	}
}

.expired-forfait-alert {
	position: relative;
	top: 2rem;
	z-index: 1;
}

.pricing-link {
	cursor: pointer;
	&:hover {
		text-decoration: underline;
		opacity: 0.8;
	}
}

@media (max-width: 1023px) {
	ul.edu_list li {
		font-size: 16px !important;
	}
}

@media screen and (max-width: 550px) {
	.ed_rate_info {
		display: flex;
		flex-direction: column-reverse;
		align-items: flex-start;
	}
	.ul.lectures_lists li:before {
		display: none;
	}

	.single_instructor_caption {
		text-align: center;
	}
}

@media only screen and (max-width: 375) {
	.mat-expansion-panel-header-title,
	.mat-expansion-panel-header-description {
		font-size: 10px !important;
	}
}
