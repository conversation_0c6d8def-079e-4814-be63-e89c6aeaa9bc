<div class="red-skin">
	<!-- ============================================================== -->
	<!-- Main wrapper - style you can find in pages.scss -->
	<!-- ============================================================== -->
	<div id="main-wrapper">
		<!-- ============================================================== -->
		<!-- Top header  -->
		<!-- ============================================================== -->
		<!-- Start Navigation -->
		<app-home-header [theclass]="'header-light '"></app-home-header>
		<!-- End Navigation -->
		<div class="clearfix"></div>
		<!-- ============================================================== -->
		<!-- Top header  -->
		<!-- ============================================================== -->

		<!-- ============================ Course Header Info Start================================== -->
		<div
			*ngIf="userAbonnementExpired"
			class="expired-forfait-alert alert alert-danger text-bold text-center"
		>
			<span class="pricing-link" routerLink="/home/<USER>">{{
				'user.details.expiredSubscriptionAlertMessage' | translate
			}}</span>
		</div>
		<div
			class="image-cover ed_detail_head lg"
			*ngIf="course"
			style="background: #f4f4f4 url('/assets/img/course-detail.png')"
			data-overlay="9"
		>
			<div class="container">
				<div class="row">
					<div class="col-lg-7 col-md-9">
						<div class="ed_detail_wrap light">
							<ul class="cources_facts_list">
								<li class="facts-1" *ngFor="let keyword of course.Keywords">
									{{ keyword }}
								</li>
							</ul>
							<div class="ed_header_caption">
								<h2 class="ed_title">{{ course.Title }}</h2>
								<ul>
									<li><i class="ti-calendar"></i>{{ courseDuration }}</li>
									<li>
										<i class="ti-control-forward"></i>{{ getCountContent() }}
										{{ 'course.contents' | translate }}
									</li>
									<li>
										<i class="ti-user"></i>{{ courseViewsCount }}
										{{ 'course.details.learnersEnrolled' | translate }}
									</li>
								</ul>
							</div>
							<div class="ed_rate_info">
								<div class="course-opinions static-op pt-0">
									<span>
										<strong>{{ likeCount }}</strong>
										<span
											matTooltip="{{ 'course.likes' | translate }}"
											class="material-icons"
										>
											thumb_up_alt</span
										>
									</span>

									<span>
										<strong>{{ disLikeCount }}</strong>
										<span
											matTooltip="{{ 'course.dislikes' | translate }}"
											class="material-icons"
										>
											thumb_down_alt</span
										>
									</span>

									<!-- comment love opinion-->
									<span>
										<strong>{{ loveCount }}</strong>
										<span
											matTooltip="{{ 'course.loves' | translate }}"
											class="material-icons"
											>favorite</span
										>
									</span>
								</div>

								<div class="star_info">
									<!-- <i class="fas fa-star filled"></i>
                  <i class="fas fa-star filled"></i>
                  <i class="fas fa-star filled"></i>
                  <i class="fas fa-star filled"></i>
                  <i class="fas fa-star"></i> -->
									<app-course-rating
										[globalRating]="globalRating"
										[course]="course"
										[userIsNotConnected]="userIsNotConnected"
										[static]="true"
										(onRating)="handleCourseRating($event)"
										(onGlobalRating)="handleCourseGlobalRating($event)"
									>
									</app-course-rating>
								</div>
								<div class="review_counter">
									<strong>{{ globalRating + '.0' }}</strong>
									<span style="display: iniline-block; margin-left: 1rem">{{
										viewCount
									}}</span>
									{{ 'home.page.reviews' | translate }}
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!-- ============================ Course Header Info End ================================== -->

		<!-- ============================ Course Detail ================================== -->
		<section class="bg-light pt-0" *ngIf="course">
			<div class="container">
				<div class="row">
					<div class="col-lg-8 col-md-8 pt-5">
						<div class="inline_edu_wrap">
							<div class="inline_edu_first">
								<h1>{{ course.Title }}</h1>
								<ul class="edu_inline_info">
									<li><i class="ti-calendar"></i>{{ courseDuration }}</li>
									<li>
										<i class="ti-control-forward"></i>{{ getCountContent() }}
										{{ 'course.contents' | translate }}
									</li>
									<li>
										<i class="ti-user"></i>{{ courseViewsCount }}
										{{ 'course.details.learnersEnrolled' | translate }}
									</li>
								</ul>
							</div>
							<div class="inline_edu_last">
								<h4 class="edu_price" *ngIf="!course.Free">
									{{ course.Currency }}
									<span *ngIf="course.NewPrice">
										{{ course.NewPrice }}
									</span>
									<sup class="small text-muted" *ngIf="course.NewPrice">
										<strike> {{ course.Currency }} {{ course.Price }} </strike>
									</sup>

									<span *ngIf="!course.NewPrice">
										{{ course.Price }}
									</span>
								</h4>
								<a
									*ngIf="
										(isPay && !buyNow) || course.Free || userAbonnementActive
									"
									class="btn btn-theme enroll-btn"
									[routerLink]="'/courses/reader/' + course.Slug"
								>
									{{ 'course.details.readCourse' | translate }}
									<i class="fas fa-chevron-right"></i>
								</a>
								<a
									*ngIf="
										!isPay && !buyNow && !course.Free && !userAbonnementActive
									"
									class="btn btn-theme enroll-btn"
									(click)="addToCard($event)"
								>
									<i class="fas fa-shopping-basket mr-2"></i>
									{{ 'course.details.add' | translate }}
								</a>

								<a
									*ngIf="
										buyNow && !isPay && !course.Free && !userAbonnementActive
									"
									class="btn btn-theme enroll-btn"
									(click)="onBuy($event)"
								>
									{{ 'course.details.buyNow' | translate }}
									<i class="fas fa-chevron-right"></i>
								</a>
							</div>
						</div>

						<a
							*ngIf="(isPay && !buyNow) || course.Free || userAbonnementActive"
							[routerLink]="'/courses/reader/' + course.Slug"
						>
							<div class="property_video xl">
								<div class="thumb">
									<img
										class="pro_img img-fluid w100"
										[src]="cover"
										alt="7.jpg"
									/>
									<div class="overlay_icon">
										<div class="bb-video-box">
											<div class="bb-video-box-inner">
												<div class="bb-video-box-innerup">
													<i class="ti-control-play"></i>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</a>

						<div
							class="property_video xl"
							style="filter: blur(2.5px)"
							*ngIf="
								!((isPay && !buyNow) || course.Free || userAbonnementActive)
							"
						>
							<div class="thumb">
								<img class="pro_img img-fluid w100" [src]="cover" alt="7.jpg" />
								<div class="overlay_icon">
									<div class="bb-video-box">
										<div class="bb-video-box-inner">
											<div class="bb-video-box-innerup">
												<i class="ti-control-play"></i>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>

						<!-- All Info Show in Tab -->
						<div class="tab_box_info mt-4">
							<ul
								class="nav nav-pills mb-3 light"
								id="pills-tab"
								role="tablist"
							>
								<li class="nav-item">
									<a
										class="nav-link active"
										id="overview-tab"
										data-toggle="pill"
										href="#overview"
										role="tab"
										aria-controls="overview"
										aria-selected="true"
										>{{ 'course.details.overview' | translate }}</a
									>
								</li>
								<li class="nav-item">
									<a
										class="nav-link"
										id="curriculum-tab"
										data-toggle="pill"
										href="#curriculum"
										role="tab"
										aria-controls="curriculum"
										aria-selected="false"
										>{{ 'course.details.curriculum' | translate }}</a
									>
								</li>
								<li class="nav-item">
									<a
										class="nav-link"
										id="instructor-tab"
										data-toggle="pill"
										href="#instructor"
										role="tab"
										aria-controls="instructor"
										aria-selected="false"
										>{{ 'course.details.instructor' | translate }}</a
									>
								</li>
								<li class="nav-item">
									<a
										class="nav-link"
										id="reviews-tab"
										data-toggle="pill"
										href="#reviews"
										role="tab"
										aria-controls="reviews"
										aria-selected="false"
										>{{ 'course.details.reviews' | translate }}</a
									>
								</li>
							</ul>

							<div class="tab-content" id="pills-tabContent">
								<!-- Overview Detail -->
								<div
									class="tab-pane fade show active"
									id="overview"
									role="tabpanel"
									aria-labelledby="overview-tab"
								>
									<!-- Overview -->
									<div class="edu_wraper">
										<h4 class="edu_title">
											{{ 'course.details.courseOverview' | translate }}
										</h4>
										<p [innerHTML]="course.Resume"></p>
										<br />
										<h4 class="edu_title">
											{{ 'course.details.requirements' | translate }}
										</h4>
										<ul class="lists-3">
											<li *ngFor="let req of course.Prerequisites">
												{{ req }}
											</li>
										</ul>
									</div>

									<!-- Overview -->
									<div class="edu_wraper">
										<h4 class="edu_title">
											{{ 'course.details.whatYouLearn' | translate }}
										</h4>
										<ul class="lists-custom" *ngFor="let goal of course.Goals">
											<li>{{ goal }}</li>
										</ul>
									</div>
								</div>

								<!-- Curriculum Detail -->
								<div
									class="tab-pane fade"
									id="curriculum"
									role="tabpanel"
									aria-labelledby="curriculum-tab"
								>
									<div class="edu_wraper">
										<h4 class="edu_title">
											{{ 'course.details.curriculum' | translate }}
										</h4>

										<mat-accordion>
											<mat-expansion-panel
												*ngFor="let section of course.Sections"
											>
												<mat-expansion-panel-header>
													<mat-panel-title>
														Part {{ section.Position }}: {{ section.Title }}
													</mat-panel-title>
												</mat-expansion-panel-header>
												<ul
													*ngIf="
														section.Contents && section.Contents.length === 0
													"
													class="lectures_lists"
												>
													<li>
														{{ 'course.details.noCourseSection' | translate }}
													</li>
												</ul>
												<ul
													class="lectures_lists"
													*ngIf="
														section.Contents &&
														section.Contents.length > 0 &&
														(+section.Contents[0].Type === contentType.EBOOK ||
															+section.Contents[0].Type === contentType.PDF ||
															+section.Contents[0].Type === contentType.VIDEO)
													"
												>
													<li *ngFor="let content of section.Contents">
														<div class="lectures_lists_title">
															<i class="ti-control-play"></i
															>{{ 'course.details.course' | translate }}:
															{{ content.Position }}
														</div>
														{{ content.Title }}
													</li>
												</ul>

												<ul
													class="lectures_lists"
													*ngIf="
														section.Contents &&
														section.Contents.length > 0 &&
														+section.Contents[0].Type === contentType.QUIZ
													"
												>
													<li>
														<div class="lectures_lists_title">
															<i
																class="fas fa-question hide-on-med-and-down"
															></i>
															Questions ({{ section.Contents.length }})
														</div>
													</li>
												</ul>

												<ul
													class="lectures_lists"
													*ngIf="
														section.Contents &&
														section.Contents.length > 0 &&
														+section.Contents[0].Type === contentType.ASSIGNMENT
													"
												>
													<li>
														<div class="lectures_lists_title">
															<i
																class="fas fa-book-reader hide-on-med-and-down"
															></i>
															{{ 'course.details.assignments' | translate }} ({{
																section.Contents.length
															}})
														</div>
													</li>
												</ul>
											</mat-expansion-panel>
										</mat-accordion>
									</div>
								</div>

								<!-- Instructor Detail -->
								<div
									class="tab-pane fade"
									id="instructor"
									role="tabpanel"
									aria-labelledby="instructor-tab"
								>
									<div class="single_instructor">
										<div class="single_instructor_thumb">
											<a href="#"
												><img
													[src]="getInstructorProfileImage()"
													class="img-fluid"
													alt="instructor avatar"
											/></a>
										</div>
										<div class="single_instructor_caption">
											<h4>
												<a href="#">{{
													course.CreatedBy.Firstname +
														' ' +
														course.CreatedBy.Lastname
												}}</a>
											</h4>
											<ul class="instructor_info">
												<li>
													<i class="ti-video-camera"
														>&nbsp;{{ instructorAllCourses.length }}</i
													>&nbsp;{{ 'course.courses' | translate }}
												</li>
												<li>
													<i class="ti-control-forward"></i
													>{{ getCountContent() }}
													{{ 'course.contents' | translate }}
												</li>
												<!-- <li><i class="ti-user"></i>Exp. 4 Year</li> -->
											</ul>
											<p>
												{{
													course.CreatedBy.About
														? course.CreatedBy.About
														: ('course.no-desc' | translate)
												}}
											</p>

											<ul
												*ngIf="
													course.CreatedBy.FacebookAddress ||
													course.CreatedBy.TwitterAddress ||
													course.CreatedBy.LinkedInAddress ||
													course.CreatedBy.GooleAddress
												"
												class="social_info"
											>
												<li *ngIf="course.CreatedBy.FacebookAddress">
													<a
														(click)="
															openInNewTab(course.CreatedBy.FacebookAddress)
														"
														><i class="ti-facebook"></i
													></a>
												</li>
												<li *ngIf="course.CreatedBy.TwitterAddress">
													<a
														(click)="
															openInNewTab(course.CreatedBy.TwitterAddress)
														"
														><i class="ti-twitter"></i
													></a>
												</li>
												<li *ngIf="course.CreatedBy.LinkedInAddress">
													<a
														(click)="
															openInNewTab(course.CreatedBy.LinkedInAddress)
														"
														><i class="ti-linkedin"></i
													></a>
												</li>
												<li *ngIf="course.CreatedBy.GooleAddress">
													<a
														(click)="
															openInNewTab(course.CreatedBy.GooleAddress)
														"
														><i class="ti-google"></i
													></a>
												</li>
											</ul>
										</div>
									</div>
								</div>

								<!-- Reviews Detail -->
								<div
									class="tab-pane fade"
									id="reviews"
									role="tabpanel"
									aria-labelledby="reviews-tab"
								>
									<!-- Overall Reviews -->
									<div class="rating-overview">
										<div class="rating-overview-box">
											<span class="rating-overview-box-total">{{
												userRating + '.0'
											}}</span>
											<span class="rating-overview-box-percent"
												>out of 4.0</span
											>
											<div class="star-rating" data-rating="5">
												<!-- <i class="ti-star"></i><i class="ti-star"></i><i class="ti-star"></i><i class="ti-star"></i><i class="ti-star"></i> -->
												<app-course-rating
													[GloabalRating]="globalRating"
													[course]="course"
													[userIsNotConnected]="userIsNotConnected"
													[static]="false"
													(onRating)="handleCourseRating($event)"
													(onGlobalRating)="handleCourseGlobalRating($event)"
												>
												</app-course-rating>
											</div>

											<div class="course-opinions">
												<span (click)="likeComment()">
													<strong
														*ngIf="
															loveCount || likeCount != 0 || disLikeCount != 0
														"
														>{{ likeCount }}</strong
													>

													<span
														matTooltip="{{ 'course.like' | translate }}"
														*ngIf="!isLiked && !userIsNotConnected"
														class="material-icons"
													>
														thumb_up_alt</span
													>
													<span
														matTooltip="{{ 'course.like' | translate }}"
														*ngIf="isLiked && !userIsNotConnected"
														class="material-icons active"
														>thumb_up_alt</span
													>
													<span
														matTooltip="{{ 'course.like' | translate }}"
														*ngIf="userIsNotConnected"
														class="material-icons disabled"
														>thumb_up_alt</span
													>
												</span>

												<span (click)="disLikeComment()">
													<strong
														*ngIf="
															loveCount || likeCount != 0 || disLikeCount != 0
														"
														>{{ disLikeCount }}</strong
													>

													<span
														matTooltip="{{ 'course.dislike' | translate }}"
														*ngIf="!isDisLiked && !userIsNotConnected"
														class="material-icons"
													>
														thumb_down_alt</span
													>
													<span
														matTooltip="{{ 'course.dislike' | translate }}"
														*ngIf="isDisLiked && !userIsNotConnected"
														class="material-icons active"
														>thumb_down_alt</span
													>
													<span
														matTooltip="{{ 'course.dislike' | translate }}"
														*ngIf="userIsNotConnected"
														class="material-icons disabled"
														>thumb_down_alt</span
													>
												</span>

												<!-- comment love opinion-->
												<span (click)="loveComment()">
													<strong
														*ngIf="
															loveCount || likeCount != 0 || disLikeCount != 0
														"
														>{{ loveCount }}</strong
													>

													<span
														matTooltip="{{ 'course.love' | translate }}"
														*ngIf="!isLoved && !userIsNotConnected"
														class="material-icons"
														>favorite</span
													>
													<span
														matTooltip="{{ 'course.love' | translate }}"
														*ngIf="isLoved && !userIsNotConnected"
														class="material-icons active"
														>favorite</span
													>
													<span
														matTooltip="{{ 'course.love' | translate }}"
														*ngIf="userIsNotConnected"
														class="material-icons disabled"
														>favorite</span
													>
												</span>
											</div>
										</div>

										<div class="rating-bars">
											<div class="rating-bars-item">
												<span class="rating-bars-name"
													>4 {{ 'course.details.stars' | translate }}</span
												>
												<span class="rating-bars-inner">
													<span
														[class]="
															pourcentVote4 > 75
																? 'rating-bars-rating high'
																: pourcentVote4 > 53
																? 'rating-bars-rating good'
																: 20 > pourcentVote4
																? 'rating-bars-rating poor'
																: 'rating-bars-rating mid'
														"
														data-rating="4.7"
													>
														<span
															class="rating-bars-rating-inner"
															[style.width.%]="pourcentVote4"
														></span>
													</span>
													<strong
														>{{ pourcentVote4 ? pourcentVote4 : '0' }}%</strong
													>
												</span>
											</div>
											<div class="rating-bars-item">
												<span class="rating-bars-name"
													>3 {{ 'course.details.stars' | translate }}</span
												>
												<span class="rating-bars-inner">
													<span
														[class]="
															pourcentVote3 > 75
																? 'rating-bars-rating high'
																: pourcentVote3 > 53
																? 'rating-bars-rating good'
																: 20 > pourcentVote3
																? 'rating-bars-rating poor'
																: 'rating-bars-rating mid'
														"
														data-rating="0.9"
													>
														<span
															class="rating-bars-rating-inner"
															[style.width.%]="pourcentVote3"
														></span>
													</span>
													<strong
														>{{ pourcentVote3 ? pourcentVote3 : '0' }}%</strong
													>
												</span>
											</div>
											<div class="rating-bars-item">
												<span class="rating-bars-name"
													>2 {{ 'course.details.stars' | translate }}</span
												>
												<span class="rating-bars-inner">
													<span
														[class]="
															pourcentVote2 > 75
																? 'rating-bars-rating high'
																: pourcentVote2 > 53
																? 'rating-bars-rating good'
																: 20 > pourcentVote2
																? 'rating-bars-rating poor'
																: 'rating-bars-rating mid'
														"
														data-rating="3.2"
													>
														<span
															class="rating-bars-rating-inner"
															[style.width.%]="pourcentVote2"
														></span>
													</span>
													<strong
														>{{ pourcentVote2 ? pourcentVote2 : '0' }}%</strong
													>
												</span>
											</div>
											<div class="rating-bars-item">
												<span class="rating-bars-name"
													>1 {{ 'course.details.star' | translate }}</span
												>
												<span class="rating-bars-inner">
													<span
														[class]="
															pourcentVote1 > 75
																? 'rating-bars-rating high'
																: pourcentVote1 > 53
																? 'rating-bars-rating good'
																: 20 > pourcentVote1
																? 'rating-bars-rating poor'
																: 'rating-bars-rating mid'
														"
														data-rating="2.0"
													>
														<span
															class="rating-bars-rating-inner"
															[style.width.%]="pourcentVote1"
														></span>
													</span>
													<strong
														>{{ pourcentVote1 ? pourcentVote1 : '0' }}%</strong
													>
												</span>
											</div>
										</div>
									</div>

									<!-- Reviews -->
									<div class="list-single-main-item fl-wrap">
										<div class="reviews-comments-wrap">
											<app-courses-comments
												[course]="course"
											></app-courses-comments>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>

					<div class="col-lg-4 col-md-4">
						<!-- Course info -->
						<div class="ed_view_box style_2 overlio">
							<div class="ed_author">
								<div class="ed_author_thumb">
									<a [routerLink]="'/instructors/' + course.CreatedBy.Slug">
										<img
											class="img-fluid"
											[src]="getInstructorProfileImage()"
											alt="7.jpg"
										/>
									</a>
								</div>
								<div class="ed_author_box">
									<a [routerLink]="'/instructors/' + course.CreatedBy.Slug">
										<h4>
											<span>{{ course.CreatedBy.Firstname }}</span>
										</h4>
										<span
											*ngIf="
												course.CreatedBy &&
												course.CreatedBy.Categories &&
												course.CreatedBy.Categories.length > 0
											"
										>
											{{
												course.CreatedBy.Categories &&
												course.CreatedBy.Categories &&
												course.CreatedBy.Categories.length > 0
													? course.CreatedBy.Categories[0].Title
													: ''
											}}
											in {{ course.CreatedBy.Country }}
										</span>
									</a>
								</div>
							</div>

							<div class="ed_view_price pl-4">
								<span>{{ 'course.actualPrice' | translate }}</span>
								<h2 class="theme-cl" *ngIf="course.Free">
									<span class="badge badge-success">{{
										'course.free' | translate
									}}</span>
								</h2>
								<h2 class="theme-cl" *ngIf="!course.Free && course.NewPrice">
									{{ course.Currency }} {{ course.NewPrice }}

									<sup class="small text-muted">
										<strike> {{ course.Currency }} {{ course.Price }} </strike>
									</sup>
								</h2>
								<h2 class="theme-cl" *ngIf="!course.Free && !course.NewPrice">
									{{ course.Currency }} {{ course.Price }}
								</h2>
							</div>
							<div class="ed_view_features pl-4">
								<span>{{ 'course.courseFeatures' | translate }}</span>
								<ul>
									<li *ngFor="let section of course.Sections">
										<i class="ti-angle-right"></i> {{ section.Title }}
									</li>
								</ul>
							</div>
							<div class="ed_view_link">
								<a
									*ngIf="
										(isPay && !buyNow) || course.Free || userAbonnementActive
									"
									class="btn btn-theme enroll-btn"
									[routerLink]="'/courses/reader/' + course.Slug"
								>
									{{ 'course.details.readCourse' | translate }}
									<i class="fas fa-chevron-right"></i>
								</a>

								<a
									*ngIf="
										!course.Free && !userAbonnementActive && buyNow && !isPay
									"
									class="btn btn-theme enroll-btn"
									[routerLink]="'/pay'"
								>
									{{ 'course.details.buyNow' | translate
									}}<i class="fas fa-chevron-right"></i>
								</a>
								<a
									href=""
									*ngIf="
										!course.Free && !userAbonnementActive && !isPay && !buyNow
									"
									class="btn btn-theme enroll-btn"
									(click)="addToCard($event)"
								>
									<i class="fas fa-shopping-basket mr-2"></i>
									{{ 'course.details.add' | translate }}
								</a>
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>
		<!-- ============================ Course Detail ================================== -->

		<!-- ============================ Footer Start ================================== -->
		<app-home-footer></app-home-footer>
		<!-- ============================ Footer End ================================== -->
	</div>
</div>
