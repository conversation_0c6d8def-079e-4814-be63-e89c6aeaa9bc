import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { Location } from '@angular/common';
import { AngularEditorConfig } from '@kolkov/angular-editor';
import { UtilsService } from '~/app/services/utils.service';
import { Course } from '~/app/models/course.model';
import { Category } from '~/app/models/category.model';
import { CategoryService } from '~/app/services/categories/category.service';
import { validate, ValidationError } from 'class-validator';
import { User } from '~/app/models/user.model';
import { TranslateService } from '@ngx-translate/core';
import { UserService } from '~/app/services/users/user.service';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute, CanActivate, Router } from '@angular/router';
import { CourseSection } from '~/app/models/course-section.model';
import { CourseService } from '~/app/services/courses/course.service';
import { CourseContentService } from '~/app/services/courses/course-content.service';

@Component({
	selector: 'app-courses-manage',
	templateUrl: './courses-manage.component.html',
	styleUrls: ['./courses-manage.component.scss']
})
export class CoursesManageComponent implements OnInit {
	isLinear = false;
	course: Course;

	user: User;
	indexTab: number = 0;

	constructor(
		private utilsService: UtilsService,
		private categoryService: CategoryService,
		private translateService: TranslateService,
		private userService: UserService,
		private toastr: ToastrService,
		private activeRoute: ActivatedRoute,
		private router: Router,
		private courseService: CourseService,
		private changeDetectorRef: ChangeDetectorRef,
		private location: Location
	) {}

	async ngOnInit(): Promise<void> {
		const id = this.activeRoute.snapshot.params.id as string;
		if (id) {
			this.course = await this.courseService.getBySlug(id).toPromise();
			const lang = await this.utilsService.getLanguages().toPromise();
			this.course = this.courseService.BuildCourse(this.course, lang);
			/* if (this.course.Sections && this.course.Sections.length > 0) {
				 const sections: Array<CourseSection> = [];
				 for (const section of this.course.Sections) {
					 section.Contents = await this.courseContentService.getContentBySection(section.Slug).toPromise();
					 sections.push(section);
				 }

				 this.course.Sections = sections;
			 } */
			console.log('courses', this.course);
		} else {
			// TODO: instancier le cours comme il se doit il cree bcp d'erreurs

			this.course = new Course(null, '123', null, null, [], null, null);
		}
		if (this.userService.isConnected()) {
			this.user = await this.userService.getUserConnected().toPromise();
			this.course.CreatedBy = this.user;

			if (!this.user.IsEmailConfirm) {
				const message = await this.translateService
					.get('course.create.emailNotVerified')
					.toPromise();
				this.toastr.error(message, 'Brain-maker');
				this.location.back();
			}
		} else {
			this.router.navigate(['/']);
		}
	}

	openNext() {
		this.indexTab = this.indexTab === 5 ? 5 : this.indexTab + 1;
		window.scrollTo(0, 0);
	}

	openPrev() {
		this.indexTab = this.indexTab === 0 ? 0 : this.indexTab - 1;
		window.scrollTo(0, 0);
	}

	listenerCourseChanged(course: Course): void {
		console.log('EMMMIIITTTT', course);
		this.course = { ...course };
		this.changeDetectorRef.detectChanges();
	}
}
