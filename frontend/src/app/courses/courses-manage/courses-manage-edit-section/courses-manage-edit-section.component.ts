import { Component, Inject, OnInit } from '@angular/core';
import { validate, ValidationError } from 'class-validator';
import { CourseSection } from '~/app/models/course-section.model';

import { UtilsService } from '~/app/services/utils.service';
import { TranslateService } from '@ngx-translate/core';
import { UserService } from '~/app/services/users/user.service';
import { LY_DIALOG_DATA, LyDialogRef } from '@alyle/ui/dialog';
import { User } from '~/app/models/user.model';
import { ToastrService } from 'ngx-toastr';
import { CourseTranscription } from '~/app/models/course-transcription.model';
import { CourseSectionService } from '~/app/services/courses/course-section.service';

@Component({
	selector: 'app-courses-manage-edit-section',
	templateUrl: './courses-manage-edit-section.component.html',
	styleUrls: ['./courses-manage-edit-section.component.scss']
})
export class CoursesManageEditSectionComponent implements OnInit {
	errors: ValidationError[];
	errorsHelper: ValidationError[];
	isLoading: boolean;

	section: CourseSection;

	constructor(
		private utilsService: UtilsService,

		private translateService: TranslateService,
		private userService: UserService,
		public dialogRef: LyDialogRef,
		@Inject(LY_DIALOG_DATA)
		private data: {
			section: CourseSection;

			user: User;
		},
		private toastr: ToastrService,
		private courseSectionService: CourseSectionService
	) {}

	async ngOnInit(): Promise<void> {
		this.section = this.data.section;
	}

	close(e): void {
		if (e) {
			e.preventDefault();
		}
		this.dialogRef.close();
	}

	async onSubmit(e): Promise<void> {
		e.preventDefault();
		try {
			this.isLoading = true;
			if (this.section.Title && this.section.Description) {
				const response = await this.courseSectionService
					.edit(this.section)
					.toPromise();

				if (response && response.Id) {
					const message = await this.translateService
						.get('home.register.success')
						.toPromise();
					this.dialogRef.close(true);
				} else {
					const message = await this.translateService
						.get('home.register.error')
						.toPromise();
					this.toastr.error(message, 'Brain-maker');
				}
			} else {
				const message = await this.translateService
					.get('home.register.error')
					.toPromise();
				this.toastr.error(message, 'Brain-maker');
			}
		} catch (e) {
			console.log(e);
			const message = await this.translateService
				.get('home.register.error')
				.toPromise();
			this.toastr.error(message, 'Brain-maker');
		} finally {
			this.isLoading = false;
		}
	}

	async onItemChange(): Promise<void> {
		this.errorsHelper = await validate(this.section);
	}

	isNotvalid(name: string): boolean {
		return this.utilsService.isNotvalid(name, this.errorsHelper);
	}

	getAllError(name: string): string[] {
		return this.utilsService.getAllError(name, this.errorsHelper);
	}
}
