<form (ngSubmit)="onSubmit($event)" *ngIf="section">
    <div ly-dialog-content class="px-0 mx-0">
        <div class="login-pop-form" role="document">
            <div class="" id="sign-up">
                <div class="d-flex flex-row align-items-center">
                    <h4 class="modal-header-title mt-3 pl-3">
                        {{'blog.create-post.edit-section-label'|translate}}
                    </h4>
                    <span class="mod-close" (click)="close($event)" aria-hidden="true"><i class="ti-close"></i></span>
                </div>
                <div class="modal-body mw-100">
                    <div class="row">
                        <div class="form-group col-12">
                            <label for="newsectionTitle">{{'blog.create-post.title-label'|translate}}</label>
                            <input id="newsectionTitle" [(ngModel)]="section.Title" required="" class="form-control" name="newSection.Title" maxlength="100" type="text">
                        </div>
                        <div class="form-group col-12">
                            <label for="newsectionContent">{{'course.create.learnObj'|translate}}</label>
                            <input id="newsectionContent" class="form-control" [(ngModel)]="section.Description" name="newSection.Description" type="text" maxlength="200" aria-describedby="sectionContent-help">
                            <small id="sectionContent-help">{{'course.create.whatWeLearn'|translate}} </small>
                        </div>

                        <div class="col-12 d-flex form-group mt-2">

                            <button type="submit" class="btn  btn-dark">
                {{'course.comments.save'|translate}}
              </button>
                            <div class="mt-2 d-flex align-items-center justify-content-center" *ngIf="isLoading">

                                <i class="fas fa-spin fa-spinner mr-2"></i> {{ 'home.loading' | translate }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>