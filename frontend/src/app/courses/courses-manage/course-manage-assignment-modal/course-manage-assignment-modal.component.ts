import { Component, Inject, OnInit } from '@angular/core';
import { User } from '~/app/models/user.model';
import { CourseSection } from '~/app/models/course-section.model';
import { AngularEditorConfig } from '@kolkov/angular-editor';
import { UtilsService } from '~/app/services/utils.service';
import { CategoryService } from '~/app/services/categories/category.service';
import { TranslateService } from '@ngx-translate/core';
import { UserService } from '~/app/services/users/user.service';
import { LY_DIALOG_DATA, LyDialogRef } from '@alyle/ui/dialog';
import { ToastrService } from 'ngx-toastr';
import { CourseAssignment } from '~/app/models/course-assignment.model';
import { CourseAssignmentService } from '~/app/services/courses/course-assignment.service';

@Component({
	selector: 'app-course-manage-assignment-modal',
	templateUrl: './course-manage-assignment-modal.component.html',
	styleUrls: ['./course-manage-assignment-modal.component.scss']
})
export class CourseManageAssignmentModalComponent implements OnInit {
	user: User;
	courseAssignment: CourseAssignment;
	section: CourseSection;
	contentConfig: AngularEditorConfig;

	constructor(
		private utilsService: UtilsService,
		private categoryService: CategoryService,
		private translateService: TranslateService,
		private userService: UserService,
		public dialogRef: LyDialogRef,
		@Inject(LY_DIALOG_DATA)
		private data: {
			section: CourseSection;
			courseAssignment: CourseAssignment;
			user: User;
		},
		private toastr: ToastrService,
		private courseAssignmentService: CourseAssignmentService
	) {}

	async ngOnInit(): Promise<void> {
		this.contentConfig = this.utilsService.getSimpleconfigAngular();
		this.section = this.data.section;
		this.user = this.data.user;
		if (this.data.courseAssignment) {
			this.courseAssignment = this.data.courseAssignment;
			this.courseAssignment.Section = this.data.section;
		} else {
			this.courseAssignment = new CourseAssignment();
			this.courseAssignment.Section = this.data.section;
		}
		this.courseAssignment.CreatedByISlug = this.data.user.Slug;
		// console.log('content : ', this.courseAssignment);
	}

	close(e): void {
		if (e) {
			e.preventDefault();
		}
		this.dialogRef.close();
	}

	onUpdateContent(courseContent: CourseAssignment): void {
		this.dialogRef.close(true);
	}
}
