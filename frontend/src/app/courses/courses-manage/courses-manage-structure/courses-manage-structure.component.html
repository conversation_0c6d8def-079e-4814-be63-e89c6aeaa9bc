<div class="row mt-4">
	<div class="col-12 h5">
		{{ 'course.create.courseStruc' | translate }}
		<hr />
	</div>
</div>
<div class="row">
	<div class="col-12"></div>
	<div class="alert alert-dark" role="alert">
		{{ 'course.create.courseStrucMsg1' | translate }}
		<strong>{{ 'course.create.courseStrucMsg2' | translate }}</strong>
	</div>
	<div class="col-12 mt-4">
		<div class="row">
			<div class="col-12 d-flex justify-content-between">
				<button
					class="btn btn-primary"
					type="button"
					(click)="openSectionSection(true)"
				>
					<i class="fas fa-plus mr-2"></i>
					{{ 'blog.create-post.new-section' | translate }}
				</button>

				<!--button class="btn btn-outline-light text-dark" type="button">
          <i class="fas fa-sort mr-2"></i>
          Range section
        </button-->
			</div>
		</div>

		<form class="row" (ngSubmit)="onAddSection($event)" *ngIf="showAddSection">
			<div class="col-12 my-2">
				<div class="">
					<button
						type="button"
						class="btn btn-link"
						(click)="openSectionSection(false)"
					>
						<i class="fas fa-times-circle"></i>
					</button>
				</div>
				<div class="card">
					<div class="card-body">
						<div class="card-title">
							{{ 'blog.create-post.new-section' | translate }}
						</div>

						<div class="row">
							<div class="form-group col-12">
								<label for="newsectionTitle"
									>{{ 'blog.create-post.title-label' | translate }}
								</label>
								<input
									id="newsectionTitle"
									[(ngModel)]="newSection.Title"
									required=""
									class="form-control"
									name="newSection.Title"
									maxlength="100"
									type="text"
								/>
							</div>
							<div class="form-group col-12">
								<label for="newsectionContent">{{
									'course.create.learnObj' | translate
								}}</label>
								<input
									id="newsectionContent"
									class="form-control"
									[(ngModel)]="newSection.Description"
									name="newSection.Description"
									type="text"
									maxlength="200"
									aria-describedby="sectionContent-help"
								/>
								<small id="sectionContent-help"
									>{{ 'course.create.whatWeLearn' | translate }}
								</small>
							</div>

							<div class="col-12 form-group mt-2 d-flex">
								<button
									type="reset"
									class="btn btn-sm btn-outline-primary mr-2"
								>
									{{ 'course.create.reset' | translate }}
								</button>
								<button type="submit" class="btn btn-sm btn-dark">
									{{ 'course.comments.save' | translate }}
								</button>
								<div
									class="mt-2 d-flex align-items-center justify-content-center"
									*ngIf="isLoading"
								>
									<i class="fas fa-spin fa-spinner mr-2"></i>
									{{ 'home.loading' | translate }}
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</form>

		<div class="" *ngIf="course && course.Sections">
			<div
				*ngFor="let section of course.Sections; let ind = index"
				class="card bg-light my-2"
			>
				<div
					class="card-header d-flex justify-content-between align-items-center"
				>
					<div class="">
						Section {{ section.Position }}:
						{{
							section.Title.length > 80
								? section.Title.substr(0, 77) + '...'
								: section.Title
						}}
					</div>
					<div class="">
						<!--mat-fab-menu color="primary"
                          direction="left"
                          (onFabMenuItemSelected)="onSelect($event)"
                          [fabButtons]="fabButtonsRandom">
            </mat-fab-menu-->
						<button
							class="btn btn-sm btn-outline-light text-dark"
							(click)="onEditSection($event, section, ind)"
							*ngIf="listOfManageMenu[ind]"
						>
							<i class="fas fa-edit mr-2"></i> Edit
						</button>
						<button
							class="btn btn-sm btn-outline-light text-dark"
							*ngIf="
								listOfManageMenu[ind] &&
								(isRightType(section, contentType.VIDEO) ||
									isRightType(section, contentType.PDF) ||
									isRightType(section, contentType.EBOOK))
							"
							(click)="onCreateContent($event, section, null, ind)"
						>
							<i class="fas fa-book mr-2"></i> Content
						</button>
						<button
							class="btn btn-sm btn-outline-light text-dark"
							*ngIf="
								listOfManageMenu[ind] &&
								isRightType(section, contentType.ASSIGNMENT)
							"
							(click)="onCreateAssignment($event, section, null, ind)"
						>
							<i class="fas fa-book-reader mr-2"></i>
							{{ 'course.details.assignment' | translate }}
						</button>

						<button
							class="btn btn-sm btn-outline-light ml-2 text-dark"
							(click)="onCreateQuiz($event, section, null, ind)"
							*ngIf="
								listOfManageMenu[ind] && isRightType(section, contentType.QUIZ)
							"
						>
							<i class="fas fa-question mr-2"></i> Quiz
						</button>
						<!--button class="btn btn-sm btn-outline-light ml-2 text-dark" *ngIf="ind > 0 && listOfManageMenu[ind]">
              <i class="fas fa-chevron-up mr-2"></i> Move up
            </button-->
						<button
							class="btn btn-sm btn-outline-light text-dark"
							*ngIf="
								listOfManageMenu[ind] &&
								isRightType(section, contentType.ASSIGNMENT)
							"
							(click)="onDeleteSection($event, section, ind)"
						>
							<i class="fas fa-times mr-2"></i>
							{{ 'course.comments.delete' | translate }}
						</button>
						<button
							class="btn btn-sm btn-secondary"
							(click)="onManageMenu($event, ind)"
						>
							<span *ngIf="!listOfManageMenu[ind]">
								<i class="fas fa-plus mr-2"></i> Menu</span
							>
							<span *ngIf="listOfManageMenu[ind]">
								<i class="fas fa-times mr-2"></i> Close</span
							>
						</button>
					</div>
				</div>
				<div class="card-body" *ngIf="section.Contents?.length > 0">
					<mat-accordion>
						<div
							class=""
							*ngFor="let content of section.Contents; let i = index"
						>
							<mat-expansion-panel *ngIf="content.Title">
								<mat-expansion-panel-header>
									<mat-panel-title class="d-flex align-items-center">
										<span class="mr-2">
											<span
												class=""
												*ngIf="
													content.Type === contentType.VIDEO ||
													content.Type === contentType.PDF ||
													content.Type === contentType.EBOOK
												"
											>
												{{ 'course.details.course' | translate }}
											</span>
											<span class="" *ngIf="content.Type === contentType.QUIZ">
												Question
											</span>
											<span
												class=""
												*ngIf="content.Type === contentType.ASSIGNMENT"
											>
												{{ 'course.details.assignment' | translate }}
											</span>
											{{ content.Position }}
											<i
												class="fas fa-book mx-2"
												*ngIf="
													content.Type === contentType.VIDEO ||
													content.Type === contentType.PDF ||
													content.Type === contentType.EBOOK
												"
											>
											</i>
											<i
												class="fas fa-question mx-2"
												*ngIf="content.Type === contentType.QUIZ"
											>
											</i>
											<i
												class="fas fa-book-reader mx-2"
												*ngIf="content.Type === contentType.ASSIGNMENT"
											>
											</i>
											:
										</span>
										{{
											content.Title.length > 80
												? content.Title.substr(0, 77) + '...'
												: content.Title
										}}
									</mat-panel-title>
								</mat-expansion-panel-header>
								<app-courses-manage-quiz-factor
									[user]="user"
									*ngIf="content.Type === contentType.QUIZ && user"
									(updateCourse)="onUpdateQuiz($event)"
									[courseSectionTest]="content"
								></app-courses-manage-quiz-factor>

								<app-courses-manage-content-factor
									*ngIf="
										user &&
										(content.Type === contentType.VIDEO ||
											content.Type === contentType.PDF ||
											content.Type === contentType.EBOOK)
									"
									[user]="user"
									[section]="section"
									[courseContent]="content"
									[course]="course"
									(updateCourse)="onUpdateContent($event)"
								></app-courses-manage-content-factor>

								<app-course-manage-assignment-factor
									*ngIf="content.Type === contentType.ASSIGNMENT && user"
									[user]="user"
									[section]="section"
									[courseAssignment]="content"
									(updateCourse)="onUpdateAssignment($event)"
								></app-course-manage-assignment-factor>
							</mat-expansion-panel>
						</div>
					</mat-accordion>
				</div>
			</div>
		</div>
	</div>
</div>
