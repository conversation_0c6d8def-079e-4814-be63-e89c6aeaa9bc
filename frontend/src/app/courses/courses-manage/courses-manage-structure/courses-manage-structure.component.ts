import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ContentType, Course } from '~/app/models/course.model';
import { User } from '~/app/models/user.model';
import { CourseSection } from '~/app/models/course-section.model';
import { TranslateService } from '@ngx-translate/core';
import { ToastrService } from 'ngx-toastr';
import { UtilsService } from '~/app/services/utils.service';
import { CategoryService } from '~/app/services/categories/category.service';
import { UserService } from '~/app/services/users/user.service';
import { ActivatedRoute, Router } from '@angular/router';
import { CourseContent } from '~/app/models/course-content.model';
import { AngularEditorConfig } from '@kolkov/angular-editor';
import { CoursesManageContentModalComponent } from '~/app/courses/courses-manage/courses-manage-content-modal/courses-manage-content-modal.component';
import { MatFabMenu } from '@angular-material-extensions/fab-menu';
import { CoursesManageEditSectionComponent } from '~/app/courses/courses-manage/courses-manage-edit-section/courses-manage-edit-section.component';
import { CoursesManageQuizModalComponent } from '~/app/courses/courses-manage/courses-manage-quiz-modal/courses-manage-quiz-modal.component';
import { CourseSectionTest } from '~/app/models/course-section-test.model';
import { CourseAssignment } from '~/app/models/course-assignment.model';
import { CourseManageAssignmentModalComponent } from '~/app/courses/courses-manage/course-manage-assignment-modal/course-manage-assignment-modal.component';
import { CourseSectionService } from '~/app/services/courses/course-section.service';
import { CourseContentService } from '~/app/services/courses/course-content.service';
import { CourseSectionTestService } from '~/app/services/courses/course-section-test.service';
import { CourseService } from '~/app/services/courses/course.service';

@Component({
  selector: 'app-courses-manage-structure',
  templateUrl: './courses-manage-structure.component.html',
  styleUrls: ['./courses-manage-structure.component.scss']
})
export class CoursesManageStructureComponent implements OnInit {
  @Input()
  course: Course;
  isLoading: boolean;

  newSection: CourseSection;

  contentType = ContentType;
  @Input()
  user: User;
  contentConfig: AngularEditorConfig;
  showAddSection: boolean;
  fabButtonsRandom: MatFabMenu[];

  @Output() emitCourse: EventEmitter<Course> = new EventEmitter<Course>();

  listOfManageMenu: boolean[];
  constructor(
    private utilsService: UtilsService,
    private categoryService: CategoryService,
    private userService: UserService,
    private translateService: TranslateService,
    private toastr: ToastrService,
    private activeRoute: ActivatedRoute,
    private courseService: CourseService,
    private router: Router,
    private courseSectionService: CourseSectionService,
    private courseContentService: CourseContentService,
    private courseSectionTestService: CourseSectionTestService
  ) { }

  ngOnInit(): void {
    this.contentConfig = this.utilsService.getSimpleconfigAngular();
    this.fabButtonsRandom = [
      {
        id: 1,
        icon: 'create',
        tooltip: 'Edit'
      },
      {
        id: 2,
        icon: 'book',
        tooltip: 'Content'
      },
      {
        id: 3,
        icon: 'help_outline',
        tooltip: 'Quiz'
      },
      {
        id: 4,
        icon: 'upgrade',
        tooltip: 'Move up'
      }
    ];
    this.initSection();
  }

  onSubmit(): void { }

  async initSection(): Promise<void> {
    this.showAddSection = false;

    this.newSection = new CourseSection(
      null,
      null,
      null,
      null,
      null,
      null,
      [],
      null
    );
    if (!this.course.Id) {
      const id = this.activeRoute.snapshot.params.id as string;
      if (!id) {
        // this.router.navigate(['/course', 'manage', id]);
      } else {
        this.course = await this.courseService.getBySlug(id).toPromise();
      }
    }
    this.newSection.Course = this.course;
    this.listOfManageMenu = this.course.Sections?.map((f) => false);
  }

  openSectionSection(open): void {
    if (!open) {
      this.initSection();
    }
    this.showAddSection = open;
  }

  async onAddSection(e): Promise<void> {
    e.preventDefault();
    try {
      this.isLoading = true;
      this.newSection.Position = this.course?.Sections?.length + 1;
      console.log('section : ', this.newSection);
      if (this.newSection.Title) {
        const response = await this.courseSectionService
          .add(this.newSection)
          .toPromise();
        if (response && response.Id) {
          const message = await this.translateService
            .get('home.register.success')
            .toPromise();
          this.initSection();
          const course = await this.courseService
            .getBySlug(this.course.Slug)
            .toPromise();
          this.emitCourse.emit(course);
        } else {
          const message = await this.translateService
            .get('home.register.error')
            .toPromise();
          this.toastr.error(message, 'Brain-maker');
        }
      }
    } catch (e) {
      console.log(e);
      const message = await this.translateService
        .get('home.register.error')
        .toPromise();
      this.toastr.error(message, 'Brain-maker');
    } finally {
      this.isLoading = false;
    }
  }

  onCreateContent(
    e,
    section: CourseSection,
    courseContent: CourseContent = null,
    ind: number = null
  ): void {
    e.preventDefault();
    if (ind > -1) {
      this.onManageMenu(null, ind);
    }
    this.utilsService.buildModal(
      CoursesManageContentModalComponent,
      (changed) => {
        if (changed) {
          this.courseService
            .getBySlug(this.course.Slug)
            .subscribe((course) => this.emitCourse.emit(course));
        }
      },
      700,
      true,
      {
        section,
        courseContent,
        course: this.course,
        user: this.user
      }
    );
  }

  onManageMenu(e, ind: number): void {
    if (e) {
      e.preventDefault();
    }
    const lastValue: boolean = this.listOfManageMenu[ind];
    this.listOfManageMenu = this.listOfManageMenu.map((f) => false);
    this.listOfManageMenu[ind] = !lastValue;
  }

  onEditSection(e, section: CourseSection, ind: number): void {
    e.preventDefault();
    if (ind > -1) {
      this.onManageMenu(null, ind);
    }
    this.utilsService.buildModal(
      CoursesManageEditSectionComponent,
      (changed) => {
        if (changed) {
          this.courseService
            .getBySlug(this.course.Slug)
            .subscribe((course) => this.emitCourse.emit(course));
        }
      },
      700,
      true,
      {
        section,
        user: this.user
      }
    );
  }

  onCreateQuiz(
    e,
    section: CourseSection,
    courseSectionTest: CourseSectionTest = null,
    ind: number
  ): void {
    e.preventDefault();
    if (ind > -1) {
      this.onManageMenu(null, ind);
    }
    this.utilsService.buildModal(
      CoursesManageQuizModalComponent,
      (changed) => {
        if (changed) {
          this.courseService
            .getBySlug(this.course.Slug)
            .subscribe((course) => this.emitCourse.emit(course));
        }
      },
      700,
      true,
      {
        section,
        courseSectionTest,
        user: this.user
      }
    );
  }

  async onUpdateContent(courseContent: CourseContent): Promise<void> {
    const course = await this.courseService
      .getBySlug(this.course.Slug)
      .toPromise();
    this.emitCourse.emit(course);
  }

  async onUpdateQuiz(courseSectionTest: CourseSectionTest): Promise<void> {
    const course = await this.courseService
      .getBySlug(this.course.Slug)
      .toPromise();
    this.emitCourse.emit(course);
  }

  isRightType(section: CourseSection, type: number = null): boolean {
    return section.Contents && section.Contents.length > 0
      ? section.Contents[0].Type === type
      : true;
  }

  onCreateAssignment(
    e,
    section: CourseSection,
    courseContent: CourseAssignment = null,
    ind: number
  ): void {
    e.preventDefault();
    if (ind > -1) {
      this.onManageMenu(null, ind);
    }
    this.utilsService.buildModal(
      CourseManageAssignmentModalComponent,
      (changed) => {
        if (changed) {
          this.courseService
            .getBySlug(this.course.Slug)
            .subscribe((course) => this.emitCourse.emit(course));
        }
      },
      700,
      true,
      {
        section,
        courseContent,
        user: this.user
      }
    );
  }
  async onUpdateAssignment(courseAssignment: CourseAssignment): Promise<void> {
    const course = await this.courseService
      .getBySlug(this.course.Slug)
      .toPromise();
    this.emitCourse.emit(course);
  }

  onDeleteSection(
    $event: MouseEvent,
    section: CourseSection,
    ind: number
  ): void {
    try {
      this.isLoading = true;
      if (confirm('do you  really want to delete this?')) {
        this.courseSectionService.delete(section).subscribe((res) => {
          this.course.Sections.splice(ind, 1);
        });
      }
    } catch (e) {
      console.log(e);
    } finally {
      this.isLoading = false;
    }
  }
}
