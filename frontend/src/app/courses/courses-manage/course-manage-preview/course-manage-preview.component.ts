import { Component, Input, OnInit } from '@angular/core';
import { Course } from '~/app/models/course.model';
import { UtilsService } from '~/app/services/utils.service';
import { Media } from '~/app/models/media.model';
import { environment } from '~/environments/environment';

@Component({
  selector: 'app-course-manage-preview',
  templateUrl: './course-manage-preview.component.html',
  styleUrls: ['./course-manage-preview.component.scss']
})
export class CourseManagePreviewComponent implements OnInit {
  @Input()
  course: Course;

  constructor() { }

  ngOnInit(): void {
  }

}
