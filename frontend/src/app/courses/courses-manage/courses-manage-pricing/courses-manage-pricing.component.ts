import { Component, Input, OnInit, EventEmitter, Output } from '@angular/core';
import { Course } from '~/app/models/course.model';
import { User } from '~/app/models/user.model';
import { UtilsService } from '~/app/services/utils.service';
import { CategoryService } from '~/app/services/categories/category.service';
import { UserService } from '~/app/services/users/user.service';
import { TranslateService } from '@ngx-translate/core';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { CourseService } from '~/app/services/courses/course.service';
import { E } from '@angular/cdk/keycodes';

@Component({
	selector: 'app-courses-manage-pricing',
	templateUrl: './courses-manage-pricing.component.html',
	styleUrls: ['./courses-manage-pricing.component.scss']
})
export class CoursesManagePricingComponent implements OnInit {
	@Input()
	course: Course;

	@Input()
	user: User;

	isLoading: boolean;

	@Output() onNextTab = new EventEmitter<void>();

	courseCurrencies: {
		symbol: string;
		name: string;
		symbol_native: string;
		decimal_digits: number;
		rounding: number;
		code: string;
		name_plural: string;
	}[];
	constructor(
		private utilsService: UtilsService,
		private categoryService: CategoryService,
		private userService: UserService,
		private translateService: TranslateService,
		private toastr: ToastrService,
		private courseService: CourseService,
		private router: Router
	) {}

	async ngOnInit(): Promise<void> {
		// this.courseCurrencies = await this.utilsService.getCurrency().toPromise();
		this.course.Currency = {
			symbol: 'CA$',
			name: 'Canadian Dollar',
			symbol_native: '$',
			decimal_digits: 2,
			rounding: 0,
			code: 'CAD',
			name_plural: 'Canadian dollars'
		}.symbol;
	}

	async onSubmit(): Promise<void> {
		try {
			this.isLoading = true;
			// this.course.Currency = this.courseCurrencySelect.code;
			let course: Course;
			if (this.course.Id && this.course.Id > 0) {
				course = await this.courseService
					.edit({ ...this.course, CoverImage: null })
					.toPromise();
			} else {
				course = await this.courseService.add(this.course).toPromise();
			}

			if (course && course.Id) {
				const message = await this.translateService
					.get('home.register.success')
					.toPromise();
				this.router.navigate([`/courses/manage/${course.Slug}`]).then(() => {
					this.onNextTab.emit();
				});
				this.toastr.success(message, 'Brain-maker');
			} else {
				const message = await this.translateService
					.get('home.register.error')
					.toPromise();
				this.toastr.error(message, 'Brain-maker');
			}
		} catch (e) {
			console.log(e);
			const message = await this.translateService
				.get('home.register.error')
				.toPromise();
			this.toastr.error(message, 'Brain-maker');
		} finally {
			this.isLoading = false;
		}
	}
}
