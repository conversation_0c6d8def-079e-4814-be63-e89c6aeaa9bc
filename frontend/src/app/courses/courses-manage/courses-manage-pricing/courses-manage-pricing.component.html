<form action="" (ngSubmit)="onSubmit()">
	<div class="row mt-4">
		<div class="col-12">
			<!--hr-->
		</div>
	</div>
	<div class="row">
		<div class="col-12 mt-2">
			{{ 'course.create.priceLevel' | translate }}
		</div>

		<div class="alert alert-dark" role="alert">
			{{ 'course.create.priceLevelMsg1' | translate }}

			<strong class="">
				{{ 'course.create.priceLevelMsg2' | translate }}
			</strong>
		</div>
		<div class="col-12 p-fluid p-formgrid mt-4">
			<div class="form-group d-flex">
				<p-checkbox
					id="course-free"
					name="course-free"
					class="mr-2"
					[(ngModel)]="course.Free"
					[binary]="true"
				></p-checkbox>
				<label for="course-free" class="w-100 mt-2">{{
					'course.create.free' | translate
				}}</label>
			</div>
		</div>
		<div class="col-12 p-fluid p-formgrid row" *ngIf="!course.Free">
			<!-- <div class="p-field form-group p-grid col-12 col-md-2">
                <label for="currency">{{'course.create.currency'|translate}}</label>
                <select id="currency" class="form-control" [(ngModel)]="course.Currency" required="" name="currency">
                  <option [value]="c.symbol" *ngFor="let c of courseCurrencies"> {{c.code}}</option>
                </select>
            </div> -->
			<div class="p-field form-group col-12 col-md-5">
				<label for="newPrice">{{
					'course.create.step5.phPrice' | translate
				}}</label>
				<div class="input-group">
					<div class="input-group-prepend">
						<span class="input-group-text" id="basic-addon1">CA$</span>
					</div>
					<input
						id="newPrice"
						name="newPrice"
						[(ngModel)]="course.Price"
						required=""
						min="7"
						class="form-control"
						type="number"
					/>
				</div>
			</div>
			<div class="p-field form-group col-12 col-md-5">
				<label for="lastPrice">{{
					'course.create.promoPrice' | translate
				}}</label>
				<div class="input-group">
					<div class="input-group-prepend">
						<span class="input-group-text" id="basic-addon1">CA$</span>
					</div>
					<input
						id="lastPrice"
						name="lastPrice"
						class="form-control"
						[(ngModel)]="course.NewPrice"
						type="number"
					/>
				</div>
			</div>
		</div>
		<div class="col-12 form-group mt-2 d-flex">
			<button type="submit" class="btn btn-dark">
				{{ 'course.comments.save' | translate }}
			</button>

			<div
				class="mt-2 d-flex align-items-center justify-content-center"
				*ngIf="isLoading"
			>
				<i class="fas fa-spin fa-spinner mr-2"></i>
				{{ 'home.loading' | translate }}
			</div>
		</div>
	</div>
</form>
