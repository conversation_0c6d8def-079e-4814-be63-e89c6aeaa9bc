<form (ngSubmit)="onSubmit()">
	<div class="row mb-4">
		<div class="col-12">
			<div class="alert alert-primary" role="alert">
				<i class="fas fa-info-circle mr-2"></i>
				{{ 'course.create.infoCourse' | translate }}
			</div>
			<!--hr-->
		</div>
	</div>
	<div class="p-fluid row">
		<div class="p-field form-group col-12">
			<label for="title">{{ 'course.create.courseTitle' | translate }} </label>
			<input
				id="title"
				type="text"
				[ngClass]="{ 'is-invalid': errors && isNotvalid('Title') }"
				name="title"
				required=""
				[(ngModel)]="course.Title"
				maxlength="100"
				pInputText
			/>
			<div class="invalid-feedback">
				<ul *ngIf="errors && isNotvalid('Title')">
					<li class="" *ngFor="let item of getAllError('Title')">
						{{ item }}
					</li>
				</ul>
			</div>
			<small id="title-help"></small>
		</div>

		<div class="p-field form-group col-12">
			<label for="title"
				>{{ 'course.create.selectCourseFormat' | translate }}
			</label>

			<p-dropdown
				[options]="formats"
				[disabled]="haveContent"
				required=""
				[ngClass]="{ 'is-invalid': errors && isNotvalid('Format') }"
				[(ngModel)]="course.Format"
				[showClear]="true"
				id="formatCourse"
				name="formatCourse"
				[placeholder]="'course.create.selectCourseFormat' | translate"
				optionValue="id"
				optionLabel="title"
			></p-dropdown>
			<div class="invalid-feedback">
				<ul *ngIf="errors && isNotvalid('Format')">
					<li class="" *ngFor="let item of getAllError('Format')">
						{{ item }}
					</li>
				</ul>
			</div>
			<small id="title-helpcourse"></small>
		</div>

		<div class="p-field form-group col-12">
			<label for="resume">{{ 'course.create.courseDesc' | translate }} </label>
			<!--angular-editor required="" [ngClass]="{ 'is-invalid': errors && isNotvalid('Resume') }"
                            [(ngModel)]="course.Resume" name="resume"
                            id="resume" [config]="resumeConfig">
            </angular-editor-->

			<ckeditor
				class="form-control ckeditor"
				type="inline"
				required=""
				[ngClass]="{ 'is-invalid': errors && isNotvalid('Resume') }"
				[(ngModel)]="course.Resume"
				name="resume"
				id="resume"
			></ckeditor>
			<div class="invalid-feedback">
				<ul *ngIf="errors && isNotvalid('Resume')">
					<li class="" *ngFor="let item of getAllError('Resume')">
						{{ item }}
					</li>
				</ul>
			</div>
			<small id="resume-help"></small>
		</div>

		<div class="p-field form-group col-12">
			<label for="keywords"
				>{{ 'course.create.studentTarget' | translate }}
			</label>
			<p-chips
				id="keywords"
				required=""
				[ngClass]="{ 'is-invalid': errors && isNotvalid('Keywords') }"
				name="keywords"
				placeholder="{{ 'course.create.studentTargetDesc' | translate }}"
				[(ngModel)]="course.Keywords"
				separator=","
				[allowDuplicate]="false"
				[addOnBlur]="true"
			></p-chips>
			<div class="invalid-feedback">
				<ul *ngIf="errors && isNotvalid('Keywords')">
					<li class="" *ngFor="let item of getAllError('Keywords')">
						{{ item }}
					</li>
				</ul>
			</div>
			<small id="keywords-help">{{
				'course.create.pressEnter' | translate
			}}</small>
		</div>
	</div>
	<div class="p-fluid p-formgrid row">
		<div class="p-field col-12 col-md-4">
			<label for="language"> Language</label>
			<p-dropdown
				[options]="courseLanguage"
				required=""
				[ngClass]="{
					'is-invalid': errors && isNotvalid('courseLanguageSelect')
				}"
				[(ngModel)]="courseLanguageSelect"
				(ngModelChange)="onLanguageSelect()"
				[showClear]="true"
				id="language"
				name="language"
				[placeholder]="'course.create.selectALang' | translate"
				optionLabel="name"
			></p-dropdown>
			<div class="invalid-feedback">
				<ul *ngIf="errors && isNotvalid('courseLanguageSelect')">
					<li class="" *ngFor="let item of getAllError('courseLanguageSelect')">
						{{ item }}
					</li>
				</ul>
			</div>
			<small id="language-help">{{
				'course.create.publishedLang' | translate
			}}</small>
		</div>
		<div class="p-field col-12 col-md-4">
			<label for="level">{{ 'course.create.level' | translate }}</label>
			<p-multiSelect
				[options]="courseLevel"
				[ngClass]="{ 'is-invalid': errors && isNotvalid('courseLevelSelect') }"
				[(ngModel)]="courseLevelSelect"
				(ngModelChange)="onLevelSelect()"
				id="level"
				required=""
				name="level"
				defaultLabel="Select a level"
				optionLabel="name"
			></p-multiSelect>
			<div class="invalid-feedback">
				<ul *ngIf="errors && isNotvalid('courseLevelSelect')">
					<li class="" *ngFor="let item of getAllError('courseLevelSelect')">
						{{ item }}
					</li>
				</ul>
			</div>
			<small id="level-help"></small>
		</div>
		<div class="p-field col-12 col-md-4">
			<label for="category">{{
				'course.create.step3.phCategory' | translate
			}}</label>
			<p-multiSelect
				[options]="categories"
				[(ngModel)]="course.Categories"
				[ngClass]="{ 'is-invalid': errors && isNotvalid('Categories') }"
				id="category"
				name="category"
				required=""
				defaultLabel="Select a category"
				optionLabel="Title"
			></p-multiSelect>
			<div class="invalid-feedback">
				<ul *ngIf="errors && isNotvalid('Categories')">
					<li class="" *ngFor="let item of getAllError('Categories')">
						{{ item }}
					</li>
				</ul>
			</div>
			<small id="category-help"></small>
		</div>
	</div>

	<div class="p-fluid row mt-4">
		<div class="p-field form-group col-12">
			<label for="goals">{{ 'course.create.toLearn' | translate }}</label>
			<p-chips
				id="goals"
				[ngClass]="{ 'is-invalid': errors && isNotvalid('Goals') }"
				placeholder=""
				required=""
				name="goals"
				aria-describedby="goals-help"
				[(ngModel)]="course.Goals"
				[allowDuplicate]="false"
				[addOnBlur]="true"
			></p-chips>
			<div class="invalid-feedback">
				<ul *ngIf="errors && isNotvalid('Goals')">
					<li class="" *ngFor="let item of getAllError('Goals')">
						{{ item }}
					</li>
				</ul>
			</div>
			<small id="goals-help">{{
				'course.create.pressEnter' | translate
			}}</small>
		</div>

		<div class="p-field form-group col-12">
			<label for="prerequisites">
				{{ 'course.create.preReqs' | translate }}
			</label>
			<p-chips
				id="prerequisites"
				[ngClass]="{ 'is-invalid': errors && isNotvalid('Prerequisites') }"
				placeholder=""
				name="prerequisites"
				aria-describedby="prerequisites-help"
				[(ngModel)]="course.Prerequisites"
				[allowDuplicate]="false"
				[addOnBlur]="true"
			></p-chips>
			<div class="invalid-feedback">
				<ul *ngIf="errors && isNotvalid('Prerequisites')">
					<li class="" *ngFor="let item of getAllError('Prerequisites')">
						{{ item }}
					</li>
				</ul>
			</div>
			<small id="prerequisites-help">{{
				'course.create.pressEnter' | translate
			}}</small>
		</div>

		<div class="col-12 form-group mt-2 d-flex mr-2">
			<button type="submit" class="btn btn-dark">
				{{ 'course.comments.save' | translate }}
			</button>
			<div
				class="mt-2 d-flex align-items-center justify-content-center"
				*ngIf="isLoading"
			>
				<i class="fas fa-spin fa-spinner mr-2"></i>
				{{ 'home.loading' | translate }}
			</div>
		</div>
	</div>
</form>
