import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import {
	ContentType,
	Course,
	notificationMessages
} from '~/app/models/course.model';
import { Category } from '~/app/models/category.model';
import { AngularEditorConfig } from '@kolkov/angular-editor';
import { validate, ValidationError } from 'class-validator';
import { User, UserRoleType } from '~/app/models/user.model';
import { UtilsService } from '~/app/services/utils.service';
import { CategoryService } from '~/app/services/categories/category.service';
import { TranslateService } from '@ngx-translate/core';
import { UserService } from '~/app/services/users/user.service';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { CourseService } from '~/app/services/courses/course.service';
import { CourseNotificationsService } from '~/app/services/notifications/course-notifications.service';
import {
	CourseNotification,
	courseNotificationType
} from '~/app/models/course-notification.model';
import { BillingService } from '~/app/services/billings/billing.service';

@Component({
	selector: 'app-courses-manage-information',
	templateUrl: './courses-manage-information.component.html',
	styleUrls: ['./courses-manage-information.component.scss']
})
export class CoursesManageInformationComponent implements OnInit {
	@Input() course: Course;

	isloading: boolean;

	@Input()
	user: User;
	haveContent: boolean;
	isLoading: boolean;
	categories: Array<Category>;
	isLinear = false;
	resumeConfig: AngularEditorConfig;
	courseLanguage: { code: string; name: string; nativeName: string }[];
	courseLevel: { code: number; name: string }[];
	courseLanguageSelect: { code: string; name: string; nativeName: string };
	courseLevelSelect: { code: number; name: string }[];

	errors: ValidationError[];
	errorsHelper: ValidationError[];
	formats: { id: ContentType; title: string }[] = [];
	formatSelected: { id: ContentType; title: string };

	@Output() onNextTab = new EventEmitter<void>();

	constructor(
		private utilsService: UtilsService,
		private categoryService: CategoryService,
		private userService: UserService,
		private translateService: TranslateService,
		private toastr: ToastrService,
		private courseService: CourseService,
		private router: Router,
		private courseNotificationService: CourseNotificationsService,
		private billingService: BillingService
	) {}

	async ngOnInit(): Promise<void> {
		this.resumeConfig = this.utilsService.getSimpleconfigAngular();
		this.courseLevel = this.utilsService.getLevel();
		this.categories = await this.categoryService.getAll().toPromise();
		this.courseLanguage = await this.utilsService.getLanguages().toPromise();
		this.formats = [
			{ id: ContentType.PDF, title: 'PDF' },
			{ id: ContentType.EBOOK, title: 'EBOOK' },
			{ id: ContentType.VIDEO, title: 'VIDEO' }
		];

		if (this.course.Id && +this.course.Id > 0) {
			this.courseLanguageSelect = this.courseLanguage?.find(
				(f) => this.course.Language === f.code
			);
		}

		if (this.course.Id && +this.course.Id > 0) {
			this.courseLevelSelect = this.courseLevel?.filter((f) =>
				this.course.Level?.find((g) => +g === +f.code)
			);
		}
		this.haveContent = !!(
			this.course &&
			this.course.Sections &&
			this.course.Sections.find((f) => f.Contents && f.Contents.length > 0)
		);
	}

	onLanguageSelect(): void {
		this.course.Language = this.courseLanguageSelect.code;
	}

	onLevelSelect(): void {
		this.course.Level = this.courseLevelSelect.map((f) => f.code);
		console.log(this.course.Level);
	}

	async onSubmit(): Promise<void> {
		this.errors = await validate(this.course);
		this.errorsHelper = this.errors;
		console.log('errors', this.errors);

		if (
			!this.errors ||
			(this.errors.length === 0 &&
				this.course.Categories &&
				this.course.Categories?.length > 0 &&
				this.course.Level &&
				this.course.Level?.length > 0 &&
				this.course.Keywords &&
				this.course.Keywords.length > 0)
		) {
			try {
				this.isLoading = true;
				let course: Course;
				if (this.course.Id && this.course.Id > 0) {
					course = await this.courseService
						.edit({ ...this.course, CoverImage: null })
						.toPromise();
				} else {
					course = await this.courseService.add(this.course).toPromise();
				}

				if (course && course.Id) {
					const message = await this.translateService
						.get('home.register.success')
						.toPromise();

					try {
						console.log('*** Envoie de la notification');
						const res = await this.courseNotificationService
							.add(
								new CourseNotification(
									null,
									null,
									null,
									null,
									notificationMessages.NEW_COURSE_CREATED,
									course,
									false,
									[],
									courseNotificationType.COURSE_CREATE
								)
							)
							.toPromise();

						// Si le user n'est pas admin et n'est pas encore instructor, on le met instructor
						if (this.user.Role == UserRoleType.USER) {
							try {
								await this.userService
									.editRole(this.user.Slug, {
										Role: UserRoleType.INSTRUCTOR
									})
									.toPromise();
								this.toastr.success(
									'Vous ete desormais un instructeur',
									'Brain-Maker',
									{ timeOut: 10000 }
								);
							} catch (error) {
								this.toastr.error(
									'Une erreur est survenu dans la mise à jour de votre statut',
									'Brain-Maker',
									{ timeOut: 10000 }
								);
							}
						}

						// Vérification ou initialisation du système de facturation
						const result = await this.billingService
							.InitBillingForInstructor(this.user.Slug)
							.toPromise();
					} catch (error) {
						console.log(error);
					}

					this.toastr.success(message, 'Brain-maker');
					this.router.navigate([`/courses/manage/${course.Slug}`]).then(() => {
						this.onNextTab.emit();
					});
				} else {
					const message = await this.translateService
						.get('home.register.error')
						.toPromise();
					this.toastr.error(message, 'Brain-maker');
				}
			} catch (e) {
				console.log(e);
				const message = await this.translateService
					.get('home.register.error')
					.toPromise();
				this.toastr.error(message, 'Brain-maker');
			} finally {
				this.isLoading = false;
			}
		} else {
			const message = await this.translateService
				.get('language.message')
				.toPromise();
			this.toastr.error(message, 'Brain-maker');
		}
	}

	onFormatSelect(): void {
		alert(this.course.Format);
		this.course.Format = this.formatSelected.id;
	}
	async onItemChange(): Promise<void> {
		this.errorsHelper = await validate(this.course);
	}

	isNotvalid(name: string): boolean {
		return this.utilsService.isNotvalid(name, this.errorsHelper);
	}

	getAllError(name: string): string[] {
		return this.utilsService.getAllError(name, this.errorsHelper);
	}
}
