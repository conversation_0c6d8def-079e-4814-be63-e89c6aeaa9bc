import { Component, Input, OnInit, EventEmitter, Output } from '@angular/core';
import { Course } from '~/app/models/course.model';
import { User } from '~/app/models/user.model';
import { validate } from 'class-validator';
import { UtilsService } from '~/app/services/utils.service';
import { CategoryService } from '~/app/services/categories/category.service';
import { UserService } from '~/app/services/users/user.service';
import { TranslateService } from '@ngx-translate/core';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { CourseService } from '~/app/services/courses/course.service';

@Component({
	selector: 'app-courses-manage-messaging',
	templateUrl: './courses-manage-messaging.component.html',
	styleUrls: ['./courses-manage-messaging.component.scss']
})
export class CoursesManageMessagingComponent implements OnInit {
	@Input()
	course: Course;
	isLoading: boolean;

	@Input()
	user: User;

	@Output() onNextTab = new EventEmitter<void>();
	constructor(
		private utilsService: UtilsService,
		private categoryService: CategoryService,
		private userService: UserService,
		private translateService: TranslateService,
		private toastr: ToastrService,
		private courseService: CourseService,
		private router: Router
	) {}

	async onSubmit(): Promise<void> {
		try {
			this.isLoading = true;
			let course: Course;
			if (this.course.Id && this.course.Id > 0) {
				course = await this.courseService
					.edit({ ...this.course, CoverImage: null })
					.toPromise();
			} else {
				course = await this.courseService.add(this.course).toPromise();
			}

			if (course && course.Id) {
				const message = await this.translateService
					.get('home.register.success')
					.toPromise();
				this.router.navigate([`/courses/manage/${course.Slug}`]).then(() => {
					this.onNextTab.emit();
				});
				this.toastr.success(message, 'Brain-maker');
			} else {
				const message = await this.translateService
					.get('home.register.error')
					.toPromise();
				this.toastr.error(message, 'Brain-maker');
				this.course = course;
			}
		} catch (e) {
			console.log(e);
			const message = await this.translateService
				.get('home.register.error')
				.toPromise();
			this.toastr.error(message, 'Brain-maker');
		} finally {
			this.isLoading = false;
		}
	}

	ngOnInit(): void {}
}
