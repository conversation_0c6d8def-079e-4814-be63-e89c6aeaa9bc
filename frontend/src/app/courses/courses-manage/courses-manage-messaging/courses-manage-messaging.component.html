<form action="" (ngSubmit)="onSubmit()">
	<div class="row">
		<div class="col-12">
			<div class="h5">{{ 'course.create.courseMessages' | translate }}</div>
			<hr />
		</div>

		<div class="my-4 col-12"></div>

		<div class="col-12 form-group">
			<label for="message">{{
				'course.create.messageWelcome' | translate
			}}</label>
			<textarea
				class="form-control"
				id="message"
				name="message"
				placeholder="{{
					'course.create.messageWelcomePlaceholder' | translate
				}}"
				[(ngModel)]="course.Message"
				maxlength="250"
				rows="5"
			></textarea>
		</div>

		<div class="col-12 form-group">
			<label for="message">{{
				'course.create.messageCongrats' | translate
			}}</label>
			<textarea
				class="form-control"
				id="congratulation"
				placeholder="{{
					'course.create.messageCongratsPlaceholder' | translate
				}}"
				[(ngModel)]="course.Congratulation"
				name="congratulation"
				maxlength="250"
				rows="5"
			></textarea>
		</div>

		<div class="col-12 form-group mt-2 d-flex">
			<button type="submit" class="btn btn-dark">
				{{ 'course.comments.save' | translate }}
			</button>

			<div
				class="mt-2 d-flex align-items-center justify-content-center"
				*ngIf="isLoading"
			>
				<i class="fas fa-spin fa-spinner mr-2"></i>
				{{ 'home.loading' | translate }}
			</div>
		</div>
	</div>
</form>
