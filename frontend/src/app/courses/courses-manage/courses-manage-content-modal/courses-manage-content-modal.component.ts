import { Component, Inject, OnInit } from '@angular/core';
import { UtilsService } from '~/app/services/utils.service';
import { CategoryService } from '~/app/services/categories/category.service';
import { TranslateService } from '@ngx-translate/core';
import { UserService } from '~/app/services/users/user.service';
import { ToastrService } from 'ngx-toastr';
import { validate, ValidationError } from 'class-validator';
import { LY_DIALOG_DATA, LyDialogRef } from '@alyle/ui/dialog';
import { CourseSection } from '~/app/models/course-section.model';
import { CourseContent } from '~/app/models/course-content.model';
import { AngularEditorConfig } from '@kolkov/angular-editor';
import { ContentType, Course } from '~/app/models/course.model';
import { CourseTranscription } from '~/app/models/course-transcription.model';
import { User } from '~/app/models/user.model';
import { CourseContentService } from '~/app/services/courses/course-content.service';

@Component({
	selector: 'app-courses-manage-content-modal',
	templateUrl: './courses-manage-content-modal.component.html',
	styleUrls: ['./courses-manage-content-modal.component.scss']
})
export class CoursesManageContentModalComponent implements OnInit {
	user: User;
	courseContent: CourseContent;
	section: CourseSection;
	course: Course;
	contentConfig: AngularEditorConfig;

	constructor(
		private utilsService: UtilsService,
		private categoryService: CategoryService,
		private translateService: TranslateService,
		private userService: UserService,
		public dialogRef: LyDialogRef,
		@Inject(LY_DIALOG_DATA)
		private data: {
			section: CourseSection;
			course: Course;
			courseContent: CourseContent;
			user: User;
		},
		private toastr: ToastrService,
		private courseContentService: CourseContentService
	) {}

	async ngOnInit(): Promise<void> {
		this.contentConfig = this.utilsService.getSimpleconfigAngular();
		this.section = this.data.section;
		this.course = this.data.course;
		this.user = this.data.user;

		if (this.data.courseContent) {
			this.courseContent = this.data.courseContent;
			this.courseContent.Section = this.data.section;
		} else {
			this.courseContent = new CourseContent();
			this.courseContent.Section = this.data.section;
		}
		this.courseContent.CreatedByISlug = this.data.user.Slug;
		// console.log('content : ', this.courseContent);
	}

	close(e): void {
		if (e) {
			e.preventDefault();
		}
		this.dialogRef.close();
	}

	onUpdateContent(courseContent: CourseContent): void {
		this.dialogRef.close(true);
	}
}
