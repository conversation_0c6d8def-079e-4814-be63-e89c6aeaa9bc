<form autocomplete="off" (ngSubmit)="onSubmit($event)" *ngIf="courseAssignment">
    <div class="row">
        <div class="col-12 p-field form-group">
            <label for="courseContentTitle">{{'course.create.titleContent'|translate}}</label>
            <input type="text" id="courseContentTitle" name="courseContentTitle" required="" [(ngModel)]="courseAssignment.Title" class="form-control">
        </div>
        <div class="col-12 my-2 mx-0" *ngIf="!showOption">
            <button class="btn btn-sm btn-light" (click)="showOption = !showOption">{{'course.create.changeType'|translate}}</button>
        </div>


        <div class="p-fluid col-12 form-group ">
            <input #_fileInputCourseSection type="file" multiple (change)="openFile($event)" hidden>
            <label for="PDFCourseSection">
        {{ 'instructor.manageCourse.lblVideoDesc' | translate }}
      </label>
            <div class="p-inputgroup" (click)="_fileInputCourseSection.click()">
                <input type="text" disabled id="PDFCourseSection" name="PDFCourseSection" [value]="getName()" pInputText [placeholder]="'course.create.fileName'|translate">
                <button type="button" pButton pRipple class="p-button-raised p-button-secondary p-button-text" label="Upload file"></button>
            </div>
        </div>

        <div class="col-12 form-group">
            <ul class="list-group" *ngIf="courseAssignment.Medias">
                <li *ngFor="let media of courseAssignment.Medias; let i= index" class="list-group-item d-flex justify-content-between align-items-center">
                    <div>
                        <a [href]="media.Hashname" class="btn btn-link" target="_blank"> {{media.Name}} </a>
                    </div>
                    <button class="btn btn-sm btn-light text-dark" (click)="onDeleteMedia($event, i, media)">
            {{'course.comments.delete'|translate}}
          </button>
                </li>
            </ul>
        </div>


        <div class="col-12 p-field form-group">
            <label for="courseContentDuration">{{'course.create.durationContent'|translate}} (minutes)</label>
            <input type="number" id="courseContentDuration" name="courseContentDuration" required="" [(ngModel)]="courseAssignment.TotalTime" class="form-control">
        </div>
        <div class="col-12 d-flex form-group mt-2">
            <button type="submit" class="btn" [ngClass]="{'btn-dark':modal, ' btn-light btn-sm text-dark':!modal}">
        {{'course.details.save'|translate}}
      </button>

            <button type="button" (click)="onDeleteContent($event)" *ngIf="!modal" class="btn btn-sm btn-light text-dark ml-2">{{'course.comments.delete'|translate}}</button>
            <div class="mt-2 d-flex align-items-center justify-content-center" *ngIf="isLoading">

                <i class="fas fa-spin fa-spinner mr-2"></i> {{ 'home.loading' | translate }}
            </div>
        </div>
    </div>
</form>