import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { validate, ValidationError } from 'class-validator';
import { CourseAssignment } from '~/app/models/course-assignment.model';
import { User } from '~/app/models/user.model';
import { CourseSection } from '~/app/models/course-section.model';
import { AngularEditorConfig } from '@kolkov/angular-editor';
import { ContentType } from '~/app/models/course.model';
import { UtilsService } from '~/app/services/utils.service';
import { ToastrService } from 'ngx-toastr';
import { TranslateService } from '@ngx-translate/core';
import { Media } from '~/app/models/media.model';
import { MediaService } from '~/app/services/medias/media.service';
import { CourseAssignmentService } from '~/app/services/courses/course-assignment.service';

@Component({
	selector: 'app-course-manage-assignment-factor',
	templateUrl: './course-manage-assignment-factor.component.html',
	styleUrls: ['./course-manage-assignment-factor.component.scss']
})
export class CourseManageAssignmentFactorComponent implements OnInit {
	errors: ValidationError[];
	errorsHelper: ValidationError[];
	isLoading: boolean;
	@Input()
	courseAssignment: CourseAssignment;
	@Input()
	user: User;
	@Input()
	section: CourseSection;
	contentType = ContentType;
	contentConfig: AngularEditorConfig;
	showOption: boolean;
	courseLanguage: { code: string; name: string; nativeName: string }[];

	@Input()
	modal: boolean;

	@Output()
	updateCourse: EventEmitter<CourseAssignment> = new EventEmitter<CourseAssignment>();
	uploadedFiles: any[] = [];

	constructor(
		private utilsService: UtilsService,
		private toastr: ToastrService,
		private translateService: TranslateService,
		private mediaService: MediaService,
		private courseAssignmentService: CourseAssignmentService
	) {}

	async ngOnInit(): Promise<void> {
		this.contentConfig = this.utilsService.getSimpleconfigAngular();
		this.courseLanguage = await this.utilsService.getLanguages().toPromise();
		this.showOption = this.modal;
		this.courseAssignment.CreatedByISlug = this.user.Slug;
		this.courseAssignment.Type = ContentType.ASSIGNMENT;
		console.log('assignment : ', this.courseAssignment);
	}

	async openFile(e): Promise<void> {
		const files = e.target.files as File[];

		for (const file of files) {
			const media = await this.utilsService.convertToBase64(file);
			this.courseAssignment.Medias = [...this.courseAssignment.Medias, media];
		}
	}

	async onSubmit(e): Promise<void> {
		e.preventDefault();
		try {
			this.isLoading = true;
			if (this.courseAssignment.Title) {
				let response: CourseAssignment;
				if (this.courseAssignment.Id && +this.courseAssignment.Id > 0) {
					response = await this.courseAssignmentService
						.edit(this.courseAssignment)
						.toPromise();
				} else {
					this.courseAssignment.Position =
						this.section.Contents && this.section.Contents.length > 0
							? this.section.Contents.length + 1
							: 1;
					console.log('coursecontent : ', this.courseAssignment);
					response = await this.courseAssignmentService
						.add(this.courseAssignment)
						.toPromise();
				}

				if (response && response.Id) {
					const message = await this.translateService
						.get('home.register.success')
						.toPromise();
					this.updateCourse.emit(this.courseAssignment);
				} else {
					const message = await this.translateService
						.get('home.register.error')
						.toPromise();
					this.toastr.error(message, 'Brain-maker');
				}
			} else {
				const message = await this.translateService
					.get('home.register.error')
					.toPromise();
				this.toastr.error(message, 'Brain-maker');
			}
		} catch (e) {
			console.log(e);
			const message = await this.translateService
				.get('home.register.error')
				.toPromise();
			this.toastr.error(message, 'Brain-maker');
		} finally {
			this.isLoading = false;
		}
	}

	async onItemChange(): Promise<void> {
		this.errorsHelper = await validate(this.courseAssignment);
	}

	isNotvalid(name: string): boolean {
		return this.utilsService.isNotvalid(name, this.errorsHelper);
	}

	getAllError(name: string): string[] {
		return this.utilsService.getAllError(name, this.errorsHelper);
	}

	onDelete($event: MouseEvent, ind: number): void {
		this.courseAssignment.Transcriptions.splice(ind, 1);
	}

	async onDeleteAssignment(e): Promise<void> {
		e.preventDefault();
		if (confirm('do you really want to delete this ')) {
			const c = await this.courseAssignmentService
				.delete(this.courseAssignment)
				.toPromise();
			this.updateCourse.emit(this.courseAssignment);
		}
	}

	async onDeleteContent(e): Promise<void> {
		e.preventDefault();
		if (confirm('do you really want to delete this ')) {
			const c = await this.courseAssignmentService
				.delete(this.courseAssignment)
				.toPromise();
			this.updateCourse.emit(this.courseAssignment);
		}
	}

	getName(): string {
		return null;
	}

	async onDeleteMedia(e, i: number, media: Media): Promise<void> {
		this.courseAssignment.Medias.splice(i, 1);
		const y = await this.courseAssignmentService
			.edit(this.courseAssignment)
			.toPromise();
		const x = await this.mediaService.delete(media).toPromise();
		this.updateCourse.emit(this.courseAssignment);
	}
}
