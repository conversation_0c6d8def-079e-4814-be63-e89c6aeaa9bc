import { ComponentFixture, TestBed } from '@angular/core/testing';

import { CourseManageAssignmentFactorComponent } from './course-manage-assignment-factor.component';

describe('CourseManageAssignmentFactorComponent', () => {
  let component: CourseManageAssignmentFactorComponent;
  let fixture: ComponentFixture<CourseManageAssignmentFactorComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ CourseManageAssignmentFactorComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(CourseManageAssignmentFactorComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
