import {
	Component,
	Inject,
	Input,
	OnInit,
	EventEmitter,
	Output
} from '@angular/core';
import { validate, ValidationError } from 'class-validator';
import {
	CourseSectionTest,
	QuestionType
} from '~/app/models/course-section-test.model';
import { UtilsService } from '~/app/services/utils.service';
import { TranslateService } from '@ngx-translate/core';
import { UserService } from '~/app/services/users/user.service';
import { User } from '~/app/models/user.model';
import { ToastrService } from 'ngx-toastr';
import { ContentType, Course } from '~/app/models/course.model';
import { CourseQuestion } from '~/app/models/course-question.model';
import { CourseQuestionService } from '~/app/services/courses/course-question.service';
import { CourseSectionTestService } from '~/app/services/courses/course-section-test.service';

@Component({
	selector: 'app-courses-manage-quiz-factor',
	templateUrl: './courses-manage-quiz-factor.component.html',
	styleUrls: ['./courses-manage-quiz-factor.component.scss']
})
export class CoursesManageQuizFactorComponent implements OnInit {
	errors: ValidationError[];
	errorsHelper: ValidationError[];
	isLoading: boolean;
	@Input()
	courseSectionTest: CourseSectionTest;
	questionType = QuestionType;
	oneChooseValue: string;
	@Input()
	user: User;

	@Input()
	modal: boolean;

	@Output()
	updateCourse: EventEmitter<CourseSectionTest> = new EventEmitter<CourseSectionTest>();

	constructor(
		private utilsService: UtilsService,

		private translateService: TranslateService,
		private userService: UserService,
		private courseQuestionService: CourseQuestionService,
		private utilService: UtilsService,
		private toastr: ToastrService,
		private courseSectionTestService: CourseSectionTestService
	) {}

	async ngOnInit(): Promise<void> {
		this.courseSectionTest.Type = ContentType.QUIZ;
		this.courseSectionTest.CreatedByISlug = this.user.Slug;

		this.setChecked();
	}

	setChecked(): void {
		if (
			(this.courseSectionTest.Questions &&
				this.courseSectionTest.Questions.length > 0 &&
				+this.courseSectionTest.TypeOfTest === QuestionType.SELECT) ||
			+this.courseSectionTest.TypeOfTest === QuestionType.RADIO
		) {
			this.oneChooseValue = this.courseSectionTest.Questions.find(
				(f) => f.IsRightAnswer
			)?.Slug.substr(0, 5);
			// console.log(this.oneChooseValue);
			// console.log('courseSectionTest : ', this.courseSectionTest);
		}
	}

	async onSubmit(e): Promise<void> {
		e.preventDefault();
		try {
			this.isLoading = true;
			if (
				(this.courseSectionTest.Questions &&
					this.courseSectionTest.Questions.length > 0 &&
					+this.courseSectionTest.TypeOfTest === QuestionType.SELECT) ||
				+this.courseSectionTest.TypeOfTest === QuestionType.RADIO
			) {
				this.courseSectionTest.Questions = this.courseSectionTest.Questions.map(
					(f) => {
						return {
							...f,
							IsRightAnswer: f.Slug.substr(0, 5) === this.oneChooseValue
						};
					}
				);
				console.log('selected item : ', this.oneChooseValue);
				console.log('content :', this.courseSectionTest);
			}

			if (this.courseSectionTest.Title) {
				let response: CourseSectionTest;

				if (this.courseSectionTest.Id) {
					response = await this.courseSectionTestService
						.edit(this.courseSectionTest)
						.toPromise();
				} else {
					response = await this.courseSectionTestService
						.add(this.courseSectionTest)
						.toPromise();
				}

				if (response && response.Id) {
					const message = await this.translateService
						.get('home.register.success')
						.toPromise();
					this.updateCourse.emit(this.courseSectionTest);
				} else {
					const message = await this.translateService
						.get('home.register.error')
						.toPromise();
					this.toastr.error(message, 'Brain-maker');
				}
			} else {
				const message = await this.translateService
					.get('home.register.error')
					.toPromise();
				this.toastr.error(message, 'Brain-maker');
			}
		} catch (e) {
			console.log(e);
			const message = await this.translateService
				.get('home.register.error')
				.toPromise();
			this.toastr.error(message, 'Brain-maker');
		} finally {
			this.isLoading = false;
		}
	}

	async onItemChange(): Promise<void> {
		this.errorsHelper = await validate(this.courseSectionTest);
	}

	isNotvalid(name: string): boolean {
		return this.utilsService.isNotvalid(name, this.errorsHelper);
	}

	getAllError(name: string): string[] {
		return this.utilsService.getAllError(name, this.errorsHelper);
	}

	addNewQuestion(e): void {
		e.preventDefault();
		this.courseSectionTest.Questions.push(
			new CourseQuestion(
				null,
				this.utilsService.getUniqueId(),
				null,
				null,
				null,
				false,
				null
			)
		);
	}

	async onDelete(e, i: number): Promise<void> {
		e.preventDefault();
		try {
			this.isLoading = true;
			const question = this.courseSectionTest.Questions[i];
			this.courseSectionTest.Questions.splice(i, 1);
			if (question.Id) {
				const q = await this.courseQuestionService.delete(question).toPromise();
			}
		} catch (e) {
			console.log(e);
		} finally {
			this.isLoading = false;
		}
	}

	async onDeleteContent(e): Promise<void> {
		e.preventDefault();
		if (confirm('do you really want to delete this ')) {
			const c = await this.courseSectionTestService
				.delete(this.courseSectionTest)
				.toPromise();
			this.updateCourse.emit(this.courseSectionTest);
		}
	}
}
