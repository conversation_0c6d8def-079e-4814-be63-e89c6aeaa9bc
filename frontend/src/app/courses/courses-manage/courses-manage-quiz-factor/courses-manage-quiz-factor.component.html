<form (ngSubmit)="onSubmit($event)" *ngIf="courseSectionTest">
	<div class="row">
		<div class="form-group col-12">
			<label for="questionTitle">{{
				'course.create.yourQuestion' | translate
			}}</label>
			<input
				id="questionTitle"
				[(ngModel)]="courseSectionTest.Title"
				required=""
				class="form-control"
				name="courseSectionTest.Title"
				type="text"
			/>
		</div>
		<div class="col-12 p-field form-group">
			<label for="courseContentDuration"
				>{{ 'course.create.durationContent' | translate }} (minutes)</label
			>
			<input
				type="number"
				id="courseContentDuration"
				name="courseContentDuration"
				required=""
				[(ngModel)]="courseSectionTest.TotalTime"
				class="form-control"
			/>
		</div>
		<div class="form-group col-12">
			<label for="questionContent">{{
				'course.create.explainMore' | translate
			}}</label>
			<input
				id="questionContent"
				class="form-control"
				[(ngModel)]="courseSectionTest.Content"
				name="courseSectionTest.Description"
				type="text"
				maxlength="200"
				aria-describedby="sectionContent-help"
			/>
			<small id="sectionContent-help"
				>{{ 'course.create.specifyExplainMore' | translate }}
			</small>
		</div>

		<div class="form-group col-12">
			<label for="questionType">Type of question</label>
			<select
				id="questionType"
				class="form-control"
				[(ngModel)]="courseSectionTest.TypeOfTest"
				name="courseSectionTest.questionType"
			>
				<option value="" disabled></option>
				<option [value]="questionType.CHEKED">
					{{ 'course.create.mChoose' | translate }}
				</option>
				<option [value]="questionType.RADIO">
					{{ 'course.create.sChoose' | translate }}
				</option>
				<!--  <option [value]="questionType.FREE"> {{'course.create.fChoose'|translate}}</option>
        <option [value]="questionType.SELECT"> {{'course.create.selectChoose'|translate}} </option> -->
			</select>
		</div>

		<div
			class="col-12 form-group my-1"
			*ngFor="let question of courseSectionTest.Questions; let i = index"
		>
			<div class="card bg-light my-0">
				<div class="card-body">
					<h5>Option {{ i + 1 }}</h5>
					<div
						class="row"
						*ngIf="
							+courseSectionTest.TypeOfTest === questionType.RADIO ||
							+courseSectionTest.TypeOfTest === questionType.SELECT
						"
					>
						<div class="col-12 d-flex flex-row align-items-center">
							<div class="form-check">
								<input
									class="form-check-input"
									type="radio"
									[name]="'question-radio'"
									[value]="question.Slug.substr(0, 5)"
									[id]="question.Slug + 'radio'"
									[(ngModel)]="oneChooseValue"
								/>
								<label
									class="form-check-label text-light"
									style="width: 0"
									[for]="question.Slug + 'radio'"
								>
									c
								</label>
							</div>
							<input
								[id]="question.Slug + 'title'"
								class="form-control"
								required
								[(ngModel)]="question.Title"
								[name]="question.Slug + 'title-name'"
								type="text"
							/>
							<div class="">
								<button
									class="btn btn-sm btn-outline-light text-dark ml-2"
									(click)="onDelete($event, i)"
									type="button"
								>
									<i class="fas fa-times mr-1"></i>
								</button>
							</div>
						</div>
					</div>

					<div
						class="row"
						*ngIf="+courseSectionTest.TypeOfTest === questionType.CHEKED"
					>
						<div class="col-12 d-flex flex-row align-items-center">
							<div class="form-check form-check-inline">
								<input
									class="form-check-input mr-2"
									type="checkbox"
									[name]="question.Slug + 'checkbox'"
									[(ngModel)]="question.IsRightAnswer"
									[id]="question.Slug + 'checkbox'"
								/>
							</div>
							<input
								[id]="question.Slug + 'title'"
								class="form-control"
								required
								[(ngModel)]="question.Title"
								[name]="question.Slug + 'title-name'"
								type="text"
							/>
							<div class="">
								<button
									class="btn btn-sm btn-outline-light text-dark ml-2"
									(click)="onDelete($event, i)"
									type="button"
								>
									<i class="fas fa-times mr-1"></i>
								</button>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div
			class="col-12 form-group"
			*ngIf="
				courseSectionTest.TypeOfTest &&
				+courseSectionTest.TypeOfTest !== questionType.FREE
			"
		>
			<button class="btn btn-sm btn-light" (click)="addNewQuestion($event)">
				<i class="fa fa-plus mr-2"></i>
				{{ 'course.create.addQuestion' | translate }}
			</button>
		</div>

		<div class="col-12 form-group mt-2 d-flex">
			<button
				type="submit"
				class="btn"
				[ngClass]="{ 'btn-dark': modal, ' btn-light btn-sm text-dark': !modal }"
			>
				{{ 'course.comments.save' | translate }}
			</button>

			<button
				type="button"
				(click)="onDeleteContent($event)"
				*ngIf="!modal"
				class="btn btn-sm btn-light text-dark ml-2"
			>
				{{ 'course.comments.delete' | translate }}
			</button>

			<div
				class="mt-2 d-flex align-items-center justify-content-center"
				*ngIf="isLoading"
			>
				<i class="fas fa-spin fa-spinner mr-2"></i>
				{{ 'home.loading' | translate }}
			</div>
		</div>
	</div>
</form>
