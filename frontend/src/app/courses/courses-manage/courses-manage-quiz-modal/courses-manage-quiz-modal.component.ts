import { Component, Inject, OnInit } from '@angular/core';
import { CourseSection } from '~/app/models/course-section.model';
import { UtilsService } from '~/app/services/utils.service';
import { TranslateService } from '@ngx-translate/core';
import { UserService } from '~/app/services/users/user.service';
import { LY_DIALOG_DATA, LyDialogRef } from '@alyle/ui/dialog';
import { User } from '~/app/models/user.model';
import { ToastrService } from 'ngx-toastr';
import { CourseSectionTest } from '~/app/models/course-section-test.model';
import { ContentType } from '~/app/models/course.model';
import { CourseSectionTestService } from '~/app/services/courses/course-section-test.service';

@Component({
	selector: 'app-courses-manage-quiz-modal',
	templateUrl: './courses-manage-quiz-modal.component.html',
	styleUrls: ['./courses-manage-quiz-modal.component.scss']
})
export class CoursesManageQuizModalComponent implements OnInit {
	courseSectionTest: CourseSectionTest;
	user: User;

	constructor(
		private utilsService: UtilsService,

		private translateService: TranslateService,
		private userService: UserService,
		private utilService: UtilsService,
		public dialogRef: LyDialogRef,
		@Inject(LY_DIALOG_DATA)
		private data: {
			section: CourseSection;
			courseSectionTest: CourseSectionTest;
			user: User;
		},
		private toastr: ToastrService,
		private courseSectionTestService: CourseSectionTestService
	) {}

	async ngOnInit(): Promise<void> {
		if (this.data.courseSectionTest) {
			this.courseSectionTest = this.data.courseSectionTest;
			if (!this.courseSectionTest.Questions) {
				this.courseSectionTest.Questions = [];
			}
		} else {
			this.courseSectionTest = new CourseSectionTest(
				null,
				'123',
				null,
				null,
				null,
				ContentType.QUIZ,
				this.data.section.Contents ? this.data.section.Contents.length + 1 : 1,
				this.data.section,
				null,
				null,
				null,
				this.data.user.Slug,
				null,
				[],
				null,
				null
			);
		}
		this.courseSectionTest.Type = ContentType.QUIZ;
		this.courseSectionTest.Section = this.data.section;
		this.courseSectionTest.CreatedByISlug = this.data.user.Slug;
		this.user = this.data.user;
	}

	close(e): void {
		if (e) {
			e.preventDefault();
		}
		this.dialogRef.close();
	}

	onUpdateContent(courseSectionTest1: CourseSectionTest): void {
		this.dialogRef.close(true);
	}
}
