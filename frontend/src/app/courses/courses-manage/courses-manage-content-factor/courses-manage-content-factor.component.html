<form autocomplete="off" (ngSubmit)="onSubmit($event)" *ngIf="courseContent">
	<div class="row">
		<div class="col-12 p-field form-group">
			<label for="courseContentTitle">{{
				'course.create.titleContent' | translate
			}}</label>
			<input
				type="text"
				id="courseContentTitle"
				name="courseContentTitle"
				required=""
				[(ngModel)]="courseContent.Title"
				class="form-control"
			/>
		</div>

		<!--div class="col-12 my-2 mx-0" *ngIf="!showOption && canChangeSectionType">
      <button class="btn btn-sm btn-light" (click)="showOption = !showOption"> Change type</button>
    </div-->
		<!--div class="col-12 form-group justify-content-center" *ngIf="showOption && canChangeSectionType">
      <div class="text-center">
        Select the main type of content. Files and links can be added as resources. Learn about content types.
      </div>
      <div class="container">
        <div class="row">
          <div class="col-12 d-flex align-items-center justify-content-center ">
            <a  class="btn btn-link" href="" (click)="$event.preventDefault(); courseContent.Type = contentType.VIDEO; showOption = !showOption;">
              <div class="card bg-light mr-md-2 option">
                <div class="card-body d-flex flex-column justify-content-center align-items-center">
                  <i class="fas fa-4x fa-photo-video"></i>
                  VIDEO
                </div>
              </div>
            </a>


            <a  class="btn btn-link" href="" (click)="$event.preventDefault(); ; courseContent.Type = contentType.EBOOK;  showOption = !showOption;">
              <div class="card bg-light mr-md-2 option">
                <div class="card-body d-flex flex-column justify-content-center align-items-center">
                  <i class="fas fa-4x fa-book"></i>
                  E-BOOK
                </div>
              </div>
            </a>


            <a  class="btn btn-link" href="" (click)="$event.preventDefault(); ; courseContent.Type = contentType.PDF; showOption = !showOption;">
              <div class="card bg-light mr-md-2 option">
                <div class="card-body d-flex flex-column justify-content-center align-items-center">
                  <i class="fas fa-4x fa-file-pdf"></i>
                  PDF
                </div>
              </div>
            </a>

          </div>

        </div>

      </div>
    </div-->

		<div
			class="p-field form-group col-12"
			*ngIf="courseContent.Type === contentType.EBOOK && !showOption"
		>
			<label for="section.Slug+'content-id'">{{
				'course.create.courseDesc' | translate
			}}</label>
			<ckeditor
				class="form-control ckeditor"
				type="inline"
				required=""
				[(ngModel)]="courseContent.Content"
				name="section.Slug+'content-name'"
				id="section.Slug+'content-id'"
			></ckeditor>
			<!--angular-editor required="" [(ngModel)]="courseContent.Content"
                            name="section.Slug+'content-name'"
                            id="section.Slug+'content-id'" [config]="contentConfig">
            </angular-editor-->
			<small id="resume-help"></small>
		</div>

		<div
			class="col-12 p-fluid form-group"
			*ngIf="courseContent.Type === contentType.VIDEO && !showOption"
		>
			<input
				#_fileInputVideoCourseSection
				type="file"
				(change)="onSelectVideoOrPdf($event, 'video')"
				accept="video/*"
				hidden
			/>
			<label for="VideoCourseSection">
				{{ 'instructor.manageCourse.lblVideoDesc' | translate }}
			</label>
			<div class="p-inputgroup" (click)="_fileInputVideoCourseSection.click()">
				<input
					type="text"
					disabled
					id="VideoCourseSection"
					name="VideoCourseSection"
					[value]="selectedMedia?.Name || courseContent.Media?.Name"
					pInputText
					[placeholder]="'course.create.fileName' | translate"
				/>
				<button
					type="button"
					pButton
					pRipple
					class="p-button-raised p-button-secondary p-button-text"
					label="Upload file"
				></button>
			</div>

			<div
				class="d-flex align-items-end mt-1 b-2"
				*ngIf="format === 'video' && url"
			>
				<video [src]="url" height="200" controls class="mr-2"></video>

				<button
					type="button"
					class="btn btn-danger"
					(click)="cancelUpload()"
					*ngIf="!isLoading"
				>
					Cancel upload
				</button>
			</div>
			<p-progressBar
				[value]="progress"
				*ngIf="progress > 0 && this.selectedMedia"
			></p-progressBar>
		</div>

		<div
			class="p-fluid col-12 form-group"
			*ngIf="courseContent.Type === contentType.PDF && !showOption"
		>
			<input
				#_fileInputPDFCourseSection
				type="file"
				(change)="onSelectVideoOrPdf($event, 'pdf')"
				accept="application/pdf"
				hidden
			/>
			<label for="PDFCourseSection">
				{{ 'instructor.manageCourse.lblVideoDesc' | translate }}
			</label>
			<div class="p-inputgroup" (click)="_fileInputPDFCourseSection.click()">
				<input
					type="text"
					disabled
					id="PDFCourseSection"
					name="PDFCourseSection"
					[value]="selectedMedia?.Name || courseContent.Media?.Name"
					pInputText
					[placeholder]="'course.create.fileName' | translate"
				/>
				<button
					type="button"
					pButton
					pRipple
					class="p-button-raised p-button-secondary p-button-text"
					label="Upload file"
				></button>
			</div>
			<div class="mt-1 mb-2" *ngIf="format === 'pdf' && url">
				<pdf-viewer
					[src]="url"
					[render-text]="true"
					style="display: block; height: 200px; width: 100%"
					class="mb-2"
				></pdf-viewer>

				<button
					type="button"
					class="btn btn-danger mb-2"
					(click)="cancelUpload()"
					*ngIf="!isLoading"
				>
					Cancel upload
				</button>
			</div>
			<p-progressBar
				[value]="progress"
				*ngIf="progress > 0 && this.selectedMedia"
			></p-progressBar>
		</div>

		<div
			class="col-12"
			*ngIf="courseContent.Type === contentType.VIDEO && !showOption"
		>
			<mat-accordion *ngIf="courseContent.Transcriptions?.length > 0">
				<mat-expansion-panel
					[expanded]="ind + 1 === courseContent.Transcriptions.length"
					*ngFor="
						let transcription of courseContent.Transcriptions;
						let ind = index
					"
				>
					<mat-expansion-panel-header>
						<mat-panel-title>
							{{ transcription.Language }}
						</mat-panel-title>
					</mat-expansion-panel-header>
					<div class="p-fluid row">
						<div class="p-field col-12 form-group col-md-6">
							<label [for]="transcription.Slug + 'language'">{{
								'language.code' | translate
							}}</label>
							<select
								[id]="transcription.Slug + 'language'"
								[name]="transcription.Slug + 'language'"
								[(ngModel)]="transcription.Language"
								class="form-control"
							>
								<option value="" disabled=""></option>
								<option [value]="lang.code" *ngFor="let lang of courseLanguage">
									{{ lang.name }}
								</option>
							</select>
						</div>
						<div class="p-fluid col-12 col-md-6 form-group">
							<input
								#_transcriptionSlug
								type="file"
								(change)="openFileTranscription($event, transcription, ind)"
								accept=".vtt"
								hidden
							/>
							<label [for]="transcription.Slug + 'media'">
								{{ 'course.create.uploadVtt' | translate }}
							</label>
							<div class="p-inputgroup" (click)="_transcriptionSlug.click()">
								<input
									type="text"
									disabled
									[id]="transcription.Slug + 'media'"
									[name]="transcription.Slug + 'media'"
									[value]="transcription?.Media?.Name"
									required=""
									pInputText
									[placeholder]="'course.create.fileName' | translate"
								/>
								<button
									type="button"
									pButton
									pRipple
									class="p-button-raised p-button-secondary p-button-text"
									label="Upload file"
								></button>
							</div>
						</div>
						<div class="form-group col-12">
							<button
								class="btn btn-sm btn-outline-dark text-dark"
								type="button"
								(click)="onDelete($event, ind)"
							>
								{{ 'course.comments.delete' | translate }}
							</button>
						</div>
					</div>
				</mat-expansion-panel>
			</mat-accordion>
		</div>

		<div
			class="col-12 mt-2"
			*ngIf="courseContent.Type === contentType.VIDEO && !showOption"
		>
			<button
				class="btn btn-sm btn-light"
				type="button"
				(click)="onAddTranscription($event)"
			>
				{{ 'course.create.addTranscript' | translate }}
			</button>
		</div>

		<div class="col-12 p-field form-group">
			<label for="courseContentDuration"
				>{{ 'course.create.durationContent' | translate }} (minutes)</label
			>
			<input
				type="number"
				id="courseContentDuration"
				name="courseContentDuration"
				required=""
				[(ngModel)]="courseContent.TotalTime"
				class="form-control"
			/>
		</div>

		<div class="col-12 form-group d-flex mt-2">
			<button
				type="submit"
				class="btn btn-dark"
				[ngClass]="{ 'btn-md': !modal }"
				[disabled]="isLoading || isLoadingDelete"
			>
				<span *ngIf="isLoading" class="mr-2"
					><i class="fas fa-spin fa-spinner"></i
				></span>
				{{ 'course.details.save' | translate }}
			</button>

			<button
				type="button"
				(click)="onDeleteContent($event)"
				*ngIf="!modal"
				class="btn btn-md btn-danger ml-2"
				[disabled]="isLoading || isLoadingDelete"
			>
				<span *ngIf="isLoadingDelete" class="mr-2"
					><i class="fas fa-spin fa-spinner"></i
				></span>
				{{ 'course.comments.delete' | translate }}
			</button>
		</div>
	</div>
</form>
