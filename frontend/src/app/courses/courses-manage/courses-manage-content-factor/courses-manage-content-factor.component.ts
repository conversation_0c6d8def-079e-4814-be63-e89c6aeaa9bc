import { Media } from '~/app/models/media.model';
import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Inject,
	Input,
	OnInit,
	Output
} from '@angular/core';
import { validate, ValidationError } from 'class-validator';
import { CourseContent } from '~/app/models/course-content.model';
import { CourseSection } from '~/app/models/course-section.model';
import { ContentType, Course } from '~/app/models/course.model';
import { AngularEditorConfig } from '@kolkov/angular-editor';
import { UtilsService } from '~/app/services/utils.service';
import { CategoryService } from '~/app/services/categories/category.service';
import { TranslateService } from '@ngx-translate/core';
import { UserService } from '~/app/services/users/user.service';
import { User } from '~/app/models/user.model';
import { ToastrService } from 'ngx-toastr';
import { CourseTranscription } from '~/app/models/course-transcription.model';
import { CourseContentService } from '~/app/services/courses/course-content.service';
import { HttpEvent, HttpEventType } from '@angular/common/http';
import { environment } from '~/environments/environment';

@Component({
	selector: 'app-courses-manage-content-factor',
	templateUrl: './courses-manage-content-factor.component.html',
	styleUrls: ['./courses-manage-content-factor.component.scss']
})
export class CoursesManageContentFactorComponent implements OnInit {
	errors: ValidationError[];
	errorsHelper: ValidationError[];
	isLoading: boolean;
	isLoadingDelete: boolean;

	@Input()
	courseContent: CourseContent;
	@Input()
	user: User;
	@Input()
	section: CourseSection;
	@Input()
	course: Course;
	contentType = ContentType;
	contentConfig: AngularEditorConfig;
	showOption: boolean;
	courseLanguage: { code: string; name: string; nativeName: string }[];

	selectedMedia: Media;
	url;
	format;

	@Input()
	modal: boolean;

	@Output()
	updateCourse: EventEmitter<CourseContent> = new EventEmitter<CourseContent>();
	private canChangeSectionType = true;

	progress = 0;

	constructor(
		private utilsService: UtilsService,
		private categoryService: CategoryService,
		private translateService: TranslateService,
		private userService: UserService,
		private toastr: ToastrService,
		private courseContentService: CourseContentService,
		private changeDetectorRef: ChangeDetectorRef
	) {}

	async ngOnInit(): Promise<void> {
		this.contentConfig = this.utilsService.getSimpleconfigAngular();
		this.courseLanguage = await this.utilsService.getLanguages().toPromise();
		this.showOption = this.modal;
		this.courseContent.CreatedByISlug = this.user.Slug;
		console.log('section', this.section);
		this.courseContent.Type = this.course.Format;
		this.showOption = false;
		/*if (this.section && this.section.Contents.length > 0){
      this.canChangeSectionType = false;
      this.showOption = false;
      this.courseContent.Type = this.section.Contents[0].Type;
    }*/
	}

	async onSelectVideoOrPdf(e, format: string): Promise<void> {
		const file = e.target.files[0] as File;

		this.format = format;
		if (file) {
			var reader = new FileReader();
			reader.readAsDataURL(file);
			if (file.type.indexOf('pdf') > -1) {
				this.format = 'pdf';
			} else if (file.type.indexOf('video') > -1) {
				this.format = 'video';
			}
			reader.onload = (event) => {
				this.url = (<FileReader>event.target).result;
			};
		}

		this.selectedMedia = await this.utilsService.convertToBase64(file);
		this.changeDetectorRef.detectChanges();
	}

	cancelUpload() {
		this.format = null;
		this.url = null;
		this.selectedMedia = null;
	}

	getMediaURL(media: Media): string {
		console.log('media url', `${environment.path}/${media.Hashname}`);
		return media && media.Hashname
			? `${environment.path}/${media.Hashname}`
			: null;
	}

	async onSubmit(e): Promise<void> {
		e.preventDefault();

		this.isLoading = true;
		this.progress = 0;
		this.changeDetectorRef.detectChanges();

		if (this.courseContent.Title) {
			let response: CourseContent;

			this.courseContent.Media = this.selectedMedia
				? {
						...this.selectedMedia,
						Slug: this.courseContent.Media?.Slug,
						Id: this.courseContent.Media?.Id,
						CreatedAt: this.courseContent.Media?.CreatedAt
				  }
				: null;

			if (this.courseContent.Id && +this.courseContent.Id > 0) {
				this.courseContentService.edit(this.courseContent).subscribe(
					(event: HttpEvent<CourseContent>) => {
						switch (event.type) {
							case HttpEventType.Sent:
								console.log('Request has been made!');
								break;
							case HttpEventType.ResponseHeader:
								console.log('Response header has been received!');
								break;
							case HttpEventType.UploadProgress:
								this.progress = Math.round((event.loaded / event.total) * 100);
								console.log(`Uploaded! ${this.progress}%`);
								break;
							case HttpEventType.Response:
								this.isLoading = false;
								console.log('course content successfully updated!', event.body);
								this.handleCourseContentSaveSuccess(event.body);
							/* setTimeout(() => {
									this.progress = 0;
								}, 1500); */
						}
					},
					async (error) => {
						console.log(e);
						const message = await this.translateService
							.get('home.register.error')
							.toPromise();
						this.toastr.error(message, 'Brain-maker');
						this.isLoading = false;
					}
				);
			} else {
				this.courseContent.Position =
					this.section.Contents && this.section.Contents.length > 0
						? this.section.Contents.length + 1
						: 1;
				/* response = await this.courseContentService
					.add(this.courseContent)
					.toPromise(); */

				this.courseContentService.add(this.courseContent).subscribe(
					(event: HttpEvent<CourseContent>) => {
						switch (event.type) {
							case HttpEventType.Sent:
								console.log('Request has been made!');
								break;
							case HttpEventType.ResponseHeader:
								console.log('Response header has been received!');
								break;
							case HttpEventType.UploadProgress:
								this.progress = Math.round((event.loaded / event.total) * 100);
								console.log(`Uploaded! ${this.progress}%`);
								this.changeDetectorRef.detectChanges();
								break;
							case HttpEventType.Response:
								this.isLoading = false;
								console.log('course content successfully created!', event.body);
								this.handleCourseContentSaveSuccess(event.body);
							/* setTimeout(() = > {
									this.progress = 0;
								}, 1500); */
						}
					},
					async (error) => {
						console.log(e);
						const message = await this.translateService
							.get('home.register.error')
							.toPromise();
						this.toastr.error(message, 'Brain-maker');
						this.isLoading = false;
						this.changeDetectorRef.detectChanges();
					}
				);
			}
		} else {
			const message = await this.translateService
				.get('home.register.error')
				.toPromise();
			this.toastr.error(message, 'Brain-maker');
		}
	}

	async handleCourseContentSaveSuccess(response: any) {
		if (response && response.Id) {
			const message = await this.translateService
				.get('home.register.success')
				.toPromise();
			this.updateCourse.emit(this.courseContent);
			this.cancelUpload();
		} else {
			const message = await this.translateService
				.get('home.register.error')
				.toPromise();
			this.toastr.error(message, 'Brain-maker');
		}
	}

	async onItemChange(): Promise<void> {
		this.errorsHelper = await validate(this.courseContent);
	}

	isNotvalid(name: string): boolean {
		return this.utilsService.isNotvalid(name, this.errorsHelper);
	}

	getAllError(name: string): string[] {
		return this.utilsService.getAllError(name, this.errorsHelper);
	}

	async openFileTranscription(
		e,
		transcription: CourseTranscription,
		ind: number
	): Promise<void> {
		const file = e.target.files[0] as File;
		transcription.Media = await this.utilsService.convertToBase64(file);
		this.courseContent.Transcriptions.splice(ind, 1, transcription);
	}

	onAddTranscription(e): void {
		e.preventDefault();
		const transcription = new CourseTranscription(
			null,
			this.utilsService.getUniqueId()
		);
		transcription.UserSlug = this.user.Slug;
		this.courseContent.Transcriptions.push(transcription);
	}

	onDelete($event: MouseEvent, ind: number): void {
		this.courseContent.Transcriptions.splice(ind, 1);
	}

	async onDeleteContent(e): Promise<void> {
		e.preventDefault();
		if (confirm('do you really want to delete this ')) {
			this.isLoadingDelete = true;
			try {
				const c = await this.courseContentService
					.delete(this.courseContent)
					.toPromise();
				this.updateCourse.emit(this.courseContent);
			} catch (error) {
				console.log(e);
				const message = 'Error to delete course content';
				this.toastr.error(message, 'Brain-maker');
			} finally {
				this.isLoadingDelete = false;
			}
		}
	}
}
