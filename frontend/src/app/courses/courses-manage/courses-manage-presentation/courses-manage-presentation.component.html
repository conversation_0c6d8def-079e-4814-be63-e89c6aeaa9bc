<form action="" (ngSubmit)="onSubmit()">
	<div class="row mb-5">
		<div class="col-12">
			Presentation
			<hr />
		</div>
	</div>

	<div class="row">
		<input
			#_fileInput
			type="file"
			(change)="openCropperDialog($event)"
			accept="image/*"
			hidden
		/>
		<div class="col d-flex align-items-center">
			<div *ngIf="cover"><img class="img mw-100" [src]="cover" /></div>
		</div>
		<div class="p-fluid col form-group" (click)="_fileInput.click()">
			<label for="coverIage">{{
				'instructor.manageCourse.lblImageDesc' | translate
			}}</label>
			<div class="p-inputgroup">
				<input
					type="text"
					[value]="coverName"
					disabled
					id="coverIage"
					pInputText
					[placeholder]="'course.create.coverImg' | translate"
				/>
				<button
					type="button"
					pButton
					pRipple
					class="p-button-raised p-button-secondary p-button-text"
					label="Upload file"
				></button>
			</div>
		</div>
	</div>

	<!--div class="row mt-4">
        <input #_fileInputVideo type="file" (change)="openVideo($event)" accept="video/*" hidden>
        <div class="col d-flex  align-items-center ">
            <div *ngIf="!videoLoaded"><img class="img" [src]="presentation"></div>
            <video id="video" [src]="presentation" class="img" *ngIf="videoLoaded" controls></video>
        </div>
        <div class="p-fluid col form-group">
            <label for="presentation">
        {{ 'instructor.manageCourse.lblVideoDesc' | translate }}
      </label>
            <div class="p-inputgroup" (click)="_fileInputVideo.click()">
                <input type="text" disabled id="presentation" [value]="presentationName" pInputText [placeholder]="'course.create.presentVid'|translate">
                <button type="button" pButton pRipple class="p-button-raised p-button-secondary p-button-text" label="Upload file"></button>
            </div>
        </div>
    </div-->

	<div class="row mt-2">
		<div class="col-12 form-group mt-2 d-flex">
			<button type="submit" class="btn btn-dark" *ngIf="selectedImg">
				{{ 'course.comments.save' | translate }}
			</button>
			<div
				class="mt-2 d-flex align-items-center justify-content-center"
				*ngIf="isLoading"
			>
				<i class="fas fa-spin fa-spinner mr-2"></i>
				{{ 'home.loading' | translate }}
			</div>
		</div>
	</div>
</form>
