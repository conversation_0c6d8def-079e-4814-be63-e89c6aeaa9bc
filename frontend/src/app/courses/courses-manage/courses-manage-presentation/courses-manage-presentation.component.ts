import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output
} from '@angular/core';
import { Course } from '~/app/models/course.model';
import { User } from '~/app/models/user.model';
import { UtilsService } from '~/app/services/utils.service';
import { CategoryService } from '~/app/services/categories/category.service';
import { UserService } from '~/app/services/users/user.service';
import { TranslateService } from '@ngx-translate/core';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { LyDialog } from '@alyle/ui/dialog';
import { ImageCroppedComponent } from '~/app/shared/image-cropped/image-cropped.component';
import {
	ImgCropperConfig,
	ImgCropperEvent,
	ImgResolution
} from '@alyle/ui/image-cropper';
import { Media } from '~/app/models/media.model';
import { environment } from '~/environments/environment';
import { CourseService } from '~/app/services/courses/course.service';

@Component({
	selector: 'app-courses-manage-presentation',
	templateUrl: './courses-manage-presentation.component.html',
	styleUrls: ['./courses-manage-presentation.component.scss']
})
export class CoursesManagePresentationComponent implements OnInit {
	@Input()
	course: Course;

	@Output()
	emitCourse: EventEmitter<Course> = new EventEmitter<Course>();

	isLoading: boolean;

	@Input()
	user: User;

	cover = '/assets/img/learning1.jpg';
	coverName: string = null;
	presentation = '/assets/img/learning1.jpg';
	presentationName: string = null;
	videoLoaded: boolean;

	selectedImg: any = null;

	@Output() onNextTab = new EventEmitter<void>();

	constructor(
		private utilsService: UtilsService,
		private categoryService: CategoryService,
		private userService: UserService,
		private translateService: TranslateService,
		private toastr: ToastrService,
		private courseService: CourseService,
		private router: Router,
		private dialog: LyDialog,
		private cd: ChangeDetectorRef
	) {}

	async onSubmit(): Promise<void> {
		try {
			this.isLoading = true;
			let course: Course;

			if (this.course.Id && this.course.Id > 0) {
				course = await this.courseService.edit(this.course).toPromise();
			} else {
				course = await this.courseService.add(this.course).toPromise();
			}

			console.log('AFTER SAVE ===> ', course);
			this.emitCourse.emit(course);

			if (course && course.Id) {
				const message = await this.translateService
					.get('home.register.success')
					.toPromise();
				this.router.navigate([`/courses/manage/${course.Slug}`]).then(() => {
					this.onNextTab.emit();
				});
				this.toastr.success(message, 'Brain-maker');
				this.course = course;
			} else {
				const message = await this.translateService
					.get('home.register.error')
					.toPromise();
				this.toastr.error(message, 'Brain-maker');
			}
		} catch (e) {
			console.log(e);
			const message = await this.translateService
				.get('home.register.error')
				.toPromise();
			this.toastr.error(message, 'Brain-maker');
		} finally {
			this.isLoading = false;
		}
	}

	openCropperDialog(event: Event): void {
		// this.cover = null;
		this.dialog
			.open<ImageCroppedComponent, Event>(ImageCroppedComponent, {
				data: event,
				width: 700,
				height: 500,
				disableClose: true
			})
			.afterClosed.subscribe(
				(result?: { cropper: ImgCropperEvent; original: ImgCropperEvent }) => {
					if (result) {
						console.log('result end : ', result);
						this.selectedImg = result;

						this.cover = result.cropper.dataURL;
						this.coverName = result.original.name;
						this.course.CoverImage = new Media(
							this.coverName,
							result.cropper.dataURL?.substr(
								result.cropper.dataURL?.indexOf(',') + 1
							),
							result.original.type,
							result.original.size,
							'123'
						);
						this.cd.markForCheck();
					}
				}
			);
	}

	ngOnInit(): void {
		if (this.course.PresentationVideo) {
			this.presentation = this.course.PresentationVideo.Hashname;
			// this.presentation = `${environment.path}/${this.course.PresentationVideo.Hashname}`;
			this.presentationName = this.course.PresentationVideo.Name;
			console.log(this.presentation);
			this.videoLoaded = true;
		}

		if (this.course.CoverImage) {
			// this.cover =  `${environment.path}/${this.course.CoverImage.Hashname}`;
			this.cover = this.course.CoverImage.Hashname;
			console.log(this.cover);
			this.coverName = this.course.CoverImage.Name;
		}
	}

	async openVideo(e): Promise<void> {
		//  console.log('video : ', e);
		const file = e.target.files[0] as File;
		this.presentationName = file.name;
		this.course.PresentationVideo = await this.utilsService.convertToBase64(
			file
		);
		console.log('video base 64 : ', this.course.PresentationVideo);
		this.presentation = (await this.utilsService.getFileToBase64(
			file
		)) as string;
		this.videoLoaded = true;
	}
}
