<div class="red-skin">
	<!-- ============================================================== -->
	<!-- Main wrapper - style you can find in pages.scss -->
	<!-- ============================================================== -->
	<div id="main-wrapper">
		<!-- ============================================================== -->
		<!-- Top header  -->
		<!-- ============================================================== -->
		<!-- Start Navigation -->
		<app-home-header [theclass]="'header-light'"></app-home-header>
		<!-- End Navigation -->
		<div class="clearfix"></div>
		<!-- ============================================================== -->
		<!-- Top header  -->
		<!-- ============================================================== -->

		<!-- ============================ Dashboard: My Order Start ================================== -->
		<div class="container">
			<div class="my-3">
				<div class="card border-dark bg-light">
					<div class="card-body">
						<div class="d-flex justify-content-start align-items-baseline">
							<i
								class="far fa-edit font-weight-bold text-dark mr-3"
								style="font-size: 2.5em"
							></i>
							<div class="">
								<div class="h4 text-dark">
									{{ 'course.create.coursePublication' | translate }}
								</div>
								<span class=" ">
									{{ 'course.create.coursePublicationDesc' | translate }}
								</span>
							</div>
						</div>
					</div>
				</div>
			</div>
			<section class="gray pt-0" *ngIf="course && user">
				<p-tabView [(activeIndex)]="indexTab">
					<p-tabPanel
						[header]="'course.create.step1.btn' | translate"
						[selected]="true"
					>
						<div>
							<app-courses-manage-information
								[course]="course"
								[user]="user"
								(onNextTab)="openNext()"
							></app-courses-manage-information>
						</div>
					</p-tabPanel>
					<p-tabPanel
						[header]="'course.create.step2.btn' | translate"
						[disabled]="!(course.Id && +course.Id > 0)"
					>
						<div class="alert alert-primary" role="alert">
							<i class="fas fa-info-circle mr-2"></i>
							{{ 'course.create.step2.desc' | translate }}
						</div>
						<!-- <button
							class="btn btn-dark btn-sm"
							[disabled]="!(course.Id && +course.Id > 0)"
						>
							{{ 'course.create.step2.btn' | translate }}
						</button> -->
						<div>
							<app-courses-manage-presentation
								[course]="course"
								[user]="user"
								(emitCourse)="listenerCourseChanged($event)"
								(onNextTab)="openNext()"
							></app-courses-manage-presentation>
						</div>
					</p-tabPanel>
					<p-tabPanel
						[header]="'course.create.step3.btn' | translate"
						[disabled]="!(course.Id && +course.Id > 0)"
					>
						<div class="alert alert-primary" role="alert">
							<i class="fas fa-info-circle mr-2"></i>
							{{ 'course.create.step3.desc' | translate }}
						</div>
						<!-- <button
							class="btn btn-sm btn-dark"
							[disabled]="!(course.Id && +course.Id > 0)"
						>
							{{ 'course.create.step3.btn' | translate }}
						</button> -->
						<div>
							<app-courses-manage-pricing
								[course]="course"
								[user]="user"
								(onNextTab)="openNext()"
							></app-courses-manage-pricing>
						</div>
					</p-tabPanel>
					<p-tabPanel
						[header]="'course.create.step4.btn' | translate"
						[disabled]="!(course.Id && +course.Id > 0)"
					>
						<div class="alert alert-primary" role="alert">
							<i class="fas fa-info-circle mr-2"></i>
							{{ 'course.create.step4.desc' | translate }}
						</div>
						<div>
							<app-courses-manage-messaging
								[course]="course"
								[user]="user"
								(onNextTab)="openNext()"
							></app-courses-manage-messaging>
						</div>
					</p-tabPanel>
					<p-tabPanel
						[header]="'course.create.step5.btn' | translate"
						[disabled]="!(course.Id && +course.Id > 0)"
					>
						<div class="alert alert-primary" role="alert">
							<i class="fas fa-info-circle mr-2"></i>
							{{ 'course.create.step5.desc' | translate }}
						</div>
						<!-- <button
							class="btn btn-sm btn-dark"
							[disabled]="!(course.Id && +course.Id > 0)"
						>
							{{ 'course.create.step5.btn' | translate }}
						</button> -->
						<div>
							<app-courses-manage-structure
								[course]="course"
								(emitCourse)="listenerCourseChanged($event)"
								[user]="user"
							></app-courses-manage-structure>
						</div>
					</p-tabPanel>
					<p-tabPanel
						[header]="'course.create.step6.btn' | translate"
						[disabled]="!(course.Id && +course.Id > 0)"
					>
						<div class="alert alert-primary" role="alert">
							<i class="fas fa-info-circle mr-2"></i>
							{{ 'course.create.step5.desc' | translate }}
						</div>
						<div>
							<!-- <app-course-manage-preview
								[course]="course"
							></app-course-manage-preview> -->
							<div class="row">
								<div class="col-lg-4 col-md-6">
									<app-course-item [course]="course"></app-course-item>
								</div>
							</div>
						</div>
					</p-tabPanel>
				</p-tabView>

				<!--ly-paper>
          <ly-tabs [headerPlacement]="''">
            <ly-tab>
              <button ly-tab-label>Course information</button>
              <div [lyP]="6" lyTyp="body1">
                <app-courses-manage-information [course]="course"
                                                [user]="user"></app-courses-manage-information>
              </div>
            </ly-tab>
            <ly-tab>
              <button [disabled]="!(course.Id && +course.Id > 0)">Course presentation</button>
              <div [lyP]="6" lyTyp="body1">
                <app-courses-manage-presentation [course]="course"
                                                 [user]="user"></app-courses-manage-presentation>
              </div>
            </ly-tab>
            <ly-tab>
              <button [disabled]="!(course.Id && +course.Id > 0)">Course pricing </button>
              <div [lyP]="6" lyTyp="body1">
                <app-courses-manage-pricing [course]="course"
                                            [user]="user"></app-courses-manage-pricing>
              </div>
            </ly-tab>
            <ly-tab>
              <button [disabled]="!(course.Id && +course.Id > 0)">Course structure</button>
              <div [lyP]="6" lyTyp="body1">
                <app-courses-manage-structure [course]="course"
                                              (emitCourse)="listenerCourseChanged($event)"
                                              [user]="user"></app-courses-manage-structure>
              </div>
            </ly-tab>
            <ly-tab>
              <button  [disabled]="!(course.Id && +course.Id > 0)" ly-tab-label>Course messaging</button>
              <div [lyP]="6" lyTyp="body1">
                <app-courses-manage-messaging [course]="course"
                                              [user]="user"></app-courses-manage-messaging>
              </div>
            </ly-tab>
          </ly-tabs >
        </ly-paper-->
			</section>
		</div>
		<!-- ============================ Dashboard: My Order Start End ================================== -->

		<!-- ============================ Footer Start ================================== -->
		<app-home-footer></app-home-footer>
		<!-- ============================ Footer End ================================== -->
	</div>
</div>
