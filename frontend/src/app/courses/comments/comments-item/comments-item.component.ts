import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { ToastrService } from 'ngx-toastr';
import {
	CourseCommentOpinion,
	LIKE_TYPE,
	LOVE_TYPE,
	VIEW_TYPE,
	DISLIKE_TYPE
} from '~/app/models/course-comment-opinion.model';
import { CourseComment } from '~/app/models/course-comment.model';
import { Course } from '~/app/models/course.model';
import { Opinion } from '~/app/models/opinion';
import { User } from '~/app/models/user.model';
import { CourseCommentOpinionService } from '~/app/services/courses/course-comment-opinion.service';
import { CourseCommentService } from '~/app/services/courses/course-comment.service';
import { UserService } from '~/app/services/users/user.service';
import { environment } from '~/environments/environment';

@Component({
	selector: 'app-courses-comments-item',
	templateUrl: './comments-item.component.html',
	styleUrls: ['./comments-item.component.scss']
})
export class CommentsItemComponent implements OnInit {
	@Input() comment: CourseComment;
	// @Input() comment: Comment;
	// @Input() post: Post;
	// @Input() level;
	replyingComment: CourseComment = undefined;
	editingComment: CourseComment = undefined;
	// commentsCount: number;

	isLiked: boolean;
	isDisLiked: boolean;
	isLoved: boolean;

	likeCount = 0;
	disLikeCount = 0;
	loveCount = 0;

	COMMENT_CONTENT_MAX_LENGTH = 500;

	/**
	 * Event to inform post-details.component when user add new comment
	 */
	@Output()
	onReplyComment: EventEmitter<CourseComment> = new EventEmitter<CourseComment>();
	@Output()
	onDeleteComment: EventEmitter<CourseComment> = new EventEmitter<CourseComment>();

	/**
	 * Post that we want to manage comments
	 */
	// @Input() post: Post;
	@Input() userIsNotConnected;
	@Input() replyOption = true;
	@Input() course: Course;

	isLoading = false;

	user: User;
	userOpinion: CourseCommentOpinion;

	createCommentForm: FormGroup;

	constructor(
		private formBuilder: FormBuilder,
		private userService: UserService,
		private toastr: ToastrService,
		private translateService: TranslateService,
		private opinionService: CourseCommentOpinionService,
		private courseCommentService: CourseCommentService
	) {
		this.createCommentForm = this.formBuilder.group({
			content: ''
		});
	}
	/**
	 * Getting all comments of post and count comments
	 */
	async ngOnInit(): Promise<void> {
		/*
		 * Create new opinion with type VIEW_TYPE and store the opinion,
		 * to tell that user have see post
		 */
		if (this.userService.isConnected()) {
			this.user = await this.userService.getUserConnected().toPromise();

			this.userOpinion = (await this.opinionService
				.getCurrentUserCommentOpinion(this.user, this.comment)
				.toPromise()) as CourseCommentOpinion;

			// console.log(this.userOpinion);
			if (this.userOpinion && this.userOpinion.Type === LIKE_TYPE) {
				this.like();
			} else if (this.userOpinion && this.userOpinion.Type === DISLIKE_TYPE) {
				this.disLike();
			} else if (this.userOpinion && this.userOpinion.Type === LOVE_TYPE) {
				this.love();
			}
		}

		if (!this.user) {
			this.userIsNotConnected = true;
		}

		this.countCommentsOpinions();
		// console.log('user', this.user);
	}

	/**
	 * Get avatar image of user that have comment
	 * @param user
	 */
	getImage(): string {
		if (this.comment.CommentedBy && this.comment.CommentedBy.Photo) {
			return `${environment.path}/${this.comment.CommentedBy.Photo.Hashname}`;
		}
		return '/assets/img/avatar.png';
	}

	/**
	 * Get user avatar image
	 */
	getUserImage(): string {
		if (this.user && this.user.Photo) {
			return `${environment.path}/${this.user.Photo.Hashname}`;
		}
		return '/assets/img/avatar.png';
	}

	/**
	 * Set comment user want to reply
	 * @param comment
	 */
	async replyComment(comment): Promise<void> {
		if (this.userIsNotConnected) {
			this.toastWarningMessage(
				await this.translateService
					.get('blog.post-comments.must-auth-content')
					.toPromise(),
				await this.translateService
					.get('blog.post-comments.must-auth-label')
					.toPromise()
			);
			return;
		}
		this.replyingComment = comment;
	}

	async editComment(): Promise<void> {
		if (this.userIsNotConnected) {
			this.toastWarningMessage(
				await this.translateService
					.get('blog.post-comments.must-auth-content')
					.toPromise(),
				await this.translateService
					.get('blog.post-comments.must-auth-label')
					.toPromise()
			);
			return;
		}
		this.createCommentForm.setValue({
			content: this.comment.Comment.replace('<br />', '\n')
		});
		this.editingComment = this.comment;
	}

	async deleteComment() {
		if (this.userIsNotConnected) {
			this.toastWarningMessage(
				await this.translateService
					.get('blog.post-comments.must-auth-content')
					.toPromise(),
				await this.translateService
					.get('blog.post-comments.must-auth-label')
					.toPromise()
			);
			return;
		}

		this.onDeleteComment.emit(this.comment);
	}

	cancelReplying(): void {
		this.replyingComment = undefined;
		this.editingComment = undefined;
		this.resetFormValues();
	}

	toastWarningMessage(title, content): void {
		this.toastr.warning(title, content);
	}

	/**
	 * Submit comment reply
	 */
	async onSubmitReply(event): Promise<void> {
		event.preventDefault();

		if (this.userIsNotConnected) {
			this.toastWarningMessage(
				await this.translateService
					.get('blog.post-comments.must-auth-content')
					.toPromise(),
				await this.translateService
					.get('blog.post-comments.must-auth-label')
					.toPromise()
			);
		}
		const content = this.createCommentForm
			.get('content')
			.value.trim()
			.replace(/\r?\n/g, '<br />');

		if (!content || content == '') {
			return;
		}
		if (content.length > this.COMMENT_CONTENT_MAX_LENGTH) {
			this.toastr.error(
				await this.translateService
					.get('blog.post-comments.content-length-error')
					.toPromise(),
				await this.translateService
					.get('blog.post-comments.content-length-error-label')
					.toPromise()
			);
		}

		this.createCommentForm.get('content').disable();
		this.isLoading = true;

		if (this.replyingComment) {
			const newComment = new CourseComment();
			newComment.Comment = content;
			newComment.Content = this.course;
			newComment.CommentedBy = this.user;
			newComment.Parent = this.comment;
			newComment.reply = [];

			const savedComment = (await this.courseCommentService
				.add(newComment)
				.toPromise()) as CourseComment;
			if (this.comment.reply) {
				// @ts-ignore
				this.comment.reply.push(savedComment);
			} else {
				// @ts-ignore
				this.comment.reply = [savedComment];
			}

			this.onReplyComment.emit(this.comment);
		}

		if (this.editingComment) {
			this.comment.Comment = content;
			const updatedComment = (await this.courseCommentService
				.edit(this.comment)
				.toPromise()) as CourseComment;
			// console.log('updated course comment', updatedComment);
		}

		this.resetFormValues();
		this.isLoading = false;
		this.replyingComment = undefined;
		this.editingComment = undefined;

		this.createCommentForm.get('content').enable();
	}

	//  handleReplyComment(comment): void {
	//    this.onReplyComment.emit(this.comment);
	//  }
	/**
	 * Reset comment form values
	 * @private
	 */
	private resetFormValues(): void {
		this.createCommentForm = this.formBuilder.group({
			content: ''
		});
	}

	/**
	 * Count all comments of post, parent comments and replies
	 * @param comments
	 * @private
	 */
	/*
	private countComments(comments): number {
	let count = comments.length;
	comments.map(comment => {
		if (comment.reply) {
		count += comment.reply.length;
		}
	});
	return count;
	}
	 */

	/**
	 * Event onClick on like button
	 * if user liked update opinion and set VIEW_TYPE
	 * else set LIKE_TYPE
	 */
	async likeComment(): Promise<void> {
		// alert('like comment');
		if (this.userIsNotConnected) {
			return;
		}
		if (!this.userOpinion) {
			const opinion = new CourseCommentOpinion();
			opinion.Type = LIKE_TYPE;
			opinion.CourseComment = this.comment;
			// opinion.Post = null;
			opinion.User = this.user;
			// opinion.Type = LIKE_TYPE;
			this.userOpinion = (await this.opinionService
				.add(opinion)
				.toPromise()) as CourseCommentOpinion;
			this.like();
			this.countCommentsOpinions();
			return;
		}
		if (this.userOpinion && this.isLiked) {
			await this.opinionService
				.update(this.userOpinion.Slug, VIEW_TYPE)
				.toPromise();
			this.clearLike();
		} else if (this.userOpinion) {
			await this.opinionService
				.update(this.userOpinion.Slug, LIKE_TYPE)
				.toPromise();
			this.like();
		}

		// console.log('user opinion', this.userOpinion);
		await this.countCommentsOpinions();
	}

	/**
	 * Event onClick on dislike button
	 * if user disliked, update opinion and set VIEW_TYPE
	 * else set DISLIKE_TYPE
	 */
	async disLikeComment(): Promise<void> {
		if (this.userIsNotConnected) {
			return;
		}

		if (!this.userOpinion) {
			const opinion = new CourseCommentOpinion();
			opinion.CourseComment = this.comment;
			// opinion.Post = null;
			opinion.User = this.user;
			opinion.Type = DISLIKE_TYPE;
			this.userOpinion = (await this.opinionService
				.add(opinion)
				.toPromise()) as CourseCommentOpinion;
			this.disLike();
			this.countCommentsOpinions();
			return;
		}

		if (this.userOpinion && this.isDisLiked) {
			await this.opinionService
				.update(this.userOpinion.Slug, VIEW_TYPE)
				.toPromise();
			this.clearDisLike();
		} else if (this.userOpinion) {
			await this.opinionService
				.update(this.userOpinion.Slug, DISLIKE_TYPE)
				.toPromise();
			this.disLike();
		}
		await this.countCommentsOpinions();
	}

	async loveComment(): Promise<void> {
		if (this.userIsNotConnected) {
			return;
		}

		if (!this.userOpinion) {
			const opinion = new CourseCommentOpinion();
			opinion.CourseComment = this.comment;
			// opinion.Post = null;
			opinion.User = this.user;
			opinion.Type = LOVE_TYPE;
			this.userOpinion = (await this.opinionService
				.add(opinion)
				.toPromise()) as CourseCommentOpinion;
			this.love();
			this.countCommentsOpinions();
			return;
		}

		if (this.userOpinion && this.isLoved) {
			await this.opinionService
				.update(this.userOpinion.Slug, VIEW_TYPE)
				.toPromise();
			this.clearLove();
		} else if (this.userOpinion) {
			await this.opinionService
				.update(this.userOpinion.Slug, LOVE_TYPE)
				.toPromise();
			this.love();
		}
		await this.countCommentsOpinions();
	}

	/**
	 * Change likes icons in template
	 * active like icon and disabled dislike icon
	 */
	like(): void {
		this.isLiked = true;
		this.isDisLiked = false;
		this.isLoved = false;
	}

	/**
	 * Change likes icons,
	 * active dislike icon and disabled like icon
	 */
	disLike(): void {
		this.isLiked = false;
		this.isDisLiked = true;
		this.isLoved = false;
	}

	love(): void {
		this.isLoved = true;
		this.isLiked = false;
		this.isDisLiked = false;
	}

	/**
	 * Disabled like icon when user cancel like opinion
	 */
	clearLike(): void {
		this.isLiked = false;
	}

	/**
	 * Disabled dislike icon when user cancel dislike opinion
	 */
	clearDisLike(): void {
		this.isDisLiked = false;
	}

	clearLove(): void {
		this.isLoved = false;
	}

	/*
	 * Get all comment opinions and count view, likes and dislikes
	 */
	async countCommentsOpinions(): Promise<void> {
		let likeCount = 0;
		let disLikeCount = 0;
		let loveCount = 0;
		const commentOpinions = (await this.opinionService
			.getAllCommentOpinions(this.comment)
			.toPromise()) as CourseCommentOpinion[];
		commentOpinions.map((opinion) => {
			if (opinion.Type === LIKE_TYPE) {
				++likeCount;
			}
			if (opinion.Type === DISLIKE_TYPE) {
				++disLikeCount;
			}
			if (opinion.Type === LOVE_TYPE) {
				++loveCount;
			}
		});
		this.likeCount = likeCount;
		this.disLikeCount = disLikeCount;
		this.loveCount = loveCount;
	}
}
