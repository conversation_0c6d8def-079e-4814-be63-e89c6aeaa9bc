<div class="course-comment-item">
    <div class="custom-comment">
        <div class="custom-comment-item">
            <div class="image">
                <img [src]="getImage()" alt="" />
            </div>

            <div class="comments-infos">
                <div class="username">
                    {{ comment.CommentedBy.Firstname + ' ' + comment.CommentedBy.Lastname }}&nbsp;
                    <span>{{ comment.CreatedAt |dateAgo }}</span>
                </div>

                <div class="content" [innerHTML]="comment.Comment"></div>

                <div class="controls">
                    <div *ngIf="!replyingComment && !editingComment && replyOption" (click)="replyComment(comment)" class="reply">
                        {{'course.comments.reply'|translate}}
                    </div>

                    <div *ngIf="replyingComment || editingComment" (click)="cancelReplying()" class="reply">
                        {{'course.comments.cancel'|translate}}
                    </div>

                    <div class="custom-comment-likes">
                        <span (click)="likeComment()">
              <strong *ngIf="loveCount || likeCount != 0 || disLikeCount != 0">{{ likeCount }}</strong>

              <span matTooltip="{{ 'course.comments.like' | translate }}" *ngIf="!isLiked && !userIsNotConnected"
                class="material-icons">
                thumb_up_alt</span>

                        <span matTooltip="{{ 'course.comments.like' | translate }}" *ngIf="isLiked && !userIsNotConnected" class="material-icons active">thumb_up_alt</span>

                        <span matTooltip="{{ 'course.comments.like' | translate }}" *ngIf="userIsNotConnected" class="material-icons disabled">thumb_up_alt</span>
                        </span>

                        <span (click)="disLikeComment()">
              <strong *ngIf="loveCount || likeCount != 0 || disLikeCount != 0">{{ disLikeCount }}</strong>

              <span matTooltip="{{ 'course.comments.dislike' | translate }}" *ngIf="!isDisLiked && !userIsNotConnected"
                class="material-icons">
                thumb_down_alt</span>
                        <span matTooltip="{{ 'course.comments.dislike' | translate }}" *ngIf="isDisLiked && !userIsNotConnected" class="material-icons active">thumb_down_alt</span>
                        <span matTooltip="{{ 'course.comments.dislike' | translate }}" *ngIf="userIsNotConnected" class="material-icons disabled">thumb_down_alt</span>
                        </span>

                        <!-- comment love opinion-->
                        <span (click)="loveComment()">
              <strong *ngIf="loveCount || likeCount != 0 || disLikeCount != 0">{{ loveCount }}</strong>

              <span matTooltip="{{ 'course.comments.love' | translate }}" *ngIf="!isLoved && !userIsNotConnected"
                class="material-icons">favorite</span>
                        <span matTooltip="{{ 'course.comments.love' | translate }}" *ngIf="isLoved && !userIsNotConnected" class="material-icons active">favorite</span>
                        <span matTooltip="{{ 'course.comments.love' | translate }}" *ngIf="userIsNotConnected" class="material-icons disabled">favorite</span>
                        </span>

                        <!-- Comment edit and delete option-->
                        <span *ngIf="
								!userIsNotConnected &&
								user &&
								user.Id === comment.CommentedBy.Id
							" [matMenuTriggerFor]="menu" class="material-icons">
              more_vert
            </span>

                        <mat-menu #menu="matMenu">
                            <span (click)="editComment()" mat-menu-item>{{'course.comments.edit'|translate}}</span>
                            <span (click)="deleteComment()" mat-menu-item>{{'course.comments.delete'|translate}}</span>
                        </mat-menu>
                    </div>
                </div>
            </div>
        </div>

        <div *ngIf="replyingComment || editingComment" class="comment-form">
            <div class="image">
                <img [src]="getUserImage()" alt="" />
            </div>

            <div class="custom-form">
                <!-- <div
              *ngIf="replyingComment "
              (click)="cancelReplying()"
              class="reply"
            >Cancel</div> -->

                <form [formGroup]="createCommentForm" (ngSubmit)="onSubmitReply()" action="#">
                    <textarea [formControlName]="'content'" name="comment" placeholder="{{
							'blog.show-one-post.comment-content-label' | translate
						}}" (keydown.enter)="onSubmitReply($event)" pInputTextarea autoResize="autoResize"></textarea>
                    <button class="btn-primary rounded btn-sm" *ngIf="!isLoading" (click)="onSubmitReply($event)">
            Commenter
          </button>
                </form>
                <app-loading [isLoading]="isLoading"></app-loading>
            </div>
        </div>

        <ul *ngIf="comment.reply && comment.reply.length > 0 && replyOption" class="comment-children">
            <li *ngFor="let replyComment of comment.reply">
                <app-courses-comments-item [userIsNotConnected]="userIsNotConnected" [course]="course" [comment]="replyComment" [replyOption]="false" [level]="1" (onDeleteComment)="onDeleteComment.emit($event)">
                </app-courses-comments-item>
                <!-- <post-comment-item [post]="post" [comment]="replyComment"></post-comment-item> -->

                <!-- <div class="custom-comment">

              <div class="custom-comment-item">

                <div class="image">
                  <img src="http://localhost:3200/upload/blog/images/e9c78ae83ca7-42f8-b92b-77cfb2e1b0b6-FireShot Capture 203 - Auditez la sécurité d'un système d'exploitation - OpenClassrooms_ - openclassrooms.com.png" alt="" />
                </div>

                <div class="comments-infos">

                  <div class="username">Black Dexter&nbsp;.&nbsp;<span>4H</span></div>

                  <div class="content">Lorem ipsum dolor sit amet consectetur adipisicing elit. Ea id iure asperiores vero voluptates maiores perspiciatis nemo. Consectetur delectus, exercitationem dignissimos at ipsam obcaecati iste inventore placeat quis, tenetur ut.</div>

                  <div class="controls">

                    <div class="reply">Reply</div>

                    <div class="custom-comment-likes">
                      <span><strong>14</strong>
                          <span class="material-icons"> thumb_up_alt</span>
                           <span *ngIf="isLiked && !userIsNotConnected" class="material-icons active">thumb_up_alt</span>
                          <span *ngIf="userIsNotConnected" class="material-icons disabled">thumb_up_alt</span>
                      </span>
                      <span><strong>2</strong>
                        <span class="material-icons"> thumb_down_alt</span>
                        <span *ngIf="isDisLiked && !userIsNotConnected" class="material-icons active">thumb_down_alt</span>
                        <span *ngIf="userIsNotConnected" class="material-icons disabled">thumb_down_alt</span>
                      </span>

                    </div>

                  </div>

                </div>

              </div>

            </div> -->
            </li>
        </ul>
    </div>
</div>
