.btn.cancel {
	background-color: #131b31;
	margin-left: 1rem !important;
}

.author-name {
	font-size: 1rem !important;
}

.comment-date {
	font-size: 0.7rem !important;
}

.comment-text {
	font-size: 1rem !important;
	line-height: 0 !important;
}

.comment-reply {
	margin-top: 10px;
	.reply {
		cursor: pointer;
	}
}

.custom-comment-likes {
	display: flex;
	justify-content: flex-start;
	align-items: center;
	padding-top: 0.5rem;

	span {
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 1.2rem;
		margin: 0 0.1rem;
		cursor: pointer;
		color: #ccc;

		strong {
			color: #da0b4e;
			margin: 0 0.2rem;
			font-size: 0.9rem;
		}
	}
}

.material-icons.disabled {
	color: #da0b4e !important;
}
.material-icons.active {
	color: #da0b4e !important;
}

.custom-comment {
	padding: 10px;

	.custom-comment-item {
		display: flex;
		justify-content: space-between;

		.image {
			width: 50px;
			height: 50px;
			border-radius: 50px;
			overflow: hidden;
			margin-right: 16px;

			img {
				width: 100%;
				height: 100%;
				object-fit: cover;
			}
		}

		.comments-infos {
			width: 100%;

			.username {
				font-size: 0.8rem;
				font-weight: bold;

				span {
					font-weight: initial;
					color: #999;
				}
			}

			.content {
				background-color: #f6f6f6;
				border-radius: 3px;
				padding: 10px 16px;
				box-shadow: 0 2px 4px rgba(0, 0, 0, 0.01);
				border-radius: 12px;
			}

			.controls {
				display: flex;
				justify-content: flex-start;
				align-items: center;
				font-size: 0.8rem;

				.reply {
					margin: 0 1rem;
					cursor: pointer;
					color: #da0b4e;

					&:hover {
						color: #131b31;
					}
				}

				.custom-comment-likes {
				}
			}
		}
	}

	.comment-form {
		position: relative;
		width: 90%;
		left: 10%;
		display: flex;
		justify-content: flex-end;
		align-items: center;
		margin: 16px 0;

		.image {
			position: relative;
			bottom: 12px;
			width: 50px;
			height: 50px;
			border-radius: 50px;
			overflow: hidden;

			img {
				width: 100%;
				height: 100%;
				object-fit: cover;
			}
		}

		.custom-form {
			width: calc(100% - 70px);
			margin-left: 20px;
			background-color: white;

			form {
				text-align: end;

				textarea {
					margin-bottom: 5px;
					border-radius: 1em;
					min-height: 100px;
					width: 100%;
					background-color: #ececec;
				}

				button {
					border: none;
					background-color: #da0b4e;
					border-radius: 0 0 12px 0;
					padding: 0.5rem 1rem;
					color: #fff;
				}
			}
		}
	}
}

.comment-children {
	li {
		.custom-comment {
			.custom-comment-item {
				.image {
					width: 40px;
					height: 40px;
					border-radius: 40px;
				}
				.comments-infos {
					.controls {
						.reply {
							// display: none;
						}
					}
				}
			}
		}
	}
}

.comment-children {
	padding-left: 40px;
}

@media screen and (max-width: 550px) {
	.custom-comment {
		.custom-comment-item {
			.image {
				width: 50px;
				height: 50px;
				border-radius: 50px;

				img {
				}
			}

			.comments-infos {
				width: calc(100% - 50px);

				.username {
					// font-size: .8rem;
					// font-weight: bold;

					span {
						// font-weight: initial;
					}
				}

				.content {
					// background-color: #F6F6F6;
					// border-radius: 5px;
					// padding: 10px 16px;
				}

				.controls {
					// display: flex;
					// justify-content: flex-start;
					// align-items: center;
					// font-size: .8rem;

					.reply {
						// margin: 0 1rem;
						// cursor: pointer;
						// color: #da0b4e;

						&:hover {
							// color: #131b31;
						}
					}

					.custom-comment-likes {
					}
				}
			}
		}

		.comment-form {
			// position: relative;
			// width: 90%;
			// left: 10%;
			// display: flex;
			// justify-content: flex-end;
			// align-items: center;
			// margin: 16px 0;

			.image {
				// position: relative;
				// bottom: 12px;
				// width: 50px;
				// height: 50px;
				// border-radius: 50px;
				// overflow: hidden;

				img {
					// width: 100%;
					// height: 100%;
					// object-fit: cover;
				}
			}

			.custom-form {
				// width: calc(100% - 70px);
				// margin-left: 20px;
				// background-color: white;

				form {
					textarea {
						margin-bottom: 10px;
						border-radius: 1em;
						min-height: 50px;
						width: 100%;
						background-color: #f6f6f6;
					}
				}
			}
		}
	}

	.comment-children {
		padding-left: 20px;

		li {
			.custom-comment {
				.custom-comment-item {
					.image {
						width: 30px;
						height: 30px;
					}
					.comments-infos {
						width: calc(100% - 50px);

						.controls {
							.reply {
								// display: none;
							}
						}
					}
				}
			}
		}
	}
}

.mat-focus-indicator {
	height: 30px;
	line-height: 30px;
	color: #131b31;
}

.reply {
	color: #da0b4e;
	cursor: pointer;

	&:hover {
		color: #131b31;
	}
}

textarea.reply {
	border: 1px solid #ccc !important;
}

.p-inputtext:enabled:focus {
	box-shadow: 0 0 0 0.1rem #da0b4e !important;
	border-color: #da0b4e !important;
}

.p-inputtext:enabled:hover {
	border-color: #da0b4e !important;
}
