import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { ToastrService } from 'ngx-toastr';
import { Course } from '~/app/models/course.model';
import { User } from '~/app/models/user.model';
import { CourseCommentService } from '~/app/services/courses/course-comment.service';
import { UserService } from '~/app/services/users/user.service';
import { CourseComment } from '../../models/course-comment.model';

@Component({
	selector: 'app-courses-comments',
	templateUrl: './comments.component.html',
	styleUrls: ['./comments.component.scss']
})
export class CommentsComponent implements OnInit {
	@Input() course: Course;

	comments: CourseComment[] = [];
	replyingComment: Comment = undefined;
	commentsCount: number;

	isLiked: boolean;
	isDisLiked: boolean;

	likeCount = 0;
	disLikeCount = 0;

	COMMENT_CONTENT_MAX_LENGTH = 1000;

	/**
	 * Event to inform post-details.component when user add new comment
	 */
	@Output() onAddComments: EventEmitter<Comment[]> = new EventEmitter<
		Comment[]
	>();

	/**
	 * Post that we want to manage comments
	 */
	// @Input() post: Post;
	@Input() userIsNotConnected;
	user: User;

	isLoading = false;

	createCommentForm: FormGroup;

	take: number;
	page: number;
	isLastPage: boolean;
	loadingData: boolean;
	count: number;
	countAll: number;

	constructor(
		private formBuilder: FormBuilder,
		private userService: UserService,
		private courseCommentService: CourseCommentService,
		private toastr: ToastrService,
		private translateService: TranslateService
	) {
		this.createCommentForm = this.formBuilder.group({
			content: ''
		});

		this.take = 5;
		this.page = 1;
		this.isLastPage = false;
		this.loadingData = false;
		this.count = 0;
		this.countAll = 0;
	}
	/**
	 * Getting all comments of post and count comments
	 */
	async ngOnInit(): Promise<void> {
		// const commentArray = (await this.courseCommentService
		// 	.getByCourse(this.course.Slug)
		// 	.toPromise()) as CourseComment[];
		// this.comments = commentArray.reverse();
		// this.commentsCount = this.countComments(this.comments);
		// console.log('Commentaires: ', this.comments);

		const response = await this.courseCommentService
			.getByCourseLazy(this.course.Slug, this.take)
			.toPromise();
		this.comments = response.CommentList;
		this.countAll = response.CountAll;
		this.count = response.Count;

		if (this.userService.isConnected()) {
			this.user = await this.userService.getUserConnected().toPromise();
		}

		if (!this.user) {
			this.userIsNotConnected = true;
		}
		// console.log(this.user);
	}

	async ngOnChanges(): Promise<void> {
		// this.comments = (await this.courseCommentService
		// 	.getByCourse(this.course.Slug)
		// 	.toPromise()) as CourseComment[];
		// this.commentsCount = this.countComments(this.course.Comments);
	}

	/**
	 * Submit new comment
	 */
	async onSubmit(event): Promise<void> {
		event.preventDefault();
		// alert('Soumission du commentaire');
		// console.log(this.userIsNotConnected);

		if (this.userIsNotConnected) {
			this.toastWarningMessage(
				await this.translateService
					.get('blog.post-comments.must-auth-content')
					.toPromise(),
				await this.translateService
					.get('blog.post-comments.must-auth-label')
					.toPromise()
			);
			return;
		}
		let content = this.createCommentForm.get('content').value;
		if (!content) {
			return;
		}

		content = content.trim().replace(/\r?\n/g, '<br />');

		this.createCommentForm.get('content').disable();

		if (content.length > this.COMMENT_CONTENT_MAX_LENGTH) {
			this.toastr.error(
				await this.translateService
					.get('blog.post-comments.content-length-error')
					.toPromise(),
				await this.translateService
					.get('blog.post-comments.content-length-error-label')
					.toPromise()
			);
			return;
		}
		this.isLoading = true;

		const newComment = new CourseComment();
		newComment.CommentedBy = this.user;
		newComment.Content = this.course;
		newComment.Comment = content;
		const savedComment = await this.courseCommentService
			.add(newComment)
			.toPromise();
		this.comments.unshift(savedComment);

		/**
		 * Update comment counter
		 */
		// this.commentsCount++;
		this.countAll++;
		this.createCommentForm.get('content').enable();

		/**
		 * Emit event to inform post-details.component that we have new comment
		 * and reset form
		 */
		this.resetFormValues();
		this.isLoading = false;
	}

	toastWarningMessage(title: string, content: string): void {
		this.toastr.warning(title, content);
	}

	handleReplyComment(): void {
		// this.commentsCount++;
		this.countAll++;
	}

	async handleDeleteComment(comment: CourseComment): Promise<void> {
		if (!comment.Parent) {
			const index = this.comments.indexOf(comment);
			this.comments.splice(index, 1);
			this.countAll -= comment.reply ? comment.reply.length + 1 : 1;
		} else {
			const parent = this.comments.find((c) => c.Id === comment.Parent.Id);
			if (parent) {
				const parentIndex = this.comments.indexOf(parent);
				const childIndex = parent.reply.indexOf(comment);
				this.comments[parentIndex].reply.splice(childIndex, 1);
				// this.commentsCount--;
				this.countAll--;
			}
		}

		await this.courseCommentService.delete(comment).toPromise();
	}

	/**
	 * Reset comment form values
	 * @private
	 */
	private resetFormValues(): void {
		this.createCommentForm = this.formBuilder.group({
			content: ''
		});
	}

	/**
	 * Count all comments of post, parent comments and replies
	 * @param comments
	 * @private
	 */
	// private countComments(comments): number {
	// 	if (!comments) {
	// 		return 0;
	// 	}
	// 	let count = comments ? comments.length : [];
	// 	comments.map((comment) => {
	// 		if (comment.reply) {
	// 			count += comment.reply.length;
	// 		}
	// 	});
	// 	return count;
	// }

	async paginate(): Promise<void> {
		if (this.isLastPage) return;

		const lastPageNumber =
			Math.round(this.count / this.take) > this.count / this.take
				? Math.round(this.count / this.take) - 1
				: Math.round(this.count / this.take);

		console.log('Last page: ', lastPageNumber, 'Current page: ', this.page);

		this.isLastPage = lastPageNumber > 0 ? this.page === lastPageNumber : true;

		const skip = this.page * this.take;

		console.log('take: ', this.take, 'Skip: ', skip);
		this.loadingData = true;
		try {
			const response = await this.courseCommentService
				.getByCourseLazy(this.course.Slug, this.take, skip)
				.toPromise();
			this.comments = [...this.comments, ...response.CommentList];
			console.log(response.CommentList);
			++this.page;
			this.loadingData = false;
		} catch (error) {
			console.log(error);
			this.toastr.error(
				'Une erreur est survenue lors de la récupération des articles',
				'Impossible de récupérer les articles'
			);

			this.loadingData = false;
		}
	}
}
