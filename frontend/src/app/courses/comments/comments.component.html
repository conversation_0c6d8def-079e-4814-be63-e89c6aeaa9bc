<div class="course-comment">
	<h1>{{ countAll }} {{ 'blog.show-one-post.comments-label' | translate }}</h1>

	<div *ngIf="!replyingComment" class="comment-box submit-form">
		<h3 *ngIf="!userIsNotConnected" class="reply-title">
			{{ 'blog.show-one-post.make-comment' | translate }}
		</h3>

		<div class="comment-form">
			<div class="custom-form">
				<form
					[formGroup]="createCommentForm"
					(ngSubmit)="onSubmit()"
					action="#"
				>
					<textarea
						[formControlName]="'content'"
						name="comment"
						placeholder="{{
							'blog.show-one-post.comment-content-label' | translate
						}}"
						(keydown.enter)="onSubmit($event)"
						pInputTextarea
						autoResize="autoResize"
					></textarea>
					<button
						class="btn-primary rounded btn-sm"
						*ngIf="!isLoading"
						(click)="onSubmit($event)"
					>
						{{ 'course.comments.comment' | translate }}
					</button>
				</form>

				<app-loading [isLoading]="isLoading"></app-loading>
			</div>
		</div>
	</div>

	<div *ngFor="let comment of comments">
		<app-courses-comments-item
			[comment]="comment"
			[course]="course"
			[userIsNotConnected]="userIsNotConnected"
			(onReplyComment)="handleReplyComment($event)"
			(onDeleteComment)="handleDeleteComment($event)"
		>
		</app-courses-comments-item>
	</div>
</div>

<!-- Row -->
<div class="row" *ngIf="comments.length > 0 && !isLastPage">
	<div class="col-lg-12 col-md-12 col-sm-12">
		<!-- Pagination -->
		<div class="row">
			<div class="col-lg-12 col-md-12 col-sm-12 text-right">
				<button class="btn-primary rounded btn-sm" (click)="paginate()">
					<span *ngIf="!loadingData">
						{{ 'home.page.loadMore' | translate }}&nbsp;<i
							class="ti-reload ml-3"
						></i>
					</span>
					<span *ngIf="loadingData">
						<app-loading [isLoading]="true"></app-loading>
					</span>
				</button>
			</div>
		</div>
	</div>
</div>
<!-- /Row -->
