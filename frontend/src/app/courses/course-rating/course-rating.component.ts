import {
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	SimpleChange,
	SimpleChanges
} from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ToastrService } from 'ngx-toastr';
import { CourseRating } from '~/app/models/course-rating.model';
import { Course } from '~/app/models/course.model';
import { User } from '~/app/models/user.model';
import { CourseRatingService } from '~/app/services/courses/course-rating.service';
import { UserService } from '~/app/services/users/user.service';

@Component({
	selector: 'app-course-rating',
	templateUrl: './course-rating.component.html',
	styleUrls: ['./course-rating.component.scss']
})
export class CourseRatingComponent implements OnInit {
	@Input() course: Course;
	@Input() userIsNotConnected;
	@Input() static = false;
	@Input() globalRating = 0;

	@Output() onGlobalRating: EventEmitter<number> = new EventEmitter<number>();
	@Output() onRating: EventEmitter<number> = new EventEmitter<number>();

	rating = 0;
	// globalRating = 0;
	user: User;

	constructor(
		private rateService: CourseRatingService,
		private toastr: ToastrService,
		private translateService: TranslateService,
		private userService: UserService
	) {}

	async ngOnInit(): Promise<void> {
		if (this.userService.isConnected()) {
			this.user = await this.userService
				.getBySlug(localStorage.getItem(this.userService.LOGINSTORAGE))
				.toPromise();

			const userRating = await this.rateService
				.getCurrentUserRate(this.user, this.course)
				.toPromise();

			if (userRating) {
				this.rating = userRating.Rating;
				this.rateService.ratingSubject.next(this.rating);
				// console.log('user Rating', userRating);
				this.onRating.emit(this.rating);
			}

			// this.rateService.ratingSubject.subscribe((value) => {
			// 	if (this.static) {
			// 		this.calculRating();
			// 	}
			// });
		} else {
			this.static = true;
		}
	}

	// async ngAfterViewInit(): Promise<void> {
	// 	const val = await this.calculRating();
	// 	this.rating = val;
	// 	this.cdRef.markForCheck();
	// }

	async ngOnChanges(changes: SimpleChanges): Promise<void> {
		// console.log('appelé !!', changes);
		await this.calculRating();
	}

	async calculRating(): Promise<void> {
		const rates = (await this.rateService
			.getCourseRate(this.course)
			.toPromise()) as CourseRating[];

		let rating = 0;
		rates.map((rate: CourseRating) => {
			rating += rate.Rating;
		});

		const result = rating / rates.length;
		// this.rating = result;
		if (result) {
			this.globalRating = parseInt(`${result}`);
		} else {
			this.globalRating = 0;
		}
		// this.rating = 3;
		// console.log(this.rating);
		this.onGlobalRating.emit(this.globalRating);
		// this.rateService.globalRatingSubject.next(this.globalRating);
	}

	async selected(event): Promise<void> {
		if (this.static) {
			// event.preventDefault();
			return;
		}
		// alert(eve<:nt);
		if (this.userIsNotConnected && this.userService.isConnected()) {
			// event.preventDefault();
			this.toastr.warning(
				await this.translateService
					.get('blog.postrates.mustauthcontent')
					.toPromise(),
				await this.translateService
					.get('blog.postcomments.mustauthlabel')
					.toPromise()
			);
			return;
		}
		const existingRate = (await this.rateService
			.getRateByCourseUser(this.user, this.course)
			.toPromise()) as CourseRating;

		if (existingRate) {
			existingRate.Rating = event;
			const updatedRating = await this.rateService
				.edit(existingRate)
				.toPromise();

			this.rating = updatedRating.Rating;
			// console.log(this.rating, updatedRating);
		} else {
			const newRating = new CourseRating();
			newRating.Course = this.course;
			newRating.RatedBy = this.user;
			newRating.Rating = event;
			const savedRating = await this.rateService.add(newRating).toPromise();
			this.rating = savedRating.Rating;
		}

		// this.rateService.globalRatingSubject.next(this.globalRating);
		// this.rateService.ratingSubject.next(this.rating);

		// this.onGlobalRating.emit(this.globalRating);
		// this.onRating.emit(this.rating);
		this.onRating.emit(this.rating);
		this.calculRating();

		// this.toastr.success(
		// 	await this.translateService
		// 		.get('course.rating-success-content')
		// 		.toPromise(),
		// 	await this.translateService.get('course.rating-success-label').toPromise()
		// );
	}
}
