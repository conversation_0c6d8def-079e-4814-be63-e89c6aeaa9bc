import { ToastrService } from 'ngx-toastr';
import {
	ChangeDetectorRef,
	Component,
	HostListener,
	OnInit,
	ViewChild
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import {
	Course,
	CourseSearch,
	CourseSearchResult,
	OrderbyType
} from '~/app/models/course.model';
import { Category } from '~/app/models/category.model';
import { User } from '~/app/models/user.model';
import { CourseService } from '../services/courses/course.service';
import { CategoryService } from '../services/categories/category.service';
import { UserService } from '../services/users/user.service';

@Component({
	selector: 'app-courses',
	templateUrl: './courses.component.html',
	styleUrls: ['./courses.component.scss']
})
export class CoursesComponent implements OnInit {
	@ViewChild('mainWrapper') mainWrapper: any;

	skip = 0;
	take: number;
	// countOfItem = 0;
	courseSearchResult: CourseSearchResult;
	coursesModeList = true;
	categorySlug: string;
	category: Category;
	loadMore: boolean;
	search: CourseSearch;
	user: User;

	isLoading = false;
	isLoadingFromPaginate = false;

	paginateNumberList = [];
	totalRecord = 0;
	page = null;

	constructor(
		private courseService: CourseService,
		private categoryService: CategoryService,
		private userService: UserService,
		private activatedRoute: ActivatedRoute,
		private router: Router,
		private changeDetectorRef: ChangeDetectorRef,
		private toastr: ToastrService
	) {}
	async ngOnInit(): Promise<void> {
		this.search = {
			Categories: [],
			Orderby: OrderbyType.RECENT,
			PriceType: [],
			Title: '',
			Type: []
		};

		if (this.userService.getUserConnected()) {
			this.user = await this.userService.getUserConnected().toPromise();
		}

		// this.countOfItem = await this.courseService.getCount().toPromise();
		this.take = CourseService.COURSETAKE;
		this.categorySlug = this.activatedRoute.snapshot.params.id;

		if (this.categorySlug) {
			this.category = await this.categoryService
				.getBySlug(this.categorySlug)
				.toPromise();
			if (
				this.category &&
				!this.search.Categories.find((f) => this.category.Id === f)
			) {
				this.search.Categories.push(this.category.Id);
			}
		}

		this.activatedRoute.queryParamMap.subscribe((r) => {
			const search = r.get('q');
			this.page = +r.get('page') == 0 ? 1 : +r.get('page');

			if (search) {
				this.search.Title = search;
			}
			console.log('this.page == ', this.page);
			this.onCourseLoadFromPaginateListener(this.page);
		});

		/*
    if (this.categorySlug) {
      this.courseService.getAllByCategories(this.categorySlug, this.take, this.skip).subscribe(  (res) =>
        {
          let search = null;
          this.activatedRoute.queryParamMap.subscribe( r => {
            search = r.get('q');
            if (search) {
              this.courses =  res.Courses.filter(f =>
                f.Title.toLowerCase().trim()
                .includes(search.toLowerCase().trim()));
            } else {
              this.courses = res.Courses;
            }
          });

          this.countOfItem = res.Count;
          this.loadMore = this.countOfItem > this.take + this.skip;
        }
      );
    } else {

      this.courseService.getByFilter({take: this.take, skip: this.skip}, this.search).subscribe(
        res => {
          console.log('the search result :', res);
          let search = null;
          this.activatedRoute.queryParamMap.subscribe( r => {
            search = r.get('q');
            if (search) {
              this.courses =  res.filter(f =>
                f.Title.toLowerCase().trim()
                  .includes(search.toLowerCase().trim()));
            } else {
              this.courses = res;
            }
          });

        });
      this.courseService.getAll(this.take, this.skip).subscribe(  (res) => {
        let search = null;
        this.activatedRoute.queryParamMap.subscribe( r => {
          search = r.get('q');
          if (search) {
            this.courses =  res.filter(f =>
              f.Title.toLowerCase().trim()
                .includes(search.toLowerCase().trim()));
          } else {
            this.courses = res;
          }
        });

      });
    }
    */
	}

	onSearch = async (searchMode = null, reinitialSkip = false) => {
		if (searchMode === 'FROM_PAGINATE') {
			this.isLoadingFromPaginate = true;
		} else {
			this.isLoading = true;
		}
		this.changeDetectorRef.detectChanges();

		if (reinitialSkip) {
			this.skip = 0;
			this.page = 1;
		}

		try {
			const res = await this.courseService
				.getByFilter({ take: this.take, skip: this.skip }, this.search)
				.toPromise();

			console.log('the search result :', res);
			if (this.courseSearchResult && searchMode !== 'FROM_PAGINATE') {
				const lastCours = this.courseSearchResult.Courses;
				this.courseSearchResult = res;
				this.courseSearchResult.Courses = [
					...lastCours,
					...this.courseSearchResult.Courses
				];
			} else {
				this.courseSearchResult = res;
			}

			this.totalRecord = this.courseSearchResult.Count;
			const paginateNumber = Math.ceil(
				this.courseSearchResult.Count / CourseService.COURSETAKE
			);
			this.paginateNumberList = new Array(paginateNumber)
				.fill(5, 0, paginateNumber)
				.map((x, i) => i + 1);
			//console.log("paginateNumberpaginateNumberpaginateNumber", paginateNumber, this.paginateNumberList);

			this.loadMore = this.courseSearchResult.Count > this.take + this.skip;

			this.isLoading = false;
			this.isLoadingFromPaginate = false;

			this.changeDetectorRef.detectChanges();
			this.router.navigate(['/courses'], {
				queryParams: { page: this.page }
			});
		} catch (error) {
			this.toastr.error('Error occur during loading courses', 'Error');
		} finally {
			this.isLoading = false;
			this.isLoadingFromPaginate = false;
		}
	};

	onCourseMoreListener(slug: string): void {
		this.skip =
			this.courseSearchResult.Count < this.skip + this.take
				? this.courseSearchResult.Count
				: this.skip + this.take;
		this.loadMore = this.courseSearchResult.Count > this.take + this.skip;
		this.onSearch();
	}

	onCourseLoadFromPaginateListener(pageNumber: number): void {
		this.skip = pageNumber === 1 ? 0 : (pageNumber - 1) * this.take;
		this.page = pageNumber;
		this.onSearch('FROM_PAGINATE');
	}
}
