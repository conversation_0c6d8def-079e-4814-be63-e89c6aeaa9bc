import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { CoursesRoutingModule } from './courses-routing.module';
import { CoursesComponent } from './courses.component';
import { LearningComponent } from '~/app/courses/learning/learning.component';
import { SharedModule } from '~/app/shared/shared.module';
import { FormsModule } from '@angular/forms';
import { CoursesListComponent } from './courses-list/courses-list.component';
import { CoursesGridComponent } from './courses-grid/courses-grid.component';
import { MultiSelectModule } from 'primeng/multiselect';
import { SelectButtonModule } from 'primeng/selectbutton';
import { InputTextModule } from 'primeng/inputtext';
import { CoursesSearchComponent } from './courses-search/courses-search.component';
import { CoursesCreateComponent } from './courses-create/courses-create.component';
import { CoursesManageComponent } from './courses-manage/courses-manage.component';
import { CoursesQuizComponent } from './courses-quiz/courses-quiz.component';
import { CoursesDetailComponent } from './courses-detail/courses-detail.component';

import { ChipsModule } from 'primeng/chips';
import { StepsModule } from 'primeng/steps';
import { DropdownModule } from 'primeng/dropdown';
import { CoursesManageInformationComponent } from './courses-manage/courses-manage-information/courses-manage-information.component';
import { CoursesManagePresentationComponent } from './courses-manage/courses-manage-presentation/courses-manage-presentation.component';
import { CoursesManagePricingComponent } from './courses-manage/courses-manage-pricing/courses-manage-pricing.component';
import { CoursesManageStructureComponent } from './courses-manage/courses-manage-structure/courses-manage-structure.component';
import { ButtonModule } from 'primeng/button';
import { RippleModule } from 'primeng/ripple';
import { CoursesManageMessagingComponent } from './courses-manage/courses-manage-messaging/courses-manage-messaging.component';
import { LyTabsModule } from '@alyle/ui/tabs';
import { FieldsetModule } from 'primeng/fieldset';
import { CoursesManageContentModalComponent } from './courses-manage/courses-manage-content-modal/courses-manage-content-modal.component';
import { SidebarModule } from 'primeng/sidebar';
import { PanelModule } from 'primeng/panel';
import { MenuModule } from 'primeng/menu';
import { RadioButtonModule } from 'primeng/radiobutton';
import { TabViewModule } from 'primeng/tabview';
import { DividerModule } from 'primeng/divider';
import { ChipModule } from 'primeng/chip';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { ScrollPanelModule } from 'primeng/scrollpanel';
import { CourseReaderComponent } from '~/app/courses/course-reader/course-reader.component';
import { CoursesManageEditSectionComponent } from './courses-manage/courses-manage-edit-section/courses-manage-edit-section.component';
import { CoursesManageQuizModalComponent } from './courses-manage/courses-manage-quiz-modal/courses-manage-quiz-modal.component';

import { CheckboxModule } from 'primeng/checkbox';
import { CoursesManageQuizFactorComponent } from './courses-manage/courses-manage-quiz-factor/courses-manage-quiz-factor.component';
// tslint:disable-next-line:max-line-length
import { CoursesManageContentFactorComponent } from './courses-manage/courses-manage-content-factor/courses-manage-content-factor.component';
import { CommentsComponent } from './comments/comments.component';
import { CommentsItemComponent } from './comments/comments-item/comments-item.component';
import { CourseManageAssignmentModalComponent } from './courses-manage/course-manage-assignment-modal/course-manage-assignment-modal.component';
import { CourseManageAssignmentFactorComponent } from './courses-manage/course-manage-assignment-factor/course-manage-assignment-factor.component';
import { PdfViewerModule } from 'ng2-pdf-viewer';
import { PlyrModule } from 'ngx-plyr';
import { PdfVideoEbookComponent } from '~/app/courses/pdf-video-ebook/pdf-video-ebook.component';
import { PdfVideoEbookModalComponent } from '~/app/courses/pdf-video-ebook/pdf-video-ebook-modal/pdf-video-ebook-modal.component';
import { CourseManagePreviewComponent } from './courses-manage/course-manage-preview/course-manage-preview.component';
import { CoursesReaderPdfComponent } from './course-reader/courses-reader-pdf/courses-reader-pdf.component';
import { CoursesReaderEbookComponent } from './course-reader/courses-reader-ebook/courses-reader-ebook.component';
import { CoursesReaderVideoComponent } from './course-reader/courses-reader-video/courses-reader-video.component';
import { CoursesReaderQuizComponent } from './course-reader/courses-reader-quiz/courses-reader-quiz.component';
import { CoursesReaderHeaderComponent } from './course-reader/courses-reader-header/courses-reader-header.component';
import { CoursesReaderContentComponent } from './course-reader/courses-reader-content/courses-reader-content.component';
import { CoursesListToFinishComponent } from './courses-list/courses-list-to-finish/courses-list-to-finish.component';
import { CoursesListSelectForYouComponent } from './courses-list/courses-list-select-for-you/courses-list-select-for-you.component';
import { CoursesCategorieComponent } from './courses-categorie/courses-categorie.component';
import { DialogModule } from 'primeng/dialog';
import { CourseItemHorizontalComponent } from './course-item-horizontal/course-item-horizontal.component';
import { CourseNoteItemComponent } from './course-reader/courses-reader-content/course-note-item/course-note-item.component';

import { ProgressBarModule } from 'primeng/progressbar';

@NgModule({
	declarations: [
		CoursesComponent,
		LearningComponent,
		CourseManageAssignmentModalComponent,
		CourseManageAssignmentFactorComponent,
		CoursesListComponent,
		CoursesGridComponent,
		CoursesSearchComponent,
		CoursesCreateComponent,
		CoursesManageComponent,
		CoursesQuizComponent,
		CoursesDetailComponent,
		PdfVideoEbookComponent,
		PdfVideoEbookModalComponent,
		CoursesManageInformationComponent,
		CoursesManagePresentationComponent,
		CoursesManagePricingComponent,
		CoursesManageStructureComponent,
		CoursesManageMessagingComponent,
		CoursesManageContentModalComponent,
		CourseReaderComponent,
		CoursesManageEditSectionComponent,
		CoursesManageQuizModalComponent,
		CoursesManageQuizFactorComponent,
		CoursesManageContentFactorComponent,
		CommentsComponent,
		CommentsItemComponent,
		CourseManagePreviewComponent,
		CoursesReaderPdfComponent,
		CoursesReaderEbookComponent,
		CoursesReaderVideoComponent,
		CoursesReaderQuizComponent,
		CoursesReaderHeaderComponent,
		CoursesReaderContentComponent,
		CoursesListToFinishComponent,
		CoursesListSelectForYouComponent,
		CoursesCategorieComponent,
		CourseItemHorizontalComponent,
		CourseNoteItemComponent
	],
	exports: [CourseReaderComponent],
	imports: [
		CommonModule,
		CoursesRoutingModule,
		SelectButtonModule,
		InputTextModule,
		ChipsModule,
		StepsModule,
		DropdownModule,
		ButtonModule,
		RippleModule,
		PdfViewerModule,
		PlyrModule,
		LyTabsModule,
		FieldsetModule,
		ScrollPanelModule,
		ChipModule,
		SharedModule,
		FormsModule,
		MultiSelectModule,
		DividerModule,
		InputTextareaModule,
		SelectButtonModule,
		InputTextModule,
		ChipsModule,
		TabViewModule,
		SidebarModule,
		ButtonModule,
		PanelModule,
		MenuModule,
		ScrollPanelModule,
		RadioButtonModule,
		CheckboxModule,
		DialogModule,
		ProgressBarModule
	]
})
export class CoursesModule {}
