<form method="post" class="row" (ngSubmit)="onSearchFilteredCourses($event)">
	<div class="form-group col-12">
		<input
			class="form-control"
			type="search"
			[(ngModel)]="search.Title"
			name="search"
			id="search"
			[placeholder]="'course.details.searchCourses' | translate"
		/>
	</div>

	<div class="form-group col-12">
		<h4 class="side_title">
			{{ 'course.details.courseCategories' | translate }}
		</h4>
		<p-multiSelect
			[options]="categories"
			[(ngModel)]="search.Categories"
			display="chip"
			name="categories"
			optionValue="Id"
			id="categories"
			[defaultLabel]="'course.details.selectCategs' | translate"
			optionLabel="Title"
		></p-multiSelect>
	</div>

	<div class="form-group col-12">
		<h4 class="side_title">
			{{ 'course.details.courseCategories' | translate }}
		</h4>
		<div class="p-field-checkbox d-flex align-items-center">
			<p-checkbox
				name="TypeOfCourse"
				[value]="contentType.VIDEO"
				[(ngModel)]="search.Type"
				id="TypeOfCourse2"
			>
			</p-checkbox>
			<label for="TypeOfCourse2" class="mx-2 mt-2">Video</label>
		</div>
		<div class="p-field-checkbox d-flex align-items-center">
			<p-checkbox
				name="TypeOfCourse"
				[value]="contentType.PDF"
				[(ngModel)]="search.Type"
				id="TypeOfCourse3"
			>
			</p-checkbox>
			<label for="TypeOfCourse3" class="mx-2 mt-2">PDF</label>
		</div>

		<div class="p-field-checkbox d-flex align-items-center">
			<p-checkbox
				name="TypeOfCourse"
				[value]="contentType.EBOOK"
				[(ngModel)]="search.Type"
				id="TypeOfCourse4"
			>
			</p-checkbox>
			<label for="TypeOfCourse4" class="mx-2 mt-2">Ebook</label>
		</div>
	</div>

	<div class="form-group col-12">
		<h4 class="side_title">{{ 'course.details.price' | translate }}</h4>

		<div class="p-field-checkbox d-flex align-items-center">
			<p-checkbox
				name="PriceType"
				[value]="1"
				[(ngModel)]="search.PriceType"
				id="TypeOfPrice1"
			></p-checkbox>
			<label for="TypeOfPrice1" class="mx-2 mt-2"
				>Free ({{ courseSearchResult ? courseSearchResult.Free : '' }})</label
			>
		</div>

		<div class="p-field-checkbox d-flex align-items-center">
			<p-checkbox
				name="PriceType"
				[value]="2"
				[(ngModel)]="search.PriceType"
				id="TypeOfPrice2"
			></p-checkbox>
			<label for="TypeOfPrice2" class="mx-2 mt-2"
				>{{ 'course.details.paid' | translate }} ({{
					courseSearchResult ? courseSearchResult.Paid : ''
				}})</label
			>
		</div>
	</div>

	<div class="form-group col-12">
		<button
			type="submit"
			class="btn btn-theme full-width"
			[disabled]="isLoading"
		>
			<span *ngIf="isLoading" class="mr-2"
				><i class="fas fa-spin fa-spinner"></i
			></span>
			{{ 'course.details.filterRes' | translate }}
		</button>
	</div>
</form>
