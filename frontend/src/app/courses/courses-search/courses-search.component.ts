import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import {
	ContentType,
	Course,
	CourseSearch,
	CourseSearchResult,
	SKIP,
	TAKE
} from '~/app/models/course.model';
import { Category } from '~/app/models/category.model';
import { CategoryService } from '~/app/services/categories/category.service';
import { CourseService } from '~/app/services/courses/course.service';
import { Subscription } from 'rxjs';

@Component({
	selector: 'app-courses-search',
	templateUrl: './courses-search.component.html',
	styleUrls: ['./courses-search.component.scss']
})
export class CoursesSearchComponent implements OnInit {
	@Input()
	search: CourseSearch;

	@Output()
	searchChange: EventEmitter<CourseSearch> = new EventEmitter<CourseSearch>();

	@Input()
	skip = 0;

	@Output()
	skipChange: EventEmitter<number> = new EventEmitter<number>();

	@Input()
	take: number;

	@Output()
	takeChange: EventEmitter<number> = new EventEmitter<number>();

	@Input() courseSearchResult: CourseSearchResult;
	@Output()
	courseSearchResultChange: EventEmitter<CourseSearchResult> = new EventEmitter<CourseSearchResult>();

	@Output()
	searchCourse: EventEmitter<any> = new EventEmitter<any>();

	@Input()
	isLoading = false;

	@Output()
	courseSearchLoadingEmitter: EventEmitter<boolean> = new EventEmitter<boolean>();

	categories: Category[];
	contentType = ContentType;
	constructor(
		private categoryService: CategoryService,
		private courseService: CourseService
	) { }

	async ngOnInit(): Promise<void> {
		console.log('$$$ Search: ', this.search);
		this.categories = await this.categoryService.getAll().toPromise();
	}

	setsearch(): void {
		this.searchChange.emit(this.search);
	}

	onSearchFilteredCourses(e): void {
		e.preventDefault();
		/* console.log('serach is :', this.search);
		this.take = CourseService.COURSETAKE;
		this.skip = SKIP;
		this.skipChange.emit(SKIP);
		this.takeChange.emit(TAKE);

		this.isLoading = true;
		this.courseSearchLoadingEmitter.emit(true);

		this.courseService
			.getByFilter({ take: this.take, skip: this.skip }, this.search)
			.subscribe((res) => {
				console.log('the search result on the filter is :', res);
				this.courseSearchResult = res;
				this.courseSearchResultChange.emit(this.courseSearchResult);
				this.courseService.courseSearchResultSubject.next(this.courseSearchResult);
				this.isLoading = false;
				this.courseSearchLoadingEmitter.emit(false);
				
			}, err => { this.isLoading = false; this.courseSearchLoadingEmitter.emit(false); });
 */
		console.log("ZZZZZZZZZZ");
		this.searchCourse.emit('Searching course');
	}
}
