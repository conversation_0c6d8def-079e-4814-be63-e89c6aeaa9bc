import { Component, Input, OnInit } from '@angular/core';
import { ContentType, Course } from '~/app/models/course.model';
import { Media } from '~/app/models/media.model';
import { environment } from '~/environments/environment';
import { UtilsService } from '~/app/services/utils.service';

@Component({
  selector: 'app-course-item-horizontal',
  templateUrl: './course-item-horizontal.component.html',
  styleUrls: ['./course-item-horizontal.component.scss']
})
export class CourseItemHorizontalComponent implements OnInit {
  @Input()
  course: Course;
  color: string;
  title: string;
  contentType = ContentType;
  profile = '/assets/img/avatar.png';
  cover = '/assets/img/700x500.png';
  resume: string;
  @Input()
  position: number;

  time: string;
  countViews: number;

  constructor(private utilsService: UtilsService) { }


  ngOnInit(): void {
    this.resume = this.utilsService.getHtml(this.course.Resume, 118);
    // this.setRandomColor();
    console.log(this.course)
    if (this.course.Title !== null) {
      this.title = this.course.Title.length > 70 ? this.course.Title.substring(0, 67) + '...' : this.course.Title;
    } else {
      this.title = '';
    }
    if (this.course.CreatedBy.Photo) {
      this.profile = `${environment.path}/${this.course.CreatedBy.Photo.Hashname}`;
    }
    if (this.course.CoverImage && !this.course.CoverImage.Hashname.includes(environment.path)) {
      this.cover = `${environment.path}/${this.course.CoverImage.Hashname}`;
    } else if (this.course.CoverImage) {
      this.cover = this.course.CoverImage.Hashname;
    }
    if (this.course.Sections.length !== 0) {
      this.time = this.utilsService.getCourseTime(this.course);
    } else {
      this.time = '';
    }
    this.countViews = this.utilsService.getCourseCountViews(this.course).length;

  }
  setRandomColor(): void {
    const items = ['facts-1', 'facts-2', 'facts-3', 'facts-4', 'facts-5'];
    this.color = items[Math.floor(Math.random() * items.length)];
  }



  scroolItemActivity(e, scroolLef: boolean): void {
    e.preventDefault();
    if (scroolLef) {
      document.getElementById('cources_facts_list').scrollLeft -= window.innerWidth;
    } else {
      document.getElementById('cources_facts_list').scrollLeft += window.innerWidth;
    }
  }

}

