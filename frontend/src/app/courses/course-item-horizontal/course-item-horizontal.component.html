<ng-template #popContent>
    <div class="d-flex flex-column py-2">
        <div class="h6 font-weight-bold">{{ course.Title }}</div>
        <small class="text-success">{{ course.UpdatedAt | dateAgo }}</small>
        <p class="" [innerHTML]="resume"></p>
        <ol class="">
            <li *ngFor="let goal of course.Goals; let i = index">
                {{ goal }}
            </li>
        </ol>
    </div>
</ng-template>
<div class="row border-top pt-3 pb-3" [ngbPopover]="popContent" triggers="mouseenter:mouseleave">

    <div class="col-md-4 col-sm-3">
        <a routerLink="/courses/{{ course.Slug }}"><img [src]="cover" class="img-fluid" alt="" /></a>

    </div>
    <div class="col-md-8 col-sm-9">
        <div class="row">
            <div class="col-sm-12 col-md-9">

                <div class="education_block_body p-0 pt-2">
                    <span class="bl-title h6"><a routerLink="/courses/{{ course.Slug }}">
              {{ title }}
            </a></span>
                </div>
                <small>{{course.Message.length
          < 100 ? (course.Message) : (course.Message|slice:0:120)+ '...' }}</small>

                <div class="cources_facts pl-0">
                    <div class="cources_facts_list my-2" id="cources_facts_list">
                        <div class="badge badge-secondary mx-1" *ngFor="let keyword of course.Keywords">
                            {{ keyword }}
                        </div>
                    </div>
                </div>
                <div class="education_block_author">
                    <div class="path-img">
                        <a [routerLink]="'/instructors/' + course.CreatedBy.Slug"><img [src]="profile" class="img-fluid" alt="" /></a>
                    </div>
                    <div class="d-flex flex-column">
                        <a [routerLink]="'/instructors/' + course.CreatedBy.Slug" class="">{{
                  course.CreatedBy.Firstname
                  }}</a>
                        <span class="d-flex small text-muted">{{
                  course.CreatedBy.Profil
                  }}</span>
                    </div>
                    <div class="ml-0 type-doc">
                        <i class="far fa-file-pdf text-primary font-weight-bold" *ngIf="contentType.PDF === course.Format"></i>
                        <i class="fas fa-book text-primary font-weight-bold" *ngIf="contentType.EBOOK === course.Format"></i>
                        <i class="far fa-play-circle text-primary font-weight-bold" *ngIf="contentType.VIDEO === course.Format"></i>
                    </div>
                </div>
            </div>
            <div class="col-sm-12 col-md-3 pt-3">
                <b>{{course.Price}} {{course.Currency}}</b>
            </div>
        </div>
    </div>
</div>
