import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CourseContent } from '~/app/models/course-content.model';
import { ContentType, Course } from '~/app/models/course.model';
import { Section } from '~/app/models/section.model';
import { CourseSection } from '~/app/models/course-section.model';
import { CourseSectionTest } from '~/app/models/course-section-test.model';
import { CourseTranscription } from '~/app/models/course-transcription.model';
import { UtilsService } from '~/app/services/utils.service';
import { User } from '~/app/models/user.model';
import { ToastrService } from 'ngx-toastr';
import { TranslateService } from '@ngx-translate/core';
import { CourseView } from '~/app/models/course-view.model';
import { CourseViewService } from '~/app/services/courses/course-view.service';

@Component({
	selector: 'app-courses-reader-video',
	templateUrl: './courses-reader-video.component.html',
	styleUrls: ['./courses-reader-video.component.scss']
})
export class CoursesReaderVideoComponent implements OnInit {
	@Input()
	user: User;
	@Input()
	courseViews: CourseView[];
	@Output()
	courseViewsChange: EventEmitter<CourseView[]> = new EventEmitter<
		CourseView[]
	>();

	@Input()
	currentContent: CourseContent;
	@Output()
	currentContentChange: EventEmitter<CourseContent> = new EventEmitter<CourseContent>();

	@Input()
	currentSection: CourseSection;
	@Output()
	currentSectionChange: EventEmitter<CourseSection> = new EventEmitter<CourseSection>();

	@Input()
	currentTranscription: string;
	@Output()
	currentTranscriptionChange: EventEmitter<string> = new EventEmitter<string>();

	@Input()
	course: Course;

	@Output()
	emitResetCourseSectionTest = new EventEmitter<CourseSectionTest>();

	autoPlay = false;

	contentType = ContentType;
	constructor(
		private utilsService: UtilsService,
		private courseViewService: CourseViewService,
		private toastr: ToastrService,
		private translateService: TranslateService
	) {}

	async ngOnInit(): Promise<void> {
		this.emitResetCourseSectionTest.emit();
		await this.setSection(this.currentSection, true);
		this.setTranscription();
	}

	setcurrentSection(val: CourseSection): void {
		this.currentSectionChange.emit(val);
		this.currentSection = val;
	}

	setcurrentContent(val: CourseContent): void {
		this.currentContentChange.emit(val);
		this.currentContent = val;
	}

	async setTranscription(): Promise<void> {
		if (
			this.currentContent &&
			this.currentContent.Type === this.contentType.VIDEO &&
			this.currentContent.Transcriptions &&
			this.currentContent.Transcriptions.length > 0
		) {
			const currentTranscription = await this.utilsService
				.getElementFromUrl(
					(this.currentContent.Transcriptions[0] as CourseTranscription).Media
						.Hashname
				)
				.toPromise();

			this.setcurrentTranscription(currentTranscription);
		}
	}

	setcurrentTranscription(val: string): void {
		this.currentTranscriptionChange.emit(val);
		this.currentTranscription = val;
	}

	async setSection(
		section: CourseSection,
		onStart: boolean = false
	): Promise<void> {
		this.emitResetCourseSectionTest.emit();
		let contentLastUpdate = null;
		if (onStart) {
			/* this.courseViews = await this.courseViewService
        .GetAllViewCourseByUserAndCourse(this.user.Slug, this.course.Slug)
        .toPromise(); */
			const sort = this.courseViews?.sort((a, b) =>
				a.UpdatedAt > b.UpdatedAt ? -1 : 1
			);
			contentLastUpdate = sort && sort.length > 0 ? sort[0] : null;
			if (
				contentLastUpdate &&
				contentLastUpdate.Content.Type !== ContentType.QUIZ &&
				contentLastUpdate.Content.Type !== ContentType.ASSIGNMENT
			) {
				section = this.course.Sections.find(
					(f) => f.Id === contentLastUpdate.Content.Section.Id
				);
				const content = section.Contents.find(
					(f) => f.Id === contentLastUpdate.Content.Id
				);
				this.setcurrentContent(content as CourseContent);
			}
		}
		this.currentSection = section;
		const index = section.Contents.map((f) => f.Id).indexOf(
			this.currentContent.Id
		);

		this.setcurrentSection(section);

		if (contentLastUpdate) {
			this.onNext();
		}
	}

	onNext(): void {
		const index = this.currentSection.Contents.map((f) => f.Id).indexOf(
			this.currentContent.Id
		);
		if (index < this.currentSection.Contents.length) {
			const content = this.currentSection.Contents[index + 1] as CourseContent;
			if (content) {
				this.setcurrentContent(content);
				this.setSection(this.currentSection);
			} else {
				this.onChangeSection(null, true);
			}
		} else {
			this.onChangeSection(null, true);
		}
	}

	onChangeSection(e, next: boolean): void {
		if (e) {
			e.preventDefault();
		}
		let index = this.course.Sections.findIndex(
			(f) => f.Id === this.currentSection.Id
		);
		let first = true;
		let content: CourseContent;
		if (next && index + 1 < this.course.Sections.length) {
			let s: CourseSection;
			do {
				if (!first) {
					index = this.course.Sections.findIndex((f) => f.Id === s.Id);
				} else {
					first = false;
				}

				if (next && index + 1 < this.course.Sections.length) {
					s = this.course.Sections[index + 1];
					content =
						s.Contents && s.Contents.length > 0
							? (s.Contents[0] as CourseContent)
							: null;
				}
			} while (
				content &&
				(content.Type === ContentType.QUIZ ||
					content.Type === ContentType.ASSIGNMENT) &&
				index + 1 < this.course.Sections.length
			);

			if (
				content &&
				content.Type !== ContentType.QUIZ &&
				content.Type !== ContentType.ASSIGNMENT
			) {
				this.setcurrentContent(content);
				this.setSection(s);
			}
		} else if (!next && index > 0) {
			let s: CourseSection;
			do {
				if (!first) {
					index = this.course.Sections.findIndex((f) => f.Id === s.Id);
				} else {
					first = false;
				}

				if (!next && index > 0) {
					s = this.course.Sections[index - 1];
					content =
						s.Contents && s.Contents.length > 0
							? (s.Contents[0] as CourseContent)
							: null;
				}
			} while (
				content &&
				(content.Type === ContentType.QUIZ ||
					content.Type === ContentType.ASSIGNMENT) &&
				index > 0
			);
			if (
				content &&
				content.Type !== ContentType.QUIZ &&
				content.Type !== ContentType.ASSIGNMENT
			) {
				this.setcurrentContent(content);
				this.setSection(s);
			}
		}
	}

	async onFinish(): Promise<void> {
		try {
			const message = await this.translateService
				.get('home.register.success')
				.toPromise();
			let cv: CourseView = this.courseViews?.find(
				(f) => f.Content.Id === this.currentContent.Id
			);

			if (!cv) {
				cv = new CourseView(
					null,
					'123',
					null,
					null,
					null,
					new Date(),
					{ Id: this.user.Id } as any,
					{ Id: this.currentContent.Id } as any,
					this.currentContent.TotalTime
				);

				cv.ProgressRating = this.currentContent.TotalTime;
				cv = await this.courseViewService.add(cv).toPromise();

				if (cv) {
					this.courseViews.push(cv);
				}
			}

			// this.toastr.success(message, 'Brain-maker');
			setTimeout(() => {
				this.onNext();
				this.autoPlay = true;
			}, 3000);
		} catch (e) {
			console.log(e);
			const message = await this.translateService
				.get('home.register.error')
				.toPromise();
			this.toastr.error(message, 'Brain-maker');
		}
	}

	onProgress(ev: Plyr.PlyrEvent): void {
		// console.log('video progress : ', ev);
	}

	onPauseChange(ev: Plyr.PlyrEvent): void {
		console.log('video pause : ', ev);
	}

	async onEndedChange(ev: Plyr.PlyrEvent): Promise<void> {
		console.log('video ended : ', ev);
		await this.onFinish();
	}
}
