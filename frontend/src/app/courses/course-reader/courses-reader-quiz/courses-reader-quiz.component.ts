import {
	Component,
	ElementRef,
	EventEmitter,
	Input,
	OnInit,
	Output,
	ViewChild
} from '@angular/core';
import { Course } from '~/app/models/course.model';
import {
	CourseSectionTest,
	QuestionType
} from '~/app/models/course-section-test.model';
import { Section } from '~/app/models/section.model';
import { CourseSection } from '~/app/models/course-section.model';
import { CourseContent } from '~/app/models/course-content.model';
import { User } from '~/app/models/user.model';
import { CourseView } from '~/app/models/course-view.model';
import { ToastrService } from 'ngx-toastr';
import { TranslateService } from '@ngx-translate/core';
import { CourseViewService } from '~/app/services/courses/course-view.service';

interface QuestionUserAnswer {
	slug: string;
	value: any;
}

interface SectionTestAnswers {
	sectionTest: CourseSectionTest;
	answers: QuestionUserAnswer[];
}

@Component({
	selector: 'app-courses-reader-quiz',
	templateUrl: './courses-reader-quiz.component.html',
	styleUrls: ['./courses-reader-quiz.component.scss']
})
export class CoursesReaderQuizComponent implements OnInit {
	reply: any;
	@Input()
	user: User;

	@Input()
	courseViews: CourseView[];
	@Output()
	courseViewsChange: EventEmitter<CourseView[]> = new EventEmitter<
		CourseView[]
	>();

	@Input()
	course: Course;

	@Output()
	emitResetContent = new EventEmitter<CourseContent>();

	@Input()
	currentSectionTest: CourseSectionTest;
	@Output()
	currentSectionTestChange: EventEmitter<CourseSectionTest> = new EventEmitter<CourseSectionTest>();

	@Input()
	currentSection: CourseSection;
	@Output()
	currentSectionChange: EventEmitter<CourseSection> = new EventEmitter<CourseSection>();

	currentPrevSectionTest: CourseSectionTest;
	currentNextSectionTest: CourseSectionTest;

	questionType = QuestionType;
	oneChooseValue: any;

	endOfTestSection = false;
	questionNumber = 1;
	canContinue = false;

	userAnswers: SectionTestAnswers[] = [];
	fails = [];
	wons = [];
	testOver = false;
	showScore = false;
	userScore = 0;
	userGoodAnswers = 0;
	totalQuestions = 0;
	totalGoodAnswers = 0;

	radioButtonStatus: any = {};

	test = 0;

	@ViewChild('quizRadio') quizRadio: any;

	constructor(
		private courseViewService: CourseViewService,
		private toastr: ToastrService,
		private translateService: TranslateService
	) {}

	ngOnInit(): void {
		this.setSection(this.currentSection);
		this.currentNextSectionTest = this.currentSection
			.Contents[1] as CourseSectionTest;
		this.totalQuestions = this.currentSection.Contents.length;
		this.currentSection.Contents.map((c: CourseSectionTest) => {
			this.radioButtonStatus[c.Id] = '';
			c.Questions?.map((q) => {
				if (q.IsRightAnswer) {
					++this.totalGoodAnswers;
				}
			});
		});

		console.log('--->>', this.radioButtonStatus);

		console.log(
			'TotalQuestion: ',
			this.totalQuestions,
			'TotalGoodAnwsers',
			this.totalGoodAnswers
		);
	}

	setcurrentSection(val: CourseSection): void {
		this.currentSectionChange.emit(val);
		this.currentSection = val;
	}

	setcurrentSectionTest(val: CourseSectionTest): void {
		this.currentSectionTest = val;
	}

	async nextAndPreviousTest(
		e,
		contentSection: CourseSectionTest
	): Promise<void> {
		e.preventDefault();
		this.currentSectionTest = contentSection;
		this.setSectionTest(this.currentSection);
		console.log('Test: ', this.currentSectionTest);

		console.log('saved value: ', this.radioButtonStatus);
	}

	async setSectionTest(section: CourseSection): Promise<void> {
		this.emitResetContent.emit();
		this.currentSection = section;
		const index = section.Contents.map((f) => f.Id).indexOf(
			this.currentSectionTest.Id
		);

		this.questionNumber = index + 1;

		if (index > 0) {
			this.currentPrevSectionTest = section.Contents[
				index - 1
			] as CourseSectionTest;
		} else {
			this.currentPrevSectionTest = null;
		}

		if (index < section.Contents.length - 1) {
			console.log('nexxxxt');
			this.currentNextSectionTest = section.Contents[
				index + 1
			] as CourseSectionTest;
		} else {
			console.log('enddd');
			this.endOfTestSection = true;
			this.currentNextSectionTest = null;
		}

		console.log(
			'inspects : ',
			this.currentNextSectionTest,
			this.currentPrevSectionTest
		);
		this.courseViews = await this.courseViewService
			.GetAllViewCourseByUserAndCourse(this.user.Slug, this.course.Slug)
			.toPromise();
	}

	setChecked(): void {
		/* if (this.currentNextSectionTest.Questions &&
			 this.currentNextSectionTest.Questions.length > 0 &&
			 +this.currentNextSectionTest.TypeOfTest === QuestionType.SELECT ||
			 +this.currentNextSectionTest.TypeOfTest === QuestionType.RADIO) {
			 this.oneChooseValue = this.currentNextSectionTest.Questions.find(f => f.IsRightAnswer)?.Slug.substr(0, 5);
			 // console.log(this.oneChooseValue);
			 // console.log('courseSectionTest : ', this.courseSectionTest);
		 }

		 */
	}

	async setSection(section: CourseSection): Promise<void> {
		this.emitResetContent.emit();
		this.currentSection = section;

		this.setcurrentSection(section);

		this.courseViews = await this.courseViewService
			.GetAllViewCourseByUserAndCourse(this.user.Slug, this.course.Slug)
			.toPromise();
	}

	async onFinish(e): Promise<void> {
		e.preventDefault();
		try {
			const message = await this.translateService
				.get('home.register.success')
				.toPromise();
			let cv: CourseView = this.courseViews?.find(
				(f) => f.Content.Id === this.currentSectionTest.Id
			);
			if (!cv) {
				cv = new CourseView(
					null,
					'123',
					null,
					null,
					null,
					new Date(),
					{ Id: this.user.Id } as any,
					{ Id: this.currentSectionTest.Id } as any,
					this.currentSectionTest.TotalTime
				);
			}

			if (cv && !cv.Id) {
				cv = await this.courseViewService.add(cv).toPromise();
			} else {
				cv.ProgressRating = this.currentSectionTest.TotalTime;
				cv = await this.courseViewService.edit(cv).toPromise();
			}

			if (cv) {
				this.toastr.success(message, 'Brain-maker');
				// this.setSectionTest(this.currentSection);
			}
		} catch (e) {
			console.log(e);
			const message = await this.translateService
				.get('home.register.error')
				.toPromise();
			this.toastr.error(message, 'Brain-maker');
		}

		let score = 0;
		// this.userAnswers.map((userAnswer) => {
		// 	// this.onValidate(userAnswer.sectionTest);
		// });
		this.currentSection.Contents.map((c: CourseSectionTest) => {
			const userGoodAnswersCount = this.onValidate(c);
			if (userGoodAnswersCount) {
				score += userGoodAnswersCount;
			}
		});

		this.userScore = score;

		this.testOver = true;
		this.onShowScore();
	}

	onValidate(sectionTest: CourseSectionTest): number {
		let countTotalGoodAnwsers = 0;
		sectionTest.Questions.map((q) => {
			if (q.IsRightAnswer) {
				this.wons.push(q.Slug);
				++countTotalGoodAnwsers;
			}
		});
		// console.log(this.currentSectionTest);
		// console.log('all answser: ', this.currentSectionTest.Questions);
		// console.log('user Answsers', this.userAnswers);

		// total Good answers for the section
		sectionTest.Questions.length;
		let countGoodAnswers = 0;

		console.log('Resp: ', this.userAnswers);
		this.userAnswers
			.find((t) => t.sectionTest.Slug === sectionTest.Slug)
			?.answers.map((aswr, index: number) => {
				// if (aswr.value) {
				if (aswr) {
					const result = this.isCorrectAnwser(sectionTest, aswr);
					// console.log('***result: ', result);
					console.log(
						result ? aswr.slug + ': bonne rep' : aswr.slug + ': mauvaise rep'
					);

					if (!result) {
						this.fails = [...this.fails, aswr.slug];
					} else {
						console.log('+++anwsers: ', countGoodAnswers);
						countGoodAnswers += 1;
					}
				}
			});

		// if (countGoodAnswers > 0) {
		// console.log('Good anwers', countGoodAnswers);
		// console.log('Questions count: ', sectionTest.Questions.length);
		this.userGoodAnswers += countGoodAnswers;
		// this.userScore += countGoodAnswers / sectionTest.Questions.length;
		// }
		return countGoodAnswers / countTotalGoodAnwsers;
	}

	/**
	 * Save all the user answers
	 * @param e
	 * @param name
	 */
	onAnwser(e: any, name: string, slug: string): void {
		if (this.testOver) {
			return;
		}
		if (this.fails.includes(slug)) {
			this.fails.splice(
				this.fails.indexOf(this.fails.find((item) => item === slug))
			);
		}
		// console.log(this.currentSectionTest);
		// console.log(name);
		// console.log(e.currentTarget.checked);

		// Create current answer
		const newAnswer: QuestionUserAnswer = {
			slug,
			value: e.currentTarget.checked ? e.currentTarget.checked : true
		};
		console.log('New answer: ', newAnswer);
		// Find Question Section in user answer if exist.
		const currentSectionAnswers = this.userAnswers.find(
			(sa: SectionTestAnswers) =>
				sa.sectionTest.Slug === this.currentSectionTest.Slug
		);

		if (currentSectionAnswers) {
			// If user already provide answer for the Question section, update ansers and
			// add newAnser
			const index = this.userAnswers.indexOf(currentSectionAnswers);
			const anwserIndex = this.userAnswers[index].answers.indexOf(
				this.userAnswers[index].answers.find(
					(ans) => ans.slug === newAnswer.slug
				)
			);
			// event come form radio button
			if (!e.currentTarget.checked) {
				// Update or create a new anwser value
				// if (anwserIndex > -1) {
				this.userAnswers[index].answers[0] = newAnswer;

				// } else {
				// 	this.userAnswers[index].answers.push({ ...newAnswer });
				// }
			} else {
				// Update or create a new anwser value
				if (anwserIndex > -1) {
					this.userAnswers[index].answers[anwserIndex].value = newAnswer.value;
				} else {
					this.userAnswers[index].answers.push({ ...newAnswer });
				}
			}
		} else {
			// Create a new SectionTestAnswer for the currentSectionTest, and store them inside
			//  user newAnwser
			const currentSectionTestAnswers: SectionTestAnswers = {
				sectionTest: this.currentSectionTest,
				answers: [{ ...newAnswer }]
			};
			// Finally store the sectionTestAnswer in the global userAnswsers
			this.userAnswers.push(currentSectionTestAnswers);
		}

		console.log('User globals answsers: ', this.userAnswers);
	}

	isCorrectAnwser(
		sectionTest: CourseSectionTest,
		answser: QuestionUserAnswer
	): boolean {
		// console.log('heyehhf', sectionTest, answser);
		const quest = sectionTest.Questions.find((q) => q.Slug === answser.slug);
		// console.log('qqqqqqqqqqqqqqqqqqq', quest);
		if (quest) {
			// console.log('***quest', quest, 'value: ', answser.value);
			return quest.IsRightAnswer === answser.value;
		}
		return false;
	}

	getUserAnswer(question): any {
		const userAnswer = this.userAnswers
			.find((t) => t.sectionTest.Slug === this.currentSectionTest.Slug)
			?.answers.find((aswr, index: number) => aswr.slug === question.Slug);

		if (!userAnswer) {
			return false;
		}
		return userAnswer.value;
	}

	seeCorrection(): void {
		this.currentSectionTest = this.currentSection
			.Contents[0] as CourseSectionTest;
		// this.nextAndPreviousTest = null;
		// this.currentNextSectionTest = this.currentSection
		// 	.Contents[1] as CourseSectionTest;
		this.setSectionTest(this.currentSection);
		this.showScore = false;
	}

	onShowScore(): void {
		this.showScore = true;
	}

	getScore(): string {
		console.log(
			'Score: ',
			this.userScore,
			'Total question: ',
			this.currentSection.Contents.length
		);
		if (this.totalGoodAnswers === 0) {
			return '0 %';
		}
		return `${Math.round(
			// this.userScore * 100) / this.totalGoodAnswers
			(this.userScore * 100) / this.currentSection.Contents.length
		).toFixed(1)} %`;
	}

	onRadioBlur(): void {
		console.log('Blur !!');
	}

	setRadioValue(value: string) {
		console.log('aaaaa', value);
	}
}
