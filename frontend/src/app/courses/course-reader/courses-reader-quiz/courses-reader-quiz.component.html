<div class="">
	<div class="container bg-white w-50 text-dark p-5 my-2">
		<!--Header of quiz formular-->
		<div class="d-flex justify-content-start align-items-center">
			<i class="fas fa-tools" style="font-size: 1.5em"></i>
			<span class="mx-2 h4">Titre de la partie concernée par le TEST</span>
		</div>
		<div *ngIf="!showScore" class="h4 mt-5">Question {{ questionNumber }}</div>
		<hr />
		<div *ngIf="!showScore" class="question-label h3">
			{{ currentSectionTest.Title }}
		</div>
		<div *ngIf="showScore">
			{{ 'course.create.showScoreTitle' | translate }}
		</div>
		<!--End header of quiz formular-->
		<!--Question ouverte-->
		<div class="mw-100">
			<div
				class="col-12 form-group my-1"
				*ngIf="
					+currentSectionTest.TypeOfTest === questionType.FREE && !showScore
				"
			>
				<div class="card border-0 my-0 shadow-0">
					<div class="card-body border-0">
						<div class="row">
							<div class="col-12">
								<label class="h6" [for]="'xxxxxx'">
									{{ 'course.details.yourReply' | translate }}
								</label>
								<input
									[id]="'xxxxxx'"
									class="form-control"
									required
									[(ngModel)]="reply"
									[name]="'xxxxxx'"
									type="text"
								/>
							</div>
						</div>
					</div>
				</div>
			</div>
			<!--Fin des question ouverte-->

			<div
				class="col-12 form-group my-1"
				*ngFor="let question of currentSectionTest.Questions; let i = index"
			>
				<div class="card border-0 my-0 shadow-0">
					<div class="card-body border-0" style="padding: 0.75rem !important">
						<div
							class="row"
							*ngIf="
								(+currentSectionTest.TypeOfTest === questionType.RADIO ||
									+currentSectionTest.TypeOfTest === questionType.SELECT) &&
								!showScore
							"
						>
							<div class="col-12 d-flex flex-row align-items-center">
								<div class="form-check h5">
									<!-- <input
										class="form-check-input"
										type="radio"
										[name]="'question-radio'"
										[value]="question.Slug.substr(0, 5)"
										[id]="question.Slug + 'radio'"
										[(ngModel)]="oneChooseValue"
									/> -->
									<p-radioButton
										#quizRadio
										[disabled]="testOver"
										name="radioButtonStatus"
										value="{{ question.Slug }}"
										[(ngModel)]="radioButtonStatus[currentSectionTest.Id]"
										(ngModelChange)="setRadioValue($event)"
										inputId="{{ question.Slug + 'radio' }}"
										(onFocus)="
											onAnwser($event, question.Slug + 'radio', question.Slug)
										"
										(onBlur)="onRadioBlur($event, question.Slug)"
									></p-radioButton>
									<label
										[class]="
											testOver && fails.includes(question.Slug)
												? 'h6 text-danger'
												: (testOver && wons.includes(question.Slug)) ||
												  (testOver && question.IsRightAnswer)
												? 'h6 text-success'
												: 'h6'
										"
										style="cursor: pointer"
										[innerHTML]="question.Title"
										for="{{ question.Slug + 'radio' }}"
									></label>

									<!-- <label
										class="form-check-label mr-2 bg-dark text-light"
										style="width: 0"
										[for]="question.Slug + 'radio'"
									>
										c
									</label> -->
								</div>
								<!-- <div
									[class]="
										testOver && fails.includes(question.Slug)
											? 'h6 text-danger'
											: (testOver && wons.includes(question.Slug)) ||
											  (testOver && question.IsRightAnswer)
											? 'h6 text-success'
											: 'h6'
									"
									[innerHTML]="question.Title"
								></div> -->
							</div>
						</div>
						<!--- Multi choose -->
						<div
							class="row"
							*ngIf="
								+currentSectionTest.TypeOfTest === questionType.CHEKED &&
								!showScore
							"
						>
							<div class="col-12 d-flex flex-row align-items-center">
								<div class="form-check form-check-inline">
									<input
										class="form-check-input mr-2 errors"
										type="checkbox"
										[name]="question.Slug + 'checkbox'"
										[id]="question.Slug + 'checkbox'"
										[disabled]="testOver"
										[checked]="getUserAnswer(question)"
										(change)="
											onAnwser(
												$event,
												question.Slug + 'checkbox',
												question.Slug
											)
										"
									/>
									<label
										style="cursor: pointer"
										for="{{ question.Slug + 'checkbox' }}"
										[class]="
											testOver && fails.includes(question.Slug)
												? 'h6 text-danger'
												: (testOver && wons.includes(question.Slug)) ||
												  (testOver && question.IsRightAnswer)
												? 'h6 text-success'
												: 'h6'
										"
										[innerHTML]="question.Title"
									></label>
								</div>

								<!-- <div
								for="{{ question.Slug + 'checkbox' }}"
									[class]="
										testOver && fails.includes(question.Slug)
											? 'h6 text-danger'
											: (testOver && wons.includes(question.Slug)) ||
											  (testOver && question.IsRightAnswer)
											? 'h6 text-success'
											: 'h6'
									"
									[innerHTML]="question.Title"
								></div> -->
							</div>
						</div>
						<!---End Multi choose-->
					</div>
				</div>
			</div>

			<div class="col-12 form-group my-1" *ngIf="showScore">
				<div>
					{{ 'course.create.yourScoreIs' | translate }}:
					<h1 style="display: inline-block; font-weight: bold">
						{{ getScore() }}
					</h1>
					<p>
						{{ 'course.create.goodAnswers' | translate }}:
						<strong
							>{{ userGoodAnswers }} {{ 'course.create.in' | translate }}
							{{ totalGoodAnswers }}</strong
						>
					</p>
					<!-- <p>
						{{ 'course.create.badAnswers' | translate }}: <strong>132</strong>
					</p> -->
					<p>
						{{ 'course.create.totalNumberOfQuestion' | translate }}:
						<strong>{{ totalQuestions }}</strong>
					</p>
				</div>
			</div>
		</div>
		<hr />
		<div class="my-2 d-flex flex-column justify-content-center align-items-end">
			<div class="d-flex justify-content-end">
				<button
					class="btn btn-light mr-2 btn-sm"
					(click)="nextAndPreviousTest($event, currentPrevSectionTest)"
					*ngIf="currentPrevSectionTest && !showScore"
				>
					<i class="fas fa-chevron-left mr-2"></i>
					{{ 'course.create.btnPrevious' | translate }}.
				</button>
				<button
					class="btn btn-light btn-sm"
					*ngIf="currentNextSectionTest && !showScore"
					(click)="nextAndPreviousTest($event, currentNextSectionTest)"
				>
					{{ 'course.create.btnContinue' | translate }}.

					<i class="fas fa-chevron-right ml-2"></i>
				</button>
			</div>
			<div class="d-flex justify-content-end">
				<button
					class="btn btn-dark mr-2 btn-sm"
					*ngIf="!testOver && endOfTestSection"
					(click)="onFinish($event)"
				>
					{{ 'course.create.btnEnd' | translate }}

					<!-- <i class="fas fa-chevron-right ml-2"></i> -->
				</button>
				<button
					class="btn btn-danger mr-2 btn-sm"
					*ngIf="
						(testOver &&
							endOfTestSection &&
							questionNumber === currentSection.Contents.length) ||
						showScore
					"
					(click)="seeCorrection()"
				>
					{{ 'course.create.seeCorrection' | translate }}

					<!-- <i class="fas fa-chevron-right ml-2"></i> -->
				</button>
				<button
					class="btn btn-success btn-sm"
					*ngIf="testOver && !showScore"
					(click)="onShowScore()"
				>
					{{ 'course.create.score' | translate }}.
				</button>
			</div>
		</div>
	</div>
</div>
