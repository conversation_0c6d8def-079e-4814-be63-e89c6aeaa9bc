.course_header {
	background: #0b0b0bfa;
	// background: linear-gradient(180deg, rgba(113, 113, 136, 1) 0%, rgba(105, 87, 92, 0.6253851882549895) 35%);
	padding: 1em 0em;
	// padding-left: 3em;
}

.h-default {
	height: 50vh !important;
}

.content_menu_btn {
	height: 100%;
	// border-left: 1px solid #d87a51;
	background: #0b0b0bfa;
	padding-right: 3em;
	padding-left: 1em;
	color: #666;
	// border: 1px solid #666;
}

.p-sidebar h1 {
	padding-left: 0.5em;
	padding-right: 0.5em;
	color: black;
	font-weight: 400;
}

.p-sidebar .p-panel-content {
	background: #fbfbfb !important;
	border: 0px;
	color: black;
}

.p-sidebar .p-panel-header {
	/* background: #636465 !important; */
	border: 0px;
	color: black;
	border-bottom: 1px solid white;
}

.p-sidebar .p-sidebar-close {
	background: #636465 !important;
}

.p-sidebar .pi {
	color: white;
}

/* .p-sidebar {
	padding-left: 0 !important;
	padding-right: 0 !important;
	background: #fff;
} */

.sidebar_menu_content button {
	float: right;
	text-align: left;
	position: absolute;
	right: 0;
}

.sidebar_menu_content {
	display: flex;
	align-items: center;
	position: relative;
}

.sidebar_menu_content_main {
	display: flex;
	flex-direction: column;
}

.video_contain {
	//height: auto;
	max-width: 100% !important;
	background: #0b0b0bfa;
	overflow: hidden;
	.contain-video {
		max-width: 100% !important;
	}
}

.video_contain video {
	height: 65vh;
}

.p-tabview-nav {
	display: flex;
	align-items: center;
	justify-content: center;
}

.p-tabview {
	margin-bottom: 4em;
}

// .ng-star-inserted {
//     padding-left: 1em;
// }
.teacher_details img {
	height: 50px;
	width: 50px;
	border-radius: 100%;
}

.details_cours_chiffre {
	font-weight: 500;
}

.image_holder_course_proposal img {
	height: 100%;
	width: 200px;
	border-radius: 5px;
	object-fit: cover;
}

.image_holder_course_proposal {
	position: relative;
}

.duree_proposal {
	background-color: #3f3c3b79;
	position: absolute;
	bottom: 0;
	right: 0;
	border-radius: 5px;
	height: 100%;
}

@media only screen and (max-width: 992px) {
	.course_header {
		/* padding-left: 1em; */
	}
	.content_menu_btn {
		padding-right: 1em;
		left: -7px;
		top: 5px;
		position: relative;
	}
	.hide_on_med_and_down {
		display: none;
	}
	.title-reader {
		/* padding-top: 1em; */
	}
	section {
		padding: 0px !important;
	}
}

img {
	max-width: 100% !important;
}

.p-panel .p-panel-header {
	// background-color: white !important;
	// border: 0 !important;
	// border-bottom: solid 1px #dee2e6;;
}

.p-panel .p-panel-header .p-panel-title {
	font-weight: 600;
	line-height: 1.4;
	font-size: 1.1rem;
	font-family: sans-serif;
}

.p-panel-content {
	padding-top: 0.5em !important;
	padding-bottom: 0.5em !important;
}

.p-divider {
	margin-top: 0.5em !important;
	margin-bottom: 0.5em !important;
}

@media screen and (max-width: 992px) {
	.ng-trigger.ng-trigger-animation.ng-tns-c485-7.p-dialog.p-component.p-dialog-draggable.p-dialog-resizable.ng-star-inserted {
		width: 100vw !important;
	}
}
.ng-tns-c486-7.p-dialog-content {
	height: 250px !important;
}

.dialog-report-course .p-dialog-content {
	overflow-y: unset !important;
}

.p-dropdown.p-component {
	width: 100%;
}

.p-inputtextarea.p-inputtext {
	border-radius: 4px !important;
}

.report-desc-label {
	margin: 10px 0 !important;
}

.user-report {
	color: red;
	background-color: #f6f6f6;
	border-radius: 4px;
	width: 100%;
	font-weight: bold;
	padding: 1rem 2rem;

	&.desc {
		color: #636465;
		padding: 0.5rem 1rem;
		font-size: 14px;
		font-weight: initial;
	}
}

.report-loader {
	height: 200px;
	padding-top: 100px;
}

@media screen and (max-width: 600px) {
	// .ng-trigger.ng-trigger-animation.ng-tns-c486-7
	.p-dialog.p-component.p-dialog-draggable.p-dialog-resizable.ng-star-inserted {
		width: 95vw !important;
		overflow-y: hidden;
	}

	.p-dropdown-item.p-ripple {
		font-size: 0.9rem;
	}
}
