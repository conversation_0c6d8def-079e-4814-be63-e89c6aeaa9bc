<div class="">
	<div
		class="container bg-white text-dark p-5 my-2"
		style="padding-bottom: 0px !important"
	>
		<div class="row">
			<div class="col-sm-12 col-lg-9">
				<p-scrollPanel [style]="{ width: '100%', height: '65vh' }">
					<div class="my-4 text-center h1">
						{{ currentContent.Title }}
					</div>
					<div [innerHTML]="currentContent.Content" class="mw-100"></div>
				</p-scrollPanel>
			</div>
			<div class="col-lg-3">
				<div class="card hide-on-med-and-down shadow">
					<div class="card-header d-flex p-0">
						<div
							class="d-flex align-items-center justify-content-center dark-menu-right pointer bg-dark p-2"
							(click)="onChangeSection($event, false)"
						>
							<i class="pi pi-chevron-left text-white"></i>
						</div>
						<div
							class="d-flex align-items-center justify-content-center dark-menu-right p-1 text-center w-100"
						>
							<h6
								class="mb-0 text-white"
								style="line-height: 20px; font-size: 14px"
							>
								{{ currentSection.Title }}
							</h6>
						</div>
						<div
							class="d-flex align-items-center justify-content-center dark-menu-right pointer bg-dark p-2"
							(click)="onChangeSection($event, true)"
						>
							<i class="pi pi-chevron-right text-white"></i>
						</div>
					</div>
					<div class="card-body" style="max-height: 40vh">
						<p-scrollPanel [style]="{ width: '100%', height: '40vh' }">
							<a
								*ngFor="let con of currentSection.Contents; let ind = index"
								href=""
								class="d-block mb-2 text-dark"
								(click)="onChangeContent($event, con)"
								[ngClass]="{ 'text-danger': currentContent.Id === con.Id }"
							>
								<span *ngIf="courseViews && getIsView(con)"
									><i class="fas fa-circle text-success"></i
								></span>

								<span *ngIf="courseViews && !getIsView(con)"
									><i class="far fa-circle"></i
								></span>

								{{ ind + 1 }}.
								<span>{{ con.Title }}</span>
							</a>
						</p-scrollPanel>
					</div>
				</div>
				<div
					class="my-4 text-center d-flex justify-content-center flex-column align-items-center"
				>
					<div class="w-100">
						<button
							class="btn btn-success w-100"
							(click)="onFinish($event)"
							[disabled]="isLoadingFinish"
						>
							<span *ngIf="isLoadingFinish" class="mr-2">
								<i class="fas fa-spin fa-spinner"></i
							></span>
							I finish this chapter.
						</button>
					</div>

					<div class="my-4 ml-0">
						<button
							class="btn btn-light mr-2 mb-2"
							(click)="nextAndPrevious($event, currentPrevContent)"
							*ngIf="currentPrevContent"
						>
							<i class="fas fa-chevron-left mr-2"></i>
							{{ currentPrevContent.Title }}
						</button>
						<button
							class="btn btn-light mb-0"
							*ngIf="currentNextContent"
							(click)="nextAndPrevious($event, currentNextContent)"
						>
							{{ currentNextContent.Title }}
							<i class="fas fa-chevron-right ml-2"></i>
						</button>
					</div>
				</div>
			</div>
			<div
				class="hide-on-large-only bg-primary d-flex align-items-center justify-content-center"
				style="
					height: 50px;
					width: 50px;
					position: absolute;
					right: 0;
					top: 50%;
				"
				(click)="showContentMenu = true"
			>
				<i class="pi pi-chevron-left text-white"></i>
			</div>
		</div>
	</div>
</div>
<p-sidebar [(visible)]="showContentMenu" [baseZIndex]="10000" position="right">
	<!-- <h1 style="font-weight:normal">Left Sidebar</h1> -->
	<div class="card" style="height: 100vh">
		<div
			class="card-header d-flex p-0 justify-content-between"
			style="height: 10vh"
		>
			<div
				class="d-flex align-items-center justify-content-center dark-menu-right pointer bg-dark"
				(click)="onChangeSection($event, false)"
			>
				<i class="pi pi-chevron-left text-white"></i>
			</div>
			<div
				class="d-flex align-items-center justify-content-center dark-menu-right p-1 text-center w-100"
			>
				<h3 class="mb-0 text-white" style="line-height: 20px">
					{{ currentSection.Title }}
				</h3>
			</div>
			<div
				class="d-flex align-items-center justify-content-center dark-menu-right pointer bg-dark"
				(click)="onChangeSection($event, true)"
			>
				<i class="pi pi-chevron-right text-white"></i>
			</div>
		</div>
		<div class="card-body">
			<a
				*ngFor="let con of currentSection.Contents; let ind = index"
				href=""
				class="d-block mb-2 text-dark"
				(click)="onChangeContent($event, con)"
				[ngClass]="{ 'text-danger': currentContent.Id === con.Id }"
			>
				<span *ngIf="courseViews && getIsView(con)"
					><i class="fas fa-circle text-success"></i
				></span>

				<span *ngIf="courseViews && !getIsView(con)"
					><i class="far fa-circle"></i
				></span>

				{{ ind + 1 }}.
				<span>{{ con.Title }}</span>
			</a>
			<div class="w-100">
				<button
					class="btn btn-success w-100"
					(click)="onFinish($event)"
					[disabled]="isLoadingFinish"
				>
					<span *ngIf="isLoadingFinish" class="mr-2">
						<i class="fas fa-spin fa-spinner"></i
					></span>
					I finish this chapter.
				</button>
			</div>
		</div>
	</div>
</p-sidebar>
