import {
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	ViewEncapsulation
} from '@angular/core';
import { CourseContent } from '~/app/models/course-content.model';
import { ContentType, Course } from '~/app/models/course.model';
import { CourseSection } from '~/app/models/course-section.model';
import { CourseSectionTest } from '~/app/models/course-section-test.model';
import { CourseBaseContent } from '~/app/models/course-base-content.model';
import { CourseView } from '~/app/models/course-view.model';
import { User } from '~/app/models/user.model';
import { ToastrService } from 'ngx-toastr';
import { TranslateService } from '@ngx-translate/core';
import { CourseViewService } from '~/app/services/courses/course-view.service';

@Component({
	selector: 'app-courses-reader-ebook',
	templateUrl: './courses-reader-ebook.component.html',
	styleUrls: ['./courses-reader-ebook.component.scss'],
	encapsulation: ViewEncapsulation.None
})
export class CoursesReaderEbookComponent implements OnInit {
	currentPrevContent: CourseContent;
	currentNextContent: CourseContent;

	@Input()
	courseViews: CourseView[];
	@Output()
	courseViewsChange: EventEmitter<CourseView[]> = new EventEmitter<
		CourseView[]
	>();

	@Input()
	user: User;

	@Input()
	currentContent: CourseContent;
	@Output()
	currentContentChange: EventEmitter<CourseContent> = new EventEmitter<CourseContent>();

	@Input()
	currentSection: CourseSection;
	@Output()
	currentSectionChange: EventEmitter<CourseSection> = new EventEmitter<CourseSection>();

	@Output()
	emitResetCourseSectionTest = new EventEmitter<CourseSectionTest>();

	@Input()
	course: Course;
	showContentMenu = false;

	isLoadingFinish = false;

	constructor(
		private courseViewService: CourseViewService,
		private toastr: ToastrService,
		private translateService: TranslateService
	) {}

	async ngOnInit(): Promise<void> {
		await this.setSection(this.currentSection, true);
	}

	setcurrentSection(val: CourseSection): void {
		this.currentSectionChange.emit(val);
		this.currentSection = val;
	}

	setcurrentContent(val: CourseContent): void {
		this.currentContent = val;
		this.currentContentChange.emit(val);
	}

	async nextAndPrevious(e, contentSection: CourseContent): Promise<void> {
		e.preventDefault();
		this.setcurrentContent(contentSection);
		this.setSection(this.currentSection);
		this.emitResetCourseSectionTest.emit();
	}

	getIsView(content: CourseBaseContent): boolean {
		return !this.courseViews
			? false
			: this.courseViews.find((f) => f.Content.Id === content.Id)
			? true
			: false;
	}

	onNext(): void {
		const index = this.currentSection.Contents.map((f) => f.Id).indexOf(
			this.currentContent.Id
		);
		if (index < this.currentSection.Contents.length) {
			const content = this.currentSection.Contents[index + 1] as CourseContent;
			if (content) {
				this.setcurrentContent(content);
				this.setSection(this.currentSection);
			} else {
				this.onChangeSection(null, true);
			}
		} else {
			this.onChangeSection(null, true);
		}
	}

	async setSection(
		section: CourseSection,
		onStart: boolean = false
	): Promise<void> {
		this.emitResetCourseSectionTest.emit();
		let contentLastUpdate = null;
		if (onStart) {
			/*
      this.courseViews = await this.courseViewService
        .GetAllViewCourseByUserAndCourse(this.user.Slug, this.course.Slug)
        .toPromise();
       */

			const sort = this.courseViews?.sort((a, b) =>
				a.UpdatedAt > b.UpdatedAt ? -1 : 1
			);
			contentLastUpdate = sort && sort.length > 0 ? sort[0] : null;
			if (
				contentLastUpdate &&
				contentLastUpdate.Content.Type !== ContentType.QUIZ &&
				contentLastUpdate.Content.Type !== ContentType.ASSIGNMENT
			) {
				section = this.course.Sections.find(
					(f) => f.Id === contentLastUpdate.Content.Section.Id
				);
				const content = section.Contents.find(
					(f) => f.Id === contentLastUpdate.Content.Id
				);
				this.setcurrentContent(content as CourseContent);
			}
		}

		this.currentSection = section;
		const index = section.Contents.map((f) => f.Id).indexOf(
			this.currentContent.Id
		);
		if (index > 0) {
			this.currentPrevContent = section.Contents[index - 1] as CourseContent;
		} else {
			this.currentPrevContent = null;
		}

		if (index < section.Contents.length) {
			this.currentNextContent = section.Contents[index + 1] as CourseContent;
		} else {
			this.currentNextContent = null;
		}

		this.setcurrentSection(section);

		if (contentLastUpdate) {
			this.onNext();
		}
	}

	onChangeContent(e, content: CourseBaseContent): void {
		e.preventDefault();
		this.setcurrentContent(content as CourseContent);
		this.setSection(this.currentSection);
	}

	onChangeSection(e, next: boolean): void {
		if (e) {
			e.preventDefault();
		}
		let index = this.course.Sections.findIndex(
			(f) => f.Id === this.currentSection.Id
		);
		let first = true;
		let content: CourseContent;
		if (next && index + 1 < this.course.Sections.length) {
			let s: CourseSection;
			do {
				if (!first) {
					index = this.course.Sections.findIndex((f) => f.Id === s.Id);
				} else {
					first = false;
				}

				if (next && index + 1 < this.course.Sections.length) {
					s = this.course.Sections[index + 1];
					content =
						s.Contents && s.Contents.length > 0
							? (s.Contents[0] as CourseContent)
							: null;
				}
			} while (
				content &&
				(content.Type === ContentType.QUIZ ||
					content.Type === ContentType.ASSIGNMENT) &&
				index + 1 < this.course.Sections.length
			);

			if (
				content &&
				content.Type !== ContentType.QUIZ &&
				content.Type !== ContentType.ASSIGNMENT
			) {
				this.setcurrentContent(content);
				this.setSection(s);
			}
		} else if (!next && index > 0) {
			let s: CourseSection;
			do {
				if (!first) {
					index = this.course.Sections.findIndex((f) => f.Id === s.Id);
				} else {
					first = false;
				}

				if (!next && index > 0) {
					s = this.course.Sections[index - 1];
					content =
						s.Contents && s.Contents.length > 0
							? (s.Contents[0] as CourseContent)
							: null;
				}
			} while (
				content &&
				(content.Type === ContentType.QUIZ ||
					content.Type === ContentType.ASSIGNMENT) &&
				index > 0
			);
			if (
				content &&
				content.Type !== ContentType.QUIZ &&
				content.Type !== ContentType.ASSIGNMENT
			) {
				this.setcurrentContent(content);
				this.setSection(s);
			}
		}
	}

	async onFinish(e): Promise<void> {
		e.preventDefault();

		this.isLoadingFinish = true;
		try {
			const message = await this.translateService
				.get('home.register.success')
				.toPromise();
			let cv: CourseView = this.courseViews?.find(
				(f) => f.Content.Id === this.currentContent.Id
			);

			if (!cv) {
				cv = new CourseView(
					null,
					'123',
					null,
					null,
					null,
					new Date(),
					{ Id: this.user.Id } as any,
					{ Id: this.currentContent.Id } as any,
					this.currentContent.TotalTime
				);

				cv.ProgressRating = this.currentContent.TotalTime;
				cv = await this.courseViewService.add(cv).toPromise();

				if (cv) {
					this.courseViews.push(cv);
				}
			}

			if (cv) {
				//  this.toastr.success(message, 'Brain-maker');
				this.onNext();
			}
		} catch (e) {
			console.log(e);
			const message = await this.translateService
				.get('home.register.error')
				.toPromise();
			this.toastr.error(message, 'Brain-maker');
		} finally {
			this.isLoadingFinish = false;
		}
	}
}
