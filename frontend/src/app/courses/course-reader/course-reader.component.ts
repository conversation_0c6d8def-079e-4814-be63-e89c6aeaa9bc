import { Component, Input, OnInit, ViewEncapsulation } from '@angular/core';
import { MenuItem, PrimeNGConfig } from 'primeng/api';
import {
	ContentType,
	Course,
	MAX_USERS_NUMBERS_WHO_REPORT_TO_UNPUBLISH_COURSE,
	notificationMessages,
	ReportObject
} from '~/app/models/course.model';
import { ActivatedRoute, Router } from '@angular/router';
import { UtilsService } from '~/app/services/utils.service';
import { Media } from '~/app/models/media.model';
import { Observable } from 'rxjs';
import { UserService } from '~/app/services/users/user.service';
import { User, UserRoleType } from '~/app/models/user.model';
import { CourseSection } from '~/app/models/course-section.model';
import { CourseSectionTest } from '~/app/models/course-section-test.model';
import { CourseContent } from '~/app/models/course-content.model';
import { CourseSaved } from '~/app/models/course-saved.model';
import { ToastrService } from 'ngx-toastr';
import { TranslateService } from '@ngx-translate/core';
import { CourseService } from '~/app/services/courses/course.service';
import { OrderService } from '~/app/services/orders/order.service';
import { CourseSavedService } from '~/app/services/courses/course-saved.service';
import { CourseReport } from '~/app/models/course-report.model';
import { CourseReportService } from '~/app/services/course-report.service';
import { CourseNotificationsService } from '~/app/services/notifications/course-notifications.service';
import {
	CourseNotification,
	courseNotificationType
} from '~/app/models/course-notification.model';
import { CourseView } from '~/app/models/course-view.model';
import { CourseViewService } from '~/app/services/courses/course-view.service';
import {
	Abonnement,
	AbonnementStatus
} from '~/app/models/abonnements/abonnement.model';
import { AbonnementsService } from '~/app/services/abonnements/abonnements.service';

interface CourseReportObject {
	label: string;
	object: number;
}

@Component({
	selector: 'app-course-reader',
	templateUrl: './course-reader.component.html',
	styleUrls: ['./course-reader.component.scss'],
	encapsulation: ViewEncapsulation.None
})
export class CourseReaderComponent implements OnInit {
	items: MenuItem[];

	courseViews: CourseView[];
	cover = '/assets/img/1920x650.png';
	course: Course;
	currentContent: CourseContent;
	currentSectionTest: CourseSectionTest;
	currentSection: CourseSection;
	contentType = ContentType;
	activeItem: MenuItem;
	currentTranscription: string;
	user: User;
	languages: { code: string; name: string; nativeName: string }[];
	showMenu: boolean;

	courseSaved: CourseSaved;

	displayConfirmReportModal = false;

	reportObjects: CourseReportObject[] = [];
	selectedCourseReportObject: CourseReportObject;
	userHaveCustomReportObject = false;
	userCustomReportContent = '';
	courseReportLoading = false;
	userAlreadyReportTheContent = false;
	userReportForCurrentContent: CourseReport;
	loadingCheckIfUserAlreadyReport = false;

	@Input()
	slug: string;

	finishloading = false;

	userAbonnement: Abonnement;

	constructor(
		private primengConfig: PrimeNGConfig,
		private courseService: CourseService,
		private utilsService: UtilsService,
		private orderService: OrderService,
		private userService: UserService,
		private router: Router,
		private toastr: ToastrService,
		private translateService: TranslateService,
		private activedRoute: ActivatedRoute,
		private courseSavedService: CourseSavedService,
		private courseViewService: CourseViewService,
		private courseReportService: CourseReportService,
		private courseNotificationService: CourseNotificationsService,
		private abonnementService: AbonnementsService
	) {}

	async ngOnInit(): Promise<void> {
		this.finishloading = false;
		if (this.userService.isConnected()) {
			this.user = await this.userService.getUserConnected().toPromise();
		} else {
			this.router.navigate(['/']);
		}

		let slug = null;
		if (this.activedRoute.snapshot.params.id) {
			slug = this.activedRoute.snapshot.params.id;
		} else if (this.slug) {
			slug = this.slug;
		} else {
			this.router.navigate(['/']);
		}

		this.languages = await this.utilsService.getLanguages().toPromise();
		this.course = await this.courseService.getBySlug(slug).toPromise();
		this.course = this.courseService.BuildCourse(this.course, this.languages);

		if (!this.course) {
			this.router.navigate(['/']);
		}

		this.userAbonnement = await this.abonnementService
			.getUserActiveAbonnement(this.user.Slug)
			.toPromise();

		if (
			!this.course.Published &&
			!(
				this.user?.Role === UserRoleType.ADMIN ||
				this.user?.Slug === this.course.CreatedBy.Slug ||
				this.userAbonnement?.Status === AbonnementStatus.ACTIVE
			)
		) {
			this.router.navigate(['/courses']);
		}

		const order = await this.orderService
			.getByCoursesSlugAndUserSug(this.course.Slug, this.user.Slug)
			.toPromise();
		let isPay =
			(this.user && this.user.Role === UserRoleType.ADMIN) ||
			!!(order && order.Id) ||
			+this.course.CreatedBy.Id === +this.user.Id;

		if (!isPay && !this.course.Free) {
			const orders = await this.orderService
				.getByUserHaveAbonnement(this.user.Slug)
				.toPromise();
			isPay = !!orders.find((f) => +f.User.Id === +this.user.Id);
		}

		if (
			!isPay &&
			!this.course.Free &&
			this.userAbonnement.Status !== AbonnementStatus.ACTIVE
		) {
			this.router.navigate(['/courses', this.activedRoute.snapshot.params.id]);
		}

		if (this.course.CoverImage) {
			this.cover = `${this.course.CoverImage.Hashname}`;
		}
		// console.log('course', this.course);

		this.primengConfig.ripple = true;
		this.items = [
			{ label: 'Description', icon: 'pi pi-fw pi-book' },
			{ label: 'Notes', icon: 'pi pi-fw pi-bookmark' },
			{ label: 'Transcription', icon: 'pi pi-fw pi-align-left' }
		];

		this.currentSection = this.course.Sections[0];

		if (this.course.Sections[0].Contents[0]) {
			if (
				this.course.Sections[0].Contents[0].Type === ContentType.PDF ||
				this.course.Sections[0].Contents[0].Type === ContentType.EBOOK ||
				this.course.Sections[0].Contents[0].Type === ContentType.VIDEO
			) {
				this.currentContent = this.course.Sections[0]
					.Contents[0] as CourseContent;

				this.currentSectionTest = null;
			} else if (
				this.course.Sections[0].Contents[0].Type === ContentType.QUIZ
			) {
				this.currentSectionTest = this.course.Sections[0]
					.Contents[0] as CourseSectionTest;
				this.currentContent = null;
			}
		}

		this.currentSection = this.course.Sections[0];

		// console.log('content', this.currentContent);

		this.activeItem = this.items[0];

		this.courseSaved = await this.courseSavedService
			.getAllByUserAndCourse(this.user.Slug, this.course.Slug)
			.toPromise();
		// console.log('Saved', this.courseSaved);

		if (!this.courseSaved) {
			this.courseSaved = new CourseSaved(
				null,
				'123',
				null,
				null,
				{ Id: this.course.Id } as any,
				{ Id: this.user.Id } as any
			);
		}

		this.courseViews = await this.courseViewService
			.GetAllViewCourseByUserAndCourse(this.user.Slug, this.course.Slug)
			.toPromise();

		this.initReport();
		this.finishloading = true;

		console.log('fist init: ', this.user, this.courseViews);
	}

	async initReport(): Promise<void> {
		this.reportObjects = [
			{
				label: 'Ce contenue est de mauvaise qualité',
				object: ReportObject.BAD_QUALITY
			},
			{
				label: 'Ce contenue est inappriprié',
				object: ReportObject.INAPPROPRIATE
			},
			{
				label: 'Ce contenue est un contenue mensongé',
				object: ReportObject.LYING
			}
		];
		this.checkIfUserAlreadyReportTheCurrentContent();
	}

	async checkIfUserAlreadyReportTheCurrentContent(): Promise<void> {
		if (!this.currentContent) return;

		this.loadingCheckIfUserAlreadyReport = true;
		this.userReportForCurrentContent = null;
		this.userAlreadyReportTheContent = false;
		try {
			this.userReportForCurrentContent = await this.courseReportService
				.getReportOfUserForContentCourseIfExist(
					this.currentContent.Slug,
					this.user.Slug
				)
				.toPromise();
			this.userAlreadyReportTheContent =
				this.userReportForCurrentContent !== null;
			// console.log('current content', this.currentContent);
			// console.log('user report', this.userReportForCurrentContent);
			// console.log(this.userAlreadyReportTheContent);
		} catch (error) {
			console.log(error);
		} finally {
			this.loadingCheckIfUserAlreadyReport = false;
		}
	}

	getReportObject(object: number): string {
		if (object === ReportObject.BAD_QUALITY) {
			return 'Mauvaise qualité';
		} else if (object === ReportObject.INAPPROPRIATE) {
			return 'Contenue inaproprié';
		} else if (object === ReportObject.LYING) {
			return 'Contenue mensongé';
		}
	}

	reportObjectsChange(event): void {
		// console.log(event);
	}

	async submitContentReport(): Promise<void> {
		// console.log(
		//   this.userAlreadyReportTheContent,
		//   this.userCustomReportContent,
		//   this.selectedCourseReportObject
		// );
		if (!this.userCustomReportContent && !this.selectedCourseReportObject) {
			return;
		}

		if (
			!this.selectedCourseReportObject &&
			!this.userHaveCustomReportObject &&
			this.userCustomReportContent
		) {
			return;
		}

		if (this.userAlreadyReportTheContent) {
			// console.log('current content', this.currentContent);
			// console.log('user report', this.userAlreadyReportTheContent);
			return;
		}

		this.courseReportLoading = true;

		try {
			const newCourseReport = new CourseReport(
				null,
				null,
				null,
				null,
				this.userHaveCustomReportObject ? this.userCustomReportContent : null,
				this.selectedCourseReportObject
					? this.selectedCourseReportObject.object
					: ReportObject.OTHER,
				this.user,
				this.currentContent
			);

			// console.log(newCourseReport);
			const response = await this.courseReportService
				.add(newCourseReport)
				.toPromise();

			// console.log(response);
			if (response) {
				this.userCustomReportContent = '';
				this.userHaveCustomReportObject = false;
				this.selectedCourseReportObject = null;
				this.displayConfirmReportModal = false;
				this.courseReportLoading = false;

				this.toastr.success(
					'Votre signalement a bien été pris en compte',
					'Signalement du contenue'
				);
			}

			const countUsers = await this.courseService
				.countUsersWhoHaveReportTheCourse(this.course.Slug)
				.toPromise();

			if (
				countUsers &&
				countUsers >= MAX_USERS_NUMBERS_WHO_REPORT_TO_UNPUBLISH_COURSE
			) {
				this.course.Published = false;
				this.courseService
					.edit({ ...this.course, CoverImage: null })
					.toPromise();
			}

			try {
				const res = await this.courseNotificationService
					.add(
						new CourseNotification(
							null,
							null,
							null,
							null,
							notificationMessages.NEW_REPORTED_COURSE,
							this.course,
							false,
							[],
							courseNotificationType.COURSE_REPORT
						)
					)
					.toPromise();
				// console.log('**** notification', res);
			} catch (error) {
				console.log(error);
			}
		} catch (error) {
			console.log(error);
		}
	}

	getTranslationFile(media: Media): Observable<string> {
		return this.utilsService.getElementFromUrl(media.Hashname);
	}
	showDetailTab(tab): void {
		// console.log(tab);
	}

	downloadAssigment(e, courseSection: CourseSection): void {
		e.preventDefault();
	}

	resetContent(item): void {
		this.currentContent = null;
	}

	resetTest(item): void {
		this.currentSectionTest = null;
	}

	async onSaveOrRemoveSave(e): Promise<void> {
		e.preventDefault();
		try {
			let saved: CourseSaved;
			if (this.courseSaved.Id) {
				saved = await this.courseSavedService
					.delete(this.courseSaved)
					.toPromise();
				const message = await this.translateService
					.get('home.register.success')
					.toPromise();
				this.toastr.success(message, 'Brain-maker');
				this.courseSaved = new CourseSaved(
					null,
					'123',
					null,
					null,
					{ Id: this.course.Id } as any,
					{ Id: this.user.Id } as any
				);
			} else {
				saved = await this.courseSavedService.add(this.courseSaved).toPromise();
				if (saved) {
					this.courseSaved = saved;
					const message = await this.translateService
						.get('home.register.success')
						.toPromise();
					this.toastr.success(message, 'Brain-maker');
				}
			}
		} catch (e) {
			// console.log(e);
			const message = await this.translateService
				.get('home.register.error')
				.toPromise();
			this.toastr.error(message, 'Brain-maker');
		}
	}

	onReport(e): void {
		this.checkIfUserAlreadyReportTheCurrentContent();
		e.preventDefault();
		this.displayConfirmReportModal = true;
	}

	onDownload(e): void {
		e.preventDefault();
	}
}
