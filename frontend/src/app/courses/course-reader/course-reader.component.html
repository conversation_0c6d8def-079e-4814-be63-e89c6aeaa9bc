<div class="red-skin course-reader" *ngIf="course">
	<div id="main-wrapper">
		<app-home-header *ngIf="!slug"></app-home-header>
		<section class="p-0 pb-3">
			<div
				class="d-flex justify-content-between course_header align-items-center"
			>
				<div class="d-flex">
					<div
						class="d-flex align-items-center content_menu_btn pointer"
						(click)="showMenu = true"
					>
						<i class="pi pi-bars text-white"></i>
						<h4 class="m-0 pl-2 text-white hide-on-med-and-down">
							<b>{{ 'course.details.content' | translate }}</b>
						</h4>
					</div>
					<div>
						<h5
							class="m-0 text-white title-reader font-weight-bold"
							style="line-height: 20px"
						>
							{{ course.Title }}
						</h5>
						<h6 class="m-0 text-white title-reader" style="font-size: 14px">
							{{ currentContent?.Title }}
						</h6>
					</div>
					<!-- <button class="btn btn-sm btn-outline-primary mx-1">{{'course.details.create' | translate}}</button> -->
				</div>
				<div class="">
					<button
						class="btn btn-sm btn-link mx-1"
						id="courseMenuDrop"
						[matMenuTriggerFor]="courseMenuDrop"
					>
						<i class="fa fa-ellipsis-v text-white"></i>
					</button>

					<p-dialog
						[header]="
							loadingCheckIfUserAlreadyReport
								? ''
								: !userAlreadyReportTheContent
								? 'Dites nous pour quelles raison vous signalez ce contenue'
								: 'Vous avez déjà signalé ce contenue'
						"
						[(visible)]="displayConfirmReportModal"
						[style]="{ width: '50vw' }"
						class="dialog-report-course"
						[baseZIndex]="10000"
					>
						<div class="report-loader" *ngIf="loadingCheckIfUserAlreadyReport">
							<app-loading
								[isLoading]="loadingCheckIfUserAlreadyReport"
							></app-loading>
						</div>

						<div
							*ngIf="
								!loadingCheckIfUserAlreadyReport && userAlreadyReportTheContent
							"
						>
							<div
								*ngIf="
									userReportForCurrentContent &&
									userReportForCurrentContent.Object !== 4
								"
							>
								<span>Objet du signalement:</span>
								<ul>
									<li>
										<h3 class="user-report">
											{{ getReportObject(userReportForCurrentContent.Object) }}
										</h3>
									</li>
								</ul>
							</div>
							<div
								*ngIf="
									userReportForCurrentContent &&
									userReportForCurrentContent.Description
								"
							>
								<span>Description:</span>
								<p class="user-report desc">
									{{ userReportForCurrentContent.Description }}
								</p>
							</div>
						</div>

						<p-dropdown
							*ngIf="
								!loadingCheckIfUserAlreadyReport && !userAlreadyReportTheContent
							"
							[options]="reportObjects"
							[(ngModel)]="selectedCourseReportObject"
							optionLabel="label"
							placeholder="Selection l'objet du signalement"
						></p-dropdown>

						<div
							class="mt-50"
							*ngIf="
								!loadingCheckIfUserAlreadyReport &&
								userHaveCustomReportObject &&
								!userAlreadyReportTheContent
							"
						>
							<label class="report-desc-label"
								>Decrivez l'objet de votre signalement:</label
							>
							<textarea
								*ngIf="
									!loadingCheckIfUserAlreadyReport &&
									!userAlreadyReportTheContent
								"
								class="w-100"
								[rows]="5"
								[cols]="30"
								pInputTextarea
								autoResize="autoResize"
								[(ngModel)]="userCustomReportContent"
							></textarea>
						</div>

						<ng-template
							*ngIf="
								!loadingCheckIfUserAlreadyReport && !userAlreadyReportTheContent
							"
							pTemplate="footer"
						>
							<div class="p-field-checkbox">
								<p-checkbox
									*ngIf="
										!loadingCheckIfUserAlreadyReport &&
										!userAlreadyReportTheContent
									"
									[(ngModel)]="userHaveCustomReportObject"
									binary="true"
									inputId="binary"
								></p-checkbox>
								<label
									*ngIf="
										!loadingCheckIfUserAlreadyReport &&
										!userAlreadyReportTheContent
									"
									class="mb-0 pl-2"
									for="binary"
									>Autre raison de signalement ?</label
								>
							</div>
							<div
								*ngIf="
									!loadingCheckIfUserAlreadyReport &&
									!userAlreadyReportTheContent
								"
							>
								<button
									pButton
									pRipple
									type="submit"
									label="."
									class="p-button-raised p-button-danger"
									(click)="submitContentReport()"
								>
									<span *ngIf="!courseReportLoading">Signaler le cours</span>
									<app-loading
										*ngIf="courseReportLoading"
										[isLoading]="courseReportLoading"
									></app-loading>
								</button>
							</div>
						</ng-template>
					</p-dialog>

					<mat-menu #courseMenuDrop="matMenu">
						<button (click)="onSaveOrRemoveSave($event)" mat-menu-item>
							{{
								courseSaved && courseSaved.Id ? 'Unsave course' : 'Save course'
							}}
						</button>
						<button (click)="onReport($event)" mat-menu-item>
							{{ 'course.details.reportContent' | translate }}
						</button>
						<!-- <button (click)="onDownload($event)" mat-menu-item>
							{{ 'course.details.downloadCourse' | translate }}
						</button> -->
					</mat-menu>
				</div>
			</div>

			<div
				class="video_contain pb-0 d-flex align-items-center h-default justify-content-center"
				*ngIf="!finishloading"
			>
				<i class="fa-4x fas fa-spinner fa-spin"></i>
			</div>

			<div
				*ngIf="!currentContent && finishloading"
				class="video_contain pb-0 d-flex align-items-center h-default justify-content-center"
			></div>

			<div
				class="video_contain pb-0"
				*ngIf="currentSectionTest && finishloading"
			>
				<app-courses-reader-quiz
					[user]="user"
					[(currentSection)]="currentSection"
					[(currentSectionTest)]="currentSectionTest"
					(emitResetContent)="resetContent($event)"
					[course]="course"
				></app-courses-reader-quiz>
			</div>

			<div class="video_contain pb-0" *ngIf="currentContent && currentContent">
				<app-courses-reader-video
					[user]="user"
					(emitResetCourseSectionTest)="resetTest($event)"
					[(currentSection)]="currentSection"
					[(currentContent)]="currentContent"
					[(currentTranscription)]="currentTranscription"
					[course]="course"
					[(courseViews)]="courseViews"
					*ngIf="
						currentContent &&
						contentType &&
						currentContent.Type === contentType.VIDEO
					"
				></app-courses-reader-video>

				<app-courses-reader-pdf
					[user]="user"
					[(currentSection)]="currentSection"
					[(currentContent)]="currentContent"
					[course]="course"
					[(courseViews)]="courseViews"
					*ngIf="
						currentContent &&
						contentType &&
						currentContent.Type === contentType.PDF
					"
				></app-courses-reader-pdf>

				<app-courses-reader-ebook
					[user]="user"
					[(currentSection)]="currentSection"
					[(currentContent)]="currentContent"
					[(courseViews)]="courseViews"
					[course]="course"
					*ngIf="
						currentContent &&
						contentType &&
						currentContent.Type === contentType.EBOOK
					"
				></app-courses-reader-ebook>

				<!--video controls class="w-100">
                  <source src="../../../assets/img/video_test.mp4" type="video/mp4">
                  {{
          'course.details.errorLoadingVideo' | translate
          }}
                </video-->
			</div>
		</section>

		<app-courses-reader-content
			*ngIf="finishloading"
			[userAbonnement]="userAbonnement"
			[course]="course"
			[user]="user"
			[currentTranscription]="currentTranscription"
			[(courseViews)]="courseViews"
		>
		</app-courses-reader-content>
	</div>
</div>

<app-courses-reader-header
	[userAbonnement]="userAbonnement"
	[user]="user"
	[course]="course"
	[(showMenu)]="showMenu"
	[(currentSection)]="currentSection"
	[(currentTranscription)]="currentTranscription"
	[(currentContent)]="currentContent"
	[(currentSectionTest)]="currentSectionTest"
	[(courseSaved)]="courseSaved"
	[(courseViews)]="courseViews"
></app-courses-reader-header>

<app-home-footer *ngIf="!slug"></app-home-footer>
