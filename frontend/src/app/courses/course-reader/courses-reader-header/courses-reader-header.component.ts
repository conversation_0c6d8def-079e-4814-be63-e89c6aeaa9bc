import {
	Component,
	EventEmitter,
	Input,
	OnChanges,
	OnInit,
	Output,
	ViewEncapsulation
} from '@angular/core';
import { CourseBaseContent } from '~/app/models/course-base-content.model';
import { CourseSection } from '~/app/models/course-section.model';
import { ContentType, Course } from '~/app/models/course.model';
import { CourseContent } from '~/app/models/course-content.model';
import { CourseSectionTest } from '~/app/models/course-section-test.model';
import { CourseTranscription } from '~/app/models/course-transcription.model';
import { UtilsService } from '~/app/services/utils.service';
import { User, UserRoleType } from '~/app/models/user.model';
import { CourseView } from '~/app/models/course-view.model';
import { CourseSaved } from '~/app/models/course-saved.model';
import { TranslationService } from '~/app/services/translate/translation.service';
import { CourseSavedService } from '~/app/services/courses/course-saved.service';
import { UserService } from '~/app/services/users/user.service';
import { environment } from '~/environments/environment';
import { Abonnement } from '~/app/models/abonnements/abonnement.model';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { AbonnementsService } from '~/app/services/abonnements/abonnements.service';
import { OrderService } from '~/app/services/orders/order.service';

@Component({
	selector: 'app-courses-reader-header',
	templateUrl: './courses-reader-header.component.html',
	styleUrls: ['./courses-reader-header.component.scss'],
	encapsulation: ViewEncapsulation.None
})
export class CoursesReaderHeaderComponent implements OnInit, OnChanges {
	visibleSidebar2 = false;

	@Input()
	user: User;

	@Input()
	course: Course;

	@Input()
	currentSectionTest: CourseSectionTest;
	@Output()
	currentSectionTestChange: EventEmitter<CourseSectionTest> = new EventEmitter<CourseSectionTest>();

	@Input()
	showMenu: boolean;
	@Output()
	showMenuChange: EventEmitter<boolean> = new EventEmitter<boolean>();

	@Input()
	currentSection: CourseSection;
	@Output()
	currentSectionChange: EventEmitter<CourseSection> = new EventEmitter<CourseSection>();

	@Input()
	currentContent: CourseContent;
	@Output()
	currentContentChange: EventEmitter<CourseContent> = new EventEmitter<CourseContent>();

	@Input()
	currentTranscription: string;
	@Output()
	currentTranscriptionChange: EventEmitter<string> = new EventEmitter<string>();

	contentType = ContentType;

	@Input()
	courseViews: CourseView[];
	@Output()
	courseViewsChange: EventEmitter<CourseView[]> = new EventEmitter<
		CourseView[]
	>();

	@Input()
	courseSaved: CourseSaved;
	@Output()
	courseSavedChange: EventEmitter<CourseSaved> = new EventEmitter<CourseSaved>();

	courseHasSaved = null;

	rootURLCourse = `${environment.api}/courses`;

	@Input() userAbonnement: Abonnement;

	ableToDownload = false;

	constructor(
		private utilsService: UtilsService,
		private translationService: TranslationService,
		private courseSavedService: CourseSavedService,
		private userService: UserService,
		private router: Router,
		private toastrService: ToastrService,
		private abonnementService: AbonnementsService,
		private orderService: OrderService
	) {}

	async ngOnInit(): Promise<void> {
		const abonnement = await this.abonnementService
			.getUserActiveAbonnement(this.user?.Slug)
			.toPromise();

		const order = await this.orderService
			.getByCoursesSlugAndUserSug(this.course?.Slug, this.user?.Slug)
			.toPromise();
		console.log('***ORDER: ', order);
		this.ableToDownload =
			(this.user && this.user.Role === UserRoleType.ADMIN) ||
			!!(order && order.Id) ||
			+this.course?.CreatedBy.Id === +this.user?.Id;
		console.log('able to download: ', this.ableToDownload);

		if (
			abonnement &&
			abonnement.Forfait &&
			abonnement.Forfait.Name.toLowerCase() !== 'essential'
		) {
			this.ableToDownload = true;
		}
	}

	async ngOnChanges(props): Promise<void> {
		//console.log('ccc : ', this.courseViews);
		// console.log('change---', props);
		// console.log(this.user, this.course);
		if (this.user && this.course) {
			this.user = await this.userService.getUserConnected().toPromise();
			this.courseHasSaved = await this.courseSavedService
				.checkIfUserHasSavedTheCourse(this.user.Slug, this.course.Slug)
				.toPromise();
			// console.log('initialisation', this.courseHasSaved);
		}
	}

	getRithTime(courseDuration: number): string {
		return this.utilsService.getRithTime(
			courseDuration,
			this.translationService.getLanguage()
		);
	}

	getIsView(content: CourseBaseContent): boolean {
		return !this.courseViews
			? false
			: this.courseViews.find((f) => f.Content.Id === content.Id)
			? true
			: false;
	}

	startTheQuiz(e, courseSection: CourseSection): void {
		e.preventDefault();
		this.setcurrentSectionTest(courseSection.Contents[0] as CourseSectionTest);
		this.setcurrentSection(courseSection);
		console.log('current test', this.currentSectionTest);
		this.onClose(false);
	}

	async changeContent(
		e,
		contentSection: CourseBaseContent,
		section: CourseSection
	): Promise<void> {
		e.preventDefault();
		if (
			contentSection.Type === ContentType.VIDEO ||
			contentSection.Type === ContentType.EBOOK ||
			contentSection.Type === ContentType.PDF
		) {
			this.setcurrentSectionTest(null);
			this.setcurrentContent(contentSection as CourseContent);
		} else if (contentSection.Type === ContentType.QUIZ) {
			this.setcurrentContent(null);
			this.setcurrentSectionTest(contentSection as CourseSectionTest);
		}
		this.setcurrentSection(section);
		this.onClose(false);
	}

	setcurrentSection(val: CourseSection): void {
		this.currentSectionChange.emit(val);
		this.currentSection = val;
	}

	setcurrentTranscription(val: string): void {
		this.currentTranscriptionChange.emit(val);
		this.currentTranscription = val;
	}

	setcurrentContent(val: CourseContent): void {
		this.currentContentChange.emit(val);
		this.currentContent = val;
	}

	setcurrentSectionTest(val: CourseSectionTest): void {
		this.currentSectionTestChange.emit(val);
		this.currentSectionTest = val;
	}

	async setTranscription(): Promise<void> {
		if (
			this.currentContent &&
			this.currentContent.Type === this.contentType.VIDEO &&
			this.currentContent.Transcriptions &&
			this.currentContent.Transcriptions.length > 0
		) {
			const currentTranscription = await this.utilsService
				.getElementFromUrl(
					(this.currentContent.Transcriptions[0] as CourseTranscription).Media
						.Hashname
				)
				.toPromise();

			this.setcurrentTranscription(currentTranscription);
		}
	}

	onClose(visible): void {
		this.showMenu = visible;
		this.showMenuChange.emit(visible);
	}

	downloadAssigment($event: MouseEvent, courseSection: CourseSection) {}

	redirectToPricing(): void {
		this.router.navigateByUrl('/home/<USER>');
		this.toastrService.warning(
			'Veullez choisir le forfait approprié pour avoir acccès au exercices',
			'Brain-maker'
		);
	}
}
