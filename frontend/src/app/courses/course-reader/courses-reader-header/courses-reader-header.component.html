<p-sidebar
	[visible]="showMenu"
	(visibleChange)="onClose($event)"
	position="left"
	[baseZIndex]="10000"
	class="pl-0 pr-0 p-sidebar-course-reader"
>
	<h3 class="">
		{{ 'course.details.content' | translate }}
	</h3>
	<p-scrollPanel [style]="{ width: '100%', height: '100vh' }" *ngIf="course">
		<p-panel
			header="{{ courseSection.Title }}"
			[toggleable]="true"
			[collapsed]="true"
			class="bg-white"
			*ngFor="let courseSection of course.Sections; let ind = index"
		>
			<ng-template pTemplate="icons"> </ng-template>
			<ul
				*ngIf="
					courseSection &&
					courseSection.Contents &&
					courseSection.Contents.length > 0 &&
					courseSection.Contents[0].Type === contentType.ASSIGNMENT
				"
			>
				<li class="pb-0 pt-0 mb-2">
					<a
						*ngIf="ableToDownload"
						class="sidebar_menu_content pb-0 pt-0 btn-link"
						[href]="
							rootURLCourse + '/' + course.Slug + '/download/exercicesFiles'
						"
						download
						>{{ 'course.details.download' | translate }}.</a
					>
					<a
						*ngIf="!ableToDownload"
						class="sidebar_menu_content pb-0 pt-0 btn-link"
						(click)="redirectToPricing()"
						>{{ 'course.details.download' | translate }}.</a
					>
					<!-- <a
						href="#"
						(click)="downloadAssigment($event, courseSection)"
						class="sidebar_menu_content text-white pb-0 pt-0 btn-link"
					>
						{{ 'course.details.download' | translate }}
					</a> -->
				</li>
			</ul>
			<ul
				*ngIf="
					courseSection &&
					courseSection.Contents &&
					courseSection.Contents.length > 0 &&
					courseSection.Contents[0].Type === contentType.QUIZ
				"
			>
				<li class="pb-0 pt-0">
					<a
						href="#"
						(click)="startTheQuiz($event, courseSection)"
						class="sidebar_menu_content pb-0 pt-0 btn-link"
					>
						{{ 'course.details.startQuiz' | translate }}
					</a>
				</li>
			</ul>
			<ul
				*ngIf="
					courseSection &&
					courseSection.Contents &&
					courseSection.Contents.length > 0 &&
					(courseSection.Contents[0].Type === contentType.VIDEO ||
						courseSection.Contents[0].Type === contentType.PDF ||
						courseSection.Contents[0].Type === contentType.EBOOK)
				"
			>
				<li
					class="pb-0 pt-0"
					*ngFor="let contentSection of courseSection.Contents; let i = index"
				>
					<a
						href="#"
						(click)="changeContent($event, contentSection, courseSection)"
						class="sidebar_menu_content d-flex justify-content-between pb-0 pt-0 btn-link"
						[ngClass]="{ active: contentSection?.Id === currentContent?.Id }"
					>
						<div class="">
							<span *ngIf="courseViews && getIsView(contentSection)"
								><i class="fas fa-circle text-success"></i
							></span>

							<span *ngIf="courseViews && !getIsView(contentSection)"
								><i class="far fa-circle"></i
							></span>

							<!-- <p *ngIf="getIsView(contentSection)">Oui</p>
							<p *ngIf="getIsView(contentSection)">Non</p> -->
							<!-- <i class="far fa-circle" *ngIf="!getIsView(contentSection)"></i> -->
						</div>
						<div class="sidebar_menu_content_main pl-2">
							<span style="font-size: 0.9em; font-weight: 500"
								>{{ contentSection.Title }}
							</span>
							<small>
								{{ getRithTime(contentSection.TotalTime) }}
							</small>
						</div>
						<div>
							<i
								*ngIf="courseHasSaved !== null"
								[class]="courseHasSaved ? 'fas fa-bookmark' : 'far fa-bookmark'"
							></i>

							<!-- <p>{{ courseHasSaved ? 'Oui' : 'NOn' }}</p> -->
						</div>
					</a>
					<p-divider></p-divider>
				</li>
			</ul>
		</p-panel>
	</p-scrollPanel>
</p-sidebar>
