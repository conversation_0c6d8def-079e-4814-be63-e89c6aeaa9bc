import {
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	ViewEncapsulation
} from '@angular/core';
import { Course } from '~/app/models/course.model';
import { CourseNote } from '~/app/models/course-note.model';
import { O } from '@angular/cdk/keycodes';
import { Media } from '~/app/models/media.model';
import { environment } from '~/environments/environment';
import { UtilsService } from '~/app/services/utils.service';
import { TranslateService } from '@ngx-translate/core';
import { ToastrService } from 'ngx-toastr';
import { CKEditor4 } from 'ckeditor4-angular';
import { User, UserRoleType } from '~/app/models/user.model';
import { CourseService } from '~/app/services/courses/course.service';
import { CourseNoteService } from '~/app/services/courses/course-note.service';
import { CourseView } from '~/app/models/course-view.model';
import { TranslationService } from '~/app/services/translate/translation.service';
import { CourseBaseContent } from '~/app/models/course-base-content.model';
import { CourseSavedService } from '~/app/services/courses/course-saved.service';
import { UserService } from '~/app/services/users/user.service';
import { Abonnement } from '~/app/models/abonnements/abonnement.model';
import { HomeLoginComponent } from '~/app/home/<USER>/home-login.component';
import { AbonnementsService } from '~/app/services/abonnements/abonnements.service';
import { Router } from '@angular/router';
import { OrderService } from '~/app/services/orders/order.service';
import { CourseOpinionService } from '~/app/services/courses/course-opinion.service';

@Component({
	selector: 'app-courses-reader-content',
	templateUrl: './courses-reader-content.component.html',
	styleUrls: ['./courses-reader-content.component.scss'],
	encapsulation: ViewEncapsulation.None
})
export class CoursesReaderContentComponent implements OnInit {
	@Input()
	course: Course;

	@Input()
	user: User;

	cover = '/assets/img/700x500.png';
	totalLearners = 0;
	isLoadingDowloadAssignment = false;

	@Input()
	currentTranscription: string;
	profile = './assets/img/default.png';
	similarCourses: any[];

	@Input()
	courseViews: CourseView[];
	@Output()
	courseViewsChange: EventEmitter<CourseView[]> = new EventEmitter<
		CourseView[]
	>();

	courseNoteInput = '';
	courseNotesUser: CourseNote[] = [];
	isLoadingSaveCourseNote = false;

	rootURLCourse = `${environment.api}/courses`;

	@Input() userAbonnement: Abonnement;

	ableToDownload = false;

	likeCount: number;
	disLikeCount: number;
	loveCount: number;
	viewCount: number;

	constructor(
		private courseService: CourseService,
		private utilService: UtilsService,
		private toastr: ToastrService,
		private translationService: TranslationService,
		private translateService: TranslateService,
		private courseNoteSerice: CourseNoteService,
		private courseSavedService: CourseSavedService,
		private abonnementService: AbonnementsService,
		private router: Router,
		private orderService: OrderService,
		private courseOpinionService: CourseOpinionService
	) {}

	async ngOnInit(): Promise<void> {
		console.log('**** CourseViews: ', this.courseViews);
		if (this.course.CreatedBy.Photo) {
			this.profile = this.course.CreatedBy.Photo.Hashname;
		}

		const categories = this.course.Categories.map((f) => f.Id);
		const res = await this.courseService
			.getBySameCourse({ categories })
			.toPromise();
		let similarCourses = res.filter((f) => f.Id !== this.course.Id);

		this.similarCourses = await Promise.all(
			similarCourses.map(async (f) => {
				// const Saved =
				// !this.courseViews || this.courseViews.length === 0
				// 	? false
				// 	: !!this.courseViews.find((g) =>
				// 			f.Sections.find((p) =>
				// 				p.Contents.find((k) => k.Id === g.Content.Id)
				// 			)
				// 	  );
				const LCount = await this.courseService
					.countCourseViewsByCourse(f.Slug)
					.toPromise();

				const Saved = await this.courseSavedService
					.checkIfUserHasSavedTheCourse(this.user?.Slug, f?.Slug)
					.toPromise();
				console.log('!!!!!!!!!!!!!!!!!!!!!!!!', Saved, LCount);
				return { ...f, Saved, LCount };
			})
		);

		// this.similarCourses = similarCourses;

		this.addToatalLearnersToSimilarCourse();

		console.log('list of same', this.similarCourses);

		this.totalLearners = await this.getTotalLearners(this.course.Slug);

		this.getCourseNotesForUser();

		const abonnement = await this.abonnementService
			.getUserActiveAbonnement(this.user.Slug)
			.toPromise();

		const order = await this.orderService
			.getByCoursesSlugAndUserSug(this.course.Slug, this.user.Slug)
			.toPromise();
		console.log('***ORDER: ', order);
		this.ableToDownload =
			(this.user && this.user.Role === UserRoleType.ADMIN) ||
			!!(order && order.Id) ||
			+this.course.CreatedBy.Id === +this.user.Id;
		console.log('able to download: ', this.ableToDownload);
		if (
			abonnement &&
			abonnement.Forfait &&
			abonnement.Forfait.Name.toLowerCase() !== 'essential'
		) {
			this.ableToDownload = true;
		}

		// We GET course opinions and count view, likes and dislikes
		const {
			likeCount,
			disLikeCount,
			loveCount,
			viewCount
		} = await this.courseOpinionService.countCourseOpinions(this.course);
		this.likeCount = likeCount;
		this.loveCount = loveCount;
		this.viewCount = viewCount;
	}

	async getTotalLearners(slug: string): Promise<number> {
		try {
			const result = await this.courseService
				.getCountLearnersForCourse(slug)
				.toPromise();
			return result;
		} catch (error) {
			console.log('ERROR TO GET LEARNERS OF COURSE ', error);
		}
	}

	async addToatalLearnersToSimilarCourse() {
		for (const c of this.similarCourses) {
			const totalLearners = await this.getTotalLearners(c.Slug);
			c['TotalLearners'] = totalLearners;
		}
	}

	/* async downloadExerciceFiles() {

		this.isLoadingDowloadAssignment = true;
		try {

			await this.courseService.DownloadExercicesFiles(this.course.Slug).toPromise();
		} catch (error) {

			console.log('ERROR TO DOWNLOAD EXERCICE FILES', error);
		} finally {

			this.isLoadingDowloadAssignment = false;
		}

	} */

	getIsView(content: CourseBaseContent): boolean {
		return !this.courseViews
			? false
			: !!this.courseViews.find((f) => f.Content.Id === content.Id);
	}

	getRithTime(courseDuration: number): string {
		return this.utilService.getRithTime(
			courseDuration,
			this.translationService.getLanguage()
		);
	}

	onChange(event: CKEditor4.EventInfo): void {
		this.course.Notes[0].Message = event.editor.getData();
	}

	async getCourseNotesForUser() {
		try {
			this.courseNotesUser = await this.courseNoteSerice
				.getNotesBelongToUser(this.course.Id, this.user.Id)
				.toPromise();
			console.log('COURSES NOTES FOR USER ', this.courseNotesUser);
		} catch (error) {
			console.log('ERROR TO GET COURSES NOTES FOR USER ', error);
		} finally {
		}
	}

	async onSaveNote(e): Promise<void> {
		if (this.courseNoteInput.length < 10) {
			this.toastr.error(
				'Course note should be at least 10 characters',
				'Brain-maker'
			);
			return;
		}

		e.preventDefault();

		const courseNote = new CourseNote(
			0,
			'',
			null,
			null,
			'',
			this.course,
			this.user
		);
		courseNote.Message = this.courseNoteInput;

		this.isLoadingSaveCourseNote = true;
		try {
			const message = await this.translateService
				.get('home.register.success')
				.toPromise();
			/* if (this.course.Notes[0].Id) {
				this.course.Notes[0] = await this.courseNoteSerice
					.edit(this.course.Notes[0])
					.toPromise();
			}  */
			const result = await this.courseNoteSerice.add(courseNote).toPromise();
			this.courseNotesUser.unshift(result);

			this.toastr.success(message, 'Brain-maker');

			document.getElementById('courseNotes-container').scrollTo(0, 0);
			this.courseNoteInput = '';
		} catch (e) {
			console.log(e);
			const message = await this.translateService
				.get('home.register.error')
				.toPromise();
			this.toastr.error(message, 'Brain-maker');
		} finally {
			this.isLoadingSaveCourseNote = false;
		}
	}

	async deleteCourseNote(courseNote: CourseNote) {
		try {
			const result = await this.courseNoteSerice.delete(courseNote).toPromise();

			const foundIndexDeleted = this.courseNotesUser.findIndex(
				(m) => m.Id === courseNote.Id
			);
			this.courseNotesUser.splice(foundIndexDeleted, 1);
			this.toastr.warning('Course note deleted', 'Brain-maker');
		} catch (error) {
			console.log('course note delete error', error);
			//let message = await this.translateService.get('admin.misc.editStatusError').toPromise();
			this.toastr.error('Error to delete note', 'Brain-maker');
		}
	}

	async updateCourseNote(courseNote: CourseNote) {
		try {
			const result = await this.courseNoteSerice.edit(courseNote).toPromise();

			const foundIndexUpdated = this.courseNotesUser.findIndex(
				(m) => m.Id === courseNote.Id
			);
			this.courseNotesUser.splice(foundIndexUpdated, 1, result);
			this.toastr.info('Course note updated', 'Brain-maker');
		} catch (error) {
			console.log('course note update error', error);
			//let message = await this.translateService.get('admin.misc.editStatusError').toPromise();
			this.toastr.error('Error to update course note', 'Brain-maker');
		}
	}

	copyCourseNote(courseNote: CourseNote) {
		if (this.copyTextToClipboard(courseNote.Message)) {
			this.toastr.info('Copied', 'Message');
		} else {
			this.toastr.error('Error to copy', 'Message');
		}
	}

	getCourseImage(cover: Media): string {
		return cover && cover.Hashname
			? `${environment.path}/${cover.Hashname}`
			: this.cover;
	}

	getTime(course: Course): string {
		return this.utilService.getCourseTime(course);
	}

	copyTextToClipboard(text): boolean {
		const txtArea = document.createElement('textarea');
		txtArea.id = 'txt';
		txtArea.style.position = 'fixed';
		txtArea.style.top = '0';
		txtArea.style.left = '0';
		txtArea.style.opacity = '0';
		txtArea.value = text;
		document.body.appendChild(txtArea);
		txtArea.select();

		try {
			const successful = document.execCommand('copy');
			const msg = successful ? 'successful' : 'unsuccessful';
			console.log('Copying text command was ' + msg);
			if (successful) {
				return true;
			}
		} catch (err) {
			console.log('Oops, unable to copy');
		} finally {
			document.body.removeChild(txtArea);
		}
		return false;
	}

	redirectToPricing(): void {
		this.router.navigateByUrl('/home/<USER>');
		this.toastr.warning(
			'Veullez choisir le forfait approprié pour avoir accès au exercies',
			'Brain-maker'
		);
	}
}
