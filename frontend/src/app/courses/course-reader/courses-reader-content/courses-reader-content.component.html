<p-tabView styleClass="tabview-custom">
	<p-tabPanel>
		<ng-template pTemplate="header">
			<i class="pi pi-book"></i>
			<span class="ml-2"> {{ 'course.details.description' | translate }}</span>
		</ng-template>
		<div class="container">
			<div class="row">
				<div class="col-md-7 col-sm-12">
					<div class="row">
						<div class="col-sm-12 col-md-6 mb-3">
							<h4>
								<b>{{ 'course.details.teacher' | translate }}</b>
							</h4>
							<div class="teacher_details d-flex">
								<img [src]="profile" alt="" />
								<div class="teacher_details_info ml-3">
									<p class="mb-1">
										<b>{{ course.CreatedBy.Firstname }}</b>
									</p>
									<small>{{ course.CreatedBy.Profil }}</small>
								</div>
							</div>
						</div>

						<div class="col-sm-12 col-md-6 d-flex">
							<p-divider layout="vertical"></p-divider>

							<div>
								<h4>
									<b>{{ 'course.details.linkedToCourse' | translate }}</b>
								</h4>
								<div class="lien_details_cours mb-2">
									<i class="pi pi-folder-open"></i>
									<a
										*ngIf="ableToDownload"
										class="ml-2 mr-2 download-link"
										[href]="
											rootURLCourse +
											'/' +
											course.Slug +
											'/download/exercicesFiles'
										"
										download
										>{{ 'course.details.exerciceFiles' | translate }}.</a
									>

									<a
										*ngIf="!ableToDownload"
										class="ml-2 mr-2 download-link"
										(click)="redirectToPricing()"
										>{{ 'course.details.exerciceFiles' | translate }}.</a
									>
									<!-- <a *ngIf="!isLoadingDowloadAssignment" href=""
										><small>{{
											'course.details.showAll' | translate
										}}</small></a
									> -->
									<a *ngIf="isLoadingDowloadAssignment"
										><small>Downloading...</small></a
									>
								</div>
								<!-- <div class="lien_details_cours mb-2">
                                    <i class="pi pi-briefcase"></i>
                                    <b class="ml-2 mr-2">{{'course.details.certificates'|translate}} .</b>
                                    <a href=""><small>{{'course.details.showAll' |translate}}</small></a>
                                </div> -->
							</div>
						</div>
					</div>
					<div class="details_cours mt-3">
						<h4 class="mb-1">
							<b>{{ 'course.details.detailsOnCourse' | translate }}</b>
						</h4>
						<div class="details_cours_chiffre mb-2">
							<span> {{ getRithTime(course.Duration) }} . </span>
							<span>{{ course.Level.join(',') }} . </span>
							<span
								>{{ 'course.details.publishedThe' | translate }}
								{{ course.CreatedAt | timeago }}
							</span>
						</div>
						<p [innerHTML]="course.Resume" class="text-justify"></p>
						<h4>
							<b>{{ 'course.details.couveredSkills' | translate }}</b>
						</h4>
						<div class="details_competences">
							<p-chip
								label="Gestion des projets agiles"
								styleClass="mr-2 mb-2 custom-chip pointer"
							></p-chip>
							<p-chip
								label="Gestion du temps"
								styleClass="mr-2 mb-2 custom-chip pointer"
							></p-chip>
						</div>
						<h4 class="mt-2">
							<b
								>{{ totalLearners }}
								{{ 'course.details.learners' | translate }}</b
							>
						</h4>
						<div class="details_competences">
							<p class="d-flex">
								<span
									matTooltip="{{ 'course.like' | translate }}"
									class="material-icons"
								>
									thumb_up_alt</span
								>&nbsp; {{ likeCount }}
								{{ 'course.details.loveContent' | translate }}
							</p>
							<p class="d-flex">
								<span
									matTooltip="{{ 'course.like' | translate }}"
									class="material-icons"
								>
									visibility</span
								>&nbsp; {{ viewCount }}
								{{ 'course.details.startCourse' | translate }}
							</p>
						</div>
						<h4 class="mt-3 mb-3">
							<b>{{ 'course.details.teacher' | translate }}</b>
						</h4>
						<div class="teacher_details d-flex">
							<img [src]="profile" alt="" />
							<div class="teacher_details_info ml-3">
								<p>
									<b>{{ course.CreatedBy.Firstname }} </b>
								</p>
								<small>{{ course.CreatedBy.Profil }}</small>
							</div>
						</div>
					</div>
				</div>
				<div class="col-md-5 col-sm-12 d-flex">
					<p-divider layout="vertical" class="hide_on_med_and_down"></p-divider>
					<div class="course_proposals">
						<h4>
							<b>{{ 'course.details.relatedCourses' | translate }}</b>
						</h4>
						<div *ngIf="similarCourses">
							<div
								class="course_proposal d-flex flex-column"
								*ngFor="let similarCourse of similarCourses"
							>
								<div class="similar-course-item">
									<div class="similar-course-image">
										<img
											[src]="getCourseImage(similarCourse.CoverImage)"
											alt=""
										/>
										<!-- <small class="duree_proposal text-white p-1">{{getTime(similarCourse)}}</small> -->
									</div>
									<div
										class="course_proposal_details d-flex flex-column justify-content-between"
									>
										<b
											><a [routerLink]="'/courses/' + similarCourse.Slug">{{
												similarCourse.Title
											}}</a></b
										>
										<small class="text-success">{{
											getTime(similarCourse)
										}}</small>
										<div
											class="d-flex justify-content-between align-items-center"
										>
											<span
												>{{ similarCourse.LCount }}
												{{ 'course.details.learners' | translate }}</span
											>
											<i
												[class]="
													similarCourse.Saved
														? 'fas fa-bookmark'
														: 'far fa-bookmark'
												"
											></i>
										</div>
									</div>
								</div>
								<p-divider></p-divider>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!-- <div class="details_cours mt-3">
            <h4 class="mb-1"><b>{{'course.details.detailsOnCourse' |translate}}</b></h4>
            <div class="details_cours_chiffre mb-2">
                <span>50 minutes . </span>
                <span>{{course.Level.join(',')}} . </span>
                <span>{{'course.details.publishedThe' |translate}} {{ course.CreatedAt|dateAgo }} </span>
            </div>
        </div> -->
	</p-tabPanel>
	<p-tabPanel header="Header II">
		<ng-template pTemplate="header">
			<i class="pi pi-bookmark"></i>
			<span class="ml-2">{{ 'course.details.notes' | translate }}</span>
		</ng-template>
		<div class="container">
			<div class="row mt-2">
				<form
					(ngSubmit)="onSaveNote($event)"
					class="col-sm-12 col-md-5 mb-3"
					*ngIf="course"
				>
					<div class="d-flex flex-column">
						<ckeditor
							class="form-control ckeditor my-2"
							type="inline"
							name="note"
							id="note"
							[(ngModel)]="courseNoteInput"
						></ckeditor>
						<button class="btn btn-sm btn-primary ml-auto" type="submit">
							Save
						</button>
					</div>
					<span>0 {{ 'course.details.notesTaken' | translate }}</span>
				</form>
				<div class="col-sm-12 col-md-7 export_notes d-flex">
					<p-divider layout="vertical"></p-divider>
					<div
						class="d-flex flex-column align-items-baseline"
						style="width: 100% !important"
					>
						<!-- <h4>{{ 'course.details.exportNotes' | translate }}</h4> -->
						<!-- <small>{{
							'course.details.descriptionExportNotes' | translate
						}}</small> -->

						<!-- <button
							pButton
							pRipple
							type="button"
							class="p-button mt-1"
							label="{{ 'course.details.download' | translate }}"
						></button> -->

						<div class="courseNotes-container" id="courseNotes-container">
							<!-- <div *ngFor="let courseNote of courseNotesUser">
								<div [innerHtml]="courseNote.Message"></div>
								<hr />
							</div> -->

							<app-course-note-item
								*ngFor="let courseNote of courseNotesUser"
								[courseNote]="courseNote"
								[currentUser]="user"
								(onDeleteCourseNote)="deleteCourseNote($event)"
								(onUpdateCourseNote)="updateCourseNote($event)"
								(onCopyCourseNote)="copyCourseNote($event)"
							>
							</app-course-note-item>
						</div>
					</div>
				</div>
			</div>
		</div>
	</p-tabPanel>

	<p-tabPanel header="Header III">
		<ng-template pTemplate="header">
			<i class="pi pi-align-left"></i>
			<span class="ml-2">{{ 'course.details.transcription' | translate }}</span>
		</ng-template>
		<div class="container">
			<h3>
				<b>{{ 'course.details.welcome' | translate }}</b>
			</h3>
			<p [innerHTML]="currentTranscription"></p>
		</div>
	</p-tabPanel>
</p-tabView>
