import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CourseNote } from '~/app/models/course-note.model';
import { User } from '~/app/models/user.model';

@Component({
  selector: 'app-course-note-item',
  templateUrl: './course-note-item.component.html',
  styleUrls: ['./course-note-item.component.scss']
})
export class CourseNoteItemComponent implements OnInit {
  @Output('onDeleteCourseNote') deleteCourseNoteEvent = new EventEmitter<CourseNote>();
  @Output('onUpdateCourseNote') updateCourseNoteEvent = new EventEmitter<CourseNote>();
  @Output('onCopyCourseNote') copyCourseNoteEvent = new EventEmitter<CourseNote>();

  // tslint:disable-next-line: no-input-rename
  @Input('courseNote') courseNote = new CourseNote(0, '', null, null, '', null, null);

  @Input('currentUser') currentUser;

  showUpdate = false;

  user: User;
  showResponse = false;
  options = [];

  constructor() { }

  async ngOnInit(): Promise<void> {
    //this.user = await this.userService.getUserConnected().toPromise();
    this.options = [
      { name: 'Copier', icon: 'delete', id_option: 1 },
      { name: 'Modifer', icon: 'payment', id_option: 2 },
      { name: 'Supprimer', icon: 'payment', id_option: 4 }
    ].filter(Boolean);
  }

  formatDate(date) {
    return new Date(date).toDateString();
  }

  formaTime(date) {
    return new Date(date).toLocaleTimeString();
  }

  toogleShowResponse(): void {
    this.showResponse = !this.showResponse;
  }
  selectedOptionFxn(option): void {
    switch (option.id_option) {
      case 1:
        this.copyCourseNoteEvent.emit(this.courseNote);
        break;
      case 2:
        this.showUpdate = true;
        break;
      case 4:
        this.deleteCourseNoteEvent.emit(this.courseNote);
        break;

      default:
        break;
    }
  }

  updateMessage() {
    this.updateCourseNoteEvent.emit({ ...this.courseNote });
    this.showUpdate = false;
  }
}
