<div class="row">
	<div class="w-100 col-sm-12 col-lg-12" style="position: relative">
		<div class="mb-0 left w-100">
			<div class="d-flex flex-column" style="position: relative">
				<small>
					{{ formatDate(courseNote.CreatedAt) }},
					{{ formaTime(courseNote.CreatedAt) }}</small
				>
				<div class="rounded" [class]="">
					<div class="p-2 pt-1" *ngIf="!showUpdate">
						<div class="p-2 rounded" [innerHtml]="courseNote.Message"></div>
					</div>

					<form *ngIf="showUpdate">
						<!-- <input #inputUpdate [value]="courseNote.Message" class="p-2 pt-1" /> -->
						<ckeditor
							class="form-control ckeditor my-2"
							type="inline"
							name="note"
							id="note"
							[(ngModel)]="courseNote.Message"
						></ckeditor>
						<div>
							<button
								(click)="updateMessage()"
								class="btn btn-sm btn-primary mr-2"
							>
								update
							</button>
							<button (click)="showUpdate = false" class="btn btn-sm btn-dark">
								cancel
							</button>
						</div>
					</form>
				</div>
			</div>
			<div class="pl-2 ellipsis">
				<i class="pi pi-ellipsis-h pointer" [matMenuTriggerFor]="menu"></i>
				<mat-menu #menu="matMenu">
					<button
						mat-menu-item
						*ngFor="let option of options"
						(click)="selectedOptionFxn(option)"
					>
						<span>{{ option.name }}</span>
					</button>
				</mat-menu>
			</div>
		</div>
	</div>
</div>
<hr />
