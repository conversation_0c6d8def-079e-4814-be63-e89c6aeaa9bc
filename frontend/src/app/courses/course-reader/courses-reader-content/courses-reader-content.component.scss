@import '../../../../assets/scss/variables';

.similar-course-item {
	display: flex;
	flex-direction: row;
	position: relative;
	justify-content: space-between;
	align-items: center;

	.similar-course-image {
		width: 40% !important;
		img {
			width: 100% !important;
			overflow: hidden;
			object-fit: cover;
			max-height: 300px;
		}
	}

	.course_proposal_details {
		width: 60%;
		padding: 0 1rem;

		a {
			color: $color-dark-gray;
			font-weight: initial;

			&:hover {
				color: $color-pink;
			}
		}
	}
}
.lien_details_cours a.download-link {
	cursor: pointer;
	color: $color-dark-gray !important;

	&:hover {
		color: $color-pink !important;
	}
}
.courseNotes-container {
	max-height: 500px;
	padding: 20px 10px;
	overflow-y: auto;
	overflow-x: hidden;
	width: 100%;
}

@media screen and (max-width: 720px) {
	.similar-course-item {
		flex-direction: column;

		.similar-course-image {
			width: 100% !important;
		}

		.course_proposal_details {
			width: 100%;
			padding-top: 1rem;
		}
	}
}
