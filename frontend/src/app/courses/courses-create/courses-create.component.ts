import { Component, OnInit } from '@angular/core';
import {
	ContentType,
	Course,
	notificationMessages
} from '~/app/models/course.model';
import { Category } from '~/app/models/category.model';
import { validate, ValidationError } from 'class-validator';
import { UtilsService } from '~/app/services/utils.service';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { TranslateService } from '@ngx-translate/core';
import { User } from '~/app/models/user.model';
import { UserService } from '~/app/services/users/user.service';
import { CategoryService } from '~/app/services/categories/category.service';
import { Observable } from 'rxjs';
import { AngularEditorConfig } from '@kolkov/angular-editor';
import { CourseService } from '~/app/services/courses/course.service';
import {
	CourseNotification,
	courseNotificationType
} from '~/app/models/course-notification.model';
import { CourseNotificationsService } from '~/app/services/notifications/course-notifications.service';

@Component({
	selector: 'app-courses-create',
	templateUrl: './courses-create.component.html',
	styleUrls: ['./courses-create.component.scss']
})
export class CoursesCreateComponent implements OnInit {
	activeStep = 1;
	course: Course;
	categories: Array<Category>;
	courseType = ContentType;
	user: User;
	resumeConfig: AngularEditorConfig;

	errors: ValidationError[];
	errorsHelper: ValidationError[];
	isLoading: boolean;
	constructor(
		private utilsService: UtilsService,
		private userService: UserService,
		private toastr: ToastrService,
		private translateService: TranslateService,
		private courseService: CourseService,
		private categoryService: CategoryService,
		private router: Router,
		private courseNotificationService: CourseNotificationsService
	) {}

	async ngOnInit(): Promise<void> {
		this.resumeConfig = this.utilsService.getSimpleconfigAngular();
		this.course = new Course(1, '123', null, null, null, null, null);
		if (this.userService.isConnected()) {
			this.user = await this.userService.getUserConnected().toPromise();
			this.course.CreatedBy = this.user;
		} else {
			this.router.navigate(['/']);
		}

		this.categories = await this.categoryService.getAll().toPromise();
	}

	async onSubmit(e): Promise<void> {
		e.preventDefault();

		this.errors = await validate(this.course);
		this.errorsHelper = this.errors;
		console.log('errors', this.errors);
		if (!this.errors || this.errors.length === 0) {
			this.isLoading = true;
			try {
				const course = await this.courseService.add(this.course).toPromise();
				if (course && course.Id) {
					const message = await this.translateService
						.get('home.register.success')
						.toPromise();

					try {
						console.log('*** Envoie de la notification');
						const res = await this.courseNotificationService
							.add(
								new CourseNotification(
									null,
									null,
									null,
									null,
									notificationMessages.NEW_COURSE_CREATED,
									this.course,
									false,
									[],
									courseNotificationType.COURSE_CREATE
								)
							)
							.toPromise();
					} catch (error) {
						alert('Notification cours créé');
					}

					this.router.navigate([`/courses/manage/${course.Slug}`]);
					this.toastr.success(message, 'Brain-maker');
				} else {
					const message = await this.translateService
						.get('home.register.error')
						.toPromise();
					this.toastr.error(message, 'Brain-maker');
				}
			} catch (e) {
				console.log(e);
				const message = await this.translateService
					.get('home.register.error')
					.toPromise();
				this.toastr.error(message, 'Brain-maker');
			} finally {
				this.isLoading = false;
			}
		} else {
			const message = await this.translateService
				.get('language.message')
				.toPromise();
			this.toastr.error(message, 'Brain-maker');
		}
	}

	async onItemChange(): Promise<void> {
		this.errorsHelper = await validate(this.course);
	}

	isNotvalid(name: string): boolean {
		return this.utilsService.isNotvalid(name, this.errorsHelper);
	}

	getAllError(name: string): string[] {
		return this.utilsService.getAllError(name, this.errorsHelper);
	}

	/* onNext(e): void {
    e.preventDefault();
   if (this.activeStep === 1  && this.course.Type){
        this.activeStep = this.activeStep + 1;
      } else if (this.activeStep === 2 && this.course.Title){
        this.activeStep = this.activeStep + 1;
      } else if (this.activeStep === 3 && this.course.Categories && this.course.Categories.length > 0){
        this.activeStep = this.activeStep + 1;
      }
  } */
}
