mat-toolbar {
  background-color: white !important;
  .spacer {
    flex: 1;
  }
  mat-divider {
    height: 100%;
    margin: 0 24px !important;
  }
}
.mat-progress-bar {
  opacity: 0.6;
  min-height: 8px;
}
.main {
  height: 100vh;
  display: flex;
  flex-direction: column;
}
.switch {
  flex: 1;
  .step {
    justify-content: center;
    display: flex;
    flex-direction: column;
    h1 {
      margin: 16px auto;
    }
    &.step-1 {
      .row {
        text-align: center;
        justify-content: center;
        .dec {
          margin-right: 24px;
          position: relative;
          padding-top: 16px;
          cursor: pointer;
          padding-bottom: 16px;
          border: 1px solid rgba(81, 83, 107, 0.6);
          &.back-primary,
          &.back-primary h3 {
            color: white !important;
          }
        }
      }
    }
  }
  fieldset {
    border: 1px solid;
    border-radius: 16px;
    padding: 16px;
    position: relative;
    min-width: 75vh;
    margin: 8px;
    legend {
      width: auto;
    }
    button[mat-mini-fab] {
      position: absolute;
      right: 0;
      top: 0;
      outline: none;
    }
    .answers {
      position: relative;
    }
    mat-checkbox .mat-form-field-wrapper,
    mat-radio-button .mat-form-field-wrapper {
      padding: 0;
      .mat-form-field-infix {
        padding: 0;
        height: 48px;
        input {
          height: 48px;
        }
      }
    }
  }
}
.btns {
  border-top: 2px solid rgba(0, 0, 0, 0.4);
  display: flex;
  padding: 24px 32px;
  justify-content: space-between;
  &.end {
    justify-content: flex-end;
  }
  button {
    height: 56px;
  }
}
