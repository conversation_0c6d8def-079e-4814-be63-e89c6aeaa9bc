<!--form  class="main">
  <mat-toolbar>
    <div class="d-flex flex-row justify-content-between w-100">
      <div class="d-flex">
         <span>
          <span class="text-black">Brain-</span
          ><span class="text-danger">maker</span>
        </span>
        <mat-divider [vertical]="true"> </mat-divider>
        <span> {{ 'course.create.lblStep' | translate }} {{ activeStep }} / 4</span>
        <span class="spacer"></span>
      </div>
      <div class="">
        <button mat-stroked-button routerLink="../">
          {{ 'course.create.lblExit' | translate }}
        </button>
      </div>
    </div>
  </mat-toolbar>
  <mat-progress-bar
    mode="determinate"
    color="accent"
    [value]="(activeStep / 4) * 100"
  >
  </mat-progress-bar>

  <div class="switch m-auto p-5">
    <div class="step step-1" *ngIf="activeStep === 1">
      <h1>{{ 'course.create.step1.header' | translate }}</h1>
      <div class="row">
        <div
          class="col-3 dec"
          [class.back-primary]="course.Type === courseType.VIDEO"
          (click)="course.Type = courseType.VIDEO"
          matRipple
        >
          <mat-icon>play_arrow</mat-icon>
          <h3>{{ 'course.create.step1.lblVideo' | translate }}</h3>
          <p>
            {{ 'course.create.step1.descVid' | translate }}
          </p>
        </div>
        <div
          class="col-3 dec"
          matRipple
          [class.back-primary]="course.Type === courseType.PDF"
          (click)="course.Type = courseType.PDF"
        >
          <mat-icon>description</mat-icon>
          <h3>{{ 'course.create.step1.lblDoc' | translate }}</h3>
          <p>
            {{ 'course.create.step1.descDoc' | translate }}
          </p>
        </div>
        <div
          class="col-3 dec"
          matRipple
          [class.back-primary]="course.Type === courseType.EBOOK"
          (click)="course.Type = courseType.EBOOK"
        >
          <mat-icon>description</mat-icon>
          <h3>{{ 'course.create.step1.lblEbook' | translate }}</h3>
          <p>
            {{ 'course.create.step1.descEbook' | translate }}
          </p>
        </div>
      </div>
    </div>
    <div class="step step-2" *ngIf="activeStep === 2">
      <h1>{{ 'course.create.step2.header' | translate }}</h1>
      <h3>{{ 'course.create.step2.subHeader' | translate }}</h3>
      <label for="Title">{{ 'course.create.step2.phTitle' | translate }}</label>
      <input
        [placeholder]="'course.create.step2.phTitle' | translate"
        maxlength="120"
        class="form-control"
        [(ngModel)]="course.Title"
        name="Title"
        id="Title"
        aria-label="Title"
      />
    </div>
    <div class="step step-3" *ngIf="activeStep === 3">
      <h1>{{ 'course.create.step3.header' | translate }}</h1>
      <h3>{{ 'course.create.step3.subHeader' | translate }}</h3>
      <label for="category">{{
        'course.create.step3.phCategory' | translate
        }}</label>
      <p-multiSelect [options]="categories"
                     [(ngModel)]="course.Categories"
                     id="category"
                     name="category"
                     defaultLabel="Select a category"
                     optionLabel="Title"></p-multiSelect>
    </div>
    <div class="step step-4 align-items-center" *ngIf="activeStep === 4">
      <h1>{{ 'course.create.step4.header' | translate }}</h1>
      <h4>{{ 'course.create.step4.subHeader' | translate }}</h4>
      <label for="Resume">{{ 'course.create.step4.phResume' | translate }}</label>
      <angular-editor
        [(ngModel)]="course.Resume"
        name="Resume"
        id="Resume"
        [config]="resumeConfig"
      >
      </angular-editor>
      <div class="">
        <label for="keywords">Keywords</label>
        <p-chips [(ngModel)]="course.Keywords" class="d-block"
                 name="keywords"
                 id="keywords" separator=","></p-chips>
        <div class="text-muted small">
          Separate by commas or click enter to save your proposal.
        </div>
      </div>
    </div>
  </div>
  <div class="btns" [class.end]="activeStep <= 1">
    <button
      mat-stroked-button
      *ngIf="activeStep > 1"
      color="primary"
      (click)="activeStep = activeStep - 1"
    >
      {{ 'course.create.btnPrevious' | translate }}
    </button>
    <button
      mat-flat-button
      color="primary"
      [disabled]="!course.Type || (activeStep === 4 && !(course.Resume && course.Keywords && course.Keywords.length >0))"
      (click)="
        activeStep === 4 ? onSubmit($event) : onNext($event)
      "
    >
      {{ 'course.create.btnContinue' | translate }}
    </button>
  </div>
</form-->
