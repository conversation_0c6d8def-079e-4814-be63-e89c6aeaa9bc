import { environment } from '~/environments/environment';
import { Component, OnInit } from '@angular/core';
import { Course } from '~/app/models/course.model';
import { CourseService } from '~/app/services/courses/course.service';
import { OwlOptions } from 'ngx-owl-carousel-o';

@Component({
	selector: 'app-learning-top',
	templateUrl: './learning-top.component.html',
	styleUrls: ['./learning-top.component.scss']
})
export class LearningTopComponent implements OnInit {
	customOptions: OwlOptions = {
		loop: true,
		mouseDrag: true,
		touchDrag: true,
		pullDrag: true,
		margin: 30,
		dots: false,
		navSpeed: 700,
		center: false,
		navText: ['', ''],
		responsive: {
			0: {
				items: 1
			},
			400: {
				items: 2
			},
			740: {
				items: 3
			},
			940: {
				items: 4
			}
		},
		nav: false
	};

	TAKE = environment.paginationTake.COURSES_TRENDING_TAKE;

	courses: Array<Course>;
	coursesModeList = true;
	constructor(private courseService: CourseService) {}
	async ngOnInit(): Promise<void> {
		this.courses = await this.courseService
			.getAll(this.TAKE, 0, 'justPublished')
			.toPromise();
		//console.log('list of course', this.courses);
	}
}
