<!-- <div class="education_block_list_layout">
	<div class="education_block_thumb n-shadow">
		<a [routerLink]="'/courses/' + course.Slug"
			><img [src]="cover" class="img-fluid" alt=""
		/></a>
	</div>

	<div class="list_layout_ecucation_caption">
		<div class="education_block_body">
			<h4 class="bl-title">
				<a [routerLink]="'/courses/' + course.Slug">{{ course.Title }}</a>
			</h4>
			<div class="course_rate_system">
				<div class="course_ratting">
					<div class="star_info">
						<app-course-rating
							[course]="course"
							[userIsNotConnected]="userIsNotConnected"
							[static]="true"
							(onGlobalRating)="setGlobalRating($event)"
						>
						</app-course-rating>
					</div>
				</div>
				<div class="course_reviews_info">
					<strong class="mr-2">&nbsp;{{ courseRating + '.0' }}</strong
					>({{ viewCount }} {{ 'home.page.reviews' | translate }})
				</div>
			</div>
			<div class="cources_price" *ngIf="course.NewPrice">
				{{ course.Currency }} {{ course.NewPrice }}
				<div class="less_offer">{{ course.Currency }} {{ course.Price }}</div>
			</div>
			<div class="cources_price" *ngIf="!course.NewPrice">
				{{ course.Currency }} {{ course.Price }}
			</div>
		</div>

		<div class="education_block_footer mt-3">
			<div class="education_block_author">
				<div class="path-img">
					<a [routerLink]="'/instructors/' + course.CreatedBy.Slug"
						><img [src]="profile" class="img-fluid" alt=""
					/></a>
				</div>
				<h5>
					<a [routerLink]="'/instructors/' + course.CreatedBy.Slug">{{
						course.CreatedBy.Firstname
					}}</a>
				</h5>
			</div>
			<div class="cources_info_style3">
				<ul>
					<li>
						<div class="foot_lecture">
							<i
								class="ti-control-skip-forward mr-2"
								[ngClass]="getRandomColor()"
							></i>
							{{ getCountContent() }} lectures
						</div>
					</li>
				</ul>
			</div>
		</div>
	</div>
</div>
 -->
<div
	class="course-item d-flex justify-content-center flex-column shadow"
	[routerLink]="'/courses/' + course.Slug"
>
	<div class="course-img">
		<img [src]="cover" alt="" />
		<span *ngIf="course.Free" class="free">Free</span>
	</div>
	<div class="course-item-info">
		<div class="course-teacher">
			<img [src]="profile" alt="" />
			<small>
				<a href="#"
					>{{ course.CreatedBy.Lastname }} {{ course.CreatedBy.Firstname }}
					<i class="ti-check"></i>
				</a>
			</small>
		</div>
		<div class="title-block">
			{{ course.Title | truncatetext: 60 }}
		</div>
		<!-- | truncatetext: 45 -->
		<div class="course_rate_system d-flex">
			<div class="course_ratting">
				<div class="star_info">
					<app-course-rating
						[course]="course"
						[userIsNotConnected]="userIsNotConnected"
						[static]="true"
						(onGlobalRating)="setGlobalRating($event)"
					>
					</app-course-rating>
				</div>
			</div>
			<div class="course_reviews_info">
				<strong class="mr-2">&nbsp;{{ courseRating + '.0' }}</strong
				>({{ viewCount }} {{ 'home.page.reviews' | translate }})
			</div>
		</div>
		<ng-container *ngIf="!course.Free">
			<p *ngIf="course.NewPrice">
				<span class="font-weight-bold price"> {{ course.NewPrice }}</span>
				{{ course.Currency }}
			</p>
			<p *ngIf="!course.NewPrice">
				<span class="font-weight-bold price"> {{ course.Price }}</span>
				{{ course.Currency }}
			</p>
		</ng-container>
		<ng-container *ngIf="course.Free">
			<p>
				<span class="font-weight-bold price">Free</span>
			</p>
		</ng-container>
	</div>
</div>
