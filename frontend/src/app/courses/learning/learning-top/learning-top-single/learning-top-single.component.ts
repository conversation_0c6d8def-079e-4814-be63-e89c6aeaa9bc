import { Component, Input, OnInit } from '@angular/core';
import {
	CourseOpinion,
	DISLIKE_TYPE,
	LIKE_TYPE,
	LOVE_TYPE,
	VIEW_TYPE
} from '~/app/models/course-opinion.model';
import { Course } from '~/app/models/course.model';
import { User } from '~/app/models/user.model';
import { CourseOpinionService } from '~/app/services/courses/course-opinion.service';
import { UserService } from '~/app/services/users/user.service';
import { environment } from '~/environments/environment';

@Component({
	selector: 'app-learning-top-single',
	templateUrl: './learning-top-single.component.html',
	styleUrls: ['./learning-top-single.component.scss']
})
export class LearningTopSingleComponent implements OnInit {
	@Input() course: Course;
	user: User;
	userIsNotConnected: boolean;
	viewCount = 0;
	courseRating = 0;
	cover = '/assets/img/700x500.png';
	profile = '/assets/img/avatar.png';
	constructor(
		private userService: UserService,
		private opinionService: CourseOpinionService
	) {}

	async ngOnInit(): Promise<void> {
		if (this.course.CoverImage) {
			this.cover = `${environment.path}/${this.course.CoverImage.Hashname}`;
		}

		// console.log(this.course.CreatedBy);
		if (this.course.CreatedBy.Photo) {
			this.profile = `${environment.path}/${this.course.CreatedBy.Photo.Hashname}`;
		}

		if (this.userService.isConnected()) {
			this.user = await this.userService.getUserConnected().toPromise();
			if (!this.user) {
				this.userIsNotConnected = true;
			}
		}

		this.countCourseOpinions();
	}

	getRandomColor(): string {
		const items = ['color-1', 'color-2', 'color-3', 'color-4', 'color-5'];
		return items[Math.floor(Math.random() * items.length)][0];
	}

	getCountContent(): number {
		return !this.course ||
			(!this.course.Sections && this.course.Sections.length === 0)
			? 0
			: this.course.Sections.map((s) => s.Contents.length).reduce(
					(sum, current) => sum + current,
					0
			  );
	}

	async countCourseOpinions(): Promise<void> {
		let viewCount = 0;
		let likeCount = 0;
		let disLikeCount = 0;
		let loveCount = 0;
		const commentOpinions = (await this.opinionService
			.getAllCourseOpinions(this.course)
			.toPromise()) as CourseOpinion[];
		commentOpinions.map((opinion) => {
			if (opinion.Type === VIEW_TYPE) {
				++viewCount;
			}
			if (opinion.Type === LIKE_TYPE) {
				++likeCount;
			}
			if (opinion.Type === DISLIKE_TYPE) {
				++disLikeCount;
			}
			if (opinion.Type === LOVE_TYPE) {
				++loveCount;
			}
		});

		this.viewCount = viewCount + likeCount + disLikeCount + loveCount;
	}

	setGlobalRating(rating: number) {
		this.courseRating = rating;
	}
}
