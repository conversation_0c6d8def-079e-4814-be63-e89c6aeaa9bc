@import '~src/colors';

.course-item {
	border-radius: 5px 5px 0 0;
	transition: 0.15s ease-out 100ms;
	cursor: pointer;
	&:hover {
		transform: translateY(-7px);
	}
	.course-img {
		width: 100%;
		height: 150px;
		position: relative;
		border-radius: 5px 5px 0 0;
		overflow: hidden;
		img {
			width: inherit;
			height: inherit;
			object-fit: cover;
		}
		.free {
			position: absolute;
			top: 10px;
			right: 10px;
			padding: 0.1rem 0.6rem;
			border-radius: 15px;
			background-color: #fbe6ed;
			font-size: 0.8rem;
			font-weight: 500;
		}
	}
	.course-item-info {
		padding: 1rem;
		background-color: $white;
		border-radius: 0 0 5px 5px;
		p {
			margin-bottom: 8px !important;
		}
		.price {
			font-size: 1.12rem;
		}
		.star {
			font-size: 1.1rem;
			color: #b0c4de;
		}
		.filled {
			color: #f5bc00;
		}
		.bad {
			color: #f5bc00;
		}
		.filled.bad {
			color: #f5bc00;
		}
		.course-teacher {
			margin-top: 0px;
			img {
				width: 50px;
				height: 50px;
				object-fit: cover;
				border-radius: 5%;
				position: relative;
				margin-top: -42px;
				margin-bottom: 5px;
				background: $primary;
				box-shadow: 0px 0px 0 4px white;
			}
			small {
				position: relative;
				top: -25px;
				left: 62px;
				a {
					color: $linkText !important;
				}
				i {
					color: $white;
					background-color: green;
					width: 15px;
					height: 15px;
					border-radius: 15px;
					padding: 5px;
					font-weight: 800;
					font-size: 0.5rem;
					margin-left: 5px;
				}
			}
		}
		.title-block {
			font-size: 14px;
			font-weight: 500;
			/* margin-bottom: 0px !important; */
			height: 45px;
		}
	}
}
