import { ComponentFixture, TestBed } from '@angular/core/testing';

import { LearningTopSingleComponent } from './learning-top-single.component';

describe('LearningTopSingleComponent', () => {
	let component: LearningTopSingleComponent;
	let fixture: ComponentFixture<LearningTopSingleComponent>;

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			declarations: [LearningTopSingleComponent]
		}).compileComponents();
	});

	beforeEach(() => {
		fixture = TestBed.createComponent(LearningTopSingleComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});
});
