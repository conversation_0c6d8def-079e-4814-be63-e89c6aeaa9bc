<!-- <section class="gray-bg">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-5 col-md-6 col-sm-12">
                <div class="sec-heading center">
                    <p>{{'home.page.hotTending'|translate}}</p>
                    <h2>
                        <span class="theme-cl">{{'home.page.redTextTrending'|translate}}
            </span>{{'home.page.redTextTrendingRest'|translate}}
                    </h2>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-xl-6 col-lg-12 col-md-12 col-sm-12" *ngFor="let course of courses">
                <app-learning-top-single [course]="course"></app-learning-top-single>
            </div>
        </div>
    </div>
</section>
 -->

<section class="courses">
	<div class="container">
		<span class="sup-title">{{ 'home.page.hotNPopular' | translate }}</span>
		<h2>
			<span class="text-primary">{{
				'home.page.hotNPopularitlleTxt1' | translate
			}}</span>
			{{ 'home.page.hotNPopularitlleTxt2' | translate }}
		</h2>

		<owl-carousel-o [options]="customOptions" #owlCar>
			<ng-container *ngFor="let course of courses">
				<ng-template carouselSlide [id]="course.id">
					<!-- <div
              class="course-item d-flex justify-content-center flex-column shadow"
            >
              <div class="course-img">
                <img src="{{ course.image }}" alt="" />
                <span *ngIf="course.free === true" class="free">{{
                  course.free_text
                }}</span>
              </div>
              <div class="course-item-info">
                <div class="course-teacher">
                  <img src="{{ course.teacher }}" alt="" />
                  <small>
                    <a href="#"
                      >{{ course.teacher_name }} <i class="ti-check"></i>
                    </a>
                  </small>
                </div>
                <h5>{{ course.title | truncatetext: 45 }}</h5>
                <p>
                  <span class="mr-2 font-weight-bold text-primary">{{
                    course.rate
                  }}</span>
                  <ngb-rating [(rate)]="course.rate">
                    <ng-template let-fill="fill" let-index="index">
                      <span
                        class="star"
                        [class.filled]="fill === 100"
                        [class.bad]="index < 3"
                        >&#9733;</span
                      >
                    </ng-template>
                  </ngb-rating>
                  <small class="ml-2">({{ course.votes }})</small>
                </p>
                <p>
                  <span class="font-weight-bold price">{{ course.price }}</span> €
                </p>
              </div>
            </div> -->
					<app-learning-top-single [course]="course"></app-learning-top-single>
				</ng-template>
			</ng-container>
		</owl-carousel-o>
		<p class="position-relative">
			<a class="nav-icon left text-primary" (click)="owlCar.prev()">
				<span class="ti-angle-left"></span>
			</a>
			<a class="nav-icon right text-primary" (click)="owlCar.next()">
				<span class="ti-angle-right"></span>
			</a>
		</p>

		<div class="row mt-4"></div>

		<div class="d-flex justify-content-center mt-5">
			<button class="btn btn-primary" [routerLink]="'/courses'">
				{{ 'home.page.seeAllCourses' | translate }}
			</button>
		</div>
	</div>
</section>
