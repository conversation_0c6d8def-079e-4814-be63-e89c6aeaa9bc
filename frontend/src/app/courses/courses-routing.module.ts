import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { CoursesComponent } from './courses.component';
import { LearningComponent } from '~/app/courses/learning/learning.component';
import { CoursesCreateComponent } from '~/app/courses/courses-create/courses-create.component';
import { CoursesDetailComponent } from '~/app/courses/courses-detail/courses-detail.component';
import { CoursesManageComponent } from '~/app/courses/courses-manage/courses-manage.component';
import { CourseReaderComponent } from './course-reader/course-reader.component';
import { CoursesCategorieComponent } from './courses-categorie/courses-categorie.component';

const routes: Routes = [
  {
    path: '',
    component: CoursesComponent
  },
  {
    path: 'category/:id',
    component: CoursesCategorieComponent
  },
  {
    path: 'create',
    component: CoursesManageComponent
  },
  {
    path: 'reader/:id',
    component: CourseReaderComponent
  },
  {
    path: ':id',
    component: CoursesDetailComponent
  },
  {
    path: 'manage/:id',
    component: CoursesManageComponent
  },
  { path: 'learning', children: [{ path: '', component: LearningComponent }] },
  { path: 'categorie/:id', component: CoursesCategorieComponent }

];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CoursesRoutingModule { }
