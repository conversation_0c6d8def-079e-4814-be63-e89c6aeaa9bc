const axios = require('axios');

const BASE_URL = 'http://localhost:3200/api';

async function testVideoUrl() {
  try {
    console.log('🧪 Testing Video URL Construction\n');

    // Get the latest course with video
    const coursesResponse = await axios.get(`${BASE_URL}/courses/10/0`);
    const courseWithVideo = coursesResponse.data.find(course => course.PresentationVideo);
    
    if (!courseWithVideo) {
      console.log('❌ No course with presentation video found');
      return;
    }

    console.log('📹 Course with video found:', {
      Title: courseWithVideo.Title,
      VideoHashname: courseWithVideo.PresentationVideo.Hashname,
      VideoName: courseWithVideo.PresentationVideo.Name
    });

    // Test different URL constructions
    const hashname = courseWithVideo.PresentationVideo.Hashname;
    
    const urlVariations = [
      `http://localhost:3200/${hashname}`,
      `http://localhost:3200/upload/courses/videos/${hashname}`,
      `http://localhost:3200/public/upload/courses/videos/${hashname}`,
      hashname.startsWith('upload/') ? `http://localhost:3200/${hashname}` : `http://localhost:3200/upload/courses/videos/${hashname}`
    ];

    console.log('\n🔍 Testing URL variations:');
    
    for (let i = 0; i < urlVariations.length; i++) {
      const url = urlVariations[i];
      console.log(`\n${i + 1}. Testing: ${url}`);
      
      try {
        const response = await axios.head(url, { timeout: 5000 });
        console.log(`   ✅ Status: ${response.status} - Content-Type: ${response.headers['content-type']}`);
        console.log(`   📏 Content-Length: ${response.headers['content-length']} bytes`);
      } catch (error) {
        if (error.response) {
          console.log(`   ❌ Status: ${error.response.status} - ${error.response.statusText}`);
        } else {
          console.log(`   ❌ Error: ${error.message}`);
        }
      }
    }

    // Test with URL encoding for special characters
    if (hashname.includes('#') || hashname.includes(' ')) {
      console.log('\n🔤 Testing URL encoding for special characters:');
      const encodedUrl = encodeURI(`http://localhost:3200/${hashname}`);
      console.log(`Encoded URL: ${encodedUrl}`);
      
      try {
        const response = await axios.head(encodedUrl, { timeout: 5000 });
        console.log(`✅ Encoded URL works - Status: ${response.status}`);
      } catch (error) {
        console.log(`❌ Encoded URL failed: ${error.response?.status || error.message}`);
      }
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testVideoUrl();
