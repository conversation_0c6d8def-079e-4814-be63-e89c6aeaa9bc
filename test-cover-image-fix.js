const axios = require('axios');

const API_BASE = 'http://localhost:3200/api';

/**
 * Test script to verify cover image URL fixes
 * Tests both backend API responses and frontend URL construction logic
 */

async function testCoverImageFix() {
  console.log('🔍 Testing Cover Image URL Fix\n');

  try {
    // Test 1: Check if backend is running
    console.log('1️⃣ Testing backend connectivity...');
    try {
      const healthCheck = await axios.get(`${API_BASE.replace('/api', '')}`);
      console.log('✅ Backend is running');
    } catch (error) {
      console.log('❌ Backend is not running. Please start the backend first.');
      console.log('   Run: cd backend && npm run start:dev');
      return;
    }

    // Test 2: Test public courses endpoint (no auth required)
    console.log('\n2️⃣ Testing public courses endpoint...');
    try {
      const response = await axios.get(`${API_BASE}/courses/15/0`);
      const courses = response.data;
      
      if (courses && courses.length > 0) {
        console.log(`✅ Found ${courses.length} courses`);
        
        // Check first course with cover image
        const courseWithImage = courses.find(course => course.CoverImage?.Hashname);
        if (courseWithImage) {
          console.log('\n📋 Course with cover image found:');
          console.log(`   - Title: ${courseWithImage.Title}`);
          console.log(`   - Slug: ${courseWithImage.Slug}`);
          console.log(`   - CoverImage.Hashname: ${courseWithImage.CoverImage.Hashname}`);
          
          // Test URL construction logic (simulating frontend)
          const testUrls = testUrlConstruction(courseWithImage.CoverImage.Hashname);
          console.log('\n🔗 URL Construction Tests:');
          testUrls.forEach((test, index) => {
            console.log(`   ${index + 1}. ${test.method}: ${test.url}`);
          });
          
          // Test actual image accessibility
          console.log('\n🌐 Testing image accessibility...');
          for (const test of testUrls) {
            try {
              const imageResponse = await axios.head(test.url, { timeout: 5000 });
              console.log(`   ✅ ${test.method}: ${imageResponse.status} ${imageResponse.statusText}`);
            } catch (error) {
              console.log(`   ❌ ${test.method}: ${error.response?.status || 'Network Error'}`);
            }
          }
        } else {
          console.log('⚠️  No courses with cover images found');
        }
      } else {
        console.log('⚠️  No courses returned from API');
      }
    } catch (error) {
      console.error('❌ Public courses test failed:', error.response?.status, error.response?.data?.message || error.message);
    }

    // Test 3: Test specific course by slug (if we have one)
    console.log('\n3️⃣ Testing specific course endpoint...');
    try {
      // Try to get a course slug from the previous response
      const coursesResponse = await axios.get(`${API_BASE}/courses/5/0`);
      const courses = coursesResponse.data;
      
      if (courses && courses.length > 0) {
        const testCourse = courses[0];
        const courseResponse = await axios.get(`${API_BASE}/courses/public/${testCourse.Slug}`);
        const course = courseResponse.data;
        
        console.log(`✅ Retrieved course: ${course.Title}`);
        if (course.CoverImage?.Hashname) {
          console.log(`   - Cover Image: ${course.CoverImage.Hashname}`);
          
          // Test the constructed URL
          const constructedUrl = constructMediaUrl(course.CoverImage.Hashname);
          console.log(`   - Constructed URL: ${constructedUrl}`);
          
          try {
            const imageTest = await axios.head(constructedUrl, { timeout: 5000 });
            console.log(`   ✅ Image accessible: ${imageTest.status}`);
          } catch (error) {
            console.log(`   ❌ Image not accessible: ${error.response?.status || 'Network Error'}`);
          }
        }
      }
    } catch (error) {
      console.log('⚠️  Specific course test skipped:', error.response?.status || error.message);
    }

  } catch (error) {
    console.error('💥 Test failed:', error.message);
  }

  console.log('\n🎉 Cover image URL fix test completed!');
}

/**
 * Test different URL construction methods
 */
function testUrlConstruction(hashname) {
  const baseUrl = 'http://localhost:3200';
  const tests = [];

  // Method 1: Direct append (current backend setup)
  tests.push({
    method: 'Direct Append',
    url: `${baseUrl}/${hashname}`
  });

  // Method 2: With public prefix
  tests.push({
    method: 'Public Prefix',
    url: `${baseUrl}/public/${hashname}`
  });

  // Method 3: With upload prefix
  tests.push({
    method: 'Upload Prefix',
    url: `${baseUrl}/upload/${hashname}`
  });

  // Method 4: Clean hashname (remove leading slash)
  const cleanHashname = hashname.startsWith('/') ? hashname.substring(1) : hashname;
  tests.push({
    method: 'Clean Hashname',
    url: `${baseUrl}/${cleanHashname}`
  });

  return tests;
}

/**
 * Simulate the frontend URL construction logic
 */
function constructMediaUrl(hashname) {
  if (!hashname) return '';

  // If already a complete URL, return as-is
  if (hashname.startsWith('http://') || hashname.startsWith('https://')) {
    return hashname;
  }

  // Remove leading slash if present to avoid double slashes
  const cleanHashname = hashname.startsWith('/') ? hashname.substring(1) : hashname;

  // Use environment path (simulating frontend environment)
  const environmentPath = 'http://localhost:3200'; // Development environment
  return `${environmentPath}/${cleanHashname}`;
}

// Run the test
testCoverImageFix().catch(console.error);
