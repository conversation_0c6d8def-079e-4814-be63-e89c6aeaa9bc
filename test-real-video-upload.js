const axios = require('axios');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3200/api';

async function uploadRealVideo() {
  try {
    console.log('🎬 Testing Real Video Upload\n');

    // Read the real video file
    const videoPath = './frontend/dist/assets/img/video_test.mp4';
    if (!fs.existsSync(videoPath)) {
      console.log('❌ Video file not found at:', videoPath);
      return;
    }

    const videoBuffer = fs.readFileSync(videoPath);
    const videoBase64 = videoBuffer.toString('base64');
    const videoSize = videoBuffer.length;

    console.log(`📹 Video file info:`);
    console.log(`   Path: ${videoPath}`);
    console.log(`   Size: ${videoSize} bytes (${(videoSize / 1024 / 1024).toFixed(2)} MB)`);
    console.log(`   Base64 length: ${videoBase64.length} characters`);

    // Upload the real video
    console.log('\n📤 Uploading real video file...');
    const uploadResponse = await axios.post(`${BASE_URL}/medias`, {
      Name: `real-video-test-${Date.now()}.mp4`,
      Hashname: videoBase64,
      Extension: "video/mp4",
      Size: videoSize,
      SubDir: "courses/videos"
    });

    console.log('✅ Video uploaded successfully:', {
      Id: uploadResponse.data.Id,
      Name: uploadResponse.data.Name,
      Hashname: uploadResponse.data.Hashname,
      Size: uploadResponse.data.Size
    });

    // Test the uploaded video URL
    const videoUrl = `http://localhost:3200/${uploadResponse.data.Hashname}`;
    console.log(`\n🔍 Testing uploaded video URL: ${videoUrl}`);

    try {
      const response = await axios.head(videoUrl, { timeout: 10000 });
      console.log(`✅ Video accessible:`);
      console.log(`   Status: ${response.status}`);
      console.log(`   Content-Type: ${response.headers['content-type']}`);
      console.log(`   Content-Length: ${response.headers['content-length']} bytes`);
      console.log(`   Accept-Ranges: ${response.headers['accept-ranges']}`);

      // Test if we can get the first few bytes (video header)
      const partialResponse = await axios.get(videoUrl, {
        headers: { 'Range': 'bytes=0-100' },
        responseType: 'arraybuffer',
        timeout: 10000
      });

      const headerBytes = new Uint8Array(partialResponse.data.slice(0, 8));
      console.log(`✅ Video header bytes: ${Array.from(headerBytes).map(b => b.toString(16).padStart(2, '0')).join(' ')}`);

      // Check for common video file signatures
      const headerHex = Array.from(headerBytes).map(b => b.toString(16).padStart(2, '0')).join('');
      if (headerHex.startsWith('000000')) {
        console.log(`✅ Looks like a valid MP4 file (starts with ftyp box)`);
      } else {
        console.log(`⚠️  Unexpected file header: ${headerHex}`);
      }

    } catch (error) {
      console.log(`❌ Error accessing video: ${error.message}`);
    }

    // Now update a test course with this real video
    console.log('\n📝 Updating test course with real video...');
    
    // Get a test course
    const coursesResponse = await axios.get(`${BASE_URL}/courses/10/0`);
    const testCourse = coursesResponse.data.find(course => course.Title.includes('demo course'));
    
    if (!testCourse) {
      console.log('❌ No test course found');
      return;
    }

    // Update the course with the real video
    const updateData = {
      ...testCourse,
      Title: testCourse.Title + ' (Real Video)',
      PresentationVideo: uploadResponse.data
    };

    const updateResponse = await axios.put(`${BASE_URL}/courses/${testCourse.Slug}`, updateData);
    console.log('✅ Course updated with real video');

    // Verify the update
    const updatedCourseResponse = await axios.get(`${BASE_URL}/courses/public/${testCourse.Slug}`);
    const updatedCourse = updatedCourseResponse.data;

    console.log('\n🎯 Final verification:');
    console.log(`   Course Title: ${updatedCourse.Title}`);
    console.log(`   Video Name: ${updatedCourse.PresentationVideo?.Name}`);
    console.log(`   Video Size: ${updatedCourse.PresentationVideo?.Size} bytes`);
    console.log(`   Video URL: http://localhost:3200/${updatedCourse.PresentationVideo?.Hashname}`);

    console.log('\n🎉 Real video upload and course update completed!');
    console.log('🔗 You can now test this video in the CoursePageEditor');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
uploadRealVideo();
