/**
 * Test to verify that the course data structure issue has been fixed
 * This simulates the React frontend course service formatting
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3200/api';

async function apiRequest(method, endpoint, data = null) {
  const config = {
    method,
    url: `${API_BASE_URL}${endpoint}`,
    headers: {
      'Content-Type': 'application/json'
    }
  };
  
  if (data) {
    config.data = data;
  }
  
  try {
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`API Error (${method} ${endpoint}):`, error.response?.data || error.message);
    throw error;
  }
}

// Simulate the updated React course service formatCourse method
function formatCourse(course) {
  if (!course) return course;

  // Extract media IDs from media objects for frontend compatibility
  if (course.CoverImage?.Id && !course.coverImageId) {
    course.coverImageId = course.CoverImage.Id;
  }
  
  if (course.PresentationVideo?.Id && !course.presentationVideoId) {
    course.presentationVideoId = course.PresentationVideo.Id;
  }
  
  // Add computed fields for media existence
  course.hasCoverImage = !!course.CoverImage;
  course.hasPresentationVideo = !!course.PresentationVideo;

  // Format cover image URL
  if (course.CoverImage?.Hashname) {
    const originalHashname = course.CoverImage.Hashname;
    course.CoverImage.Hashname = constructMediaUrl(originalHashname);
  }

  // Format presentation video URL
  if (course.PresentationVideo?.Hashname) {
    const originalHashname = course.PresentationVideo.Hashname;
    course.PresentationVideo.Hashname = constructMediaUrl(originalHashname);
  }

  return course;
}

function constructMediaUrl(hashname) {
  if (!hashname) return '';
  
  // If already a complete URL, return as-is
  if (hashname.startsWith('http://') || hashname.startsWith('https://')) {
    return hashname;
  }
  
  // Remove leading slash if present to avoid double slashes
  const cleanHashname = hashname.startsWith('/') ? hashname.substring(1) : hashname;
  
  // Backend serves files directly from the root with the full path
  return `http://localhost:3200/${cleanHashname}`;
}

async function testCourseDataStructureFixed() {
  console.log('🔧 TESTING COURSE DATA STRUCTURE FIX');
  console.log('Verifying that course data now includes proper ID fields and computed fields');
  console.log('=' .repeat(80));
  
  // Test courses that we know have media from previous tests
  const testCourseIds = [88, 87];
  
  console.log('\n1️⃣ Testing course data structure fixes...');
  
  for (const courseId of testCourseIds) {
    console.log(`\n   📚 Testing Course ${courseId}:`);
    
    try {
      // Get raw course data from backend
      const rawCourse = await apiRequest('GET', `/courses/${courseId}`);
      console.log(`      ✅ Raw course fetched: "${rawCourse.Title}"`);
      
      // Apply the updated formatCourse method
      const formattedCourse = formatCourse({ ...rawCourse }); // Clone to avoid mutation
      
      console.log(`      📊 Data structure analysis:`);
      
      // Check cover image
      if (formattedCourse.CoverImage) {
        console.log(`         🖼️ Cover Image:`);
        console.log(`            ✅ CoverImage object: ID ${formattedCourse.CoverImage.Id}`);
        console.log(`            ✅ coverImageId field: ${formattedCourse.coverImageId}`);
        console.log(`            ✅ hasCoverImage: ${formattedCourse.hasCoverImage}`);
        console.log(`            ✅ Organized directory: ${rawCourse.CoverImage.SubDir}`);
        console.log(`            ✅ Formatted URL: ${formattedCourse.CoverImage.Hashname}`);
        
        const idMatch = formattedCourse.coverImageId === formattedCourse.CoverImage.Id;
        console.log(`            ID Match: ${idMatch ? '✅ CORRECT' : '❌ MISMATCH'}`);
      } else {
        console.log(`         🖼️ Cover Image: ❌ None`);
      }
      
      // Check presentation video
      if (formattedCourse.PresentationVideo) {
        console.log(`         🎥 Presentation Video:`);
        console.log(`            ✅ PresentationVideo object: ID ${formattedCourse.PresentationVideo.Id}`);
        console.log(`            ✅ presentationVideoId field: ${formattedCourse.presentationVideoId}`);
        console.log(`            ✅ hasPresentationVideo: ${formattedCourse.hasPresentationVideo}`);
        console.log(`            ✅ Organized directory: ${rawCourse.PresentationVideo.SubDir}`);
        console.log(`            ✅ Formatted URL: ${formattedCourse.PresentationVideo.Hashname}`);
        
        const idMatch = formattedCourse.presentationVideoId === formattedCourse.PresentationVideo.Id;
        console.log(`            ID Match: ${idMatch ? '✅ CORRECT' : '❌ MISMATCH'}`);
      } else {
        console.log(`         🎥 Presentation Video: ❌ None`);
      }
      
      // Test the BuildCourse-like output that was causing issues
      console.log(`      🔍 BuildCourse simulation:`);
      const buildCourseOutput = {
        id: formattedCourse.Id,
        slug: formattedCourse.Slug,
        title: formattedCourse.Title,
        coverImageId: formattedCourse.coverImageId,
        presentationVideoId: formattedCourse.presentationVideoId,
        hasCoverImage: formattedCourse.hasCoverImage,
        hasPresentationVideo: formattedCourse.hasPresentationVideo
      };
      
      console.log(`         BuildCourse output:`, buildCourseOutput);
      
      const hasUndefinedIds = buildCourseOutput.coverImageId === undefined || 
                             buildCourseOutput.presentationVideoId === undefined;
      
      if (hasUndefinedIds && (buildCourseOutput.hasCoverImage || buildCourseOutput.hasPresentationVideo)) {
        console.log(`         ❌ ISSUE: Still has undefined IDs despite having media`);
      } else {
        console.log(`         ✅ SUCCESS: No undefined ID issues`);
      }
      
    } catch (error) {
      console.log(`      ❌ Error testing course ${courseId}:`, error.message);
    }
  }
  
  // Test 2: Create a new course to verify the fix works end-to-end
  console.log('\n2️⃣ Testing end-to-end course creation with organized structure...');
  
  const tempCourseSlug = `fix-test-${Date.now()}`;
  
  // Upload cover image
  const coverImageData = {
    Name: `${Date.now()}-fix-test-cover.png`,
    Hashname: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==',
    Extension: 'image/png',
    Size: 1500,
    SubDir: `courses/${tempCourseSlug}/cover`
  };
  
  const uploadedCover = await apiRequest('POST', '/medias', coverImageData);
  console.log(`   📤 Cover uploaded: ID ${uploadedCover.Id}, SubDir: ${uploadedCover.SubDir}`);
  
  // Upload presentation video
  const videoData = {
    Name: `${Date.now()}-fix-test-video.mp4`,
    Hashname: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==',
    Extension: 'video/mp4',
    Size: 5000,
    SubDir: `courses/${tempCourseSlug}/video`
  };
  
  const uploadedVideo = await apiRequest('POST', '/medias', videoData);
  console.log(`   📤 Video uploaded: ID ${uploadedVideo.Id}, SubDir: ${uploadedVideo.SubDir}`);
  
  // Create course
  const coursePayload = {
    body: {
      Title: 'Course Data Structure Fix Test',
      Resume: 'Testing the fix for course data structure issues',
      Keywords: ['fix', 'test', 'structure'],
      Format: 2,
      Language: 'en',
      Free: true,
      Level: [1],
      Message: 'Welcome message',
      Congratulation: 'Congratulations message',
      Categories: [],
      Published: false,
      Archived: false,
      coverImageId: uploadedCover.Id,
      presentationVideoId: uploadedVideo.Id,
      Slug: tempCourseSlug,
      CreatedBy: { Id: 1 }
    },
    origin: 'http://localhost:3000'
  };
  
  const createdCourse = await apiRequest('POST', '/courses', coursePayload);
  console.log(`   📚 Course created: ID ${createdCourse.Id}, Slug: ${createdCourse.Slug}`);
  
  // Retrieve and format the course
  const retrievedCourse = await apiRequest('GET', `/courses/${createdCourse.Id}`);
  const formattedNewCourse = formatCourse({ ...retrievedCourse });
  
  console.log(`   🔍 New course data structure:`);
  console.log(`      coverImageId: ${formattedNewCourse.coverImageId}`);
  console.log(`      presentationVideoId: ${formattedNewCourse.presentationVideoId}`);
  console.log(`      hasCoverImage: ${formattedNewCourse.hasCoverImage}`);
  console.log(`      hasPresentationVideo: ${formattedNewCourse.hasPresentationVideo}`);
  
  const newCourseFixed = formattedNewCourse.coverImageId !== undefined && 
                        formattedNewCourse.presentationVideoId !== undefined &&
                        formattedNewCourse.hasCoverImage === true &&
                        formattedNewCourse.hasPresentationVideo === true;
  
  console.log(`      Fix Status: ${newCourseFixed ? '✅ FIXED' : '❌ STILL BROKEN'}`);
  
  // Final assessment
  console.log('\n3️⃣ FINAL ASSESSMENT:');
  
  if (newCourseFixed) {
    console.log('🎉 SUCCESS: Course data structure issue has been FIXED!');
    console.log('✅ coverImageId and presentationVideoId fields are now populated');
    console.log('✅ hasCoverImage and hasPresentationVideo computed fields work');
    console.log('✅ Organized directory structure is maintained');
    console.log('✅ Media URLs are properly formatted');
    console.log('\n🚀 The BuildCourse logs should no longer show undefined IDs!');
  } else {
    console.log('❌ ISSUE: Course data structure problem still exists');
    console.log('   Further investigation needed');
  }
  
  return {
    success: newCourseFixed,
    testCourseId: createdCourse.Id,
    tempCourseSlug
  };
}

testCourseDataStructureFixed().catch(console.error);
