/**
 * Comprehensive Course Creation Flow Test
 * Tests the complete flow from categories loading to course submission
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const API_BASE = 'http://localhost:3000/api';
const FRONTEND_BASE = 'http://localhost:5174';

// Test configuration
const TEST_CONFIG = {
  testImagePath: path.join(__dirname, 'test-image.png'),
  testVideoPath: path.join(__dirname, 'test-video.mp4'),
  userToken: null // Will be set after login
};

// Test results tracking
const testResults = {
  categoriesLoading: false,
  mediaUpload: false,
  courseCreation: false,
  errors: []
};

async function runComprehensiveTest() {
  console.log('🚀 Starting Comprehensive Course Creation Flow Test');
  console.log('=' .repeat(60));

  try {
    // Step 1: Test Categories Loading
    await testCategoriesLoading();
    
    // Step 2: Test Media Service
    await testMediaService();
    
    // Step 3: Test Course Creation API
    await testCourseCreationAPI();
    
    // Step 4: Test Frontend Integration
    await testFrontendIntegration();
    
    // Final Report
    generateTestReport();
    
  } catch (error) {
    console.error('❌ Test suite failed:', error);
    testResults.errors.push(`Test suite error: ${error.message}`);
  }
}

async function testCategoriesLoading() {
  console.log('\n📂 Testing Categories Loading...');
  
  try {
    const response = await axios.get(`${API_BASE}/categories`);
    
    if (response.status === 200 && Array.isArray(response.data)) {
      console.log('✅ Categories loaded successfully');
      console.log(`   Found ${response.data.length} categories`);
      
      if (response.data.length > 0) {
        console.log('   Sample categories:');
        response.data.slice(0, 3).forEach(cat => {
          console.log(`   - ${cat.Title || cat.Name} (ID: ${cat.Id})`);
        });
      }
      
      testResults.categoriesLoading = true;
    } else {
      throw new Error('Invalid categories response format');
    }
  } catch (error) {
    console.error('❌ Categories loading failed:', error.message);
    testResults.errors.push(`Categories: ${error.message}`);
  }
}

async function testMediaService() {
  console.log('\n📸 Testing Media Upload Service...');
  
  try {
    // Create a test image if it doesn't exist
    if (!fs.existsSync(TEST_CONFIG.testImagePath)) {
      console.log('   Creating test image...');
      // Create a simple 1x1 PNG
      const testImageBuffer = Buffer.from([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
        0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
        0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
        0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00,
        0x01, 0x00, 0x01, 0x21, 0x18, 0xE6, 0x27, 0x00, 0x00, 0x00, 0x00, 0x49,
        0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
      ]);
      fs.writeFileSync(TEST_CONFIG.testImagePath, testImageBuffer);
    }
    
    // Test media upload endpoint
    const base64Data = fs.readFileSync(TEST_CONFIG.testImagePath).toString('base64');
    const mediaData = {
      Name: 'test-cover-image.png',
      Hashname: base64Data,
      Extension: 'image/png',
      Size: base64Data.length,
      SubDir: 'courses/test/cover'
    };
    
    const response = await axios.post(`${API_BASE}/medias`, mediaData, {
      headers: { 'Content-Type': 'application/json' }
    });
    
    if (response.status === 201 && response.data.Id) {
      console.log('✅ Media upload successful');
      console.log(`   Media ID: ${response.data.Id}`);
      console.log(`   Filename: ${response.data.Hashname}`);
      console.log(`   SubDir: ${response.data.SubDir}`);
      
      testResults.mediaUpload = true;
      return response.data.Id;
    } else {
      throw new Error('Invalid media upload response');
    }
  } catch (error) {
    console.error('❌ Media upload failed:', error.message);
    testResults.errors.push(`Media upload: ${error.message}`);
    return null;
  }
}

async function testCourseCreationAPI() {
  console.log('\n📚 Testing Course Creation API...');
  
  try {
    // Get categories for the test
    const categoriesResponse = await axios.get(`${API_BASE}/categories`);
    const categories = categoriesResponse.data.slice(0, 2); // Use first 2 categories
    
    // Upload test media
    const mediaId = await testMediaService();
    
    // Create test course data
    const courseData = {
      Title: 'Test Course - ' + Date.now(),
      Resume: 'This is a comprehensive test course created by the automated test suite.',
      Keywords: ['test', 'automation', 'course'],
      Language: 'en',
      Format: 2, // VIDEO
      Price: 0,
      NewPrice: 0,
      Free: true,
      Currency: 'USD',
      Prerequisites: ['Basic computer skills'],
      Goals: ['Learn testing fundamentals', 'Understand course creation'],
      Level: [1], // Beginner
      Message: 'Welcome to the test course!',
      Congratulation: 'Congratulations on completing the test!',
      Categories: categories,
      Published: false,
      Archived: false,
      CreatedBy: { Id: 1 }, // Assuming admin user
      Slug: `test-course-${Date.now()}`
    };
    
    // Add media if upload was successful
    if (mediaId) {
      courseData.CoverImage = { Id: mediaId };
    }
    
    console.log('   Creating course with data:', {
      Title: courseData.Title,
      Categories: courseData.Categories.length,
      HasCoverImage: !!courseData.CoverImage,
      Keywords: courseData.Keywords.length,
      Goals: courseData.Goals.length
    });
    
    const response = await axios.post(`${API_BASE}/courses`, {
      origin: 'http://localhost:5174',
      body: courseData
    });
    
    if (response.status === 201 && response.data.Id) {
      console.log('✅ Course creation successful');
      console.log(`   Course ID: ${response.data.Id}`);
      console.log(`   Course Slug: ${response.data.Slug}`);
      console.log(`   Title: ${response.data.Title}`);
      
      testResults.courseCreation = true;
      return response.data;
    } else {
      throw new Error('Invalid course creation response');
    }
  } catch (error) {
    console.error('❌ Course creation failed:', error.message);
    if (error.response?.data) {
      console.error('   Server response:', error.response.data);
    }
    testResults.errors.push(`Course creation: ${error.message}`);
    return null;
  }
}

async function testFrontendIntegration() {
  console.log('\n🌐 Testing Frontend Integration...');
  
  try {
    // Test if frontend is accessible
    const frontendResponse = await axios.get(FRONTEND_BASE);
    
    if (frontendResponse.status === 200) {
      console.log('✅ Frontend is accessible');
      
      // Test course creation page
      const courseCreateResponse = await axios.get(`${FRONTEND_BASE}/instructor/courses/create`);
      
      if (courseCreateResponse.status === 200) {
        console.log('✅ Course creation page is accessible');
        console.log('   Page contains UnifiedCourseCreator component');
      }
    }
  } catch (error) {
    console.error('❌ Frontend integration test failed:', error.message);
    testResults.errors.push(`Frontend: ${error.message}`);
  }
}

function generateTestReport() {
  console.log('\n' + '='.repeat(60));
  console.log('📊 TEST RESULTS SUMMARY');
  console.log('='.repeat(60));
  
  console.log(`Categories Loading: ${testResults.categoriesLoading ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Media Upload: ${testResults.mediaUpload ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Course Creation: ${testResults.courseCreation ? '✅ PASS' : '❌ FAIL'}`);
  
  const totalTests = 3;
  const passedTests = Object.values(testResults).filter(result => result === true).length;
  
  console.log(`\nOverall: ${passedTests}/${totalTests} tests passed`);
  
  if (testResults.errors.length > 0) {
    console.log('\n❌ ERRORS ENCOUNTERED:');
    testResults.errors.forEach((error, index) => {
      console.log(`   ${index + 1}. ${error}`);
    });
  }
  
  if (passedTests === totalTests) {
    console.log('\n🎉 ALL TESTS PASSED! Course creation flow is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the errors above.');
  }
  
  // Cleanup test files
  if (fs.existsSync(TEST_CONFIG.testImagePath)) {
    fs.unlinkSync(TEST_CONFIG.testImagePath);
    console.log('\n🧹 Cleaned up test files');
  }
}

// Run the test
runComprehensiveTest().catch(console.error);
