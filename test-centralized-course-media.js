/**
 * Test to verify that course media is now stored in centralized directories:
 * - Course covers: upload/courses/covers/
 * - Course videos: upload/courses/videos/
 * This matches the user's requirement for better organization and visibility
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3200/api';

// Test configuration
const TEST_CONFIG = {
  // Test image (1x1 PNG)
  testImage: {
    name: 'centralized-test-cover.png',
    type: 'image/png',
    base64: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg=='
  },
  
  // Test video data
  testVideo: {
    name: 'centralized-test-video.mp4',
    type: 'video/mp4',
    base64: 'ZGF0YTp2aWRlby9tcDQ7YmFzZTY0LGRhdGE='
  },
  
  // Test user data
  testUser: {
    slug: 'testuser',
    id: 1
  },
  
  // Course data
  courseData: {
    Title: 'Centralized Media Test Course',
    Resume: 'This course tests the new centralized media directory structure where all course covers are stored in upload/courses/covers/ and all course videos are stored in upload/courses/videos/.',
    Keywords: ['centralized', 'media', 'test'],
    Format: 2,
    Language: 'en',
    Free: false,
    Price: 49.99,
    NewPrice: 39.99,
    Level: [1],
    Message: 'Welcome to the centralized media test!',
    Congratulation: 'Congratulations on completing the centralized media test!',
    Categories: [],
    Published: false,
    Archived: false
  }
};

/**
 * Helper function to make API requests
 */
async function apiRequest(method, endpoint, data = null) {
  const config = {
    method,
    url: `${API_BASE_URL}${endpoint}`,
    headers: {
      'Content-Type': 'application/json'
    }
  };
  
  if (data) {
    config.data = data;
  }
  
  try {
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`API Error (${method} ${endpoint}):`, error.response?.data || error.message);
    throw error;
  }
}

/**
 * Step 1: Upload cover image to centralized directory
 */
async function uploadCoverImageCentralized() {
  console.log('🖼️ Step 1: Uploading cover image to centralized directory...');
  
  const coverImageData = {
    Name: TEST_CONFIG.testImage.name,
    Hashname: TEST_CONFIG.testImage.base64,
    Extension: TEST_CONFIG.testImage.type,
    Size: 1000,
    SubDir: 'courses/covers' // NEW: Centralized directory structure
  };
  
  const uploadedCover = await apiRequest('POST', '/medias', coverImageData);
  
  console.log('✅ Cover image uploaded:', {
    id: uploadedCover.Id,
    hashname: uploadedCover.Hashname,
    subDir: uploadedCover.SubDir,
    expectedPrefix: 'cvr_' // Should have cover prefix
  });
  
  // Verify the filename has the correct prefix
  const hasCorrectPrefix = uploadedCover.Hashname.startsWith('cvr_');
  console.log(`   Filename prefix check: ${hasCorrectPrefix ? '✅ CORRECT (cvr_)' : '❌ INCORRECT'}`);
  
  // Verify the subdirectory is correct
  const hasCorrectSubDir = uploadedCover.SubDir === 'courses/covers';
  console.log(`   SubDirectory check: ${hasCorrectSubDir ? '✅ CORRECT (courses/covers)' : '❌ INCORRECT'}`);
  
  return uploadedCover;
}

/**
 * Step 2: Upload presentation video to centralized directory
 */
async function uploadPresentationVideoCentralized() {
  console.log('\n🎥 Step 2: Uploading presentation video to centralized directory...');
  
  const videoData = {
    Name: TEST_CONFIG.testVideo.name,
    Hashname: TEST_CONFIG.testVideo.base64,
    Extension: TEST_CONFIG.testVideo.type,
    Size: 5000,
    SubDir: 'courses/videos' // NEW: Centralized directory structure
  };
  
  const uploadedVideo = await apiRequest('POST', '/medias', videoData);
  
  console.log('✅ Presentation video uploaded:', {
    id: uploadedVideo.Id,
    hashname: uploadedVideo.Hashname,
    subDir: uploadedVideo.SubDir,
    expectedPrefix: 'vid_' // Should have video prefix for courses/videos
  });
  
  // Verify the filename has the correct prefix
  const hasCorrectPrefix = uploadedVideo.Hashname.startsWith('vid_');
  console.log(`   Filename prefix check: ${hasCorrectPrefix ? '✅ CORRECT (vid_)' : '❌ INCORRECT'}`);
  
  // Verify the subdirectory is correct
  const hasCorrectSubDir = uploadedVideo.SubDir === 'courses/videos';
  console.log(`   SubDirectory check: ${hasCorrectSubDir ? '✅ CORRECT (courses/videos)' : '❌ INCORRECT'}`);
  
  return uploadedVideo;
}

/**
 * Step 3: Create course with centralized media
 */
async function createCourseWithCentralizedMedia(coverImage, presentationVideo) {
  console.log('\n📚 Step 3: Creating course with centralized media references...');
  
  const coursePayload = {
    body: {
      ...TEST_CONFIG.courseData,
      coverImageId: coverImage.Id,
      presentationVideoId: presentationVideo.Id,
      CreatedBy: { Id: TEST_CONFIG.testUser.id }
    },
    origin: 'http://localhost:3000'
  };

  console.log('📋 Course payload with media IDs:', {
    title: coursePayload.body.Title,
    coverImageId: coursePayload.body.coverImageId,
    presentationVideoId: coursePayload.body.presentationVideoId,
    createdById: coursePayload.body.CreatedBy.Id,
    hasCoverImageObject: !!coursePayload.body.CoverImage,
    hasPresentationVideoObject: !!coursePayload.body.PresentationVideo
  });

  const createdCourse = await apiRequest('POST', '/courses', coursePayload);
  
  console.log('✅ Course created successfully:', {
    id: createdCourse.Id,
    slug: createdCourse.Slug,
    title: createdCourse.Title
  });
  
  return createdCourse;
}

/**
 * Step 4: Verify centralized media URLs
 */
async function verifyCentralizedMediaURLs(course, originalCoverImage, originalPresentationVideo) {
  console.log('\n🔍 Step 4: Verifying centralized media URLs...');
  
  // Retrieve the course to get media information
  console.log(`🔍 Retrieving course by slug: ${course.Slug}`);

  // Try the public endpoint first (which should include media relations)
  let retrievedCourse;
  try {
    retrievedCourse = await apiRequest('GET', `/courses/public/${course.Slug}`);
    console.log('✅ Retrieved via public endpoint');
  } catch (error) {
    console.log('❌ Public endpoint failed, trying direct ID endpoint');
    retrievedCourse = await apiRequest('GET', `/courses/${course.Id}`);
    console.log('✅ Retrieved via ID endpoint');
  }

  console.log('🔍 Retrieved course data:', {
    id: retrievedCourse.Id,
    title: retrievedCourse.Title,
    hasCoverImage: !!retrievedCourse.CoverImage,
    hasVideo: !!retrievedCourse.PresentationVideo,
    coverImageId: retrievedCourse.coverImageId,
    videoId: retrievedCourse.presentationVideoId
  });

  console.log('📊 Centralized Media URL Verification:');
  
  // Check cover image
  if (retrievedCourse.CoverImage) {
    const coverUrl = `http://localhost:3200/${retrievedCourse.CoverImage.Hashname}`;
    const expectedPath = `upload/courses/covers/${retrievedCourse.CoverImage.Hashname}`;
    
    console.log(`   🖼️ Cover Image:`);
    console.log(`     Database ID: ${retrievedCourse.CoverImage.Id}`);
    console.log(`     Filename: ${retrievedCourse.CoverImage.Hashname}`);
    console.log(`     SubDir: ${retrievedCourse.CoverImage.SubDir}`);
    console.log(`     Full URL: ${coverUrl}`);
    console.log(`     Expected Path: ${expectedPath}`);
    
    // Test URL accessibility
    try {
      const response = await axios.head(coverUrl);
      console.log(`     URL Access: ✅ ACCESSIBLE (${response.status})`);
    } catch (error) {
      console.log(`     URL Access: ❌ NOT ACCESSIBLE (${error.response?.status || error.message})`);
    }
  }
  
  // Check presentation video
  if (retrievedCourse.PresentationVideo) {
    const videoUrl = `http://localhost:3200/${retrievedCourse.PresentationVideo.Hashname}`;
    const expectedPath = `upload/courses/videos/${retrievedCourse.PresentationVideo.Hashname}`;
    
    console.log(`   🎥 Presentation Video:`);
    console.log(`     Database ID: ${retrievedCourse.PresentationVideo.Id}`);
    console.log(`     Filename: ${retrievedCourse.PresentationVideo.Hashname}`);
    console.log(`     SubDir: ${retrievedCourse.PresentationVideo.SubDir}`);
    console.log(`     Full URL: ${videoUrl}`);
    console.log(`     Expected Path: ${expectedPath}`);
    
    // Test URL accessibility
    try {
      const response = await axios.head(videoUrl);
      console.log(`     URL Access: ✅ ACCESSIBLE (${response.status})`);
    } catch (error) {
      console.log(`     URL Access: ❌ NOT ACCESSIBLE (${error.response?.status || error.message})`);
    }
  }
  
  // Overall assessment
  const coverCorrect = retrievedCourse.CoverImage && 
                      retrievedCourse.CoverImage.SubDir === 'courses/covers' &&
                      retrievedCourse.CoverImage.Hashname.startsWith('cvr_');
  
  const videoCorrect = retrievedCourse.PresentationVideo && 
                      retrievedCourse.PresentationVideo.SubDir === 'courses/videos' &&
                      retrievedCourse.PresentationVideo.Hashname.startsWith('vid_');
  
  console.log('\n🎯 Centralized Media Assessment:');
  console.log(`   Cover Image Structure: ${coverCorrect ? '✅ PERFECT' : '❌ NEEDS FIXING'}`);
  console.log(`   Video Structure: ${videoCorrect ? '✅ PERFECT' : '❌ NEEDS FIXING'}`);
  console.log(`   Overall Centralization: ${coverCorrect && videoCorrect ? '✅ SUCCESS' : '❌ PARTIAL'}`);
  
  return { coverCorrect, videoCorrect, success: coverCorrect && videoCorrect };
}

/**
 * Main test execution
 */
async function runCentralizedMediaTest() {
  console.log('🧪 Centralized Course Media Test');
  console.log('Testing new directory structure: upload/courses/covers/ and upload/courses/videos/');
  console.log('=' .repeat(80));
  
  try {
    // Execute the complete flow
    const coverImage = await uploadCoverImageCentralized();
    const presentationVideo = await uploadPresentationVideoCentralized();
    const course = await createCourseWithCentralizedMedia(coverImage, presentationVideo);
    const verification = await verifyCentralizedMediaURLs(course, coverImage, presentationVideo);
    
    console.log('\n' + '=' .repeat(80));
    if (verification.success) {
      console.log('🎉 CENTRALIZED MEDIA TEST PASSED!');
      console.log('✅ Course covers stored in upload/courses/covers/');
      console.log('✅ Course videos stored in upload/courses/videos/');
      console.log('✅ Proper UUID prefixes applied (cvr_ for covers, vid_ for videos)');
      console.log('✅ Media files are accessible via direct URLs');
      console.log('✅ Frontend can now clearly see all course media in centralized locations');
      
      console.log('\n📋 Test Summary:');
      console.log(`   Course: ${course.Title} (ID: ${course.Id})`);
      console.log(`   Cover: upload/courses/covers/${coverImage.Hashname}`);
      console.log(`   Video: upload/courses/videos/${presentationVideo.Hashname}`);
      console.log(`   Frontend URL: http://localhost:5174/courses/${course.Slug}`);
      
    } else {
      console.log('❌ CENTRALIZED MEDIA TEST FAILED!');
      console.log('❌ Media is not being stored in the correct centralized directories');
      
      if (!verification.coverCorrect) {
        console.log('❌ Cover image not in upload/courses/covers/');
      }
      if (!verification.videoCorrect) {
        console.log('❌ Presentation video not in upload/courses/videos/');
      }
    }
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    
    if (error.message.includes('500')) {
      console.log('\n💡 Possible causes:');
      console.log('   - Backend server not running');
      console.log('   - Database connection issues');
      console.log('   - Media service configuration errors');
    }
  }
}

// Run the centralized media test
runCentralizedMediaTest();
